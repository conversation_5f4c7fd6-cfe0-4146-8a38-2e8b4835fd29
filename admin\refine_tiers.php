<?php
$pageTitle = "凝练档位管理";
$currentPage = "refine_tiers";

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

include 'layout_header.php';

// 获取所有档位数据
$tiers_query = "SELECT tier, min_rv, max_rv, prefix_equipment FROM refine_tiers ORDER BY tier";
$tiers_stmt = $db->prepare($tiers_query);
$tiers_stmt->execute();
$tiers = $tiers_stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_tiers'])) {
        // 开始事务
        $db->beginTransaction();
        try {
            foreach ($_POST['tiers'] as $tier => $data) {
                $min_rv = $data['min_rv'];
                $max_rv = $data['max_rv'];
                $prefix_equipment = $data['prefix_equipment'];
                
                // 检查是否已存在
                $check_query = "SELECT tier FROM refine_tiers WHERE tier = ?";
                $check_stmt = $db->prepare($check_query);
                $check_stmt->execute([$tier]);
                
                if ($check_stmt->rowCount() > 0) {
                    // 更新
                    $update_query = "UPDATE refine_tiers SET min_rv = ?, max_rv = ?, prefix_equipment = ? WHERE tier = ?";
                    $update_stmt = $db->prepare($update_query);
                    $update_stmt->execute([$min_rv, $max_rv, $prefix_equipment, $tier]);
                } else {
                    // 插入
                    $insert_query = "INSERT INTO refine_tiers (tier, min_rv, max_rv, prefix_equipment) VALUES (?, ?, ?, ?)";
                    $insert_stmt = $db->prepare($insert_query);
                    $insert_stmt->execute([$tier, $min_rv, $max_rv, $prefix_equipment]);
                }
            }
            
            $db->commit();
            echo "<div class='alert alert-success'>档位数据已成功保存</div>";
        } catch (Exception $e) {
            $db->rollBack();
            echo "<div class='alert alert-danger'>保存失败: " . $e->getMessage() . "</div>";
        }
    }
    
    if (isset($_POST['update_tiers_ajax'])) {
        header('Content-Type: application/json');
        
        $tierData = json_decode($_POST['tier_data'], true);
        if (!$tierData) {
            echo json_encode(['success' => false, 'message' => '无效的数据格式']);
            exit;
        }
        
        // 开始事务
        $db->beginTransaction();
        try {
            foreach ($tierData as $tier) {
                $update_query = "UPDATE refine_tiers SET min_rv = ?, max_rv = ? WHERE tier = ?";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->execute([$tier['min_rv'], $tier['max_rv'], $tier['tier']]);
            }
            
            $db->commit();
            echo json_encode(['success' => true]);
        } catch (Exception $e) {
            $db->rollBack();
            echo json_encode(['success' => false, 'message' => $e->getMessage()]);
        }
        exit;
    }
    
    if (isset($_POST['delete_tier']) && isset($_POST['tier'])) {
        $tier = $_POST['tier'];
        $delete_query = "DELETE FROM refine_tiers WHERE tier = ?";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->execute([$tier]);
        
        echo "<div class='alert alert-success'>档位 $tier 已删除</div>";
        header("Location: refine_tiers.php");
        exit;
    }
}

// 转换为前端所需的JSON格式
$tiersJson = json_encode($tiers);
?>

<div class="container-fluid">
    <p>管理装备凝练的不同档位RV区间和前缀。</p>
    
    <div class="row">
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-bar mr-1"></i>
                    RV值档位分布
                </div>
                <div class="card-body">
                    <div id="rv-slider-container" style="height: 200px; padding: 20px;">
                        <div id="rv-slider" style="position: relative; height: 100px;"></div>
                    </div>
                    <div class="text-center mt-4">
                        <button id="save-slider" class="btn btn-primary">保存调整后的档位区间</button>
                    </div>
                    <div class="text-muted text-center mt-2">
                        <small>提示: 拖动各档位区间的边界来调整RV值范围。</small>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-12">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table mr-1"></i>
                    档位详细配置
                </div>
                <div class="card-body">
                    <form method="post" action="" id="tiers-form">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>档位</th>
                                        <th>最小RV值</th>
                                        <th>最大RV值</th>
                                        <th>区间长度</th>
                                        <th>装备前缀</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($tiers as $tier): ?>
                                    <tr class="tier-row" data-tier="<?= $tier['tier'] ?>">
                                        <td><?= $tier['tier'] ?></td>
                                        <td>
                                            <input type="number" step="0.01" min="0" max="30" 
                                                class="form-control min-rv" 
                                                name="tiers[<?= $tier['tier'] ?>][min_rv]" 
                                                value="<?= $tier['min_rv'] ?>" required>
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" min="0" max="30" 
                                                class="form-control max-rv" 
                                                name="tiers[<?= $tier['tier'] ?>][max_rv]" 
                                                value="<?= $tier['max_rv'] ?>" required>
                                        </td>
                                        <td class="interval-length">
                                            <?= number_format($tier['max_rv'] - $tier['min_rv'], 2) ?>
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" 
                                                name="tiers[<?= $tier['tier'] ?>][prefix_equipment]" 
                                                value="<?= htmlspecialchars($tier['prefix_equipment']) ?>" required>
                                        </td>
                                        <td>
                                            <?php if ($tier['tier'] > 1): ?>
                                            <form method="post" action="" style="display:inline">
                                                <input type="hidden" name="tier" value="<?= $tier['tier'] ?>">
                                                <button type="submit" name="delete_tier" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个档位吗？')">删除</button>
                                            </form>
                                            <?php else: ?>
                                            <button class="btn btn-sm btn-secondary" disabled>基础档位</button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        
                        <button type="button" id="add-tier" class="btn btn-success">添加新档位</button>
                        <button type="submit" name="save_tiers" class="btn btn-primary">保存所有档位</button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<link rel="stylesheet" href="https://code.jquery.com/ui/1.12.1/themes/base/jquery-ui.css">
<style>
#rv-slider {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}
.rv-tier {
    position: absolute;
    height: 80px;
    top: 10px;
    border-right: 3px solid #ccc;
    border-left: 3px solid #ccc;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
    transition: background-color 0.2s;
    cursor: move;
}
.rv-tier:hover {
    background-color: rgba(0, 123, 255, 0.2);
}
.rv-tier-label {
    position: absolute;
    top: -25px;
    width: 100%;
    text-align: center;
    font-weight: bold;
    color: #495057;
}
.rv-tier-value {
    position: absolute;
    bottom: -25px;
    width: 100%;
    text-align: center;
    font-size: 0.8em;
    color: #6c757d;
}
.ui-resizable-handle {
    width: 10px;
    background-color: #6c757d;
    opacity: 0.5;
}
.ui-resizable-handle:hover {
    opacity: 1;
}
</style>

<script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
<script>
$(document).ready(function() {
    // 初始化档位数据
    var tiersData = <?= $tiersJson ?>;
    var minRV = 4; // 全局最小RV
    var maxRV = 30; // 全局最大RV
    var containerWidth = $('#rv-slider').width();
    var scale = containerWidth / (maxRV - minRV);
    
    // 渲染档位滑块
    function renderTiers() {
        $('#rv-slider').empty();
        
        tiersData.forEach(function(tier, index) {
            var left = (tier.min_rv - minRV) * scale;
            var width = (tier.max_rv - tier.min_rv) * scale;
            
            var tierDiv = $('<div class="rv-tier" data-tier="' + tier.tier + '"></div>')
                .css({
                    left: left + 'px',
                    width: width + 'px'
                });
            
            var labelDiv = $('<div class="rv-tier-label">档位 ' + tier.tier + '</div>');
            var valueDiv = $('<div class="rv-tier-value">' + tier.min_rv + ' - ' + tier.max_rv + '</div>');
            
            tierDiv.append(labelDiv).append(valueDiv);
            $('#rv-slider').append(tierDiv);
            
            // 使档位可拖动和调整大小
            tierDiv.resizable({
                handles: 'e, w',
                containment: '#rv-slider',
                grid: [scale * 0.01, 0], // 0.01单位的网格
                resize: function(event, ui) {
                    var tierIndex = tiersData.findIndex(t => t.tier == $(this).data('tier'));
                    var currentTier = tiersData[tierIndex];

                    var newMinRV = parseFloat((minRV + (ui.position.left / scale)).toFixed(2));
                    var newMaxRV = parseFloat((minRV + ((ui.position.left + ui.size.width) / scale)).toFixed(2));

                    // 限制左侧
                    var leftLimit = (tierIndex > 0) ? tiersData[tierIndex-1].max_rv : minRV;
                    if (newMinRV < leftLimit) {
                        newMinRV = leftLimit;
                    }

                    // 限制右侧
                    var rightLimit = (tierIndex < tiersData.length - 1) ? tiersData[tierIndex+1].min_rv : maxRV;
                    if (newMaxRV > rightLimit) {
                        newMaxRV = rightLimit;
                    }
                    
                    if(newMinRV >= newMaxRV){
                        if ($(event.originalEvent.target).hasClass('ui-resizable-w')) { // Resizing from west
                            newMinRV = newMaxRV - 0.01;
                        } else { // Resizing from east
                            newMaxRV = newMinRV + 0.01;
                        }
                    }

                    currentTier.min_rv = newMinRV;
                    currentTier.max_rv = newMaxRV;
                    
                    ui.position.left = (currentTier.min_rv - minRV) * scale;
                    ui.size.width = (currentTier.max_rv - currentTier.min_rv) * scale;

                    $(this).find('.rv-tier-value').text(currentTier.min_rv + ' - ' + currentTier.max_rv);
                    updateTableInputs();
                }
            });
            
            tierDiv.draggable({
                axis: 'x',
                containment: '#rv-slider',
                grid: [scale * 0.01, 0],
                drag: function(event, ui) {
                    var tierIndex = tiersData.findIndex(t => t.tier == $(this).data('tier'));
                    var currentTier = tiersData[tierIndex];
                    var widthInRV = currentTier.max_rv - currentTier.min_rv;

                    var newMinRV = parseFloat((minRV + (ui.position.left / scale)).toFixed(2));
                    var newMaxRV = parseFloat((newMinRV + widthInRV).toFixed(2));

                    // 限制左侧
                    var leftLimit = (tierIndex > 0) ? tiersData[tierIndex-1].max_rv : minRV;
                    if (newMinRV < leftLimit) {
                        newMinRV = leftLimit;
                        newMaxRV = newMinRV + widthInRV;
                    }

                    // 限制右侧
                    var rightLimit = (tierIndex < tiersData.length - 1) ? tiersData[tierIndex+1].min_rv : maxRV;
                    if (newMaxRV > rightLimit) {
                        newMaxRV = rightLimit;
                        newMinRV = newMaxRV - widthInRV;
                    }
                    
                    currentTier.min_rv = newMinRV;
                    currentTier.max_rv = newMaxRV;
                    
                    ui.position.left = (currentTier.min_rv - minRV) * scale;

                    $(this).find('.rv-tier-value').text(currentTier.min_rv + ' - ' + currentTier.max_rv);
                    updateTableInputs();
                }
            });
        });
        
        // 添加刻度线
        for (var i = Math.floor(minRV); i <= Math.ceil(maxRV); i += 2) {
            var tickLeft = (i - minRV) * scale;
            var tick = $('<div class="rv-tick"></div>').css({
                position: 'absolute',
                left: tickLeft + 'px',
                top: '85px',
                width: '1px',
                height: '10px',
                backgroundColor: '#adb5bd'
            });
            
            var label = $('<div class="rv-tick-label"></div>').css({
                position: 'absolute',
                left: (tickLeft - 10) + 'px',
                top: '100px',
                width: '20px',
                textAlign: 'center',
                fontSize: '0.7em',
                color: '#6c757d'
            }).text(i);
            
            $('#rv-slider').append(tick).append(label);
        }
    }
    
    // 更新表格中的输入值
    function updateTableInputs() {
        tiersData.forEach(function(tier) {
            var row = $('.tier-row[data-tier="' + tier.tier + '"]');
            row.find('.min-rv').val(tier.min_rv);
            row.find('.max-rv').val(tier.max_rv);
            row.find('.interval-length').text((tier.max_rv - tier.min_rv).toFixed(2));
        });
    }
    
    // 初始渲染
    renderTiers();
    
    // 保存按钮事件
    $('#save-slider').click(function() {
        $.ajax({
            url: 'refine_tiers.php',
            method: 'POST',
            data: {
                update_tiers_ajax: true,
                tier_data: JSON.stringify(tiersData)
            },
            success: function(response) {
                if (response.success) {
                    alert('档位区间已成功保存');
                } else {
                    alert('保存失败: ' + response.message);
                }
            },
            error: function() {
                alert('保存请求失败');
            }
        });
    });
    
    // 监听表格输入变化
    $('.min-rv, .max-rv').change(function() {
        var row = $(this).closest('.tier-row');
        var tier = row.data('tier');
        var tierIndex = tiersData.findIndex(t => t.tier == tier);
        
        var minRVInput = row.find('.min-rv');
        var maxRVInput = row.find('.max-rv');
        
        var newMinRV = parseFloat(minRVInput.val());
        var newMaxRV = parseFloat(maxRVInput.val());
        
        // 验证输入
        if (newMinRV >= newMaxRV) {
            alert('最小RV值必须小于最大RV值');
            minRVInput.val(tiersData[tierIndex].min_rv);
            maxRVInput.val(tiersData[tierIndex].max_rv);
            return;
        }
        
        // 确保不超过左右边界
        if (tierIndex > 0 && newMinRV < tiersData[tierIndex-1].max_rv) {
            alert('最小RV值不能小于上一档位的最大RV值');
            minRVInput.val(tiersData[tierIndex-1].max_rv);
            newMinRV = tiersData[tierIndex-1].max_rv;
        }
        
        if (tierIndex < tiersData.length - 1 && newMaxRV > tiersData[tierIndex+1].min_rv) {
            alert('最大RV值不能大于下一档位的最小RV值');
            maxRVInput.val(tiersData[tierIndex+1].min_rv);
            newMaxRV = tiersData[tierIndex+1].min_rv;
        }
        
        tiersData[tierIndex].min_rv = newMinRV;
        tiersData[tierIndex].max_rv = newMaxRV;
        
        // 更新区间长度
        row.find('.interval-length').text((newMaxRV - newMinRV).toFixed(2));
        
        // 重新渲染滑块
        renderTiers();
    });
    
    // 添加新档位
    $('#add-tier').click(function() {
        var lastTier = tiersData[tiersData.length - 1];
        var newTier = {
            tier: lastTier.tier + 1,
            min_rv: lastTier.max_rv,
            max_rv: Math.min(lastTier.max_rv + 2, maxRV),
            prefix_equipment: '新档位'
        };
        
        // 添加到数据
        tiersData.push(newTier);
        
        // 添加到表格
        var newRow = `
            <tr class="tier-row" data-tier="${newTier.tier}">
                <td>${newTier.tier}</td>
                <td>
                    <input type="number" step="0.01" min="0" max="30" 
                        class="form-control min-rv" 
                        name="tiers[${newTier.tier}][min_rv]" 
                        value="${newTier.min_rv}" required>
                </td>
                <td>
                    <input type="number" step="0.01" min="0" max="30" 
                        class="form-control max-rv" 
                        name="tiers[${newTier.tier}][max_rv]" 
                        value="${newTier.max_rv}" required>
                </td>
                <td class="interval-length">
                    ${(newTier.max_rv - newTier.min_rv).toFixed(2)}
                </td>
                <td>
                    <input type="text" class="form-control" 
                        name="tiers[${newTier.tier}][prefix_equipment]" 
                        value="${newTier.prefix_equipment}" required>
                </td>
                <td>
                    <form method="post" action="" style="display:inline">
                        <input type="hidden" name="tier" value="${newTier.tier}">
                        <button type="submit" name="delete_tier" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个档位吗？')">删除</button>
                    </form>
                </td>
            </tr>
        `;
        
        $('table tbody').append(newRow);
        
        // 给新添加的输入框绑定事件
        $('.tier-row[data-tier="' + newTier.tier + '"] .min-rv, .tier-row[data-tier="' + newTier.tier + '"] .max-rv').change(function() {
            var row = $(this).closest('.tier-row');
            var tier = row.data('tier');
            var tierIndex = tiersData.findIndex(t => t.tier == tier);
            
            var minRVInput = row.find('.min-rv');
            var maxRVInput = row.find('.max-rv');
            
            var newMinRV = parseFloat(minRVInput.val());
            var newMaxRV = parseFloat(maxRVInput.val());
            
            if (newMinRV >= newMaxRV) {
                alert('最小RV值必须小于最大RV值');
                minRVInput.val(tiersData[tierIndex].min_rv);
                maxRVInput.val(tiersData[tierIndex].max_rv);
                return;
            }
            
            if (tierIndex > 0 && newMinRV < tiersData[tierIndex-1].max_rv) {
                alert('最小RV值不能小于上一档位的最大RV值');
                minRVInput.val(tiersData[tierIndex-1].max_rv);
                newMinRV = tiersData[tierIndex-1].max_rv;
            }
            
            if (tierIndex < tiersData.length - 1 && newMaxRV > tiersData[tierIndex+1].min_rv) {
                alert('最大RV值不能大于下一档位的最小RV值');
                maxRVInput.val(tiersData[tierIndex+1].min_rv);
                newMaxRV = tiersData[tierIndex+1].min_rv;
            }
            
            tiersData[tierIndex].min_rv = newMinRV;
            tiersData[tierIndex].max_rv = newMaxRV;
            
            row.find('.interval-length').text((newMaxRV - newMinRV).toFixed(2));
            
            renderTiers();
        });
        
        // 重新渲染滑块
        renderTiers();
    });
    
    // 窗口调整大小时重新渲染
    $(window).resize(function() {
        containerWidth = $('#rv-slider').width();
        scale = containerWidth / (maxRV - minRV);
        renderTiers();
    });
});
</script>

<?php include 'layout_footer.php'; ?> 