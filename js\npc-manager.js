/**
 * NPC管理器类
 * 处理游戏中NPC的显示、交互和对话功能
 */
class NPCManager {
    constructor(gameClient) {
        this.game = gameClient;
        this.currentDialogue = null;
        this.dialogueHistory = [];
        this.currentNpc = null;
        
        // 初始化对话视图
        this.initDialogueView();
        // 初始化NPC详情视图
        this.initNpcDetailView();
    }
    
    /**
     * 初始化对话界面
     */
    initDialogueView() {
        // 创建对话界面元素
        const dialogueView = document.createElement('div');
        dialogueView.id = 'dialogueView';
        dialogueView.className = 'view';
        dialogueView.style.display = 'none';
        dialogueView.innerHTML = `
            <h4 id="npcDialogueName" class="dialogue-title"></h4>
            <div id="dialogueContainer"></div>
            <div id="dialogueOptions" class="dialogue-options"></div>
        `;
        document.getElementById('gameContent').appendChild(dialogueView);
        
        // 添加事件监听器
        document.getElementById('dialogueOptions').addEventListener('click', (event) => {
            const target = event.target;
            if (target.classList.contains('dialogue-option-btn')) {
                const optionId = target.dataset.optionId;
                this.selectDialogueOption(optionId);
            } else if (target.classList.contains('dialogue-continue-btn') && !target.classList.contains('dialogue-close-btn')) {
                // 只处理继续对话按钮，不处理关闭对话按钮
                this.continueDialogue();
            }
        });
    }
    
    /**
     * 初始化NPC详情视图
     */
    initNpcDetailView() {
        // 创建NPC详情视图元素
        const npcDetailView = document.createElement('div');
        npcDetailView.id = 'npcDetailView';
        npcDetailView.className = 'view';
        npcDetailView.style.display = 'none';
        npcDetailView.innerHTML = `
            <h4 id="npcDetailName" class="npc-detail-title"></h4>
            <div id="npcDetailContent" class="npc-detail-content"></div>
            <div id="npcDetailMessage" class="npc-detail-message" style="display: none; margin-top: 10px; padding: 5px; border-radius: 4px;"></div>
            <div id="npcDetailActions" class="npc-detail-actions"></div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn-primary" onclick="game.npcManager.hideNpcDetailView()">[返回]</button>
            </div>
        `;
        document.getElementById('gameContent').appendChild(npcDetailView);
    }
    
    /**
     * 显示对话界面
     */
    showDialogueView() {
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('npcDetailView').style.display = 'none';
        document.getElementById('dialogueView').style.display = 'block';
    }
    
    /**
     * 隐藏对话界面
     */
    hideDialogueView() {
        document.getElementById('dialogueView').style.display = 'none';
        
        // 如果是从NPC详情过来的，返回NPC详情视图
        if (this.currentNpc && document.getElementById('npcDetailView').style.display === 'none') {
            document.getElementById('npcDetailView').style.display = 'block';
        } else {
            document.getElementById('main-view').style.display = 'block';
        }
        
        // 清空对话历史，以便下次开始新对话
        this.dialogueHistory = [];
        this.currentDialogue = null;
    }
    
    /**
     * 显示NPC详情视图
     */
    showNpcDetailView() {
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('dialogueView').style.display = 'none';
        
        // 确保隐藏所有任务相关的视图
        if (document.getElementById('questView')) {
            document.getElementById('questView').style.display = 'none';
        }
        if (document.getElementById('questLogView')) {
            document.getElementById('questLogView').style.display = 'none';
        }
        if (document.getElementById('npcQuestView')) {
            document.getElementById('npcQuestView').style.display = 'none';
        }
        
        document.getElementById('npcDetailView').style.display = 'block';
    }
    
    /**
     * 隐藏NPC详情视图
     */
    hideNpcDetailView() {
        document.getElementById('npcDetailView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentNpc = null;
        // 清除savedNpcData，避免影响后续物品详情返回逻辑
        this.savedNpcData = null;
    }
    
    /**
     * 查看NPC详情
     * @param {number} npcId NPC ID
     */
    viewNpcDetail(npcId) {
        this.currentNpcId = npcId;
        // 重置对话标志
        this.dialoguePreloaded = false;

        // 现有的NPC详情请求（服务器端已经集成了对话检查，不需要单独的预加载请求）
        this.game.sendMessage(MessageProtocol.C2S_VIEW_NPC_DETAIL, { npc_id: npcId });

        // 注释掉单独的预加载请求，避免重复检查
        // this.game.sendMessage(MessageProtocol.C2S_PRELOAD_DIALOGUE, { npc_id: npcId });
    }
    
    /**
     * 处理NPC详情数据
     * @param {object} payload 服务器返回的NPC详情数据
     */
    handleNpcDetail(payload) {
        this.currentNpc = payload.npc;
        
        // 调试输出
        console.log('收到NPC详情数据:', payload);
        console.log('NPC是否有任务标志:', this.currentNpc.has_quests_for_player);
        console.log('NPC是否是任务发布者:', this.currentNpc.is_quest_giver);
        console.log('NPC装备数据:', payload.inventory);
        
        // 服务器端已经集成了对话检查，直接使用服务器返回的has_dialogue标志
        console.log('NPC对话标志:', this.currentNpc.has_dialogue);
        
        // 获取NPC装备信息（新增）- 移到前面，确保在显示详情前设置
        if (payload.inventory) {
            console.log('设置NPC装备数据，数量:', payload.inventory.length);
            this.currentNpc.inventory = payload.inventory;
        } else {
            console.log('服务器未返回NPC装备数据');
            // 确保inventory字段存在，即使为空数组
            this.currentNpc.inventory = [];
        }
        
        // 如果NPC有任务标志，先获取任务列表再显示NPC详情
        if (this.currentNpc.has_quests_for_player && this.game.questManager) {
            console.log('NPC有任务标志，先获取任务列表');
            // 保存当前NPC信息，确保包含装备数据
            const currentNpcData = {...this.currentNpc};
            
            // 获取任务列表
            this.game.questManager.getNpcQuests(this.currentNpc.id, () => {
                // 任务列表获取完成后的回调
                this.displayNpcDetailWithQuestInfo(currentNpcData);
            });
        } else {
            // 没有任务标志，直接显示NPC详情
            this.displayNpcDetailWithQuestInfo(this.currentNpc);
        }
    }
    
    /**
     * 显示NPC详情，包括任务信息
     * @param {object} npcData NPC数据
     */
    displayNpcDetailWithQuestInfo(npcData) {
        // 确保当前NPC数据是最新的
        this.currentNpc = npcData;
        
        // 显示NPC详情视图
        this.showNpcDetailView();
        
        // 更新NPC名称
        document.getElementById('npcDetailName').textContent = this.currentNpc.name;
        
        // 更新NPC详情内容
        let detailContent = '';

        if (this.currentNpc.description) {
            detailContent += `<div class="npc-info-row"><span class="npc-info-description">${this.currentNpc.description}</span></div>`;
        }
        
        if (this.currentNpc.is_merchant) {
            detailContent += `<div class="npc-info-row"><span class="npc-type-tag merchant-tag">商人</span></div>`;
        }
        
        if (this.currentNpc.is_quest_giver) {
            detailContent += `<div class="npc-info-row"><span class="npc-type-tag quest-tag">任务</span></div>`;
        }
        
        // 新增：显示NPC装备信息
        if (this.currentNpc.inventory && this.currentNpc.inventory.length > 0) {
            detailContent += `<div class="npc-info-row npc-equipment-section">
                <h4>装备</h4>
                <div class="npc-equipment-list">`;
            
            this.currentNpc.inventory.forEach(item => {
                if (item.is_equipped) {
                    detailContent += `
                        <div class="npc-equipment-item">
                            <div class="equipment-slot-name">${this.getSlotName(item.slot)}</div>
                            <div class="equipment-item-name" data-item-id="${item.item_template_id}" style="color: #3366cc; cursor: pointer; text-decoration: underline;">${item.name}</div>
                        </div>`;
                }
            });
            
            detailContent += `</div></div>`;
        }
        
        document.getElementById('npcDetailContent').innerHTML = detailContent;
        
        // 添加装备物品点击事件
        console.log('正在为装备项添加点击事件，找到项数：', document.querySelectorAll('.equipment-item-name').length);
        
        // 先移除可能存在的旧事件监听器
        const equipmentItems = document.querySelectorAll('.equipment-item-name');
        equipmentItems.forEach(item => {
            const newItem = item.cloneNode(true);
            item.parentNode.replaceChild(newItem, item);
        });
        
        // 重新添加事件监听器
        document.querySelectorAll('.equipment-item-name').forEach(item => {
            console.log('为装备项添加点击事件:', item.textContent, '物品ID:', item.getAttribute('data-item-id'));
            
            // 直接使用onclick属性
            item.onclick = (e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log('装备项被点击:', e.target.textContent);
                const itemId = item.getAttribute('data-item-id');
                console.log('获取到物品ID:', itemId);
                this.viewItemDetail(itemId);
                return false;
            };
            
            // 添加明显的视觉提示
            item.title = "点击查看物品详情";
        });
        
        // 更新NPC详情操作按钮
        const actionsContainer = document.getElementById('npcDetailActions');
        actionsContainer.innerHTML = '';
        
        // 检查是否有与NPC相关的可完成任务或进行中任务
        let hasActiveOrCompletableQuests = this.currentNpc.has_quests_for_player;
        
        // 如果有任务管理器，可以更精确地检查
        if (this.game.questManager) {
            // 检查是否有可完成的任务
            if (this.game.questManager.completableQuestsForNpc && 
                this.game.questManager.completableQuestsForNpc.length > 0) {
                hasActiveOrCompletableQuests = true;
            }
            
            // 检查是否有与该NPC相关的进行中任务
            if (this.game.questManager.relatedActiveQuests && 
                this.game.questManager.relatedActiveQuests.length > 0) {
                hasActiveOrCompletableQuests = true;
            }
        }
        
        // 如果NPC有可完成任务或接收进行中任务，添加任务按钮
        if (hasActiveOrCompletableQuests) {
            console.log('添加任务按钮 - NPC有可完成或进行中任务或任务标志');
            const questButton = document.createElement('button');
            questButton.className = 'btn-primary';
            questButton.textContent = '[任务]';
            questButton.addEventListener('click', () => {
                // 从NPC获取任务列表
                this.game.questManager.getNpcQuests(this.currentNpc.id);
            });
            actionsContainer.appendChild(questButton);
        } else {
            console.log('不添加任务按钮 - NPC没有可完成或进行中任务');
        }
        
        // 如果NPC有对话功能，添加对话按钮
        if (this.currentNpc.has_dialogue) {
            const talkButton = document.createElement('button');
            talkButton.className = 'btn-primary';
            talkButton.textContent = '[对话]';
            talkButton.style.marginLeft = actionsContainer.childElementCount > 0 ? '10px' : '0';
            talkButton.addEventListener('click', () => this.talkToNpc(this.currentNpc.id));
            actionsContainer.appendChild(talkButton);
        }
        
        // 如果是商人，添加交易按钮 暂时关闭
        // if (this.currentNpc.is_merchant) {
        //     const tradeButton = document.createElement('button');
        //     tradeButton.className = 'btn-primary';
        //     tradeButton.textContent = '[交易]';
        //     tradeButton.style.marginLeft = actionsContainer.childElementCount > 0 ? '10px' : '0';
        //     tradeButton.addEventListener('click', () => {
        //         // 这里可以添加交易功能
        //         if (typeof this.game.tradingManager !== 'undefined' && this.game.tradingManager.openTradeWindow) {
        //             this.game.tradingManager.openTradeWindow(this.currentNpc.id);
        //         } else {
        //             console.log('交易功能尚未实现');
        //         }
        //     });
        //     actionsContainer.appendChild(tradeButton);
        // }
    }
    
    /**
     * 新增：获取装备槽位名称
     * @param {string} slot 槽位代码
     * @return {string} 槽位名称
     */
    getSlotName(slot) {
        const slotNames = {
            'Head': '头部', 'Neck': '颈部', 'Body': '身体', 'Back': '背部',
            'LeftHand': '左手', 'RightHand': '右手', 'TwoHanded': '双手',
            'Finger': '手指'
        };
        return slotNames[slot] || slot;
    }
    
    /**
     * 新增：查看物品详情
     * @param {number} itemId 物品ID
     */
    viewItemDetail(itemId) {
        console.log('viewItemDetail被调用，物品ID:', itemId);
        // 在这里添加查看物品详情的逻辑
        // 可以发送请求到服务器获取物品详情，或者直接使用缓存的数据
        
        // 示例：从NPC的装备中查找物品
        let itemDetail = null;
        console.log('当前NPC:', this.currentNpc);
        console.log('NPC装备:', this.currentNpc?.inventory);
        
        if (this.currentNpc && this.currentNpc.inventory) {
            console.log('开始查找物品，装备数量:', this.currentNpc.inventory.length);
            this.currentNpc.inventory.forEach(item => {
                console.log('检查物品:', item.name, '物品模板ID:', item.item_template_id, '对比ID:', itemId);
                if (item.item_template_id == itemId) {
                    console.log('找到匹配物品:', item.name);
                    itemDetail = item;
                }
            });
        }
        
        if (itemDetail) {
            console.log('准备显示物品详情:', itemDetail);
            // 显示物品详情
            // this.game.addLog(`物品详情: ${itemDetail.name}\n${itemDetail.description || '无描述'}`, 'system');
            
            // 使用更复杂的UI显示物品详情
            this.showItemDetailPopup(itemDetail);
        } else {
            console.log('未找到物品详情，物品ID:', itemId);
            this.game.addLog(`无法获取物品详情`, 'error');
        }
    }
    
    /**
     * 显示物品详情页面
     * @param {object} item 物品数据
     */
    showItemDetailPopup(item) {
        console.log('显示物品详情页面:', item);
        
        // 隐藏主视图和NPC详情视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('npcDetailView').style.display = 'none';
        
        // 获取物品详情视图
        const itemDetailView = document.getElementById('itemDetailView');
        if (!itemDetailView) {
            console.error('找不到物品详情视图元素');
            return;
        }
        
        // 物品类型中文名称
        const categoryNames = {
            'Equipment': '装备',
            'Gem': '宝石',
            'Material': '材料',
            'Potion': '药水',
            'Rune': '符文',
            'Misc': '杂项',
            'Scroll': '卷轴'
        };
        
        // 设置物品名称
        document.getElementById('itemDetailName').textContent = item.name;
        
        // 清空错误和成功消息
        document.getElementById('itemDetailError').textContent = '';
        document.getElementById('itemDetailSuccess').textContent = '';
        
        // 构建物品详情内容
        let contentHtml = `<p><b>分类:</b> ${categoryNames[item.category] || item.category}</p>`;
        
        if (item.description) {
            contentHtml += `<p style="color: #555;">${item.description}</p>`;
        }
        
        contentHtml += `<hr class="section-divider">`;
        
        // 如果是装备，显示装备位置
        if (item.is_equipped) {
            contentHtml += `<p><b>装备位置:</b> <span style="color: #28a745; font-weight: bold;">[${this.getSlotName(item.slot)}]</span></p>`;
        }
        
        // 如果有数量信息，显示数量
        if (item.quantity > 1) {
            contentHtml += `<p><b>数量:</b> ${item.quantity}</p>`;
        }
        
        // 更新物品详情内容
        document.getElementById('itemDetailContent').innerHTML = contentHtml;
        
        // 清空操作按钮区域
        document.getElementById('itemDetailActions').innerHTML = '';
        
        // 显示物品详情视图
        itemDetailView.style.display = 'block';
        
        // 保存当前NPC信息，以便返回时恢复
        this.savedNpcData = this.currentNpc;
    }
    
    /**
     * 隐藏物品详情视图，返回NPC详情
     */
    hideItemDetailView() {
        // 隐藏物品详情视图
        document.getElementById('itemDetailView').style.display = 'none';
        
        // 如果有保存的NPC数据，恢复NPC详情视图
        if (this.savedNpcData) {
            this.displayNpcDetailWithQuestInfo(this.savedNpcData);
        } else {
            // 否则返回主视图
            document.getElementById('main-view').style.display = 'block';
        }
    }
    
    /**
     * 与NPC对话
     * @param {number} npcId NPC ID
     */
    talkToNpc(npcId) {
        // 清空历史记录，开始新对话
        this.dialogueHistory = [];
        this.currentDialogue = null;
        
        // 如果任务视图是打开的，先隐藏它
        if (document.getElementById('npcQuestView').style.display === 'block') {
            document.getElementById('npcQuestView').style.display = 'none';
        }
        
        // 清除之前的错误消息并显示加载中
        this.showNpcDetailMessage('正在与NPC对话...', 'info');
        
        // 设置一个全局错误处理器来捕获对话错误并显示在NPC界面上
        const originalErrorHandler = this.game.handleError;
        this.game.handleError = (message, context = null) => {
            // 如果是对话相关的错误，显示在NPC界面上
            if (context === 'dialogue' || (!context && this.currentNpcId === npcId)) {
                this.showNpcDetailMessage(message, 'error');
            } else {
                // 否则使用原始错误处理器
                originalErrorHandler.call(this.game, message, context);
            }
        };
        
        // 发送请求到服务器
        this.game.sendMessage(MessageProtocol.C2S_TALK_TO_NPC, { npc_id: npcId });
        
        // 在短暂延迟后恢复原始错误处理器
        setTimeout(() => {
            this.game.handleError = originalErrorHandler;
        }, 2000);
    }
    
    /**
     * 在NPC详情界面显示消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (error, success, info)
     */
    showNpcDetailMessage(message, type = 'info') {
        const messageEl = document.getElementById('npcDetailMessage');
        if (!messageEl) return;
        
        // 设置消息样式
        messageEl.className = 'npc-detail-message';
        
        // 根据消息类型设置不同的样式和前缀
        let icon = '';
        switch (type) {
            case 'error':
                icon = '❌ ';
                messageEl.style.backgroundColor = '#ffebee';
                messageEl.style.color = '#d32f2f';
                messageEl.style.border = '1px solid #ef9a9a';
                break;
            case 'success':
                icon = '✅ ';
                messageEl.style.backgroundColor = '#e8f5e9';
                messageEl.style.color = '#2e7d32';
                messageEl.style.border = '1px solid #a5d6a7';
                break;
            case 'info':
            default:
                icon = 'ℹ️ ';
                messageEl.style.backgroundColor = '#e3f2fd';
                messageEl.style.color = '#1565c0';
                messageEl.style.border = '1px solid #90caf9';
                break;
        }
        
        // 设置消息内容
        messageEl.textContent = icon + message;
        
        // 显示消息
        messageEl.style.display = 'block';
        
        // 如果是错误消息，保持显示，否则3秒后自动隐藏
        if (type !== 'error') {
            setTimeout(() => {
                if (messageEl) {
                    messageEl.style.display = 'none';
                }
            }, 3000);
        }
    }
    
    /**
     * 清除NPC详情界面的消息
     */
    clearNpcDetailMessage() {
        const messageEl = document.getElementById('npcDetailMessage');
        if (messageEl) {
            messageEl.style.display = 'none';
            messageEl.textContent = '';
        }
    }
    
    /**
     * 处理NPC对话数据
     * @param {object} payload 服务器返回的对话数据
     */
    handleNpcDialogue(payload) {
        console.log('处理NPC对话数据:', payload);

        // 清除加载或错误消息
        this.clearNpcDetailMessage();

        // 处理错误情况
        if (payload.error) {
            this.showNpcDetailMessage(payload.error, 'error');
            return;
        }

        this.currentNpc = payload.npc;
        this.currentDialogue = payload.dialogue;

        // 始终使用服务器返回的历史记录，避免客户端和服务器端重复添加
        if (payload.history !== undefined) {
            console.log('更新对话历史记录，长度:', payload.history.length);
            this.dialogueHistory = payload.history;
        }

        // 【新增】处理服务器返回的动作结果
        if (payload.action_results && Array.isArray(payload.action_results)) {
            console.log('处理动作结果:', payload.action_results);
            this.processActionResults(payload.action_results);
        }

        // 显示对话界面
        this.showDialogueView();

        // 更新NPC名称
        document.getElementById('npcDialogueName').textContent = this.currentNpc.name;

        // 延迟更新对话内容，确保DOM完全准备好
        setTimeout(() => {
            // 更新对话内容
            this.updateDialogueContainer();

            // 更新对话选项
            this.updateDialogueOptions();
        }, 10);

        console.log('当前对话历史记录:', this.dialogueHistory);
        console.log('当前对话:', this.currentDialogue);
    }
    
    /**
     * 【新增】处理对话动作的结果
     * @param {Array} results - 服务器返回的动作结果数组
     */
    processActionResults(results) {
        if (!results || results.length === 0) {
            return;
        }

        results.forEach(result => {
            if (!result) return;

            // 处理特殊的任务相关消息类型
            switch (result.type) {
                case 'quest_started':
                    // 处理任务开始
                    if (result.quest_id && this.game.questManager) {
                        // 显示任务开始通知
                        this.game.questManager.showNotification(`✅ ${result.message}`, 'success');
                        this.game.addLog(`📜 ${result.message}`);

                        // 延迟刷新任务列表，避免干扰当前对话流程
                        setTimeout(() => {
                            if (this.game.questManager) {
                                this.game.questManager.getPlayerQuests();
                            }
                        }, 100);
                    }
                    break;

                case 'quest_progress':
                    // 处理任务进度更新
                    if (result.quest_id && result.objective_id && this.game.questManager) {
                        // 构造标准的任务更新数据格式
                        const questUpdateData = {
                            action: 'progress_updated',
                            quest_id: result.quest_id,
                            objective_id: result.objective_id,
                            new_progress: result.new_progress,
                            target: result.target,
                            can_complete: result.can_complete,
                            description: result.message || `进度更新 (${result.new_progress}/${result.target})`,
                            receiver_npc_name: result.receiver_npc_name
                        };

                        // 使用任务管理器的标准处理方法
                        this.game.questManager.handleQuestUpdateData(questUpdateData);
                    } else {
                        // 如果缺少必要信息，退回到普通消息处理
                        this.game.handleInfoMessage(result);
                    }
                    break;

                default:
                    // 其他类型的消息使用通用信息处理器
                    this.game.handleInfoMessage(result);
                    break;
            }
        });
    }
    
    /**
     * 更新对话内容区域
     */
    updateDialogueContainer() {
        const container = document.getElementById('dialogueContainer');
        if (!container) {
            console.error('找不到对话容器元素');
            return;
        }

        container.innerHTML = '';

        // 创建一个包装器来保证对话内容的正确显示
        const dialogueWrapper = document.createElement('div');
        dialogueWrapper.className = 'dialogue-wrapper';

        console.log('更新对话容器，历史记录数量:', this.dialogueHistory ? this.dialogueHistory.length : 0);
        console.log('当前对话:', this.currentDialogue);

        // 只显示历史对话，服务器已经将所有对话都包含在历史记录中
        if (this.dialogueHistory && this.dialogueHistory.length > 0) {
            // 使用Set来跟踪已添加的对话ID，避免重复
            const addedDialogueIds = new Set();

            this.dialogueHistory.forEach((dialogue, index) => {
                // 检查是否已经添加过这个对话
                if (!addedDialogueIds.has(dialogue.id)) {
                    const dialogueElement = document.createElement('div');
                    dialogueElement.className = dialogue.is_player ? 'player-dialogue' : 'npc-dialogue';
                    dialogueElement.textContent = dialogue.content;
                    dialogueWrapper.appendChild(dialogueElement);
                    addedDialogueIds.add(dialogue.id);
                    console.log(`添加对话 ${index + 1}:`, dialogue.content);
                } else {
                    console.log(`跳过重复对话 ${index + 1}:`, dialogue.content);
                }
            });
        }

        // 添加包装器到容器
        container.appendChild(dialogueWrapper);

        // 使用更可靠的方法确保滚动到底部
        this.scrollDialogueToBottom();
    }
    
    /**
     * 确保对话滚动到底部的可靠方法
     */
    scrollDialogueToBottom() {
        const container = document.getElementById('dialogueContainer');
        if (!container) return;
        
        // 立即尝试滚动
        container.scrollTop = container.scrollHeight;
        
        // 使用多个延时来确保在DOM更新后滚动到底部
        const scrollAttempts = [10, 50, 100, 200];
        scrollAttempts.forEach(delay => {
            setTimeout(() => {
                if (container) {
                    container.scrollTop = container.scrollHeight;
                }
            }, delay);
        });
    }
    
    /**
     * 更新对话选项区域
     */
    updateDialogueOptions() {
        const optionsContainer = document.getElementById('dialogueOptions');
        optionsContainer.innerHTML = '';
        
        if (!this.currentDialogue) {
            // 如果对话结束，显示关闭按钮
            const closeButton = document.createElement('button');
            closeButton.className = 'dialogue-continue-btn dialogue-close-btn';
            closeButton.textContent = '关闭对话';
            closeButton.addEventListener('click', () => this.hideDialogueView());
            optionsContainer.appendChild(closeButton);
            return;
        }
        
        if (this.currentDialogue.is_player_choice && this.currentDialogue.options && this.currentDialogue.options.length > 0) {
            // 玩家选择选项
            const optionsTitle = document.createElement('div');
            optionsTitle.className = 'dialogue-options-title';
            optionsTitle.textContent = '选择回应：';
            optionsContainer.appendChild(optionsTitle);

            const optionsList = document.createElement('div');
            optionsList.className = 'dialogue-options-list';

            this.currentDialogue.options.forEach(option => {
                const optionButton = document.createElement('button');
                optionButton.className = 'dialogue-option-btn';
                optionButton.textContent = option.content;
                optionButton.dataset.optionId = option.id;
                // 确保按钮是启用状态
                optionButton.disabled = false;
                optionButton.style.opacity = '1';
                optionsList.appendChild(optionButton);
            });

            optionsContainer.appendChild(optionsList);
        } else if (!this.currentDialogue.is_player_choice && this.currentDialogue.next_node_ids && this.currentDialogue.next_node_ids.length > 0) {
            // 继续对话按钮
            const continueButton = document.createElement('button');
            continueButton.className = 'dialogue-continue-btn';
            continueButton.textContent = '继续';
            optionsContainer.appendChild(continueButton);
        } else {
            // 对话结束按钮
            const closeButton = document.createElement('button');
            closeButton.className = 'dialogue-continue-btn dialogue-close-btn';
            closeButton.textContent = '结束对话';
            closeButton.addEventListener('click', () => this.hideDialogueView());
            optionsContainer.appendChild(closeButton);
        }
        
        // 每次更新选项后再次确保滚动到底部
        this.scrollDialogueToBottom();
    }
    
    /**
     * 继续对话
     */
    continueDialogue() {
        if (!this.currentNpc || !this.currentDialogue) return;
        
        // 发送继续对话请求给服务器，让服务器处理历史记录
        this.game.sendMessage(MessageProtocol.C2S_CONTINUE_DIALOGUE, {
            npc_id: this.currentNpc.id,
            node_id: this.currentDialogue.id,
            history: this.dialogueHistory
        });
    }
    
    /**
     * 选择对话选项
     * @param {number} optionId 选项ID
     */
    selectDialogueOption(optionId) {
        if (!this.currentNpc || !this.currentDialogue) {
            console.error('选择对话选项失败：缺少NPC或对话数据');
            return;
        }

        console.log(`选择对话选项: ${optionId}, NPC: ${this.currentNpc.id}, 节点: ${this.currentDialogue.id}`);

        // 禁用所有对话选项按钮，防止重复点击
        const optionButtons = document.querySelectorAll('.dialogue-option-btn');
        optionButtons.forEach(btn => {
            btn.disabled = true;
            btn.style.opacity = '0.6';
        });

        // 发送选择给服务器，让服务器处理历史记录
        this.game.sendMessage(MessageProtocol.C2S_SELECT_DIALOGUE_OPTION, {
            npc_id: this.currentNpc.id,
            node_id: this.currentDialogue.id,
            option_id: optionId,
            history: this.dialogueHistory
        });
    }
    
    /**
     * 更新场景信息中的NPC显示
     */
    updateSceneNpcs() {
        if (!this.game.currentScene || !this.game.currentScene.npcs) return;
        
        const npcsEl = document.getElementById('sceneNpcs');
        if (!npcsEl) return;
        
        npcsEl.innerHTML = '';
        
        const npcs = this.game.currentScene.npcs;
        
        // 确保展开状态保留，但对于少量NPC默认展开
        if (npcsEl.dataset.isExpanded === undefined) {
            // 3个或以下NPC默认展开，超过3个默认折叠
            npcsEl.dataset.isExpanded = npcs.length <= 3 ? 'true' : 'false';
        }
        const isNpcsExpanded = npcsEl.dataset.isExpanded === 'true';
        
        if (npcs.length > 0) {
            npcsEl.style.display = 'block';
            
            let html = '<b style="vertical-align: middle;">你看到：</b> ';
            
            // 只有超过3个NPC时才显示展开/收起按钮
            if (npcs.length > 3) {
                html += isNpcsExpanded 
                    ? `<a href="#" class="toggle-npcs">[收起]</a> `
                    : `<a href="#" class="toggle-npcs">[展开]</a> (${npcs.length}) `;
            }
            
            if (isNpcsExpanded || npcs.length <= 3) {
                html += '<div class="scene-npcs-list">';
                
                npcs.forEach(npc => {
                    const npcTypeClass = this.getNpcTypeClass(npc);
                    const npcTypeIcon = this.getNpcTypeIcon(npc);

                    html += `
                    <div class="scene-npc-group ${npcTypeClass}" data-npc-id="${npc.id}">
                        <span class="npc-type-icon">${npcTypeIcon}</span>
                        <a href="#" class="npc-link" data-npc-id="${npc.id}">${npc.name}</a>
                    </div>`;
                });
                
                html += '</div>';
            }
            
            npcsEl.innerHTML = html;
            
            // 添加NPC点击事件 - 修改为查看NPC详情
            const npcLinks = npcsEl.querySelectorAll('.npc-link');
            npcLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const npcId = parseInt(link.dataset.npcId);
                    this.viewNpcDetail(npcId);
                });
            });
            
            // 添加展开/收起事件
            const toggleNpcsLink = npcsEl.querySelector('.toggle-npcs');
            if (toggleNpcsLink) {
                toggleNpcsLink.addEventListener('click', (e) => {
                    e.preventDefault();
                    npcsEl.dataset.isExpanded = isNpcsExpanded ? 'false' : 'true';
                    this.updateSceneNpcs();
                });
            }
        } else {
            npcsEl.style.display = 'none';
        }
    }
    
    /**
     * 获取NPC类型的CSS类名
     * @param {object} npc NPC对象
     * @returns {string} CSS类名
     */
    getNpcTypeClass(npc) {
        // 基于实际是否有任务和是否是商人来设置样式
        const hasQuests = npc.has_quests_for_player || false;
        const isMerchant = npc.is_merchant || false;

        if (isMerchant && hasQuests) {
            return 'merchant-quest-npc';
        } else if (isMerchant) {
            return 'merchant-npc';
        } else if (hasQuests) {
            return 'quest-npc';
        } else {
            return 'normal-npc';
        }
    }
    
    /**
     * 获取NPC类型的图标
     * @param {object} npc NPC对象
     * @returns {string} 图标HTML
     */
    getNpcTypeIcon(npc) {
        // 基于实际是否有任务和是否是商人来设置图标
        const hasQuests = npc.has_quests_for_player || false;
        const isMerchant = npc.is_merchant || false;

        if (isMerchant && hasQuests) {
            return '💰❗';
        } else if (isMerchant) {
            return '💰';
        } else if (hasQuests) {
            return '❗';
        } else {
            return '👤';
        }
    }
} 