/**
 * 任务管理器类
 * 处理游戏中的任务显示、交互和进度跟踪
 */
class QuestManager {
    constructor(gameClient) {
        this.game = gameClient;
        this.activeQuests = [];
        this.completedQuests = [];
        this.abandonedQuests = []; // 用于存储已放弃的任务，如果服务器提供
        this.allPlayerQuests = []; // 包含所有状态的玩家任务
        this.currentQuest = null; // 当前查看的任务
        this.npcQuestsCallback = null; // 用于存储NPC任务数据加载后的回调
        this.availableQuestsFromNpc = [];
        this.completableQuestsForNpc = [];
        this.relatedActiveQuests = []; // 与NPC相关的进行中任务
        this.currentNpcId = null;
        this.fromQuestLog = false; // 标记是否从任务日志进入任务详情
        this._currentQuestViewOrigin = null; // 用于跟踪任务详情视图的来源 ('questLog', 'npc')
        
        // 初始化任务视图
        this.initQuestView();
        this.initQuestLogView();
        this.initNpcQuestView();
    }
    
    /**
     * 初始化任务视图
     */
    initQuestView() {
        // 创建任务视图元素
        const questView = document.createElement('div');
        questView.id = 'questView';
        questView.className = 'view';
        questView.style.display = 'none';
        questView.innerHTML = `
            <h4 style="margin-top:0; text-align: center;">任务信息</h4>
            <div class="view-content" style="max-height: 70vh; overflow-y: auto; padding-right: 5px;">
                <div class="quest-details">
                    <h4 id="questTitle" class="quest-title"></h4>
                    <p id="questDescription" class="quest-description"></p>
                    <div class="quest-progress">
                        <h5>任务目标:</h5>
                        <ul id="questObjectives" class="objectives-list"></ul>
                    </div>
                    <div class="quest-rewards">
                        <h5>任务奖励:</h5>
                        <ul id="questRewards" class="rewards-list"></ul>
                    </div>
                    <div class="quest-actions">
                        <button id="acceptQuest" class="action-button primary-button">接受任务</button>
                        <button id="completeQuest" class="action-button success-button">完成任务</button>
                        <button id="abandonQuest" class="action-button danger-button">放弃任务</button>
                    </div>
                </div>
                
                <!-- 任务放弃确认区域 -->
                <div id="abandonConfirmArea" class="abandon-confirm-area" style="display:none; width: auto; min-width: 250px; max-width: 90%;">
                    <h5 class="abandon-title" style="color: #e74c3c; text-align: center; margin-top: 0;">确认放弃任务</h5>
                    <p id="abandonQuestMessage" style="text-align: center; margin: 15px 0;">你确定要放弃这个任务吗？放弃后进度将会丢失。</p>
                    <div class="confirmation-actions" style="display: flex; justify-content: center; gap: 10px; margin-top: 15px;">
                        <button id="confirmAbandon" class="action-button danger-button" style="padding: 8px 15px;">确认放弃</button>
                        <button id="cancelAbandon" class="action-button secondary-button" style="padding: 8px 15px;">取消</button>
                    </div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn-primary" id="closeQuestView">[返回]</button>
            </div>
        `;
        document.getElementById('gameContent').appendChild(questView);
        
        // 添加事件监听器
        document.getElementById('closeQuestView').addEventListener('click', () => {
            this.hideQuestView(); // 隐藏当前视图
            // 根据来源决定返回哪个界面
            if (this._currentQuestViewOrigin === 'questLog') {
                this.showQuestLogView();
            } else if (this._currentQuestViewOrigin === 'npc') {
                // 如果是从NPC详情或NPC任务列表进入，返回NPC任务列表
                // 确保 currentNpcId 被正确设置，以便 showNpcQuestView 可以更新标题
                this.showNpcQuestView();
            } else {
                // 默认返回主界面
                document.getElementById('main-view').style.display = 'block';
            }
            // 清除任务视图来源，以便下次正确判断
            this._currentQuestViewOrigin = null;
        });
        document.getElementById('acceptQuest').addEventListener('click', () => this.handleAcceptQuest());
        document.getElementById('completeQuest').addEventListener('click', () => this.handleCompleteQuest());
        document.getElementById('abandonQuest').addEventListener('click', () => this.showAbandonConfirmation());
        document.getElementById('confirmAbandon').addEventListener('click', () => {
            this.hideAbandonConfirmation();
            this.handleAbandonQuest();
        });
        document.getElementById('cancelAbandon').addEventListener('click', () => {
            this.hideAbandonConfirmation();
        });
    }
    
    /**
     * 初始化任务日志视图
     */
    initQuestLogView() {
        // 创建任务日志视图元素
        const questLogView = document.createElement('div');
        questLogView.id = 'questLogView';
        questLogView.className = 'view';
        questLogView.style.display = 'none';
        questLogView.innerHTML = `
            <h4 style="margin-top:0; text-align: center;">任务日志</h4>
            <div class="view-content">
                <div class="quest-filter">
                    <button id="activeQuestFilter" class="filter-button active">进行中</button>
                    <button id="completedQuestFilter" class="filter-button">已完成</button>
                </div>
                <div id="questList" class="quest-list">
                    <div id="activeQuestList"></div>
                    <div id="completedQuestList" style="display: none;"></div>
                </div>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn-primary" id="closeQuestLogView">[返回]</button>
            </div>
        `;
        document.getElementById('gameContent').appendChild(questLogView);
        
        // 添加事件监听器
        document.getElementById('closeQuestLogView').addEventListener('click', () => this.hideQuestLogView());
        document.getElementById('activeQuestFilter').addEventListener('click', () => this.showActiveQuests());
        document.getElementById('completedQuestFilter').addEventListener('click', () => this.showCompletedQuests());
    }
    
    /**
     * 初始化NPC任务视图
     */
    initNpcQuestView() {
        // 创建NPC任务视图元素
        const npcQuestView = document.createElement('div');
        npcQuestView.id = 'npcQuestView';
        npcQuestView.className = 'view';
        npcQuestView.style.display = 'none';
        npcQuestView.innerHTML = `
            <h4 id="npcQuestTitle" style="margin-top:0; text-align: center;">NPC任务</h4>
            <div class="view-content">
                <ul id="npcQuestList" class="quest-list"></ul>
            </div>
            <div style="text-align: center; margin-top: 10px;">
                <button class="btn-primary" id="closeNpcQuestView">[返回]</button>
            </div>
        `;
        document.getElementById('gameContent').appendChild(npcQuestView);
        
        // 添加事件监听器
        document.getElementById('closeNpcQuestView').addEventListener('click', () => {
            document.getElementById('npcQuestView').style.display = 'none';
            // 如果是从NPC详情过来的，返回NPC详情视图
            if (this.game.npcManager && this.game.npcManager.currentNpc) {
                this.game.npcManager.showNpcDetailView();
            } else {
                document.getElementById('main-view').style.display = 'block';
            }
        });
    }
    
    /**
     * 处理任务列表数据
     * @param {Object} data 任务列表数据
     */
    handleQuestListData(data) {
        console.log('收到任务列表数据:', data);
        
        if (data.quests_from_npc) {
            // NPC的任务列表
            this.currentNpcId = data.npc_id;
            this.availableQuestsFromNpc = data.available_quests || [];
            this.completableQuestsForNpc = data.completable_quests || [];
            this.relatedActiveQuests = data.related_active_quests || [];
            
            console.log(`NPC(ID: ${this.currentNpcId}) 可完成任务数量: ${this.completableQuestsForNpc.length}`);
            console.log(`NPC(ID: ${this.currentNpcId}) 相关进行中任务数量: ${this.relatedActiveQuests.length}`);
            
            if (this.completableQuestsForNpc.length > 0) {
                console.log('可完成任务详情:', this.completableQuestsForNpc);
            }
            if (this.relatedActiveQuests.length > 0) {
                console.log('相关进行中任务详情:', this.relatedActiveQuests);
            }
            
            // 如果有回调函数，先调用回调
            if (typeof this.npcQuestsCallback === 'function') {
                this.npcQuestsCallback();
                this.npcQuestsCallback = null; // 清除回调
            } else {
                // 没有回调时才更新视图
                this.updateNpcQuestView();
            }
        } else {
            // 玩家的任务列表
            this.activeQuests = data.active_quests || [];
            this.completedQuests = data.completed_quests || [];
            this.abandonedQuests = data.abandoned_quests || [];

            this.allPlayerQuests = [...this.activeQuests, ...this.completedQuests, ...this.abandonedQuests];
    
            this.updateQuestLogView();
        }
    }
    
    /**
     * 处理任务更新数据
     * @param {Object} data 任务更新数据
     */
    handleQuestUpdateData(data) {
        if (data.action === 'accepted') {
            // 显示任务接受成功的提示消息
            // this.showNotification(`✅ ${data.message}`, 'success'); // 移除浮动提示

            // 记录到游戏日志
            this.game.addLog(`📜 ${data.message}`);
            
            // 更新NPC任务列表，移除已接受的任务
            if (this.currentNpcId && this.availableQuestsFromNpc.length > 0) {
                // 找到并移除已接受的任务
                const acceptedQuestIndex = this.availableQuestsFromNpc.findIndex(q => q.id === this.currentQuest.id);
                if (acceptedQuestIndex !== -1) {
                    this.availableQuestsFromNpc.splice(acceptedQuestIndex, 1);
                }
                
                // 更新NPC任务列表视图
                this.updateNpcQuestView();
            }
            
            // 隐藏任务详情视图
            this.hideQuestView();
            
            // 重新获取玩家任务列表
            this.getPlayerQuests('active');
        } else if (data.action === 'completed') {
            // 显示任务完成成功的提示消息
            // this.showNotification(`✅ ${data.message}`, 'success'); // 移除浮动提示

            // 记录到游戏日志
            this.game.addLog(`✅ ${data.message}`);
            
            // 显示奖励信息
            let rewardText = '';
            if (data.rewards.gold > 0) {
                rewardText += `获得 ${data.rewards.gold} 金币\n`;
            }
            if (data.rewards.exp > 0) {
                rewardText += `获得 ${data.rewards.exp} 经验\n`;
            }
            if (data.rewards.items && data.rewards.items.length > 0) {
                rewardText += '获得物品:\n';
                data.rewards.items.forEach(item => {
                    rewardText += `- ${item.name} × ${item.quantity}\n`;
                });
            }
            
            if (rewardText) {
                this.game.addLog(rewardText.trim());
            }
            
            // 更新NPC任务列表，移除已完成的任务
            if (this.currentNpcId && this.completableQuestsForNpc.length > 0) {
                // 找到并移除已完成的任务
                const completedQuestIndex = this.completableQuestsForNpc.findIndex(q => q.id === this.currentQuest.id);
                if (completedQuestIndex !== -1) {
                    this.completableQuestsForNpc.splice(completedQuestIndex, 1);
                }
                
                // 更新NPC任务列表视图
                this.updateNpcQuestView();
            }
            
            // 隐藏任务详情视图
            this.hideQuestView();
            
            // 重新获取玩家任务列表
            this.getPlayerQuests();
        }         else if (data.action === 'abandoned') {
            // 显示任务放弃的提示消息
            this.showNotification(`❌ ${data.message}`, 'info'); // 将错误改为信息，因为这不是一个真正的"错误"
            
            // 记录到游戏日志
            this.game.addLog(`❌ ${data.message}`);
            
            // 隐藏任务详情视图和放弃确认框
            this.hideQuestView(); // 隐藏任务详情视图
            this.hideAbandonConfirmation(); // 隐藏放弃确认框

            // 重新启用放弃按钮（如果之前是禁用的）
            const abandonButton = document.getElementById('abandonQuest');
            if (abandonButton) {
                abandonButton.disabled = false;
                abandonButton.textContent = '放弃任务';
            }
            
            // 根据任务详情视图的来源决定返回哪个界面
            if (this._currentQuestViewOrigin === 'questLog') {
                this.getPlayerQuests(); // 刷新任务日志数据
                this.showQuestLogView(true); // 显示任务日志视图，跳过隐藏其他视图
            } else if (this._currentQuestViewOrigin === 'npc') {
                // 刷新NPC任务列表
                this.getNpcQuests(this.currentNpcId, () => {
                    this.showNpcQuestView(true); // 显示NPC任务视图，跳过隐藏其他视图
                });
            } else {
                document.getElementById('main-view').style.display = 'block'; // 默认返回主界面
            }

            // 清除任务视图来源
            this._currentQuestViewOrigin = null;

        } else if (data.action === 'progress_updated') {
            // 更新任务进度
            const quest = this.activeQuests.find(q => q.quest_id === data.quest_id);
            if (quest) {
                const objective = quest.objectives.find(o => o.id === data.objective_id);
                if (objective) {
                    objective.progress = data.new_progress;
                    objective.completed = (data.new_progress >= data.target);
                    
                    // 更新进度百分比
                    let completedCount = 0;
                    for (const obj of quest.objectives) {
                        if (obj.completed) completedCount++;
                    }
                    quest.progress_percentage = Math.round((completedCount / quest.objectives.length) * 100);
                    
                    // 显示任务进度更新提示
                    const progressMessage = `任务"${quest.title}"进度更新: ${data.description} (${data.new_progress}/${data.target})`;
                    // this.showNotification(`📝 ${progressMessage}`, 'info'); // 移除浮动提示

                    // 记录到游戏日志
                    this.game.addLog(`📝 ${progressMessage}`);
                    
                    // 如果任务可以完成，提示玩家
                    if (data.can_complete) {
                        // 优先使用传入的receiver_npc_name，否则使用任务对象中的
                        const receiverNpcName = data.receiver_npc_name || quest.receiver_npc_name || '相关NPC';
                        const completeMessage = `任务"${quest.title}"已可以完成，请找${receiverNpcName}交付任务`;
                        // this.showNotification(`📢 ${completeMessage}`, 'success'); // 移除浮动提示
                        this.game.addLog(`📢 ${completeMessage}`); // 保留游戏日志中的提示
                    }
                    
                    // 更新界面
                    this.updateQuestLogView();
                }
            }
        }
    }
    
    /**
     * 获取玩家任务列表
     * @param {string} status 任务状态（可选）
     */
    getPlayerQuests(status = null) {
        if (!this.game.isConnected() || !this.game.currentPlayer) return;
        
        const payload = {
            player_id: this.game.currentPlayer.id
        };
        
        if (status) {
            payload.status = status;
        }
        
        this.game.sendMessage(MessageProtocol.C2S_GET_QUEST_LIST, payload);
    }
    
    /**
     * 获取NPC任务列表
     * @param {number} npcId NPC ID
     * @param {Function} callback 获取完成后的回调函数
     */
    getNpcQuests(npcId, callback) {
        if (!this.game.isConnected() || !this.game.currentPlayer) return;
        
        console.log(`正在获取NPC(ID: ${npcId})的任务列表...`);
        
        // 保存回调函数
        this.npcQuestsCallback = callback;
        
        this.game.sendMessage(MessageProtocol.C2S_GET_QUEST_LIST, {
            player_id: this.game.currentPlayer.id,
            npc_id: npcId
        });
    }
    
    /**
     * 接受任务
     */
    handleAcceptQuest() {
        if (!this.currentQuest) return;
        
        // 禁用接受按钮，防止重复点击
        const acceptButton = document.getElementById('acceptQuest');
        acceptButton.disabled = true;
        acceptButton.textContent = '接受中...';
        
        // 发送接受任务请求
        this.game.sendMessage(MessageProtocol.C2S_ACCEPT_QUEST, {
            quest_id: this.currentQuest.id
        });
    }
    
    /**
     * 完成任务
     */
    handleCompleteQuest() {
        if (!this.currentQuest) return;
        
        // 禁用完成按钮，防止重复点击
        const completeButton = document.getElementById('completeQuest');
        completeButton.disabled = true;
        completeButton.textContent = '完成中...';
        
        // 发送完成任务请求
        this.game.sendMessage(MessageProtocol.C2S_COMPLETE_QUEST, {
            quest_id: this.currentQuest.id
        });
    }
    
    /**
     * 放弃任务
     */
    handleAbandonQuest() {
        if (!this.currentQuest) return;
        
        // 禁用放弃按钮，防止重复点击
        const abandonButton = document.getElementById('abandonQuest');
        abandonButton.disabled = true;
        abandonButton.textContent = '放弃中...';
        
        // 发送放弃任务请求 - 使用quest_id而不是id
        const questId = this.currentQuest.quest_id || this.currentQuest.id;
        
        console.log(`放弃任务，使用ID: ${questId}，任务对象:`, this.currentQuest);
        
        // 记录任务来源，以便在任务放弃后正确返回 (已通过 _currentQuestViewOrigin 记录)
        // const fromQuestLog = this.fromQuestLog;
        // const tempNpcId = this.currentNpcId;
        // this.currentNpcId = null;
        
        // 发送放弃任务请求
        this.game.sendMessage(MessageProtocol.C2S_ABANDON_QUEST, {
            quest_id: questId
        });
        
        // 后续的视图切换和通知将在 handleQuestUpdateData 中处理
    }
    
    /**
     * 显示任务视图
     * @param {Object} quest 任务对象
     * @param {string} type 任务类型（'available', 'active', 'completable'）
     * @param {string} origin 来源 ('questLog', 'npc')
     */
    showQuestView(quest, type, origin = null) {
        // 设置任务详情视图的来源
        this._currentQuestViewOrigin = origin;
        
        // 如果是从任务日志进入，设置fromQuestLog标志
        this.fromQuestLog = (origin === 'questLog');
        
        // 隐藏其他所有视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('npcQuestView').style.display = 'none';
        document.getElementById('npcDetailView').style.display = 'none';
        document.getElementById('questLogView').style.display = 'none';
        document.getElementById('dialogueView').style.display = 'none';
        
        // 清除可能存在的通知
        this.clearNotifications();
        
        // 显示任务视图
        document.getElementById('questView').style.display = 'block';
        
        // 更新任务标题和描述
        document.getElementById('questTitle').textContent = quest.title;
        document.getElementById('questDescription').textContent = quest.description || '无描述';
        
        // 清空目标列表
        document.getElementById('questObjectives').innerHTML = '';
        
        // 添加任务目标
        if (quest.objectives && quest.objectives.length > 0) {
            quest.objectives.forEach(objective => {
                const li = document.createElement('li');
                let objectiveText = '';
                
                // 根据目标类型显示不同的文本
                switch (objective.type) {
                    case 'kill':
                        // 显示怪物名称和数量
                        const monsterName = objective.target_details?.name || '未知生物';
                        objectiveText = `击杀 ${monsterName} (${objective.progress || 0}/${objective.quantity})`;
                        break;
                        
                    case 'collect':
                        // 显示物品名称和数量
                        const itemName = objective.target_details?.name || '未知物品';
                        objectiveText = `收集 ${itemName} (${objective.progress || 0}/${objective.quantity})`;
                        break;
                        
                    case 'visit':
                    case 'dialogue':
                        // 显示NPC名称
                        const npcName = objective.target_details?.name || '未知人物';
                        objectiveText = `与 ${npcName} 交谈`;
                        if (objective.progress) {
                            objectiveText += ' (已完成)';
                        }
                        break;
                        
                    case 'talk':
                        objectiveText = objective.description || '进行对话';
                        if (objective.progress) {
                            objectiveText += ' (已完成)';
                        }
                        break;
                        
                    default:
                        objectiveText = objective.description || `${objective.type} (${objective.progress || 0}/${objective.quantity})`;
                }
                
                li.textContent = objectiveText;
                
                // 如果目标已完成，添加完成标记
                if (objective.completed) {
                    li.classList.add('completed-objective');
                    li.innerHTML = `${objectiveText} ✓`;
                }
                
                document.getElementById('questObjectives').appendChild(li);
            });
        }
        
        // 清空奖励列表
        document.getElementById('questRewards').innerHTML = '';
        
        // 添加奖励
        const rewards = [];
        
        // 金币奖励
        if (quest.reward_gold && quest.reward_gold > 0) {
            rewards.push(`<li>金币: ${quest.reward_gold}</li>`);
        }
        
        // 经验奖励
        if (quest.reward_exp && quest.reward_exp > 0) {
            rewards.push(`<li>经验: ${quest.reward_exp}</li>`);
        }
        
        // 物品奖励
        if (quest.reward_items && quest.reward_items.length > 0) {
            quest.reward_items.forEach(item => {
                const itemName = item.name || '未知物品';
                rewards.push(`<li>${itemName} × ${item.quantity}</li>`);
            });
        }
        
        if (rewards.length > 0) {
            document.getElementById('questRewards').innerHTML = rewards.join('');
        } else {
            document.getElementById('questRewards').innerHTML = '<li>没有奖励</li>';
        }
        
        // 添加交付NPC信息
        if (quest.receiver_npc_name || quest.turn_in_tip || (quest.receiver_location && quest.receiver_location.npc_name)) {
            const turnInInfo = document.createElement('div');
            turnInInfo.className = 'quest-turn-in-info';
            turnInInfo.style.marginTop = '10px';
            turnInInfo.style.paddingTop = '10px';
            turnInInfo.style.borderTop = '1px dashed #ccc';
            
            // 清除之前可能存在的交付信息
            const existingTurnInInfo = document.querySelector('.quest-turn-in-info');
            if (existingTurnInInfo) {
                existingTurnInInfo.remove();
            }
            
            // 优先使用turn_in_tip，其次使用receiver_location，最后使用receiver_npc_name
            if (quest.turn_in_tip) {
                turnInInfo.innerHTML = quest.turn_in_tip;
            } else if (quest.receiver_location && quest.receiver_location.npc_name) {
                turnInInfo.innerHTML = `将任务交付给: ${quest.receiver_location.npc_name}`;
            } else if (quest.receiver_npc_name) {
                turnInInfo.innerHTML = `将任务交付给: ${quest.receiver_npc_name}`;
            }
            
            // 将交付信息添加到任务详情中
            document.querySelector('.quest-rewards').appendChild(turnInInfo);
        }
        
        // 根据任务类型和来源设置按钮显示
        document.getElementById('acceptQuest').style.display = type === 'available' ? 'block' : 'none';
        document.getElementById('completeQuest').style.display = type === 'completable' ? 'block' : 'none';
        
        // 只有当任务是进行中，且不是从NPC界面查看时，才显示放弃按钮
        const abandonButton = document.getElementById('abandonQuest');
        if (type === 'active' && origin !== 'npc') {
            abandonButton.style.display = 'block';
        } else {
            abandonButton.style.display = 'none';
        }
        
        // 重置按钮状态
        document.getElementById('acceptQuest').disabled = false;
        document.getElementById('acceptQuest').textContent = '接受任务';
        document.getElementById('completeQuest').disabled = false;
        document.getElementById('completeQuest').textContent = '完成任务';
        abandonButton.disabled = false;
        abandonButton.textContent = '放弃任务';
        
        // 保存当前查看的任务
        this.currentQuest = quest;
        
        // 调试输出
        console.log('显示任务详情:', quest);
    }
    
    /**
     * 隐藏任务视图
     */
    hideQuestView() {
        document.getElementById('questView').style.display = 'none';
        
        // 清除可能存在的通知
        this.clearNotifications();
        
        // 隐藏放弃确认区域
        this.hideAbandonConfirmation();
        
        // 移除原有的导航逻辑，这部分逻辑现在由返回按钮的事件监听器或handleQuestUpdateData处理
    }
    
    /**
     * 显示任务日志视图
     */
    showQuestLogView() {
        // 隐藏其他视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('npcQuestView').style.display = 'none';
        document.getElementById('questView').style.display = 'none';
        
        // 先获取最新的任务列表
        this.getPlayerQuests();
        
        document.getElementById('questLogView').style.display = 'block';
        this.showActiveQuests(); // 默认显示进行中的任务
    }
    
    /**
     * 隐藏任务日志视图
     */
    hideQuestLogView() {
        document.getElementById('questLogView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    /**
     * 显示NPC任务视图
     */
    showNpcQuestView() {
        // 隐藏其他所有视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('npcDetailView').style.display = 'none';
        document.getElementById('questView').style.display = 'none';
        document.getElementById('questLogView').style.display = 'none';
        document.getElementById('dialogueView').style.display = 'none';
        
        // 清除可能存在的通知
        this.clearNotifications();
        
        // 显示NPC任务视图
        document.getElementById('npcQuestView').style.display = 'block';
        
        // 更新NPC任务视图标题
        if (this.game.npcManager && this.game.npcManager.currentNpc) {
            document.getElementById('npcQuestTitle').textContent = `${this.game.npcManager.currentNpc.name} 的任务`;
        } else {
            document.getElementById('npcQuestTitle').textContent = '任务';
        }
    }
    
    /**
     * 显示进行中的任务
     */
    showActiveQuests() {
        const activeFilter = document.getElementById('activeQuestFilter');
        const completedFilter = document.getElementById('completedQuestFilter');
        const activeList = document.getElementById('activeQuestList');
        const completedList = document.getElementById('completedQuestList');
        
        activeFilter.classList.add('active');
        completedFilter.classList.remove('active');
        
        activeList.style.display = 'block';
        completedList.style.display = 'none';
        
        this.updateQuestLogView('active');
    }
    
    /**
     * 显示已完成的任务
     */
    showCompletedQuests() {
        const activeFilter = document.getElementById('activeQuestFilter');
        const completedFilter = document.getElementById('completedQuestFilter');
        const activeList = document.getElementById('activeQuestList');
        const completedList = document.getElementById('completedQuestList');
        
        activeFilter.classList.remove('active');
        completedFilter.classList.add('active');
        
        activeList.style.display = 'none';
        completedList.style.display = 'block';
        
        // 如果尚未加载完成的任务，则加载
        if (this.completedQuests.length === 0) {
            this.getPlayerQuests('completed');
        } else {
            this.updateQuestLogView('completed');
        }
    }
    
    /**
     * 更新任务日志视图
     * @param {string} type 任务类型（'active', 'completed'）
     */
    updateQuestLogView(type = 'active') {
        if (type === 'active') {
            const activeList = document.getElementById('activeQuestList');
            activeList.innerHTML = '';
            
            if (this.activeQuests.length === 0) {
                activeList.innerHTML = '<div class="empty-message">没有进行中的任务</div>';
                return;
            }
            
            // 创建一个div容器来包含任务项，避免使用默认的列表样式
            const questContainer = document.createElement('div');
            questContainer.className = 'quest-container';
            
            this.activeQuests.forEach(quest => {
                const questItem = this.createQuestListItem(quest, 'active');
                questContainer.appendChild(questItem);
            });
            
            activeList.appendChild(questContainer);
        } else if (type === 'completed') {
            const completedList = document.getElementById('completedQuestList');
            completedList.innerHTML = '';
            
            if (this.completedQuests.length === 0) {
                completedList.innerHTML = '<div class="empty-message">没有已完成的任务</div>';
                return;
            }
            
            // 创建一个div容器来包含任务项，避免使用默认的列表样式
            const questContainer = document.createElement('div');
            questContainer.className = 'quest-container';
            
            this.completedQuests.forEach(quest => {
                const questItem = this.createQuestListItem(quest, 'completed');
                questContainer.appendChild(questItem);
            });
            
            completedList.appendChild(questContainer);
        }
    }
    
    /**
     * 创建任务列表项
     * @param {Object} quest 任务对象
     * @param {string} type 类型 (active, completed)
     * @returns {HTMLElement} 列表项元素
     */
    createQuestListItem(quest, type) {
        const div = document.createElement('div');
        div.className = 'quest-list-item';
        
        // 创建任务标题和进度文本
        let progressText = '';
        
        // 如果是进行中的任务，显示进度数字
        if (type === 'active' && quest.objectives && quest.objectives.length > 0) {
            // 获取第一个任务目标的进度
            const firstObjective = quest.objectives[0];
            progressText = ` (${firstObjective.progress || 0}/${firstObjective.quantity})`;
        }
        
        // 准备交付NPC信息
        let turnInInfo = '';
        if (type === 'active' && (quest.receiver_npc_name || quest.turn_in_tip || (quest.receiver_location && quest.receiver_location.npc_name))) {
            if (quest.turn_in_tip) {
                turnInInfo = `<div class="quest-turn-in">${quest.turn_in_tip}</div>`;
            } else if (quest.receiver_location) {
                turnInInfo = `<div class="quest-turn-in">交付给: ${quest.receiver_location.npc_name}</div>`;
            } else if (quest.receiver_npc_name) {
                turnInInfo = `<div class="quest-turn-in">交付给: ${quest.receiver_npc_name}</div>`;
            }
        }
        
        div.innerHTML = `
            <div class="quest-info">
                <div class="quest-title">${quest.title}${progressText}</div>
                <div class="quest-description">${quest.description || ''}</div>
                ${turnInInfo}
            </div>
        `;
        
        // 添加点击事件
        div.addEventListener('click', () => {
            // 标记是从任务日志进入任务详情
            // this.fromQuestLog = true; // 这一行将被_currentQuestViewOrigin取代
            // 使用正确的任务ID、类型和来源显示任务详情
            this.showQuestView(quest, type, 'questLog');
        });
        
        return div;
    }
    
    /**
     * 更新NPC任务视图
     */
    updateNpcQuestView() {
        // 获取与NPC相关的进行中任务
        let activeQuestsForNpc = this.game.questManager?.relatedActiveQuests || [];
        
        if (activeQuestsForNpc.length === 0 && this.completableQuestsForNpc.length === 0) {
            // 如果没有可完成或进行中的任务，显示空任务列表和提示信息
            const npcQuestList = document.createElement('div');
            npcQuestList.className = 'empty-quest-list';
            npcQuestList.innerHTML = `
                <div class="empty-message">
                    ${this.game.npcManager && this.game.npcManager.currentNpc && this.game.npcManager.currentNpc.has_dialogue ? 
                        '<button id="talkToNpcInstead" class="action-button primary-button">与NPC对话</button>' : ''}
                </div>
            `;
            
            // 清空并添加新的任务列表
            const npcQuestView = document.getElementById('npcQuestList');
            npcQuestView.innerHTML = '';
            npcQuestView.appendChild(npcQuestList);
            
            // 如果有对话按钮，添加事件监听器
            const talkButton = document.getElementById('talkToNpcInstead');
            if (talkButton) {
                talkButton.addEventListener('click', () => {
                    // 隐藏任务界面
                    document.getElementById('npcQuestView').style.display = 'none';
                    // 启动对话
                    this.game.npcManager.talkToNpc(this.currentNpcId);
                });
            }
            
            // 显示NPC任务视图
            this.showNpcQuestView();
            return;
        }

        // 创建任务列表
        const npcQuestList = document.createElement('ul');
        npcQuestList.className = 'npc-quest-list';
        
        // 添加可完成任务
        if (this.completableQuestsForNpc.length > 0) {
            const completableHeader = document.createElement('li');
            completableHeader.className = 'quest-category-header';
            completableHeader.textContent = '可完成任务';
            npcQuestList.appendChild(completableHeader);
            
            this.completableQuestsForNpc.forEach(quest => {
                const questItem = document.createElement('li');
                questItem.className = 'npc-quest-item completable-quest';
                questItem.innerHTML = `
                    <span class="quest-status-icon"></span>
                    <span class="quest-title">${quest.title}</span>
                    <span class="quest-level">Lv.${quest.min_level}</span>
                `;
                questItem.addEventListener('click', () => this.showQuestView(quest, 'completable', 'npc'));
                npcQuestList.appendChild(questItem);
            });
        }
        
        // 添加与当前NPC相关的进行中任务
        if (activeQuestsForNpc.length > 0) {
            const activeHeader = document.createElement('li');
            activeHeader.className = 'quest-category-header';
            activeHeader.textContent = '进行中任务';
            npcQuestList.appendChild(activeHeader);
            
            activeQuestsForNpc.forEach(quest => {
                const questItem = document.createElement('li');
                questItem.className = 'npc-quest-item active-quest';
                
                // 获取第一个任务目标的进度文本
                let progressText = '';
                if (quest.objectives && quest.objectives.length > 0) {
                    const firstObjective = quest.objectives[0];
                    if (firstObjective.quantity) {
                        progressText = ` (${firstObjective.progress || 0}/${firstObjective.quantity})`;
                    }
                }
                
                questItem.innerHTML = `
                    <span class="quest-status-icon">⏳</span>
                    <span class="quest-title">${quest.title}${progressText}</span>
                    <span class="quest-level">Lv.${quest.min_level || 1}</span>
                `;
                questItem.addEventListener('click', () => this.showQuestView(quest, 'active', 'npc'));
                npcQuestList.appendChild(questItem);
            });
        }
        
        // 清空并添加新的任务列表
        const npcQuestView = document.getElementById('npcQuestList');
        npcQuestView.innerHTML = '';
        npcQuestView.appendChild(npcQuestList);
        
        // 显示NPC任务视图
        this.showNpcQuestView();
    }

    /**
     * 显示任务放弃确认区域
     */
    showAbandonConfirmation() {
        if (!this.currentQuest) return;
        
        // 隐藏操作按钮
        document.getElementById('acceptQuest').style.display = 'none';
        document.getElementById('completeQuest').style.display = 'none';
        document.getElementById('abandonQuest').style.display = 'none';
        
        // 设置确认信息
        const message = document.getElementById('abandonQuestMessage');
        message.textContent = `你确定要放弃任务"${this.currentQuest.title}"吗？放弃后进度将会丢失。`;
        
        // 获取游戏内容区域的尺寸
        const gameContent = document.getElementById('gameContent');
        const gameRect = gameContent.getBoundingClientRect();
        
        // 显示确认区域，使其看起来像一个独立的对话框
        const confirmArea = document.getElementById('abandonConfirmArea');
        confirmArea.style.display = 'block';
        confirmArea.style.position = 'absolute';
        confirmArea.style.top = '50%';
        confirmArea.style.left = '50%';
        confirmArea.style.transform = 'translate(-50%, -50%)';
        confirmArea.style.backgroundColor = '#f9f9f9';
        confirmArea.style.border = '2px solid #e74c3c';
        confirmArea.style.borderRadius = '5px';
        confirmArea.style.padding = '15px';
        confirmArea.style.zIndex = '1000';
        confirmArea.style.boxShadow = '0 0 10px rgba(0,0,0,0.3)';
        confirmArea.style.maxWidth = '80%'; // 确保不超过游戏界面宽度的80%
        confirmArea.style.maxHeight = '60%'; // 确保不超过游戏界面高度的60%
        confirmArea.style.overflow = 'auto'; // 内容过多时可滚动
        
        // 确保确认框在游戏主界面内
        setTimeout(() => {
            const confirmRect = confirmArea.getBoundingClientRect();
            
            // 如果确认框超出了游戏内容区域，调整位置
            if (confirmRect.width > gameRect.width * 0.8) {
                confirmArea.style.width = (gameRect.width * 0.8) + 'px';
            }
            
            if (confirmRect.height > gameRect.height * 0.6) {
                confirmArea.style.height = (gameRect.height * 0.6) + 'px';
            }
        }, 0);
        
        // 添加半透明覆盖层，限制在questView内
        let overlay = document.getElementById('abandonConfirmOverlay');
        if (!overlay) {
            overlay = document.createElement('div');
            overlay.id = 'abandonConfirmOverlay';
            overlay.style.position = 'absolute'; // 改为绝对定位而不是固定定位
            overlay.style.top = '0';
            overlay.style.left = '0';
            overlay.style.width = '100%';
            overlay.style.height = '100%';
            overlay.style.backgroundColor = 'rgba(0,0,0,0.5)';
            overlay.style.zIndex = '999';
            document.getElementById('questView').appendChild(overlay);
        } else {
            overlay.style.display = 'block';
        }
    }

    /**
     * 隐藏任务放弃确认区域
     */
    hideAbandonConfirmation() {
        // 隐藏确认区域
        const confirmArea = document.getElementById('abandonConfirmArea');
        confirmArea.style.display = 'none';
        
        // 隐藏覆盖层
        const overlay = document.getElementById('abandonConfirmOverlay');
        if (overlay) {
            overlay.style.display = 'none';
        }
        
        // 恢复操作按钮
        if (this.currentQuest) {
            // 根据任务类型设置按钮
            const type = this.getQuestType(this.currentQuest);
            document.getElementById('acceptQuest').style.display = type === 'available' ? 'block' : 'none';
            document.getElementById('completeQuest').style.display = type === 'completable' ? 'block' : 'none';
            document.getElementById('abandonQuest').style.display = type === 'active' ? 'block' : 'none';
        }
    }

    /**
     * 获取任务类型
     * @param {Object} quest 任务对象
     * @returns {string} 任务类型 ('available', 'active', 'completable')
     */
    getQuestType(quest) {
        // 检查是否是可接受任务
        if (this.availableQuestsFromNpc.find(q => q.id === quest.id)) {
            return 'available';
        }
        // 检查是否是可完成任务
        if (this.completableQuestsForNpc.find(q => q.id === quest.id)) {
            return 'completable';
        }
        // 默认为进行中任务
        return 'active';
    }

    /**
     * 显示通知消息
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, info)
     */
    showNotification(message, type = 'info') {
        // 使用游戏内容容器作为通知的父元素，确保通知显示在游戏界面的固定位置
        const gameContent = document.getElementById('gameContent');
        
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `game-notification ${type}`;
        notification.textContent = message;
        
        // 设置通知ID以便后续引用
        const notificationId = 'notification_' + Date.now();
        notification.id = notificationId;
        
        // 检查是否已有其他通知，如果有则调整位置
        const existingNotifications = document.querySelectorAll('.game-notification');
        const offsetTop = 80 + (existingNotifications.length * 70); // 从顶部80px开始，每个通知间隔70px
        
        // 在小屏幕上，通知显示在底部
        if (window.innerWidth <= 480) {
            notification.style.bottom = offsetTop + 'px';
            notification.style.top = 'auto';
        } else {
            notification.style.top = offsetTop + 'px';
        }
        
        // 添加到游戏内容容器
        gameContent.appendChild(notification);
        
        // 淡入效果
        setTimeout(() => {
            notification.classList.add('show');
        }, 10);
        
        // 3秒后淡出并移除
        setTimeout(() => {
            notification.classList.remove('show');
            setTimeout(() => {
                // 移除通知并重新调整其他通知的位置
                if (gameContent.contains(notification)) {
                    gameContent.removeChild(notification);
                    this.repositionNotifications();
                }
            }, 500); // 等待淡出动画完成
        }, 3000);
    }
    
    /**
     * 重新调整所有通知的位置
     */
    repositionNotifications() {
        // 获取所有通知元素
        const notifications = document.querySelectorAll('.game-notification');
        
        notifications.forEach((notification, index) => {
            const offsetTop = 80 + (index * 70); // 从顶部80px开始，每个通知间隔70px
            
            // 在小屏幕上，通知显示在底部
            if (window.innerWidth <= 480) {
                notification.style.bottom = offsetTop + 'px';
                notification.style.top = 'auto';
            } else {
                notification.style.top = offsetTop + 'px';
            }
        });
    }

    /**
     * 清除可能存在的通知
     */
    clearNotifications() {
        const notifications = document.querySelectorAll('.game-notification');
        notifications.forEach(notification => {
            notification.classList.remove('show');
            setTimeout(() => {
                notification.remove();
            }, 500);
        });
    }
} 