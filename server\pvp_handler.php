<?php
// server/pvp_handler.php

require_once __DIR__ . '/../classes/PvpBattleSystem.php';
require_once __DIR__ . '/../config/Database.php';

/**
 * PVP处理类，管理玩家间PVP挑战和战斗
 */
class PvpHandler {
    private $db;
    private $pvpBattleSystem;
    private $activeBattles = [];
    private $pendingChallenges = [];
    private $playerBattleMap = []; // 玩家ID到战斗ID的映射
    private $wsServer;
    
    public function __construct($wsServer) {
        $this->db = Database::getInstance();
        $this->pvpBattleSystem = new PvpBattleSystem();
        $this->wsServer = $wsServer;
        
        // 初始化存储，可以从Redis或数据库加载现有战斗
        $this->loadActiveBattles();
    }
    
    /**
     * 从Redis加载活跃的战斗（如果有的话）
     */
    private function loadActiveBattles() {
        // 在实际实现中，可以从Redis或数据库加载进行中的战斗
        // 这里仅作为示例，实际实现根据项目需求调整
    }
    
    /**
     * 处理PVP挑战请求
     * @param int $fd 请求者的WebSocket连接ID
     * @param string $playerId 挑战者ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleChallenge($fd, $playerId, $data) {
        $targetPlayerId = $data['target_player_id'] ?? null;
        
        if (!$targetPlayerId) {
            return ['success' => false, 'message' => '目标玩家ID不能为空'];
        }
        
        // 检查目标玩家是否在线
        $targetFd = $this->getPlayerFd($targetPlayerId);
        if (!$targetFd) {
            return ['success' => false, 'message' => '目标玩家不在线'];
        }
        
        // 检查玩家是否已经在战斗中
        if ($this->isPlayerInBattle($playerId)) {
            return ['success' => false, 'message' => '你已经在战斗中'];
        }
        
        if ($this->isPlayerInBattle($targetPlayerId)) {
            return ['success' => false, 'message' => '目标玩家已经在战斗中'];
        }
        
        // 检查挑战者和目标玩家是否在同一场景
        $challengerSceneId = $this->getPlayerCurrentScene($playerId);
        $targetSceneId = $this->getPlayerCurrentScene($targetPlayerId);
        
        if (!$challengerSceneId || !$targetSceneId) {
            return ['success' => false, 'message' => '无法获取玩家场景信息'];
        }
        
        if ($challengerSceneId !== $targetSceneId) {
            return ['success' => false, 'message' => '只能向同一场景中的玩家发起挑战'];
        }
        
        // 检查双方玩家是否存活
        $challengerAlive = $this->isPlayerAlive($playerId);
        $targetAlive = $this->isPlayerAlive($targetPlayerId);
        
        if (!$challengerAlive) {
            return ['success' => false, 'message' => '你已经死亡，无法发起PVP挑战'];
        }
        
        if (!$targetAlive) {
            return ['success' => false, 'message' => '目标玩家已经死亡，无法发起PVP挑战'];
        }
        
        // 获取挑战者信息
        $challengerInfo = $this->getPlayerInfo($playerId);
        if (!$challengerInfo) {
            return ['success' => false, 'message' => '无法获取挑战者信息'];
        }
        
        // 创建挑战记录
        $challengeId = uniqid('pvp_challenge_');
        $expiryTime = time() + 60; // 60秒后过期
        
        $this->pendingChallenges[$challengeId] = [
            'id' => $challengeId,
            'challenger_id' => $playerId,
            'challenger_name' => $challengerInfo['username'],
            'target_id' => $targetPlayerId,
            'created_at' => time(),
            'expiry_time' => $expiryTime,
            'status' => 'pending',
            'scene_id' => $challengerSceneId // 保存挑战场景ID
        ];
        
        // 向目标玩家发送挑战通知
        $this->sendPvpChallenge($targetFd, $challengeId, $playerId, $challengerInfo['username'], $expiryTime);
        
        // 向挑战者发送确认消息
        return [
            'success' => true, 
            'message' => "已向玩家 {$challengerInfo['username']} 发送PVP挑战请求",
            'challenge_id' => $challengeId
        ];
    }
    
    /**
     * 处理接受PVP挑战
     * @param int $fd 接受者的WebSocket连接ID
     * @param string $playerId 接受者ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleAcceptChallenge($fd, $playerId, $data) {
        $challengeId = $data['challenge_id'] ?? null;
        
        if (!$challengeId || !isset($this->pendingChallenges[$challengeId])) {
            return ['success' => false, 'message' => '挑战不存在或已过期'];
        }
        
        $challenge = $this->pendingChallenges[$challengeId];
        
        // 检查是否是目标玩家接受的挑战
        if ($challenge['target_id'] !== $playerId) {
            return ['success' => false, 'message' => '你不是此挑战的目标玩家'];
        }
        
        // 检查挑战是否已过期
        if (time() > $challenge['expiry_time']) {
            unset($this->pendingChallenges[$challengeId]);
            return ['success' => false, 'message' => '挑战已过期'];
        }
        
        // 检查玩家是否已经在战斗中
        if ($this->isPlayerInBattle($playerId) || $this->isPlayerInBattle($challenge['challenger_id'])) {
            unset($this->pendingChallenges[$challengeId]);
            return ['success' => false, 'message' => '有玩家已经在战斗中'];
        }
        
        // 检查双方是否仍在同一场景
        $challengerSceneId = $this->getPlayerCurrentScene($challenge['challenger_id']);
        $defenderSceneId = $this->getPlayerCurrentScene($playerId);
        
        if (!$challengerSceneId || !$defenderSceneId || $challengerSceneId != $defenderSceneId) {
            unset($this->pendingChallenges[$challengeId]);
            return ['success' => false, 'message' => '玩家不在同一场景，无法进行PVP战斗'];
        }
        
        // 检查双方玩家是否存活
        $challengerAlive = $this->isPlayerAlive($challenge['challenger_id']);
        $defenderAlive = $this->isPlayerAlive($playerId);
        
        if (!$challengerAlive || !$defenderAlive) {
            unset($this->pendingChallenges[$challengeId]);
            return ['success' => false, 'message' => '双方必须都存活才能进行PVP战斗'];
        }
        
        // 更新挑战状态
        $this->pendingChallenges[$challengeId]['status'] = 'accepted';
        
        // 开始PVP战斗
        $battleResult = $this->startPvpBattle($challenge['challenger_id'], $playerId, $challengerSceneId, false, false);
        
        if (!$battleResult['success']) {
            return $battleResult;
        }
        
        // 清理已处理的挑战
        unset($this->pendingChallenges[$challengeId]);
        
        return [
            'success' => true,
            'message' => '已接受PVP挑战，战斗即将开始',
            'battle_id' => $battleResult['battle_id']
        ];
    }
    
    /**
     * 处理拒绝PVP挑战
     * @param int $fd 拒绝者的WebSocket连接ID
     * @param string $playerId 拒绝者ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleDeclineChallenge($fd, $playerId, $data) {
        $challengeId = $data['challenge_id'] ?? null;
        
        if (!$challengeId || !isset($this->pendingChallenges[$challengeId])) {
            return ['success' => false, 'message' => '挑战不存在或已过期'];
        }
        
        $challenge = $this->pendingChallenges[$challengeId];
        
        // 检查是否是目标玩家拒绝的挑战
        if ($challenge['target_id'] !== $playerId) {
            return ['success' => false, 'message' => '你不是此挑战的目标玩家'];
        }
        
        // 获取拒绝者信息
        $defenderInfo = $this->getPlayerInfo($playerId);
        
        // 向挑战者发送拒绝通知
        $challengerFd = $this->getPlayerFd($challenge['challenger_id']);
        if ($challengerFd) {
            $this->sendPvpChallengeResponse($challengerFd, $challengeId, false, $defenderInfo['username']);
        }
        
        // 清理已处理的挑战
        unset($this->pendingChallenges[$challengeId]);
        
        return [
            'success' => true,
            'message' => '已拒绝PVP挑战'
        ];
    }
    
    /**
     * 开始PVP战斗
     * @param string $challengerId 挑战者ID
     * @param string $defenderId 被挑战者ID
     * @param string $sceneId 战斗场景ID
     * @param bool $isRedNameBattle 是否为洗红名战斗
     * @param bool $isDirectAttack 是否为直接攻击模式
     * @return array 处理结果
     */
    private function startPvpBattle($challengerId, $defenderId, $sceneId, $isRedNameBattle = false, $isDirectAttack = false) {
        // 初始化战斗状态
        $battleState = $this->pvpBattleSystem->initializePvpBattle($challengerId, $defenderId);
        
        if (isset($battleState['error'])) {
            return ['success' => false, 'message' => $battleState['error']];
        }
        
        $battleId = $battleState['battle_id'];
        
        // 设置战斗场景ID
        $battleState['scene_id'] = $sceneId;
        
        // 设置是否为洗红名战斗
        $battleState['is_redname_battle'] = $isRedNameBattle;
        
        // 设置是否为直接攻击模式
        $battleState['is_direct_attack'] = $isDirectAttack;
        
        // 获取场景名称
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("SELECT name FROM scenes WHERE id = ?");
            $stmt->execute([$sceneId]);
            $sceneName = $stmt->fetchColumn();
            if ($sceneName) {
                $battleState['scene_name'] = $sceneName;
            }
        } catch (Exception $e) {
            error_log("获取场景名称出错: " . $e->getMessage());
        }
        
        // 保存战斗状态
        $this->activeBattles[$battleId] = $battleState;
        
        // 记录战斗初始HP和MP状态
        $challenger = $battleState['challenger'];
        $defender = $battleState['defender'];
        $challengerName = $challenger['username'];
        $defenderName = $defender['username'];
        
        // 构建战斗开始状态日志
        $initialStatsLog = ["━━━━ 战斗开始状态 ━━━━"];
        $initialStatsLog[] = "{$challengerName}: HP {$challenger['attributes']['hp']}/{$challenger['attributes']['max_hp']} MP {$challenger['attributes']['mp']}/{$challenger['attributes']['max_mp']} 善恶值: {$challenger['attributes']['karma']}";
        $initialStatsLog[] = "{$defenderName}: HP {$defender['attributes']['hp']}/{$defender['attributes']['max_hp']} MP {$defender['attributes']['mp']}/{$defender['attributes']['max_mp']} 善恶值: {$defender['attributes']['karma']}";
        
        // 记录战斗初始状态日志
        $this->logPvpBattle($battleId, $initialStatsLog);
        
        // 如果是洗红名战斗，添加特殊标记
        if ($isRedNameBattle) {
            $this->logPvpBattle($battleId, ["⚠️ 这是一场洗红名战斗 ⚠️"]);
        }
        
        // 如果是直接攻击模式，添加特殊标记
        if ($isDirectAttack) {
            $this->logPvpBattle($battleId, ["⚠️ 这是一场直接攻击战斗，失败方将掉落物品和金币 ⚠️"]);
        }
        
        // 添加战斗开始日志
        $this->logPvpBattle($battleId, ["PVP战斗开始！{$challengerName} VS {$defenderName}"]);
        
        // 更新玩家战斗映射
        $this->playerBattleMap[$challengerId] = $battleId;
        $this->playerBattleMap[$defenderId] = $battleId;
        
        // 通知两位玩家战斗开始
        $challengerFd = $this->getPlayerFd($challengerId);
        $defenderFd = $this->getPlayerFd($defenderId);
        
        if ($challengerFd) {
            $this->sendPvpBattleStarted($challengerFd, $battleState);
        }
        
        if ($defenderFd) {
            $this->sendPvpBattleStarted($defenderFd, $battleState);
        }
        
        // 启动ATB更新循环
        $this->startPvpBattleLoop($battleId);
        
        return [
            'success' => true,
            'message' => 'PVP战斗已开始',
            'battle_id' => $battleId
        ];
    }
    
    /**
     * 启动PVP战斗更新循环
     * @param string $battleId 战斗ID
     */
    private function startPvpBattleLoop($battleId) {
        $this->wsServer->startPvpBattleTimer($battleId);
    }
    
    /**
     * 更新PVP战斗状态
     * @param string $battleId 战斗ID
     */
    public function updatePvpBattle($battleId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = &$this->activeBattles[$battleId];
        
        // 增加调试日志
        // error_log("正在更新PVP战斗状态: {$battleId}");

        // 更新ATB状态
        $actionInfo = $this->pvpBattleSystem->updateAtbState($battleState);
        
        // 广播ATB状态更新
        $this->broadcastPvpAtbStatus($battleId);
        
        // 如果有角色可以行动，执行行动
        if ($actionInfo) {
            $this->executePvpAction($battleId, $actionInfo);
        }
        
        // 检查战斗是否结束
        if ($battleState['is_over']) {
            $this->endPvpBattle($battleId);
        }
    }
    
    /**
     * 记录PVP战斗日志信息
     * @param string $battleId 战斗ID
     * @param array $logEntries 日志条目数组
     */
    private function logPvpBattle($battleId, array $logEntries) {
        if (empty($logEntries)) {
            return;
        }
        
        if (!isset($_SESSION['pvp_battle_logs'])) {
            $_SESSION['pvp_battle_logs'] = [];
        }
        
        if (!isset($_SESSION['pvp_battle_logs'][$battleId])) {
            $_SESSION['pvp_battle_logs'][$battleId] = [];
        }
        
        // 将新的日志条目添加到战斗日志中
        foreach ($logEntries as $entry) {
            $_SESSION['pvp_battle_logs'][$battleId][] = $entry;
        }
    }
    
    /**
     * 执行PVP战斗行动
     * @param string $battleId 战斗ID
     * @param array $actionInfo 行动信息
     */
    private function executePvpAction($battleId, $actionInfo) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = &$this->activeBattles[$battleId];
        
        $actorType = $actionInfo['actor_type']; // 'challenger' 或 'defender'
        
        // 检查行动信息中是否包含player_id，如果没有，根据actorType确定
        if (!isset($actionInfo['player_id']) || $actionInfo['player_id'] === null) {
            if ($actorType === 'challenger') {
                $playerId = $battleState['challenger']['id'];
            } else if ($actorType === 'defender') {
                $playerId = $battleState['defender']['id'];
            } else {
                error_log("错误: executePvpAction无法确定行动玩家ID，actorType={$actorType}");
                return;
            }
        } else {
            $playerId = $actionInfo['player_id'];
        }
        
        // 检查playerId是否存在
        if (!$playerId) {
            error_log("错误: executePvpAction获取到无效的玩家ID");
            return;
        }

        // 1. 处理技能冷却 (只针对当前行动的玩家)
        $this->processPvpSkillCooldowns($battleState, $playerId);
        
        // 2. 处理玩家回合开始时的效果
        $effectsResult = $this->pvpBattleSystem->processEffectsTurn($battleState, $playerId);
        
        // 如果处理效果时产生了日志，广播给玩家
        if (!empty($effectsResult['log'])) {
            $effectUpdateResult = [
                'log' => $effectsResult['log']
            ];
            $this->broadcastPvpBattleUpdate($battleId, $playerId, 'effects_update', $effectUpdateResult);
        }
        
        // 3. 检查玩家是否正在吟唱，如果是，则忽略意图，直接处理吟唱
        $player = null;
        if ($battleState['challenger']['id'] == $playerId) {
            $player = &$battleState['challenger'];
        } else {
            $player = &$battleState['defender'];
        }
        
        if ($player && isset($player['casting_info'])) {
            // 玩家正在吟唱，直接处理吟唱进度
            $actionResult = $this->pvpBattleSystem->handleCastingTurn($battleState, $playerId);
            $this->broadcastPvpBattleUpdate($battleId, $playerId, 'casting', $actionResult);
            return;
        }
        
        // 如果不在吟唱中，则获取玩家的意图并执行
        $intentionObj = $this->getPlayerIntention($battleId, $playerId);
        
        if (!$intentionObj) {
            // 如果没有意图，默认为攻击
            $intentionObj = ['action' => 'attack'];
        }
        
        // 提取动作类型
        $action = is_array($intentionObj) ? ($intentionObj['action'] ?? 'attack') : $intentionObj;
        
        // 根据意图执行不同的行动
        $actionResult = null;
        
        switch ($action) {
            case 'attack':
                $actionResult = $this->pvpBattleSystem->handlePlayerAttack($battleState, $playerId);
                break;
            // 'heal'选项已移除，防止作弊
            case 'use_hp_potion':
                $actionResult = $this->pvpBattleSystem->handleUseItem($battleState, $playerId, 'hp');
                break;
            case 'use_mp_potion':
                $actionResult = $this->pvpBattleSystem->handleUseItem($battleState, $playerId, 'mp');
                break;
            case 'skill':
                // 从意图对象中获取具体技能ID
                $skillId = 1; // 默认值
                
                if (is_array($intentionObj) && isset($intentionObj['skill_id'])) {
                    $skillId = $intentionObj['skill_id'];
                } elseif (is_array($battleState['player_intentions'][$playerId]) && isset($battleState['player_intentions'][$playerId]['skill_id'])) {
                    $skillId = $battleState['player_intentions'][$playerId]['skill_id'];
                }
                
                $actionResult = $this->pvpBattleSystem->handleUseSkill($battleState, $playerId, $skillId);
                break;
            case 'surrender':
                $this->handlePvpSurrender($battleId, $playerId);
                return;
            default:
                // 默认为攻击
                $actionResult = $this->pvpBattleSystem->handlePlayerAttack($battleState, $playerId);
                break;
        }
        
        // 清除玩家意图
        $this->clearPlayerIntention($battleId, $playerId);
        
        // 记录战斗日志
        if (!empty($actionResult['log'])) {
            $this->logPvpBattle($battleId, $actionResult['log']);
        }
        
        // 广播战斗更新，使用动作作为action_type
        $this->broadcastPvpBattleUpdate($battleId, $playerId, $action, $actionResult);
    }
    
    /**
     * 处理玩家投降
     * @param string $battleId 战斗ID
     * @param string $playerId 投降玩家ID
     */
    private function handlePvpSurrender($battleId, $playerId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = &$this->activeBattles[$battleId];
        
        // 根据投降玩家设置胜负
        if ($playerId === $battleState['challenger']['id']) {
            $battleState['winner'] = 'defender';
            $battleState['loser'] = 'challenger';
        } else {
            $battleState['winner'] = 'challenger';
            $battleState['loser'] = 'defender';
        }
        
        $battleState['is_over'] = true;
        
        // 广播战斗更新
        $actionResult = [
            'log' => ["{$this->getPlayerInfo($playerId)['username']} 选择了投降！"]
        ];
        
        // 记录战斗日志
        $this->logPvpBattle($battleId, $actionResult['log']);
        
        $this->broadcastPvpBattleUpdate($battleId, $playerId, 'surrender', $actionResult);
        
        // 结束战斗
        $this->endPvpBattle($battleId);
    }
    
    /**
     * 结束PVP战斗
     * @param string $battleId 战斗ID
     */
    private function endPvpBattle($battleId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = $this->activeBattles[$battleId];
        
        // 添加检查确保战斗结束逻辑只执行一次
        if (isset($battleState['ending_processed']) && $battleState['ending_processed'] === true) {
            return;
        }
        
        // 标记战斗结束逻辑已处理
        $this->activeBattles[$battleId]['ending_processed'] = true;
        
        // 记录战斗结束时双方的HP和MP状态
        $challenger = $battleState['challenger'];
        $defender = $battleState['defender'];
        $challengerName = $challenger['username'];
        $defenderName = $defender['username'];
        
        // 构建战斗结束状态日志
        $finalStatsLog = ["━━━━ 战斗结束状态 ━━━━"];
        $finalStatsLog[] = "{$challengerName}: HP {$challenger['attributes']['hp']}/{$challenger['attributes']['max_hp']} MP {$challenger['attributes']['mp']}/{$challenger['attributes']['max_mp']} 善恶值: {$challenger['attributes']['karma']}";
        $finalStatsLog[] = "{$defenderName}: HP {$defender['attributes']['hp']}/{$defender['attributes']['max_hp']} MP {$defender['attributes']['mp']}/{$defender['attributes']['max_mp']} 善恶值: {$defender['attributes']['karma']}";
        
        // 记录战斗结束状态日志
        $this->logPvpBattle($battleId, $finalStatsLog);
        
        // 添加战斗伤害统计信息到战斗日志
        $challengerId = $battleState['challenger']['id'];
        $defenderId = $battleState['defender']['id'];
        $challengerDamage = $battleState['damage_contribution'][$challengerId] ?? 0;
        $defenderDamage = $battleState['damage_contribution'][$defenderId] ?? 0;
        
        // 构建伤害统计日志
        $damageStatsLog = ["━━━━ 战斗伤害统计 ━━━━"];
        $damageStatsLog[] = "{$challengerName} 造成总伤害: {$challengerDamage}";
        $damageStatsLog[] = "{$defenderName} 造成总伤害: {$defenderDamage}";
        
        // 记录伤害统计日志
        $this->logPvpBattle($battleId, $damageStatsLog);
        
        // 如果是洗红名战斗，处理善恶值变化
        if (isset($battleState['is_redname_battle']) && $battleState['is_redname_battle']) {
            try {
                $conn = $this->db->getConnection();
                $winnerRole = $battleState['winner'] ?? null;
                $challengerId = $battleState['challenger']['id'];
                $defenderId = $battleState['defender']['id'];
                
                // 确定挑战者（攻击方）和防守方（被攻击方/红名玩家）
                // 在直接攻击模式中，挑战者是攻击方，防守者是被攻击方
                $attackerId = $challengerId;
                $targetId = $defenderId;
                
                // 根据winner角色确定获胜者ID
                $winnerId = null;
                if ($winnerRole === 'challenger') {
                    $winnerId = $challengerId;
                } else if ($winnerRole === 'defender') {
                    $winnerId = $defenderId;
                }
                
                if ($winnerId === $attackerId) {
                    // 攻击方胜利，攻击者和红名玩家善恶值各+1
                    $stmt = $conn->prepare("UPDATE player_attributes SET karma = karma + 1 WHERE account_id = ? OR account_id = ?");
                    $stmt->execute([$attackerId, $targetId]);
                    
                    $karmaLog = ["━━━━ 洗红名战斗结果 ━━━━"];
                    $karmaLog[] = "攻击方 {$challengerName} 战胜了红名玩家！";
                    $karmaLog[] = "{$challengerName} 和 {$defenderName} 的善恶值各增加了1点。";
                    $this->logPvpBattle($battleId, $karmaLog);
                    
                    // 获取更新后的善恶值
                    $stmt = $conn->prepare("SELECT account_id, karma FROM player_attributes WHERE account_id IN (?, ?)");
                    $stmt->execute([$attackerId, $targetId]);
                    $updatedKarma = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                    
                    $updatedKarmaLog = ["━━━━ 更新后善恶值 ━━━━"];
                    $updatedKarmaLog[] = "{$challengerName} 的善恶值: {$updatedKarma[$attackerId]}";
                    $updatedKarmaLog[] = "{$defenderName} 的善恶值: {$updatedKarma[$targetId]}";
                    $this->logPvpBattle($battleId, $updatedKarmaLog);
                    
                    // 通知玩家
                    $attackerFd = $this->getPlayerFd($attackerId);
                    if ($attackerFd) {
                        $this->wsServer->sendMessage($attackerFd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => "你战胜了红名玩家，完成对他的洗礼！",
                            'type' => 'success'
                        ]);
                    }
                    
                    $defenderFd = $this->getPlayerFd($targetId);
                    if ($defenderFd) {
                        $this->wsServer->sendMessage($defenderFd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => "你被战胜，但感觉自己的心灵受到了洗礼！",
                            'type' => 'info'
                        ]);
                    }
                } else if ($winnerId === $targetId) {
                    // 攻击方失败，红名玩家善恶值-1
                    $stmt = $conn->prepare("UPDATE player_attributes SET karma = karma - 1 WHERE account_id = ?");
                    $stmt->execute([$targetId]);
                    
                    $karmaLog = ["━━━━ 洗红名战斗结果 ━━━━"];
                    $karmaLog[] = "红名玩家 {$defenderName} 击败了攻击方！";
                    $karmaLog[] = "{$defenderName} 的善恶值减少了1点。";
                    $this->logPvpBattle($battleId, $karmaLog);
                    
                    // 获取更新后的善恶值
                    $stmt = $conn->prepare("SELECT account_id, karma FROM player_attributes WHERE account_id IN (?, ?)");
                    $stmt->execute([$attackerId, $targetId]);
                    $updatedKarma = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                    
                    $updatedKarmaLog = ["━━━━ 更新后善恶值 ━━━━"];
                    $updatedKarmaLog[] = "{$challengerName} 的善恶值: {$updatedKarma[$attackerId]}";
                    $updatedKarmaLog[] = "{$defenderName} 的善恶值: {$updatedKarma[$targetId]}";
                    $this->logPvpBattle($battleId, $updatedKarmaLog);
                    
                    // 通知玩家
                    $defenderFd = $this->getPlayerFd($targetId);
                    if ($defenderFd) {
                        $this->wsServer->sendMessage($defenderFd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => "你击败了攻击者，此时感觉自己罪孽又加深了！",
                            'type' => 'warning'
                        ]);
                    }
                }
                // 平局不处理善恶值变化
            } catch (Exception $e) {
                error_log("更新洗红名战斗善恶值出错: " . $e->getMessage());
                // 错误不影响游戏流程
            }
        }
        
        // 保存战斗结果
        $this->pvpBattleSystem->savePvpBattleResults($battleState);
        
        // 如果是直接攻击模式，处理失败者掉落物品
        if (isset($battleState['is_direct_attack']) && $battleState['is_direct_attack'] && isset($battleState['winner']) && $battleState['winner'] !== 'draw') {
            try {
                $conn = $this->db->getConnection();
                $winnerRole = $battleState['winner']; // 'challenger' 或 'defender'
                $loserRole = $winnerRole === 'challenger' ? 'defender' : 'challenger';
                
                $winnerId = $battleState[$winnerRole]['id'];
                $loserId = $battleState[$loserRole]['id'];
                $winnerName = $battleState[$winnerRole]['username'];
                $loserName = $battleState[$loserRole]['username'];
                
                // 获取失败者的更多信息，包括背包物品
                $loserData = $battleState[$loserRole];
                
                // 获取失败者的背包物品
                $stmt = $conn->prepare("
                    SELECT pi.id, pi.item_template_id, pi.quantity, pi.is_equipped, it.name, it.category, it.description, it.effects
                    FROM player_inventory pi
                    JOIN item_templates it ON pi.item_template_id = it.id
                    WHERE pi.player_id = ? AND pi.is_bound = 0
                ");
                $stmt->execute([$loserId]);
                $loserData['inventory'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 计算掉落物品和金币
                $drops = Formulas::calculatePvpDrops($loserData);
                
                // 如果有掉落物品或金币
                if ($drops['gold'] > 0 || !empty($drops['items'])) {
                    $dropsLog = ["━━━━ PVP战利品掉落 ━━━━"];
                    
                    // 处理金币掉落
                    if ($drops['gold'] > 0) {
                        // 从失败者扣除金币
                        $stmt = $conn->prepare("UPDATE player_attributes SET gold = gold - ? WHERE account_id = ?");
                        $stmt->execute([$drops['gold'], $loserId]);
                        
                        // 将金币添加给胜利者
                        $stmt = $conn->prepare("UPDATE player_attributes SET gold = gold + ? WHERE account_id = ?");
                        $stmt->execute([$drops['gold'], $winnerId]);
                        
                        $dropsLog[] = "{$winnerName} 从 {$loserName} 获得了 {$drops['gold']} 金币";
                        
                        // 获取双方更新后的金币数量
                        $stmt = $conn->prepare("SELECT account_id, gold FROM player_attributes WHERE account_id IN (?, ?)");
                        $stmt->execute([$winnerId, $loserId]);
                        $updatedGold = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
                        
                        $dropsLog[] = "【金币更新】{$winnerName}: {$updatedGold[$winnerId]} 金币";
                        $dropsLog[] = "【金币更新】{$loserName}: {$updatedGold[$loserId]} 金币";
                    }
                    
                    // 处理物品掉落
                    if (!empty($drops['items'])) {
                        $dropsLog[] = "━━ 掉落物品明细 ━━";
                    }
                    
                    foreach ($drops['items'] as $droppedItem) {
                        $itemId = $droppedItem['item_template_id'];
                        $itemName = $droppedItem['name'];
                        $quantity = $droppedItem['quantity'];
                        
                        // 获取物品更详细的信息
                        $stmt = $conn->prepare("
                            SELECT category, description, effects, stackable, max_stack
                            FROM item_templates 
                            WHERE id = ?
                        ");
                        $stmt->execute([$itemId]);
                        $itemDetails = $stmt->fetch(PDO::FETCH_ASSOC);
                        $itemCategory = $itemDetails['category'] ?? 'Unknown';
                        $isStackable = (bool)($itemDetails['stackable'] ?? 0);
                        $maxStack = (int)($itemDetails['max_stack'] ?? 1);
                        
                        // 转换为中文类别
                        $itemCategoryZh = Formulas::$itemCategoryNames[$itemCategory] ?? '未知';
                        
                        // 从失败者背包中移除物品
                        $stmt = $conn->prepare("
                            UPDATE player_inventory
                            SET quantity = quantity - ?
                            WHERE id = ? AND player_id = ?
                        ");
                        $stmt->execute([$quantity, $droppedItem['inventory_id'], $loserId]);
                        
                        // 检查物品数量是否为0，如果是，删除该物品
                        $stmt = $conn->prepare("
                            DELETE FROM player_inventory
                            WHERE id = ? AND quantity <= 0
                        ");
                        $stmt->execute([$droppedItem['inventory_id']]);
                        
                        // 根据物品是否可堆叠采取不同的处理方式
                        if ($isStackable) {
                            // 可堆叠物品的处理
                            $quantityToAdd = $quantity;
                            
                            // 查找现有未满堆叠的物品
                            $stmt = $conn->prepare("
                                SELECT id, quantity FROM player_inventory
                                WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND quantity < ?
                                ORDER BY quantity DESC
                            ");
                            $stmt->execute([$winnerId, $droppedItem['item_template_id'], $maxStack]);
                            $existingStacks = $stmt->fetchAll(PDO::FETCH_ASSOC);
                            
                            // 先填满现有堆叠
                            foreach ($existingStacks as $stack) {
                                if ($quantityToAdd <= 0) break;
                                
                                $spaceAvailable = $maxStack - $stack['quantity'];
                                $amountToFill = min($quantityToAdd, $spaceAvailable);
                                
                                $stmt = $conn->prepare("
                                    UPDATE player_inventory
                                    SET quantity = quantity + ?
                                    WHERE id = ?
                                ");
                                $stmt->execute([$amountToFill, $stack['id']]);
                                
                                $quantityToAdd -= $amountToFill;
                            }
                            
                            // 如果还有剩余物品，创建新堆叠
                            while ($quantityToAdd > 0) {
                                $newStackQuantity = min($quantityToAdd, $maxStack);
                                
                                $stmt = $conn->prepare("
                                    INSERT INTO player_inventory
                                    (player_id, item_template_id, quantity, is_equipped)
                                    VALUES (?, ?, ?, 0)
                                ");
                                $stmt->execute([$winnerId, $droppedItem['item_template_id'], $newStackQuantity]);
                                
                                $quantityToAdd -= $newStackQuantity;
                            }
                        } else {
                            // 不可堆叠物品的处理，一个一个添加
                            for ($i = 0; $i < $quantity; $i++) {
                                $stmt = $conn->prepare("
                                    INSERT INTO player_inventory
                                    (player_id, item_template_id, quantity, is_equipped)
                                    VALUES (?, ?, 1, 0)
                                ");
                                $stmt->execute([$winnerId, $droppedItem['item_template_id']]);
                            }
                        }
                        
                        $dropsLog[] = "【物品掉落】{$quantity} × [{$itemCategoryZh}] {$itemName}";
                        
                        // 如果物品有描述，添加到日志
                        if (!empty($itemDetails['description'])) {
                            $description = $itemDetails['description'];
                            if (mb_strlen($description) > 50) {
                                $description = mb_substr($description, 0, 50) . '...';
                            }
                            $dropsLog[] = "【物品描述】{$description}";
                        }
                    }
                    
                    // 查询双方当前物品数量
                    $stmt = $conn->prepare("
                        SELECT COUNT(*) as item_count FROM player_inventory 
                        WHERE player_id = ?
                    ");
                    $stmt->execute([$winnerId]);
                    $winnerItemCount = $stmt->fetchColumn();
                    
                    $stmt->execute([$loserId]);
                    $loserItemCount = $stmt->fetchColumn();
                    
                    $dropsLog[] = "━━ 背包物品统计 ━━";
                    $dropsLog[] = "{$winnerName} 当前拥有 {$winnerItemCount} 种物品";
                    $dropsLog[] = "{$loserName} 当前拥有 {$loserItemCount} 种物品";
                    
                    // 记录掉落日志
                    $this->logPvpBattle($battleId, $dropsLog);
                    
                    // 通知玩家
                    $pvpWinnerFd = $this->getPlayerFd($winnerId);
                    if ($pvpWinnerFd) {
                        $itemNames = [];
                        foreach ($drops['items'] as $item) {
                            // 使用category已经是中文类别
                            $itemNames[] = "{$item['quantity']}个[{$item['category']}]{$item['name']}";
                        }
                        
                        $itemsText = !empty($itemNames) ? "和" . implode("、", $itemNames) : "";
                        $message = "你从 {$loserName} 获得了 {$drops['gold']} 金币{$itemsText}！";
                        
                        $this->wsServer->sendMessage($pvpWinnerFd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => $message,
                            'type' => 'success'
                        ]);
                    }
                    
                    $pvpLoserFd = $this->getPlayerFd($loserId);
                    if ($pvpLoserFd) {
                        $itemNames = [];
                        foreach ($drops['items'] as $item) {
                            // 使用category已经是中文类别
                            $itemNames[] = "{$item['quantity']}个[{$item['category']}]{$item['name']}";
                        }
                        
                        $itemsText = !empty($itemNames) ? "和" . implode("、", $itemNames) : "";
                        $message = "你在PVP战斗中损失了 {$drops['gold']} 金币{$itemsText}！";
                        
                        $this->wsServer->sendMessage($pvpLoserFd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => $message,
                            'type' => 'danger'
                        ]);
                    }
                }
                
            } catch (Exception $e) {
                error_log("处理PVP掉落物品时出错: " . $e->getMessage());
                // 错误不影响游戏流程
            }
        }
        
        // 停止战斗定时器
        $this->wsServer->stopPvpBattleTimer($battleId);

        // 通知玩家战斗结束
        $challengerFd = $this->getPlayerFd($battleState['challenger']['id']);
        $defenderFd = $this->getPlayerFd($battleState['defender']['id']);
        
        if ($challengerFd) {
            $this->sendPvpBattleEnded($challengerFd, $battleState);
        }
        
        if ($defenderFd) {
            $this->sendPvpBattleEnded($defenderFd, $battleState);
        }
        
        // 清理战斗数据
        $this->cleanupPvpBattle($battleId);
    }
    
    /**
     * 清理PVP战斗数据
     * @param string $battleId 战斗ID
     */
    private function cleanupPvpBattle($battleId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = $this->activeBattles[$battleId];
        
        // 移除玩家战斗映射
        if (isset($this->playerBattleMap[$battleState['challenger']['id']])) {
            unset($this->playerBattleMap[$battleState['challenger']['id']]);
        }
        
        if (isset($this->playerBattleMap[$battleState['defender']['id']])) {
            unset($this->playerBattleMap[$battleState['defender']['id']]);
        }
        
        // 移除战斗状态
        unset($this->activeBattles[$battleId]);
    }
    
    /**
     * 处理设置PVP战斗意图
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleSetIntention($fd, $playerId, $data) {
        $battleId = $data['battle_id'] ?? null;
        $intention = $data['intention'] ?? null;
        
        if (!$battleId || !$intention) {
            return ['success' => false, 'message' => '缺少必要参数'];
        }
        
        if (!isset($this->activeBattles[$battleId])) {
            return ['success' => false, 'message' => '战斗不存在'];
        }
        
        $battleState = $this->activeBattles[$battleId];
        
        // 检查玩家是否在此战斗中
        if ($battleState['challenger']['id'] !== $playerId && $battleState['defender']['id'] !== $playerId) {
            return ['success' => false, 'message' => '你不是此战斗的参与者'];
        }
        
        // 检查意图是否有效
        $validActions = ['attack', 'use_hp_potion', 'use_mp_potion', 'skill', 'surrender'];
        
        // 新的意图格式：意图现在是一个包含action属性的对象
        $action = $intention['action'] ?? null;
        
        if (!$action || !in_array($action, $validActions)) {
            return ['success' => false, 'message' => '无效的意图动作'];
        }
        
        // 如果是技能动作，需要检查skill_id参数
        if ($action === 'skill' && !isset($intention['skill_id'])) {
            return ['success' => false, 'message' => '技能动作必须指定技能ID'];
        }
        
        // 设置玩家意图
        if (!isset($this->activeBattles[$battleId]['player_intentions'])) {
            $this->activeBattles[$battleId]['player_intentions'] = [];
        }
        
        $this->activeBattles[$battleId]['player_intentions'][$playerId] = $intention;
        
        return ['success' => true, 'message' => '意图已设置'];
    }
    
    /**
     * 处理PVP战斗中的直接动作
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handlePvpAction($fd, $playerId, $data) {
        $battleId = $data['battle_id'] ?? null;
        $action = $data['action'] ?? null;
        
        if (!$battleId || !$action) {
            return ['success' => false, 'message' => '缺少必要参数'];
        }
        
        if (!isset($this->activeBattles[$battleId])) {
            return ['success' => false, 'message' => '战斗不存在'];
        }
        
        $battleState = &$this->activeBattles[$battleId];
        
        // 检查玩家是否在此战斗中
        if ($battleState['challenger']['id'] !== $playerId && $battleState['defender']['id'] !== $playerId) {
            return ['success' => false, 'message' => '你不是此战斗的参与者'];
        }
        
        // 注意：不再在此处减少技能冷却时间
        // 冷却时间的减少统一由executePvpAction中调用的processPvpSkillCooldowns处理
        
        // 处理不同类型的动作
        switch ($action) {
            case 'use_skill':
                $skillId = $data['skill_id'] ?? null;
                if (!$skillId) {
                    return ['success' => false, 'message' => '缺少技能ID'];
                }
                
                // 检查玩家是否可以行动
                $atbStatus = $this->pvpBattleSystem->getAtbStatus($battleState);
                $isChallenger = ($playerId === $battleState['challenger']['id']);
                $playerAtb = $isChallenger ? $atbStatus['challengerATB'] : $atbStatus['defenderATB'];
                
                if ($playerAtb < $atbStatus['atbMax']) {
                    return ['success' => false, 'message' => '你的行动条还未准备好'];
                }
                
                // 处理玩家回合开始时的效果
                $effectsResult = $this->pvpBattleSystem->processEffectsTurn($battleState, $playerId);
                
                // 如果处理效果时产生了日志，广播给玩家
                if (!empty($effectsResult['log'])) {
                    $effectUpdateResult = [
                        'log' => $effectsResult['log']
                    ];
                    $this->broadcastPvpBattleUpdate($battleId, $playerId, 'effects_update', $effectUpdateResult);
                }
                
                // 执行技能
                $result = $this->pvpBattleSystem->handleUseSkill($battleState, $playerId, $skillId);
                
                // 重置ATB
                if ($isChallenger) {
                    $battleState['atb_state']['challengerATB'] = 0;
                } else {
                    $battleState['atb_state']['defenderATB'] = 0;
                }
                
                        // 记录战斗日志
        if (!empty($result['log'])) {
            $this->logPvpBattle($battleId, $result['log']);
        }
        
        // 广播战斗更新
        $this->broadcastPvpBattleUpdate($battleId, $playerId, 'use_skill', $result);
        
        // 广播ATB状态更新
        $this->broadcastPvpAtbStatus($battleId);
                
                // 检查战斗是否结束
                if (isset($battleState['is_over']) && $battleState['is_over']) {
                    $this->endPvpBattle($battleId);
                }
                
                return ['success' => true, 'message' => '技能已使用'];
                
            // 可以添加其他直接动作类型
            
            default:
                return ['success' => false, 'message' => '不支持的动作类型'];
        }
    }
    
    /**
     * 获取玩家的PVP战斗意图
     * @param string $battleId 战斗ID
     * @param string $playerId 玩家ID
     * @return string|null 玩家意图
     */
    private function getPlayerIntention($battleId, $playerId) {
        if (!isset($this->activeBattles[$battleId])) {
            return 'attack';
        }
        
        $battleState = $this->activeBattles[$battleId];
        
        if (!isset($battleState['player_intentions']) || !isset($battleState['player_intentions'][$playerId])) {
            return 'attack';
        }
        
        return $battleState['player_intentions'][$playerId];
    }
    
    /**
     * 清除玩家PVP意图
     * @param string $battleId 战斗ID
     * @param string $playerId 玩家ID
     */
    private function clearPlayerIntention($battleId, $playerId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = &$this->activeBattles[$battleId];
        
        if (isset($battleState['player_intentions']) && isset($battleState['player_intentions'][$playerId])) {
            unset($battleState['player_intentions'][$playerId]);
        }
    }
    
    /**
     * 处理PVP投降请求
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleSurrender($fd, $playerId, $data) {
        $battleId = $data['battle_id'] ?? null;
        
        if (!$battleId) {
            return ['success' => false, 'message' => '缺少战斗ID'];
        }
        
        if (!isset($this->activeBattles[$battleId])) {
            return ['success' => false, 'message' => '战斗不存在'];
        }
        
        $battleState = $this->activeBattles[$battleId];
        
        // 检查玩家是否在此战斗中
        if ($battleState['challenger']['id'] !== $playerId && $battleState['defender']['id'] !== $playerId) {
            return ['success' => false, 'message' => '你不是此战斗的参与者'];
        }
        
        // 检查是否为直接攻击模式
        if (isset($battleState['is_direct_attack']) && $battleState['is_direct_attack']) {
            return ['success' => false, 'message' => '在强制PVP模式下无法投降！'];
        }
        
        // 处理投降
        $this->handlePvpSurrender($battleId, $playerId);
        
        return [
            'success' => true,
            'message' => '你已投降，战斗结束'
        ];
    }
    
    /**
     * 处理获取PVP排行榜请求
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleGetLeaderboard($fd, $playerId) {
        // 从数据库获取PVP排行榜数据
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT ps.player_id, a.username as player_name,
                       ps.total_battles, ps.wins, ps.losses, ps.draws,
                       CASE WHEN ps.total_battles > 0 
                            THEN ROUND((ps.wins / ps.total_battles) * 100, 2)
                            ELSE 0 
                       END as win_rate
                FROM pvp_player_stats ps
                JOIN accounts a ON ps.player_id = a.id
                ORDER BY ps.wins DESC, win_rate DESC
                LIMIT 10
            ");
            $stmt->execute();
            $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 发送排行榜数据
            $this->sendPvpLeaderboard($fd, $leaderboard);
            
            return ['success' => true, 'message' => '已获取PVP排行榜'];
            
        } catch (Exception $e) {
            error_log("获取PVP排行榜出错: " . $e->getMessage());
            return ['success' => false, 'message' => '获取排行榜失败'];
        }
    }
    
    /**
     * 处理获取等级排行榜请求
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @return array 处理结果
     */
    public function handleGetLevelRanking($fd, $playerId) {
        // 从数据库获取等级排行榜数据
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT 
                    pa.account_id as player_id, 
                    a.username as player_name,
                    pa.level,
                    j.name as job
                FROM player_attributes pa
                JOIN accounts a ON pa.account_id = a.id
                LEFT JOIN jobs j ON pa.current_job_id = j.id
                ORDER BY pa.level DESC, pa.experience DESC
                LIMIT 10
            ");
            $stmt->execute();
            $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 发送排行榜数据
            $this->sendLevelRanking($fd, $leaderboard);
            
            return ['success' => true, 'message' => '已获取等级排行榜'];
            
        } catch (Exception $e) {
            error_log("获取等级排行榜出错: " . $e->getMessage());
            return ['success' => false, 'message' => '获取排行榜失败'];
        }
    }
    
    /**
     * 处理获取英雄排行榜请求（善恶值高的玩家）
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @return array 处理结果
     */
    public function handleGetHeroRanking($fd, $playerId) {
        // 从数据库获取英雄排行榜数据（善恶值大于0且从高到低排序）
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT 
                    pa.account_id as player_id, 
                    a.username as player_name,
                    pa.level,
                    pa.karma as alignment_value,
                    CASE 
                        WHEN pa.karma >= 100 THEN '圣光骑士'
                        WHEN pa.karma >= 50 THEN '正义使者'
                        WHEN pa.karma >= 10 THEN '善良之人'
                        ELSE '平民'
                    END as title
                FROM player_attributes pa
                JOIN accounts a ON pa.account_id = a.id
                WHERE pa.karma > 0
                ORDER BY pa.karma DESC, pa.level DESC
                LIMIT 10
            ");
            $stmt->execute();
            $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 发送排行榜数据
            $this->sendHeroRanking($fd, $leaderboard);
            
            return ['success' => true, 'message' => '已获取英雄排行榜'];
            
        } catch (Exception $e) {
            error_log("获取英雄排行榜出错: " . $e->getMessage());
            return ['success' => false, 'message' => '获取排行榜失败'];
        }
    }
    
    /**
     * 处理获取黑手排行榜请求（善恶值低的玩家）
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @return array 处理结果
     */
    public function handleGetVillainRanking($fd, $playerId) {
        // 从数据库获取黑手排行榜数据（善恶值小于等于0且从低到高排序）
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT 
                    pa.account_id as player_id, 
                    a.username as player_name,
                    pa.level,
                    pa.karma as alignment_value,
                    CASE 
                        WHEN pa.karma <= -100 THEN '混沌魔王'
                        WHEN pa.karma <= -50 THEN '邪恶术士'
                        WHEN pa.karma <= -10 THEN '黑暗使者'
                        ELSE '落魄之人'
                    END as title
                FROM player_attributes pa
                JOIN accounts a ON pa.account_id = a.id
                WHERE pa.karma < 0
                ORDER BY pa.karma ASC, pa.level DESC
                LIMIT 10
            ");
            $stmt->execute();
            $leaderboard = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 发送排行榜数据
            $this->sendVillainRanking($fd, $leaderboard);
            
            return ['success' => true, 'message' => '已获取黑手排行榜'];
            
        } catch (Exception $e) {
            error_log("获取黑手排行榜出错: " . $e->getMessage());
            return ['success' => false, 'message' => '获取排行榜失败'];
        }
    }
    
    /**
     * 处理获取个人PVP统计请求
     * @param int $fd WebSocket连接ID
     * @param string $playerId 玩家ID
     * @return array 处理结果
     */
    public function handleGetStats($fd, $playerId) {
        // 从数据库获取玩家PVP统计数据
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT total_battles, wins, losses, draws, 
                       DATE_FORMAT(last_battle_time, '%Y-%m-%d %H:%i') as last_battle_time
                FROM pvp_player_stats 
                WHERE player_id = ?
            ");
            $stmt->execute([$playerId]);
            $stats = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$stats) {
                $stats = [
                    'total_battles' => 0,
                    'wins' => 0,
                    'losses' => 0,
                    'draws' => 0,
                    'last_battle_time' => null
                ];
            }
            
            // 获取最近10条战斗记录
            $battleRecords = $this->getRecentBattleRecords($playerId, 10);
            $stats['battle_records'] = $battleRecords;
            
            // 发送统计数据
            $this->sendPvpStats($fd, $stats);
            
            return ['success' => true, 'message' => '已获取PVP统计'];
            
        } catch (Exception $e) {
            error_log("获取PVP统计出错: " . $e->getMessage());
            return ['success' => false, 'message' => '获取统计失败'];
        }
    }
    
    /**
     * 获取玩家最近的战斗记录
     * @param string $playerId 玩家ID
     * @param int $limit 记录数量限制
     * @return array 战斗记录列表
     */
    private function getRecentBattleRecords($playerId, $limit = 10) {
        try {
            $conn = $this->db->getConnection();
            
            // 查询玩家参与的最近战斗记录（作为挑战者或防御者）
            $stmt = $conn->prepare("
                SELECT 
                    r.battle_id,
                    r.challenger_id,
                    (SELECT username FROM accounts WHERE id = r.challenger_id) AS challenger_name,
                    r.defender_id,
                    (SELECT username FROM accounts WHERE id = r.defender_id) AS defender_name,
                    r.winner_id,
                    DATE_FORMAT(r.created_at, '%Y-%m-%d %H:%i') AS battle_time,
                    r.rounds
                FROM 
                    pvp_battle_records r
                WHERE 
                    r.challenger_id = ? OR r.defender_id = ?
                ORDER BY 
                    r.created_at DESC
                LIMIT ?
            ");
            
            $stmt->execute([$playerId, $playerId, $limit]);
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 格式化记录，添加额外信息
            foreach ($records as &$record) {
                // 确定当前玩家是挑战者还是防御者
                $isChallenger = $record['challenger_id'] == $playerId;
                
                // 确定对手名称
                $record['opponent_name'] = $isChallenger ? $record['defender_name'] : $record['challenger_name'];
                
                // 确定对战结果
                if ($record['winner_id'] == null) {
                    $record['result'] = '平局';
                } else if ($record['winner_id'] == $playerId) {
                    $record['result'] = '胜利';
                } else {
                    $record['result'] = '失败';
                }
                
                // 添加玩家角色
                $record['player_role'] = $isChallenger ? '挑战者' : '防御者';
            }
            
            return $records;
            
        } catch (Exception $e) {
            error_log("获取战斗记录出错: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * 向客户端发送PVP挑战通知
     * @param int $fd 目标客户端连接ID
     * @param string $challengeId 挑战ID
     * @param string $challengerId 挑战者ID
     * @param string $challengerName 挑战者名称
     * @param int $expiryTime 过期时间戳
     */
    private function sendPvpChallenge($fd, $challengeId, $challengerId, $challengerName, $expiryTime) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_CHALLENGE, [
            'challenge_id' => $challengeId,
            'challenger_id' => $challengerId,
            'challenger_name' => $challengerName,
            'expiry_time' => $expiryTime
        ]);
    }
    
    /**
     * 向客户端发送PVP挑战响应
     * @param int $fd 目标客户端连接ID
     * @param string $challengeId 挑战ID
     * @param bool $accepted 是否接受
     * @param string $responderName 响应者名称
     */
    private function sendPvpChallengeResponse($fd, $challengeId, $accepted, $responderName) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_CHALLENGE_RESPONSE, [
            'challenge_id' => $challengeId,
            'accepted' => $accepted,
            'responder_name' => $responderName,
            'message' => $accepted ? "玩家 {$responderName} 接受了你的PVP挑战!" : "玩家 {$responderName} 拒绝了你的PVP挑战。"
        ]);
    }
    
    /**
     * 向客户端发送PVP战斗开始通知
     * @param int $fd 目标客户端连接ID
     * @param array $battleState 战斗状态
     */
    private function sendPvpBattleStarted($fd, $battleState) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_BATTLE_STARTED, [
            'battle_id' => $battleState['battle_id'],
            'challenger' => $battleState['challenger'],
            'defender' => $battleState['defender'],
            'is_direct_attack' => $battleState['is_direct_attack'] ?? false,
            'is_redname_battle' => $battleState['is_redname_battle'] ?? false
        ]);
    }
    
    /**
     * 向参与战斗的客户端广播PVP战斗更新
     * @param string $battleId 战斗ID
     * @param string $actorId 行动者ID
     * @param string $actionType 行动类型
     * @param array $actionResult 行动结果
     */
    private function broadcastPvpBattleUpdate($battleId, $actorId, $actionType, $actionResult) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = $this->activeBattles[$battleId];
        $challengerFd = $this->getPlayerFd($battleState['challenger']['id']);
        $defenderFd = $this->getPlayerFd($battleState['defender']['id']);
        
        // 处理技能释放结果
        if ($actionType === 'use_skill') {
            // 添加攻击信息，如闪避、暴击等
            if (isset($actionResult['damage'])) {
                $actionResult['is_dodged'] = $actionResult['is_dodged'] ?? false;
                $actionResult['is_critical'] = $actionResult['is_critical'] ?? false;
                $actionResult['physical_damage'] = $actionResult['physical_damage'] ?? 0;
                $actionResult['elemental_damage'] = $actionResult['elemental_damage'] ?? 0;
            }
            
            // 如果有治疗量，添加治疗信息
            if (isset($actionResult['heal_amount'])) {
                $actionResult['heal_target_id'] = $actorId; // 假设治疗目标是自己
            }
        }
        
        $updatePayload = [
            'battle_id' => $battleId,
            'battle_state' => $battleState,
            'actor_id' => $actorId,
            'action_type' => $actionType,
            'action_result' => $actionResult
        ];
        
        if ($challengerFd) {
            $this->wsServer->sendMessage($challengerFd, MessageProtocol::S2C_PVP_BATTLE_UPDATE, $updatePayload);
        }
        
        if ($defenderFd) {
            $this->wsServer->sendMessage($defenderFd, MessageProtocol::S2C_PVP_BATTLE_UPDATE, $updatePayload);
        }
    }
    
    /**
     * 向参与战斗的客户端广播ATB状态更新
     * @param string $battleId 战斗ID
     */
    private function broadcastPvpAtbStatus($battleId) {
        if (!isset($this->activeBattles[$battleId])) {
            return;
        }
        
        $battleState = $this->activeBattles[$battleId];
        $atbStatus = $this->pvpBattleSystem->getAtbStatus($battleState);
        
        $challengerFd = $this->getPlayerFd($battleState['challenger']['id']);
        $defenderFd = $this->getPlayerFd($battleState['defender']['id']);
        
        if ($challengerFd) {
            $this->wsServer->sendMessage($challengerFd, MessageProtocol::S2C_PVP_ATB_STATUS_UPDATE, $atbStatus);
        }
        
        if ($defenderFd) {
            $this->wsServer->sendMessage($defenderFd, MessageProtocol::S2C_PVP_ATB_STATUS_UPDATE, $atbStatus);
        }
    }
    
    /**
     * 向客户端发送PVP战斗结束通知
     * @param int $fd 目标客户端连接ID
     * @param array $battleState 战斗状态
     */
    private function sendPvpBattleEnded($fd, $battleState) {
        // 获取玩家当前的HP和MP信息
        $playerId = null;
        
        // 确定当前接收消息的玩家是谁
        if ($fd == $this->getPlayerFd($battleState['challenger']['id'])) {
            $playerId = $battleState['challenger']['id'];
        } else if ($fd == $this->getPlayerFd($battleState['defender']['id'])) {
            $playerId = $battleState['defender']['id'];
        }
        
        // 获取玩家最新的属性信息
        $playerAttributes = null;
        if ($playerId) {
            try {
                $conn = $this->db->getConnection();
                $stmt = $conn->prepare("
                    SELECT hp, max_hp, mp, max_mp 
                    FROM player_attributes 
                    WHERE account_id = ?
                ");
                $stmt->execute([$playerId]);
                $playerAttributes = $stmt->fetch(PDO::FETCH_ASSOC);
            } catch (Exception $e) {
                error_log("获取玩家属性信息出错: " . $e->getMessage());
            }
        }
        
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_BATTLE_ENDED, [
            'battle_id' => $battleState['battle_id'],
            'battle_state' => $battleState,
            'player_attributes' => $playerAttributes
        ]);
    }
    
    /**
     * 向客户端发送PVP排行榜数据
     * @param int $fd 目标客户端连接ID
     * @param array $leaderboard 排行榜数据
     */
    private function sendPvpLeaderboard($fd, $leaderboard) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_LEADERBOARD, [
            'leaderboard' => $leaderboard
        ]);
    }
    
    /**
     * 向客户端发送PVP统计数据
     * @param int $fd 目标客户端连接ID
     * @param array $stats 统计数据
     */
    private function sendPvpStats($fd, $stats) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_PVP_STATS, [
            'stats' => $stats
        ]);
    }
    
    /**
     * 向客户端发送等级排行榜数据
     * @param int $fd 目标客户端连接ID
     * @param array $leaderboard 排行榜数据
     */
    private function sendLevelRanking($fd, $leaderboard) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_LEVEL_RANKING, [
            'leaderboard' => $leaderboard
        ]);
    }
    
    /**
     * 向客户端发送英雄排行榜数据
     * @param int $fd 目标客户端连接ID
     * @param array $leaderboard 排行榜数据
     */
    private function sendHeroRanking($fd, $leaderboard) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_HERO_RANKING, [
            'leaderboard' => $leaderboard
        ]);
    }
    
    /**
     * 向客户端发送黑手排行榜数据
     * @param int $fd 目标客户端连接ID
     * @param array $leaderboard 排行榜数据
     */
    private function sendVillainRanking($fd, $leaderboard) {
        $this->wsServer->sendMessage($fd, MessageProtocol::S2C_VILLAIN_RANKING, [
            'leaderboard' => $leaderboard
        ]);
    }
    
    /**
     * 获取玩家的WebSocket连接ID
     * @param string $playerId 玩家ID
     * @return int|null 连接ID或null
     */
    private function getPlayerFd($playerId) {
        return $this->wsServer->getPlayerFd($playerId);
    }
    
    /**
     * 获取玩家信息
     * @param string $playerId 玩家ID
     * @return array|null 玩家信息或null
     */
    private function getPlayerInfo($playerId) {
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT a.id, a.username
                FROM accounts a
                WHERE a.id = ?
            ");
            $stmt->execute([$playerId]);
            return $stmt->fetch(PDO::FETCH_ASSOC);
        } catch (Exception $e) {
            error_log("获取玩家信息出错: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 检查玩家是否在战斗中
     * @param string $playerId 玩家ID
     * @return bool 是否在战斗中
     */
    public function isPlayerInBattle($playerId) {
        return isset($this->playerBattleMap[$playerId]);
    }
    
    /**
     * 获取玩家当前所在场景ID
     * @param string $playerId 玩家ID
     * @return int|null 玩家当前场景ID，如果无法获取则返回null
     */
    private function getPlayerCurrentScene($playerId) {
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT current_scene_id FROM player_attributes
                WHERE account_id = ?
            ");
            $stmt->execute([$playerId]);
            return $stmt->fetchColumn();
        } catch (Exception $e) {
            error_log("获取玩家场景信息出错: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 获取玩家当前的战斗ID
     * @param string $playerId 玩家ID
     * @return string|null 战斗ID或null
     */
    public function getPlayerBattleId($playerId) {
        return $this->playerBattleMap[$playerId] ?? null;
    }
    
    /**
     * 处理PVP战斗中玩家的技能冷却
     * @param array &$battleState 战斗状态引用
     * @param string $playerId 玩家ID
     */
    private function processPvpSkillCooldowns(array &$battleState, string $playerId): void {
        if (!empty($battleState['skill_cooldowns'][$playerId])) {
            foreach ($battleState['skill_cooldowns'][$playerId] as $skillId => &$turns) {
                $turns--;
                if ($turns <= 0) {
                    unset($battleState['skill_cooldowns'][$playerId][$skillId]);
                }
            }
            unset($turns); // 断开引用
            if (empty($battleState['skill_cooldowns'][$playerId])) {
                unset($battleState['skill_cooldowns'][$playerId]);
            }
        }
    }
    
    /**
     * 检查玩家是否存活
     * @param string $playerId 玩家ID
     * @return bool 是否存活
     */
    private function isPlayerAlive($playerId) {
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT hp FROM player_attributes
                WHERE account_id = ?
            ");
            $stmt->execute([$playerId]);
            $currentHp = $stmt->fetchColumn();
            
            return $currentHp > 0;
        } catch (Exception $e) {
            error_log("检查玩家存活状态出错: " . $e->getMessage());
            return false; // 出错时默认为不存活，以防止错误挑战
        }
    }
    
    /**
     * 处理直接攻击请求（无需目标玩家接受）
     * @param int $fd 攻击者的WebSocket连接ID
     * @param string $playerId 攻击者ID
     * @param array $data 请求数据
     * @return array 处理结果
     */
    public function handleDirectAttack($fd, $playerId, $data) {
        $targetPlayerId = $data['target_player_id'] ?? null;
        
        if (!$targetPlayerId) {
            return ['success' => false, 'message' => '目标玩家ID不能为空'];
        }
        
        // 检查目标玩家是否在线
        $targetFd = $this->getPlayerFd($targetPlayerId);
        if (!$targetFd) {
            return ['success' => false, 'message' => '目标玩家不在线'];
        }
        
        // 检查玩家是否已经在战斗中
        if ($this->isPlayerInBattle($playerId)) {
            return ['success' => false, 'message' => '你已经在战斗中'];
        }
        
        if ($this->isPlayerInBattle($targetPlayerId)) {
            return ['success' => false, 'message' => '目标玩家已经在战斗中'];
        }
        
        // 检查攻击者和目标玩家是否在同一场景
        $attackerSceneId = $this->getPlayerCurrentScene($playerId);
        $targetSceneId = $this->getPlayerCurrentScene($targetPlayerId);
        
        if (!$attackerSceneId || !$targetSceneId) {
            return ['success' => false, 'message' => '无法获取玩家场景信息'];
        }
        
        if ($attackerSceneId !== $targetSceneId) {
            return ['success' => false, 'message' => '只能攻击同一场景中的玩家'];
        }
        
        // 检查当前场景是否为安全区，以及目标玩家的善恶值
        try {
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("SELECT is_safe_zone FROM scenes WHERE id = ?");
            $stmt->execute([$attackerSceneId]);
            $isSafeZone = $stmt->fetchColumn();

            if ($isSafeZone == 1) {
                // 在安全区，但需要检查目标玩家的善恶值
                $stmt = $conn->prepare("SELECT karma FROM player_attributes WHERE account_id = ?");
                $stmt->execute([$targetPlayerId]);
                $targetKarma = $stmt->fetchColumn();

                // 如果目标玩家善恶值为负数（红名），即使在安全区也允许攻击
                if ($targetKarma >= 0) {
                    return ['success' => false, 'message' => '安全区内不能对善良玩家发起攻击'];
                } else {
                    // 目标是红名玩家，在安全区也可以攻击，给出特殊提示
                    error_log("安全区内攻击红名玩家: 攻击者={$playerId}, 目标={$targetPlayerId}, 目标善恶值={$targetKarma}");
                }
            }
        } catch (Exception $e) {
            error_log("检查安全区状态出错: " . $e->getMessage());
            return ['success' => false, 'message' => '检查安全区状态时出错'];
        }
        
        // 检查双方玩家是否存活
        $attackerAlive = $this->isPlayerAlive($playerId);
        $targetAlive = $this->isPlayerAlive($targetPlayerId);
        
        if (!$attackerAlive) {
            return ['success' => false, 'message' => '你已经死亡，无法发起攻击'];
        }
        
        if (!$targetAlive) {
            return ['success' => false, 'message' => '目标玩家已经死亡，无法发起攻击'];
        }
        
        // 获取攻击者信息
        $attackerInfo = $this->getPlayerInfo($playerId);
        $targetInfo = $this->getPlayerInfo($targetPlayerId);
        if (!$attackerInfo || !$targetInfo) {
            return ['success' => false, 'message' => '无法获取玩家信息'];
        }
        
        // 检查目标玩家的karma值
        try {
            $conn = $this->db->getConnection();
            
            // 获取目标玩家的善恶值
            $stmt = $conn->prepare("SELECT karma FROM player_attributes WHERE account_id = ?");
            $stmt->execute([$targetPlayerId]);
            $targetKarma = $stmt->fetchColumn();
            
            // 判断是否为洗红名战斗和更新攻击者善恶值
            $isRedNameBattle = false;
            
            if ($targetKarma < 0) {
                // 目标是红名玩家，标记为洗红名战斗
                $isRedNameBattle = true;
                
                // 记录攻击者发起了洗红名战斗
                $this->wsServer->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => "你正对红名玩家 {$targetInfo['username']} 发起洗礼！",
                    'type' => 'info'
                ]);
            } else {
                // 目标是正常玩家，攻击者善恶值-1
                $stmt = $conn->prepare("UPDATE player_attributes SET karma = karma - 1 WHERE account_id = ?");
                $stmt->execute([$playerId]);
                
                // 通知攻击者善恶值减少
                $this->wsServer->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => "你攻击其他玩家，罪孽加深了！",
                    'type' => 'warning'
                ]);
            }
        } catch (Exception $e) {
            error_log("更新玩家善恶值出错: " . $e->getMessage());
            // 即使出错也继续处理战斗，不影响游戏流程
        }
        
        // 直接开始PVP战斗，无需等待对方接受
        $battleResult = $this->startPvpBattle($playerId, $targetPlayerId, $attackerSceneId, $isRedNameBattle ?? false, true);
        
        if (!$battleResult['success']) {
            return $battleResult;
        }
        
        // 向被攻击者发送通知
        $this->wsServer->sendMessage($targetFd, MessageProtocol::S2C_INFO_MESSAGE, [
            'message' => "你被玩家 {$attackerInfo['username']} 突然攻击了！",
            'type' => 'warning'
        ]);
        
        return [
            'success' => true,
            'message' => "你对玩家 {$targetInfo['username']} 发起了攻击！",
            'battle_id' => $battleResult['battle_id']
        ];
    }
} 