<?php
require_once 'auth.php';
$pageTitle = '大陆区域管理';
$extra_css = '
<link rel="stylesheet" href="continents.css">
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
';
$extra_js = '<script src="continents.js"></script>';
require_once 'layout_header.php';
?>

<div class="container">
    <div class="header-section">
        <h1>🌍 大陆区域管理</h1>
        <div class="header-actions">
            <button id="debug-layers-btn" class="btn btn-info" title="查看图层同步状态" onclick="window.open('debug_layers.php', '_blank')">🔍 调试图层</button>
            <button id="sync-layers-btn" class="btn btn-warning" title="同步大陆图层到场景管理系统">🔄 同步图层</button>
            <button id="add-continent-btn" class="btn btn-primary">+ 添加大陆</button>
            <button id="back-to-scenes-btn" class="btn btn-secondary">返回场景管理</button>
        </div>
    </div>

    <!-- 大陆列表 -->
    <div class="continents-section">
        <h2>大陆列表</h2>
        <div id="continents-list" class="continents-grid">
            <div class="loading-placeholder">正在加载大陆数据...</div>
        </div>
    </div>

    <!-- 区域管理区 -->
    <div id="zones-section" class="zones-section" style="display: none;">
        <div class="zones-header">
            <h2 id="zones-title">区域管理</h2>
            <div class="zones-actions">
                <button id="add-zone-btn" class="btn btn-primary">+ 添加区域</button>
                <button id="close-zones-btn" class="btn btn-secondary">关闭</button>
            </div>
        </div>
        <div id="zones-list" class="zones-grid">
            <div class="loading-placeholder">正在加载区域数据...</div>
        </div>
    </div>

    <!-- 场景管理区 -->
    <div id="scenes-section" class="scenes-section" style="display: none;">
        <div class="scenes-header">
            <h2 id="scenes-title">场景管理</h2>
            <div class="scenes-actions">
                <button id="close-scenes-btn" class="btn btn-secondary">关闭</button>
            </div>
        </div>

        <!-- 场景网格地图 -->
        <div class="scenes-map-container">
            <div class="map-controls">
                <label>地图范围: </label>
                <input type="number" id="scene-min-x" value="-5" style="width: 60px;">
                <input type="number" id="scene-min-y" value="-5" style="width: 60px;">
                <span>到</span>
                <input type="number" id="scene-max-x" value="5" style="width: 60px;">
                <input type="number" id="scene-max-y" value="5" style="width: 60px;">
                <button id="redraw-scenes-map" class="btn btn-sm btn-secondary">重绘地图</button>
                <span class="control-divider">|</span>
                <label>图层: </label>
                <select id="scene-layer-select" style="width: 80px;">
                    <option value="0">Z=0</option>
                </select>
            </div>
            <div id="scenes-map" class="scenes-map-grid">
                <div class="loading-placeholder">正在加载场景地图...</div>
            </div>
        </div>

        <!-- 场景统计和列表 -->
        <div class="scenes-list-container">
            <div class="scenes-stats-header">
                <h3>场景列表</h3>
                <div id="scenes-stats" class="scenes-stats">
                    <div class="stat-item">
                        <div class="stat-value" id="total-scenes">0</div>
                        <div class="stat-label">总场景</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="safe-zones">0</div>
                        <div class="stat-label">安全区</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value" id="layers-count">0</div>
                        <div class="stat-label">图层数</div>
                    </div>
                </div>
            </div>
            <div id="scenes-list" class="scenes-list">
                <div class="loading-placeholder">正在加载场景列表...</div>
            </div>
        </div>
    </div>
</div>

<!-- 大陆编辑模态框 -->
<div id="continent-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="continent-modal-title">添加大陆</h3>
            <span class="close">&times;</span>
        </div>
        <form id="continent-form">
            <input type="hidden" id="continent-id" name="id">
            
            <div class="form-group">
                <label for="continent-display-name">显示名称:</label>
                <input type="text" id="continent-display-name" name="display_name" required
                       placeholder="例如: 奥术联邦">
            </div>

            <div class="form-group">
                <label for="continent-name">大陆ID:</label>
                <div class="input-group">
                    <input type="text" id="continent-name" name="id" required
                           placeholder="将根据显示名称自动生成"
                           pattern="[a-z_]+" title="只能包含小写字母和下划线" readonly>
                    <button type="button" id="generate-continent-id" class="btn btn-secondary btn-sm">🔄 重新生成</button>
                    <button type="button" id="edit-continent-id" class="btn btn-secondary btn-sm">✏️ 手动编辑</button>
                </div>
                <small class="form-hint">大陆的唯一标识符，根据显示名称自动生成</small>
            </div>
            
            <div class="form-group">
                <label for="continent-description">描述:</label>
                <textarea id="continent-description" name="description" rows="3" 
                          placeholder="大陆的详细描述..."></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="continent-color">主题颜色:</label>
                    <input type="color" id="continent-color" name="color" value="#007bff">
                </div>
                <div class="form-group">
                    <label for="continent-z-level">Z坐标层级:</label>
                    <input type="number" id="continent-z-level" name="z_level" min="0" max="100"
                           placeholder="自动分配">
                    <small class="form-hint">留空则自动分配下一个可用Z坐标</small>
                </div>
            </div>

            <div class="form-row">
                <div class="form-group">
                    <label for="continent-sort-order">排序顺序:</label>
                    <input type="number" id="continent-sort-order" name="sort_order" value="0" min="0">
                </div>
                <div class="form-group">
                    <label for="continent-is-active">状态:</label>
                    <select id="continent-is-active" name="is_active">
                        <option value="1">启用</option>
                        <option value="0">禁用</option>
                    </select>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('continent-modal')">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 区域编辑模态框 -->
<div id="zone-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3 id="zone-modal-title">添加区域</h3>
            <span class="close">&times;</span>
        </div>
        <form id="zone-form">
            <input type="hidden" id="zone-id" name="id">
            <input type="hidden" id="zone-continent" name="continent">
            
            <div class="form-group">
                <label for="zone-display-name">区域名称:</label>
                <input type="text" id="zone-display-name" name="name" required
                       placeholder="例如: 奥术学院区">
            </div>

            <div class="form-group">
                <label for="zone-name">区域ID:</label>
                <div class="input-group">
                    <input type="text" id="zone-name" name="id" required
                           placeholder="将根据区域名称自动生成"
                           pattern="[a-z0-9_]+" title="只能包含小写字母、数字和下划线" readonly>
                    <button type="button" id="generate-zone-id" class="btn btn-secondary btn-sm">🔄 重新生成</button>
                    <button type="button" id="edit-zone-id" class="btn btn-secondary btn-sm">✏️ 手动编辑</button>
                </div>
                <small class="form-hint">区域的唯一标识符，根据区域名称自动生成</small>
            </div>
            
            <div class="form-group">
                <label for="zone-description">描述:</label>
                <textarea id="zone-description" name="description" rows="3" 
                          placeholder="区域的详细描述..."></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="zone-min-level">最低等级:</label>
                    <input type="number" id="zone-min-level" name="min_level" value="1" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="zone-max-level">最高等级:</label>
                    <input type="number" id="zone-max-level" name="max_level" min="1" max="100" 
                           placeholder="留空表示无上限">
                </div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="closeModal('zone-modal')">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 确认删除模态框 -->
<div id="confirm-modal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>确认删除</h3>
            <span class="close">&times;</span>
        </div>
        <div class="modal-body">
            <p id="confirm-message">确定要删除这个项目吗？</p>
            <p class="warning-text">⚠️ 此操作不可撤销！</p>
        </div>
        <div class="form-actions">
            <button id="confirm-delete-btn" class="btn btn-danger">确认删除</button>
            <button type="button" class="btn btn-secondary" onclick="closeModal('confirm-modal')">取消</button>
        </div>
    </div>
</div>

<!-- Toast 通知 -->
<div id="toast" class="toast"></div>

<?php require_once 'layout_footer.php'; ?>
