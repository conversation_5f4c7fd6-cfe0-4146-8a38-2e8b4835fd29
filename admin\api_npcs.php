<?php
// admin/api_npcs.php
require_once '../config/Database.php';
require_once 'auth.php';

header('Content-Type: application/json');

// 检查用户是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

$db = Database::getInstance();
$conn = $db->getConnection();
$action = $_REQUEST['action'] ?? '';

try {
    switch ($action) {
        case 'get_npc_inventory':
            $npcTemplateId = $_GET['npc_id'] ?? 0;
            
            if (!$npcTemplateId) {
                throw new Exception('未提供NPC模板ID');
            }
            
            // 获取该NPC模板的所有实例ID
            $stmt = $conn->prepare("SELECT id FROM npc_instances WHERE template_id = ?");
            $stmt->execute([$npcTemplateId]);
            $instanceIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($instanceIds)) {
                // 如果没有实例，返回空数据
                echo json_encode([
                    'success' => true,
                    'equipped' => [],
                    'inventory' => []
                ]);
                break;
            }
            
            // 构建IN查询的占位符
            $placeholders = implode(',', array_fill(0, count($instanceIds), '?'));
            
            // 获取NPC的已装备物品
            $stmt = $conn->prepare("
                SELECT ni.id, ni.item_template_id, ni.slot, ni.quantity, ni.is_equipped,
                       it.name, it.category, it.description
                FROM npc_inventories ni
                JOIN item_templates it ON ni.item_template_id = it.id
                WHERE ni.npc_instance_id IN ($placeholders) AND ni.is_equipped = 1
            ");
            $stmt->execute($instanceIds);
            $equipped = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 获取NPC的背包物品
            $stmt = $conn->prepare("
                SELECT ni.id, ni.item_template_id, ni.slot, ni.quantity, ni.is_equipped,
                       it.name, it.category, it.description
                FROM npc_inventories ni
                JOIN item_templates it ON ni.item_template_id = it.id
                WHERE ni.npc_instance_id IN ($placeholders) AND ni.is_equipped = 0
            ");
            $stmt->execute($instanceIds);
            $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'equipped' => $equipped,
                'inventory' => $inventory
            ]);
            break;
            
        case 'get_available_items':
            // 获取所有可用装备物品，包含装备位置信息
            $stmt = $conn->query("
                SELECT it.id, it.name, it.category, ed.slot
                FROM item_templates it
                JOIN equipment_details ed ON it.id = ed.item_template_id
                WHERE it.category = 'Equipment'
                ORDER BY ed.slot, it.name
            ");
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true,
                'items' => $items
            ]);
            break;
            
        case 'add_item_to_npc':
            $npcId = $_POST['npc_id'] ?? 0;
            $itemTemplateId = $_POST['item_template_id'] ?? 0;
            $quantity = $_POST['quantity'] ?? 1;
            $slot = $_POST['slot'] ?? null;
            $isEquipped = !empty($slot) ? 1 : 0;
            
            if (!$npcId || !$itemTemplateId) {
                throw new Exception('缺少必要参数');
            }
            
            // 检查NPC模板是否存在
            $stmt = $conn->prepare("SELECT COUNT(*) FROM npc_templates WHERE id = ?");
            $stmt->execute([$npcId]);
            if ($stmt->fetchColumn() == 0) {
                throw new Exception('NPC模板不存在');
            }
            
            // 获取该NPC模板的所有实例ID
            $stmt = $conn->prepare("SELECT id FROM npc_instances WHERE template_id = ?");
            $stmt->execute([$npcId]);
            $instanceIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (empty($instanceIds)) {
                // 创建一个临时NPC实例
                $stmt = $conn->prepare("
                    INSERT INTO npc_instances (template_id, scene_id, position_x, position_y, respawn_time)
                    VALUES (?, 1, 0, 0, 0)
                ");
                $stmt->execute([$npcId]);
                $npcInstanceId = $conn->lastInsertId();
                $instanceIds = [$npcInstanceId];
            } else {
                $npcInstanceId = $instanceIds[0]; // 使用第一个实例
            }
            
            // 检查物品是否存在
            $stmt = $conn->prepare("SELECT * FROM item_templates WHERE id = ?");
            $stmt->execute([$itemTemplateId]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            if (!$item) {
                throw new Exception('物品不存在');
            }
            
            // 如果要装备，进行额外检查
            if ($isEquipped) {
                // 检查是否为装备类物品
                if ($item['category'] !== 'Equipment') {
                    throw new Exception('只有装备类物品才能被装备');
                }
                
                // 检查所有实例中该槽位是否已有装备
                $placeholders = implode(',', array_fill(0, count($instanceIds), '?'));
                $stmt = $conn->prepare("
                    SELECT ni.id, ni.npc_instance_id, it.name
                    FROM npc_inventories ni
                    JOIN item_templates it ON ni.item_template_id = it.id
                    WHERE ni.npc_instance_id IN ($placeholders) AND ni.slot = ? AND ni.is_equipped = 1
                ");
                
                $params = array_merge($instanceIds, [$slot]);
                $stmt->execute($params);
                $existingEquipments = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 如果有任何实例的该槽位已有装备，处理旧装备
                if (!empty($existingEquipments)) {
                    foreach ($existingEquipments as $equipment) {
                        // 获取装备的数量
                        $stmt = $conn->prepare("
                            SELECT id, quantity FROM npc_inventories WHERE id = ?
                        ");
                        $stmt->execute([$equipment['id']]);
                        $oldItem = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        // 如果数量为1，直接删除该记录
                        if ($oldItem['quantity'] == 1) {
                            $stmt = $conn->prepare("DELETE FROM npc_inventories WHERE id = ?");
                            $stmt->execute([$equipment['id']]);
                        } else {
                            // 如果数量大于1，则更新为未装备状态
                            $stmt = $conn->prepare("
                                UPDATE npc_inventories
                                SET slot = NULL, is_equipped = 0
                                WHERE id = ?
                            ");
                            $stmt->execute([$equipment['id']]);
                        }
                    }
                }
            }
            
            // 添加物品到NPC背包
            $stmt = $conn->prepare("
                INSERT INTO npc_inventories (npc_instance_id, item_template_id, slot, quantity, is_equipped)
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$npcInstanceId, $itemTemplateId, $slot, $quantity, $isEquipped]);
            
            echo json_encode([
                'success' => true,
                'message' => '物品已成功添加到NPC背包'
            ]);
            break;
            
        case 'equip_item':
            $inventoryId = $_POST['inventory_id'] ?? 0;
            $slot = $_POST['slot'] ?? '';
            
            if (!$inventoryId || !$slot) {
                throw new Exception('缺少必要参数');
            }
            
            // 获取物品和NPC信息
            $stmt = $conn->prepare("
                SELECT ni.*, it.category, nt.id as template_id
                FROM npc_inventories ni
                JOIN item_templates it ON ni.item_template_id = it.id
                JOIN npc_instances inst ON ni.npc_instance_id = inst.id
                JOIN npc_templates nt ON inst.template_id = nt.id
                WHERE ni.id = ?
            ");
            $stmt->execute([$inventoryId]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$item) {
                throw new Exception('物品不存在');
            }
            
            if ($item['category'] !== 'Equipment') {
                throw new Exception('只有装备类物品才能被装备');
            }
            
            // 获取同一NPC模板的所有实例ID
            $stmt = $conn->prepare("
                SELECT id FROM npc_instances WHERE template_id = ?
            ");
            $stmt->execute([$item['template_id']]);
            $instanceIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            // 检查所有实例中该槽位是否已有装备
            $placeholders = implode(',', array_fill(0, count($instanceIds), '?'));
            $stmt = $conn->prepare("
                SELECT ni.id, ni.npc_instance_id 
                FROM npc_inventories ni
                WHERE ni.npc_instance_id IN ($placeholders) AND ni.slot = ? AND ni.is_equipped = 1
            ");
            
            $params = array_merge($instanceIds, [$slot]);
            $stmt->execute($params);
            $existingEquipments = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
                         // 如果有任何实例的该槽位已有装备，处理旧装备
            if (!empty($existingEquipments)) {
                foreach ($existingEquipments as $equipment) {
                    // 获取装备的数量
                    $stmt = $conn->prepare("
                        SELECT id, quantity FROM npc_inventories WHERE id = ?
                    ");
                    $stmt->execute([$equipment['id']]);
                    $oldItem = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                    // 如果数量为1，直接删除该记录
                    if ($oldItem['quantity'] == 1) {
                        $stmt = $conn->prepare("DELETE FROM npc_inventories WHERE id = ?");
                        $stmt->execute([$equipment['id']]);
                    } else {
                        // 如果数量大于1，则更新为未装备状态
                        $stmt = $conn->prepare("
                            UPDATE npc_inventories
                            SET slot = NULL, is_equipped = 0
                            WHERE id = ?
                        ");
                        $stmt->execute([$equipment['id']]);
                    }
                }
            }
            
            // 更新物品为已装备状态
            $stmt = $conn->prepare("
                UPDATE npc_inventories 
                SET is_equipped = 1, slot = ? 
                WHERE id = ?
            ");
            $stmt->execute([$slot, $inventoryId]);
            
            echo json_encode([
                'success' => true,
                'message' => '物品已成功装备'
            ]);
            break;
            
        case 'unequip_item':
            $inventoryId = $_POST['inventory_id'] ?? 0;
            
            if (!$inventoryId) {
                throw new Exception('缺少必要参数');
            }
            
            // 更新物品为未装备状态
            $stmt = $conn->prepare("
                UPDATE npc_inventories 
                SET is_equipped = 0, slot = NULL 
                WHERE id = ?
            ");
            $stmt->execute([$inventoryId]);
            
            echo json_encode([
                'success' => true,
                'message' => '物品已成功卸下'
            ]);
            break;
            
        case 'remove_item':
            $inventoryId = $_POST['inventory_id'] ?? 0;
            
            if (!$inventoryId) {
                throw new Exception('缺少必要参数');
            }
            
            // 删除物品
            $stmt = $conn->prepare("DELETE FROM npc_inventories WHERE id = ?");
            $stmt->execute([$inventoryId]);
            
            echo json_encode([
                'success' => true,
                'message' => '物品已成功删除'
            ]);
            break;
            
        default:
            throw new Exception('未知操作');
    }
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'message' => $e->getMessage()
    ]);
} 