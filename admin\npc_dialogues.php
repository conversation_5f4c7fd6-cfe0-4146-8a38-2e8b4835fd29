<?php
// admin/npc_dialogues.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 检查是否提供了NPC ID
if (!isset($_GET['npc_id']) || empty($_GET['npc_id'])) {
    header('Location: npcs.php');
    exit;
}

$npc_id = (int)$_GET['npc_id'];
$pageTitle = 'NPC对话管理';
$currentPage = 'npcs';

// 添加额外的CSS
$extra_css = '
<style>
    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    .header-actions {
        display: flex;
        gap: 10px;
    }
    /* --- Visual Script Builder Styles (Copied from dialogue_editor.php) --- */
    .script-builder { border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin-top: 5px; background: #fafafa; }
    .script-list { display: flex; flex-direction: column; gap: 10px; }
    .script-item { display: flex; gap: 8px; align-items: center; padding: 8px; background: #fff; border: 1px solid #ddd; border-radius: 4px; }
    .script-item-params { display: flex; gap: 8px; align-items: center; flex-wrap: wrap; }
    .script-item select, .script-item input { padding: 5px; border-radius: 3px; border: 1px solid #ccc; }
    .logical-operator-group { margin-bottom: 10px; }
    .btn-add-script, .btn-remove-script {
        border: none; background: none; cursor: pointer; padding: 5px;
    }
    .btn-add-script { color: #28a745; font-size: 1.2em; }
    .btn-remove-script { color: #dc3545; }

    /* 物品选择器样式 */
    .item-selector-container {
        position: relative;
        display: inline-block;
        min-width: 200px;
    }
    .item-selector-input {
        width: 100%;
        padding: 5px 30px 5px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        background: white;
        cursor: pointer;
    }
    .item-selector-arrow {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #666;
    }
    .item-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-top: none;
        border-radius: 0 0 3px 3px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    .item-selector-search {
        width: 100%;
        padding: 8px;
        border: none;
        border-bottom: 1px solid #eee;
        outline: none;
    }
    .item-selector-categories {
        padding: 5px 0;
    }
    .item-category {
        padding: 5px 10px;
        font-weight: bold;
        background: #f5f5f5;
        border-bottom: 1px solid #eee;
        color: #666;
        font-size: 12px;
    }
    .item-option {
        padding: 8px 15px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
    }
    .item-option:hover {
        background: #f0f0f0;
    }
    .item-option.selected {
        background: #007bff;
        color: white;
    }
</style>
';

// 初始化变量
$success_message = '';
$error_message = '';
$npc = null;
$npc_dialogues = [];
$available_dialogues = [];
$all_items = [];
$all_quests = [];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有物品和任务，用于UI构建
    $all_items = $pdo->query("SELECT id, name, category FROM item_templates ORDER BY category, name")->fetchAll(PDO::FETCH_ASSOC);
    $all_quests = $pdo->query("SELECT id, title FROM quests ORDER BY title")->fetchAll(PDO::FETCH_ASSOC);

    // 获取所有职业，用于职业条件选择
    $all_jobs = $pdo->query("SELECT id, name FROM jobs ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);

    // 获取玩家属性表的所有字段
    $columns_query = "SHOW COLUMNS FROM player_attributes";
    $columns_stmt = $pdo->prepare($columns_query);
    $columns_stmt->execute();
    $table_columns_info = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);

    $allowed_types = ['int', 'decimal', 'bigint', 'float', 'double'];
    $excluded_columns = [
        'id', 'account_id', 'native_job_id',
        'experience_to_next_level', 'combat_hp_potion_id',
        'combat_mp_potion_id', 'current_scene_id', 'job'
    ];

    $player_attributes = [];
    foreach ($table_columns_info as $column) {
        $is_allowed = false;
        foreach ($allowed_types as $type) {
            if (strpos(strtolower($column['Type']), $type) !== false) {
                $is_allowed = true;
                break;
            }
        }

        if ($is_allowed && !in_array($column['Field'], $excluded_columns)) {
            $player_attributes[] = $column['Field'];
        }
    }

    // 属性中文名对照
    $attributeNames = [
        'level' => '等级', 'experience' => '经验值', 'hp' => '当前生命值', 'mp' => '当前法力值',
        'attack' => '攻击力', 'defense' => '防御力', 'max_hp' => '最大生命值', 'max_mp' => '最大法力值',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智力',
        'potential_points' => '潜力点', 'knowledge_points' => '知识点', 'gold' => '金币', 'diamonds' => '钻石',
        'karma' => '善恶值', 'rage' => '怒气', 'current_job_id' => '当前职业',
        'fire_damage' => '火系伤害', 'fire_resistance' => '火系抗性', 'ice_damage' => '冰系伤害', 'ice_resistance' => '冰系抗性',
        'wind_damage' => '风系伤害', 'wind_resistance' => '风系抗性', 'electric_damage' => '电系伤害', 'electric_resistance' => '电系抗性',
        'attack_speed' => '攻击速度', 'dodge_bonus' => '闪避加成'
    ];

    // 获取NPC信息
    $stmt = $pdo->prepare("SELECT * FROM npc_templates WHERE id = :id");
    $stmt->execute([':id' => $npc_id]);
    $npc = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$npc) {
        $error_message = "找不到指定的NPC";
    } else {
        // 处理添加/编辑/删除对话关联
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            if ($_POST['action'] === 'add_dialogue') {
                // 添加对话关联
                $dialogue_id = (int)$_POST['dialogue_id'];
                $is_default = isset($_POST['is_default']) ? 1 : 0;
                $condition_script = $_POST['condition_script'] ?? null;
                
                // 如果设置为默认对话，先清除其他默认对话
                if ($is_default) {
                    $stmt = $pdo->prepare("
                        UPDATE npc_dialogues 
                        SET is_default = 0 
                        WHERE npc_template_id = :npc_id
                    ");
                    $stmt->execute([':npc_id' => $npc_id]);
                    
                    // 同时更新NPC模板的默认对话树
                    $stmt = $pdo->prepare("
                        UPDATE npc_templates 
                        SET default_dialogue_tree_id = :dialogue_id 
                        WHERE id = :npc_id
                    ");
                    $stmt->execute([
                        ':dialogue_id' => $dialogue_id,
                        ':npc_id' => $npc_id
                    ]);
                }
                
                // 检查是否已存在此对话关联
                $stmt = $pdo->prepare("
                    SELECT COUNT(*) FROM npc_dialogues 
                    WHERE npc_template_id = :npc_id AND dialogue_tree_id = :dialogue_id
                ");
                $stmt->execute([
                    ':npc_id' => $npc_id,
                    ':dialogue_id' => $dialogue_id
                ]);
                $exists = $stmt->fetchColumn();
                
                if ($exists) {
                    $error_message = "此对话树已经关联到该NPC";
                } else {
                    $stmt = $pdo->prepare("
                        INSERT INTO npc_dialogues 
                        (npc_template_id, dialogue_tree_id, is_default, condition_script)
                        VALUES 
                        (:npc_id, :dialogue_id, :is_default, :condition_script)
                    ");
                    $stmt->execute([
                        ':npc_id' => $npc_id,
                        ':dialogue_id' => $dialogue_id,
                        ':is_default' => $is_default,
                        ':condition_script' => $condition_script
                    ]);
                    $success_message = '对话树关联添加成功！';
                }
                
            } elseif ($_POST['action'] === 'update_dialogue' && isset($_POST['dialogue_relation_id'])) {
                // 更新对话关联
                $relation_id = (int)$_POST['dialogue_relation_id'];
                $is_default = isset($_POST['is_default']) ? 1 : 0;
                $condition_script = $_POST['condition_script'] ?? null;
                
                // 获取对话树ID
                $stmt = $pdo->prepare("
                    SELECT dialogue_tree_id FROM npc_dialogues 
                    WHERE id = :id AND npc_template_id = :npc_id
                ");
                $stmt->execute([
                    ':id' => $relation_id,
                    ':npc_id' => $npc_id
                ]);
                $dialogue_id = $stmt->fetchColumn();
                
                // 如果设置为默认对话，先清除其他默认对话
                if ($is_default) {
                    $stmt = $pdo->prepare("
                        UPDATE npc_dialogues 
                        SET is_default = 0 
                        WHERE npc_template_id = :npc_id
                    ");
                    $stmt->execute([':npc_id' => $npc_id]);
                    
                    // 同时更新NPC模板的默认对话树
                    $stmt = $pdo->prepare("
                        UPDATE npc_templates 
                        SET default_dialogue_tree_id = :dialogue_id 
                        WHERE id = :npc_id
                    ");
                    $stmt->execute([
                        ':dialogue_id' => $dialogue_id,
                        ':npc_id' => $npc_id
                    ]);
                } elseif ($npc['default_dialogue_tree_id'] == $dialogue_id) {
                    // 如果取消了默认对话，清除NPC模板的默认对话树
                    $stmt = $pdo->prepare("
                        UPDATE npc_templates 
                        SET default_dialogue_tree_id = NULL 
                        WHERE id = :npc_id
                    ");
                    $stmt->execute([':npc_id' => $npc_id]);
                }
                
                // 更新对话关联
                $stmt = $pdo->prepare("
                    UPDATE npc_dialogues 
                    SET is_default = :is_default,
                        condition_script = :condition_script
                    WHERE id = :id AND npc_template_id = :npc_id
                ");
                $stmt->execute([
                    ':is_default' => $is_default,
                    ':condition_script' => $condition_script,
                    ':id' => $relation_id,
                    ':npc_id' => $npc_id
                ]);
                $success_message = '对话关联更新成功！';
                
            } elseif ($_POST['action'] === 'delete_dialogue' && isset($_POST['dialogue_relation_id'])) {
                // 删除对话关联
                $relation_id = (int)$_POST['dialogue_relation_id'];
                
                // 获取对话树ID
                $stmt = $pdo->prepare("
                    SELECT dialogue_tree_id, is_default FROM npc_dialogues 
                    WHERE id = :id AND npc_template_id = :npc_id
                ");
                $stmt->execute([
                    ':id' => $relation_id,
                    ':npc_id' => $npc_id
                ]);
                $relation = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if ($relation) {
                    // 如果删除的是默认对话，清除NPC模板的默认对话树
                    if ($relation['is_default'] || $npc['default_dialogue_tree_id'] == $relation['dialogue_tree_id']) {
                        $stmt = $pdo->prepare("
                            UPDATE npc_templates 
                            SET default_dialogue_tree_id = NULL 
                            WHERE id = :npc_id
                        ");
                        $stmt->execute([':npc_id' => $npc_id]);
                    }
                    
                    // 删除对话关联
                    $stmt = $pdo->prepare("
                        DELETE FROM npc_dialogues 
                        WHERE id = :id AND npc_template_id = :npc_id
                    ");
                    $stmt->execute([
                        ':id' => $relation_id,
                        ':npc_id' => $npc_id
                    ]);
                    $success_message = '对话关联删除成功！';
                }
            }
        }
        
        // 获取NPC的所有对话关联
        $stmt = $pdo->prepare("
            SELECT nd.*, dt.name AS dialogue_name 
            FROM npc_dialogues nd
            JOIN dialogue_trees dt ON nd.dialogue_tree_id = dt.id
            WHERE nd.npc_template_id = :npc_id
            ORDER BY nd.is_default DESC, dt.name
        ");
        $stmt->execute([':npc_id' => $npc_id]);
        $npc_dialogues = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取可用的对话树（排除已关联的）
        $used_dialogue_ids = array_map(function($item) {
            return $item['dialogue_tree_id'];
        }, $npc_dialogues);
        
        $sql = "SELECT * FROM dialogue_trees ORDER BY name";
        $params = [];
        
        if (!empty($used_dialogue_ids)) {
            $placeholders = implode(',', array_fill(0, count($used_dialogue_ids), '?'));
            $sql = "SELECT * FROM dialogue_trees WHERE id NOT IN ($placeholders) ORDER BY name";
            $params = $used_dialogue_ids;
        }
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        $available_dialogues = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 引入页面头部
require_once 'layout_header.php';
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <?php if ($npc): ?>
        <div class="content-header">
            <h2>NPC对话管理: <?php echo htmlspecialchars($npc['name']); ?></h2>
            <div class="header-actions">
                <button id="clear-cache-btn" class="btn btn-warning">清除缓存</button>
                <a href="npcs.php" class="btn">返回NPC列表</a>
            </div>
        </div>
        
        <!-- 缓存清除通知 -->
        <div id="cache-notification" class="alert alert-success" style="display: none;"></div>
        
        <div class="card">
            <div class="card-header">
                <h3>当前对话树</h3>
                <?php if (!empty($available_dialogues)): ?>
                    <button id="add-dialogue-btn" class="btn btn-primary">添加对话树</button>
                <?php endif; ?>
            </div>
            
            <?php if (empty($npc_dialogues)): ?>
                <p class="empty-state">此NPC还没有关联任何对话树</p>
            <?php else: ?>
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>对话树名称</th>
                            <th>默认对话</th>
                            <th>条件</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($npc_dialogues as $dialogue): ?>
                            <tr>
                                <td><?php echo $dialogue['id']; ?></td>
                                <td><?php echo htmlspecialchars($dialogue['dialogue_name']); ?></td>
                                <td>
                                    <?php if ($dialogue['is_default']): ?>
                                        <span class="badge badge-success">默认</span>
                                    <?php else: ?>
                                        <span class="badge">否</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if (!empty($dialogue['condition_script'])): ?>
                                        <span class="badge badge-info">有条件</span>
                                    <?php else: ?>
                                        <span class="badge">无</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <button class="btn btn-sm edit-dialogue" 
                                        data-id="<?php echo $dialogue['id']; ?>"
                                        data-default="<?php echo $dialogue['is_default']; ?>"
                                        data-condition="<?php echo htmlspecialchars($dialogue['condition_script'] ?? ''); ?>">
                                        编辑
                                    </button>
                                    <a href="dialogue_editor.php?id=<?php echo $dialogue['dialogue_tree_id']; ?>" class="btn btn-sm">编辑对话内容</a>
                                    <button class="btn btn-sm btn-danger delete-dialogue" 
                                        data-id="<?php echo $dialogue['id']; ?>"
                                        data-name="<?php echo htmlspecialchars($dialogue['dialogue_name']); ?>">
                                        删除关联
                                    </button>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            <?php endif; ?>
        </div>
        
        <?php if (empty($available_dialogues) && empty($npc_dialogues)): ?>
            <div class="alert alert-info">
                没有可用的对话树。请先 <a href="dialogues.php">创建对话树</a>。
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div>

<!-- 添加对话关联模态框 -->
<div id="add-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加对话树关联</h3>
        
        <form method="post" action="npc_dialogues.php?npc_id=<?php echo $npc_id; ?>">
            <input type="hidden" name="action" value="add_dialogue">
            
            <div class="form-group">
                <label for="dialogue-id">选择对话树</label>
                <select id="dialogue-id" name="dialogue_id" required>
                    <option value="">-- 选择对话树 --</option>
                    <?php foreach ($available_dialogues as $dialogue): ?>
                        <option value="<?php echo $dialogue['id']; ?>"><?php echo htmlspecialchars($dialogue['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_default">
                    设为默认对话
                </label>
                <p class="form-help">如果勾选，玩家与NPC交互时默认使用此对话树</p>
            </div>
            
            <div class="form-group">
                <label for="add-condition-script">条件脚本 (可选)</label>
                <div id="add-condition-builder" class="script-builder">
                    <div class="logical-operator-group">
                        <select id="add-condition-logical-operator" class="form-control" style="width: auto;">
                            <option value="AND">所有条件都必须满足 (AND)</option>
                            <option value="OR">任意一个条件满足即可 (OR)</option>
                        </select>
                    </div>
                    <div id="add-condition-list" class="script-list"></div>
                    <button type="button" id="add-condition-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加条件</button>
                </div>
                <textarea id="add-condition-script" name="condition_script" style="display:none;"></textarea>
                <p class="form-help">仅当不设为默认对话时，条件才生效。满足所有条件后，此对话将优先于默认对话。</p>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加关联</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑对话关联模态框 -->
<div id="edit-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑对话树关联</h3>
        
        <form method="post" action="npc_dialogues.php?npc_id=<?php echo $npc_id; ?>">
            <input type="hidden" name="action" value="update_dialogue">
            <input type="hidden" name="dialogue_relation_id" id="edit-dialogue-id" value="">
            
            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_default" id="edit-is-default">
                    设为默认对话
                </label>
                <p class="form-help">如果勾选，玩家与NPC交互时默认使用此对话树</p>
            </div>
            
            <div class="form-group">
                <label for="edit-condition-script">条件脚本 (可选)</label>
                 <div id="edit-condition-builder" class="script-builder">
                    <div class="logical-operator-group">
                        <select id="edit-condition-logical-operator" class="form-control" style="width: auto;">
                            <option value="AND">所有条件都必须满足 (AND)</option>
                            <option value="OR">任意一个条件满足即可 (OR)</option>
                        </select>
                    </div>
                    <div id="edit-condition-list" class="script-list"></div>
                    <button type="button" id="edit-add-condition-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加条件</button>
                </div>
                <textarea id="edit-condition-script" name="condition_script" style="display:none;"></textarea>
                <p class="form-help">仅当不设为默认对话时，条件才生效。满足所有条件后，此对话将优先于默认对话。</p>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新关联</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-dialogue-message"></p>
        
        <form method="post" action="npc_dialogues.php?npc_id=<?php echo $npc_id; ?>">
            <input type="hidden" name="action" value="delete_dialogue">
            <input type="hidden" name="dialogue_relation_id" id="delete-dialogue-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/js/all.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // PHP数据注入到JS
    const ALL_ITEMS = <?php echo json_encode($all_items); ?>;
    const ALL_QUESTS = <?php echo json_encode($all_quests); ?>;
    const PLAYER_ATTRIBUTES = <?php echo json_encode($player_attributes ?? []); ?>;
    const ATTRIBUTE_NAMES = <?php echo json_encode($attributeNames ?? []); ?>;
    const ALL_JOBS = <?php echo json_encode($all_jobs ?? []); ?>;

    var addModal = document.getElementById('add-dialogue-modal');
    var editModal = document.getElementById('edit-dialogue-modal');
    var deleteModal = document.getElementById('delete-dialogue-modal');
    var cacheNotification = document.getElementById('cache-notification');
    
    // --- Visual Script Builder Factory ---
    function createConditionBuilder(options) {
        const { container, hiddenInput, logicalOpSelect, addBtn } = options;
        
        const builder = {
            init: function() {
                logicalOpSelect.addEventListener('change', () => this.serialize());
                addBtn.addEventListener('click', () => this.add());
                this.deserialize();
            },
            
            clear: function() {
                container.innerHTML = '';
                logicalOpSelect.value = 'AND';
                hiddenInput.value = '';
            },

            add: function(data = {}) {
                const item = document.createElement('div');
                item.className = 'script-item';
                item.innerHTML = `
                    <select class="condition-type">
                        <option value="">-- 选择条件类型 --</option>
                        <option value="player_attribute">玩家属性</option>
                        <option value="has_item">拥有物品</option>
                        <option value="does_not_have_item">不拥有物品</option>
                        <option value="quest_status">任务状态</option>
                    </select>
                    <div class="script-item-params"></div>
                    <button type="button" class="btn-remove-script"><i class="fas fa-trash"></i></button>
                `;
                container.appendChild(item);
                
                const typeSelect = item.querySelector('.condition-type');
                typeSelect.addEventListener('change', () => this.updateParams(item));
                item.querySelector('.btn-remove-script').addEventListener('click', () => {
                    item.remove();
                    this.serialize();
                });

                if (data.type) {
                    typeSelect.value = data.type;
                    this.updateParams(item, data);
                }
            },

            updateParams: function(item, data = {}) {
                const paramsContainer = item.querySelector('.script-item-params');
                const type = item.querySelector('.condition-type').value;
                paramsContainer.innerHTML = ''; 

                switch (type) {
                    case 'player_attribute':
                        const attrOptions = PLAYER_ATTRIBUTES.map(attr => {
                            const displayName = ATTRIBUTE_NAMES[attr] ? `${ATTRIBUTE_NAMES[attr]} (${attr})` : attr;
                            return `<option value="${attr}" ${data.attribute === attr ? 'selected' : ''}>${displayName}</option>`;
                        }).join('');
                        paramsContainer.innerHTML = `
                            <select name="attribute" class="attr-select">
                                <option value="">-- 选择属性 --</option>
                                ${attrOptions}
                            </select>
                            <select name="operator">
                                ${['==', '!=', '>', '>=', '<', '<='].map(op => `<option value="${op}" ${data.operator === op ? 'selected' : ''}>${op}</option>`).join('')}
                            </select>
                            <span class="value-input-container">
                                <input type="text" name="value" placeholder="值" value="${data.value || ''}">
                            </span>
                        `;

                        // 为属性选择添加事件监听器
                        const attrSelect = paramsContainer.querySelector('.attr-select');
                        const valueContainer = paramsContainer.querySelector('.value-input-container');
                        const self = this;

                        function updateValueInput() {
                            const selectedAttr = attrSelect.value;
                            if (selectedAttr === 'current_job_id') {
                                // 显示职业下拉选择
                                const jobOptions = ALL_JOBS.map(job =>
                                    `<option value="${job.id}" ${data.value == job.id ? 'selected' : ''}>${job.name}</option>`
                                ).join('');
                                valueContainer.innerHTML = `
                                    <select name="value">
                                        <option value="">-- 选择职业 --</option>
                                        ${jobOptions}
                                    </select>
                                `;
                            } else {
                                // 显示文本输入框
                                valueContainer.innerHTML = `<input type="text" name="value" placeholder="值" value="${data.value || ''}">`;
                            }

                            // 为新创建的元素绑定change事件监听器
                            valueContainer.querySelectorAll('input, select').forEach(el => {
                                el.addEventListener('change', () => self.serialize());
                            });
                        }

                        attrSelect.addEventListener('change', updateValueInput);
                        // 初始化时也要调用一次
                        updateValueInput();
                        break;
                    case 'has_item':
                    case 'does_not_have_item':
                        const qty_display = (type === 'has_item') ? '' : 'style="display:none"';
                        paramsContainer.innerHTML = `
                            <div class="item-selector-container">
                                <input type="hidden" name="item_id" value="${data.item_id || ''}">
                                <input type="text" class="item-selector-input" placeholder="-- 选择物品 --" readonly>
                                <span class="item-selector-arrow">▼</span>
                                <div class="item-selector-dropdown">
                                    <input type="text" class="item-selector-search" placeholder="搜索物品...">
                                    <div class="item-selector-categories"></div>
                                </div>
                            </div>
                            <input type="number" name="quantity" placeholder="数量" min="1" value="${data.quantity || 1}" ${qty_display}>
                        `;
                        builder.initItemSelector(paramsContainer.querySelector('.item-selector-container'), data.item_id);
                        break;
                    case 'quest_status':
                         paramsContainer.innerHTML = `
                            <select name="quest_id">
                               <option value="">-- 选择任务 --</option>
                               ${ALL_QUESTS.map(q => `<option value="${q.id}" ${data.quest_id == q.id ? 'selected' : ''}>${q.title}</option>`).join('')}
                            </select>
                            <select name="status">
                                <option value="not_started" ${data.status === 'not_started' ? 'selected' : ''}>未开始</option>
                                <option value="active" ${data.status === 'active' ? 'selected' : ''}>进行中</option>
                                <option value="completed" ${data.status === 'completed' ? 'selected' : ''}>已完成</option>
                            </select>
                        `;
                        break;
                }
                paramsContainer.querySelectorAll('input, select').forEach(el => el.addEventListener('change', () => this.serialize()));
                this.serialize();
            },

            serialize: function() {
                const conditions = [];
                container.querySelectorAll('.script-item').forEach(item => {
                    const type = item.querySelector('.condition-type').value;
                    if (!type) return;

                    const condition = { type };
                    item.querySelector('.script-item-params').querySelectorAll('input, select').forEach(el => {
                        condition[el.name] = el.value;
                    });
                    conditions.push(condition);
                });

                const data = {
                    logical_operator: logicalOpSelect.value,
                    conditions: conditions
                };
                hiddenInput.value = conditions.length > 0 ? JSON.stringify(data, null, 2) : '';
            },

            // 初始化物品选择器
            initItemSelector: function(container, selectedItemId) {
                const self = this; // 保存builder的引用
                const hiddenInput = container.querySelector('input[name="item_id"]');
                const displayInput = container.querySelector('.item-selector-input');
                const dropdown = container.querySelector('.item-selector-dropdown');
                const searchInput = container.querySelector('.item-selector-search');
                const categoriesContainer = container.querySelector('.item-selector-categories');

                // 按分类组织物品
                const itemsByCategory = {};
                ALL_ITEMS.forEach(item => {
                    if (!itemsByCategory[item.category]) {
                        itemsByCategory[item.category] = [];
                    }
                    itemsByCategory[item.category].push(item);
                });

                // 渲染分类和物品
                function renderItems(searchTerm = '') {
                    categoriesContainer.innerHTML = '';

                    Object.keys(itemsByCategory).sort().forEach(category => {
                        const items = itemsByCategory[category].filter(item =>
                            !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase())
                        );

                        if (items.length === 0) return;

                        // 分类标题
                        const categoryDiv = document.createElement('div');
                        categoryDiv.className = 'item-category';
                        categoryDiv.textContent = category;
                        categoriesContainer.appendChild(categoryDiv);

                        // 物品选项
                        items.forEach(item => {
                            const itemDiv = document.createElement('div');
                            itemDiv.className = 'item-option';
                            itemDiv.textContent = item.name;
                            itemDiv.dataset.itemId = item.id;
                            itemDiv.dataset.itemName = item.name;

                            if (item.id == selectedItemId) {
                                itemDiv.classList.add('selected');
                                displayInput.value = item.name;
                            }

                            itemDiv.addEventListener('click', () => {
                                hiddenInput.value = item.id;
                                displayInput.value = item.name;
                                dropdown.style.display = 'none';

                                // 清除其他选中状态
                                categoriesContainer.querySelectorAll('.item-option').forEach(opt => {
                                    opt.classList.remove('selected');
                                });
                                itemDiv.classList.add('selected');

                                // 触发序列化
                                self.serialize();
                            });

                            categoriesContainer.appendChild(itemDiv);
                        });
                    });
                }

                // 初始渲染
                renderItems();

                // 点击输入框显示下拉菜单
                displayInput.addEventListener('click', () => {
                    dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                    if (dropdown.style.display === 'block') {
                        searchInput.focus();
                    }
                });

                // 搜索功能
                searchInput.addEventListener('input', (e) => {
                    renderItems(e.target.value);
                });

                // 点击外部关闭下拉菜单
                document.addEventListener('click', (e) => {
                    if (!container.contains(e.target)) {
                        dropdown.style.display = 'none';
                    }
                });

                // 阻止下拉菜单内的点击事件冒泡
                dropdown.addEventListener('click', (e) => {
                    e.stopPropagation();
                });
            },

            deserialize: function(jsonString) {
                this.clear();
                try {
                    const data = JSON.parse(jsonString);
                    if (data && data.conditions) {
                        logicalOpSelect.value = data.logical_operator || 'AND';
                        data.conditions.forEach(cond => this.add(cond));
                    }
                } catch (e) { /* Do nothing on parsing error */ }
            }
        };
        builder.init();
        return builder;
    }

    const addConditionBuilder = createConditionBuilder({
        container: document.getElementById('add-condition-list'),
        hiddenInput: document.getElementById('add-condition-script'),
        logicalOpSelect: document.getElementById('add-condition-logical-operator'),
        addBtn: document.getElementById('add-condition-btn')
    });
    
    const editConditionBuilder = createConditionBuilder({
        container: document.getElementById('edit-condition-list'),
        hiddenInput: document.getElementById('edit-condition-script'),
        logicalOpSelect: document.getElementById('edit-condition-logical-operator'),
        addBtn: document.getElementById('edit-add-condition-btn')
    });
    
    // 清除缓存按钮
    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        // 禁用按钮，防止重复点击
        this.disabled = true;
        this.textContent = '正在清除...';
        
        // 发送AJAX请求清除缓存
        fetch('clear_cache.php')
            .then(response => response.json())
            .then(data => {
                // 显示通知
                cacheNotification.textContent = data.message;
                cacheNotification.style.display = 'block';
                cacheNotification.className = data.success ? 'alert alert-success' : 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
                
                // 3秒后隐藏通知
                setTimeout(function() {
                    cacheNotification.style.display = 'none';
                }, 3000);
            })
            .catch(error => {
                console.error('清除缓存出错:', error);
                cacheNotification.textContent = '清除缓存请求失败，请检查网络连接';
                cacheNotification.style.display = 'block';
                cacheNotification.className = 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
            });
    });
    
    // 添加对话树按钮
    if (document.getElementById('add-dialogue-btn')) {
        document.getElementById('add-dialogue-btn').addEventListener('click', function() {
            addModal.style.display = 'block';
            // 重置表单和构建器
            document.querySelector('#add-dialogue-modal form').reset();
            addConditionBuilder.clear();
        });
    }
    
    // 编辑对话关联按钮
    document.querySelectorAll('.edit-dialogue').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var isDefault = this.dataset.default === '1';
            var condition = this.dataset.condition;
            
            document.getElementById('edit-dialogue-id').value = id;
            document.getElementById('edit-is-default').checked = isDefault;
            
            // 反序列化条件到编辑器
            editConditionBuilder.deserialize(condition);
            
            editModal.style.display = 'block';
        });
    });
    
    // 删除对话关联按钮
    document.querySelectorAll('.delete-dialogue').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var name = this.dataset.name;
            
            document.getElementById('delete-dialogue-id').value = id;
            document.getElementById('delete-dialogue-message').textContent = '确定要删除与对话树 "' + name + '" 的关联吗？';
            
            deleteModal.style.display = 'block';
        });
    });
    
    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            addModal.style.display = 'none';
            editModal.style.display = 'none';
            deleteModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == addModal) {
            addModal.style.display = 'none';
        }
        if (event.target == editModal) {
            editModal.style.display = 'none';
        }
        if (event.target == deleteModal) {
            deleteModal.style.display = 'none';
        }
    });
});
</script>

<?php require_once 'layout_footer.php'; ?> 