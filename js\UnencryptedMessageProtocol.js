class UnencryptedMessageProtocol {
    encode(opcode, payload) {
        const payloadJson = JSON.stringify(payload);
        const payloadBytes = new TextEncoder().encode(payloadJson);
        const buffer = new ArrayBuffer(1 + payloadBytes.length);
        const view = new DataView(buffer);
        view.setUint8(0, opcode);
        new Uint8Array(buffer, 1).set(payloadBytes);
        return buffer;
    }
    decode(arrayBuffer) {
        try {
            const view = new DataView(arrayBuffer);
            const opcode = view.getUint8(0);
            const payloadBytes = new Uint8Array(arrayBuffer, 1);
            const payloadJson = new TextDecoder().decode(payloadBytes);
            const payload = JSON.parse(payloadJson);
            return { opcode, payload };
        } catch (e) {
            console.error("Failed to decode unencrypted message:", e);
            return null;
        }
    }
}