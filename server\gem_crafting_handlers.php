<?php
// server/gem_crafting_handlers.php

/**
 * 宝石合成系统服务器处理函数
 * 用于处理与宝石合成相关的WebSocket消息
 */

/**
 * 处理获取宝石配方请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleGetGemRecipes($fd, $data, $server) {
    if (!isset($data['scene_building_id']) || !isset($data['player_id'])) {
        // 错误消息需要通过服务器实例发送，但不能直接调用私有方法
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整',
            'context' => 'get_gem_recipes_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $sceneBuildingId = $data['scene_building_id'];
    
    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();
    
    // 创建宝石合成管理器实例
    $gemCraftingManager = new GemCraftingManager($conn);
    
    // 获取可用宝石配方数据
    $recipesData = $gemCraftingManager->getAvailableGemRecipes($playerId, $sceneBuildingId);
    
    // 返回配方数据，让服务器实例处理发送
    return [MessageProtocol::S2C_GEM_RECIPES_DATA, $recipesData];
}

/**
 * 处理宝石合成请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleCraftGem($fd, $data, $server) {
    if (!isset($data['recipe_id']) || !isset($data['player_id']) || !isset($data['scene_building_id'])) {
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整',
            'context' => 'craft_gem_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $recipeId = $data['recipe_id'];
    $sceneBuildingId = $data['scene_building_id'];
    $useBound = isset($data['use_bound']) ? (bool)$data['use_bound'] : false;

    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();

    // 创建宝石合成管理器实例
    $gemCraftingManager = new GemCraftingManager($conn);

    // 执行宝石合成操作
    $craftResult = $gemCraftingManager->craftGem($playerId, $recipeId, $useBound);
    
    // 返回合成结果，让服务器实例处理发送
    return [MessageProtocol::S2C_GEM_CRAFT_RESULT, $craftResult];
}
