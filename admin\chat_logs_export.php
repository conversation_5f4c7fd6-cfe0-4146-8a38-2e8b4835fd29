<?php
// admin/chat_logs_export.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

require_once '../config/Database.php';

// 处理搜索参数（与chat_logs.php相同的逻辑）
$search_type = $_GET['type'] ?? 'all';
$search_keyword = trim($_GET['keyword'] ?? '');
$search_player = trim($_GET['player'] ?? '');
$search_date_from = $_GET['date_from'] ?? '';
$search_date_to = $_GET['date_to'] ?? '';

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 构建查询条件（与chat_logs.php相同）
    $where_conditions = [];
    $params = [];
    
    if (!empty($search_date_from)) {
        $where_conditions[] = "sent_at >= ?";
        $params[] = $search_date_from . ' 00:00:00';
    }
    if (!empty($search_date_to)) {
        $where_conditions[] = "sent_at <= ?";
        $params[] = $search_date_to . ' 23:59:59';
    }
    
    if (!empty($search_keyword)) {
        $where_conditions[] = "message LIKE ?";
        $params[] = '%' . $search_keyword . '%';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 根据类型构建查询（不限制数量，导出全部）
    if ($search_type === 'public') {
        if (!empty($search_player)) {
            $where_conditions[] = "player_name LIKE ?";
            $params[] = '%' . $search_player . '%';
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        $sql = "SELECT 'public' as chat_type, id, player_id as sender_id, player_name as sender_name, 
                       NULL as receiver_id, NULL as receiver_name, message, sent_at, filtered
                FROM public_chat_messages 
                $where_clause
                ORDER BY sent_at DESC";
                
    } else if ($search_type === 'private') {
        if (!empty($search_player)) {
            $where_conditions[] = "(sender_name LIKE ? OR receiver_name LIKE ?)";
            $params[] = '%' . $search_player . '%';
            $params[] = '%' . $search_player . '%';
            $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);
        }
        
        $sql = "SELECT 'private' as chat_type, id, sender_id, sender_name, receiver_id, receiver_name, 
                       message, sent_at, filtered
                FROM private_chat_messages 
                $where_clause
                ORDER BY sent_at DESC";
                
    } else {
        // 全部类型
        $public_where = $where_clause;
        $private_where = $where_clause;
        $public_params = $params;
        $private_params = $params;
        
        if (!empty($search_player)) {
            $public_conditions = array_filter($where_conditions, function($cond) {
                return !str_contains($cond, 'player_name') && !str_contains($cond, 'sender_name');
            });
            $private_conditions = $public_conditions;
            
            $public_conditions[] = "player_name LIKE ?";
            $private_conditions[] = "(sender_name LIKE ? OR receiver_name LIKE ?)";
            
            $public_where = !empty($public_conditions) ? 'WHERE ' . implode(' AND ', $public_conditions) : '';
            $private_where = !empty($private_conditions) ? 'WHERE ' . implode(' AND ', $private_conditions) : '';
            
            $public_params = array_slice($params, 0, -1);
            $private_params = array_slice($params, 0, -1);
            
            $public_params[] = '%' . $search_player . '%';
            $private_params[] = '%' . $search_player . '%';
            $private_params[] = '%' . $search_player . '%';
        }
        
        $sql = "(SELECT 'public' as chat_type, id, player_id as sender_id, player_name as sender_name, 
                        NULL as receiver_id, NULL as receiver_name, message, sent_at, filtered
                 FROM public_chat_messages $public_where)
                UNION ALL
                (SELECT 'private' as chat_type, id, sender_id, sender_name, receiver_id, receiver_name, 
                        message, sent_at, filtered
                 FROM private_chat_messages $private_where)
                ORDER BY sent_at DESC";
        
        $params = array_merge($public_params, $private_params);
    }
    
    // 执行查询
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $chat_logs = $stmt->fetchAll();
    
    // 生成文件名
    $filename = 'chat_logs_' . date('Y-m-d_H-i-s');
    if (!empty($search_keyword)) {
        $filename .= '_keyword_' . preg_replace('/[^a-zA-Z0-9\-_]/', '', $search_keyword);
    }
    if (!empty($search_player)) {
        $filename .= '_player_' . preg_replace('/[^a-zA-Z0-9\-_]/', '', $search_player);
    }
    if ($search_type !== 'all') {
        $filename .= '_' . $search_type;
    }
    $filename .= '.csv';
    
    // 设置CSV下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: Sat, 26 Jul 1997 05:00:00 GMT');
    
    // 输出CSV内容
    $output = fopen('php://output', 'w');
    
    // 添加BOM以支持Excel正确显示中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // CSV头部
    fputcsv($output, [
        '类型',
        '发送者ID',
        '发送者名称',
        '接收者ID',
        '接收者名称',
        '消息内容',
        '是否过滤',
        '发送时间'
    ]);
    
    // 输出数据
    foreach ($chat_logs as $log) {
        fputcsv($output, [
            $log['chat_type'] === 'public' ? '公聊' : '私聊',
            $log['sender_id'],
            $log['sender_name'],
            $log['receiver_id'] ?? '',
            $log['receiver_name'] ?? '',
            $log['message'],
            $log['filtered'] ? '是' : '否',
            $log['sent_at']
        ]);
    }
    
    fclose($output);
    exit;
    
} catch (Exception $e) {
    // 如果出错，返回错误页面
    header('Content-Type: text/html; charset=utf-8');
    echo '<!DOCTYPE html>';
    echo '<html><head><meta charset="utf-8"><title>导出失败</title></head>';
    echo '<body>';
    echo '<h1>导出失败</h1>';
    echo '<p>错误信息: ' . htmlspecialchars($e->getMessage()) . '</p>';
    echo '<p><a href="chat_logs.php">返回聊天记录管理</a></p>';
    echo '</body></html>';
    exit;
}
?>
