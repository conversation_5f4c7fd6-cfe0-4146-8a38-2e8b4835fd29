document.addEventListener('DOMContentLoaded', () => {
    // State
    let scenes = {};
    let allMonsters = [];
    let allBuildings = [];
    let layers = new Set();
    let currentLayer = 0;
    let selectedCoords = new Set();
    let isDragging = false;
    let lastCellDuringDrag = null;

    // DOM Elements
    const minXInput = document.getElementById('min-x');
    const minYInput = document.getElementById('min-y');
    const maxXInput = document.getElementById('max-x');
    const maxYInput = document.getElementById('max-y');
    const mapGrid = document.getElementById('map-grid');
    const redrawButton = document.getElementById('redraw-map');
    const layerSelector = document.getElementById('layer-selector');
    const addLayerBtn = document.getElementById('add-layer-btn');
    const sceneModal = document.getElementById('scene-modal');
    const closeSceneModalBtn = sceneModal.querySelector('.close-button');
    const sceneForm = document.getElementById('scene-form');
    const toast = document.getElementById('toast');
    const monsterSelect = document.getElementById('monster-select');
    const addMonsterBtn = document.getElementById('add-monster-btn');
    const monsterList = document.getElementById('monster-list');
    const deleteSceneBtn = document.getElementById('delete-scene-btn');
    
    // 建筑相关DOM元素
    const buildingSelect = document.getElementById('building-select');
    const addBuildingBtn = document.getElementById('add-building-btn');
    const buildingList = document.getElementById('building-list');
    
    // Batch Ops Elements
    const batchActionsPanel = document.getElementById('batch-actions-panel');
    const selectionCountSpan = document.getElementById('selection-count');
    const batchCreateBtn = document.getElementById('batch-create-btn');
    const batchEditBtn = document.getElementById('batch-edit-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchEditModal = document.getElementById('batch-edit-modal');
    const closeBatchEditModalBtn = batchEditModal.querySelector('.close-button'); 
    const batchEditForm = document.getElementById('batch-edit-form');
    const batchMonsterSelect = document.getElementById('batch-monster-select');
    const batchAddMonsterBtn = document.getElementById('batch-add-monster-btn');
    const batchMonsterList = document.getElementById('batch-monster-list');
    const batchBuildingSelect = document.getElementById('batch-building-select');
    const batchAddBuildingBtn = document.getElementById('batch-add-building-btn');
    const batchBuildingList = document.getElementById('batch-building-list');

    // Batch Create Modal elements
    const batchCreateModal = document.getElementById('batch-create-modal');
    const closeBatchCreateModalBtn = batchCreateModal.querySelector('.close-button');
    const batchCreateForm = document.getElementById('batch-create-form');
    const batchCreateMonsterSelect = document.getElementById('batch-create-monster-select');
    const batchCreateAddMonsterBtn = document.getElementById('batch-create-add-monster-btn');
    const batchCreateMonsterList = document.getElementById('batch-create-monster-list');
    const batchCreateBuildingSelect = document.getElementById('batch-create-building-select');
    const batchCreateAddBuildingBtn = document.getElementById('batch-create-add-building-btn');
    const batchCreateBuildingList = document.getElementById('batch-create-building-list');

    // Utility Functions
    const getLineCoords = (x0, y0, x1, y1) => {
        // Bresenham's line algorithm
        const points = [];
        const dx = Math.abs(x1 - x0);
        const dy = Math.abs(y1 - y0);
        const sx = (x0 < x1) ? 1 : -1;
        const sy = (y0 < y1) ? 1 : -1;
        let err = dx - dy;

        while (true) {
            points.push({ x: x0, y: y0 });
            if ((x0 === x1) && (y0 === y1)) break;
            let e2 = 2 * err;
            if (e2 > -dy) { err -= dy; x0 += sx; }
            if (e2 < dx) { err += dx; y0 += sy; }
        }
        return points;
    };

    const showToast = (message, isError = false) => {
        toast.textContent = message;
        toast.className = `show ${isError ? 'error' : 'success'}`;
        setTimeout(() => { toast.className = toast.className.replace('show', ''); }, 3000);
    };

    const getCoordsFromCell = (cell) => ({ x: parseInt(cell.dataset.x), y: parseInt(cell.dataset.y) });

    // Map Drawing
    const drawMap = (recalculateFromInputs = false) => {
        const bounds = {
            minX: parseInt(minXInput.value),
            minY: parseInt(minYInput.value),
            maxX: parseInt(maxXInput.value),
            maxY: parseInt(maxYInput.value),
        };

        const cols = bounds.maxX - bounds.minX + 1;
        const rows = bounds.maxY - bounds.minY + 1;
        mapGrid.innerHTML = '';
        mapGrid.style.setProperty('--grid-cols', cols);

        for (let y = bounds.maxY; y >= bounds.minY; y--) {
            for (let x = bounds.minX; x <= bounds.maxX; x++) {
                const cell = document.createElement('div');
                cell.classList.add('map-cell');
                cell.dataset.x = x;
                cell.dataset.y = y;
                
                const scene = scenes[`${x},${y},${currentLayer}`];
                
                const contentWrapper = document.createElement('div');
                contentWrapper.className = 'cell-content';

                if (scene) {
                    cell.classList.add('existing');
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'cell-name';
                    nameDiv.textContent = scene.name;
                    contentWrapper.appendChild(nameDiv);
                    
                    const idDiv = document.createElement('div');
                    idDiv.className = 'cell-id';
                    idDiv.textContent = scene.id;
                    idDiv.title = `ID: ${scene.id}`;
                    contentWrapper.appendChild(idDiv);

                    if (scene.monsters && scene.monsters.length > 0) {
                        const monsterDiv = document.createElement('div');
                        monsterDiv.className = 'cell-monsters';
                        const monsterText = `M: ${scene.monsters.map(m => `${m.name}(${m.quantity})`).join(', ')}`;
                        monsterDiv.textContent = monsterText;
                        monsterDiv.title = monsterText;
                        contentWrapper.appendChild(monsterDiv);
                    }

                    // 显示场景中的建筑
                    if (scene.buildings && scene.buildings.length > 0) {
                        const buildingDiv = document.createElement('div');
                        buildingDiv.className = 'cell-buildings';
                        const buildingText = `B: ${scene.buildings.map(b => b.name).join(', ')}`;
                        buildingDiv.textContent = buildingText;
                        buildingDiv.title = buildingText;
                        contentWrapper.appendChild(buildingDiv);
                    }
                } else {
                    cell.classList.add('empty');
                }
                
                cell.appendChild(contentWrapper);

                const coordDiv = document.createElement('div');
                coordDiv.className = 'cell-coords';
                coordDiv.textContent = `(${x}, ${y})`;
                cell.appendChild(coordDiv);
                
                mapGrid.appendChild(cell);
            }
        }
        updateSelectionVisuals();
    };
    
    // Layer Management
    const renderLayers = () => {
        layerSelector.innerHTML = '';
        const sortedLayers = [...layers].sort((a, b) => a - b);
        sortedLayers.forEach(z => {
            const btn = document.createElement('button');
            btn.textContent = `Z: ${z}`;
            btn.className = `btn btn-sm ${z === currentLayer ? 'btn-primary' : 'btn-secondary'}`;
            btn.addEventListener('click', () => switchLayer(z));
            layerSelector.appendChild(btn);
        });
    };

    const switchLayer = (z) => {
        currentLayer = z;
        selectedCoords.clear();
        drawMap();
        renderLayers();
        updateBatchActionsVisibility();
    };

    // Selection
    const updateSelectionVisuals = () => {
        document.querySelectorAll('.map-cell').forEach(cell => {
            const { x, y } = getCoordsFromCell(cell);
            if (selectedCoords.has(`${x},${y}`)) {
                cell.classList.add('selected');
            } else {
                cell.classList.remove('selected');
            }
        });
        updateBatchActionsVisibility();
    };
    
    const updateBatchActionsVisibility = () => {
        const count = selectedCoords.size;
        if (count > 0) {
            selectionCountSpan.textContent = count;
            batchActionsPanel.style.display = 'flex';
        } else {
            batchActionsPanel.style.display = 'none';
        }
    };
    
    // Modal Handling
    const openModal = (sceneData) => {
        const modalTitle = document.getElementById('modal-title');
        const sceneIdInput = document.getElementById('scene-id');
        
        // Reset form
        sceneForm.reset();
        monsterList.innerHTML = '';
        buildingList.innerHTML = ''; // 清空建筑列表
        
        if (sceneData.id) { // Existing scene
            modalTitle.textContent = '编辑场景';
            sceneIdInput.value = sceneData.id;
            deleteSceneBtn.style.display = 'inline-block';
        } else { // New scene
            modalTitle.textContent = '创建新场景';
            sceneIdInput.value = '';
            deleteSceneBtn.style.display = 'none';
        }
        
        document.getElementById('name').value = sceneData.name || `新场景 ${sceneData.x},${sceneData.y}`;
        document.getElementById('description').value = sceneData.description || '';
        document.getElementById('max_players').value = sceneData.max_players || 10;
        document.getElementById('scene-x').value = sceneData.x;
        document.getElementById('scene-y').value = sceneData.y;
        document.getElementById('scene-z').value = sceneData.z;
        
        sceneData.monsters?.forEach(monster => {
            addMonsterToForm(monster.id, monster.quantity, 'single');
        });
        
        // 加载场景中的建筑
        sceneData.buildings?.forEach(building => {
            addBuildingToForm(building.id, 'single');
        });

        // 动态添加商店配置按钮
        const actionsContainer = sceneForm.querySelector('.form-actions');
        // 清理旧按钮
        actionsContainer.querySelectorAll('.shop-config-btn').forEach(btn => btn.remove());

        sceneData.buildings?.forEach(building => {
            // 假设商店类型的建筑包含 'shop' 关键字
            if (building.type.toLowerCase().includes('shop')) {
                const shopBtn = document.createElement('button');
                shopBtn.type = 'button';
                shopBtn.className = 'btn btn-info shop-config-btn';
                shopBtn.textContent = `配置[${building.name}]的商品`;
                shopBtn.onclick = () => openShopConfigModal(building.scene_building_id, sceneData.name, building.name);
                actionsContainer.insertBefore(shopBtn, actionsContainer.firstChild);
            }
        });
        
        sceneModal.style.display = 'block';
    };

    const addMonsterToForm = (monsterId, quantity, type) => {
        const targetList = type === 'batch' ? batchMonsterList : monsterList;
        const monster = allMonsters.find(m => m.id == monsterId);
        if (!monster) return;

        const monsterItem = document.createElement('div');
        monsterItem.className = 'monster-item';
        monsterItem.dataset.id = monsterId;
        monsterItem.dataset.quantity = quantity;
        monsterItem.innerHTML = `
            <span>${monster.name} (x${quantity})</span>
            <button type="button" class="remove-monster-btn">&times;</button>
        `;
        monsterItem.querySelector('.remove-monster-btn').addEventListener('click', () => monsterItem.remove());
        targetList.appendChild(monsterItem);
    };

    const hideBatchActions = () => { selectedCoords.clear(); updateSelectionVisuals(); };

    // API Calls
    const apiCall = async (action, body, isJson = true) => {
        try {
            const options = {
                method: 'POST',
                headers: {},
                body: null
            };

            if (isJson) {
                options.headers['Content-Type'] = 'application/json';
                options.body = JSON.stringify(body);
            } else { // form-data
                options.body = body;
            }
            
            const response = await fetch('api_scenes.php' + (isJson ? `?action=${action}` : ''), options);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const result = await response.json();
            if (!result.success) throw new Error(result.message);
            return result;
        } catch (error) {
            console.error('API Error:', error);
            showToast(error.message, true);
            return null;
        }
    };

    // Event Listeners
    redrawButton.addEventListener('click', () => drawMap(true));
    
    addLayerBtn.addEventListener('click', () => {
        const newLayer = layers.size > 0 ? Math.max(...layers) + 1 : 0;
        layers.add(newLayer);
        switchLayer(newLayer);
        showToast(`已创建并切换到新图层 ${newLayer}`);
    });

    // Selection Logic
    mapGrid.addEventListener('mousedown', (e) => {
        const cell = e.target.closest('.map-cell');
        if (!cell) return;
        isDragging = true;
        
        const { x, y } = getCoordsFromCell(cell);
        const coordKey = `${x},${y}`;

        if (!e.ctrlKey) {
            selectedCoords.clear();
        }
        
        // Always add, don't toggle, when starting a drag-based selection.
        selectedCoords.add(coordKey);
        
        lastCellDuringDrag = cell;
        updateSelectionVisuals();
    });

    document.addEventListener('mouseup', (e) => {
        isDragging = false;
        lastCellDuringDrag = null;
    });

    mapGrid.addEventListener('mousemove', (e) => {
        if (!isDragging || !lastCellDuringDrag) return;
        const currentCell = e.target.closest('.map-cell');
        if (!currentCell || currentCell === lastCellDuringDrag) return;

        // Draw a line segment from the PREVIOUS cell to the CURRENT cell.
        const startCoords = getCoordsFromCell(lastCellDuringDrag);
        const endCoords = getCoordsFromCell(currentCell);

        // Add the new segment's points to the existing selection.
        const linePoints = getLineCoords(startCoords.x, startCoords.y, endCoords.x, endCoords.y);
        linePoints.forEach(p => {
            const key = `${p.x},${p.y}`;
            // To allow "erasing" by drawing back over a line, we can toggle points.
            if (e.ctrlKey && selectedCoords.has(key)) {
                selectedCoords.delete(key)
            } else {
                selectedCoords.add(key)
            }
        });
        
        lastCellDuringDrag = currentCell;
        updateSelectionVisuals();
    });

    mapGrid.addEventListener('dblclick', (e) => {
        const cell = e.target.closest('.map-cell');
        if (!cell) return;
        
        const { x, y } = getCoordsFromCell(cell);
        const scene = scenes[`${x},${y},${currentLayer}`] || {
            x, y, z: currentLayer, monsters: []
        };
        openModal(scene);
    });

    closeSceneModalBtn.addEventListener('click', () => sceneModal.style.display = 'none');
    sceneModal.addEventListener('click', (e) => {
        if (e.target === sceneModal) sceneModal.style.display = 'none';
    });

    addMonsterBtn.addEventListener('click', () => {
        const monsterId = monsterSelect.value;
        const quantity = document.getElementById('monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'single');
        }
    });

    // 添加建筑按钮点击事件
    addBuildingBtn.addEventListener('click', () => {
        const buildingId = buildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'single');
        }
    });

    // 添加建筑到表单
    const addBuildingToForm = (buildingId, type) => {
        let targetList;
        if (type === 'batch') {
            targetList = batchBuildingList;
        } else if (type === 'batch-create') {
            targetList = batchCreateBuildingList;
        } else {
            targetList = buildingList;
        }
        
        const building = allBuildings.find(b => b.id == buildingId);
        if (!building) return;

        // 检查是否已经添加了这个建筑
        const existingItem = targetList.querySelector(`.building-item[data-id="${buildingId}"]`);
        if (existingItem) {
            showToast('该建筑已添加到场景中', true);
            return;
        }

        const buildingItem = document.createElement('div');
        buildingItem.className = 'building-item';
        buildingItem.dataset.id = buildingId;
        buildingItem.innerHTML = `
            <span>${building.name} (${building.type})</span>
            <button type="button" class="remove-building-btn">&times;</button>
        `;
        buildingItem.querySelector('.remove-building-btn').addEventListener('click', () => buildingItem.remove());
        targetList.appendChild(buildingItem);
    };

    // Form Submissions
    sceneForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const sceneData = Object.fromEntries(formData.entries());
        
        sceneData.monsters = [];
        monsterList.querySelectorAll('.monster-item').forEach(item => {
            sceneData.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });
        
        // 收集建筑数据
        sceneData.buildings = [];
        buildingList.querySelectorAll('.building-item').forEach(item => {
            sceneData.buildings.push({
                id: item.dataset.id
            });
        });
        
        const action = sceneData.id ? 'update' : 'create';
        const result = await apiCall(action, sceneData);

        if (result) {
            showToast(result.message);
            sceneModal.style.display = 'none';
            await initializeApp(false);
        }
    });

    deleteSceneBtn.addEventListener('click', async () => {
        const sceneId = document.getElementById('scene-id').value;
        if (!sceneId || !confirm(`确定要删除场景 ${sceneId} 吗？此操作不可撤销。`)) return;

        const result = await apiCall('batch_delete', { ids: [sceneId] });

        if (result) {
            showToast(result.message);
            sceneModal.style.display = 'none';
            await initializeApp(false);
        }
    });

    // Batch Ops Handlers
    batchCreateBtn.addEventListener('click', () => {
        if (selectedCoords.size === 0) return;
        
        const scenesOnLayer = Object.values(scenes).filter(s => s.z === currentLayer);
        const scenesByCoords = new Map(scenesOnLayer.map(s => [`${s.x},${s.y}`, s]));
        const hasEmptyCells = [...selectedCoords].some(coord => !scenesByCoords.has(coord));

        if (!hasEmptyCells) {
            showToast("选中的所有单元格都已有场景，无法创建。", true);
            return;
        }

        batchCreateForm.reset();
        batchCreateMonsterList.innerHTML = '';
        batchCreateModal.style.display = 'block';
    });
    
    closeBatchCreateModalBtn.addEventListener('click', () => batchCreateModal.style.display = 'none');
    batchCreateModal.addEventListener('click', (e) => {
        if (e.target === batchCreateModal) batchCreateModal.style.display = 'none';
    });

    batchCreateForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (selectedCoords.size === 0) return;

        const scenesOnLayer = Object.values(scenes).filter(s => s.z === currentLayer);
        const scenesByCoords = new Map(scenesOnLayer.map(s => [`${s.x},${s.y}`, s]));

        const scenesToCreate = [...selectedCoords]
            .filter(coordKey => !scenesByCoords.has(coordKey))
            .map(coordKey => {
                const [x, y] = coordKey.split(',').map(Number);
                return { x, y, z: currentLayer };
            });

        if (scenesToCreate.length === 0) {
            showToast("没有可创建的空白单元格。", true);
            return;
        }
        
        const formData = new FormData(e.target);
        const options = {
            name: formData.get('name'),
            description: formData.get('description'),
            use_suffix: formData.has('use_suffix'),
            monsters: []
        };
        
        batchCreateMonsterList.querySelectorAll('.monster-item').forEach(item => {
            options.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });

        // 收集建筑数据
        options.buildings = [];
        batchCreateBuildingList.querySelectorAll('.building-item').forEach(item => {
            options.buildings.push({
                id: item.dataset.id
            });
        });

        const result = await apiCall('batch_create', { scenes: scenesToCreate, options });
        
        if (result) {
            showToast(result.message);
            batchCreateModal.style.display = 'none';
            hideBatchActions();
            await initializeApp(false);
        }
    });

    batchCreateAddMonsterBtn.addEventListener('click', () => {
        const monsterId = batchCreateMonsterSelect.value;
        const quantity = document.getElementById('batch-create-monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'batch-create');
        }
    });

    batchDeleteBtn.addEventListener('click', async () => {
        if (selectedCoords.size === 0 || !confirm(`确定要删除选中的 ${selectedCoords.size} 个场景吗？`)) return;

        const idsToDelete = [];
        selectedCoords.forEach(coordKey => {
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];
            if (scene) idsToDelete.push(scene.id);
        });

        if (idsToDelete.length === 0) {
            showToast('选中的都是空坐标，无需删除。');
            hideBatchActions();
            return;
        }

        const result = await apiCall('batch_delete', { ids: idsToDelete });
        if (result) {
            showToast(result.message);
            hideBatchActions();
            await initializeApp(false);
        }
    });

    batchEditBtn.addEventListener('click', () => {
        if (selectedCoords.size === 0) return;
        
        // Reset form first
        batchEditForm.reset();
        batchMonsterList.innerHTML = '';
        batchBuildingList.innerHTML = '';

        // If exactly one scene is selected, pre-fill its data for convenience.
        // For multiple scenes, we leave it blank because their data might differ.
        if (selectedCoords.size === 1) {
            const coordKey = selectedCoords.values().next().value;
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];

            if (scene) {
                // Pre-fill name and description
                document.getElementById('batch-edit-name').value = scene.name || '';
                document.getElementById('batch-edit-description').value = scene.description || '';
                // When loading a single scene's name, it's safer to disable suffixing by default.
                document.getElementById('batch-edit-suffix-toggle').checked = false;

                // Pre-fill monsters
                scene.monsters?.forEach(monster => {
                    addMonsterToForm(monster.id, monster.quantity, 'batch');
                });
                
                // Pre-fill buildings
                scene.buildings?.forEach(building => {
                    addBuildingToForm(building.id, 'batch');
                });
            }
        }
        
        batchEditModal.style.display = 'block';
    });
    
    closeBatchEditModalBtn.addEventListener('click', () => batchEditModal.style.display = 'none');
    batchEditModal.addEventListener('click', (e) => {
        if (e.target === batchEditModal) batchEditModal.style.display = 'none';
    });
    
    batchAddMonsterBtn.addEventListener('click', () => {
        const monsterId = batchMonsterSelect.value;
        const quantity = document.getElementById('batch-monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'batch');
        }
    });

    batchEditForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (selectedCoords.size === 0) return;
        
        const idsToUpdate = [];
        selectedCoords.forEach(coordKey => {
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];
            if (scene) idsToUpdate.push(scene.id);
        });

        if (idsToUpdate.length === 0) {
            showToast('没有已存在的场景被选中，无法修改。', true);
            return;
        }

        const updates = {};
        const newName = document.getElementById('batch-edit-name').value.trim();
        if (newName) {
            updates.name = newName;
            updates.use_suffix = document.getElementById('batch-edit-suffix-toggle').checked;
        }

        const newDescription = document.getElementById('batch-edit-description').value;
        if (newDescription !== null && newDescription !== undefined) {
            updates.description = newDescription;
        }

        updates.monsters = [];
        batchMonsterList.querySelectorAll('.monster-item').forEach(item => {
            updates.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });
        
        // 收集建筑数据
        updates.buildings = [];
        batchBuildingList.querySelectorAll('.building-item').forEach(item => {
            updates.buildings.push({
                id: item.dataset.id
            });
        });

        if (Object.keys(updates).length === 0) {
            showToast('没有提供任何修改内容。', true);
            return;
        }

        const result = await apiCall('batch_update', { ids: idsToUpdate, updates });
        
        if(result) {
            showToast(result.message);
            batchEditModal.style.display = 'none';
            hideBatchActions();
            await initializeApp(false);
        }
    });

    // 批量编辑添加建筑按钮
    batchAddBuildingBtn.addEventListener('click', () => {
        const buildingId = batchBuildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'batch');
        }
    });
    
    // 批量创建添加建筑按钮
    batchCreateAddBuildingBtn.addEventListener('click', () => {
        const buildingId = batchCreateBuildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'batch-create');
        }
    });

    // Initialization
    const initializeApp = async (isFirstLoad = true) => {
        try {
            const response = await fetch('api_scenes.php?action=get_all');
            const data = await response.json();
            if (!data.success) throw new Error(data.message);

            console.log('API返回的场景数据:', data.scenes); // 添加调试信息

            scenes = {};
            layers.clear();
            layers.add(0); // Always have layer 0
            
            data.scenes.forEach(s => {
                scenes[`${s.x},${s.y},${s.z}`] = s;
                layers.add(s.z);
            });

            if (isFirstLoad && data.scenes.length > 0) {
                // Find the lowest layer that has scenes, and switch to it.
                const layersWithScenes = [...new Set(data.scenes.map(s => s.z))].sort((a, b) => a - b);
                const targetLayer = layersWithScenes.length > 0 ? layersWithScenes[0] : 0;
                currentLayer = targetLayer;

                const scenesOnTargetLayer = data.scenes.filter(s => s.z === targetLayer);

                if (scenesOnTargetLayer.length > 0) {
                    const allX = scenesOnTargetLayer.map(s => s.x);
                    const allY = scenesOnTargetLayer.map(s => s.y);
                    const minSceneX = Math.min(...allX);
                    const maxSceneX = Math.max(...allX);
                    const minSceneY = Math.min(...allY);
                    const maxSceneY = Math.max(...allY);
                    const centerX = Math.round((minSceneX + maxSceneX) / 2);
                    const centerY = Math.round((minSceneY + maxSceneY) / 2);
                    const viewRadius = 10;

                    minXInput.value = centerX - viewRadius;
                    maxXInput.value = centerX + viewRadius;
                    minYInput.value = centerY - viewRadius;
                    maxYInput.value = centerY + viewRadius;
                }
            }
            
            if(isFirstLoad) {
                const monsterResponse = await fetch('api_monsters.php?action=get_all_simple');
                const monsterData = await monsterResponse.json();
                if (!monsterData.success) throw new Error(monsterData.message);
                allMonsters = monsterData.monsters;
                
                // 加载建筑列表
                const buildingResponse = await fetch('api_buildings.php?action=get_for_list');
                const buildingData = await buildingResponse.json();
                if (!buildingData.success) throw new Error(buildingData.message);
                allBuildings = buildingData.data;
                
                console.log('加载的建筑数据:', allBuildings); // 添加调试信息
                
                // Populate monster selects
                const optionsHtml = allMonsters.map(m => `<option value="${m.id}">${m.name}</option>`).join('');
                monsterSelect.innerHTML = optionsHtml;
                batchMonsterSelect.innerHTML = optionsHtml;
                batchCreateMonsterSelect.innerHTML = optionsHtml;
                
                // 填充建筑选择器
                const buildingOptionsHtml = allBuildings.map(b => `<option value="${b.id}">${b.name} (${b.type})</option>`).join('');
                buildingSelect.innerHTML = buildingOptionsHtml;
                batchBuildingSelect.innerHTML = buildingOptionsHtml;
                batchCreateBuildingSelect.innerHTML = buildingOptionsHtml;
            }
            
            drawMap();
            renderLayers();

        } catch (error) {
            console.error('Initialization failed:', error);
            showToast(error.message, true);
        }
    };

    // ### Shop Config Modal Logic ###

    let allItems = []; // 缓存所有物品模板

    async function openShopConfigModal(sceneBuildingId, sceneName, buildingName) {
        if (allItems.length === 0) {
            try {
                const response = await fetch('api_item_templates.php?action=get_all_simple');
                const data = await response.json();
                if (!data.success) throw new Error(data.message);
                allItems = data.items;
                
                const itemSelect = document.getElementById('shop-item-select');
                itemSelect.innerHTML = '<option value=""></option>' + allItems.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
                
                // 初始化Select2
                $('#shop-item-select').select2({
                    placeholder: '输入名称搜索物品...',
                    allowClear: true,
                    dropdownParent: $('#shop-config-modal') // 关键配置：确保下拉框在modal内显示
                });

            } catch (error) {
                showToast('加载物品列表失败: ' + error.message, true);
                return;
            }
        }

        const modal = document.getElementById('shop-config-modal');
        document.getElementById('shop-config-scene-name').textContent = sceneName;
        document.getElementById('shop-config-building-name').textContent = buildingName;
        document.getElementById('shop-config-scene-building-id').value = sceneBuildingId;

        const itemListDiv = document.getElementById('shop-item-list');
        itemListDiv.innerHTML = '<p class="loading-text">正在加载商品...</p>';
        modal.style.display = 'block';

        try {
            const response = await fetch(`api_shop.php?action=get_shop_items&scene_building_id=${sceneBuildingId}`);
            const data = await response.json();
            if (!data.success) throw new Error(data.message);
            renderShopItems(data.data);
        } catch (error) {
            itemListDiv.innerHTML = `<p class="error-text">加载商品失败: ${error.message}</p>`;
        }
    }

    function renderShopItems(items) {
        const itemListDiv = document.getElementById('shop-item-list');
        if (items.length === 0) {
            itemListDiv.innerHTML = '<p>这个商店还没有商品。</p>';
            return;
        }

        itemListDiv.innerHTML = items.map(item => `
            <div class="shop-item-row" data-shop-item-id="${item.shop_item_id}">
                <span>${item.item_name} (ID: ${item.item_template_id})</span>
                <span>库存: ${item.stock ?? '无限'}</span>
                <span>排序: ${item.sort_order}</span>
                <button class="btn btn-sm btn-danger" onclick="removeShopItem(${item.shop_item_id})">移除</button>
            </div>
        `).join('');
    }

    // 移除商品
    async function removeShopItem(shopItemId) {
        if (!confirm('确定要移除这个商品吗？')) return;

        const formData = new FormData();
        formData.append('action', 'remove_item');
        formData.append('shop_item_id', shopItemId);

        try {
            const response = await fetch('api_shop.php', { method: 'POST', body: formData });
            const result = await response.json();

            if (result.success) {
                showToast(result.message, 'success');
                // 从DOM中直接移除元素，无需重新加载
                const itemRow = document.querySelector(`.shop-item-row[data-shop-item-id="${shopItemId}"]`);
                if (itemRow) {
                    itemRow.remove();
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            showToast('移除商品失败: ' + error.message, true);
        }
    }

    // 在页面加载时，为shop-config-modal的关闭按钮和事件绑定
    const shopModal = document.getElementById('shop-config-modal');
    if (shopModal) {
        shopModal.querySelector('.close-button').addEventListener('click', () => {
            shopModal.style.display = 'none';
        });
        shopModal.addEventListener('click', (e) => {
            if (e.target === shopModal) {
                shopModal.style.display = 'none';
            }
        });

        // 添加商品按钮事件
        document.getElementById('shop-add-item-btn').addEventListener('click', async () => {
            const sceneBuildingId = document.getElementById('shop-config-scene-building-id').value;
            const itemId = document.getElementById('shop-item-select').value;
            const stock = document.getElementById('shop-item-stock').value;
            const sortOrder = document.getElementById('shop-item-sort-order').value;

            if (!itemId) {
                showToast('请选择一个物品', true);
                return;
            }

            const formData = new FormData();
            formData.append('action', 'add_item');
            formData.append('scene_building_id', sceneBuildingId);
            formData.append('item_template_id', itemId);
            formData.append('stock', stock);
            formData.append('sort_order', sortOrder);

            try {
                const response = await fetch('api_shop.php', { method: 'POST', body: formData });
                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    // 刷新列表以显示新商品
                    const sceneName = document.getElementById('shop-config-scene-name').textContent;
                    const buildingName = document.getElementById('shop-config-building-name').textContent;
                    await openShopConfigModal(sceneBuildingId, sceneName, buildingName, false);
                    
                    // 清空输入以便连续添加
                    $('#shop-item-select').val(null).trigger('change');
                    document.getElementById('shop-item-stock').value = '';

                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showToast('添加商品失败: ' + error.message, true);
            }
        });
    }

    // Need to hide batch actions panel using its function to also clear selections
    // this is a global function now because it is on the element itself
    window.hideBatchActions = hideBatchActions; 
    window.removeShopItem = removeShopItem; // 将函数暴露到全局作用域
    
    initializeApp();
}); 