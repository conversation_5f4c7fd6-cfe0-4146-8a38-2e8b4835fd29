// 拆解N变量
var parts = N.split('_'); // 变量N在PHP中定义
var secretKey = parts.slice(0, -2).join('_'); // 获取除最后两个部分外的所有部分
var ase_key = parts[parts.length - 2]; // 倒数第二个部分
var ase_iv = parts[parts.length - 1]; // 最后一个部分

// AES加密函数（使用CryptoJS）
function encryptAESWithCryptoJS(data, key, iv) {
    // 确保密钥长度为32字节（AES-256），与PHP保持一致
    const keyHash = CryptoJS.SHA256(key);
    const ivUtf8 = CryptoJS.enc.Utf8.parse(iv);
    const encrypted = CryptoJS.AES.encrypt(data, keyHash, {
        iv: ivUtf8,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
    });
    return encrypted.toString();
}

// 生成随机十六进制字符串
function generateRandomHex(length) {
    if (typeof crypto !== 'undefined' && crypto.getRandomValues) {
        const array = new Uint8Array(length / 2);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    }
}

// 使用crypto-js生成时间戳签名
function generateTimestampSignature(secretKey) {
    return new Promise((resolve, reject) => {
        try {
            // 等待crypto-js库加载完成
            if (typeof CryptoJS === 'undefined') {
                // 如果crypto-js未加载，等待一段时间后重试
                setTimeout(() => {
                    if (typeof CryptoJS !== 'undefined') {
                        resolve(generateSignatureWithCryptoJS(secretKey));
                    }
                }, 100);
            } else {
                resolve(generateSignatureWithCryptoJS(secretKey));
            }
        } catch (error) {
            // console.error('Error generating signature:', error);
        }
    });
}

// 使用CryptoJS生成签名
function generateSignatureWithCryptoJS(secretKey) {
    // 获取当前时间戳（秒级）
    const timestamp = Math.floor(Date.now() / 1000);

    // JS脚本加密时间戳
    const encryptedTimestamp = encryptAESWithCryptoJS(timestamp.toString(), ase_key, ase_iv);
    // console.log(encryptedTimestamp);
    // 生成随机盐值（16字符十六进制）
    const saltHex = generateRandomHex(16);

    // 创建待签名字符串（使用加密的时间戳）
    const data = `${encryptedTimestamp}:${saltHex}`;

    // 使用CryptoJS生成HMAC-SHA256签名
    const signature = CryptoJS.HmacSHA256(data, secretKey).toString(CryptoJS.enc.Hex);

    // 组合为完整令牌并进行base64编码（使用加密的时间戳）
    const tokenData = `${encryptedTimestamp}:${saltHex}:${signature}`;
    return btoa(tokenData);
}


// 初始化token并生成HTML内容
async function initializeWithToken() {
    try {
        const token = await generateTimestampSignature(secretKey);
        // console.log('Generated token:', token);

        // 设置全局变量
        window.token = token;

        // 现在生成HTML内容（确保token已经可用）
        generateHTMLContent();

    } catch (error) {
        // console.error('Token generation failed:', error);
    }
}

// HTML内容生成函数
function generateHTMLContent() {
    // console.log('Generating HTML content with token:', window.token);

    var text = "";
    text += "    <div class=\"container\">";
    text += "        <div class=\"game-header\"><h1>星尘战纪</h1></div>";
    text += "        <div class=\"connection-status\" id=\"connectionStatus\">连接中...</div>";
    text += "        ";
    text += "        <div id=\"authSection\">";
    text += "            <div id=\"loginSection\" class=\"form-section\">";
    text += "                <h4>玩家登录</h4>";
    text += "                <div class=\"form-group\">";
    text += "                    <label for=\"username\">登录账号:</label>";
    text += "                    <input type=\"text\" id=\"username\" name=\"username\" value=\"\">";
    text += "                </div>";
    text += "                <div class=\"form-group\">";
    text += "                    <label for=\"password\">密码:</label>";
    text += "                    <input type=\"password\" id=\"password\" name=\"password\" value=\"\">";
    text += "                </div>";
    text += "                <div class=\"form-actions\">";
    text += "                    <a href=\"#\" class=\"btn-primary\" id=\"loginButton\">登录</a>";
    text += "                </div>";
    text += "                <div id=\"loginError\" class=\"error-message\"></div>";
    text += "                <p style=\"margin-top: 15px; font-size: 14px; text-align: center;\">没有账号? <a href=\"#\" id=\"showRegisterLink\" class=\"form-switch-link\">立即注册</a></p>";
    text += "            </div>";
    text += "";
    text += "            <div id=\"registerSection\" class=\"form-section\" style=\"display: none;\">";
    text += "                <h4>创建新角色</h4>";
    text += "                <div class=\"form-group\">";
    text += "                    <label for=\"reg_login_name\">登录账号 (3-10位, 仅限字母数字):</label>";
    text += "                    <input type=\"text\" id=\"reg_login_name\" name=\"reg_login_name\">";
    text += "                </div>";
    text += "                <div class=\"form-group\">";
    text += "                    <label for=\"reg_nickname\">游戏昵称 (最多10个字符):</label>";
    text += "                    <input type=\"text\" id=\"reg_nickname\" name=\"reg_nickname\">";
    text += "                </div>";
    text += "                <div class=\"form-group\">";
    text += "                    <label for=\"reg_password\">密码 (最少6位):</label>";
    text += "                    <input type=\"password\" id=\"reg_password\" name=\"reg_password\">";
    text += "                </div>";
    text += "                <div class=\"form-actions\">";
    text += "                    <a href=\"#\" class=\"btn-primary\" id=\"registerButton\">注册</a>";
    text += "                </div>";
    text += "                <div id=\"registerError\" class=\"error-message\"></div>";
    text += "                 <div id=\"registerSuccess\" class=\"success-message\"></div>";
    text += "                <p style=\"margin-top: 15px; font-size: 14px; text-align: center;\">已有账号? <a href=\"#\" id=\"showLoginLink\" class=\"form-switch-link\">返回登录</a></p>";
    text += "            </div>";
    text += "        </div>";
    text += "        ";
    text += "        <div class=\"game-content\" id=\"gameContent\">";
    text += "            <div id=\"main-view\">";
    text += "               <div class=\"announcement-bar\" id=\"announcementBar\" style=\"display: none; cursor: pointer; padding: 5px; background-color: #2c3e50; color: #ecf0f1; border-left: 4px solid #3498db; margin: 5px 0; font-size: 14px;\" onclick=\"game.showAnnouncementDetail()\"></div>";
    text += "                <div id=\"playerInfo\">";
    text += "                     <p>";
    text += "                         <span id=\"playerName\"></span>";
    text += "                         <button class=\"btn\" id=\"reviveBtn\" style=\"margin-left: 10px; display: none;\" onclick=\"game.revivePlayer()\">[复活]</button>";
    text += "                     </p>";
    text += "                     <div id=\"playerHP\"></div>";
    text += "                     <div id=\"playerMP\"></div>";
    text += "                </div>";
    text += "                <hr class=\"section-divider\">";
    text += "                <div id=\"sceneInfo\">";
    text += "                    <p><b id=\"sceneName\"></b></p>";
    text += "                    <p id=\"sceneDescription\" style=\"color: #555; margin-top: 5px; font-size: 14px;\"><a href=\"#\" onclick=\"window.location.reload()\">断线重连，刷新页面</a></p>";
    text += "                    <div id=\"scenePlayers\"></div>";
    text += "                    <div id=\"sceneNpcs\"></div>";
    text += "                    <div id=\"sceneMonsters\"></div>";
    text += "                    <div id=\"sceneItems\"></div>";
    text += "                    <div id=\"sceneBuildings\"></div>";
    text += "                </div>";
    text += "                <hr class=\"section-divider\">";
    text += "                <div id=\"actionsContainer\">";
    text += "                    <button class=\"btn\" onclick=\"game.refreshScene()\">[查看]</button>";
    text += "                    <button class=\"btn\" id=\"chat-button\" onclick=\"game.showChatView()\">[公聊]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showPrivateChatView()\">[私聊]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showSelfView()\">[角色]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showInventoryView()\">[背包]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showEquipmentView()\">[装备]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.skillManager.showSkillsView()\">[技能]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.questManager.showQuestLogView()\">[任务]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showRedemptionCodeView()\">[兑换]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showAnnouncementListView()\">[公告]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.showConfigView()\">[设置]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.logout()\">[登出]</button>";
    text += "                    <hr class=\"section-divider\">";
    text += "                    <div id=\"pvpActionsContainer\">";
    text += "                        <button class=\"btn\" onclick=\"game.showPvpLeaderboard()\">[PVP]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.showPvpStats()\">[战绩]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.showLevelRanking()\">[等级]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.showRefineLeaderboardView()\">[神兵]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.showHeroRanking()\">[英雄]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.showVillainRanking()\">[黑手]</button>";
    text += "                    </div>";
    text += "                    <hr class=\"section-divider\">";
    text += "                     <div id=\"movementActions\" style=\"margin-top: 5px;\">";
    text += "                        <!-- Exits will be dynamically populated here -->";
    text += "                     </div>";
    text += "                    <hr class=\"section-divider\">";
    text += "                </div>";
    text += "                <div class=\"battle-log\">";
    text += "                    <div id=\"log\"></div>";
    text += "                </div>";
    text += "            </div>";
    text += "             <div id=\"battleView\" style=\"display:none;\">";
    text += "                <h3>战斗开始！</h3>";
    text += "                <div id=\"battleMonsterInfo\"></div>";
    text += "                <div id=\"battleIntentionDisplay\" style=\"padding: 5px; margin-top: 10px; text-align: center; font-weight: bold; color: #444;\"></div>";
    text += "                <div id=\"battleActionsContainer\" style=\"margin-top: 10px; display: flex; flex-wrap: wrap; gap: 5px; justify-content: flex-start;\">";
    text += "                    <!-- All battle action buttons will be populated here -->";
    text += "                </div>";
    text += "                <div style=\"margin-top: 15px;\">";
    text += "                    <h4 style=\"margin: 0 0 5px 0; padding-bottom: 3px; border-bottom: 1px solid var(--border-color);\">参战者</h4>";
    text += "                    <div id=\"battlePlayerInfo\"></div>";
    text += "                    <div id=\"battleEffectsContainer\"></div>";
    text += "                </div>";
    text += "                <div style=\"margin-top: 15px;\">";
    text += "                    <h4 style=\"margin: 0 0 5px 0; padding-bottom: 3px; border-bottom: 1px solid var(--border-color);\">战斗日志</h4>";
    text += "                    <div id=\"battleLogContainer\" style=\"max-height: 200px; overflow-y: auto;\"></div>";
    text += "                </div>";
    text += "                <div id=\"battleEndActions\" style=\"display: none; margin-top: 10px; border-top: 1px solid var(--border-color); padding-top: 10px; text-align: center;\">";
    text += "                    <p id=\"battleEndMessage\" style=\"margin-bottom: 10px; font-weight: bold;\"></p>";
    text += "                    <button class=\"btn-primary\" onclick=\"game.returnToSceneFromBattle()\">[返回场景]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"mapView\" style=\"display:none; border: 1px solid var(--border-color);\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">附近地图</h4>";
    text += "                <div style=\"text-align: center; font-size: 12px; color: #666; margin-bottom: 8px;\">点击相邻场景可直接移动</div>";
    text += "                <div id=\"mapGridContainer\" style=\"display: flex; justify-content: center; margin: 10px 0; overflow-x: auto;\">";
    text += "                    <div id=\"mapGrid\" style=\"display: grid; gap: 1px; grid-template-columns: repeat(5, 1fr); width: 300px;\"></div>";
    text += "                </div>";
    text += "                <div style=\"text-align: center; font-size: 11px; color: #666; margin: 8px 0;\">";
    text += "                    <span style=\"display: inline-block; width: 12px; height: 8px; background: #e8f4fd; border: 1px solid var(--main-color); margin-right: 3px;\"></span>当前位置 ";
    text += "                    <span style=\"display: inline-block; width: 12px; height: 8px; background: #e8f5e9; border: 1px solid #4caf50; margin: 0 3px 0 8px;\"></span>安全区 ";
    text += "                    <span style=\"display: inline-block; width: 12px; height: 8px; background: #ffebee; border: 1px solid #f44336; margin: 0 3px 0 8px;\"></span>危险区";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" id=\"reviveBtn\" onclick=\"game.reviveCity()\">[回城]</button>";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideMap()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"selfView\" class=\"view\" style=\"display:none;border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">角色属性</h4>";
    text += "                <div id=\"selfInfoContent\" style=\"font-size: 14px; line-height: 1.6;\"></div>";
    text += "                <div id=\"potentialPointsAllocator\" style=\"display:none; margin-top: 15px; padding-top: 10px; border-top: 1px solid var(--border-color);\">";
    text += "                    <h5 style=\"margin: 0 0 10px 0; text-align:center;\">分配潜力点</h5>";
    text += "                    <div id=\"allocatorError\" class=\"error-message\" style=\"min-height:auto; margin-bottom: 10px;\"></div>";
    text += "                    <table width=\"100%\">";
    text += "                        <tr>";
    text += "                            <td>力量:</td>";
    text += "                            <td><input type=\"number\" id=\"strength_points\" min=\"0\" value=\"0\" style=\"width: 60px;\"></td>";
    text += "                            <td>敏捷:</td>";
    text += "                            <td><input type=\"number\" id=\"agility_points\" min=\"0\" value=\"0\" style=\"width: 60px;\"></td>";
    text += "                        </tr>";
    text += "                        <tr>";
    text += "                            <td>体质:</td>";
    text += "                            <td><input type=\"number\" id=\"constitution_points\" min=\"0\" value=\"0\" style=\"width: 60px;\"></td>";
    text += "                            <td>智慧:</td>";
    text += "                            <td><input type=\"number\" id=\"intelligence_points\" min=\"0\" value=\"0\" style=\"width: 60px;\"></td>";
    text += "                        </tr>";
    text += "                    </table>";
    text += "                    <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                        <button class=\"btn-primary\" onclick=\"game.allocatePotentialPoints()\">确认加点</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideSelfView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"skillsView\" class=\"view\" style=\"display:none;border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">我的技能</h4>";
    text += "                <div id=\"skillsListView\" style=\"display:block;\">";
    text += "                    <!-- 职业筛选按钮 -->";
    text += "                    <div id=\"skillJobFilters\" style=\"text-align: center; margin-bottom: 10px; display: none;\">";
    text += "                        <!-- 筛选按钮将在这里动态生成 -->";
    text += "                    </div>";
    text += "                    <div id=\"skillsListContainer\" class=\"view-content\" style=\"padding-top: 10px; font-size: 14px; line-height: 1.6;\">";
    text += "                        <!-- Skill list will be shown here -->";
    text += "                    </div>";
    text += "                    <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                        <button class=\"btn-primary\" onclick=\"game.skillManager.hideSkillsView()\">[返回]</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "                <div id=\"skillDetailView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                    <h4 id=\"skillDetailName\" style=\"margin-top:0; text-align: center; border-bottom: 1px solid var(--border-color); padding-bottom: 5px; margin-bottom: 10px;\"></h4>";
    text += "                    <div id=\"skillDetailContainer\" class=\"view-content\" style=\"font-size: 14px; line-height: 1.6;\">";
    text += "                        <!-- Skill details will be shown here -->";
    text += "                    </div>";
    text += "                    <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                        <button class=\"btn-primary\" onclick=\"game.skillManager.hideSkillDetail()\">[返回]</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"inventoryView\" class=\"view\" style=\"display:none;border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">我的背包</h4>";
    text += "                <div class=\"currency-display-bar\">";
    text += "                    <span id=\"inventory-gold\"></span>";
    text += "                    <span id=\"inventory-diamonds\"></span>";
    text += "                </div>";
    text += "                <div id=\"inventory-filters\" style=\"margin-bottom: 10px; text-align: center; display: flex; flex-wrap: wrap; gap: 5px; justify-content: center;\">";
    text += "                </div>";
    text += "                <div id=\"inventorySuccess\" class=\"success-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"inventoryError\" class=\"error-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"inventory-list\" style=\"font-size: 14px; line-height: 1.8; min-height: 200px;\">";
    text += "                </div>";
    text += "                <div id=\"inventory-pagination\" style=\"margin-top: 10px; text-align: center;\">";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideInventoryView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"equipmentView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">我的装备</h4>";
    text += "                <div id=\"equipment-list\" style=\"font-size: 14px; line-height: 1.6;\">";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideEquipmentView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"itemDetailView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 id=\"itemDetailName\" style=\"margin-top:0; text-align: center; border-bottom: 1px solid var(--border-color); padding-bottom: 5px; margin-bottom: 10px;\"></h4>";
    text += "                <div id=\"itemDetailSuccess\" class=\"success-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"itemDetailError\" class=\"error-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"itemDetailContent\" style=\"font-size: 14px; line-height: 1.6;\"></div>";
    text += "                <div id=\"itemDetailActions\" style=\"text-align: center; margin-top: 15px; min-height: 30px;\">";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"if(game.npcManager && game.npcManager.savedNpcData) { game.npcManager.hideItemDetailView(); } else { game.hideItemDetailView(); }\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"gemSocketingView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">选择要镶嵌的宝石</h4>";
    text += "                <div id=\"gem-selection-list\" style=\"font-size: 14px; line-height: 1.8; min-height: 150px;\">";
    text += "                    <!-- Gem list will be populated here -->";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideGemSocketingView()\">[取消]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"monsterDetailView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 id=\"monsterDetailName\" style=\"margin-top:0; text-align: center; border-bottom: 1px solid var(--border-color); padding-bottom: 5px; margin-bottom: 10px;\"></h4>";
    text += "                <div id=\"monsterDetailContent\" style=\"font-size: 14px; line-height: 1.6;\"></div>";
    text += "                <div style=\"text-align: center; margin-top: 15px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideMonsterDetailView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"playerDetailView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 id=\"playerDetailName\" style=\"margin-top:0; text-align: center;\"></h4>";
    text += "                <div id=\"playerDetailContent\"></div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hidePlayerDetailView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"tradeView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <div class=\"trade-header\" style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid var(--border-color);\">";
    text += "                    <h4 style=\"margin: 0;\">与 <span id=\"tradePartnerName\">未知玩家</span> 交易</h4>";
    text += "                    <button id=\"tradeCancelBtn\" class=\"btn-danger\" onclick=\"game.cancelTrade()\">取消交易</button>";
    text += "                </div>";
    text += "                <div class=\"trade-content\" style=\"display: inline-block; gap: 20px;\">";
    text += "                    <div class=\"trade-section my-trade\" style=\"flex: 1; border: 1px solid var(--border-color); padding: 10px; border-radius: 5px;\">";
    text += "                        <h3 style=\"margin-top: 0; text-align: center; color: #4CAF50;\">我的物品</h3>";
    text += "                        <div class=\"trade-items-section\" style=\"margin-bottom: 15px;\">";
    text += "                            <div class=\"trade-items-header\" style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;\">";
    text += "                                <span style=\"font-weight: bold;\">物品:</span>";
    text += "                                <button id=\"addMyItemBtn\" class=\"btn-secondary\" style=\"font-size: 12px; padding: 2px 8px;\" onclick=\"game.showAddTradeItemDialog()\">添加物品</button>";
    text += "                            </div>";
    text += "                            <div id=\"myTradeItems\" style=\"min-height: 80px; max-height: 150px; overflow-y: auto; border: 1px dashed var(--border-color); padding: 5px; background-color: rgba(255,255,255,0.05);\">";
    text += "                                <div style=\"text-align: center; color: #888; font-size: 12px; padding: 20px;\">暂无物品</div>";
    text += "                            </div>";
    text += "                        </div>";
    text += "                        <div class=\"trade-currency-section\" style=\"margin-bottom: 15px;\">";
    text += "                            <div style=\"font-weight: bold; margin-bottom: 8px;\">货币:</div>";
    text += "                            <div style=\"display: flex; gap: 10px; margin-bottom: 8px;\">";
    text += "                                <div style=\"flex: 1;\">";
    text += "                                    <label style=\"font-size: 12px;\">金币:</label>";
    text += "                                    <input type=\"number\" id=\"myGoldInput\" min=\"0\" value=\"0\" style=\"width: 100%; padding: 4px; margin-top: 2px;\" onchange=\"game.updateTradeCurrency()\">";
    text += "                                </div>";
    text += "                                <div style=\"flex: 1;\">";
    text += "                                    <label style=\"font-size: 12px;\">钻石:</label>";
    text += "                                    <input type=\"number\" id=\"myDiamondInput\" min=\"0\" value=\"0\" style=\"width: 100%; padding: 4px; margin-top: 2px;\" onchange=\"game.updateTradeCurrency()\">";
    text += "                                </div>";
    text += "                            </div>";
    text += "                        </div>";
    text += "                        <div style=\"text-align: center;\">";
    text += "                            <button id=\"myConfirmBtn\" class=\"btn-primary\" onclick=\"game.confirmTrade()\" disabled>确认交易</button>";
    text += "                            <div id=\"myConfirmStatus\" style=\"font-size: 12px; margin-top: 5px; color: #888;\">等待添加物品或货币</div>";
    text += "                        </div>";
    text += "                    </div>";
    text += "                    <div class=\"trade-section partner-trade\" style=\"flex: 1; border: 1px solid var(--border-color); padding: 10px; border-radius: 5px;\">";
    text += "                        <h3 style=\"margin-top: 0; text-align: center; color: #FF9800;\">对方物品</h3>";
    text += "                        <div class=\"trade-items-section\" style=\"margin-bottom: 15px;\">";
    text += "                            <div style=\"font-weight: bold; margin-bottom: 8px;\">物品:</div>";
    text += "                            <div id=\"partnerTradeItems\" style=\"min-height: 80px; max-height: 150px; overflow-y: auto; border: 1px dashed var(--border-color); padding: 5px; background-color: rgba(255,255,255,0.05);\">";
    text += "                                <div style=\"text-align: center; color: #888; font-size: 12px; padding: 20px;\">暂无物品</div>";
    text += "                            </div>";
    text += "                        </div>";
    text += "                        <div class=\"trade-currency-section\" style=\"margin-bottom: 15px;\">";
    text += "                            <div style=\"font-weight: bold; margin-bottom: 8px;\">货币:</div>";
    text += "                            <div id=\"partnerCurrency\" style=\"padding: 8px; border: 1px dashed var(--border-color); background-color: rgba(255,255,255,0.05); text-align: center; color: #888; font-size: 12px;\">";
    text += "                                暂无货币";
    text += "                            </div>";
    text += "                        </div>";
    text += "                        <div style=\"text-align: center;\">";
    text += "                            <div id=\"partnerConfirmStatus\" style=\"font-size: 14px; padding: 8px; border-radius: 3px; background-color: rgba(255,255,255,0.1);\">等待对方操作...</div>";
    text += "                        </div>";
    text += "                    </div>";
    text += "                </div>";
    text += "                <div id=\"tradeStatusMessage\" style=\"margin-top: 15px; padding: 8px; text-align: center; border-radius: 3px; display: none;\"></div>";
    text += "            </div>";
    text += "            <div id=\"tradeItemSelectionView\" style=\"display:none; position: absolute; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0,0,0,0.8); z-index: 1000;\">";
    text += "                <div style=\"position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background-color: var(--bg-color); border: 2px solid var(--border-color); border-radius: 8px; padding: 12px; width: 100%; height: 80%; overflow: hidden; box-shadow: 0 4px 20px rgba(0,0,0,0.5); display: flex; flex-direction: column;\">";
    text += "                    <h4 style=\"margin: 0 0 10px 0; text-align: center; color: var(--main-color); flex-shrink: 0;\">选择交易物品</h4>";
    text += "                    <div id=\"tradeItemSelectionError\" class=\"error-message\" style=\"min-height: 0; margin-bottom: 8px; padding: 3px; border: 1px dashed transparent; border-radius: 3px; flex-shrink: 0;\"></div>";
    text += "                    <div style=\"margin-bottom: 10px; text-align: center; flex-shrink: 0;\">";
    text += "                        <input type=\"text\" id=\"tradeItemSearchInput\" placeholder=\"搜索...\" style=\"width: 120px; padding: 6px; margin-right: 8px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;\" onkeyup=\"game.filterTradeItems()\">";
    text += "                        <select id=\"tradeItemCategoryFilter\" onchange=\"game.filterTradeItems()\" style=\"padding: 6px; border: 1px solid var(--border-color); border-radius: 4px; font-size: 12px;\">";
    text += "                            <option value=\"\">所有类别</option>";
    text += "                            <!-- 动态分类选项将在这里生成 -->";
    text += "                        </select>";
    text += "                    </div>";
    text += "                    <div id=\"tradeItemSelectionList\" style=\"flex: 1; overflow-y: auto; border: 1px solid var(--border-color); padding: 8px; background-color: rgba(255,255,255,0.05); border-radius: 4px;\">";
    text += "                        <div style=\"text-align: center; color: #888; padding: 20px;\">加载中...</div>";
    text += "                    </div>";
    text += "                    <style>";
    text += "                        .container { position: relative; }";
    text += "                        .quantity-btn { transition: all 0.2s ease; }";
    text += "                        .quantity-btn:hover { background-color: var(--main-color) !important; color: var(--bg-color) !important; }";
    text += "                        .quantity-btn:disabled { opacity: 0.5; cursor: not-allowed; }";
    text += "                        .quantity-btn:disabled:hover { background-color: var(--bg-color) !important; color: var(--text-color) !important; }";
    text += "                    </style>";
    text += "                    <div style=\"text-align: center; margin-top: 10px; flex-shrink: 0;\">";
    text += "                        <button class=\"btn-primary\" onclick=\"game.hideTradeItemSelectionView()\" style=\"padding: 6px 16px; font-size: 12px;\">取消</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"buildingView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 id=\"buildingName\" style=\"margin-top:0; text-align: center;\"></h4>";
    text += "                <div id=\"buildingContent\"></div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.buildingManager.hideBuildingView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            <div id=\"configView\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">战斗药品配置</h4>";
    text += "                <div id=\"configSuccess\" class=\"success-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"configError\" class=\"error-message\" style=\"min-height: 0; margin-bottom: 10px; padding: 5px; border: 1px dashed transparent; border-radius: 3px;\"></div>";
    text += "                <div id=\"current-potion-config\" style=\"font-size: 14px; line-height: 1.8; margin-bottom: 15px; padding-bottom: 10px; border-bottom: 1px solid var(--border-color);\">";
    text += "                    <div style=\"margin-bottom: 10px;\">";
    text += "                        <div><b>回血药剂:</b></div>";
    text += "                        <div id=\"configured-hp-potion\" style=\"padding-left: 10px; margin-top: 3px;\">未配置</div>";
    text += "                    </div>";
    text += "                    <div>";
    text += "                        <div><b>回蓝药剂:</b></div>";
    text += "                        <div id=\"configured-mp-potion\" style=\"padding-left: 10px; margin-top: 3px;\">未配置</div>";
    text += "                    </div>";
    text += "                </div>";
    text += "                <div id=\"potion-selection-list\" style=\"font-size: 14px; line-height: 1.8; min-height: 150px;\">";
    text += "                    <!-- Potion list will be populated here -->";
    text += "                </div>";
    text += "                <div style=\"text-align: center; margin-top: 10px;\">";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideConfigView()\">[返回]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 丢弃物品界面 -->";
    text += "            <div id=\"dropItemView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">丢弃物品</h4>";
    text += "                <div id=\"dropItemInfo\" style=\"text-align:center; margin-bottom:15px;\">";
    text += "                    <p id=\"dropItemName\" style=\"font-size:16px; font-weight:bold;\"></p>";
    text += "                    <p id=\"dropItemDescription\" style=\"color:#666; font-size:14px;\"></p>";
    text += "                    <p id=\"dropItemWarning\" style=\"color:#ff6b6b; font-weight:bold; margin-top:10px;\">绑定物品将会直接消失！</p>";
    text += "                </div>";
    text += "                ";
    text += "                <div id=\"dropQuantitySelector\" style=\"margin:15px 0; text-align:center; display:none;\">";
    text += "                    <p>选择丢弃数量:</p>";
    text += "                    <div style=\"display:flex; align-items:center; justify-content:center; margin-top:10px;\">";
    text += "                        <button id=\"decreaseDropQty\" class=\"btn-primary\" style=\"width:30px;\">-</button>";
    text += "                        <input type=\"number\" id=\"dropQuantity\" min=\"1\" value=\"1\" style=\"width:60px; text-align:center; margin:0 10px;\" readonly>";
    text += "                        <button id=\"increaseDropQty\" class=\"btn-primary\" style=\"width:30px;\">+</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "                ";
    text += "                <div id=\"dropItemMessage\" class=\"error-message\" style=\"min-height:auto; margin:15px 0;\"></div>";
    text += "                ";
    text += "                <div style=\"text-align:center; margin-top:20px;\">";
    text += "                    <button id=\"confirmDropBtn\" class=\"btn-primary btn-danger\" onclick=\"game.confirmDropItem()\">[确认丢弃]</button>";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideDropItemView()\">[取消]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 绑定物品界面 -->";
    text += "            <div id=\"bindItemView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px;\">";
    text += "                <h4 style=\"margin-top:0; text-align: center;\">绑定物品</h4>";
    text += "                <div id=\"bindItemInfo\" style=\"text-align:center; margin-bottom:15px;\">";
    text += "                    <p id=\"bindItemName\" style=\"font-size:16px; font-weight:bold;\"></p>";
    text += "                </div>";
    text += "                ";
    text += "                <div style=\"margin:15px 0; text-align:center;\">";
    text += "                    <p style=\"color:#ff6b6b; font-weight:bold;\">警告：物品绑定后将无法交易或掉落！</p>";
    text += "                    <p>绑定的物品丢弃时会直接消失，请谨慎操作。</p>";
    text += "                </div>";
    text += "                ";
    text += "                <div id=\"bindItemMessage\" class=\"error-message\" style=\"min-height:auto; margin:15px 0;\"></div>";
    text += "                ";
    text += "                <div style=\"text-align:center; margin-top:20px;\">";
    text += "                    <button id=\"confirmBindBtn\" class=\"btn-primary btn-danger\" onclick=\"game.confirmBindItem()\">[确认绑定]</button>";
    text += "                    <button class=\"btn-primary\" onclick=\"game.hideBindItemView()\">[取消]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 公告详情界面 -->";
    text += "            <div id=\"announcementView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 15px;\">";
    text += "                <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;\">";
    text += "                    <h3 id=\"announcementTitle\" style=\"margin: 0; color: #2c3e50;\"></h3>";
    text += "                    <button class=\"btn\" onclick=\"game.hideAnnouncementView()\">[关闭]</button>";
    text += "                </div>";
    text += "                <div id=\"announcementMeta\" style=\"color: #7f8c8d; font-size: 12px; margin-bottom: 15px; border-bottom: 1px solid #ecf0f1; padding-bottom: 10px;\"></div>";
    text += "                <div id=\"announcementContent\" style=\"line-height: 1.6; color: #2c3e50;\"></div>";
    text += "                <div style=\"text-align: center; margin-top: 20px;\">";
    text += "                    <button class=\"btn\" onclick=\"game.backToAnnouncementList()\" style=\"margin-right: 10px;\">[返回列表]</button>";
    text += "                    <button class=\"btn\" onclick=\"game.hideAnnouncementView()\">[返回游戏]</button>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 兑换码界面 -->";
    text += "            <div id=\"redemptionCodeView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px; max-width: 100%; box-sizing: border-box;\">";
    text += "                <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">";
    text += "                    <h3 style=\"margin: 0; color: #2c3e50; font-size: 16px;\">兑换码</h3>";
    text += "                    <button class=\"btn\" onclick=\"game.hideRedemptionCodeView()\">[关闭]</button>";
    text += "                </div>";
    text += "                <div class=\"redemption-code-container\" style=\"max-width: 100%;\">";
    text += "                    <div class=\"redemption-input-section\" style=\"margin-bottom: 15px; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px; background-color: rgba(255,255,255,0.05);\">";
    text += "                        <div class=\"input-group\" style=\"margin-bottom: 10px;\">";
    text += "                            <label for=\"redemption-code-input\" style=\"display: block; margin-bottom: 5px; font-weight: bold; color: #2c3e50; font-size: 13px;\">请输入兑换码：</label>";
    text += "                            <input type=\"text\" id=\"redemption-code-input\" class=\"redemption-input\" ";
    text += "                                   placeholder=\"输入6位兑换码\" maxlength=\"6\" ";
    text += "                                   style=\"width: calc(100% - 20px); max-width: 180px; padding: 6px 8px; border: 1px solid var(--border-color); border-radius: 3px; font-size: 14px; text-transform: uppercase; text-align: center; letter-spacing: 1px; font-family: monospace; box-sizing: border-box; display: block; margin: 0 auto;\">";
    text += "                        </div>";
    text += "                        <div class=\"redemption-actions\" style=\"display: flex; justify-content: center; gap: 5px; margin-top: 10px; flex-wrap: wrap;\">";
    text += "                            <button class=\"btn btn-primary\" id=\"redeem-button\" onclick=\"game.redeemCode()\" style=\"padding: 5px 12px; font-size: 12px; min-width: 50px; flex: 0 0 auto;\">[兑换]</button>";
    text += "                            <button class=\"btn\" onclick=\"game.clearRedemptionInput()\" style=\"padding: 5px 12px; font-size: 12px; min-width: 50px; flex: 0 0 auto;\">[清空]</button>";
    text += "                        </div>";
    text += "                    </div>";
    text += "                    <div class=\"redemption-result-section\" id=\"redemption-result\" style=\"display: none; margin-bottom: 15px; padding: 10px; border: 1px solid var(--border-color); border-radius: 4px;\">";
    text += "                        <div class=\"result-header\" style=\"margin-bottom: 8px;\">";
    text += "                            <h4 id=\"redemption-result-title\" style=\"margin: 0; color: #2c3e50; font-size: 14px;\">兑换结果</h4>";
    text += "                        </div>";
    text += "                        <div class=\"result-content\" id=\"redemption-result-content\" style=\"margin-bottom: 10px; font-size: 13px;\">";
    text += "                            <!-- 兑换结果将在这里显示 -->";
    text += "                        </div>";
    text += "                        <div class=\"result-actions\" style=\"text-align: center;\">";
    text += "                            <button class=\"btn btn-primary\" onclick=\"game.hideRedemptionResult()\" style=\"padding: 5px 12px; font-size: 12px;\">[确定]</button>";
    text += "                        </div>";
    text += "                    </div>";
    text += "                    <div class=\"redemption-tips\" style=\"padding: 10px; border: 1px solid var(--border-color); border-radius: 4px; background-color: rgba(255,255,255,0.02);\">";
    text += "                        <h4 style=\"margin: 0 0 8px 0; color: #2c3e50; font-size: 13px;\">使用说明：</h4>";
    text += "                        <ul style=\"margin: 0; padding-left: 16px; font-size: 12px; line-height: 1.4; color: #555;\">";
    text += "                            <li style=\"margin-bottom: 3px;\">兑换码由6位大写字母和数字组成</li>";
    text += "                            <li style=\"margin-bottom: 3px;\">每个兑换码都有使用次数限制</li>";
    text += "                            <li style=\"margin-bottom: 3px;\">部分兑换码可能有过期时间</li>";
    text += "                            <li style=\"margin-bottom: 3px;\">兑换获得的物品将直接添加到背包</li>";
    text += "                            <li style=\"margin-bottom: 3px;\">已使用的兑换码无法重复使用</li>";
    text += "                        </ul>";
    text += "                    </div>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 公告列表界面 -->";
    text += "            <div id=\"announcementListView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px; max-width: 100%; box-sizing: border-box;\">";
    text += "                <div style=\"display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;\">";
    text += "                    <h3 style=\"margin: 0; color: #2c3e50; font-size: 16px;\">游戏公告</h3>";
    text += "                    <button class=\"btn\" onclick=\"game.hideAnnouncementListView()\">[关闭]</button>";
    text += "                </div>";
    text += "                <div class=\"announcement-list-container\" style=\"max-width: 100%;\">";
    text += "                    <div class=\"announcement-list-content\" id=\"announcement-list-content\" style=\"max-height: 400px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 4px; padding: 10px; background-color: rgba(255,255,255,0.02);\">";
    text += "                        <div class=\"loading\" style=\"text-align: center; padding: 20px; color: #6c757d;\">正在加载公告列表...</div>";
    text += "                    </div>";
    text += "                    <div style=\"text-align: center; margin-top: 15px;\">";
    text += "                        <button class=\"btn\" onclick=\"game.hideAnnouncementListView()\">[返回游戏]</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "            </div>";
    text += "            ";
    text += "            <!-- 装备凝练榜界面 -->";
    text += "            <div id=\"refineLeaderboardView\" class=\"view\" style=\"display:none; border: 1px solid var(--border-color); padding: 10px; max-width: 100%; box-sizing: border-box;\">";
    text += "                <div style=\"margin-bottom: 10px;\">";
    text += "                    <h3 style=\"margin: 0; color: #2c3e50; font-size: 16px;\">神兵榜</h3>";
    text += "                </div>";
    text += "                <div class=\"refine-leaderboard-container\" style=\"max-width: 100%;\">";
    text += "                    <div class=\"refine-leaderboard-content\" id=\"refine-leaderboard-content\" style=\"max-height: 500px; overflow-y: auto; border: 1px solid var(--border-color); border-radius: 4px; padding: 10px; background-color: rgba(255,255,255,0.02);\">";
    text += "                        <div class=\"loading\" style=\"text-align: center; padding: 20px; color: #6c757d;\">正在加载神兵榜...</div>";
    text += "                    </div>";
    text += "                    <div style=\"text-align: center; margin-top: 15px;\">";
    text += "                        <button class=\"btn\" onclick=\"game.refreshRefineLeaderboard()\">[刷新]</button>";
    text += "                        <button class=\"btn\" onclick=\"game.hideRefineLeaderboardView()\">[返回游戏]</button>";
    text += "                    </div>";
    text += "                </div>";
    text += "            </div>";
    text += "        </div>";
    text += "    </div>";
    text += "";
    text += "    <script src=\"js/u.js?v=" + String(window.token) + "\"></script>";
    text += "    <script src=\"js/a.js?v=" + String(window.token) + "\"></script>";
    text += "";

    // 立即添加统一的加载界面
    const freezeStyle = document.createElement('style');
    freezeStyle.id = 'unified-loading-style';
    freezeStyle.textContent = `
        .container { position: relative; }
        #unified-loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.95);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            color: white;
            font-family: Arial, sans-serif;
        }
        .loading-content {
            text-align: center;
            max-width: 300px;
        }
        .healthy-gaming-notice {
            margin-top: 30px;
            padding: 15px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            max-width: 400px;
            backdrop-filter: blur(5px);
        }
        .healthy-gaming-notice p {
            margin: 8px 0;
        }
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px auto;
        }
        .loading-text {
            font-size: 16px;
            margin-bottom: 15px;
        }
        .loading-progress {
            width: 100%;
            height: 20px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .loading-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 10px;
            transition: width 0.3s ease;
            width: 0%;
        }
        .loading-percentage {
            font-size: 14px;
            color: #bbb;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    `;
    document.head.appendChild(freezeStyle);

    // 延迟添加统一加载界面，确保容器已经渲染
    setTimeout(() => {
        const container = document.querySelector('.container');
        if (container) {
            const loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'unified-loading-overlay';
            loadingOverlay.innerHTML = `
                <div class="loading-content">
                    <div class="loading-spinner"></div>
                    <div class="loading-text" id="loading-text">初始化游戏环境...</div>
                    <div class="loading-progress">
                        <div class="loading-progress-bar" id="loading-progress-bar"></div>
                    </div>
                    <div class="loading-percentage" id="loading-percentage">0%</div>
                </div>
                <div class="healthy-gaming-notice" style="margin-top: 30px; padding: 15px; background: rgba(255, 255, 255, 0.1); border-radius: 8px; border: 1px solid rgba(255, 255, 255, 0.2); max-width: 400px;">
                    <div style="text-align: center; margin-bottom: 10px;">
                        <span style="font-size: 16px; font-weight: bold; color: #e74c3c;">🎮 健康游戏忠告 🎮</span>
                    </div>
                    <div style="font-size: 13px; line-height: 1.6; color: #ecf0f1; text-align: center;">
                        <p style="margin: 10px 0; font-weight: bold; color: #f39c12;">抵制不良游戏，拒绝盗版游戏</p>
                        <p style="margin: 8px 0;">注意自我保护，谨防受骗上当</p>
                        <p style="margin: 8px 0;">适度游戏益脑，沉迷游戏伤身</p>
                        <p style="margin: 8px 0;">合理安排时间，享受健康生活</p>
                    </div>
                    <div style="text-align: center; margin-top: 15px; font-size: 11px; color: #95a5a6; border-top: 1px solid rgba(255,255,255,0.1); padding-top: 10px;">
                        请合理安排游戏时间，注意劳逸结合，享受健康生活
                    </div>
                </div>
            `;
            container.appendChild(loadingOverlay);

            // 设置初始进度
            window.updateLoadingProgress = function(progress, text) {
                const progressBar = document.getElementById('loading-progress-bar');
                const percentageText = document.getElementById('loading-percentage');
                const loadingText = document.getElementById('loading-text');

                if (progressBar) {
                    progressBar.style.width = progress + '%';
                }
                if (percentageText) {
                    percentageText.textContent = Math.round(progress) + '%';
                }
                if (loadingText && text) {
                    loadingText.textContent = text;
                }
            };

            // 初始进度
            window.updateLoadingProgress(10, '初始化游戏环境...');
        }
    }, 10);

    // 备用解冻机制：如果10秒后还没有解冻，自动解冻
    setTimeout(() => {
        const overlay = document.getElementById('unified-loading-overlay');
        const style = document.getElementById('unified-loading-style');
        if (overlay) {
            // console.log('Auto-unfreezing page after timeout');
            overlay.remove();
        }
        if (style) {
            style.remove();
        }
    }, 10000);

    // 全局解冻函数，供其他脚本调用
    window.unfreezeUnifiedPage = function() {
        const overlay = document.getElementById('unified-loading-overlay');
        const style = document.getElementById('unified-loading-style');
        if (overlay) {
            // console.log('Unfreezing page via global function');
            overlay.remove();
        }
        if (style) {
            style.remove();
        }
    };

    // 先写入HTML内容
    document.writeln(text);

}

// 立即执行初始化
initializeWithToken();
