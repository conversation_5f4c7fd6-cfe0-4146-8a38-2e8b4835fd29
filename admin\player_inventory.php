<?php 
$pageTitle = '玩家管理系统';
$currentPage = 'player_inventory';
$extra_css = '
    <link rel="stylesheet" href="player_inventory.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* Toast Notifications */
        .toast-container {
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 1050;
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }
        .toast {
            background-color: #28a745; /* Default success */
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            opacity: 0;
            transform: translateX(120%);
            transition: all 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55);
            min-width: 250px;
        }
        .toast.show {
            opacity: 1;
            transform: translateX(0);
        }
        .toast.error {
            background-color: #dc3545;
        }
        
        /* 在线状态指示器 */
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online {
            background-color: #28a745;
        }
        .status-offline {
            background-color: #6c757d;
        }
        
        /* 选项卡样式 */
        .tabs {
            display: flex;
            border-bottom: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .tab {
            padding: 10px 20px;
            cursor: pointer;
            border: 1px solid transparent;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
            margin-bottom: -1px;
        }
        .tab.active {
            border-color: #dee2e6 #dee2e6 #fff;
            background-color: #fff;
            color: #007bff;
            font-weight: 600;
        }
        .tab-content {
            display: none;
        }
        .tab-content.active {
            display: block;
        }
        
        /* 属性编辑表单 */
        .attributes-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
            gap: 15px;
        }
        .attribute-item {
            margin-bottom: 15px;
        }
        .attribute-item label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
    </style>
';
require_once 'layout_header.php'; 
?>

<div class="page-content-grid">
    <div class="sidebar-secondary">
        <h3>玩家列表</h3>
        <input type="text" id="player-search-input" class="form-control" placeholder="搜索玩家...">
        <ul id="player-list">
            <!-- 玩家列表将通过JS动态加载 -->
        </ul>
    </div>

    <div class="main-content-area">
        <div class="no-player-selected">
            <p>请从左侧选择一个玩家。</p>
        </div>
        
        <div id="player-details" style="display: none;">
            <div class="player-header">
                <h2 id="player-name"></h2>
                <div class="player-meta">
                    <span id="player-level"></span>
                    <span id="player-location"></span>
                    <span id="player-id"></span>
                    <span id="player-logindate"></span>
                </div>
            </div>
            
            <!-- 选项卡导航 -->
            <div class="tabs">
                <div class="tab active" data-target="inventory-tab">背包</div>
                <div class="tab" data-target="attributes-tab">属性</div>
                <div class="tab" data-target="quests-tab">任务</div>
                <div class="tab" data-target="skills-tab">技能</div>
            </div>
            
            <!-- 背包选项卡 -->
            <div id="inventory-tab" class="tab-content active">
        <div id="inventory-display">
            <div class="inventory-header">
                        <div id="currency-display">
                    <span class="currency-item">
                        <img src="../assets/icons/gold_coin.png" alt="金币" class="currency-icon">
                        <span id="gold-amount">0</span>
                    </span>
                    <span class="currency-item">
                        <img src="../assets/icons/diamond.png" alt="钻石" class="currency-icon">
                        <span id="diamonds-amount">0</span>
                    </span>
                </div>
                        <button id="add-item-to-player-btn" class="btn btn-primary">添加物品</button>
                    </div>
                    <!-- 背包内容将在这里动态渲染 -->
                </div>
            </div>
            
            <!-- 属性选项卡 -->
            <div id="attributes-tab" class="tab-content">
                <form id="player-attributes-form">
                    <div class="attributes-grid">
                        <!-- 基本属性 -->
                        <div class="attribute-group">
                            <h4>基本属性</h4>
                            <div class="attribute-item">
                                <label for="attr-level">等级</label>
                                <input type="number" id="attr-level" name="level" class="form-control attribute-input" min="1">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-experience">经验值</label>
                                <input type="number" id="attr-experience" name="experience" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-experience_to_next_level">升级所需经验</label>
                                <input type="number" id="attr-experience_to_next_level" name="experience_to_next_level" class="form-control attribute-input" min="1">
                            </div>
                        </div>
                        
                        <!-- 生命/魔法 -->
                        <div class="attribute-group">
                            <h4>生命/魔法</h4>
                            <div class="attribute-item">
                                <label for="attr-hp">当前生命值</label>
                                <input type="number" id="attr-hp" name="hp" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-max_hp">最大生命值</label>
                                <input type="number" id="attr-max_hp" name="max_hp" class="form-control attribute-input" min="1">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-mp">当前魔法值</label>
                                <input type="number" id="attr-mp" name="mp" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-max_mp">最大魔法值</label>
                                <input type="number" id="attr-max_mp" name="max_mp" class="form-control attribute-input" min="1">
                            </div>
                        </div>
                        
                        <!-- 战斗属性 -->
                        <div class="attribute-group">
                            <h4>战斗属性</h4>
                            <div class="attribute-item">
                                <label for="attr-attack">攻击力</label>
                                <input type="number" id="attr-attack" name="attack" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-defense">防御力</label>
                                <input type="number" id="attr-defense" name="defense" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-attack_speed">攻击速度</label>
                                <input type="number" id="attr-attack_speed" name="attack_speed" class="form-control attribute-input" min="0" step="0.01">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-dodge_bonus">闪避率</label>
                                <input type="number" id="attr-dodge_bonus" name="dodge_bonus" class="form-control attribute-input" min="0">
                            </div>
                        </div>
                        
                        <!-- 基础属性点 -->
                        <div class="attribute-group">
                            <h4>基础属性点</h4>
                            <div class="attribute-item">
                                <label for="attr-strength">力量</label>
                                <input type="number" id="attr-strength" name="strength" class="form-control attribute-input" min="1">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-agility">敏捷</label>
                                <input type="number" id="attr-agility" name="agility" class="form-control attribute-input" min="1">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-constitution">体质</label>
                                <input type="number" id="attr-constitution" name="constitution" class="form-control attribute-input" min="1">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-intelligence">智力</label>
                                <input type="number" id="attr-intelligence" name="intelligence" class="form-control attribute-input" min="1">
                            </div>
                        </div>
                        
                        <!-- 元素抗性 -->
                        <div class="attribute-group">
                            <h4>元素抗性</h4>
                            <div class="attribute-item">
                                <label for="attr-fire_resistance">火焰抗性</label>
                                <input type="number" id="attr-fire_resistance" name="fire_resistance" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-ice_resistance">冰冻抗性</label>
                                <input type="number" id="attr-ice_resistance" name="ice_resistance" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-wind_resistance">风裂抗性</label>
                                <input type="number" id="attr-wind_resistance" name="wind_resistance" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-electric_resistance">闪电抗性</label>
                                <input type="number" id="attr-electric_resistance" name="electric_resistance" class="form-control attribute-input" min="0">
                            </div>
                        </div>
                        
                        <!-- 元素伤害 -->
                        <div class="attribute-group">
                            <h4>元素伤害</h4>
                            <div class="attribute-item">
                                <label for="attr-fire_damage">火焰伤害</label>
                                <input type="number" id="attr-fire_damage" name="fire_damage" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-ice_damage">冰冻伤害</label>
                                <input type="number" id="attr-ice_damage" name="ice_damage" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-wind_damage">风裂伤害</label>
                                <input type="number" id="attr-wind_damage" name="wind_damage" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-electric_damage">闪电伤害</label>
                                <input type="number" id="attr-electric_damage" name="electric_damage" class="form-control attribute-input" min="0">
                            </div>
                        </div>
                        
                        <!-- 职业 -->
                        <div class="attribute-group">
                            <h4>职业</h4>
                            <div class="attribute-item">
                                <label for="attr-current_job_id">当前职业ID</label>
                                <select id="attr-current_job_id" name="current_job_id" class="form-control attribute-input">
                                    <option value="1">新手</option>
                                    <option value="2">战士</option>
                                    <option value="3">法师</option>
                                    <option value="4">弓箭手</option>
                                </select>
                            </div>
                        </div>
                        
                        <!-- 位置 -->
                        <div class="attribute-group">
                            <h4>位置</h4>
                            <div class="attribute-item">
                                <label for="attr-current_scene_id">当前场景</label>
                                <select id="attr-current_scene_id" name="current_scene_id" class="form-control attribute-input">
                                    <!-- 场景列表将通过 JS 动态加载 -->
                                </select>
                            </div>
                        </div>
                        
                        <!-- 货币 -->
                        <div class="attribute-group">
                            <h4>货币</h4>
                            <div class="attribute-item">
                                <label for="attr-gold">金币</label>
                                <input type="number" id="attr-gold" name="gold" class="form-control attribute-input" min="0">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-diamonds">钻石</label>
                                <input type="number" id="attr-diamonds" name="diamonds" class="form-control attribute-input" min="0">
                            </div>
                        </div>
                        
                        <!-- 其他 -->
                        <div class="attribute-group">
                            <h4>其他</h4>
                            <div class="attribute-item">
                                <label for="attr-karma">善恶值</label>
                                <input type="number" id="attr-karma" name="karma" class="form-control attribute-input">
                            </div>
                            <div class="attribute-item">
                                <label for="attr-rage">怒气值</label>
                                <input type="number" id="attr-rage" name="rage" class="form-control attribute-input" min="0">
                            </div>
                        </div>
                    </div>
                    <div class="form-actions mt-4">
                        <button type="button" id="save-attributes-btn" class="btn btn-primary">保存属性修改</button>
                        <button type="button" id="reset-attributes-btn" class="btn btn-secondary ml-2">重置</button>
                    </div>
                </form>
            </div>
            
            <!-- 任务选项卡 -->
            <div id="quests-tab" class="tab-content">
                <div class="quests-list">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>任务名称</th>
                                <th>状态</th>
                                <th>开始时间</th>
                                <th>完成时间</th>
                            </tr>
                        </thead>
                        <tbody id="quests-table-body">
                            <!-- 任务列表将通过 JS 动态加载 -->
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- 技能选项卡 -->
            <div id="skills-tab" class="tab-content">
                <div class="skills-grid">
                    <!-- 技能列表将通过 JS 动态加载 -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加物品模态框 -->
<div id="addItemModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">向玩家添加物品</h2>
            <span class="modal-close-button">&times;</span>
        </div>
        <div class="modal-body">
            <div class="form-row">
                <div class="form-group">
                    <label for="modal-item-category-filter">按分类筛选</label>
                    <select id="modal-item-category-filter" class="form-control">
                        <option value="">所有分类</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="modal-item-search-input">按名称搜索</label>
                    <input type="text" id="modal-item-search-input" class="form-control" placeholder="输入物品名称...">
                </div>
            </div>
            <div class="form-group">
                <label for="modal-item-template-select">选择物品</label>
                <select id="modal-item-template-select" class="form-control" style="width: 100%;"></select>
            </div>
            <div class="form-group">
                <label for="modal-add-item-quantity">数量</label>
                <input type="number" id="modal-add-item-quantity" class="form-control" value="1" min="1">
            </div>
        </div>
        <div class="modal-footer">
            <button id="modal-add-item-btn" class="btn btn-primary">确认添加</button>
        </div>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script src="player_inventory.js"></script>

<div id="toast-container"></div>

<?php
require_once 'layout_footer.php';
?> 