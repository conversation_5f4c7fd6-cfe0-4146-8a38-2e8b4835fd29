<?php
// config/ConnectionPool.php
class ConnectionPool {
    private static $instance = null;
    private $connections = [];
    private $config;
    private $maxConnections = 10;
    private $currentConnections = 0;
    
    private function __construct() {
        $this->config = [
            'host' => 'localhost',
            'dbname' => 'game_battle',
            'username' => 'root',
            'password' => '',
            'charset' => 'utf8mb4'
        ];
    }
    
    public static function getInstance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    public function getConnection() {
        // 从池中获取可用连接
        if (!empty($this->connections)) {
            $connection = array_pop($this->connections);
            
            // 检查连接是否还有效
            if ($this->isConnectionValid($connection)) {
                return $connection;
            }
        }
        
        // 创建新连接
        if ($this->currentConnections < $this->maxConnections) {
            $connection = $this->createConnection();
            $this->currentConnections++;
            return $connection;
        }
        
        // 等待连接可用
        while (empty($this->connections)) {
            usleep(10000); // 等待10ms
        }
        
        return $this->getConnection();
    }
    
    public function releaseConnection($connection) {
        if ($this->isConnectionValid($connection)) {
            $this->connections[] = $connection;
        } else {
            $this->currentConnections--;
        }
    }
    
    private function createConnection() {
        try {
            $dsn = "mysql:host={$this->config['host']};dbname={$this->config['dbname']};charset={$this->config['charset']}";
            $pdo = new PDO($dsn, $this->config['username'], $this->config['password'], [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_PERSISTENT => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES {$this->config['charset']}",
                PDO::ATTR_TIMEOUT => 5
            ]);
            
            $pdo->exec("SET SESSION wait_timeout = 3600");
            $pdo->exec("SET SESSION interactive_timeout = 3600");
            
            return $pdo;
            
        } catch (PDOException $e) {
            throw new Exception('无法创建数据库连接: ' . $e->getMessage());
        }
    }
    
    private function isConnectionValid($connection) {
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            return false;
        }
    }
    
    public function query($sql, $params = []) {
        $connection = $this->getConnection();
        
        try {
            $stmt = $connection->prepare($sql);
            $stmt->execute($params);
            return $stmt;
            
        } catch (PDOException $e) {
            // 如果是连接问题，重试一次
            if (strpos($e->getMessage(), 'MySQL server has gone away') !== false) {
                $this->currentConnections--;
                $connection = $this->getConnection();
                $stmt = $connection->prepare($sql);
                $stmt->execute($params);
                return $stmt;
            }
            throw $e;
            
        } finally {
            $this->releaseConnection($connection);
        }
    }
}
