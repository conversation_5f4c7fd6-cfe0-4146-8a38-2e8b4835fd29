<?php
// admin/clear_server_cache.php
// 这是一个命令行工具，用于直接清除服务器缓存

// 检查是否在命令行中运行
if (php_sapi_name() !== 'cli') {
    echo "此脚本只能在命令行中运行";
    exit(1);
}

// 使用Redis设置缓存标志
try {
    $redis = new Redis();
    if ($redis->connect('127.0.0.1', 6379, 1)) {
        echo "已连接到Redis服务器\n";
        
        // 设置NPC缓存清理标志
        $redis->set('cache_flags:npc', '1');
        echo "已设置NPC缓存清理标志\n";
        
        // 设置对话缓存清理标志
        $redis->set('cache_flags:dialogue', '1');
        echo "已设置对话缓存清理标志\n";
        
        $redis->close();
        echo "Redis连接已关闭\n";
        
        echo "缓存清理标志已设置，服务器将在下一个检查周期清除缓存\n";
        exit(0);
    } else {
        echo "无法连接到Redis服务器\n";
        exit(1);
    }
} catch (Exception $e) {
    echo "Redis错误: " . $e->getMessage() . "\n";
    exit(1);
} 