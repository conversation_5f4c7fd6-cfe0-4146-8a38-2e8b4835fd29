<?php
require_once '../config/Database.php';
require_once 'auth.php';

// 确保用户已登录
// 这里不需要调用ensureAdminAuthenticated()，因为auth.php已经包含了会话检查的代码

$db = Database::getInstance();
$conn = $db->getConnection();

// 处理API请求
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'get_logs':
        getLogs();
        break;
    case 'get_log_detail':
        getLogDetail();
        break;
    case 'export_log':
        exportLog();
        break;
    case 'get_scenes':
        getScenes();
        break;
    case 'clear_logs':
        clearLogs();
        break;
    default:
        outputError('未知操作');
}

// 获取交易记录列表
function getLogs() {
    global $conn;
    
    // 分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 20;
    $offset = ($page - 1) * $pageSize;
    
    // 过滤条件
    $tradeStatus = $_GET['trade_status'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';
    $sceneId = $_GET['scene_id'] ?? '';
    $playerName = $_GET['player_name'] ?? '';
    
    // 构建WHERE子句
    $whereConditions = [];
    $params = [];
    
    if (!empty($tradeStatus)) {
        $whereConditions[] = "pt.status = ?";
        $params[] = $tradeStatus;
    }
    
    if (!empty($startDate)) {
        $whereConditions[] = "DATE(pt.created_at) >= ?";
        $params[] = $startDate;
    }
    
    if (!empty($endDate)) {
        $whereConditions[] = "DATE(pt.created_at) <= ?";
        $params[] = $endDate;
    }
    
    if (!empty($sceneId)) {
        $whereConditions[] = "pt.scene_id = ?";
        $params[] = $sceneId;
    }
    
    if (!empty($playerName)) {
        $whereConditions[] = "(ai.username LIKE ? OR at.username LIKE ?)";
        $params[] = "%{$playerName}%";
        $params[] = "%{$playerName}%";
    }
    
    $whereClause = !empty($whereConditions) ? 'WHERE ' . implode(' AND ', $whereConditions) : '';
    
    // 获取总数
    $countSql = "
        SELECT COUNT(*) as total
        FROM player_trades pt
        LEFT JOIN player_attributes pa_i ON pt.initiator_id = pa_i.id
        LEFT JOIN accounts ai ON pa_i.account_id = ai.id
        LEFT JOIN player_attributes pa_t ON pt.target_id = pa_t.id
        LEFT JOIN accounts at ON pa_t.account_id = at.id
        LEFT JOIN scenes s ON pt.scene_id = s.id
        {$whereClause}
    ";
    
    $countStmt = $conn->prepare($countSql);
    $countStmt->execute($params);
    $total = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    
    // 获取记录
    $sql = "
        SELECT
            pt.id,
            pt.created_at,
            pt.completed_at,
            pt.status,
            pt.scene_id,
            s.name as scene_name,
            ai.username as initiator_name,
            at.username as target_name,
            (SELECT COUNT(*) FROM trade_items ti WHERE ti.trade_id = pt.id) as total_items,
            (SELECT COALESCE(SUM(tc.gold_amount + tc.diamond_amount), 0) FROM trade_currencies tc WHERE tc.trade_id = pt.id) as total_currency
        FROM player_trades pt
        LEFT JOIN player_attributes pa_i ON pt.initiator_id = pa_i.id
        LEFT JOIN accounts ai ON pa_i.account_id = ai.id
        LEFT JOIN player_attributes pa_t ON pt.target_id = pa_t.id
        LEFT JOIN accounts at ON pa_t.account_id = at.id
        LEFT JOIN scenes s ON pt.scene_id = s.id
        {$whereClause}
        ORDER BY pt.created_at DESC
        LIMIT {$pageSize} OFFSET {$offset}
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算分页信息
    $totalPages = ceil($total / $pageSize);
    $showingFrom = $total > 0 ? $offset + 1 : 0;
    $showingTo = min($offset + $pageSize, $total);
    
    outputSuccess([
        'logs' => $logs,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'page_size' => $pageSize,
            'total' => $total,
            'showing_from' => $showingFrom,
            'showing_to' => $showingTo
        ]
    ]);
}

// 获取交易详情
function getLogDetail() {
    global $conn;
    
    $tradeId = $_GET['trade_id'] ?? '';
    if (empty($tradeId)) {
        outputError('缺少交易ID');
        return;
    }
    
    // 获取交易基本信息
    $sql = "
        SELECT
            pt.*,
            s.name as scene_name,
            ai.username as initiator_name,
            at.username as target_name
        FROM player_trades pt
        LEFT JOIN player_attributes pa_i ON pt.initiator_id = pa_i.id
        LEFT JOIN accounts ai ON pa_i.account_id = ai.id
        LEFT JOIN player_attributes pa_t ON pt.target_id = pa_t.id
        LEFT JOIN accounts at ON pa_t.account_id = at.id
        LEFT JOIN scenes s ON pt.scene_id = s.id
        WHERE pt.id = ?
    ";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute([$tradeId]);
    $trade = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$trade) {
        outputError('交易记录不存在');
        return;
    }
    
    // 获取交易物品
    $itemsSql = "
        SELECT
            ti.*,
            it.name as template_name,
            it.category,
            ti.inventory_id as player_inventory_id
        FROM trade_items ti
        LEFT JOIN item_templates it ON ti.item_template_id = it.id
        WHERE ti.trade_id = ?
        ORDER BY ti.player_id, ti.added_at
    ";
    
    $itemsStmt = $conn->prepare($itemsSql);
    $itemsStmt->execute([$tradeId]);
    $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 分离发起者和目标者的物品
    $initiatorItems = [];
    $targetItems = [];
    
    foreach ($items as $item) {
        if ($item['player_id'] == $trade['initiator_id']) {
            $initiatorItems[] = $item;
        } else {
            $targetItems[] = $item;
        }
    }
    
    // 获取交易货币
    $currencySql = "
        SELECT tc.*
        FROM trade_currencies tc
        WHERE tc.trade_id = ?
        ORDER BY tc.player_id
    ";
    
    $currencyStmt = $conn->prepare($currencySql);
    $currencyStmt->execute([$tradeId]);
    $currencies = $currencyStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 分离发起者和目标者的货币
    $initiatorCurrency = [];
    $targetCurrency = [];
    
    foreach ($currencies as $currency) {
        if ($currency['player_id'] == $trade['initiator_id']) {
            $initiatorCurrency[] = $currency;
        } else {
            $targetCurrency[] = $currency;
        }
    }
    
    $trade['initiator_items'] = $initiatorItems;
    $trade['target_items'] = $targetItems;
    $trade['initiator_currency'] = $initiatorCurrency;
    $trade['target_currency'] = $targetCurrency;
    
    outputSuccess($trade);
}

// 导出交易记录
function exportLog() {
    global $conn;
    
    $tradeId = $_GET['trade_id'] ?? '';
    if (empty($tradeId)) {
        outputError('缺少交易ID');
        return;
    }
    
    // 获取交易详情（复用getLogDetail的逻辑）
    $_GET['trade_id'] = $tradeId;
    ob_start();
    getLogDetail();
    $output = ob_get_clean();
    
    $data = json_decode($output, true);
    if (!$data['success']) {
        outputError('获取交易详情失败');
        return;
    }
    
    $trade = $data['data'];
    
    // 生成CSV内容
    $csv = "交易记录导出\n";
    $csv .= "导出时间," . date('Y-m-d H:i:s') . "\n\n";
    $csv .= "基本信息\n";
    $csv .= "交易ID," . $trade['id'] . "\n";
    $csv .= "状态," . $trade['status'] . "\n";
    $csv .= "场景," . $trade['scene_name'] . "\n";
    $csv .= "发起者," . $trade['initiator_name'] . "\n";
    $csv .= "目标者," . $trade['target_name'] . "\n";
    $csv .= "创建时间," . $trade['created_at'] . "\n";
    $csv .= "完成时间," . ($trade['completed_at'] ?: '未完成') . "\n\n";
    
    $csv .= "发起者物品\n";
    $csv .= "物品名称,数量,分类,玩家物品ID,模板ID\n";
    foreach ($trade['initiator_items'] as $item) {
        $csv .= $item['item_name'] . "," . $item['quantity'] . "," . $item['category'] . "," . $item['inventory_id'] . "," . $item['item_template_id'] . "\n";
    }

    $csv .= "\n目标者物品\n";
    $csv .= "物品名称,数量,分类,玩家物品ID,模板ID\n";
    foreach ($trade['target_items'] as $item) {
        $csv .= $item['item_name'] . "," . $item['quantity'] . "," . $item['category'] . "," . $item['inventory_id'] . "," . $item['item_template_id'] . "\n";
    }
    
    $csv .= "\n发起者货币\n";
    $csv .= "货币类型,数量\n";
    foreach ($trade['initiator_currency'] as $currency) {
        if ($currency['gold_amount'] > 0) {
            $csv .= "金币," . $currency['gold_amount'] . "\n";
        }
        if ($currency['diamond_amount'] > 0) {
            $csv .= "钻石," . $currency['diamond_amount'] . "\n";
        }
    }

    $csv .= "\n目标者货币\n";
    $csv .= "货币类型,数量\n";
    foreach ($trade['target_currency'] as $currency) {
        if ($currency['gold_amount'] > 0) {
            $csv .= "金币," . $currency['gold_amount'] . "\n";
        }
        if ($currency['diamond_amount'] > 0) {
            $csv .= "钻石," . $currency['diamond_amount'] . "\n";
        }
    }
    
    // 设置下载头
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="trade_record_' . $tradeId . '_' . date('Y-m-d_H-i-s') . '.csv"');
    header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
    header('Pragma: public');
    
    // 输出BOM以支持中文
    echo "\xEF\xBB\xBF";
    echo $csv;
    exit;
}

// 获取场景列表
function getScenes() {
    global $conn;
    
    $sql = "SELECT id, name FROM scenes ORDER BY name";
    $stmt = $conn->prepare($sql);
    $stmt->execute();
    $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    outputSuccess($scenes);
}

// 清理交易记录
function clearLogs() {
    global $conn;
    
    $startDate = $_POST['start_date'] ?? '';
    $endDate = $_POST['end_date'] ?? '';
    
    if (empty($startDate) || empty($endDate)) {
        outputError('请提供完整的日期范围');
        return;
    }
    
    try {
        $conn->beginTransaction();
        
        // 获取要删除的交易ID列表
        $getTradesSql = "
            SELECT id FROM player_trades 
            WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?
        ";
        $getTradesStmt = $conn->prepare($getTradesSql);
        $getTradesStmt->execute([$startDate, $endDate]);
        $tradeIds = $getTradesStmt->fetchAll(PDO::FETCH_COLUMN);
        
        if (empty($tradeIds)) {
            $conn->rollback();
            outputSuccess(['deleted_count' => 0]);
            return;
        }
        
        $placeholders = str_repeat('?,', count($tradeIds) - 1) . '?';
        
        // 删除交易物品
        $deleteItemsSql = "DELETE FROM trade_items WHERE trade_id IN ($placeholders)";
        $deleteItemsStmt = $conn->prepare($deleteItemsSql);
        $deleteItemsStmt->execute($tradeIds);
        
        // 删除交易货币
        $deleteCurrencySql = "DELETE FROM trade_currencies WHERE trade_id IN ($placeholders)";
        $deleteCurrencyStmt = $conn->prepare($deleteCurrencySql);
        $deleteCurrencyStmt->execute($tradeIds);
        
        // 删除交易记录
        $deleteTradesSql = "
            DELETE FROM player_trades 
            WHERE DATE(created_at) >= ? AND DATE(created_at) <= ?
        ";
        $deleteTradesStmt = $conn->prepare($deleteTradesSql);
        $deleteTradesStmt->execute([$startDate, $endDate]);
        
        $deletedCount = $deleteTradesStmt->rowCount();
        
        $conn->commit();
        
        outputSuccess(['deleted_count' => $deletedCount]);
        
    } catch (Exception $e) {
        $conn->rollback();
        outputError('清理操作失败: ' . $e->getMessage());
    }
}

// 输出成功响应
function outputSuccess($data = null) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => true,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 输出错误响应
function outputError($message) {
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'success' => false,
        'message' => $message
    ], JSON_UNESCAPED_UNICODE);
    exit;
}
?>
