// 全局状态变量
let currentPage = 1;
let currentSearch = '';
let currentCategory = 'All';
let currentSlot = 'All';
let currentEquipmentType = 'All';
let attributeNameMap = {};

// 确保DOM完全加载后再执行
function initializePage() {
    // 检查关键元素是否存在
    const slotFilters = document.getElementById('slot-filters');
    const categoryFilters = document.querySelector('.category-filters');

    if (!slotFilters || !categoryFilters) {
        console.log('等待DOM元素加载...');
        setTimeout(initializePage, 100);
        return;
    }

    console.log('DOM元素已加载，开始初始化页面');

    // 初始化页面：获取第一页数据、设置事件监听
    fetchItems(1, '', 'All', 'All', 'All');
    setupEventListeners();

    // 创建一个从属性键到中文名的映射，方便后续使用
    if (typeof ATTRIBUTE_DEFINITIONS !== 'undefined') {
        ATTRIBUTE_DEFINITIONS.forEach(attr => {
            attributeNameMap[attr.key] = attr.name;
        });
    }

    // 这两个函数也需要在页面加载时运行，以填充模态框中的下拉列表
    populateAttributeSelectors();
    populateJobSelectors();
    populateSkillHelper();
}

// 在DOM加载完成后执行
document.addEventListener('DOMContentLoaded', initializePage);

/**
 * 设置页面上的主要事件监听器
 */
function setupEventListeners() {
    console.log('设置事件监听器...');

    // 搜索按钮
    const searchButton = document.querySelector('.search-button');
    if (searchButton) {
        searchButton.addEventListener('click', () => {
            const searchTerm = document.querySelector('.search-input').value;
            fetchItems(1, searchTerm, currentCategory, currentSlot, currentEquipmentType);
        });
    }

    // 搜索输入框（支持回车搜索）
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
        searchInput.addEventListener('keyup', (event) => {
            if (event.key === 'Enter') {
                fetchItems(1, searchInput.value, currentCategory, currentSlot, currentEquipmentType);
            }
        });
    }

    // 分类筛选按钮
    const categoryButtons = document.querySelectorAll('.category-filter-btn');
    categoryButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 移除其他按钮的 active 状态
            categoryButtons.forEach(btn => btn.classList.remove('active'));
            // 为当前点击的按钮添加 active 状态
            button.classList.add('active');

            const category = button.dataset.category;
            currentCategory = category;

            // 显示或隐藏装备部位筛选
            const slotFilters = document.getElementById('slot-filters');
            const equipmentTypeFilters = document.getElementById('equipment-type-filters');

            if (slotFilters && equipmentTypeFilters) {
                if (category === 'Equipment') {
                    slotFilters.style.display = 'flex';
                    equipmentTypeFilters.style.display = 'flex';
                } else {
                    slotFilters.style.display = 'none';
                    equipmentTypeFilters.style.display = 'none';

                    // 重置装备部位筛选
                    currentSlot = 'All';
                    const slotButtons = document.querySelectorAll('.slot-filter-btn');
                    slotButtons.forEach(btn => btn.classList.remove('active'));
                    const allSlotButton = document.querySelector('.slot-filter-btn[data-slot="All"]');
                    if (allSlotButton) {
                        allSlotButton.classList.add('active');
                    }

                    // 重置装备类型筛选
                    currentEquipmentType = 'All';
                    const equipmentTypeButtons = document.querySelectorAll('.equipment-type-filter-btn');
                    equipmentTypeButtons.forEach(btn => btn.classList.remove('active'));
                    const allEquipmentTypeButton = document.querySelector('.equipment-type-filter-btn[data-equipment-type="All"]');
                    if (allEquipmentTypeButton) {
                        allEquipmentTypeButton.classList.add('active');
                    }
                }
            } else {
                console.warn('slot-filters or equipment-type-filters element not found when trying to toggle visibility');
            }

            fetchItems(1, currentSearch, category, currentSlot, currentEquipmentType);
        });
    });

    // 装备部位筛选按钮
    const slotButtons = document.querySelectorAll('.slot-filter-btn');
    slotButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 移除其他按钮的 active 状态
            slotButtons.forEach(btn => btn.classList.remove('active'));
            // 为当前点击的按钮添加 active 状态
            button.classList.add('active');

            const slot = button.dataset.slot;
            currentSlot = slot;

            fetchItems(1, currentSearch, currentCategory, slot, currentEquipmentType);
        });
    });

    // 装备类型筛选按钮
    const equipmentTypeButtons = document.querySelectorAll('.equipment-type-filter-btn');
    equipmentTypeButtons.forEach(button => {
        button.addEventListener('click', () => {
            // 移除其他按钮的 active 状态
            equipmentTypeButtons.forEach(btn => btn.classList.remove('active'));
            // 为当前点击的按钮添加 active 状态
            button.classList.add('active');

            const equipmentType = button.dataset.equipmentType;
            currentEquipmentType = equipmentType;

            fetchItems(1, currentSearch, currentCategory, currentSlot, equipmentType);
        });
    });

    // 创建新物品按钮
    const createButton = document.getElementById('add-new-item-btn');
    if(createButton) {
        createButton.addEventListener('click', showCreateModal);
    }

    // 关闭模态框的按钮
    const closeButton = document.querySelector('.modal .close');
    if (closeButton) {
        closeButton.addEventListener('click', hideItemModal);
    }
     window.addEventListener('click', (event) => {
        const modal = document.getElementById('itemModal');
        if (event.target == modal) {
            hideItemModal();
        }
    });

    // 技能书助手按钮
    document.body.addEventListener('click', function(event) {
        if (event.target.id === 'insert-skill-btn') {
            const skillSelect = document.getElementById('skill-helper-select');
            const effectsTextarea = document.getElementById('item-effects');
            if (skillSelect && effectsTextarea && skillSelect.value) {
                const skillId = skillSelect.value;
                const effectsJson = JSON.stringify({ "learn_skill_id": parseInt(skillId, 10) }, null, 4);
                effectsTextarea.value = effectsJson;
            } else {
                showToast('请先选择一个技能', 'error');
            }
        }
    });
}

/**
 * 从服务器获取物品数据
 * @param {number} page      - 要获取的页码
 * @param {string} searchTerm- 搜索关键词
 * @param {string} category  - 物品分类
 * @param {string} slot      - 装备部位（可选）
 * @param {string} equipmentType - 装备类型（可选）
 */
function fetchItems(page, searchTerm, category, slot = 'All', equipmentType = 'All') {
    currentPage = page;
    currentSearch = searchTerm;
    currentCategory = category; // 更新当前分类
    currentSlot = slot; // 更新当前装备部位
    currentEquipmentType = equipmentType; // 更新当前装备类型

    let url = `api_items.php?action=list&page=${page}&search=${encodeURIComponent(searchTerm)}&category=${encodeURIComponent(category)}`;
    if (slot && slot !== 'All') {
        url += `&slot=${encodeURIComponent(slot)}`;
    }
    if (equipmentType && equipmentType !== 'All') {
        url += `&equipment_type=${encodeURIComponent(equipmentType)}`;
    }
    
    fetch(url)
        .then(response => {
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            return response.json();
        })
        .then(data => {
            if (data.success) {
                renderItems(data.data);
                renderPagination(data.pagination);
            } else {
                showToast(data.message || '无法加载物品列表', 'error');
                document.querySelector('.items-grid').innerHTML = `<p>${data.message || '加载物品失败。'}</p>`;
                document.querySelector('.pagination').innerHTML = '';
            }
        })
        .catch(error => {
            console.error('获取物品列表失败:', error);
            showToast('获取物品列表时发生网络或服务器错误', 'error');
            document.querySelector('.items-grid').innerHTML = `<p>加载物品时出错，请检查网络连接或联系管理员。</p>`;
        });
}

/**
 * 渲染物品列表
 * @param {Array} items - 物品数据数组
 */
function renderItems(items) {
    const itemsGrid = document.querySelector('.items-grid');
    itemsGrid.innerHTML = '';
    if (!items || items.length === 0) {
        itemsGrid.innerHTML = '<p>没有找到符合条件的物品。</p>';
        return;
    }

    items.forEach(item => {
        const card = document.createElement('div');
        card.className = 'item-card';

        // 解析并格式化属性和效果
        const attributesHtml = formatItemAttributes(item.stats, item.effects);

        // 构建装备职业信息
        const equipmentJobsHtml = formatEquipmentJobs(item);

        // 构建装备类型标识
        const equipmentTypeHtml = formatEquipmentType(item);

        // 使用JSON.stringify时要小心XSS，但这里item是对象，onclick属性值会由浏览器正确处理
        card.innerHTML = `
            <div class="item-card-header">
                <div class="item-name-section">
                    <strong class="item-name">${item.name}</strong>
                    ${equipmentTypeHtml}
                </div>
                <span class="item-id">#${item.item_id}</span>
            </div>
            <div class="item-card-body">
                <p class="item-description">${item.description || '没有描述'}</p>
                <div class="item-details">
                    <span>分类: ${item.category_name || item.category}</span>
                    <span>堆叠: ${item.stackable ? `是 (最大 ${item.max_stack})` : '否'}</span>
                </div>
                ${equipmentJobsHtml ? `<div class="item-jobs">${equipmentJobsHtml}</div>` : ''}
                <div class="item-prices">
                    ${item.buy_price ? `<span class="price-gold">💰 ${item.buy_price} 金币</span>` : ''}
                    ${item.diamond_price ? `<span class="price-diamond">💎 ${item.diamond_price} 钻石</span>` : ''}
                    ${item.sell_price ? `<span class="price-sell">🔄 ${item.sell_price} 金币</span>` : ''}
                </div>
                ${attributesHtml ? `<div class="item-attributes">${attributesHtml}</div>` : ''}
            </div>
            <div class="item-card-footer">
                <button class="btn btn-sm btn-primary" onclick='showEditModal(${JSON.stringify(item)})'>编辑</button>
                <button class="btn btn-sm btn-danger" onclick="deleteItem(${item.id}, '${item.name.replace(/'/g, "\\'")}')">删除</button>
            </div>
        `;
        itemsGrid.appendChild(card);
    });
}

/**
 * 渲染分页控件
 * @param {object} pagination - 分页数据 { current_page, total_pages }
 */
function renderPagination(pagination) {
    const paginationContainer = document.querySelector('.pagination');
    paginationContainer.innerHTML = '';
    if (!pagination || pagination.total_pages <= 1) return;

    const { current_page, total_pages } = pagination;

    let paginationHtml = '';

    // 上一页按钮
    if (current_page > 1) {
        paginationHtml += `<button onclick="fetchItems(${current_page - 1}, currentSearch, currentCategory, currentSlot)">« 上一页</button>`;
    } else {
        paginationHtml += `<button disabled>« 上一页</button>`;
    }

    // 页码
    // Logic to show limited page numbers
    const maxPagesToShow = 5;
    let startPage = Math.max(1, current_page - Math.floor(maxPagesToShow / 2));
    let endPage = Math.min(total_pages, startPage + maxPagesToShow - 1);
    if (endPage - startPage + 1 < maxPagesToShow) {
        startPage = Math.max(1, endPage - maxPagesToShow + 1);
    }

    if (startPage > 1) {
        paginationHtml += `<button onclick="fetchItems(1, currentSearch, currentCategory, currentSlot)">1</button>`;
        if (startPage > 2) {
            paginationHtml += `<span>...</span>`;
        }
    }

    for (let i = startPage; i <= endPage; i++) {
        paginationHtml += `<button class="${i === current_page ? 'active' : ''}" onclick="fetchItems(${i}, currentSearch, currentCategory, currentSlot)">${i}</button>`;
    }

    if (endPage < total_pages) {
         if (endPage < total_pages -1) {
            paginationHtml += `<span>...</span>`;
        }
        paginationHtml += `<button onclick="fetchItems(${total_pages}, currentSearch, currentCategory, currentSlot)">${total_pages}</button>`;
    }


    // 下一页按钮
    if (current_page < total_pages) {
        paginationHtml += `<button onclick="fetchItems(${current_page + 1}, currentSearch, currentCategory, currentSlot)">下一页 »</button>`;
    } else {
        paginationHtml += `<button disabled>下一页 »</button>`;
    }

    paginationContainer.innerHTML = paginationHtml;
}

/**
 * 设置分类筛选按钮
 */
/*
function setupCategoryFilters() {
    const categories = ['All', 'Equipment', 'Gem', 'Material', 'Potion', 'Rune', 'Misc', 'Scroll'];
    const categoryMap = { 'All': '全部', 'Equipment': '装备', 'Gem': '宝石', 'Material': '材料', 'Potion': '药品', 'Rune': '符石', 'Misc': '杂物', 'Scroll': '书卷' };
    const filtersContainer = document.querySelector('.category-filters');
    if (!filtersContainer) return;
    filtersContainer.innerHTML = '';
    
    categories.forEach(category => {
        const button = document.createElement('button');
        button.classList.add('btn', 'btn-sm', 'btn-outline-secondary');
        button.dataset.category = category;
        button.textContent = categoryMap[category] || category;
        if (category === 'All') {
            button.classList.add('active');
        }
        button.addEventListener('click', () => {
            document.querySelectorAll('.category-filters .btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            
            const searchTerm = document.querySelector('.search-input').value;
            fetchItems(1, searchTerm, category);
        });
        filtersContainer.appendChild(button);
    });
}
*/

/**
 * 显示创建物品的模态框
 */
function showCreateModal() {
    const modal = document.getElementById('itemModal');
    const form = document.getElementById('item-form');
    document.getElementById('modalTitle').textContent = '添加新物品';
    form.reset(); // 清空表单
    form.innerHTML = FORM_FIELDS_HTML; // 重新注入干净的表单

    document.getElementById('item-id').value = '';
    const itemIdInput = document.getElementById('item-item_id');
    if (itemIdInput) {
        itemIdInput.placeholder = '(自动生成)';
        itemIdInput.readOnly = true;
    }
    
    form.onsubmit = (event) => handleFormSubmit(event, 'create');
    setupFormEventListeners();
    populateAttributeSelectors(); // 确保属性选择器被填充
    populateJobSelectors();     // 确保职业选择器被填充
    populateSkillHelper(); // 填充技能助手
    modal.style.display = 'flex';
}

/**
 * 显示编辑物品的模态框
 * @param {object} item - 要编辑的物品对象
 */
function showEditModal(item) {
    const modal = document.getElementById('itemModal');
    const form = document.getElementById('item-form');
    document.getElementById('modalTitle').textContent = '编辑物品';
    form.innerHTML = FORM_FIELDS_HTML;
    
    populateForm(item);
    form.onsubmit = (event) => handleFormSubmit(event, 'update');
    
    // 确保在表单填充后，再执行这些依赖于表单元素的操作
    setupFormEventListeners();
    populateAttributeSelectors(); 
    populateJobSelectors();     
    populateSkillHelper();

    modal.style.display = 'flex';
}

/**
 * 隐藏物品模态框
 */
function hideItemModal() {
    const modal = document.getElementById('itemModal');
    if(modal) modal.style.display = 'none';
}

/**
 * 使用物品数据填充表单
 * @param {object} data - 物品数据
 */
function populateForm(data) {
    document.getElementById('item-id').value = data.id || '';
    document.getElementById('item-name').value = data.name || '';
    document.getElementById('item-item_id').value = data.item_id || '';
    document.getElementById('item-item_id').readOnly = true;
    document.getElementById('item-category').value = data.category || 'Misc';
    document.getElementById('item-is_consumable').value = data.is_consumable == 1 ? '1' : '0';
    document.getElementById('item-stackable').value = data.stackable == 1 ? '1' : '0';
    document.getElementById('item-max_stack').value = data.max_stack || '1';
    document.getElementById('item-buy_price').value = data.buy_price || '';
    document.getElementById('item-diamond_price').value = data.diamond_price || '';
    document.getElementById('item-sell_price').value = data.sell_price || '';
    document.getElementById('item-description').value = data.description || '';
    // effects 和 stats 是JSON，此处仅作为文本显示
    document.getElementById('item-effects').value = data.effects || '{}';
    document.getElementById('item-stats').value = data.stats || '{}';

    // 处理装备特定字段
    toggleEquipmentFields(data.category);
    if (data.category === 'Equipment') {
        document.getElementById('item-slot').value = data.slot || 'Head';
        document.getElementById('item-job_restriction').value = data.job_restriction || 'None';
        document.getElementById('item-sockets').value = data.sockets || '0';
        document.getElementById('item-grants_job_id').value = data.grants_job_id || '';
        document.getElementById('item-equipment_type').value = data.equipment_type || 'player';
    }
}


/**
 * 处理表单提交（创建和更新）
 * @param {Event} event - 表单提交事件
 * @param {string} action - 'create' 或 'update'
 */
async function handleFormSubmit(event, action) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    
    // 检查JSON的有效性
    try {
        JSON.parse(formData.get('effects') || '{}');
        JSON.parse(formData.get('stats') || '{}');
    } catch (e) {
        showToast('错误: 效果或属性的JSON格式无效。', 'error');
        return;
    }

    const url = `api_items.php?action=${action}`;

    try {
        const response = await fetch(url, {
            method: 'POST',
            body: formData
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`服务器响应错误: ${response.status} ${errorText}`);
        }

        const result = await response.json();

        if (result.success) {
            showToast(`物品已成功${action === 'create' ? '创建' : '更新'}！`);
            hideItemModal();
            // 创建成功后跳转到第一页查看，更新则留在当前页
            const pageToFetch = action === 'create' ? 1 : currentPage;
            fetchItems(pageToFetch, currentSearch, currentCategory, currentSlot);
        } else {
            showToast(result.message || '操作失败', 'error');
        }
    } catch (error) {
        console.error('表单提交失败:', error);
        showToast('发生网络或服务器错误', 'error');
    }
}

/**
 * 删除物品
 * @param {number} itemId - 物品的数据库ID
 * @param {string} itemName - 物品名称
 */
function deleteItem(itemId, itemName) {
    if (confirm(`确定要删除这个物品吗？此操作不可逆！\n物品名称: ${itemName}`)) {
        const formData = new FormData();
        formData.append('id', itemId);

        fetch(`api_items.php?action=delete`, { 
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast('物品删除成功！');
                // 刷新当前页
                fetchItems(currentPage, currentSearch, currentCategory, currentSlot);
            } else {
                showToast('删除失败：' + (data.message || '未知错误'), 'error');
            }
        })
        .catch(error => {
            console.error('删除物品失败:', error);
            showToast('删除物品时发生网络或服务器错误', 'error');
        });
    }
}

/**
 * 设置表单内的事件监听器，例如分类选择变化时
 */
function setupFormEventListeners() {
    const categorySelect = document.getElementById('item-category');
    if (categorySelect) {
        categorySelect.addEventListener('change', (e) => toggleEquipmentFields(e.target.value));
    }
    
    // JSON构建器的事件监听
    document.getElementById('add-effect-btn').addEventListener('click', () => {
        const key = document.getElementById('effect-key-select').value;
        const value = document.getElementById('effect-value-input').value;
        updateJsonTextarea('item-effects', key, value);
    });

    document.getElementById('clear-effects-btn').addEventListener('click', () => {
        document.getElementById('item-effects').value = '{}';
    });

    document.getElementById('add-stat-btn').addEventListener('click', () => {
        const key = document.getElementById('stat-key-select').value;
        const value = document.getElementById('stat-value-input').value;
        updateJsonTextarea('item-stats', key, value);
    });

    document.getElementById('clear-stats-btn').addEventListener('click', () => {
        document.getElementById('item-stats').value = '{}';
    });
}

/**
 * 根据所选分类显示或隐藏装备特定字段
 * @param {string} category - 所选分类
 */
function toggleEquipmentFields(category) {
    const equipmentFields = document.getElementById('equipment_fields');
    const itemEffectsGroup = document.getElementById('item-effects-group');
    const itemStatsGroup = document.getElementById('item-stats-group');
    const equipmentTypeGroup = document.getElementById('equipment-type-group');

    if (equipmentFields && itemEffectsGroup && itemStatsGroup) {
        const isEquipment = category === 'Equipment';

        // 装备专属字段
        equipmentFields.style.display = isEquipment ? 'block' : 'none';

        // 装备类型字段
        if (equipmentTypeGroup) {
            equipmentTypeGroup.style.display = isEquipment ? 'block' : 'none';
        }

        // 属性用于装备，效果用于非装备
        itemStatsGroup.style.display = isEquipment ? 'block' : 'none';
        itemEffectsGroup.style.display = isEquipment ? 'none' : 'block';
    }
}

/**
 * 向表单中添加一行属性输入
 * @param {string} attribute - (可选) 预设的属性
 * @param {number} value - (可选) 预设的值
 */
function addStatToForm(attribute = '', value = '') {
    const container = document.getElementById('stats-container');
    const entry = document.createElement('div');
    entry.className = 'stat-entry';
    
    const attributeSelectId = `attribute-${Date.now()}`;
    entry.innerHTML = `
        <select id="${attributeSelectId}" class="form-control">
            <option value="">--选择属性--</option>
        </select>
        <input type="number" class="form-control" value="${value}" placeholder="值">
        <button type="button" class="btn btn-danger btn-sm">移除</button>
    `;

    container.appendChild(entry);
    populateAttributeSelector(attributeSelectId, attribute);

    entry.querySelector('.btn-danger').addEventListener('click', () => {
        entry.remove();
    });
}


// Toast通知功能 - 使用全局toast系统
function showToast(message, type = 'success') {
    // 使用全局toast系统，如果不存在则回退到本地实现
    if (typeof window.globalShowToast === 'function') {
        return window.globalShowToast(message, type);
    }

    // 回退实现（保持兼容性）
    const toast = document.getElementById('toast');
    if (!toast) {
        console.error('Toast element not found!');
        return;
    }
    toast.textContent = message;
    toast.className = `toast show ${type}`;
    setTimeout(() => {
        toast.classList.remove('show');
    }, 3000);
}

// 动态填充属性下拉列表的逻辑
let attributeOptionsCache = null;

async function getAttributeOptions() {
    if (attributeOptionsCache) return attributeOptionsCache;
    // 使用从PHP传入的 ATTRIBUTE_DEFINITIONS
    attributeOptionsCache = ATTRIBUTE_DEFINITIONS;
    return attributeOptionsCache;
}

async function populateAttributeSelectors() {
    const attributes = await getAttributeOptions();
    const effectSelect = document.getElementById('effect-key-select');
    const statSelect = document.getElementById('stat-key-select');

    if (!effectSelect || !statSelect) return;

    // 清空现有选项
    effectSelect.innerHTML = '<option value="">-- 选择效果 --</option>';
    statSelect.innerHTML = '<option value="">-- 选择属性 --</option>';

    attributes.forEach(attr => {
        const option = document.createElement('option');
        option.value = attr.key;
        option.textContent = attr.name;
        
        // 将所有属性都添加到"效果"和"属性"两个下拉菜单中
        effectSelect.appendChild(option.cloneNode(true));
        statSelect.appendChild(option.cloneNode(true));
    });
}

// 动态填充职业下拉列表的逻辑
let jobOptionsCache = null;
async function getJobOptions() {
    if (jobOptionsCache) return jobOptionsCache;
    try {
        const response = await fetch('api_jobs.php?action=list');
        if (!response.ok) throw new Error('Failed to fetch jobs');
        const data = await response.json();
        if (data.success) {
            jobOptionsCache = data.jobs;
            return jobOptionsCache;
        }
    } catch (error) {
        console.error('Error fetching jobs:', error);
        return [];
    }
}
async function populateJobSelectors() {
    const jobs = await getJobOptions();
    const jobRestrictionSelect = document.getElementById('item-job_restriction');
    const grantsJobSelect = document.getElementById('item-grants_job_id');

    if (jobRestrictionSelect && jobs) {
        const currentRestrictionValue = jobRestrictionSelect.value;
        jobRestrictionSelect.innerHTML = '<option value="">无</option>';
        jobs.forEach(job => {
            if (job.id == 1) return; // Skip "无业"
            const option = document.createElement('option');
            option.value = job.id;
            option.textContent = job.name;
            jobRestrictionSelect.appendChild(option);
        });
        jobRestrictionSelect.value = currentRestrictionValue;
    }

    if (grantsJobSelect && jobs) {
        const currentGrantValue = grantsJobSelect.value;
        grantsJobSelect.innerHTML = '<option value="">无</option>';
        jobs.forEach(job => {
            if (job.id == 1) return; // Skip "无业"
            const option = document.createElement('option');
            option.value = job.id;
            option.textContent = job.name;
            grantsJobSelect.appendChild(option);
        });
        grantsJobSelect.value = currentGrantValue;
    }
}

/**
 * 这是一个辅助函数，用于向JSON文本区添加或更新键值对
 * @param {string} textareaId - a text area's id
 * @param {string} key - The key to add/update
 * @param {number} value - The value for the key
 */
function updateJsonTextarea(textareaId, key, value) {
    const textarea = document.getElementById(textareaId);
    try {
        let currentJson = textarea.value ? JSON.parse(textarea.value) : {};
        currentJson[key] = value;
        textarea.value = JSON.stringify(currentJson, null, 4); // 格式化JSON
    } catch (e) {
        // 如果现有内容不是有效的JSON，则创建一个新的
        let newJson = {};
        newJson[key] = value;
        textarea.value = JSON.stringify(newJson, null, 4);
    }
}

/**
 * 新增：填充技能助手下拉列表
 */
function populateSkillHelper() {
    const select = document.getElementById('skill-helper-select');
    if (!select || typeof ALL_SKILLS === 'undefined') {
        // 如果选择框不存在，或技能数据未定义，则直接返回
        return;
    }

    // 先清空除了第一个之外的所有选项，防止重复添加
    while (select.options.length > 1) {
        select.remove(1);
    }
    
    ALL_SKILLS.forEach(skill => {
        const option = document.createElement('option');
        option.value = skill.id;
        option.textContent = `${skill.name} (ID: ${skill.id})`;
        select.appendChild(option);
    });
}

/**
 * 格式化物品的属性和效果，用于在卡片上显示
 * @param {string} statsJson - 装备属性的JSON字符串
 * @param {string} effectsJson - 消耗品效果的JSON字符串
 * @returns {string} - 格式化后的HTML字符串
 */
function formatItemAttributes(statsJson, effectsJson) {
    let html = '';
    const stats = statsJson ? JSON.parse(statsJson) : {};
    const effects = effectsJson ? JSON.parse(effectsJson) : {};

    const formatLine = (label, data) => {
        let lines = [];
        for (const [key, value] of Object.entries(data)) {
            if (value !== 0) {
                const name = attributeNameMap[key] || key;
                const sign = value > 0 ? '+' : '';
                lines.push(`<span>${name} ${sign}${value}</span>`);
            }
        }
        if (lines.length > 0) {
            return `<div class="attribute-group"><strong>${label}:</strong> ${lines.join('')}</div>`;
        }
        return '';
    };
    
    html += formatLine('属性', stats);
    html += formatLine('效果', effects);

    return html;
}

/**
 * 格式化装备类型标识
 * @param {Object} item - 物品数据
 * @returns {string} - 格式化后的HTML字符串
 */
function formatEquipmentType(item) {
    if (item.category !== 'Equipment') return '';

    const equipmentType = item.equipment_type || 'player';

    if (equipmentType === 'monster') {
        return '<span class="equipment-type-badge monster-equipment">[怪物]</span>';
    } else {
        return '<span class="equipment-type-badge player-equipment">[玩家]</span>';
    }
}

/**
 * 格式化装备的职业信息，用于在卡片上显示
 * @param {Object} item - 物品对象
 * @returns {string} - 格式化后的HTML字符串
 */
function formatEquipmentJobs(item) {
    let html = '';

    // 只有装备类型才显示职业信息
    if (item.category !== 'Equipment') {
        return html;
    }

    const jobInfos = [];

    // 授予职业
    if (item.granted_job_name) {
        jobInfos.push(`<span class="job-grant">授予: ${item.granted_job_name}</span>`);
    }

    // 职业限制
    if (item.job_restriction_name) {
        jobInfos.push(`<span class="job-restriction">限制: ${item.job_restriction_name}</span>`);
    }

    // 装备部位
    if (item.slot) {
        const slotMap = {
            'Head': '头部', 'Neck': '颈部', 'LeftHand': '左手', 'RightHand': '右手',
            'TwoHanded': '双手', 'Body': '身体', 'Finger': '手指', 'Back': '背部'
        };
        const slotName = slotMap[item.slot] || item.slot;
        jobInfos.push(`<span class="equipment-slot">部位: ${slotName}</span>`);
    }

    // 插槽信息
    if (item.sockets && item.sockets > 0) {
        jobInfos.push(`<span class="equipment-sockets">插槽: ${item.sockets}个</span>`);
    }

    if (jobInfos.length > 0) {
        html = jobInfos.join('');
    }

    return html;
}