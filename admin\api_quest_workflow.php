<?php
// admin/api_quest_workflow.php
session_start();

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 引入数据库配置
require_once '../config/Database.php';

try {
    $database = Database::getInstance();
    $db = $database->getConnection();
    
    // 获取action参数，支持GET和POST请求
    $action = $_GET['action'] ?? '';

    // 如果是POST请求且没有GET参数，尝试从请求体获取
    if (empty($action) && $_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
    }
    
    switch ($action) {
        case 'get_scenes':
            echo json_encode(getScenes($db), JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_npcs':
            echo json_encode(getNpcs($db), JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_quests':
            echo json_encode(getQuests($db), JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_dialogues':
            echo json_encode(getDialogues($db), JSON_UNESCAPED_UNICODE);
            break;
            
        case 'get_quest_groups':
            echo json_encode(getQuestGroups($db), JSON_UNESCAPED_UNICODE);
            break;

        case 'get_dialogue_nodes':
            echo json_encode(getDialogueNodes($db), JSON_UNESCAPED_UNICODE);
            break;

        case 'get_items':
            // 检查是否使用测试数据
            if (isset($_GET['test']) && $_GET['test'] === '1') {
                echo json_encode(getTestData('get_items'), JSON_UNESCAPED_UNICODE);
            } else {
                echo json_encode(getItems($db), JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'get_dialogue_tree_detail':
            $treeId = $_GET['tree_id'] ?? null;
            if ($treeId) {
                echo json_encode(getDialogueTreeDetail($db, $treeId), JSON_UNESCAPED_UNICODE);
            } else {
                http_response_code(400);
                echo json_encode(['error' => '缺少对话树ID参数'], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'update_dialogue_tree':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);

                if (!isset($input['dialogue_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => '缺少对话树ID参数'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $dialogue_id = (int)$input['dialogue_id'];
                $name = $input['name'] ?? '';
                $description = $input['description'] ?? '';

                if (empty($name)) {
                    http_response_code(400);
                    echo json_encode(['error' => '对话树名称不能为空'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $stmt = $db->prepare("
                    UPDATE dialogue_trees
                    SET name = :name, description = :description, updated_at = NOW()
                    WHERE id = :id
                ");

                $result = $stmt->execute([
                    ':name' => $name,
                    ':description' => $description,
                    ':id' => $dialogue_id
                ]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '对话树更新成功'], JSON_UNESCAPED_UNICODE);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => '对话树更新失败'], JSON_UNESCAPED_UNICODE);
                }
            } else {
                http_response_code(405);
                echo json_encode(['error' => '不支持的请求方法'], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'update_dialogue_node':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);

                if (!isset($input['node_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => '缺少节点ID参数'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $node_id = (int)$input['node_id'];
                $content = $input['content'] ?? '';
                $is_player_choice = isset($input['is_player_choice']) ? (int)$input['is_player_choice'] : 0;
                $next_node_ids = $input['next_node_ids'] ?? '[]';
                $condition_script = $input['condition_script'] ?? null;
                $action_script = $input['action_script'] ?? null;

                if (empty($content)) {
                    http_response_code(400);
                    echo json_encode(['error' => '节点内容不能为空'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $stmt = $db->prepare("
                    UPDATE dialogue_nodes
                    SET content = :content,
                        is_player_choice = :is_player_choice,
                        next_node_ids = :next_node_ids,
                        condition_script = :condition_script,
                        action_script = :action_script,
                        updated_at = NOW()
                    WHERE id = :id
                ");

                $result = $stmt->execute([
                    ':content' => $content,
                    ':is_player_choice' => $is_player_choice,
                    ':next_node_ids' => $next_node_ids,
                    ':condition_script' => $condition_script,
                    ':action_script' => $action_script,
                    ':id' => $node_id
                ]);

                if ($result) {
                    echo json_encode(['success' => true, 'message' => '节点更新成功'], JSON_UNESCAPED_UNICODE);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => '节点更新失败'], JSON_UNESCAPED_UNICODE);
                }
            } else {
                http_response_code(405);
                echo json_encode(['error' => '不支持的请求方法'], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'create_dialogue_node':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);

                if (!isset($input['tree_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => '缺少对话树ID参数'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $tree_id = (int)$input['tree_id'];
                $content = $input['content'] ?? '新建对话节点';
                $is_player_choice = isset($input['is_player_choice']) ? (int)$input['is_player_choice'] : 0;
                $next_node_ids = $input['next_node_ids'] ?? '[]';
                $condition_script = $input['condition_script'] ?? null;
                $action_script = $input['action_script'] ?? null;

                $stmt = $db->prepare("
                    INSERT INTO dialogue_nodes
                    (tree_id, content, is_player_choice, next_node_ids, condition_script, action_script, created_at, updated_at)
                    VALUES (:tree_id, :content, :is_player_choice, :next_node_ids, :condition_script, :action_script, NOW(), NOW())
                ");

                $result = $stmt->execute([
                    ':tree_id' => $tree_id,
                    ':content' => $content,
                    ':is_player_choice' => $is_player_choice,
                    ':next_node_ids' => $next_node_ids,
                    ':condition_script' => $condition_script,
                    ':action_script' => $action_script
                ]);

                if ($result) {
                    $node_id = $db->lastInsertId();

                    // 更新对话树的节点数量
                    $updateStmt = $db->prepare("
                        UPDATE dialogue_trees
                        SET node_count = (SELECT COUNT(*) FROM dialogue_nodes WHERE tree_id = :tree_id),
                            updated_at = NOW()
                        WHERE id = :tree_id
                    ");
                    $updateStmt->execute([':tree_id' => $tree_id]);

                    echo json_encode(['success' => true, 'message' => '节点创建成功', 'node_id' => $node_id], JSON_UNESCAPED_UNICODE);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => '节点创建失败'], JSON_UNESCAPED_UNICODE);
                }
            } else {
                http_response_code(405);
                echo json_encode(['error' => '不支持的请求方法'], JSON_UNESCAPED_UNICODE);
            }
            break;

        case 'delete_dialogue_node':
            if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                $input = json_decode(file_get_contents('php://input'), true);

                if (!isset($input['node_id'])) {
                    http_response_code(400);
                    echo json_encode(['error' => '缺少节点ID参数'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $node_id = (int)$input['node_id'];

                // 先获取节点所属的对话树ID
                $treeStmt = $db->prepare("SELECT tree_id FROM dialogue_nodes WHERE id = :id");
                $treeStmt->execute([':id' => $node_id]);
                $treeData = $treeStmt->fetch(PDO::FETCH_ASSOC);

                if (!$treeData) {
                    http_response_code(404);
                    echo json_encode(['error' => '节点不存在'], JSON_UNESCAPED_UNICODE);
                    break;
                }

                $tree_id = $treeData['tree_id'];

                // 删除节点
                $stmt = $db->prepare("DELETE FROM dialogue_nodes WHERE id = :id");
                $result = $stmt->execute([':id' => $node_id]);

                if ($result) {
                    // 更新对话树的节点数量
                    $updateStmt = $db->prepare("
                        UPDATE dialogue_trees
                        SET node_count = (SELECT COUNT(*) FROM dialogue_nodes WHERE tree_id = :tree_id),
                            updated_at = NOW()
                        WHERE id = :tree_id
                    ");
                    $updateStmt->execute([':tree_id' => $tree_id]);

                    echo json_encode(['success' => true, 'message' => '节点删除成功'], JSON_UNESCAPED_UNICODE);
                } else {
                    http_response_code(500);
                    echo json_encode(['error' => '节点删除失败'], JSON_UNESCAPED_UNICODE);
                }
            } else {
                http_response_code(405);
                echo json_encode(['error' => '不支持的请求方法'], JSON_UNESCAPED_UNICODE);
            }
            break;

        default:
            http_response_code(400);
            echo json_encode(['error' => '无效的操作'], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    // 如果是测试模式，直接返回测试数据
    if (isset($_GET['test']) && $_GET['test'] === '1') {
        echo json_encode(getTestData($action), JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(500);
        echo json_encode(['error' => '服务器错误: ' . $e->getMessage()], JSON_UNESCAPED_UNICODE);
    }
}

function getScenes($db) {
    $query = "SELECT
        s.id,
        s.name,
        s.description,
        s.x,
        s.y,
        s.z,
        s.zone_id,
        s.max_players,
        s.is_safe_zone,
        z.id as zone_id_full,
        z.name as zone_name,
        z.continent as continent_id,
        c.id as continent_id_full,
        c.display_name as continent_name
    FROM scenes s
    LEFT JOIN zones z ON s.zone_id = z.id
    LEFT JOIN continents c ON z.continent = c.id
    ORDER BY c.sort_order, z.min_level, s.name";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getNpcs($db) {
    $query = "SELECT
        nt.id,
        nt.name,
        nt.description,
        nt.level,
        nt.is_merchant,
        nt.is_quest_giver,
        nt.default_dialogue_tree_id,
        ni.scene_id,
        ni.position_desc,
        s.name as scene_name
    FROM npc_templates nt
    LEFT JOIN npc_instances ni ON nt.id = ni.template_id
    LEFT JOIN scenes s ON ni.scene_id = s.id
    WHERE ni.scene_id IS NOT NULL
    ORDER BY nt.name";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getQuests($db) {
    $query = "SELECT
        q.id,
        q.title,
        q.description,
        q.type,
        q.min_level,
        q.is_repeatable,
        q.group_id,
        q.giver_npc_id,
        q.receiver_npc_id,
        q.prerequisite_quests,
        q.reward_gold,
        q.reward_exp,
        q.reward_items,
        giver.name as giver_npc_name,
        receiver.name as receiver_npc_name,
        qg.name as group_name
    FROM quests q
    LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
    LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
    LEFT JOIN quest_groups qg ON q.group_id = qg.id
    ORDER BY q.min_level, q.title";

    $stmt = $db->prepare($query);
    $stmt->execute();
    $quests = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 为每个任务获取目标信息
    foreach ($quests as &$quest) {
        $objectivesQuery = "SELECT
            qo.id,
            qo.quest_id,
            qo.type,
            qo.target_id,
            qo.quantity,
            qo.description,
            CASE
                WHEN qo.type = 'dialogue' OR qo.type = 'visit' THEN npc.name
                WHEN qo.type = 'kill' THEN monster.name
                WHEN qo.type = 'collect' THEN item.name
                ELSE NULL
            END AS target_name
        FROM quest_objectives qo
        LEFT JOIN npc_templates npc ON qo.type IN ('dialogue', 'visit') AND qo.target_id = npc.id
        LEFT JOIN monster_templates monster ON qo.type = 'kill' AND qo.target_id = monster.id
        LEFT JOIN item_templates item ON qo.type = 'collect' AND qo.target_id = item.id
        WHERE qo.quest_id = ?
        ORDER BY qo.id";

        $objectivesStmt = $db->prepare($objectivesQuery);
        $objectivesStmt->execute([$quest['id']]);
        $quest['objectives'] = $objectivesStmt->fetchAll(PDO::FETCH_ASSOC);
    }

    return $quests;
}

function getDialogues($db) {
    $query = "SELECT
        dt.id,
        dt.name,
        dt.description,
        dt.group_id,
        dg.name as group_name,
        dg.type as group_type,
        COUNT(dn.id) as node_count
    FROM dialogue_trees dt
    LEFT JOIN dialogue_nodes dn ON dt.id = dn.dialogue_tree_id
    LEFT JOIN dialogue_groups dg ON dt.group_id = dg.id
    GROUP BY dt.id, dt.name, dt.description, dt.group_id, dg.name, dg.type
    ORDER BY dg.sort_order, dg.name, dt.name";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getQuestGroups($db) {
    $query = "SELECT
        qg.id,
        qg.name,
        qg.description,
        COUNT(q.id) as quest_count
    FROM quest_groups qg
    LEFT JOIN quests q ON qg.id = q.group_id
    GROUP BY qg.id, qg.name, qg.description
    ORDER BY qg.name";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getDialogueNodes($db) {
    $query = "SELECT
        dn.id,
        dn.dialogue_tree_id,
        dn.content,
        dn.next_node_ids,
        dn.condition_script,
        dn.action_script,
        dn.is_player_choice,
        dt.name as tree_name
    FROM dialogue_nodes dn
    LEFT JOIN dialogue_trees dt ON dn.dialogue_tree_id = dt.id
    ORDER BY dn.dialogue_tree_id, dn.id";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getItems($db) {
    $query = "SELECT
        it.id,
        it.name,
        it.description,
        it.category
    FROM item_templates it
    ORDER BY it.category, it.name";

    $stmt = $db->prepare($query);
    $stmt->execute();

    return $stmt->fetchAll(PDO::FETCH_ASSOC);
}

function getDialogueTreeDetail($db, $treeId) {
    // 获取对话树基本信息
    $treeQuery = "SELECT
        dt.id,
        dt.name,
        dt.description,
        COUNT(dn.id) as node_count
    FROM dialogue_trees dt
    LEFT JOIN dialogue_nodes dn ON dt.id = dn.dialogue_tree_id
    WHERE dt.id = :tree_id
    GROUP BY dt.id, dt.name, dt.description";

    $stmt = $db->prepare($treeQuery);
    $stmt->execute([':tree_id' => $treeId]);
    $tree = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$tree) {
        return ['error' => '对话树不存在'];
    }

    // 获取对话节点详细信息
    $nodesQuery = "SELECT
        dn.id,
        dn.content,
        dn.next_node_ids,
        dn.condition_script,
        dn.action_script,
        dn.is_player_choice
    FROM dialogue_nodes dn
    WHERE dn.dialogue_tree_id = :tree_id
    ORDER BY dn.id";

    $stmt = $db->prepare($nodesQuery);
    $stmt->execute([':tree_id' => $treeId]);
    $nodes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取使用此对话树的NPC（包括默认对话树和npc_dialogues关联）
    $npcQuery = "SELECT DISTINCT
        nt.id,
        nt.name,
        ni.scene_id,
        s.name as scene_name,
        CASE
            WHEN nt.default_dialogue_tree_id = ? THEN '默认对话'
            WHEN nd.is_default = 1 THEN '默认对话'
            ELSE '条件对话'
        END as dialogue_type
    FROM npc_templates nt
    LEFT JOIN npc_instances ni ON nt.id = ni.template_id
    LEFT JOIN scenes s ON ni.scene_id = s.id
    LEFT JOIN npc_dialogues nd ON nt.id = nd.npc_template_id AND nd.dialogue_tree_id = ?
    WHERE nt.default_dialogue_tree_id = ?
       OR nd.dialogue_tree_id = ?
    ORDER BY nt.name";

    $stmt = $db->prepare($npcQuery);
    $stmt->execute([$treeId, $treeId, $treeId, $treeId]);
    $npcs = $stmt->fetchAll(PDO::FETCH_ASSOC);

    // 获取关联数据：物品和任务信息
    $relatedData = getRelatedData($db, $nodes);

    return [
        'tree' => $tree,
        'nodes' => $nodes,
        'npcs' => $npcs,
        'items' => $relatedData['items'],
        'quests' => $relatedData['quests']
    ];
}

function getRelatedData($db, $nodes) {
    $itemIds = [];
    $questIds = [];

    // 从节点的条件和动作脚本中提取物品ID和任务ID
    foreach ($nodes as $node) {
        // 解析条件脚本
        if (!empty($node['condition_script'])) {
            $conditionExtracted = extractConditionIds($node['condition_script']);
            $itemIds = array_merge($itemIds, $conditionExtracted['items']);
            $questIds = array_merge($questIds, $conditionExtracted['quests']);
        }

        // 解析动作脚本
        if (!empty($node['action_script'])) {
            $extracted = extractActionIds($node['action_script']);
            $itemIds = array_merge($itemIds, $extracted['items']);
            $questIds = array_merge($questIds, $extracted['quests']);
        }
    }

    // 去重
    $itemIds = array_unique($itemIds);
    $questIds = array_unique($questIds);

    // 获取物品信息
    $items = [];
    if (!empty($itemIds)) {
        $placeholders = str_repeat('?,', count($itemIds) - 1) . '?';
        $stmt = $db->prepare("SELECT id, name, description FROM item_templates WHERE id IN ($placeholders)");
        $stmt->execute($itemIds);
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    // 获取任务信息
    $quests = [];
    if (!empty($questIds)) {
        $placeholders = str_repeat('?,', count($questIds) - 1) . '?';
        $stmt = $db->prepare("SELECT id, title, description FROM quests WHERE id IN ($placeholders)");
        $stmt->execute($questIds);
        $quests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    return [
        'items' => $items,
        'quests' => $quests
    ];
}

function extractConditionIds($conditionScript) {
    $itemIds = [];
    $questIds = [];

    try {
        $conditions = json_decode($conditionScript, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['items' => $itemIds, 'quests' => $questIds];
        }

        // 处理不同的条件格式
        $conditionList = [];
        if (isset($conditions['conditions']) && is_array($conditions['conditions'])) {
            $conditionList = $conditions['conditions'];
        } elseif (is_array($conditions)) {
            $conditionList = $conditions;
        }

        foreach ($conditionList as $condition) {
            if (!isset($condition['type'])) continue;

            switch ($condition['type']) {
                case 'has_item':
                case 'does_not_have_item':
                    if (isset($condition['item_id'])) {
                        $itemIds[] = (int)$condition['item_id'];
                    }
                    break;
                case 'quest_status':
                    if (isset($condition['quest_id'])) {
                        $questIds[] = (int)$condition['quest_id'];
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        // 忽略解析错误
    }

    return ['items' => $itemIds, 'quests' => $questIds];
}

function extractActionIds($actionScript) {
    $itemIds = [];
    $questIds = [];

    try {
        $actions = json_decode($actionScript, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            return ['items' => $itemIds, 'quests' => $questIds];
        }

        // 处理动作列表
        $actionList = [];
        if (isset($actions['actions']) && is_array($actions['actions'])) {
            $actionList = $actions['actions'];
        } elseif (is_array($actions)) {
            $actionList = $actions;
        }

        foreach ($actionList as $action) {
            if (!isset($action['type'])) continue;

            switch ($action['type']) {
                case 'give_item':
                case 'remove_item':
                    if (isset($action['item_id'])) {
                        $itemIds[] = (int)$action['item_id'];
                    }
                    break;
                case 'start_quest':
                case 'complete_quest':
                    if (isset($action['quest_id'])) {
                        $questIds[] = (int)$action['quest_id'];
                    }
                    break;
            }
        }
    } catch (Exception $e) {
        // 忽略解析错误
    }

    return ['items' => $itemIds, 'quests' => $questIds];
}

function getTestData($action) {
    switch ($action) {
        case 'get_scenes':
            return [
                ['id' => 1, 'name' => '新手村', 'description' => '新手玩家的起始地点', 'x' => 0, 'y' => 0, 'z' => 0, 'max_players' => 100, 'zone_name' => '新手区域', 'continent_name' => '主大陆'],
                ['id' => 2, 'name' => '森林入口', 'description' => '通往森林的入口', 'x' => 1, 'y' => 0, 'z' => 0, 'max_players' => 50, 'zone_name' => '新手区域', 'continent_name' => '主大陆'],
                ['id' => 3, 'name' => '森林深处', 'description' => '危险的森林深处', 'x' => 2, 'y' => 0, 'z' => 0, 'max_players' => 30, 'zone_name' => '森林区域', 'continent_name' => '主大陆'],
                ['id' => 4, 'name' => '商业区', 'description' => '繁华的商业区', 'x' => 0, 'y' => 1, 'z' => 0, 'max_players' => 80, 'zone_name' => '城镇区域', 'continent_name' => '主大陆'],
                ['id' => 5, 'name' => '训练场', 'description' => '玩家训练的地方', 'x' => 1, 'y' => 1, 'z' => 0, 'max_players' => 40, 'zone_name' => '城镇区域', 'continent_name' => '主大陆']
            ];

        case 'get_npcs':
            return [
                ['id' => 1, 'name' => '村长老李', 'description' => '新手村的村长', 'level' => 50, 'is_merchant' => 0, 'is_quest_giver' => 1, 'default_dialogue_tree_id' => 1, 'scene_id' => 1, 'position_desc' => '村中央', 'scene_name' => '新手村'],
                ['id' => 2, 'name' => '武器商人', 'description' => '出售武器的商人', 'level' => 30, 'is_merchant' => 1, 'is_quest_giver' => 0, 'default_dialogue_tree_id' => 2, 'scene_id' => 4, 'position_desc' => '商店街', 'scene_name' => '商业区'],
                ['id' => 3, 'name' => '森林守卫', 'description' => '守护森林的战士', 'level' => 25, 'is_merchant' => 0, 'is_quest_giver' => 1, 'default_dialogue_tree_id' => 3, 'scene_id' => 2, 'position_desc' => '森林入口', 'scene_name' => '森林入口'],
                ['id' => 4, 'name' => '训练师', 'description' => '教授技能的训练师', 'level' => 40, 'is_merchant' => 1, 'is_quest_giver' => 1, 'default_dialogue_tree_id' => 4, 'scene_id' => 5, 'position_desc' => '训练场中央', 'scene_name' => '训练场']
            ];

        case 'get_quests':
            return [
                ['id' => 1, 'title' => '初来乍到', 'description' => '与村长对话了解情况', 'type' => 'dialogue', 'min_level' => 1, 'is_repeatable' => 0, 'group_id' => 1, 'giver_npc_id' => 1, 'receiver_npc_id' => 1, 'prerequisite_quests' => '[]', 'reward_gold' => 100, 'reward_exp' => 50, 'reward_items' => '[]', 'giver_npc_name' => '村长老李', 'receiver_npc_name' => '村长老李', 'group_name' => '新手任务'],
                ['id' => 2, 'title' => '收集木材', 'description' => '为村子收集10个木材', 'type' => 'collection', 'min_level' => 2, 'is_repeatable' => 0, 'group_id' => 1, 'giver_npc_id' => 1, 'receiver_npc_id' => 1, 'prerequisite_quests' => '[1]', 'reward_gold' => 200, 'reward_exp' => 100, 'reward_items' => '[{"id":1,"count":1}]', 'giver_npc_name' => '村长老李', 'receiver_npc_name' => '村长老李', 'group_name' => '新手任务'],
                ['id' => 3, 'title' => '清理野狼', 'description' => '消灭森林中的5只野狼', 'type' => 'kill', 'min_level' => 5, 'is_repeatable' => 0, 'group_id' => 1, 'giver_npc_id' => 3, 'receiver_npc_id' => 3, 'prerequisite_quests' => '[2]', 'reward_gold' => 300, 'reward_exp' => 200, 'reward_items' => '[{"id":2,"count":1}]', 'giver_npc_name' => '森林守卫', 'receiver_npc_name' => '森林守卫', 'group_name' => '新手任务'],
                ['id' => 4, 'title' => '购买装备', 'description' => '从武器商人处购买一把武器', 'type' => 'dialogue', 'min_level' => 3, 'is_repeatable' => 0, 'group_id' => 1, 'giver_npc_id' => 2, 'receiver_npc_id' => 2, 'prerequisite_quests' => '[1]', 'reward_gold' => 150, 'reward_exp' => 75, 'reward_items' => '[]', 'giver_npc_name' => '武器商人', 'receiver_npc_name' => '武器商人', 'group_name' => '新手任务'],
                ['id' => 5, 'title' => '技能训练', 'description' => '学习基础战斗技能', 'type' => 'dialogue', 'min_level' => 8, 'is_repeatable' => 0, 'group_id' => 2, 'giver_npc_id' => 4, 'receiver_npc_id' => 4, 'prerequisite_quests' => '[3,4]', 'reward_gold' => 500, 'reward_exp' => 300, 'reward_items' => '[{"id":3,"count":1}]', 'giver_npc_name' => '训练师', 'receiver_npc_name' => '训练师', 'group_name' => '进阶任务'],
                ['id' => 6, 'title' => '药草采集', 'description' => '采集治疗药草制作药水', 'type' => 'collection', 'min_level' => 6, 'is_repeatable' => 1, 'group_id' => 2, 'giver_npc_id' => 1, 'receiver_npc_id' => 1, 'prerequisite_quests' => '[4]', 'reward_gold' => 180, 'reward_exp' => 120, 'reward_items' => '[{"id":1,"count":2}]', 'giver_npc_name' => '村长老李', 'receiver_npc_name' => '村长老李', 'group_name' => '进阶任务'],
                ['id' => 7, 'title' => '命运的齿轮', 'description' => '击杀腐化的水母，将金色魔眼*5带给联邦使者伊文娜', 'type' => 'collection', 'min_level' => 1, 'is_repeatable' => 0, 'group_id' => 5, 'giver_npc_id' => 12, 'receiver_npc_id' => 13, 'prerequisite_quests' => '[]', 'reward_gold' => 50, 'reward_exp' => 50, 'reward_items' => '[]', 'giver_npc_name' => 'NPC_12', 'receiver_npc_name' => '联邦使者伊文娜', 'group_name' => '主线任务'],
                ['id' => 8, 'title' => '深入森林', 'description' => '探索森林深处的秘密', 'type' => 'kill', 'min_level' => 10, 'is_repeatable' => 0, 'group_id' => 3, 'giver_npc_id' => 3, 'receiver_npc_id' => 3, 'prerequisite_quests' => '[7]', 'reward_gold' => 500, 'reward_exp' => 350, 'reward_items' => '[{"id":3,"count":1}]', 'giver_npc_name' => '森林守卫', 'receiver_npc_name' => '森林守卫', 'group_name' => '高级任务'],
                ['id' => 9, 'title' => '商业合作', 'description' => '与武器商人建立长期合作关系', 'type' => 'dialogue', 'min_level' => 8, 'is_repeatable' => 0, 'group_id' => 3, 'giver_npc_id' => 2, 'receiver_npc_id' => 2, 'prerequisite_quests' => '[5]', 'reward_gold' => 400, 'reward_exp' => 250, 'reward_items' => '[]', 'giver_npc_name' => '武器商人', 'receiver_npc_name' => '武器商人', 'group_name' => '高级任务'],
                ['id' => 10, 'title' => '终极试炼', 'description' => '完成最终的战斗试炼', 'type' => 'kill', 'min_level' => 15, 'is_repeatable' => 0, 'group_id' => 3, 'giver_npc_id' => 4, 'receiver_npc_id' => 4, 'prerequisite_quests' => '[8,9]', 'reward_gold' => 1000, 'reward_exp' => 500, 'reward_items' => '[{"id":4,"count":1}]', 'giver_npc_name' => '训练师', 'receiver_npc_name' => '训练师', 'group_name' => '高级任务']
            ];

        case 'get_dialogues':
            return [
                ['id' => 1, 'name' => '村长对话', 'description' => '村长的默认对话', 'node_count' => 5],
                ['id' => 2, 'name' => '商人对话', 'description' => '武器商人的对话', 'node_count' => 3],
                ['id' => 3, 'name' => '守卫对话', 'description' => '森林守卫的对话', 'node_count' => 4],
                ['id' => 4, 'name' => '训练师对话', 'description' => '训练师的对话', 'node_count' => 6]
            ];

        case 'get_quest_groups':
            return [
                ['id' => 1, 'name' => '新手任务', 'description' => '适合新手玩家的任务', 'quest_count' => 4],
                ['id' => 2, 'name' => '进阶任务', 'description' => '中级玩家的任务', 'quest_count' => 1],
                ['id' => 3, 'name' => '日常任务', 'description' => '可重复的日常任务', 'quest_count' => 0]
            ];

        case 'get_dialogue_nodes':
            return [
                ['id' => 1, 'dialogue_tree_id' => 1, 'content' => '你好，欢迎来到新手村！', 'next_node_ids' => '[2]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0, 'tree_name' => '村长对话'],
                ['id' => 2, 'dialogue_tree_id' => 1, 'content' => '我需要帮助吗？', 'next_node_ids' => '[3,4]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 1, 'tree_name' => '村长对话'],
                ['id' => 3, 'dialogue_tree_id' => 1, 'content' => '是的，我需要了解这里的情况', 'next_node_ids' => '[5]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0, 'tree_name' => '村长对话'],
                ['id' => 4, 'dialogue_tree_id' => 1, 'content' => '不用了，谢谢', 'next_node_ids' => '[]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0, 'tree_name' => '村长对话'],
                ['id' => 5, 'dialogue_tree_id' => 1, 'content' => '那么请帮我收集一些木材吧', 'next_node_ids' => '[]', 'condition_script' => null, 'action_script' => '{"actions":[{"type":"start_quest","quest_id":"2"}]}', 'is_player_choice' => 0, 'tree_name' => '村长对话']
            ];

        case 'get_items':
            return [
                ['id' => 1, 'name' => '新手剑', 'description' => '适合新手使用的基础武器', 'category' => '武器'],
                ['id' => 2, 'name' => '生命药水', 'description' => '恢复少量生命值', 'category' => '消耗品'],
                ['id' => 3, 'name' => '魔法戒指', 'description' => '增加魔法攻击力的戒指', 'category' => '饰品'],
                ['id' => 4, 'name' => '传说之剑', 'description' => '传说中的神器', 'category' => '武器'],
                ['id' => 65, 'name' => '新手剑', 'description' => '适合新手使用的基础武器', 'category' => '武器'],
                ['id' => 66, 'name' => '新手盾', 'description' => '适合新手使用的基础防具', 'category' => '防具'],
                ['id' => 67, 'name' => '新手弓', 'description' => '适合新手使用的远程武器', 'category' => '武器']
            ];

        case 'get_dialogue_tree_detail':
            return [
                'tree' => ['id' => 1, 'name' => '村长对话', 'description' => '村长的默认对话', 'node_count' => 5],
                'nodes' => [
                    ['id' => 1, 'content' => '你好，欢迎来到新手村！', 'next_node_ids' => '[2]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0],
                    ['id' => 2, 'content' => '我需要帮助吗？', 'next_node_ids' => '[3,4]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 1],
                    ['id' => 3, 'content' => '是的，我需要了解这里的情况', 'next_node_ids' => '[5]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0],
                    ['id' => 4, 'content' => '不用了，谢谢', 'next_node_ids' => '[]', 'condition_script' => null, 'action_script' => null, 'is_player_choice' => 0],
                    ['id' => 5, 'content' => '那么请帮我收集一些木材吧', 'next_node_ids' => '[]', 'condition_script' => null, 'action_script' => '{"actions":[{"type":"start_quest","quest_id":"2"}]}', 'is_player_choice' => 0],
                    ['id' => 80, 'content' => '既然这样，你带上我给你的武器，向我证明的你想法吧！', 'next_node_ids' => '[]', 'condition_script' => '{"logical_operator":"OR","conditions":[{"type":"has_item","item_id":"66","quantity":"1"},{"type":"has_item","item_id":"67","quantity":"1"},{"type":"has_item","item_id":"65","quantity":"1"}]}', 'action_script' => '{"actions":[{"type":"start_quest","quest_id":"8"}]}', 'is_player_choice' => 0],
                    ['id' => 81, 'content' => '有趣！有趣！我已知晓了。', 'next_node_ids' => '[]', 'condition_script' => null, 'action_script' => '{"actions":[{"type":"give_item","item_id":"65","quantity":"1"}]}', 'is_player_choice' => 0]
                ],
                'npcs' => [
                    ['id' => 1, 'name' => '村长老李', 'scene_id' => 1, 'scene_name' => '新手村']
                ],
                'items' => [
                    ['id' => 65, 'name' => '新手剑', 'description' => '适合新手使用的基础武器'],
                    ['id' => 66, 'name' => '新手盾', 'description' => '适合新手使用的基础防具'],
                    ['id' => 67, 'name' => '新手弓', 'description' => '适合新手使用的远程武器']
                ],
                'quests' => [
                    ['id' => 2, 'title' => '收集木材', 'description' => '为村子收集10个木材'],
                    ['id' => 8, 'title' => '武器试炼', 'description' => '使用新获得的武器完成战斗试炼']
                ]
            ];

        default:
            return [];
    }
}
?>
