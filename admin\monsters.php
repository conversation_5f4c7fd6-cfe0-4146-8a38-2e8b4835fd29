<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);

// 检查是否是通过AJAX请求获取所有模板
if (isset($_GET['action']) && $_GET['action'] === 'get_all_templates') {
    header('Content-Type: application/json');
    if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
        echo json_encode(['success' => false, 'message' => '未授权']);
        exit;
    }
    
    require_once '../config/Database.php';
    $db = Database::getInstance()->getConnection();
    $stmt = $db->query("SELECT id, name FROM monster_templates ORDER BY name");
    $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode(['success' => true, 'data' => $templates]);
    exit; // 处理完AJAX请求后直接退出
}

// 辅助函数：获取装备位置的中文名称
function getSlotName($slot) {
    $slotMap = [
        'Head' => '头部',
        'Neck' => '颈部',
        'Body' => '身体',
        'Back' => '背部',
        'Finger' => '手指',
        'LeftHand' => '左手',
        'RightHand' => '右手',
        'TwoHanded' => '双手'
    ];
    return isset($slotMap[$slot]) ? $slotMap[$slot] : $slot;
}

require_once '../config/Database.php';

$db = Database::getInstance()->getConnection();
// 获取更详细的怪物信息用于列表展示
// $stmt = $db->query("SELECT id, name, level, base_hp, base_attack FROM monster_templates ORDER BY id ASC");
// $monsters = $stmt->fetchAll(PDO::FETCH_ASSOC);
// $stmt->closeCursor();

// 获取所有怪物装备物品
$stmt = $db->query(
    "SELECT it.id, it.name, it.category, ed.slot
     FROM item_templates it
     JOIN equipment_details ed ON it.id = ed.item_template_id
     WHERE it.category = 'Equipment' AND (it.equipment_type = 'monster' OR it.equipment_type IS NULL)
     ORDER BY ed.slot, it.name"
);
$equipmentItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

// 获取所有掉落表
$stmt = $db->query("SELECT id, name FROM loot_tables ORDER BY name ASC");
$lootTables = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

// 获取所有可供怪物使用的技能
$stmt = $db->query("SELECT id, name FROM skill_templates WHERE skill_type IN ('COMBAT_INSTANT', 'DEBUFF') AND target_type IN ('ENEMY', 'HOSTILE') ORDER BY name ASC");
$monsterSkills = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

// 获取所有怪物分组
$stmt = $db->query("SELECT * FROM monster_groups ORDER BY sort_order, name");
$groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

$pageTitle = '怪物管理';
$currentPage = 'monsters';
require_once 'layout_header.php';
?>

<div class="main-content-area card">
    <div class="card-header">
        <div class="header-actions">
            <button class="btn btn-success" onclick="showAddGroupModal()">添加分组</button>
            <button class="btn btn-primary" onclick="showCreateModal()">创建新怪物</button>
        </div>
    </div>
    
    <!-- 分组管理 -->
    <div class="card mb-4">
        <div class="groups-container" id="monster-groups">
            <a href="monsters.php" class="group-item <?php echo !isset($_GET['group']) ? 'active' : ''; ?>">
                <span class="group-info">全部怪物</span>
            </a>
            <?php foreach($groups as $group): ?>
                <a href="monsters.php?group=<?php echo $group['id']; ?>" 
                   class="group-item <?php echo (isset($_GET['group']) && $_GET['group'] == $group['id']) ? 'active' : ''; ?> group-type-<?php echo $group['type']; ?>">
                    <span class="group-info">
                        <?php echo htmlspecialchars($group['name']); ?> 
                        <span class="group-count">(<?php 
                            // 计算该分组下的怪物数量
                            $stmt = $db->prepare("SELECT COUNT(*) FROM monster_templates WHERE group_id = :group_id");
                            $stmt->execute([':group_id' => $group['id']]);
                            echo $stmt->fetchColumn(); 
                        ?>)</span>
                    </span>
                    <div class="group-actions">
                        <button class="btn btn-sm edit-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>"
                                data-type="<?php echo $group['type']; ?>"
                                data-sort="<?php echo $group['sort_order']; ?>"
                                data-description="<?php echo htmlspecialchars($group['description'] ?? ''); ?>">
                            <i class="fa fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-danger delete-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>">
                            <i class="fa fa-trash"></i> 删除
                        </button>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="table-container">
        <table id="monster-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>等级</th>
                    <th>HP</th>
                    <th>攻击力</th>
                    <th>防御力</th>
                    <th>攻击速度</th>
                    <th>力量</th>
                    <th>敏捷</th>
                    <th>体质</th>
                    <th>智慧</th>
                    <th>经验</th>
                    <th>掉落表</th>
                    <th class="actions-column">操作</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td colspan="14" style="text-align:center; padding: 20px;">正在加载怪物数据...</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
    
<!-- Edit Monster Modal -->
<div id="edit-monster-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>编辑怪物</h2>
            <button class="modal-close" onclick="hideActiveModal()">&times;</button>
        </div>
        <form id="edit-monster-form" onsubmit="handleFormSubmit(event, 'update')">
            <input type="hidden" id="edit-id" name="id">
            <!-- Form content will be the same as create -->
        </form>
    </div>
</div>

<!-- Create Monster Modal -->
<div id="create-monster-modal" class="modal-overlay">
        <div class="modal-content">
        <div class="modal-header">
            <h2>创建新怪物</h2>
            <button class="modal-close" onclick="hideActiveModal()">&times;</button>
        </div>
        <form id="create-monster-form" onsubmit="handleFormSubmit(event, 'create')">
            <!-- Form fields will be loaded by JS -->
        </form>
    </div>
</div>

<div id="status-message-container"></div>

<!-- 添加分组模态框 -->
<div id="add-group-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>添加怪物分组</h2>
            <button class="modal-close" onclick="hideGroupModal('add')">&times;</button>
        </div>
        <form id="add-group-form" onsubmit="handleGroupSubmit(event, 'add')">
            <div class="form-group">
                <label for="add-group-name">名称</label>
                <input type="text" id="add-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="add-group-type">类型</label>
                <select id="add-group-type" name="type" class="form-control">
                    <option value="normal">普通</option>
                    <option value="elite">精英</option>
                    <option value="boss">BOSS</option>
                    <option value="special">特殊</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-group-sort">排序顺序</label>
                <input type="number" id="add-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="add-group-description">描述</label>
                <textarea id="add-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn btn-secondary" onclick="hideGroupModal('add')">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑分组模态框 -->
<div id="edit-group-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>编辑怪物分组</h2>
            <button class="modal-close" onclick="hideGroupModal('edit')">&times;</button>
        </div>
        <form id="edit-group-form" onsubmit="handleGroupSubmit(event, 'update')">
            <input type="hidden" id="edit-group-id" name="id">
            
            <div class="form-group">
                <label for="edit-group-name">名称</label>
                <input type="text" id="edit-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="edit-group-type">类型</label>
                <select id="edit-group-type" name="type" class="form-control">
                    <option value="normal">普通</option>
                    <option value="elite">精英</option>
                    <option value="boss">BOSS</option>
                    <option value="special">特殊</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-group-sort">排序顺序</label>
                <input type="number" id="edit-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="edit-group-description">描述</label>
                <textarea id="edit-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn btn-secondary" onclick="hideGroupModal('edit')">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除分组确认模态框 -->
<div id="delete-group-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>确认删除</h2>
            <button class="modal-close" onclick="hideGroupModal('delete')">&times;</button>
        </div>
        <form id="delete-group-form" onsubmit="handleGroupSubmit(event, 'delete')">
            <input type="hidden" id="delete-group-id" name="id">
            <p id="delete-group-message"></p>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn btn-secondary" onclick="hideGroupModal('delete')">取消</button>
            </div>
        </form>
    </div>
</div>

<?php
// Re-usable form fields structure to be injected via JS
$form_fields_html = '
<input type="hidden" id="{prefix}-id" name="id">
<div class="form-group">
    <label for="{prefix}-name">名称:</label>
    <input type="text" id="{prefix}-name" name="name" required>
</div>
<div class="form-group">
    <label for="{prefix}-description">描述:</label>
    <textarea id="{prefix}-description" name="description" rows="3"></textarea>
</div>

<fieldset>
    <legend>基本信息</legend>
    <div class="form-row">
        <div class="form-group">
            <label for="{prefix}-level">等级:</label>
            <input type="number" id="{prefix}-level" name="level" value="1" min="1" required>
        </div>
        <div class="form-group">
            <label for="{prefix}-group-id">所属分组:</label>
            <select id="{prefix}-group-id" name="group_id">
                <option value="">-- 选择分组 --</option>';
foreach ($groups as $group) {
    $form_fields_html .= '<option value="' . $group['id'] . '">' . htmlspecialchars($group['name']) . '</option>';
}
$form_fields_html .= '
            </select>
        </div>
    </div>
</fieldset>

<fieldset>
    <legend>快速配置模板</legend>
    <div class="form-group template-buttons">
        <button type="button" class="btn" onclick="applyStatTemplate(\'{prefix}\', \'balanced\')">均衡型</button>
        <button type="button" class="btn" onclick="applyStatTemplate(\'{prefix}\', \'assassin\')">刺客型</button>
        <button type="button" class="btn" onclick="applyStatTemplate(\'{prefix}\', \'tank\')">肉盾型</button>
        <button type="button" class="btn btn-warning" onclick="applyStatTemplate(\'{prefix}\', \'boss\')">BOSS型</button>
    </div>
</fieldset>

<fieldset>
    <legend>核心属性</legend>
    <div class="form-grid">
        <div class="form-group">
            <label for="{prefix}-strength">力量:</label>
            <input type="number" id="{prefix}-strength" name="strength" required value="10">
        </div>
        <div class="form-group">
            <label for="{prefix}-agility">敏捷:</label>
            <input type="number" id="{prefix}-agility" name="agility" required value="10">
        </div>
        <div class="form-group">
            <label for="{prefix}-constitution">体质:</label>
            <input type="number" id="{prefix}-constitution" name="constitution" required value="10">
        </div>
        <div class="form-group">
            <label for="{prefix}-intelligence">智慧:</label>
            <input type="number" id="{prefix}-intelligence" name="intelligence" required value="10">
        </div>
    </div>
</fieldset>

<fieldset>
    <legend>衍生战斗属性 (可手动覆盖)</legend>
    <div class="form-grid">
        <div class="form-group">
            <label for="{prefix}-hp">当前HP:</label>
            <input type="number" id="{prefix}-hp" name="hp" required value="100">
        </div>
        <div class="form-group">
            <label for="{prefix}-max_hp">最大HP:</label>
            <input type="number" id="{prefix}-max_hp" name="max_hp" required value="100">
        </div>
        <div class="form-group">
            <label for="{prefix}-attack">攻击力:</label>
            <input type="number" id="{prefix}-attack" name="attack" required value="10">
        </div>
        <div class="form-group">
            <label for="{prefix}-defense">防御力:</label>
            <input type="number" id="{prefix}-defense" name="defense" required value="0">
        </div>
        <div class="form-group">
            <label for="{prefix}-attack_speed">攻击速度:</label>
            <input type="number" id="{prefix}-attack_speed" name="attack_speed">
        </div>
        <div class="form-group">
            <label for="{prefix}-experience_reward">经验奖励:</label>
            <input type="number" id="{prefix}-experience_reward" name="experience_reward" required value="0">
        </div>
    </div>
</fieldset>

<fieldset>
    <legend>元素抗性</legend>
    <div class="form-grid">
        <div class="form-group"><label for="{prefix}-fire_resistance">火抗:</label><input type="number" id="{prefix}-fire_resistance" name="fire_resistance" value="0"></div>
        <div class="form-group"><label for="{prefix}-ice_resistance">冰抗:</label><input type="number" id="{prefix}-ice_resistance" name="ice_resistance" value="0"></div>
        <div class="form-group"><label for="{prefix}-wind_resistance">风抗:</label><input type="number" id="{prefix}-wind_resistance" name="wind_resistance" value="0"></div>
        <div class="form-group"><label for="{prefix}-electric_resistance">电抗:</label><input type="number" id="{prefix}-electric_resistance" name="electric_resistance" value="0"></div>
    </div>
</fieldset>

<fieldset>
    <legend>配置</legend>
    <div class="form-group">
        <label for="{prefix}-loot_table_id">掉落表:</label>
        <select id="{prefix}-loot_table_id" name="loot_table_id">
            <option value="">无</option>';
foreach ($lootTables as $table) {
    $form_fields_html .= '<option value="' . $table['id'] . '">' . htmlspecialchars($table['name']) . '</option>';
}
$form_fields_html .= '
        </select>
    </div>
    <div class="form-group">
        <label for="{prefix}-equipment">装备:</label>
        <div class="equipment-filter-row">
            <select id="{prefix}-equipment-slot-filter" class="form-control">
                <option value="">所有位置</option>
                <option value="Head">头部</option>
                <option value="Neck">颈部</option>
                <option value="Body">身体</option>
                <option value="Back">背部</option>
                <option value="Finger">手指</option>
                <option value="LeftHand">左手</option>
                <option value="RightHand">右手</option>
                <option value="TwoHanded">双手</option>
            </select>
            <input type="text" id="{prefix}-equipment-search" class="form-control" placeholder="搜索装备...">
        </div>
        <select id="{prefix}-equipment" name="equipment[]" multiple size="8">';
foreach ($equipmentItems as $item) {
    $form_fields_html .= '<option value="' . $item['id'] . '" data-slot="' . $item['slot'] . '">' . htmlspecialchars($item['name']) . ' (' . getSlotName($item['slot']) . ')</option>';
}
$form_fields_html .= '
        </select>
    </div>
</fieldset>

<fieldset>
    <legend>技能配置</legend>
    <div id="{prefix}-skills-container">
        <!-- 技能行将由JS动态添加 -->
    </div>
    <button type="button" class="btn btn-sm" onclick="addSkillRow(\'{prefix}\')">添加技能</button>
</fieldset>

<div class="form-actions">
    <button type="submit" class="btn btn-primary">保存</button>
    <button type="button" class="btn btn-secondary" onclick="hideActiveModal()">取消</button>
';
?>

<?php ob_start(); ?>
<style>
.table-container { overflow-x: auto; }
#monster-table { width: 100%; border-collapse: collapse; }
#monster-table th, #monster-table td { padding: 12px 15px; border-bottom: 1px solid #ddd; text-align: left; }
#monster-table th { background-color: #f8f8f8; }
#monster-table tbody tr:hover { background-color: #f1f1f1; }
.actions-column { width: 1%; white-space: nowrap; }
.actions-cell { text-align: right !important; }

.modal-overlay {
    position: fixed; top: 0; left: 0; width: 100%; height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: none; justify-content: center; align-items: center;
    z-index: 1050; animation: fadeIn 0.3s;
}
.modal-content {
    background-color: #fff; border-radius: 8px;
    width: 90%; max-width: 800px; max-height: 90vh;
    display: flex; flex-direction: column;
    box-shadow: 0 5px 15px rgba(0,0,0,.5);
}
.modal-header {
    padding: 15px 20px; border-bottom: 1px solid #dee2e6;
    display: flex; justify-content: space-between; align-items: center;
}
.modal-header h2 { margin: 0; font-size: 1.25rem; }
.modal-close {
    border: none; background: transparent; font-size: 1.5rem;
    font-weight: 700; line-height: 1; opacity: .5;
    cursor: pointer; padding: 1rem; margin: -1rem -1rem -1rem auto;
}
.modal-close:hover { opacity: .75; }
.modal-content form { padding: 20px; overflow-y: auto; }
#status-message-container {
    position: fixed; bottom: 20px; left: 50%;
    transform: translateX(-50%); z-index: 1100;
}
.status-message { padding: 10px 20px; border-radius: 5px; color: #fff; }
.status-message.success { background-color: #28a745; }
.status-message.error { background-color: #dc3545; }
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }

.template-buttons {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.skill-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}
.skill-row select, .skill-row input {
    flex-grow: 1;
}
.skill-row .form-group {
    margin-bottom: 0;
    display: contents;
}
.skill-row label {
    margin-right: 5px;
    white-space: nowrap;
}

.equipment-filter-row {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}
.equipment-filter-row select,
.equipment-filter-row input {
    flex: 1;
}

/* 分组样式 */
.header-actions {
    display: flex;
    gap: 10px;
}
.groups-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
}
.group-item {
    display: flex;
    align-items: center;
    padding: 8px 120px 8px 15px; /* 右边预留120px给按钮 */
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    position: relative;
    min-width: 180px;
    width: auto;
    justify-content: flex-start; /* 左对齐内容 */
}
.group-item:hover {
    background-color: #e9ecef;
}
.group-item.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
.group-count {
    margin-left: 5px;
    font-size: 0.9em;
    color: inherit;
    opacity: 0.8;
}
.group-info {
    display: flex;
    align-items: center;
    flex: 1;
}
.group-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out;
    background: rgba(255, 255, 255, 0.95);
    padding: 2px 6px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.group-item:hover .group-actions {
    opacity: 1;
    visibility: visible;
}
.group-actions button {
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.2s ease;
    font-size: 0.75em;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    white-space: nowrap;
}
.group-actions button:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-actions button.btn-danger:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}
.group-item.active .group-actions {
    background: rgba(0, 123, 255, 0.95);
}

.group-item.active .group-actions button {
    background: rgba(255, 255, 255, 0.9);
    color: #007bff;
    border-color: rgba(255, 255, 255, 0.7);
}

.group-item.active .group-actions button:hover {
    background: white;
    color: #007bff;
    border-color: white;
}

.group-item.active .group-actions button.btn-danger:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* 分组类型颜色 */
.group-type-normal { border-left: 4px solid #6c757d; }
.group-type-elite { border-left: 4px solid #fd7e14; }
.group-type-boss { border-left: 4px solid #dc3545; }
.group-type-special { border-left: 4px solid #17a2b8; }

/* 徽章颜色 */
.badge-normal { background-color: #6c757d; color: white; }
.badge-elite { background-color: #fd7e14; color: white; }
.badge-boss { background-color: #dc3545; color: white; }
.badge-special { background-color: #17a2b8; color: white; }
</style>

    <script>
document.addEventListener('DOMContentLoaded', function() {
    fetchMonsters();
    
    // 绑定分组相关事件
    document.querySelectorAll('.edit-group').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showEditGroupModal(this.dataset);
        });
    });
    
    document.querySelectorAll('.delete-group').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            showDeleteGroupModal(this.dataset);
        });
    });
});

async function fetchMonsters() {
    try {
        // 获取URL中的group参数
        const urlParams = new URLSearchParams(window.location.search);
        const groupId = urlParams.get('group');
        
        // 构建API URL
        let apiUrl = 'api_monsters.php?action=get_for_list';
        if (groupId) {
            apiUrl += `&group=${groupId}`;
        }
        
        const response = await fetch(apiUrl);
        const res = await response.json();

        if (res.success) {
            populateMonsterTable(res.data);
        } else {
            const tbody = document.querySelector('#monster-table tbody');
            tbody.innerHTML = `<tr><td colspan="14" style="text-align:center;">加载怪物列表失败: ${res.message}</td></tr>`;
        }
    } catch (error) {
        console.error('Failed to fetch monsters:', error);
        const tbody = document.querySelector('#monster-table tbody');
        tbody.innerHTML = `<tr><td colspan="14" style="text-align:center;">加载怪物列表时发生网络错误。</td></tr>`;
    }
}

function populateMonsterTable(monsters) {
    const tbody = document.querySelector('#monster-table tbody');
    tbody.innerHTML = ''; // Clear loading message

    if (!monsters || monsters.length === 0) {
        tbody.innerHTML = '<tr><td colspan="14" style="text-align:center; padding: 20px;">还没有怪物，请创建一个。</td></tr>';
        return;
    }

    monsters.forEach(monster => {
        const row = document.createElement('tr');
        row.dataset.id = monster.id;
        // 使用 toFixed(2) 来格式化攻击速度
        const attackSpeed = monster.attack_speed ? parseFloat(monster.attack_speed).toFixed(2) : (1.0).toFixed(2);
        
        row.innerHTML = `
            <td data-label="ID">${monster.id}</td>
            <td data-label="名称">${escapeHtml(monster.name)}</td>
            <td data-label="等级">${monster.level ?? 1}</td>
            <td data-label="HP">${monster.base_hp ?? 100}</td>
            <td data-label="攻击力">${monster.base_attack ?? 10}</td>
            <td data-label="防御力">${monster.defense ?? 0}</td>
            <td data-label="攻击速度">${attackSpeed}</td>
            <td data-label="力量">${monster.strength ?? 0}</td>
            <td data-label="敏捷">${monster.agility ?? 0}</td>
            <td data-label="体质">${monster.constitution ?? 0}</td>
            <td data-label="智慧">${monster.intelligence ?? 0}</td>
            <td data-label="经验">${monster.experience_reward ?? 0}</td>
            <td data-label="掉落表">${monster.loot_table_name ? escapeHtml(monster.loot_table_name) : '无'}</td>
            <td data-label="操作" class="actions-cell">
                <button class="btn btn-secondary btn-sm" onclick="showEditModal(${monster.id})">编辑</button>
                <button class="btn btn-danger btn-sm" onclick="deleteMonster(${monster.id})">删除</button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

function escapeHtml(unsafe) {
    if (unsafe === null || unsafe === undefined) {
        return '';
    }
    return unsafe
         .toString()
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}
    
const formFieldsTemplate = <?= json_encode($form_fields_html) ?>;
const allSkills = <?= json_encode($monsterSkills) ?>;
let activeModal = null; // This will hold the DOM element of the currently active modal

// JS-side formulas, mirroring the PHP ones
const Formulas = {
    calculateMaxHp: (constitution, level) => 100 + (constitution * 10) + (level * 5),
    calculateAttack: (strength, level) => (strength * 2) + level,
    calculateDefense: (constitution, agility, level) => Math.floor(constitution * 1) + Math.floor(agility / 3) + level,
    calculateAttackSpeed: (agility) => 10 + agility,
};

// --- Modal Control ---

function showCreateModal() {
    const modal = document.getElementById('create-monster-modal');
    if (!modal) return;
    activeModal = modal;
    populateForm('create');
    // Set default multipliers for a new monster and calculate initial stats
    applyStatTemplate('create', 'balanced');
    modal.style.display = 'flex';
}

async function showEditModal(id) {
    const modal = document.getElementById('edit-monster-modal');
    if (!modal) return;
    activeModal = modal;

    try {
        const formData = new FormData();
        formData.append('action', 'get');
        formData.append('id', id);

        const response = await fetch('api_monsters.php', {
            method: 'POST',
            body: formData
        });
        const res = await response.json();

        if (res.success) {
            populateForm('edit', res.data);
            modal.style.display = 'flex';
        } else {
            showStatusMessage(res.message || '无法加载怪物数据', 'error');
            activeModal = null;
        }
    } catch (error) {
        showStatusMessage('加载怪物数据时发生网络错误', 'error');
        console.error('Fetch Error:', error);
        activeModal = null;
    }
}

function hideActiveModal() {
    if (activeModal) {
        activeModal.style.display = 'none';
        activeModal = null;
    }
}

// Attach hideActiveModal to the window object so it can be called from inline onclick
window.hideActiveModal = hideActiveModal;

// --- Form & Calculation Logic ---

function updateDerivedStats(prefix) {
    const getVal = (name) => parseInt(document.getElementById(`${prefix}-${name}`)?.value, 10) || 0;

    const level = getVal('level');
    const strength = getVal('strength');
    const agility = getVal('agility');
    const constitution = getVal('constitution');

    const max_hp = Formulas.calculateMaxHp(constitution, level);
    const attack = Formulas.calculateAttack(strength, level);
    const defense = Formulas.calculateDefense(constitution, agility, level);
    const attack_speed = Formulas.calculateAttackSpeed(agility);

    const setVal = (name, value) => {
        const el = document.getElementById(`${prefix}-${name}`);
        if (el) el.value = value;
    };
    
    setVal('max_hp', max_hp);
    setVal('attack', attack);
    setVal('defense', defense);
    setVal('attack_speed', attack_speed.toFixed(2));
    
    // 无论是创建还是编辑，都将当前HP设置为计算出的最大HP
    setVal('hp', max_hp);
}

function updateMultiplierFromManualInput(prefix, statName) {
    const form = document.getElementById(`${prefix}-monster-form`);
    const statInput = document.getElementById(`${prefix}-${statName}`);
    const levelInput = document.getElementById(`${prefix}-level`);
    
    const statValue = parseFloat(statInput.value) || 0;
    const level = parseInt(levelInput.value, 10) || 1;
    
    const halfLevel = level / 2;
    const multiplier = halfLevel > 0 ? statValue / halfLevel : 0;
    
    form.dataset[`${statName}Multiplier`] = multiplier;
    
    updateDerivedStats(prefix);
}

function recalculateCoreStats(prefix) {
    const form = document.getElementById(`${prefix}-monster-form`);
    const levelInput = document.getElementById(`${prefix}-level`);
    const level = parseInt(levelInput.value, 10) || 1;

    const attributeFields = ['strength', 'agility', 'constitution', 'intelligence'];

    attributeFields.forEach(attr => {
        const multiplier = parseFloat(form.dataset[`${attr}Multiplier`]) || 0;
        const newValue = Math.round(multiplier * level / 2);
        const input = document.getElementById(`${prefix}-${attr}`);
        if (input) {
            input.value = newValue;
        }
    });
    
    updateDerivedStats(prefix);
}

function setupStatCalculationListeners(prefix) {
    const attributeFields = ['strength', 'agility', 'constitution', 'intelligence'];
    const levelInput = document.getElementById(`${prefix}-level`);

    if (levelInput) {
        levelInput.addEventListener('input', () => recalculateCoreStats(prefix));
    }

    attributeFields.forEach(field => {
        const input = document.getElementById(`${prefix}-${field}`);
        if (input) {
            input.addEventListener('input', () => updateMultiplierFromManualInput(prefix, field));
        }
    });
}

function populateForm(prefix, data = {}) {
    const formId = `${prefix}-monster-form`;
    const form = document.getElementById(formId);
    if (!form) return;

    const formHtml = formFieldsTemplate.replace(/{prefix}/g, prefix);
    form.innerHTML = formHtml;

    // 清空技能容器
    const skillsContainer = form.querySelector(`#${prefix}-skills-container`);
    if (skillsContainer) skillsContainer.innerHTML = '';

    for (const key in data) {
        if (key === 'equipment') {
            const select = form.querySelector(`#${prefix}-equipment`);
            if (select) {
                // 在组织分组之前先清除所有选中状态
                Array.from(select.options).forEach(option => option.selected = false);
            }
            continue;
        }

        if (key === 'skills') {
            if (data.skills && data.skills.length > 0) {
                data.skills.forEach(skill => {
                    addSkillRow(prefix, skill.skill_template_id, skill.cast_chance);
                });
            }
            continue;
        }

        const input = form.querySelector(`[name="${key}"]`);
        if (input) {
            input.value = data[key] ?? '';
        }
    }
    
    const idInput = form.querySelector(`[name="id"]`);
    if (idInput) idInput.value = data.id || '';
    
    setupStatCalculationListeners(prefix);
    setupEquipmentFilters(prefix);
    
    // 在装备筛选设置完成后，再设置已选装备
    if (data.equipment && data.equipment.length > 0) {
        const select = document.getElementById(`${prefix}-equipment`);
        if (select) {
            data.equipment.forEach(itemId => {
                const option = select.querySelector(`option[value="${itemId}"]`);
                if (option) option.selected = true;
            });
        }
    }

    if (prefix === 'edit') {
        const form = document.getElementById('edit-monster-form');
        const level = parseInt(data.level, 10) || 1;
        const halfLevel = level / 2;
        const attributeFields = ['strength', 'agility', 'constitution', 'intelligence'];

        attributeFields.forEach(attr => {
            const statValue = parseFloat(data[attr]) || 0;
            const multiplier = halfLevel > 0 ? statValue / halfLevel : 0;
            form.dataset[`${attr}Multiplier`] = multiplier;
        });
        
        updateDerivedStats(prefix);
    }
}

async function handleFormSubmit(event, action) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', action);

    // --- 修改：处理技能数据 ---
    const skillRows = form.querySelectorAll('.skill-row');
    skillRows.forEach((row, index) => {
        const skillId = row.querySelector('select').value;
        const chance = row.querySelector('input[type="number"]').value;
        if (skillId) {
            formData.append(`skills[${index}][id]`, skillId);
            formData.append(`skills[${index}][chance]`, chance);
        }
    });

    const equipmentSelect = form.querySelector('select[multiple]');
    if (equipmentSelect) {
        formData.delete(equipmentSelect.name);
        Array.from(equipmentSelect.selectedOptions).forEach(option => {
            formData.append(equipmentSelect.name, option.value);
        });
    }

    try {
        const response = await fetch('api_monsters.php', {
            method: 'POST',
            body: formData
        });
        const res = await response.json();

        if (res.success) {
            showStatusMessage(res.message, 'success');
            hideActiveModal();
            setTimeout(() => location.reload(), 1000);
        } else {
            showStatusMessage(res.message || '操作失败', 'error');
        }
    } catch (error) {
        showStatusMessage('提交时发生网络错误: ' + error.message, 'error');
        console.error('Submit Error:', error);
    }
}

function addSkillRow(prefix, skillId = '', chance = '25.00') {
    const container = document.getElementById(`${prefix}-skills-container`);
    if (!container) return;

    const skillRow = document.createElement('div');
    skillRow.className = 'skill-row';

    let optionsHtml = '<option value="">-- 选择技能 --</option>';
    allSkills.forEach(skill => {
        const selected = skill.id == skillId ? 'selected' : '';
        optionsHtml += `<option value="${skill.id}" ${selected}>${skill.name}</option>`;
    });

    skillRow.innerHTML = `
        <div class="form-group">
            <select class="form-control">${optionsHtml}</select>
        </div>
        <div class="form-group">
            <label>几率:</label>
            <input type="number" class="form-control" value="${chance}" min="0" max="100" step="0.01">
        </div>
        <button type="button" class="btn btn-danger btn-sm" onclick="this.parentElement.remove()">移除</button>
    `;

    container.appendChild(skillRow);
}

function deleteMonster(id) {
    if (!confirm('确定要删除这个怪物吗？此操作不可逆，并会移除所有场景中对该怪物的引用。')) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);

    fetch('api_monsters.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(res => {
        if (res.success) {
            showStatusMessage(res.message, 'success');
            const row = document.querySelector(`tr[data-id="${id}"]`);
            if (row) row.remove();
        } else {
            showStatusMessage(res.message, 'error');
        }
    })
    .catch(error => {
        showStatusMessage('删除时发生网络错误: ' + error.message, 'error');
        console.error('Delete Error:', error);
    });
}

// --- Utility Functions ---
function showStatusMessage(message, type = 'success') {
    const container = document.getElementById('status-message-container');
    const msgDiv = document.createElement('div');
    msgDiv.className = `status-message ${type}`;
    msgDiv.textContent = message;
    container.appendChild(msgDiv);
    
    setTimeout(() => {
        msgDiv.style.opacity = '0';
        setTimeout(() => msgDiv.remove(), 500);
    }, 3000);
}

function applyStatTemplate(prefix, type) {
    const form = document.getElementById(`${prefix}-monster-form`);
    let multipliers;

    switch (type) {
        case 'assassin':
            multipliers = { strength: 6, agility: 8, constitution: 3, intelligence: 3 };
            break;
        case 'tank':
            multipliers = { strength: 6, agility: 3, constitution: 8, intelligence: 3 };
            break;
        case 'boss':
            multipliers = { strength: 10, agility: 10, constitution: 10, intelligence: 10 };
            break;
        case 'balanced':
        default:
            multipliers = { strength: 5, agility: 5, constitution: 5, intelligence: 5 };
            break;
    }

    for (const attr in multipliers) {
        form.dataset[`${attr}Multiplier`] = multipliers[attr];
    }
    
    recalculateCoreStats(prefix);
}

// 设置装备筛选功能
function setupEquipmentFilters(prefix) {
    const slotFilter = document.getElementById(`${prefix}-equipment-slot-filter`);
    const searchFilter = document.getElementById(`${prefix}-equipment-search`);
    let equipmentSelect = document.getElementById(`${prefix}-equipment`);
    
    if (!slotFilter || !searchFilter || !equipmentSelect) return;
    
    // 初始化：按装备位置对选项进行分组
    const organizeEquipmentBySlot = () => {
        // 创建一个新的select元素来替换原来的
        const newSelect = document.createElement('select');
        newSelect.id = equipmentSelect.id;
        newSelect.name = equipmentSelect.name;
        newSelect.multiple = true;
        newSelect.size = 8;
        
        // 获取所有已选中的值
        const selectedValues = Array.from(equipmentSelect.selectedOptions).map(opt => opt.value);
        
        // 按槽位分组
        const slots = {};
        Array.from(equipmentSelect.options).forEach(option => {
            const slot = option.dataset.slot || '其他';
            if (!slots[slot]) {
                slots[slot] = [];
            }
            slots[slot].push({
                value: option.value,
                text: option.text,
                selected: selectedValues.includes(option.value)
            });
        });
        
        // 创建分组
        Object.keys(slots).sort().forEach(slot => {
            const optgroup = document.createElement('optgroup');
            optgroup.label = getSlotNameJS(slot);
            optgroup.dataset.slot = slot;
            
            slots[slot].forEach(item => {
                const option = document.createElement('option');
                option.value = item.value;
                option.text = item.text;
                option.selected = item.selected;
                option.dataset.slot = slot;
                optgroup.appendChild(option);
            });
            
            newSelect.appendChild(optgroup);
        });
        
        // 替换原来的select
        equipmentSelect.parentNode.replaceChild(newSelect, equipmentSelect);
        return newSelect;
    };
    
    // 初始化分组
    equipmentSelect = organizeEquipmentBySlot();
    
    // 筛选函数
    const filterEquipment = () => {
        const slotValue = slotFilter.value;
        const searchValue = searchFilter.value.toLowerCase();
        
        // 处理所有的optgroup
        Array.from(equipmentSelect.querySelectorAll('optgroup')).forEach(optgroup => {
            const matchesSlot = !slotValue || optgroup.dataset.slot === slotValue;
            optgroup.style.display = matchesSlot ? '' : 'none';
            
            // 处理该组内的所有选项
            if (matchesSlot && searchValue) {
                Array.from(optgroup.querySelectorAll('option')).forEach(option => {
                    const matchesSearch = option.text.toLowerCase().includes(searchValue);
                    option.style.display = matchesSearch ? '' : 'none';
                });
            } else if (matchesSlot) {
                // 如果没有搜索词，显示该组内所有选项
                Array.from(optgroup.querySelectorAll('option')).forEach(option => {
                    option.style.display = '';
                });
            }
        });
    };
    
    slotFilter.addEventListener('change', filterEquipment);
    searchFilter.addEventListener('input', filterEquipment);
}

// 获取装备位置的中文名称（客户端JS版本）
function getSlotNameJS(slot) {
    const slotMap = {
        'Head': '头部',
        'Neck': '颈部',
        'Body': '身体',
        'Back': '背部',
        'Finger': '手指',
        'LeftHand': '左手',
        'RightHand': '右手',
        'TwoHanded': '双手'
    };
    return slotMap[slot] || slot;
}

// 分组管理相关函数
function showAddGroupModal() {
    const modal = document.getElementById('add-group-modal');
    if (!modal) return;
    
    // 重置表单
    document.getElementById('add-group-form').reset();
    
    // 显示模态框
    modal.style.display = 'flex';
}

function showEditGroupModal(data) {
    const modal = document.getElementById('edit-group-modal');
    if (!modal) return;
    
    // 填充表单数据
    document.getElementById('edit-group-id').value = data.id;
    document.getElementById('edit-group-name').value = data.name;
    document.getElementById('edit-group-type').value = data.type;
    document.getElementById('edit-group-sort').value = data.sort;
    document.getElementById('edit-group-description').value = data.description;
    
    // 显示模态框
    modal.style.display = 'flex';
}

function showDeleteGroupModal(data) {
    const modal = document.getElementById('delete-group-modal');
    if (!modal) return;
    
    // 设置ID和确认消息
    document.getElementById('delete-group-id').value = data.id;
    document.getElementById('delete-group-message').textContent = `确定要删除分组 "${data.name}" 吗？这将解除该分组与所有怪物的关联。`;
    
    // 显示模态框
    modal.style.display = 'flex';
}

function hideGroupModal(type) {
    const modalId = `${type}-group-modal`;
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'none';
    }
}

async function handleGroupSubmit(event, action) {
    event.preventDefault();
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', `${action}_group`);
    
    try {
        const response = await fetch('api_monsters.php', {
            method: 'POST',
            body: formData
        });
        const res = await response.json();
        
        if (res.success) {
            showStatusMessage(res.message, 'success');
            hideGroupModal(action);
            // 刷新页面以显示更改
            setTimeout(() => location.reload(), 1000);
        } else {
            showStatusMessage(res.message || '操作失败', 'error');
        }
    } catch (error) {
        showStatusMessage('提交时发生网络错误: ' + error.message, 'error');
        console.error('Submit Error:', error);
    }
}
    </script>

<?php 
$extra_js = ob_get_clean();
require_once 'layout_footer.php'; 
?> 