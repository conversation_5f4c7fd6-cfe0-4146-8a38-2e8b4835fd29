<?php
$pageTitle = '场景掉落物品清理管理';
$currentPage = 'scene_items_cleanup';

ob_start();
?>
<style>
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 20px;
        margin-bottom: 20px;
    }
    .stats-card {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stats-card h3 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 8px;
        font-size: 16px;
    }
    .stat-item {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        padding: 8px 0;
        border-bottom: 1px dotted #dee2e6;
    }
    .stat-item:last-child {
        border-bottom: none;
    }
    .stat-item .label {
        color: #6c757d;
    }
    .stat-item .value {
        font-weight: bold;
        color: #495057;
    }
    .stat-item .value.danger {
        color: #dc3545;
    }
    .cleanup-controls {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .cleanup-controls h3 {
        margin-top: 0;
        margin-bottom: 20px;
        color: #495057;
        border-bottom: 2px solid #28a745;
        padding-bottom: 8px;
    }
    .control-group {
        margin-bottom: 20px;
    }
    .control-group label {
        display: block;
        margin-bottom: 8px;
        font-weight: 600;
        color: #495057;
    }
    .control-group input, .control-group select {
        padding: 8px 12px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
        margin-right: 10px;
    }
    .control-group input:focus, .control-group select:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }
    .btn-danger {
        background-color: #dc3545;
        color: white;
        border: 1px solid #dc3545;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        font-size: 14px;
        transition: all 0.2s;
    }
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
    .btn-info {
        background-color: #17a2b8;
        color: white;
        border: 1px solid #17a2b8;
        padding: 10px 20px;
        border-radius: 4px;
        cursor: pointer;
        margin-right: 10px;
        font-size: 14px;
        transition: all 0.2s;
    }
    .btn-info:hover {
        background-color: #138496;
        border-color: #117a8b;
    }
    .result-area {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-top: 20px;
        min-height: 120px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .loading {
        color: #6c757d;
        font-style: italic;
    }
    .success {
        color: #28a745;
        font-weight: 600;
    }
    .error {
        color: #dc3545;
        font-weight: 600;
    }
    .cleaned-items {
        max-height: 300px;
        overflow-y: auto;
        margin-top: 15px;
        border: 1px solid #dee2e6;
        border-radius: 4px;
        background: white;
    }
    .cleaned-item {
        padding: 12px;
        border-bottom: 1px solid #f1f3f4;
        font-size: 13px;
        color: #495057;
    }
    .cleaned-item:last-child {
        border-bottom: none;
    }
    .cleaned-item:nth-child(even) {
        background-color: #f8f9fa;
    }
</style>
<?php
$extra_css = ob_get_clean();

require_once 'layout_header.php';
?>

<!-- 统计信息 -->
<div class="stats-grid" id="statsGrid">
    <div class="stats-card">
        <h3>📊 总体统计</h3>
        <div id="totalStats">
            <div class="loading">正在加载统计信息...</div>
        </div>
    </div>
    <div class="stats-card">
        <h3>⏰ 按时间分布</h3>
        <div id="timeStats">
            <div class="loading">正在加载统计信息...</div>
        </div>
    </div>
    <div class="stats-card">
        <h3>🗺️ 场景分布 (前10)</h3>
        <div id="sceneStats">
            <div class="loading">正在加载统计信息...</div>
        </div>
    </div>
</div>

<!-- 清理控制 -->
<div class="cleanup-controls">
    <h3>🗑️ 清理操作</h3>
    <div class="control-group">
        <label for="hoursInput">清理超过指定小时数的物品:</label>
        <input type="number" id="hoursInput" min="1" max="24" value="1" placeholder="小时数" />
        <button class="btn-danger" onclick="cleanupByHours()">执行清理</button>
    </div>
    <div class="control-group">
        <button class="btn-danger" onclick="cleanupExpired()">清理超过1小时的物品</button>
        <button class="btn-info" onclick="refreshStats()">刷新统计</button>
    </div>
    <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 4px; color: #856404;">
        <strong>⚠️ 注意:</strong> 清理操作不可撤销，请谨慎操作。系统会自动每30分钟清理一次超过1小时的掉落物品。
    </div>
</div>

<!-- 结果显示 -->
<div class="result-area" id="resultArea">
    <div style="color: #6c757d; text-align: center; padding: 20px;">
        点击上方按钮执行操作...
    </div>
</div>

<?php
ob_start();
?>
<script>
    // 页面加载时获取统计信息
    document.addEventListener('DOMContentLoaded', function() {
        refreshStats();
    });

    // 刷新统计信息
    function refreshStats() {
        showLoading('正在加载统计信息...');

        fetch('api_scene_items_cleanup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_stats'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayStats(data.data);
                showResult('统计信息已刷新', 'success');
            } else {
                showResult('获取统计信息失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showResult('请求失败: ' + error.message, 'error');
        });
    }

    // 显示统计信息
    function displayStats(stats) {
        // 总体统计
        const totalStats = stats.total_stats;
        document.getElementById('totalStats').innerHTML = `
            <div class="stat-item">
                <span class="label">总物品数:</span>
                <span class="value">${totalStats.total_items}</span>
            </div>
            <div class="stat-item">
                <span class="label">涉及场景:</span>
                <span class="value">${totalStats.scenes_with_items}</span>
            </div>
            <div class="stat-item">
                <span class="label">最早掉落:</span>
                <span class="value">${totalStats.oldest_drop || '无'}</span>
            </div>
            <div class="stat-item">
                <span class="label">最新掉落:</span>
                <span class="value">${totalStats.newest_drop || '无'}</span>
            </div>
            <div class="stat-item">
                <span class="label">可清理数量:</span>
                <span class="value danger">${stats.cleanable_count}</span>
            </div>
        `;

        // 时间统计
        let timeStatsHtml = '';
        if (stats.time_stats && stats.time_stats.length > 0) {
            stats.time_stats.forEach(item => {
                timeStatsHtml += `
                    <div class="stat-item">
                        <span class="label">${item.time_range}:</span>
                        <span class="value">${item.item_count}</span>
                    </div>
                `;
            });
        } else {
            timeStatsHtml = '<div class="stat-item"><span class="label">暂无数据</span><span class="value">-</span></div>';
        }
        document.getElementById('timeStats').innerHTML = timeStatsHtml;

        // 场景统计
        let sceneStatsHtml = '';
        if (stats.scene_stats && stats.scene_stats.length > 0) {
            stats.scene_stats.forEach(item => {
                const sceneName = item.scene_name || item.scene_id;
                sceneStatsHtml += `
                    <div class="stat-item">
                        <span class="label">${sceneName}:</span>
                        <span class="value">${item.item_count}</span>
                    </div>
                `;
            });
        } else {
            sceneStatsHtml = '<div class="stat-item"><span class="label">暂无数据</span><span class="value">-</span></div>';
        }
        document.getElementById('sceneStats').innerHTML = sceneStatsHtml;
    }

    // 清理超过1小时的物品
    function cleanupExpired() {
        if (!confirm('确定要清理所有超过1小时的掉落物品吗？此操作不可撤销！')) {
            return;
        }

        showLoading('正在清理超过1小时的掉落物品...');

        fetch('api_scene_items_cleanup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=cleanup_expired'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCleanupResult(data.data);
                setTimeout(() => refreshStats(), 1000); // 延迟刷新统计
            } else {
                showResult('清理失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showResult('请求失败: ' + error.message, 'error');
        });
    }

    // 按小时数清理
    function cleanupByHours() {
        const hours = parseInt(document.getElementById('hoursInput').value);
        if (hours < 1 || hours > 24) {
            showToast('小时数必须在1-24之间', 'error');
            return;
        }

        if (!confirm(`确定要清理所有超过${hours}小时的掉落物品吗？此操作不可撤销！`)) {
            return;
        }

        showLoading(`正在清理超过${hours}小时的掉落物品...`);

        fetch('api_scene_items_cleanup.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=cleanup_by_hours&hours=${hours}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayCleanupResult(data.data);
                setTimeout(() => refreshStats(), 1000); // 延迟刷新统计
                showToast('清理操作完成', 'success');
            } else {
                showResult('清理失败: ' + data.message, 'error');
                showToast('清理失败', 'error');
            }
        })
        .catch(error => {
            showResult('请求失败: ' + error.message, 'error');
            showToast('请求失败', 'error');
        });
    }

    // 显示清理结果
    function displayCleanupResult(result) {
        let html = `<div class="success"><strong>✅ ${result.message}</strong></div>`;

        if (result.affected_scenes && result.affected_scenes.length > 0) {
            html += `<div style="margin-top: 15px; color: #495057;">
                <strong>🗺️ 受影响的场景 (${result.affected_scenes.length}个):</strong>
                <span style="color: #007bff;">${result.affected_scenes.join(', ')}</span>
            </div>`;
        }

        if (result.items && result.items.length > 0) {
            html += `<div style="margin-top: 15px; color: #495057;">
                <strong>📋 清理的物品详情 (${result.items.length}个):</strong>
            </div>`;
            html += '<div class="cleaned-items">';
            result.items.forEach((item, index) => {
                html += `<div class="cleaned-item">
                    <strong>#${index + 1}</strong>
                    场景: <span style="color: #007bff;">${item.scene_id}</span> |
                    物品: <span style="color: #28a745;">${item.item_name}</span> |
                    数量: <span style="color: #ffc107;">${item.quantity}</span> |
                    掉落时间: <span style="color: #6c757d;">${item.dropped_at}</span>
                </div>`;
            });
            html += '</div>';
        }

        document.getElementById('resultArea').innerHTML = html;
    }

    // 显示加载状态
    function showLoading(message) {
        document.getElementById('resultArea').innerHTML = `
            <div class="loading" style="text-align: center; padding: 30px;">
                <div style="font-size: 18px; margin-bottom: 10px;">⏳</div>
                <div>${message}</div>
            </div>
        `;
    }

    // 显示结果
    function showResult(message, type) {
        const icon = type === 'success' ? '✅' : '❌';
        document.getElementById('resultArea').innerHTML = `
            <div class="${type}" style="text-align: center; padding: 20px;">
                <div style="font-size: 18px; margin-bottom: 10px;">${icon}</div>
                <div>${message}</div>
            </div>
        `;
    }
</script>
<?php
$extra_js = ob_get_clean();

require_once 'layout_footer.php';
?>
