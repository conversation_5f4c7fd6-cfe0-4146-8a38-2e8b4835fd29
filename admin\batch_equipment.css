/* 批量装备创建器样式 */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

.page-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px;
}

.page-header h1 {
    margin: 0 0 10px 0;
    font-size: 2.5em;
}

.subtitle {
    margin: 0;
    opacity: 0.9;
    font-size: 1.1em;
}

.config-section {
    background: white;
    border-radius: 8px;
    padding: 25px;
    margin-bottom: 25px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: 1px solid #e1e5e9;
}

.config-section h2 {
    margin: 0 0 20px 0;
    color: #2c3e50;
    font-size: 1.5em;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.config-section h3 {
    margin: 0 0 15px 0;
    color: #34495e;
    font-size: 1.2em;
}

/* 配置表格样式 */
.config-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 20px;
}

.config-table th,
.config-table td {
    padding: 12px;
    text-align: center;
    border: 1px solid #ddd;
}

.config-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.config-table tr:nth-child(even) {
    background-color: #f8f9fa;
}

.config-table tr:hover {
    background-color: #e9ecef;
}

.config-table input[type="number"] {
    width: 80px;
    padding: 6px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    text-align: center;
}

.config-table input[type="number"]:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* 批量创建器样式 */
.batch-creator {
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
    background-color: #f8f9fa;
}

.creator-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: white;
    border-radius: 6px;
    border: 1px solid #dee2e6;
}

.control-group {
    display: flex;
    align-items: center;
    gap: 10px;
}

.control-group label {
    font-weight: 600;
    color: #495057;
}

.control-group select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

/* 按钮样式 */
.btn {
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-info:hover {
    background-color: #117a8b;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn-success:hover {
    background-color: #1e7e34;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #bd2130;
}

/* 输入区域样式 */
.input-section {
    background: white;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #dee2e6;
}

.input-section textarea {
    width: 100%;
    padding: 15px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
    line-height: 1.4;
    resize: vertical;
}

.input-section textarea:focus {
    outline: none;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

.example-box {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin: 10px 0;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    line-height: 1.6;
    border-left: 4px solid #007bff;
}

/* 模板过滤器样式 */
.template-filters {
    display: flex;
    gap: 20px;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.template-filters label {
    font-weight: 600;
    color: #495057;
}

.template-filters select {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
}

/* 模板列表样式 */
.template-list {
    max-height: 400px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
}

.template-item {
    padding: 15px;
    border-bottom: 1px solid #dee2e6;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.template-item:hover {
    background-color: #f8f9fa;
}

.template-item.selected {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
}

.template-item h4 {
    margin: 0 0 8px 0;
    color: #2c3e50;
}

.template-item .template-meta {
    display: flex;
    gap: 15px;
    font-size: 12px;
    color: #6c757d;
}

.template-item .template-meta span {
    background-color: #e9ecef;
    padding: 2px 8px;
    border-radius: 12px;
}

/* 导入控制样式 */
.import-controls {
    margin-top: 15px;
    display: flex;
    gap: 10px;
    align-items: center;
}

/* 预览区域样式 */
.preview-content {
    max-height: 500px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
    background-color: #f8f9fa;
}

.preview-controls {
    margin-top: 20px;
    text-align: center;
}

.preview-controls .btn {
    margin: 0 10px;
}

.preview-item {
    background: white;
    border: 1px solid #ddd;
    padding: 15px;
    margin: 10px 0;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.preview-item h4 {
    margin: 0 0 10px 0;
    color: #2c3e50;
}

.preview-item .item-stats {
    background: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
    font-family: monospace;
    font-size: 12px;
}

.preview-item .stat-line {
    margin: 2px 0;
}

/* 结果显示样式 */
.result-content {
    padding: 20px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    background-color: #f8f9fa;
}

.result-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.result-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.result-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .creator-controls {
        flex-direction: column;
        gap: 15px;
    }
    
    .control-group {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .template-filters {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .config-table {
        font-size: 12px;
    }
    
    .config-table input[type="number"] {
        width: 60px;
    }
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 批量选择控制按钮样式 */
.batch-select-controls {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background-color: #e9ecef;
    border-radius: 6px;
    flex-wrap: wrap;
}

.batch-select-controls .btn-sm {
    padding: 5px 10px;
    font-size: 12px;
    border-radius: 3px;
}

.selected-count {
    font-weight: 600;
    color: #495057;
    margin-left: auto;
}

.selected-count span {
    color: #007bff;
    font-weight: 700;
}

/* 模板项选择状态 */
.template-item.selected {
    background-color: #e3f2fd;
    border-color: #2196f3;
    box-shadow: 0 0 0 2px rgba(33, 150, 243, 0.2);
}

.template-item.selected .template-checkbox {
    background-color: #2196f3;
    border-color: #2196f3;
}

.template-item.selected .template-checkbox::after {
    content: '✓';
    color: white;
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 模板复选框样式 */
.template-checkbox {
    width: 18px;
    height: 18px;
    border: 2px solid #ced4da;
    border-radius: 3px;
    background-color: white;
    cursor: pointer;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.template-checkbox:hover {
    border-color: #007bff;
}

/* 怪物装备特殊样式 */
.monster-badge {
    background-color: #dc3545;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    font-weight: normal;
    margin-left: 8px;
}

.monster-type {
    background-color: #343a40 !important;
    color: #ffc107 !important;
    font-size: 12px;
    padding: 4px 10px;
    border-radius: 4px;
    font-weight: 600;
    margin-left: 8px;
    display: inline-block;
    border: 1px solid #495057;
}
