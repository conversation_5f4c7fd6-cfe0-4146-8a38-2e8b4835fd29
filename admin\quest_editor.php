<?php
// admin/quest_editor.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

$pageTitle = '任务编辑器';
$currentPage = 'quests';

// 初始化变量
$success_message = '';
$error_message = '';
$quest = null;
$quest_objectives = [];
$npc_templates = [];
$monster_templates = [];
$item_templates = [];
$all_quests = [];
$quest_groups = [];
$is_new = true;

// 检查是否提供了任务ID
if (isset($_GET['id']) && !empty($_GET['id'])) {
    $quest_id = (int)$_GET['id'];
    $is_new = false;
} else {
    // 处理新建任务的表单提交
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        try {
            $pdo = Database::getInstance()->getConnection();
            
            // 获取表单数据
            $title = $_POST['title'] ?? '';
            $description = $_POST['description'] ?? '';
            $type = $_POST['type'] ?? 'dialogue';
            $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null;
            $giver_npc_id = isset($_POST['giver_npc_id']) ? (int)$_POST['giver_npc_id'] : null;
            $receiver_npc_id = !empty($_POST['receiver_npc_id']) ? (int)$_POST['receiver_npc_id'] : null;
            $min_level = isset($_POST['min_level']) ? (int)$_POST['min_level'] : 1;
            $is_repeatable = isset($_POST['is_repeatable']) ? 1 : 0;
            
            // 插入新任务
            $stmt = $pdo->prepare("
                INSERT INTO quests 
                (title, group_id, description, type, giver_npc_id, receiver_npc_id, min_level, is_repeatable)
                VALUES 
                (:title, :group_id, :description, :type, :giver_npc_id, :receiver_npc_id, :min_level, :is_repeatable)
            ");
            
            $stmt->execute([
                ':title' => $title,
                ':group_id' => $group_id,
                ':description' => $description,
                ':type' => $type,
                ':giver_npc_id' => $giver_npc_id,
                ':receiver_npc_id' => $receiver_npc_id,
                ':min_level' => $min_level,
                ':is_repeatable' => $is_repeatable
            ]);
            
            $quest_id = $pdo->lastInsertId();
            
            // 重定向到编辑页面
            header("Location: quest_editor.php?id=$quest_id&created=1");
            exit;
        } catch (PDOException $e) {
            $error_message = "创建任务失败: " . $e->getMessage();
        }
    }
}

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有任务分组
    $stmt = $pdo->query("SELECT * FROM quest_groups ORDER BY sort_order, name");
    $quest_groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有NPC模板
    $stmt = $pdo->query("SELECT id, name FROM npc_templates ORDER BY name");
    $npc_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有怪物模板
    $stmt = $pdo->query("SELECT id, name FROM monster_templates ORDER BY name");
    $monster_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有物品模板
    $stmt = $pdo->query("SELECT id, name, category FROM item_templates ORDER BY name");
    $item_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有任务 (用于前置任务选择)
    $stmt = $pdo->query("SELECT id, title, min_level FROM quests ORDER BY min_level, title");
    $all_quests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (!$is_new) {
        // 获取任务信息
        $stmt = $pdo->prepare("
            SELECT q.*, 
                   giver.name AS giver_name,
                   receiver.name AS receiver_name,
                   qg.name AS group_name
            FROM quests q
            LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
            LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
            LEFT JOIN quest_groups qg ON q.group_id = qg.id
            WHERE q.id = :id
        ");
        $stmt->execute([':id' => $quest_id]);
        $quest = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$quest) {
            $error_message = "找不到指定的任务";
        } else {
            // 获取任务目标
            $stmt = $pdo->prepare("
                SELECT qo.*,
                       CASE 
                           WHEN qo.type = 'dialogue' OR qo.type = 'visit' THEN npc.name
                           WHEN qo.type = 'kill' THEN monster.name
                           WHEN qo.type = 'collect' THEN item.name
                           ELSE NULL
                       END AS target_name
                FROM quest_objectives qo
                LEFT JOIN npc_templates npc ON qo.type IN ('dialogue', 'visit') AND qo.target_id = npc.id
                LEFT JOIN monster_templates monster ON qo.type = 'kill' AND qo.target_id = monster.id
                LEFT JOIN item_templates item ON qo.type = 'collect' AND qo.target_id = item.id
                WHERE qo.quest_id = :quest_id
                ORDER BY qo.id
            ");
            $stmt->execute([':quest_id' => $quest_id]);
            $quest_objectives = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 处理任务更新
            if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
                if ($_POST['action'] === 'update_quest') {
                    // 更新任务基本信息
                    $title = $_POST['title'] ?? '';
                    $description = $_POST['description'] ?? '';
                    $type = $_POST['type'] ?? 'dialogue';
                    $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null;
                    $giver_npc_id = isset($_POST['giver_npc_id']) ? (int)$_POST['giver_npc_id'] : null;
                    $receiver_npc_id = !empty($_POST['receiver_npc_id']) ? (int)$_POST['receiver_npc_id'] : null;
                    $min_level = isset($_POST['min_level']) ? (int)$_POST['min_level'] : 1;
                    $is_repeatable = isset($_POST['is_repeatable']) ? 1 : 0;
                    $reward_gold = isset($_POST['reward_gold']) ? (int)$_POST['reward_gold'] : 0;
                    $reward_exp = isset($_POST['reward_exp']) ? (int)$_POST['reward_exp'] : 0;
                    $reward_items = isset($_POST['reward_items']) ? $_POST['reward_items'] : null;
                    $prerequisite_quests = isset($_POST['prerequisite_quests']) ? $_POST['prerequisite_quests'] : null;
                    
                    $stmt = $pdo->prepare("
                        UPDATE quests 
                        SET title = :title,
                            description = :description,
                            type = :type,
                            group_id = :group_id,
                            giver_npc_id = :giver_npc_id,
                            receiver_npc_id = :receiver_npc_id,
                            min_level = :min_level,
                            is_repeatable = :is_repeatable,
                            reward_gold = :reward_gold,
                            reward_exp = :reward_exp,
                            reward_items = :reward_items,
                            prerequisite_quests = :prerequisite_quests,
                            updated_at = NOW()
                        WHERE id = :id
                    ");
                    
                    $stmt->execute([
                        ':title' => $title,
                        ':description' => $description,
                        ':type' => $type,
                        ':group_id' => $group_id,
                        ':giver_npc_id' => $giver_npc_id,
                        ':receiver_npc_id' => $receiver_npc_id,
                        ':min_level' => $min_level,
                        ':is_repeatable' => $is_repeatable,
                        ':reward_gold' => $reward_gold,
                        ':reward_exp' => $reward_exp,
                        ':reward_items' => $reward_items,
                        ':prerequisite_quests' => $prerequisite_quests,
                        ':id' => $quest_id
                    ]);
                    
                    $success_message = "任务信息已更新！";
                    
                    // 重新获取任务信息
                    $stmt = $pdo->prepare("
                        SELECT q.*, 
                               giver.name AS giver_name,
                               receiver.name AS receiver_name,
                               qg.name AS group_name
                        FROM quests q
                        LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
                        LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
                        LEFT JOIN quest_groups qg ON q.group_id = qg.id
                        WHERE q.id = :id
                    ");
                    $stmt->execute([':id' => $quest_id]);
                    $quest = $stmt->fetch(PDO::FETCH_ASSOC);
                    
                } elseif ($_POST['action'] === 'add_objective') {
                    // 添加任务目标
                    $objective_type = $_POST['objective_type'] ?? '';
                    $target_id = isset($_POST['target_id']) ? (int)$_POST['target_id'] : null;
                    $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
                    $description = $_POST['objective_description'] ?? '';
                    
                    $stmt = $pdo->prepare("
                        INSERT INTO quest_objectives 
                        (quest_id, type, target_id, quantity, description)
                        VALUES 
                        (:quest_id, :type, :target_id, :quantity, :description)
                    ");
                    
                    $stmt->execute([
                        ':quest_id' => $quest_id,
                        ':type' => $objective_type,
                        ':target_id' => $target_id,
                        ':quantity' => $quantity,
                        ':description' => $description
                    ]);
                    
                    $success_message = "任务目标已添加！";
                    
                    // 重新获取任务目标
                    $stmt = $pdo->prepare("
                        SELECT qo.*,
                               CASE 
                                   WHEN qo.type = 'dialogue' OR qo.type = 'visit' THEN npc.name
                                   WHEN qo.type = 'kill' THEN monster.name
                                   WHEN qo.type = 'collect' THEN item.name
                                   ELSE NULL
                               END AS target_name
                        FROM quest_objectives qo
                        LEFT JOIN npc_templates npc ON qo.type IN ('dialogue', 'visit') AND qo.target_id = npc.id
                        LEFT JOIN monster_templates monster ON qo.type = 'kill' AND qo.target_id = monster.id
                        LEFT JOIN item_templates item ON qo.type = 'collect' AND qo.target_id = item.id
                        WHERE qo.quest_id = :quest_id
                        ORDER BY qo.id
                    ");
                    $stmt->execute([':quest_id' => $quest_id]);
                    $quest_objectives = $stmt->fetchAll(PDO::FETCH_ASSOC);
                    
                } elseif ($_POST['action'] === 'delete_objective' && isset($_POST['objective_id'])) {
                    // 删除任务目标
                    $objective_id = (int)$_POST['objective_id'];
                    
                    $stmt = $pdo->prepare("
                        DELETE FROM quest_objectives 
                        WHERE id = :id AND quest_id = :quest_id
                    ");
                    
                    $stmt->execute([
                        ':id' => $objective_id,
                        ':quest_id' => $quest_id
                    ]);
                    
                    $success_message = "任务目标已删除！";
                    
                    // 重新获取任务目标
                    $stmt = $pdo->prepare("
                        SELECT qo.*,
                               CASE 
                                   WHEN qo.type = 'dialogue' OR qo.type = 'visit' THEN npc.name
                                   WHEN qo.type = 'kill' THEN monster.name
                                   WHEN qo.type = 'collect' THEN item.name
                                   ELSE NULL
                               END AS target_name
                        FROM quest_objectives qo
                        LEFT JOIN npc_templates npc ON qo.type IN ('dialogue', 'visit') AND qo.target_id = npc.id
                        LEFT JOIN monster_templates monster ON qo.type = 'kill' AND qo.target_id = monster.id
                        LEFT JOIN item_templates item ON qo.type = 'collect' AND qo.target_id = item.id
                        WHERE qo.quest_id = :quest_id
                        ORDER BY qo.id
                    ");
                    $stmt->execute([':quest_id' => $quest_id]);
                    $quest_objectives = $stmt->fetchAll(PDO::FETCH_ASSOC);
                }
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 检查是否刚创建了任务
if (isset($_GET['created']) && $_GET['created'] == 1) {
    $success_message = "任务创建成功！请继续编辑任务详情。";
}

// 引入页面头部
$extra_css = '
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<style>
    .quest-form {
        background: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        margin-bottom: 20px;
    }
    .objectives-section {
        background: #fff;
        padding: 20px;
        border-radius: 5px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    }
    .objective-item {
        padding: 10px;
        border: 1px solid #eee;
        margin-bottom: 10px;
        border-radius: 4px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .objective-item:hover {
        background-color: #f9f9f9;
    }
    .objective-type {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 0.8em;
        margin-right: 5px;
        background-color: #e9ecef;
    }
    .objective-type.dialogue { background-color: #c8e6c9; }
    .objective-type.collect { background-color: #ffecb3; }
    .objective-type.kill { background-color: #ffcdd2; }
    .objective-type.visit { background-color: #bbdefb; }
    
    .target-select-container {
        display: none;
    }
    .target-select-container.active {
        display: block;
    }
    .rewards-section {
        margin-top: 20px;
        border-top: 1px solid #eee;
        padding-top: 20px;
    }
    
    /* 物品奖励样式 */
    .reward-item-row {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
        padding: 8px;
        background-color: #f9f9f9;
        border-radius: 4px;
        border: 1px solid #eee;
        flex-wrap: wrap;
        gap: 10px;
    }
    .reward-item-row select {
        flex: 1;
        min-width: 200px;
        max-width: 60%;
    }
    .reward-item-row input {
        width: 80px;
        margin: 0;
    }
    .reward-item-row button {
        white-space: nowrap;
    }
    .reward-items-container {
        margin-bottom: 15px;
    }
    
    /* 调整Select2在物品奖励行中的宽度 */
    .reward-item-row .select2-container {
        width: auto !important;
        min-width: 200px !important;
        max-width: 60% !important;
    }
    
    /* Select2样式调整 */
    .select2-container {
        width: 100% !important;
    }
    .select2-selection--multiple {
        border-color: #ddd !important;
    }
</style>';

require_once 'layout_header.php';
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <div class="content-header">
        <h2><?php echo $is_new ? '创建新任务' : '编辑任务'; ?></h2>
        <a href="quests.php" class="btn">返回任务列表</a>
    </div>
    
    <?php if (!$is_new && $quest): ?>
        <div class="quest-form">
            <form method="post" action="quest_editor.php?id=<?php echo $quest_id; ?>">
                <input type="hidden" name="action" value="update_quest">
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="title">任务名称</label>
                        <input type="text" id="title" name="title" value="<?php echo htmlspecialchars($quest['title']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="type">任务类型</label>  
                        <select id="type" name="type" required>
                            <option value="dialogue" <?php echo $quest['type'] === 'dialogue' ? 'selected' : ''; ?>>对话</option>
                            <option value="collection" <?php echo $quest['type'] === 'collection' ? 'selected' : ''; ?>>收集</option>
                            <option value="kill" <?php echo $quest['type'] === 'kill' ? 'selected' : ''; ?>>击杀</option>
                            <!-- <option value="escort" <?php echo $quest['type'] === 'escort' ? 'selected' : ''; ?>>护送</option>
                            <option value="exploration" <?php echo $quest['type'] === 'exploration' ? 'selected' : ''; ?>>探索</option> -->
                        </select>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="group_id">所属分组</label>
                    <select id="group_id" name="group_id">
                        <option value="">-- 选择分组 --</option>
                        <?php foreach ($quest_groups as $group): ?>
                            <option value="<?php echo $group['id']; ?>" <?php echo $quest['group_id'] == $group['id'] ? 'selected' : ''; ?>>
                                <?php echo htmlspecialchars($group['name']); ?> (<?php echo $group['type']; ?>)
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <small>为任务选择合适的分组可以更好地组织管理</small>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="giver_npc_id">发布NPC</label>
                        <select id="giver_npc_id" name="giver_npc_id" required>
                            <option value="">-- 选择发布NPC --</option>
                            <?php foreach ($npc_templates as $npc): ?>
                                <option value="<?php echo $npc['id']; ?>" <?php echo $quest['giver_npc_id'] == $npc['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($npc['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="receiver_npc_id">接收NPC (可选)</label>
                        <select id="receiver_npc_id" name="receiver_npc_id">
                            <option value="">-- 选择接收NPC --</option>
                            <?php foreach ($npc_templates as $npc): ?>
                                <option value="<?php echo $npc['id']; ?>" <?php echo $quest['receiver_npc_id'] == $npc['id'] ? 'selected' : ''; ?>>
                                    <?php echo htmlspecialchars($npc['name']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="min_level">最低等级要求</label>
                        <input type="number" id="min_level" name="min_level" min="1" value="<?php echo $quest['min_level']; ?>" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="is_repeatable" name="is_repeatable" value="1" <?php echo $quest['is_repeatable'] ? 'checked' : ''; ?>>
                            可重复任务
                        </label>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="description">任务描述</label>
                    <textarea id="description" name="description" rows="4"><?php echo htmlspecialchars($quest['description']); ?></textarea>
                </div>
                
                <div class="rewards-section">
                    <h3>任务奖励</h3>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="reward_gold">金币奖励</label>
                            <input type="number" id="reward_gold" name="reward_gold" min="0" value="<?php echo $quest['reward_gold']; ?>">
                        </div>
                        <div class="form-group">
                            <label for="reward_exp">经验奖励</label>
                            <input type="number" id="reward_exp" name="reward_exp" min="0" value="<?php echo $quest['reward_exp']; ?>">
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>物品奖励</label>
                        <div class="reward-items-container" id="reward-items-container">
                            <?php 
                            $reward_items = [];
                            if (!empty($quest['reward_items'])) {
                                $reward_items = json_decode($quest['reward_items'], true) ?: [];
                            }
                            
                            if (!empty($reward_items)) {
                                foreach ($reward_items as $item) {
                                    $item_id = $item['id'] ?? 0;
                                    $quantity = $item['quantity'] ?? 1;
                                    ?>
                                    <div class="reward-item-row">
                                        <select name="reward_item_ids[]" class="item-select">
                                            <option value="">-- 选择物品 --</option>
                                            <?php foreach ($item_templates as $template): ?>
                                                <option value="<?php echo $template['id']; ?>" <?php echo $item_id == $template['id'] ? 'selected' : ''; ?>>
                                                    <?php echo htmlspecialchars($template['name']); ?> (<?php echo $template['category']; ?>)
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                        <input type="number" name="reward_item_quantities[]" min="1" value="<?php echo $quantity; ?>" placeholder="数量">
                                        <button type="button" class="btn btn-sm btn-danger remove-reward-item">删除</button>
                                    </div>
                                    <?php
                                }
                            }
                            ?>
                        </div>
                        <button type="button" id="add-reward-item" class="btn btn-sm">添加物品奖励</button>
                    </div>
                    
                    <div class="form-group">
                        <label for="prerequisite_quests">前置任务</label>
                        <select id="prerequisite_quests" name="prerequisite_quests[]" multiple class="prerequisite-select">
                            <?php 
                            $prerequisite_quests = [];
                            if (!empty($quest['prerequisite_quests'])) {
                                $prerequisite_quests = json_decode($quest['prerequisite_quests'], true) ?: [];
                            }
                            
                            foreach ($all_quests as $q): 
                                // 不能选择自己作为前置任务
                                if ($q['id'] == $quest_id) continue;
                            ?>
                                <option value="<?php echo $q['id']; ?>" <?php echo in_array($q['id'], $prerequisite_quests) ? 'selected' : ''; ?>>
                                    [Lv.<?php echo $q['min_level']; ?>] <?php echo htmlspecialchars($q['title']); ?>
                                </option>
                            <?php endforeach; ?>
                        </select>
                        <small>可以选择多个前置任务，玩家需要完成所有前置任务才能接取此任务</small>
                    </div>
                </div>
                
                <input type="hidden" name="reward_items" id="reward_items_json">
                <input type="hidden" name="prerequisite_quests" id="prerequisite_quests_json">
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存任务</button>
                </div>
            </form>
        </div>
        
        <div class="objectives-section">
            <h3>任务目标</h3>
            
            <form method="post" action="quest_editor.php?id=<?php echo $quest_id; ?>" class="mb-4">
                <input type="hidden" name="action" value="add_objective">
                
                <div class="form-row">
                    <div class="form-group">    
                        <label for="objective_type">目标类型</label>
                        <select id="objective_type" name="objective_type" required>
                            <option value="dialogue">对话</option>
                            <option value="collect">收集</option>
                            <option value="kill">击杀</option>
                            <!-- <option value="visit">访问</option> -->
                        </select>
                    </div>
                    
                    <div class="form-group target-select-container" id="dialogue-target" data-type="dialogue">
                        <label for="dialogue_target_id">对话目标NPC</label>
                        <select id="dialogue_target_id" name="target_id" class="target-select">
                            <option value="">-- 选择NPC --</option>
                            <?php foreach ($npc_templates as $npc): ?>
                                <option value="<?php echo $npc['id']; ?>"><?php echo htmlspecialchars($npc['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group target-select-container" id="collect-target" data-type="collect">
                        <label for="collect_target_id">收集物品</label>
                        <select id="collect_target_id" name="target_id" class="target-select">
                            <option value="">-- 选择物品 --</option>
                            <?php foreach ($item_templates as $item): ?>
                                <option value="<?php echo $item['id']; ?>"><?php echo htmlspecialchars($item['name']); ?> (<?php echo $item['category']; ?>)</option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <div class="form-group target-select-container" id="kill-target" data-type="kill">
                        <label for="kill_target_id">击杀怪物</label>
                        <select id="kill_target_id" name="target_id" class="target-select">
                            <option value="">-- 选择怪物 --</option>
                            <?php foreach ($monster_templates as $monster): ?>
                                <option value="<?php echo $monster['id']; ?>"><?php echo htmlspecialchars($monster['name']); ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                    
                    <!-- <div class="form-group target-select-container" id="visit-target" data-type="visit"> -->
                        <!-- <label for="visit_target_id">访问NPC</label> -->
                        <!-- <select id="visit_target_id" name="target_id" class="target-select"> -->
                            <!-- <option value="">-- 选择NPC --</option> -->
                            <!-- <?php foreach ($npc_templates as $npc): ?> -->
                                <!-- <option value="<?php echo $npc['id']; ?>"><?php echo htmlspecialchars($npc['name']); ?></option> -->
                            <!-- <?php endforeach; ?> -->
                        <!-- </select> -->
                    <!-- </div> -->
                    
                    <div class="form-group">
                        <label for="quantity">数量</label>
                        <input type="number" id="quantity" name="quantity" min="1" value="1" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="objective_description">目标描述 (可选)</label>
                    <textarea id="objective_description" name="objective_description" rows="2"></textarea>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">添加目标</button>
                </div>
            </form>
            
            <div class="objectives-list">
                <h4>当前任务目标</h4>
                
                <?php if (empty($quest_objectives)): ?>
                    <p class="empty-state">此任务还没有添加任务目标</p>
                <?php else: ?>
                    <?php foreach ($quest_objectives as $objective): ?>
                        <div class="objective-item">
                            <div>
                                <span class="objective-type <?php echo $objective['type']; ?>">
                                    <?php
                                    switch ($objective['type']) {
                                        case 'dialogue': echo '对话'; break;
                                        case 'collect': echo '收集'; break;
                                        case 'kill': echo '击杀'; break;
                                        // case 'visit': echo '访问'; break; // 注释掉访问类型，因为访问任务目前没有实现
                                        default: echo ucfirst($objective['type']);
                                    }
                                    ?>
                                </span>
                                <?php if ($objective['target_name']): ?>
                                    <strong><?php echo htmlspecialchars($objective['target_name']); ?></strong>
                                <?php endif; ?>
                                
                                <?php if ($objective['quantity'] > 1): ?>
                                    x <?php echo $objective['quantity']; ?>
                                <?php endif; ?>
                                
                                <?php if (!empty($objective['description'])): ?>
                                    - <?php echo htmlspecialchars($objective['description']); ?>
                                <?php endif; ?>
                            </div>
                            
                            <form method="post" action="quest_editor.php?id=<?php echo $quest_id; ?>" class="inline-form" onsubmit="return confirm('确定要删除此任务目标吗？');">
                                <input type="hidden" name="action" value="delete_objective">
                                <input type="hidden" name="objective_id" value="<?php echo $objective['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">删除</button>
                            </form>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    <?php elseif ($is_new): ?>
        <div class="alert alert-info">
            请先创建一个新任务。
        </div>
    <?php endif; ?>
</div>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Select2
    $('.prerequisite-select').select2({
        placeholder: '选择前置任务',
        allowClear: true
    });
    
    // 初始化物品选择器
    $('.item-select').select2();
    
    // 处理目标类型选择
    const objectiveTypeSelect = document.getElementById('objective_type');
    const targetContainers = document.querySelectorAll('.target-select-container');
    const targetSelects = document.querySelectorAll('.target-select');
    
    function updateTargetSelect() {
        const selectedType = objectiveTypeSelect.value;
        
        // 隐藏所有目标选择器
        targetContainers.forEach(container => {
            container.classList.remove('active');
        });
        
        // 禁用所有目标选择器
        targetSelects.forEach(select => {
            select.disabled = true;
            select.required = false;
        });
        
        // 显示对应的目标选择器
        const activeContainer = document.querySelector(`.target-select-container[data-type="${selectedType}"]`);
        if (activeContainer) {
            activeContainer.classList.add('active');
            const activeSelect = activeContainer.querySelector('select');
            activeSelect.disabled = false;
            activeSelect.required = true;
        }
    }
    
    // 初始化目标选择器
    if (objectiveTypeSelect) {
        updateTargetSelect();
        objectiveTypeSelect.addEventListener('change', updateTargetSelect);
    }
    
    // 添加物品奖励
    const addRewardItemBtn = document.getElementById('add-reward-item');
    const rewardItemsContainer = document.getElementById('reward-items-container');
    
    if (addRewardItemBtn) {
        addRewardItemBtn.addEventListener('click', function() {
            const newRow = document.createElement('div');
            newRow.className = 'reward-item-row';
            
            const itemSelect = document.createElement('select');
            itemSelect.name = 'reward_item_ids[]';
            itemSelect.className = 'item-select';
            itemSelect.innerHTML = `
                <option value="">-- 选择物品 --</option>
                <?php foreach ($item_templates as $item): ?>
                <option value="<?php echo $item['id']; ?>"><?php echo htmlspecialchars($item['name']); ?> (<?php echo $item['category']; ?>)</option>
                <?php endforeach; ?>
            `;
            
            const quantityInput = document.createElement('input');
            quantityInput.type = 'number';
            quantityInput.name = 'reward_item_quantities[]';
            quantityInput.min = '1';
            quantityInput.value = '1';
            quantityInput.placeholder = '数量';
            
            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'btn btn-sm btn-danger remove-reward-item';
            removeBtn.textContent = '删除';
            removeBtn.addEventListener('click', function() {
                newRow.remove();
            });
            
            newRow.appendChild(itemSelect);
            newRow.appendChild(quantityInput);
            newRow.appendChild(removeBtn);
            
            rewardItemsContainer.appendChild(newRow);
            
            // 初始化新添加的Select2
            $(itemSelect).select2();
        });
    }
    
    // 删除物品奖励
    document.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-reward-item')) {
            e.target.closest('.reward-item-row').remove();
        }
    });
    
    // 表单提交前处理数据
    const questForm = document.querySelector('form[action*="quest_editor.php"]');
    
    if (questForm) {
        questForm.addEventListener('submit', function(e) {
            // 处理物品奖励数据
            const rewardItemIds = Array.from(document.querySelectorAll('select[name="reward_item_ids[]"]')).map(select => parseInt(select.value));
            const rewardItemQuantities = Array.from(document.querySelectorAll('input[name="reward_item_quantities[]"]')).map(input => parseInt(input.value));
            
            const rewardItems = [];
            for (let i = 0; i < rewardItemIds.length; i++) {
                if (rewardItemIds[i]) {
                    rewardItems.push({
                        id: rewardItemIds[i],
                        quantity: rewardItemQuantities[i] || 1
                    });
                }
            }
            
            document.getElementById('reward_items_json').value = JSON.stringify(rewardItems);
            
            // 处理前置任务数据
            const prerequisiteSelect = document.querySelector('.prerequisite-select');
            if (prerequisiteSelect) {
                const selectedValues = Array.from(prerequisiteSelect.selectedOptions).map(option => parseInt(option.value));
                document.getElementById('prerequisite_quests_json').value = JSON.stringify(selectedValues);
            }
        });
    }
});
</script>

<?php require_once 'layout_footer.php'; ?> 