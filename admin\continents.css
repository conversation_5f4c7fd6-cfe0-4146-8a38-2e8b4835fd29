/* 大陆区域管理页面样式 */

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.header-section h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* 大陆网格布局 */
.continents-section {
    margin-bottom: 40px;
}

.continents-section h2 {
    color: #34495e;
    margin-bottom: 20px;
    font-size: 1.5rem;
}

.continents-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
}

.continent-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
}

.continent-card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
    transform: translateY(-2px);
}

.continent-card.inactive {
    opacity: 0.6;
    background: #f8f9fa;
}

.continent-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.continent-title {
    font-size: 1.2rem;
    font-weight: bold;
    margin: 0;
    color: #2c3e50;
}

.continent-id {
    font-size: 0.9rem;
    color: #6c757d;
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 6px;
    border-radius: 4px;
}

.continent-color {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 1px #dee2e6;
}

.continent-description {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.4;
    margin-bottom: 15px;
    min-height: 40px;
}

.continent-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 4px;
}

.stat-item {
    text-align: center;
}

.stat-value {
    font-size: 1.2rem;
    font-weight: bold;
    color: #007bff;
}

.stat-label {
    font-size: 0.8rem;
    color: #6c757d;
}

.continent-actions {
    display: flex;
    gap: 8px;
    justify-content: flex-end;
}

/* 区域管理区域 */
.zones-section {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
}

/* 场景管理区域 */
.scenes-section {
    background: #f1f3f4;
    border-radius: 8px;
    padding: 20px;
    margin-top: 30px;
}

.zones-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.zones-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.4rem;
}

.zones-actions {
    display: flex;
    gap: 10px;
}

.zones-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
}

.zone-card {
    background: #fff;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 15px;
    transition: all 0.3s ease;
}

.zone-card:hover {
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
    transform: translateY(-1px);
}

.zone-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.zone-title {
    font-size: 1.1rem;
    font-weight: bold;
    margin: 0;
    color: #2c3e50;
}

.zone-id {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: monospace;
    background: #f8f9fa;
    padding: 2px 4px;
    border-radius: 3px;
}

.zone-description {
    color: #6c757d;
    font-size: 0.85rem;
    line-height: 1.3;
    margin-bottom: 10px;
    min-height: 30px;
}

.zone-level-range {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding: 5px 8px;
    background: #e9ecef;
    border-radius: 4px;
    font-size: 0.85rem;
}

.zone-stats {
    display: flex;
    justify-content: center;
    margin-bottom: 10px;
    padding: 8px;
    background: #e9ecef;
    border-radius: 4px;
}

.zone-actions {
    display: flex;
    gap: 5px;
    justify-content: flex-end;
}

/* 场景地图样式 */
.scenes-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #dee2e6;
}

.scenes-header h2 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.4rem;
}

.scenes-actions {
    display: flex;
    gap: 10px;
}

.scenes-map-container {
    margin-bottom: 30px;
}

.map-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 15px;
    padding: 10px;
    background: #fff;
    border-radius: 6px;
    border: 1px solid #dee2e6;
    flex-wrap: wrap;
}

.map-controls label {
    font-weight: bold;
    color: #495057;
}

.map-controls input,
.map-controls select {
    padding: 4px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.control-divider {
    color: #6c757d;
    margin: 0 10px;
}

.scenes-map-grid {
    background: #fff;
    border: 2px solid #dee2e6;
    border-radius: 6px;
    padding: 10px;
    overflow: auto;
    max-height: 600px;
    position: relative;
}

.scene-grid {
    display: inline-grid;
    gap: 1px;
    background: #e9ecef;
    border: 1px solid #adb5bd;
}

.scene-cell {
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: #6c757d;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
}

.scene-cell:hover {
    background: #e9ecef;
    transform: scale(1.1);
    z-index: 10;
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}

.scene-cell.has-scene {
    background: #007bff;
    color: white;
    border-color: #0056b3;
}

.scene-cell.has-scene:hover {
    background: #0056b3;
}

.scene-cell.safe-zone {
    background: #28a745;
    border-color: #1e7e34;
}

.scene-cell.safe-zone:hover {
    background: #1e7e34;
}

.scene-tooltip {
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 100;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.2s ease;
}

.scene-cell:hover .scene-tooltip {
    opacity: 1;
}

/* 场景列表样式 */
.scenes-list-container {
    background: #fff;
    border-radius: 6px;
    padding: 20px;
    border: 1px solid #dee2e6;
}

.scenes-stats-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.scenes-stats-header h3 {
    margin: 0;
    color: #2c3e50;
    font-size: 1.2rem;
}

.scenes-stats {
    display: flex;
    gap: 20px;
}

.scenes-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.scene-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 12px;
    transition: all 0.2s ease;
}

.scene-item:hover {
    background: #e9ecef;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.scene-item.safe-zone {
    border-left: 4px solid #28a745;
}

.scene-name {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 5px;
}

.scene-id {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: monospace;
    background: #fff;
    padding: 2px 4px;
    border-radius: 3px;
    display: inline-block;
    margin-bottom: 5px;
}

.scene-coordinates {
    font-size: 0.85rem;
    color: #495057;
    margin-bottom: 5px;
}

.scene-description {
    font-size: 0.8rem;
    color: #6c757d;
    line-height: 1.3;
    max-height: 40px;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fff;
    margin: 5% auto;
    padding: 0;
    border-radius: 8px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #2c3e50;
}

.close {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    line-height: 1;
}

.close:hover {
    color: #000;
}

.modal form {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #2c3e50;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 0.8rem;
    color: #6c757d;
}

/* 输入组样式 */
.input-group {
    display: flex;
    align-items: stretch;
}

.input-group input {
    flex: 1;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    border-right: none;
}

.input-group .btn {
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: 1px solid #ced4da;
    white-space: nowrap;
    font-size: 12px;
    padding: 8px 12px;
}

.input-group .btn:not(:last-child) {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
}

.input-group .btn:last-child {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
}

.input-group .btn:hover {
    background-color: #e2e6ea;
    border-color: #dae0e5;
}

/* ID输入框只读状态样式 */
input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

input[readonly]:focus {
    background-color: #f8f9fa;
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #dee2e6;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
    text-align: center;
    transition: all 0.3s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-warning {
    background-color: #ffc107;
    color: #212529;
}

.btn-warning:hover {
    background-color: #e0a800;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

/* 加载和空状态 */
.loading-placeholder {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.empty-state {
    text-align: center;
    padding: 40px;
    color: #6c757d;
}

.empty-state h3 {
    margin-bottom: 10px;
    color: #495057;
}

/* Toast 通知 - 使用更高特异性覆盖style.css */
#toast.toast,
.toast#toast {
    position: fixed !important;
    top: 20px !important;
    right: 20px !important;
    left: auto !important;
    bottom: auto !important;
    padding: 12px 20px !important;
    border-radius: 4px !important;
    color: white !important;
    font-weight: bold !important;
    font-size: 14px !important;
    max-width: 300px !important;
    min-width: 200px !important;
    width: auto !important;
    word-wrap: break-word !important;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3) !important;
    z-index: 1001 !important;
    transform: translateX(400px) !important;
    opacity: 0 !important;
    visibility: visible !important;
    transition: all 0.3s ease !important;
}

#toast.toast.show,
.toast#toast.show {
    transform: translateX(0) !important;
    opacity: 1 !important;
    visibility: visible !important;
}

#toast.toast.success,
.toast#toast.success {
    background-color: #28a745 !important;
}

#toast.toast.error,
.toast#toast.error {
    background-color: #dc3545 !important;
}

#toast.toast.warning,
.toast#toast.warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

.warning-text {
    color: #dc3545;
    font-size: 0.9rem;
    margin: 10px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }

    .header-section {
        flex-direction: column;
        gap: 15px;
        align-items: stretch;
    }

    .continents-grid,
    .zones-grid,
    .scenes-list {
        grid-template-columns: 1fr;
    }

    .modal-content {
        width: 95%;
        margin: 10% auto;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .map-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 8px;
    }

    .map-controls > * {
        width: 100%;
    }

    .scene-cell {
        width: 30px;
        height: 30px;
        font-size: 8px;
    }

    .scenes-map-grid {
        max-height: 400px;
    }

    .scenes-list-container {
        padding: 15px;
    }

    #toast.toast,
    .toast#toast {
        right: 10px !important;
        left: 10px !important;
        max-width: none !important;
        min-width: auto !important;
        transform: translateY(-100px) !important;
        opacity: 0 !important;
    }

    #toast.toast.show,
    .toast#toast.show {
        transform: translateY(0) !important;
        opacity: 1 !important;
    }
}
