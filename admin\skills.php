<?php
$pageTitle = '技能管理';
$currentPage = 'skills';
require_once 'layout_header.php';
require_once 'auth.php';
require_once '../config/Database.php';
require_once '../config/formulas.php'; // 引入公式文件以获取伤害类型

// Fetch item templates for skill book selection
try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    // Skill books are items of category 'Scroll' that can be consumed to learn a skill.
    $stmt = $conn->prepare("SELECT id, name FROM item_templates WHERE category = 'Scroll' ORDER BY name ASC");
    $stmt->execute();
    $skillBooks = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    // Handle error, maybe log it or display a message
    $skillBooks = [];
    $error_message = "无法加载技能书列表: " . $e->getMessage();
}

// Fetch jobs for the restriction dropdown
try {
    $db_jobs = Database::getInstance();
    $conn_jobs = $db_jobs->getConnection();
    $stmt_jobs = $conn_jobs->prepare("SELECT id, name FROM jobs ORDER BY id ASC");
    $stmt_jobs->execute();
    $jobs = $stmt_jobs->fetchAll(PDO::FETCH_ASSOC);
} catch (Exception $e) {
    $jobs = [];
    $error_message .= " | 无法加载职业列表: " . $e->getMessage();
}

?>

<style>
    /* Add specific styles for the skills page if needed */
    .form-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
    }
    .form-column {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }
    #effects-help {
        font-size: 0.9em;
        color: #666;
        background: #f9f9f9;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
        margin-top: 5px;
    }
    #formula-helpers, #battle-desc-helpers {
        margin-bottom: 10px;
        border: 1px solid #ddd;
        padding: 10px;
        border-radius: 4px;
        background: #f5f5f5;
        display: flex;
        flex-direction: column;
        gap: 10px;
    }
    .formula-section { }
    #formula-helpers .button-group, #battle-desc-helpers .button-group {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-top: 5px;
    }
    #formula-helpers .button-sub-group, #battle-desc-helpers .button-sub-group {
        display: flex;
        flex-wrap: wrap;
        align-items: center;
        gap: 5px;
        padding: 5px;
        border: 1px solid #e0e0e0;
        background: #fff;
        border-radius: 4px;
    }
    .button-sub-group span {
        font-weight: bold;
        font-size: 0.9em;
        margin-right: 5px;
    }
    .btn-sm {
        padding: 2px 6px;
        font-size: 0.85em;
        cursor: pointer;
    }
    
    /* Enhanced table styles */
    #skills-list .table {
        width: 100%;
        border-collapse: collapse;
        margin-top: 20px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        background-color: #fff;
        border-radius: 8px;
        overflow: hidden;
    }
    #skills-list .table th, 
    #skills-list .table td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #e9ecef;
    }
    #skills-list .table th {
        background-color: #f8f9fa;
        color: #495057;
        font-weight: 600;
        font-size: 0.9em;
    }
    #skills-list .table tbody tr:hover {
        background-color: #f1f3f5;
    }
    #skills-list .table tbody tr:last-child td {
        border-bottom: none;
    }

    /* Rarity and other badge styles */
    .badge {
        display: inline-block;
        padding: .3em .6em;
        font-size: .85em;
        font-weight: 600;
        line-height: 1;
        color: #fff;
        text-align: center;
        white-space: nowrap;
        vertical-align: baseline;
        border-radius: .35rem;
    }
    .rarity-common { background-color: #6c757d; }
    .rarity-uncommon { background-color: #28a745; }
    .rarity-rare { background-color: #007bff; }
    .rarity-epic { background-color: #6f42c1; }
    .rarity-legendary { background-color: #fd7e14; }
    .job-badge { background-color: #17a2b8; }

    /* Toast Notifications */
    .toast-container {
        position: fixed;
        top: 20px;
        right: 20px;
        z-index: 1050;
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
    }
    .toast {
        background-color: #28a745; /* Default success */
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        opacity: 0;
        transform: translateX(120%);
        transition: all 0.4s cubic-bezier(0.68, -0.55, 0.27, 1.55);
        min-width: 250px;
    }
    .toast.show {
        opacity: 1;
        transform: translateX(0);
    }
    .toast.error {
        background-color: #dc3545;
    }
    .toast.info {
        background-color: #17a2b8;
    }

    /* 职业筛选按钮样式 */
    .job-filter-btn {
        padding: 6px 12px;
        font-size: 13px;
        border-radius: 4px;
        border: 1px solid #ccc;
        background-color: #f8f9fa;
        color: #495057;
        cursor: pointer;
        transition: all 0.2s;
        margin-right: 5px;
        margin-bottom: 5px;
    }

    .job-filter-btn:hover {
        background-color: #e9ecef;
        border-color: #adb5bd;
    }

    .job-filter-btn.active {
        background-color: #007bff;
        border-color: #007bff;
        color: white;
    }

    .job-filter-btn.active:hover {
        background-color: #0056b3;
        border-color: #0056b3;
    }

    #job-filters {
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        gap: 5px;
    }
</style>

<div class="container">
    <div class="header">
        <button id="add-skill-btn" class="btn btn-primary">添加新技能</button>

        <!-- 职业筛选按钮 -->
        <div id="job-filters" style="margin-top: 10px;">
            <label style="margin-right: 10px; font-weight: bold;">职业筛选:</label>
            <button class="btn btn-secondary job-filter-btn active" data-job="all" style="margin-right: 5px;">全部</button>
            <!-- 其他职业按钮将通过JavaScript动态生成 -->
        </div>
    </div>

    <div id="skills-list" class="content-box">
        <!-- Skills will be loaded here via JavaScript -->
        <p>正在加载技能列表...</p>
    </div>

    <!-- Modal for Add/Edit Skill -->
    <div id="skill-modal" class="modal" style="display:none;">
        <div class="modal-content">
            <span class="close-btn">&times;</span>
            <h2 id="modal-title">添加新技能</h2>
            <form id="skill-form">
                <input type="hidden" id="skill-id" name="id">
                
                <div class="form-grid">
                    <div class="form-column">
                        <div class="form-group">
                            <label for="name">技能名称:</label>
                            <input type="text" id="name" name="name" required>
                        </div>
                        <div class="form-group">
                            <label for="icon">图标 (路径):</label>
                            <input type="text" id="icon" name="icon" placeholder="e.g., assets/icons/fireball.png">
                        </div>

                        <div class="form-group">
                            <label for="skill_type">技能类型:</label>
                            <select id="skill_type" name="skill_type" required>
                                <option value="COMBAT_INSTANT">瞬时战斗技能</option>
                                <option value="COMBAT_DELAYED">延时战斗技能 (吟唱)</option>
                                <option value="BUFF">增益技能 (BUFF)</option>
                                <option value="DEBUFF">减益技能 (DEBUFF)</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="rarity">稀有度:</label>
                            <select id="rarity" name="rarity" required>
                                <option value="common">普通</option>
                                <option value="uncommon">罕见</option>
                                <option value="rare">稀有</option>
                                <option value="epic">史诗</option>
                                <option value="legendary">传说</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="target_type">目标类型:</label>
                            <select id="target_type" name="target_type" required>
                                <option value="ENEMY">敌人</option>
                                <option value="HOSTILE">群体</option>
                                <option value="SELF">自己</option>
                                <option value="ALLY">友方</option>
                            </select>
                        </div>
                    </div>

                    <div class="form-column">
                        <div class="form-group">
                            <label for="mp_cost">法力消耗:</label>
                            <input type="number" id="mp_cost" name="mp_cost" value="0" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="required_level">学习等级要求:</label>
                            <input type="number" id="required_level" name="required_level" value="1" min="1" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="required_job_id">职业限制:</label>
                            <select id="required_job_id" name="required_job_id">
                                <option value="">无限制</option>
                                <?php foreach ($jobs as $job): ?>
                                    <?php if ($job['id'] == 1) continue; // Skip "无业" ?>
                                    <option value="<?php echo htmlspecialchars($job['id']); ?>">
                                        <?php echo htmlspecialchars($job['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="knowledge_points_cost">学习知识点消耗:</label>
                            <input type="number" id="knowledge_points_cost" name="knowledge_points_cost" value="1" min="0" required>
                        </div>

                        <div class="form-group">
                            <label for="damage_type">伤害类型:</label>
                            <select id="damage_type" name="damage_type">
                                <?php foreach (Formulas::$damageTypeNames as $key => $name): ?>
                                    <option value="<?php echo htmlspecialchars($key); ?>">
                                        <?php echo htmlspecialchars($name); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group">
                            <label for="required_skill_book_id">需要技能书:</label>
                            <select id="required_skill_book_id" name="required_skill_book_id">
                                <option value="">无</option>
                                <?php foreach ($skillBooks as $book): ?>
                                    <option value="<?php echo htmlspecialchars($book['id']); ?>">
                                        <?php echo htmlspecialchars($book['name']); ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>
                    
                    <div class="form-column">
                        <div class="form-group">
                            <label for="delay_turns">延迟回合数 (吟唱):</label>
                            <input type="number" id="delay_turns" name="delay_turns" min="0" placeholder="仅对延时技能有效">
                        </div>

                        <div class="form-group">
                            <label for="duration_turns">持续回合数 (BUFF/DEBUFF):</label>
                            <input type="number" id="duration_turns" name="duration_turns" min="0" placeholder="对增益和减益技能有效">
                        </div>

                        <div class="form-group">
                            <label for="cooldown_turns">冷却回合数:</label>
                            <input type="number" id="cooldown_turns" name="cooldown_turns" value="0" min="0" required>
                        </div>
                    </div>
                </div>

                <div class="form-group">
                    <label for="description">技能描述:</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="battle_description">战斗描述 (支持变量): <small style="font-weight: normal; color: #666;">使用 {A|B|C} 创建随机描述</small></label>
                    <div id="battle-desc-helpers"></div>
                    <textarea id="battle_description" name="battle_description" rows="3"></textarea>
                </div>

                <div class="form-group">
                    <label for="effects">技能效果 (JSON格式):</label>
                    <div id="formula-helpers"></div>
                    <textarea id="effects" name="effects" rows="6"></textarea>
                    <div id="effects-help">
                        <p><b>瞬时/延时战斗技能示例:</b><br>
                        <code>{"damage_formula": "attacker.intelligence * 1.5 + 20", "element": "fire_damage"}</code></p>
                        <p><b>增益技能示例:</b><br>
                        <code>{"strength": 10, "intelligence": -5}</code></p>
                        <p><b>减益技能示例:</b><br>
                        <code>{"strength": -10, "defense": "-attacker.intelligence * 0.3"}</code><br>
                        <code>{"attack": -15, "agility": -8, "silence": 1}</code></p>
                        <p><b>特殊减益效果:</b><br>
                        <code>silence</code>: 沉默(无法使用技能), <code>stun</code>: 眩晕(无法行动)</p>
                        <p><b>可用变量(伤害公式中):</b> <code>attacker.strength</code>, <code>attacker.agility</code>, <code>attacker.constitution</code>, <code>attacker.intelligence</code>, <code>attacker.level</code>, <code>attacker.attack</code>, <code>attacker.defense</code>, etc.</p>
                    </div>
                </div>

                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存</button>
                </div>
            </form>
        </div>
    </div>
</div>

<div id="toast-container"></div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const jobsData = <?php echo json_encode($jobs); ?>;
    const jobIdToNameMap = jobsData.reduce((map, job) => {
        map[job.id] = job.name;
        return map;
    }, {});
    jobIdToNameMap[''] = '无限制'; // Handle null/empty job id

    const addSkillBtn = document.getElementById('add-skill-btn');
    const skillModal = document.getElementById('skill-modal');
    const closeModalBtn = skillModal.querySelector('.close-btn');
    const skillForm = document.getElementById('skill-form');
    const skillsList = document.getElementById('skills-list');

    // 全局变量存储技能数据和当前筛选状态
    let allSkillsData = [];
    let currentJobFilter = 'all';

    // 生成职业筛选按钮
    function generateJobFilters(skillsData) {
        const jobFiltersContainer = document.getElementById('job-filters');
        const existingButtons = jobFiltersContainer.querySelectorAll('.job-filter-btn:not([data-job="all"])');
        existingButtons.forEach(btn => btn.remove());

        // 获取所有职业类型
        const jobTypes = new Set();
        skillsData.forEach(skill => {
            const jobName = jobIdToNameMap[skill.required_job_id] || '无限制';
            jobTypes.add(jobName);
        });

        // 按职业名称排序，无限制排在最后
        const sortedJobs = Array.from(jobTypes).sort((a, b) => {
            if (a === '无限制' && b !== '无限制') return 1;
            if (a !== '无限制' && b === '无限制') return -1;
            return a.localeCompare(b, 'zh-Hans-CN');
        });

        // 生成职业筛选按钮
        sortedJobs.forEach(jobName => {
            const button = document.createElement('button');
            button.className = 'btn btn-secondary job-filter-btn';
            button.dataset.job = jobName;
            button.textContent = jobName;
            button.addEventListener('click', () => filterSkillsByJob(jobName));
            jobFiltersContainer.appendChild(button);
        });
    }

    // 根据职业筛选技能
    function filterSkillsByJob(jobFilter) {
        currentJobFilter = jobFilter;

        // 更新按钮状态
        document.querySelectorAll('.job-filter-btn').forEach(btn => {
            btn.classList.remove('active');
            if ((jobFilter === 'all' && btn.dataset.job === 'all') ||
                (jobFilter !== 'all' && btn.dataset.job === jobFilter)) {
                btn.classList.add('active');
            }
        });

        // 筛选并显示技能
        let filteredSkills = allSkillsData;
        if (jobFilter !== 'all') {
            filteredSkills = allSkillsData.filter(skill => {
                const jobName = jobIdToNameMap[skill.required_job_id] || '无限制';
                return jobName === jobFilter;
            });
        }

        renderSkillsTable(filteredSkills);
    }

    // 渲染技能表格
    function renderSkillsTable(skillsData) {
        skillsList.innerHTML = '';
        if (skillsData.length === 0) {
            skillsList.innerHTML = '<p class="error">没有找到符合条件的技能。</p>';
            return;
        }

        const table = document.createElement('table');
        table.className = 'table';
        table.innerHTML = `
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>类型</th>
                    <th>目标</th>
                    <th>消耗/冷却</th>
                    <th>等级</th>
                    <th>职业限制</th>
                    <th>稀有度</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody></tbody>
        `;
        const tbody = table.querySelector('tbody');
        skillsData.forEach(skill => {
            const tr = document.createElement('tr');
            const rarityClass = `rarity-${(skill.rarity || 'common').toLowerCase()}`;
            const jobName = jobIdToNameMap[skill.required_job_id] || '无限制';

            tr.innerHTML = `
                <td>${skill.id}</td>
                <td><strong>${skill.name}</strong></td>
                <td>${skill.skill_type}</td>
                <td>${skill.target_type}</td>
                <td>${skill.mp_cost} MP / ${skill.cooldown_turns}T</td>
                <td>${skill.required_level}</td>
                <td><span class="badge job-badge">${jobName}</span></td>
                <td><span class="badge ${rarityClass}">${skill.rarity || 'common'}</span></td>
                <td>
                    <button class="btn btn-secondary edit-btn" data-id="${skill.id}">编辑</button>
                    <button class="btn btn-danger delete-btn" data-id="${skill.id}">删除</button>
                </td>
            `;
            tbody.appendChild(tr);
        });
        skillsList.appendChild(table);
    }

    const modalTitle = document.getElementById('modal-title');
    const formulaHelpersContainer = document.getElementById('formula-helpers');
    const battleDescHelpersContainer = document.getElementById('battle-desc-helpers');
    const battleDescTextarea = document.getElementById('battle_description');
    const damageTypeSelect = document.getElementById('damage_type');
    const skillTypeSelect = document.getElementById('skill_type');
    const effectsHelp = document.getElementById('effects-help');
    let effectsEditor;

    const attributeTranslations = {
        'level': '等级', 'hp': '当前生命', 'max_hp': '最大生命', 'mp': '当前法力', 'max_mp': '最大法力',
        'attack': '攻击力', 'defense': '防御力', 'attack_speed': '攻击速度',
        'strength': '力量', 'agility': '敏捷', 'constitution': '体质', 'intelligence': '智慧',
        'fire_damage': '火焰伤害', 'ice_damage': '冰冻伤害', 'wind_damage': '风裂伤害', 'electric_damage': '闪电伤害',
        'fire_resistance': '火焰抗性', 'ice_resistance': '冰冻抗性', 'wind_resistance': '风裂抗性', 'electric_resistance': '闪电抗性'
    };

    const battleDescPlaceholders = {
        '基础': {
            '[攻击者]': '攻击方名称',
            '[敌人]': '防御方名称',
            '[武器]': '攻击方武器 (含强化等级)',
            '[武器名]': '攻击方武器 (不含强化等级)',
            '[技能]': '当前施放的技能名称',
        },
        '伤害': {
            '[伤害]': '总伤害数值',
            '[物理伤害]': '物理伤害数值',
            '[元素伤害]': '元素伤害数值',
            '[暴击]': '如果暴击，显示【暴击！】',
        },
        '伤害文字': {
            '物理伤害': '物理伤害数值',
            '火焰伤害':'火焰伤害',
            '冰冻伤害':'冰冻伤害',
            '风裂伤害':'风裂伤害',
            '闪电伤害':'闪电伤害',
        }
    };

    const randomTemplates = {
        '动作': '{挥舞|刺出|斩击|猛劈|横扫}',
        '方式': '{迅猛地|精准地|猛烈地|灵巧地}',
        '命中': '{划破了|击中了|撕裂了|贯穿了}',
        '部位': '{胸口|肩膀|手臂|腿部|要害}'
    };

    const commonOperators = {
        '+': '加', '-': '减', '*': '乘', '/': '除', 
        '(': '左括号', ')': '右括号',
    };
    const specialValues = {
        '%%SKILL_FLUCTUATION%%': '伤害浮动',
        '%%SKILL_LEVEL%%': '技能等级'
    };

    function setupBattleDescHelpers() {
        let html = '<div class="formula-section"><strong>可用变量:</strong><div class="button-group">';
        
        for (const groupName in battleDescPlaceholders) {
            html += `<div class="button-sub-group"><span>${groupName}:</span>`;
            for (const placeholder in battleDescPlaceholders[groupName]) {
                 html += `<button type="button" class="btn btn-sm btn-success battle-desc-btn" data-value="${placeholder}" title="${battleDescPlaceholders[groupName][placeholder]}">${placeholder}</button>`;
            }
            html += '</div>';
        }
        html += '</div></div>';

        html += '<div class="formula-section"><strong>随机组合 (点击插入):</strong><div class="button-group"><div class="button-sub-group">';
        for (const name in randomTemplates) {
            const template = randomTemplates[name];
            html += `<button type="button" class="btn btn-sm btn-info battle-desc-btn" data-value="${template}" title="插入随机'${name}'模板">${name}</button>`;
        }
        html += '</div></div></div>';

        battleDescHelpersContainer.innerHTML = html;
    }

    function buildFormulaButtons(prefix, groupName, attributes) {
        let html = `<div class="button-sub-group"><span>${groupName}:</span>`;
        attributes.forEach(attr => {
            const attrPath = `${prefix}.${attr}`;
            const displayName = attributeTranslations[attr] || attr;
            html += `<button type="button" class="btn btn-sm btn-info formula-btn" data-value="${attrPath}" title="${attrPath}">${displayName}</button>`;
        });
        return html + `</div>`;
    }

    function buildOperatorButtons(operators) {
        let html = '';
        for (const op in operators) {
            html += `<button type="button" class="btn btn-sm btn-secondary formula-btn" data-value=" ${op} " title="${operators[op]}">${op}</button>`;
        }
        return html;
    }

    function buildSpecialValueButtons(values) {
        let html = '';
        for (const val in values) {
            html += `<button type="button" class="btn btn-sm btn-warning formula-btn" data-value="${val}" title="${val}">${values[val]}</button>`;
        }
        return html;
    }

    function setupCombatFormulaHelpers() {
        const combatantAttributes = {
            '核心': ['level', 'hp', 'max_hp', 'mp', 'max_mp'],
            '战斗': ['attack', 'defense', 'attack_speed'],
            '四维': ['strength', 'agility', 'constitution', 'intelligence'],
            '元素伤害': ['fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'],
            '元素抗性': ['fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance']
        };
        
        let html = '';
        ['attacker', 'defender'].forEach(prefix => {
            html += `<div class="formula-section"><strong>${prefix === 'attacker' ? '攻击方' : '防御方'}属性:</strong><div class="button-group">`;
            for (const groupName in combatantAttributes) {
                html += buildFormulaButtons(prefix, groupName, combatantAttributes[groupName]);
            }
            html += `</div></div>`;
        });
        
        const logicOperators = { ...commonOperators, '>': '大于', '<': '小于', '>=': '大于等于', '<=': '小于等于', '==': '等于', '!=': '不等于', '&&': '与', '||': '或' };

        html += `<div class="formula-section"><strong>数学/逻辑:</strong><div class="button-group">`;
        html += `<div class="button-sub-group">${buildOperatorButtons(logicOperators)}</div>`;
        html += `<div class="button-sub-group">${buildSpecialValueButtons(specialValues)}</div>`;
        html += `</div></div>`;
        
        formulaHelpersContainer.innerHTML = html;

        effectsHelp.innerHTML = `
            <p><b>瞬时/延时战斗技能示例:</b><br>
            <code>{"damage_formula": "attacker.intelligence * 1.5 + 20", "element": "fire_damage"}</code></p>
            <p><b>可用变量(伤害公式中):</b> <code>attacker.strength</code>, <code>defender.agility</code>, etc.</p>
        `;
    }
    
    function setupBuffFormulaHelpers() {
        const buffAttributes = {
            '四维': ['strength', 'agility', 'constitution', 'intelligence'],
            '战斗': ['attack', 'defense', 'attack_speed'],
            '元素伤害': ['fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'],
            '元素抗性': ['fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance'],
            '其他': ['level', 'max_hp', 'max_mp']
        };

        let html = '';
        // For buffs, "attacker" is the caster, "defender" is the target.
        // Let's make it clearer in the UI.
        const prefixes = {
            'attacker': '施法者',
            'defender': '目标'
        };

        for (const prefix in prefixes) {
            html += `<div class="formula-section"><strong>${prefixes[prefix]}属性:</strong><div class="button-group">`;
            for (const groupName in buffAttributes) {
                html += buildFormulaButtons(prefix, groupName, buffAttributes[groupName]);
            }
            html += `</div></div>`;
        }

        const buffSpecialValues = {'%%SKILL_LEVEL%%': '技能等级'};

        html += `<div class="formula-section"><strong>数学/逻辑:</strong><div class="button-group">`;
        html += `<div class="button-sub-group">${buildOperatorButtons(commonOperators)}</div>`;
        html += `<div class="button-sub-group">${buildSpecialValueButtons(buffSpecialValues)}</div>`;
        html += `</div></div>`;

        formulaHelpersContainer.innerHTML = html;
        
        effectsHelp.innerHTML = `
            <p><b>增益技能示例 (JSON格式):</b><br>
            为目标增加力量和敏捷，力量受技能等级加成，敏捷为固定值。</p>
            <code>{
    "strength": "10 + %%SKILL_LEVEL%% * 2",
    "agility": "5"
}</code>
            <p><b>注意:</b> 每个键是一个属性, 每个值是<b>一个公式(字符串)或一个固定数值</b>。</p>
            <p><b>可用变量:</b> <code>attacker.strength</code> (施法者力量), <code>defender.intelligence</code> (目标智慧), <code>%%SKILL_LEVEL%%</code> (技能等级).</p>
        `;
    }

    function setupDebuffFormulaHelpers() {
        const debuffAttributes = {
            '四维': ['strength', 'agility', 'constitution', 'intelligence'],
            '战斗': ['attack', 'defense', 'attack_speed'],
            '元素伤害': ['fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'],
            '元素抗性': ['fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance'],
            '特殊效果': ['silence', 'stun'],
            '其他': ['level', 'max_hp', 'max_mp']
        };

        let html = '';
        // For debuffs, "attacker" is the caster, "defender" is the target.
        const prefixes = {
            'attacker': '施法者',
            'defender': '目标'
        };

        for (const prefix in prefixes) {
            html += `<div class="formula-section"><strong>${prefixes[prefix]}属性:</strong><div class="button-group">`;
            for (const groupName in debuffAttributes) {
                html += buildFormulaButtons(prefix, groupName, debuffAttributes[groupName]);
            }
            html += `</div></div>`;
        }

        const debuffSpecialValues = {'%%SKILL_LEVEL%%': '技能等级'};

        html += `<div class="formula-section"><strong>数学/逻辑:</strong><div class="button-group">`;
        html += `<div class="button-sub-group">${buildOperatorButtons(commonOperators)}</div>`;
        html += `<div class="button-sub-group">${buildSpecialValueButtons(debuffSpecialValues)}</div>`;
        html += `</div></div>`;

        formulaHelpersContainer.innerHTML = html;

        effectsHelp.innerHTML = `
            <p><b>减益技能示例 (JSON格式):</b><br>
            降低目标的力量和防御，效果可以基于施法者智力计算。</p>
            <code>{
    "strength": "-10",
    "defense": "-attacker.intelligence * 0.2",
    "stun": "1"
}</code>
            <p><b>特殊效果:</b> <code>silence</code> (沉默), <code>stun</code> (眩晕)</p>
            <p><b>注意:</b> 减益效果通常使用负数值。特殊效果使用正数值表示强度。</p>
            <p><b>可用变量:</b> <code>attacker.intelligence</code> (施法者智慧), <code>defender.constitution</code> (目标体质), <code>%%SKILL_LEVEL%%</code> (技能等级).</p>
        `;
    }

    function updateFormulaHelpers() {
        const skillType = skillTypeSelect.value;

        // 更新字段可见性
        updateFieldVisibility(skillType);

        if (skillType === 'BUFF') {
            setupBuffFormulaHelpers();
            formulaHelpersContainer.style.display = 'block';
        } else if (skillType === 'DEBUFF') {
            setupDebuffFormulaHelpers();
            formulaHelpersContainer.style.display = 'block';
        } else if (skillType === 'COMBAT_INSTANT' || skillType === 'COMBAT_DELAYED') {
            setupCombatFormulaHelpers();
            formulaHelpersContainer.style.display = 'block';
        } else {
            formulaHelpersContainer.style.display = 'none';
        }
    }

    function updateFieldVisibility(skillType) {
        const durationField = document.querySelector('input[name="duration_turns"]').closest('.form-group');
        const delayField = document.querySelector('input[name="delay_turns"]').closest('.form-group');

        // 持续时间字段：BUFF和DEBUFF技能需要
        if (skillType === 'BUFF' || skillType === 'DEBUFF') {
            durationField.style.display = 'block';
        } else {
            durationField.style.display = 'none';
        }

        // 延迟时间字段：只有COMBAT_DELAYED技能需要
        if (skillType === 'COMBAT_DELAYED') {
            delayField.style.display = 'block';
        } else {
            delayField.style.display = 'none';
        }
    }

    skillTypeSelect.addEventListener('change', updateFormulaHelpers);
    setupBattleDescHelpers();

    battleDescHelpersContainer.addEventListener('click', (event) => {
        if (event.target.classList.contains('battle-desc-btn')) {
            const cursorPos = battleDescTextarea.selectionStart;
            const text = battleDescTextarea.value;
            const value = event.target.dataset.value;
            battleDescTextarea.value = text.slice(0, cursorPos) + value + text.slice(cursorPos);
            battleDescTextarea.focus();
            // Move cursor to end of inserted text
            battleDescTextarea.selectionEnd = cursorPos + value.length;
        }
    });

    formulaHelpersContainer.addEventListener('click', (event) => {
        if (event.target.classList.contains('formula-btn')) {
            if (effectsEditor) {
                // For combat skills, it's usually one formula
                effectsEditor.replaceSelection(event.target.dataset.value);
            }
            effectsEditor.focus();
        }
    });

    function updateEffectsJSON(key, value) {
        if (!effectsEditor) return;

        let currentEffects = {};
        try {
            // Get value from CodeMirror editor
            const editorValue = effectsEditor.getValue();
            // Allow empty editor, default to an empty object
            if (editorValue.trim() !== '') {
                currentEffects = JSON.parse(editorValue);
            }
        } catch (e) {
            console.warn("Could not parse effects JSON. User might be typing.", e);
            // Don't overwrite invalid JSON, let the user fix it.
            return;
        }

        currentEffects[key] = value;
        
        // Pretty-print the JSON and set it back to the editor
        const newJson = JSON.stringify(currentEffects, null, 2);
        effectsEditor.setValue(newJson);
    }

    damageTypeSelect.addEventListener('change', function() {
        const skillType = document.getElementById('skill_type').value;
        if (skillType === 'COMBAT_INSTANT' || skillType === 'COMBAT_DELAYED') {
            updateEffectsJSON('element', this.value);
        }
    });

    function setupCodeMirror() {
        if (effectsEditor) {
            effectsEditor.toTextArea(); // Clean up existing instance
        }
        effectsEditor = CodeMirror.fromTextArea(document.getElementById('effects'), {
            lineNumbers: true,
            mode: { name: "javascript", json: true },
            theme: "material-darker",
            indentUnit: 2,
            tabSize: 2,
            lint: true,
            gutters: ["CodeMirror-lint-markers"]
        });
    }

    function openModal(isNew, skillType = 'COMBAT_INSTANT') {
        skillModal.style.display = 'block';
        if (!effectsEditor) {
            setupCodeMirror();
        }

        updateFormulaHelpers();

        if (isNew) {
            let defaultEffects = {};
            if (skillType === 'COMBAT_INSTANT' || skillType === 'COMBAT_DELAYED') {
                 defaultEffects = {
                    damage_formula: "",
                    element: damageTypeSelect.value 
                };
                setTimeout(() => {
                    effectsEditor.setValue(JSON.stringify(defaultEffects, null, 2));
                    effectsEditor.setCursor({line: 1, ch: 22});
                    effectsEditor.focus();
                }, 100);
            } else if (skillType === 'BUFF') {
                defaultEffects = {
                    "strength": ""
                };
                 setTimeout(() => {
                    effectsEditor.setValue(JSON.stringify(defaultEffects, null, 2));
                    effectsEditor.setCursor({line: 1, ch: 14});
                    effectsEditor.focus();
                }, 100);
            } else if (skillType === 'DEBUFF') {
                defaultEffects = {
                    "strength": "-10",
                    "defense": "-5"
                };
                 setTimeout(() => {
                    effectsEditor.setValue(JSON.stringify(defaultEffects, null, 2));
                    effectsEditor.setCursor({line: 1, ch: 15});
                    effectsEditor.focus();
                }, 100);
            }
        }
        
        // Use a short timeout to ensure the modal is fully visible before refreshing
        setTimeout(() => effectsEditor.refresh(), 50);
    }

    function closeModal() {
        skillModal.style.display = 'none';
        skillForm.reset();
        document.getElementById('skill-id').value = '';
        if (effectsEditor) {
            effectsEditor.setValue('');
        }
    }

    function showToast(message, type = 'success', duration = 3000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        setTimeout(() => {
            toast.classList.remove('show');
            toast.addEventListener('transitionend', () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
        }, duration);
    }

    addSkillBtn.addEventListener('click', () => {
        modalTitle.textContent = '添加新技能';
        skillForm.reset(); // Reset form to defaults
        document.getElementById('skill-id').value = '';
        const skillType = skillTypeSelect.value;
        openModal(true, skillType);
    });

    closeModalBtn.addEventListener('click', closeModal);
    window.addEventListener('click', (event) => {
        if (event.target === skillModal) {
            closeModal();
        }
    });

    // Load skills
    function loadSkills() {
        fetch('api_skills.php')
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    skillsList.innerHTML = `<p class="error">${data.error}</p>`;
                    return;
                }

                // 存储所有技能数据
                allSkillsData = data;

                // 生成职业筛选按钮
                generateJobFilters(data);

                // 初始化"全部"筛选按钮事件
                const allButton = document.querySelector('.job-filter-btn[data-job="all"]');
                if (allButton) {
                    allButton.addEventListener('click', () => filterSkillsByJob('all'));
                }

                // 显示所有技能
                renderSkillsTable(data);
            })
            .catch(error => {
                skillsList.innerHTML = `<p class="error">加载失败: ${error}</p>`;
            });
    }

    // Form submission
    skillForm.addEventListener('submit', function(event) {
        event.preventDefault();
        
        // Update the textarea with the CodeMirror content before submitting
        if (effectsEditor) {
            effectsEditor.save();
        }

        const effectsValue = effectsEditor ? effectsEditor.getValue().trim() : document.getElementById('effects').value.trim();

        // Manual validation for CodeMirror field
        if (effectsValue === '') {
            showToast('技能效果 (JSON格式) 不能为空！', 'error');
            if (effectsEditor) effectsEditor.focus();
            return;
        }

        try {
            JSON.parse(effectsValue);
        } catch (e) {
            showToast('技能效果 (JSON格式) 不合法: ' + e.message, 'error');
            if (effectsEditor) effectsEditor.focus();
            return;
        }

        const formData = new FormData(this);
        const id = formData.get('id');
        const url = id ? `api_skills.php?id=${id}` : 'api_skills.php';
        
        // Convert FormData to a plain object
        const data = {};
        formData.forEach((value, key) => data[key] = value);

        fetch(url, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                closeModal();
                loadSkills();
                showToast('保存成功！');
            } else {
                showToast(`错误: ${result.error}`, 'error');
            }
        })
        .catch(error => showToast(`请求失败: ${error}`, 'error'));
    });

    // Edit and Delete buttons
    skillsList.addEventListener('click', function(event) {
        const target = event.target;
        const id = target.dataset.id;

        if (target.classList.contains('edit-btn')) {
            fetch(`api_skills.php?id=${id}`)
                .then(response => response.json())
                .then(skill => {
                    if (skill.error) {
                        showToast(`错误: ${skill.error}`, 'error');
                        return;
                    }
                    modalTitle.textContent = '编辑技能';
                    document.getElementById('skill-id').value = skill.id;
                    for (const key in skill) {
                        const input = document.getElementById(key);
                        if (input) {
                            if (key === 'effects' && typeof skill[key] === 'object') {
                                // Defer setting value for CodeMirror
                            } else {
                                input.value = skill[key];
                            }
                        }
                    }
                    // Also populate the battle_description textarea
                    const battleDescInput = document.getElementById('battle_description');
                    if (battleDescInput) {
                        battleDescInput.value = skill.battle_description || '';
                    }

                    openModal(false, skill.skill_type);
                    // Now set the value for CodeMirror after the modal is open
                    if (effectsEditor) {
                        let effectsValue = "{}";
                        let effectsObj = {};
                        if (skill.effects) {
                           try {
                               // skill.effects is already an object from the API
                               effectsObj = skill.effects;
                               effectsValue = JSON.stringify(effectsObj, null, 2);
                           } catch(e) {
                               console.error("Failed to process effects JSON", e);
                               // If it's not an object for some reason, show the raw string
                               effectsValue = typeof skill.effects === 'string' ? skill.effects : '{}'; 
                           }
                        }
                        
                        // Set damage type dropdown from loaded effects
                        if (skill.skill_type !== 'BUFF' && effectsObj && effectsObj.element) {
                            damageTypeSelect.value = effectsObj.element;
                        } else if (skill.skill_type !== 'BUFF') {
                            // Default to physical if not set
                            damageTypeSelect.value = 'physical_damage';
                        }

                        effectsEditor.setValue(effectsValue);
                        setTimeout(() => effectsEditor.refresh(), 10);
                    }
                });
        }

        if (target.classList.contains('delete-btn')) {
            if (confirm(`确定要删除ID为 ${id} 的技能吗？`)) {
                fetch(`api_skills.php?id=${id}`, { method: 'DELETE' })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        loadSkills();
                        showToast('删除成功！');
                    } else {
                        showToast(`错误: ${result.error}`, 'error');
                    }
                })
                .catch(error => showToast(`请求失败: ${error}`, 'error'));
            }
        }
    });

    loadSkills();
});
</script>

<?php require_once 'layout_footer.php'; ?> 