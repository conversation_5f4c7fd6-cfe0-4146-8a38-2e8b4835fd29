<?php
// admin/npcs.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 页面标题和当前页面标识
$pageTitle = 'NPC管理';
$currentPage = 'npcs';

// 初始化变量
$success_message = '';
$error_message = '';
$npcs = [];
$dialogue_trees = [];
$groups = []; // 添加分组变量

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有NPC分组
    $stmt = $pdo->query("
        SELECT * FROM npc_groups
        ORDER BY sort_order, name
    ");
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有对话树
    $stmt = $pdo->query("SELECT id, name FROM dialogue_trees ORDER BY name");
    $dialogue_trees = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理添加/编辑/删除分组
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'add_group') {
            // 添加新分组
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'normal';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO npc_groups 
                    (name, type, description, sort_order)
                    VALUES 
                    (:name, :type, :description, :sort_order)
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order
                ]);
                $success_message = '分组添加成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM npc_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } elseif ($_POST['action'] === 'update_group' && isset($_POST['id'])) {
            // 更新分组
            $id = (int)$_POST['id'];
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'normal';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE npc_groups 
                    SET name = :name,
                        type = :type,
                        description = :description,
                        sort_order = :sort_order
                    WHERE id = :id
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order,
                    ':id' => $id
                ]);
                $success_message = '分组更新成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM npc_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } elseif ($_POST['action'] === 'delete_group' && isset($_POST['id'])) {
            // 删除分组
            $id = (int)$_POST['id'];
            
            // 先检查是否有NPC使用此分组
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM npc_templates WHERE group_id = :id
            ");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $error_message = "无法删除此分组，有 {$count} 个NPC属于该分组";
            } else {
                $stmt = $pdo->prepare("DELETE FROM npc_groups WHERE id = :id");
                $stmt->execute([':id' => $id]);
                
                $success_message = '分组删除成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM npc_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } elseif ($_POST['action'] === 'add_npc') {
            // 添加新NPC
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $level = (int)($_POST['level'] ?? 1);
            $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null; // 添加分组ID
            $avatar = $_POST['avatar'] ?? null;
            $is_merchant = isset($_POST['is_merchant']) ? 1 : 0;
            $is_quest_giver = isset($_POST['is_quest_giver']) ? 1 : 0;
            $behavior_script = $_POST['behavior_script'] ?? null;
            $default_dialogue_tree_id = !empty($_POST['default_dialogue_tree_id']) ? (int)$_POST['default_dialogue_tree_id'] : null;
            
            if (empty($name)) {
                $error_message = "NPC名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO npc_templates 
                    (name, group_id, description, level, avatar, is_merchant, is_quest_giver, behavior_script, default_dialogue_tree_id)
                    VALUES 
                    (:name, :group_id, :description, :level, :avatar, :is_merchant, :is_quest_giver, :behavior_script, :default_dialogue_tree_id)
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':group_id' => $group_id,
                    ':description' => $description,
                    ':level' => $level,
                    ':avatar' => $avatar,
                    ':is_merchant' => $is_merchant,
                    ':is_quest_giver' => $is_quest_giver,
                    ':behavior_script' => $behavior_script,
                    ':default_dialogue_tree_id' => $default_dialogue_tree_id
                ]);
                $success_message = 'NPC添加成功！';
            }
            
        } elseif ($_POST['action'] === 'update_npc' && isset($_POST['id'])) {
            // 更新NPC
            $id = (int)$_POST['id'];
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $level = (int)($_POST['level'] ?? 1);
            $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null; // 添加分组ID
            $avatar = $_POST['avatar'] ?? null;
            $is_merchant = isset($_POST['is_merchant']) ? 1 : 0;
            $is_quest_giver = isset($_POST['is_quest_giver']) ? 1 : 0;
            $behavior_script = $_POST['behavior_script'] ?? null;
            $default_dialogue_tree_id = !empty($_POST['default_dialogue_tree_id']) ? (int)$_POST['default_dialogue_tree_id'] : null;
            
            if (empty($name)) {
                $error_message = "NPC名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE npc_templates 
                    SET name = :name,
                        group_id = :group_id,
                        description = :description,
                        level = :level,
                        avatar = :avatar,
                        is_merchant = :is_merchant,
                        is_quest_giver = :is_quest_giver,
                        behavior_script = :behavior_script,
                        default_dialogue_tree_id = :default_dialogue_tree_id,
                        updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':group_id' => $group_id,
                    ':description' => $description,
                    ':level' => $level,
                    ':avatar' => $avatar,
                    ':is_merchant' => $is_merchant,
                    ':is_quest_giver' => $is_quest_giver,
                    ':behavior_script' => $behavior_script,
                    ':default_dialogue_tree_id' => $default_dialogue_tree_id,
                    ':id' => $id
                ]);
                $success_message = 'NPC更新成功！';
            }
            
        } elseif ($_POST['action'] === 'delete_npc' && isset($_POST['id'])) {
            // 删除NPC
            $id = (int)$_POST['id'];
            
            // 检查是否有场景使用此NPC
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM npc_instances WHERE template_id = :id
            ");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $error_message = "无法删除此NPC，有 {$count} 个场景正在使用它";
            } else {
                // 删除NPC
                $stmt = $pdo->prepare("DELETE FROM npc_templates WHERE id = :id");
                $stmt->execute([':id' => $id]);
                
                $success_message = 'NPC删除成功！';
            }
        }
    }
    
    // 构建查询
    $filter_group = isset($_GET['group']) ? (int)$_GET['group'] : null;
    
    $sql = "
        SELECT nt.*, 
               dt.name AS dialogue_tree_name,
               (SELECT COUNT(*) FROM npc_instances WHERE template_id = nt.id) AS instance_count,
               ng.name AS group_name,
               ng.type AS group_type
        FROM npc_templates nt
        LEFT JOIN dialogue_trees dt ON nt.default_dialogue_tree_id = dt.id
        LEFT JOIN npc_groups ng ON nt.group_id = ng.id
    ";
    
    $params = [];
    if ($filter_group) {
        $sql .= " WHERE nt.group_id = :group_id";
        $params[':group_id'] = $filter_group;
    }
    
    $sql .= " ORDER BY ng.sort_order, ng.name, nt.name";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $npcs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 引入页面头部
require_once 'layout_header.php';
?>

<style>
/* NPC装备管理样式 */
.equipment-slots {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.equipment-slot {
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    background-color: #f9f9f9;
}

.slot-name {
    font-weight: bold;
    margin-bottom: 8px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.slot-item {
    min-height: 60px;
}

.empty-slot {
    color: #999;
    font-style: italic;
    text-align: center;
    padding: 10px;
}

.item-card {
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    padding: 8px;
    background-color: #fff;
}

.item-name {
    font-weight: bold;
    margin-bottom: 8px;
    color: #3366cc;
}

.item-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 8px;
}

/* 标签样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid #ddd;
    margin-bottom: 20px;
}

.tab {
    padding: 10px 15px;
    cursor: pointer;
    border: 1px solid transparent;
    border-bottom: none;
    margin-right: 5px;
}

.tab.active {
    background-color: #fff;
    border-color: #ddd;
    border-bottom-color: #fff;
    margin-bottom: -1px;
    font-weight: bold;
}

.tab-content {
    display: none;
    padding: 15px 0;
}

.tab-content.active {
    display: block;
}

/* 分组样式 */
.header-actions {
    display: flex;
    gap: 10px;
}
.groups-container {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    padding: 15px;
}
.group-item {
    display: flex;
    align-items: center;
    padding: 8px 120px 8px 15px; /* 右边预留120px给按钮 */
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #333;
    text-decoration: none;
    position: relative;
    min-width: 180px;
    width: auto;
    justify-content: flex-start; /* 左对齐内容 */
}
.group-item:hover {
    background-color: #e9ecef;
}
.group-item.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}
.group-count {
    margin-left: 5px;
    font-size: 0.9em;
    color: inherit;
    opacity: 0.8;
}
.group-info {
    display: flex;
    align-items: center;
    flex: 1;
}
.group-actions {
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    gap: 5px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease-in-out;
    background: rgba(255, 255, 255, 0.95);
    padding: 2px 6px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}
.group-item:hover .group-actions {
    opacity: 1;
    visibility: visible;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}
.group-actions button {
    padding: 2px 6px;
    border-radius: 3px;
    transition: all 0.2s ease;
    font-size: 0.75em;
    border: 1px solid #ddd;
    background: white;
    color: #666;
    white-space: nowrap;
}
.group-actions button:hover {
    background: #f8f9fa;
    border-color: #007bff;
    color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.group-actions button.btn-danger:hover {
    background: #dc3545;
    border-color: #dc3545;
    color: white;
}
.group-item.active .group-actions {
    background: rgba(0, 123, 255, 0.95);
}

.group-item.active .group-actions button {
    background: rgba(255, 255, 255, 0.9);
    color: #007bff;
    border-color: rgba(255, 255, 255, 0.7);
}

.group-item.active .group-actions button:hover {
    background: white;
    color: #007bff;
    border-color: white;
}

.group-item.active .group-actions button.btn-danger:hover {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* 分组类型颜色 */
.group-type-merchant { border-left: 4px solid #28a745; }
.group-type-quest { border-left: 4px solid #fd7e14; }
.group-type-boss { border-left: 4px solid #dc3545; }
.group-type-normal { border-left: 4px solid #6c757d; }

/* 徽章颜色 */
.badge-merchant { background-color: #28a745; color: white; }
.badge-quest { background-color: #fd7e14; color: white; }
.badge-boss { background-color: #dc3545; color: white; }
.badge-normal { background-color: #6c757d; color: white; }

</style>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <div class="content-header">
        <div class="header-actions">
            <button id="add-group-btn" class="btn btn-success">添加分组</button>
            <button id="add-npc-btn" class="btn btn-primary">添加NPC</button>
        </div>
    </div>
    
    <!-- 分组管理 -->
    <div class="card mb-4">
        <div class="groups-container">
            <a href="npcs.php" class="group-item <?php echo !isset($_GET['group']) ? 'active' : ''; ?>">
                <span class="group-info">全部NPC</span>
            </a>
            <?php foreach($groups as $group): ?>
                <a href="npcs.php?group=<?php echo $group['id']; ?>" 
                   class="group-item <?php echo (isset($_GET['group']) && $_GET['group'] == $group['id']) ? 'active' : ''; ?> group-type-<?php echo $group['type']; ?>">
                    <span class="group-info">
                        <?php echo htmlspecialchars($group['name']); ?> 
                        <span class="group-count">(<?php 
                            // 计算该分组下的NPC数量
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM npc_templates WHERE group_id = :group_id");
                            $stmt->execute([':group_id' => $group['id']]);
                            echo $stmt->fetchColumn(); 
                        ?>)</span>
                    </span>
                    <div class="group-actions">
                        <button class="btn btn-sm edit-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>"
                                data-type="<?php echo $group['type']; ?>"
                                data-sort="<?php echo $group['sort_order']; ?>"
                                data-description="<?php echo htmlspecialchars($group['description'] ?? ''); ?>">
                            <i class="fa fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-danger delete-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>">
                            <i class="fa fa-trash"></i> 删除
                        </button>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="card">
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>等级</th>
                    <th>描述</th>
                    <th>类型</th>
                    <th>分组</th>
                    <th>默认对话树</th>
                    <th>实例数</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($npcs)): ?>
                    <tr>
                        <td colspan="8" class="text-center">暂无NPC数据</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($npcs as $npc): ?>
                        <tr>
                            <td><?php echo $npc['id']; ?></td>
                            <td><?php echo htmlspecialchars($npc['name']); ?></td>
                            <td><?php echo $npc['level']; ?></td>
                            <td><?php echo htmlspecialchars(mb_substr($npc['description'] ?? '', 0, 50) . (mb_strlen($npc['description'] ?? '') > 50 ? '...' : '')); ?></td>
                            <td>
                                <?php if ($npc['is_merchant'] && $npc['is_quest_giver']): ?>
                                    <span class="badge">商人</span> <span class="badge">任务</span>
                                <?php elseif ($npc['is_merchant']): ?>
                                    <span class="badge">商人</span>
                                <?php elseif ($npc['is_quest_giver']): ?>
                                    <span class="badge">任务</span>
                                <?php else: ?>
                                    <span class="badge">普通</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars($npc['group_name'] ?? '无'); ?></td>
                            <td><?php echo htmlspecialchars($npc['dialogue_tree_name'] ?? '无'); ?></td>
                            <td><?php echo $npc['instance_count']; ?></td>
                            <td>
                                <button class="btn btn-sm edit-npc" 
                                    data-id="<?php echo $npc['id']; ?>"
                                    data-name="<?php echo htmlspecialchars($npc['name']); ?>"
                                    data-description="<?php echo htmlspecialchars($npc['description'] ?? ''); ?>"
                                    data-level="<?php echo $npc['level']; ?>"
                                    data-avatar="<?php echo htmlspecialchars($npc['avatar'] ?? ''); ?>"
                                    data-merchant="<?php echo $npc['is_merchant']; ?>"
                                    data-quest="<?php echo $npc['is_quest_giver']; ?>"
                                    data-behavior="<?php echo htmlspecialchars($npc['behavior_script'] ?? ''); ?>"
                                    data-dialogue="<?php echo $npc['default_dialogue_tree_id'] ?? ''; ?>"
                                    data-group="<?php echo $npc['group_id'] ?? ''; ?>">
                                    编辑
                                </button>
                                <button class="btn btn-sm btn-danger delete-npc" 
                                    data-id="<?php echo $npc['id']; ?>" 
                                    data-name="<?php echo htmlspecialchars($npc['name']); ?>"
                                    data-instances="<?php echo $npc['instance_count']; ?>">
                                    删除
                                </button>
                                <a href="npc_dialogues.php?npc_id=<?php echo $npc['id']; ?>" class="btn btn-sm">对话管理</a>
                                <button class="btn btn-sm manage-inventory" data-id="<?php echo $npc['id']; ?>" data-name="<?php echo htmlspecialchars($npc['name']); ?>">装备管理</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-npc-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-npc-message"></p>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="delete_npc">
            <input type="hidden" name="id" id="delete-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger" id="confirm-delete-btn">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 添加分组模态框 -->
<div id="add-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加NPC分组</h3>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="add_group">
            
            <div class="form-group">
                <label for="add-group-name">名称</label>
                <input type="text" id="add-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="add-group-type">类型</label>
                <select id="add-group-type" name="type" class="form-control">
                    <option value="merchant">商人</option>
                    <option value="quest">任务</option>
                    <option value="boss">BOSS</option>
                    <option value="normal">普通</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-group-sort">排序顺序</label>
                <input type="number" id="add-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="add-group-description">描述</label>
                <textarea id="add-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑分组模态框 -->
<div id="edit-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑NPC分组</h3>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="update_group">
            <input type="hidden" name="id" id="edit-group-id" value="">
            
            <div class="form-group">
                <label for="edit-group-name">名称</label>
                <input type="text" id="edit-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="edit-group-type">类型</label>
                <select id="edit-group-type" name="type" class="form-control">
                    <option value="merchant">商人</option>
                    <option value="quest">任务</option>
                    <option value="boss">BOSS</option>
                    <option value="normal">普通</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-group-sort">排序顺序</label>
                <input type="number" id="edit-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="edit-group-description">描述</label>
                <textarea id="edit-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除分组确认模态框 -->
<div id="delete-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-group-message"></p>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="delete_group">
            <input type="hidden" name="id" id="delete-group-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 添加NPC模态框 -->
<div id="add-npc-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加NPC</h3>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="add_npc">
            
            <div class="form-row">
                <div class="form-group half">
                    <label for="add-name">名称</label>
                    <input type="text" id="add-name" name="name" required>
                </div>
                
                <div class="form-group half">
                    <label for="add-level">等级</label>
                    <input type="number" id="add-level" name="level" min="1" value="1">
                </div>
            </div>
            
            <div class="form-group">
                <label for="add-group-id">所属分组</label>
                <select id="add-group-id" name="group_id">
                    <option value="">-- 选择分组 --</option>
                    <?php foreach ($groups as $group): ?>
                        <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-description">描述</label>
                <textarea id="add-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group half">
                    <label for="add-avatar">头像URL</label>
                    <input type="text" id="add-avatar" name="avatar" placeholder="可选">
                </div>
                
                <div class="form-group half">
                    <label for="add-dialogue-tree">默认对话树</label>
                    <select id="add-dialogue-tree" name="default_dialogue_tree_id">
                        <option value="">-- 选择对话树 --</option>
                        <?php foreach ($dialogue_trees as $tree): ?>
                            <option value="<?php echo $tree['id']; ?>"><?php echo htmlspecialchars($tree['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group half checkbox-group">
                    <label>
                        <input type="checkbox" name="is_merchant" id="add-is-merchant">
                        是商人
                    </label>
                </div>
                
                <div class="form-group half checkbox-group">
                    <label>
                        <input type="checkbox" name="is_quest_giver" id="add-is-quest-giver">
                        是任务发放者
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="add-behavior-script">行为脚本 (可选)</label>
                <textarea id="add-behavior-script" name="behavior_script" rows="5" class="code-editor"></textarea>
                <p class="form-help">PHP代码，定义NPC的特殊行为</p>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑NPC模态框 -->
<div id="edit-npc-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑NPC</h3>
        
        <form method="post" action="npcs.php">
            <input type="hidden" name="action" value="update_npc">
            <input type="hidden" name="id" id="edit-id" value="">
            
            <div class="form-row">
                <div class="form-group half">
                    <label for="edit-name">名称</label>
                    <input type="text" id="edit-name" name="name" required>
                </div>
                
                <div class="form-group half">
                    <label for="edit-level">等级</label>
                    <input type="number" id="edit-level" name="level" min="1" value="1">
                </div>
            </div>
            
            <div class="form-group">
                <label for="edit-group-id">所属分组</label>
                <select id="edit-group-id" name="group_id">
                    <option value="">-- 选择分组 --</option>
                    <?php foreach ($groups as $group): ?>
                        <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-description">描述</label>
                <textarea id="edit-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-row">
                <div class="form-group half">
                    <label for="edit-avatar">头像URL</label>
                    <input type="text" id="edit-avatar" name="avatar" placeholder="可选">
                </div>
                
                <div class="form-group half">
                    <label for="edit-dialogue-tree">默认对话树</label>
                    <select id="edit-dialogue-tree" name="default_dialogue_tree_id">
                        <option value="">-- 选择对话树 --</option>
                        <?php foreach ($dialogue_trees as $tree): ?>
                            <option value="<?php echo $tree['id']; ?>"><?php echo htmlspecialchars($tree['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group half checkbox-group">
                    <label>
                        <input type="checkbox" name="is_merchant" id="edit-is-merchant">
                        是商人
                    </label>
                </div>
                
                <div class="form-group half checkbox-group">
                    <label>
                        <input type="checkbox" name="is_quest_giver" id="edit-is-quest-giver">
                        是任务发放者
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="edit-behavior-script">行为脚本 (可选)</label>
                <textarea id="edit-behavior-script" name="behavior_script" rows="5" class="code-editor"></textarea>
                <p class="form-help">PHP代码，定义NPC的特殊行为</p>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 新增：NPC装备管理模态框 -->
<div id="npc-inventory-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>NPC装备管理 - <span id="inventory-npc-name"></span></h3>
        
        <div class="tabs">
            <div class="tab active" data-tab="equipped-items">已装备</div>
            <div class="tab" data-tab="inventory-items">背包物品</div>
            <div class="tab" data-tab="add-items">添加物品</div>
        </div>
        
        <div class="tab-content active" id="equipped-items">
            <div class="equipment-slots">
                <div class="equipment-slot" data-slot="Head">
                    <div class="slot-name">头部</div>
                    <div class="slot-item" id="slot-Head"></div>
                </div>
                <div class="equipment-slot" data-slot="Neck">
                    <div class="slot-name">颈部</div>
                    <div class="slot-item" id="slot-Neck"></div>
                </div>
                <div class="equipment-slot" data-slot="Body">
                    <div class="slot-name">身体</div>
                    <div class="slot-item" id="slot-Body"></div>
                </div>
                <div class="equipment-slot" data-slot="Back">
                    <div class="slot-name">背部</div>
                    <div class="slot-item" id="slot-Back"></div>
                </div>
                <div class="equipment-slot" data-slot="Finger">
                    <div class="slot-name">手指</div>
                    <div class="slot-item" id="slot-Finger"></div>
                </div>
                <div class="equipment-slot" data-slot="LeftHand">
                    <div class="slot-name">左手</div>
                    <div class="slot-item" id="slot-LeftHand"></div>
                </div>
                <div class="equipment-slot" data-slot="RightHand">
                    <div class="slot-name">右手</div>
                    <div class="slot-item" id="slot-RightHand"></div>
                </div>
                <div class="equipment-slot" data-slot="TwoHanded">
                    <div class="slot-name">双手</div>
                    <div class="slot-item" id="slot-TwoHanded"></div>
                </div>
            </div>
        </div>
        
        <div class="tab-content" id="inventory-items">
            <div class="inventory-list">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th>物品名称</th>
                            <th>类型</th>
                            <th>数量</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="npc-inventory-list">
                        <!-- 物品列表将通过JS动态加载 -->
                    </tbody>
                </table>
            </div>
        </div>
        
        <div class="tab-content" id="add-items">
            <div class="form-row">
                <div class="form-group">
                    <label for="item-category-filter">装备位置</label>
                    <select id="item-category-filter" class="form-control">
                        <option value="">所有位置</option>
                        <option value="Head">头部</option>
                        <option value="Neck">颈部</option>
                        <option value="Body">身体</option>
                        <option value="Back">背部</option>
                        <option value="Finger">手指</option>
                        <option value="LeftHand">左手</option>
                        <option value="RightHand">右手</option>
                        <option value="TwoHanded">双手</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="item-search">搜索物品</label>
                    <input type="text" id="item-search" class="form-control" placeholder="输入物品名称...">
                </div>
            </div>
            
            <div class="form-group">
                <label for="item-select">选择物品</label>
                <select id="item-select" class="form-control"></select>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="item-quantity">数量</label>
                    <input type="number" id="item-quantity" class="form-control" value="1" min="1">
                </div>
                <div class="form-group">
                    <label for="item-slot">装备槽位 (仅装备类)</label>
                    <select id="item-slot" class="form-control">
                        <option value="">不装备</option>
                        <option value="Head">头部</option>
                        <option value="Neck">颈部</option>
                        <option value="Body">身体</option>
                        <option value="Back">背部</option>
                        <option value="Finger">手指</option>
                        <option value="LeftHand">左手</option>
                        <option value="RightHand">右手</option>
                        <option value="TwoHanded">双手</option>
                    </select>
                </div>
            </div>
            
            <div class="form-actions">
                <button type="button" id="add-item-to-npc" class="btn btn-primary">添加物品</button>
            </div>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn modal-cancel">关闭</button>
        </div>
    </div>
</div>



<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/php/php.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    var addModal = document.getElementById('add-npc-modal');
    var editModal = document.getElementById('edit-npc-modal');
    var deleteModal = document.getElementById('delete-npc-modal');
    var inventoryModal = document.getElementById('npc-inventory-modal');
    var addGroupModal = document.getElementById('add-group-modal');
    var editGroupModal = document.getElementById('edit-group-modal');
    var deleteGroupModal = document.getElementById('delete-group-modal');
    var addBehaviorEditor, editBehaviorEditor;
    var currentNpcId = null;
    
    // 初始化代码编辑器
    if (document.getElementById('add-behavior-script')) {
        addBehaviorEditor = CodeMirror.fromTextArea(document.getElementById('add-behavior-script'), {
            mode: 'application/x-httpd-php',
            theme: 'material-darker',
            lineNumbers: true,
            indentUnit: 4
        });
    }
    
    // 添加NPC按钮
    document.getElementById('add-npc-btn').addEventListener('click', function() {
        addModal.style.display = 'block';
        
        // 重置表单
        document.querySelector('#add-npc-modal form').reset();
        if (addBehaviorEditor) {
            addBehaviorEditor.setValue('');
        }
    });
    
    // 添加分组按钮
    document.getElementById('add-group-btn').addEventListener('click', function() {
        addGroupModal.style.display = 'block';
    });
    
    // 编辑分组按钮事件
    document.querySelectorAll('.edit-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const id = this.dataset.id;
            const name = this.dataset.name;
            const type = this.dataset.type;
            const sort = this.dataset.sort;
            const description = this.dataset.description;
            
            document.getElementById('edit-group-id').value = id;
            document.getElementById('edit-group-name').value = name;
            document.getElementById('edit-group-type').value = type;
            document.getElementById('edit-group-sort').value = sort;
            document.getElementById('edit-group-description').value = description;
            
            editGroupModal.style.display = 'block';
        });
    });
    
    // 删除分组按钮事件
    document.querySelectorAll('.delete-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const id = this.dataset.id;
            const name = this.dataset.name;
            
            document.getElementById('delete-group-id').value = id;
            document.getElementById('delete-group-message').textContent = `确定要删除分组 "${name}" 吗？这将解除该分组与所有NPC的关联。`;
            
            deleteGroupModal.style.display = 'block';
        });
    });
    
    // 编辑NPC按钮
    document.querySelectorAll('.edit-npc').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var name = this.dataset.name;
            var description = this.dataset.description;
            var level = this.dataset.level;
            var avatar = this.dataset.avatar;
            var isMerchant = this.dataset.merchant === '1';
            var isQuestGiver = this.dataset.quest === '1';
            var behavior = this.dataset.behavior;
            var dialogueTreeId = this.dataset.dialogue;
            var groupId = this.dataset.group || '';
            
            document.getElementById('edit-id').value = id;
            document.getElementById('edit-name').value = name;
            document.getElementById('edit-description').value = description;
            document.getElementById('edit-level').value = level;
            document.getElementById('edit-avatar').value = avatar;
            document.getElementById('edit-is-merchant').checked = isMerchant;
            document.getElementById('edit-is-quest-giver').checked = isQuestGiver;
            document.getElementById('edit-dialogue-tree').value = dialogueTreeId;
            
            if (document.getElementById('edit-group-id')) {
                document.getElementById('edit-group-id').value = groupId;
            }
            
            // 初始化编辑器
            if (editBehaviorEditor) {
                editBehaviorEditor.toTextArea();
            }
            editBehaviorEditor = CodeMirror.fromTextArea(document.getElementById('edit-behavior-script'), {
                mode: 'application/x-httpd-php',
                theme: 'material-darker',
                lineNumbers: true,
                indentUnit: 4
            });
            editBehaviorEditor.setValue(behavior);
            
            editModal.style.display = 'block';
        });
    });
    
    // 删除NPC按钮
    document.querySelectorAll('.delete-npc').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var name = this.dataset.name;
            var instances = parseInt(this.dataset.instances);
            
            document.getElementById('delete-id').value = id;
            
            if (instances > 0) {
                document.getElementById('delete-npc-message').textContent = '无法删除NPC "' + name + '"，因为它有 ' + instances + ' 个实例正在使用。请先删除所有实例。';
                document.getElementById('confirm-delete-btn').disabled = true;
            } else {
                document.getElementById('delete-npc-message').textContent = '确定要删除NPC "' + name + '" 吗？此操作无法撤销。';
                document.getElementById('confirm-delete-btn').disabled = false;
            }
            
            deleteModal.style.display = 'block';
        });
    });
    
    // 新增：装备管理按钮
    document.querySelectorAll('.manage-inventory').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var npcId = this.dataset.id;
            var npcName = this.dataset.name;
            
            currentNpcId = npcId;
            document.getElementById('inventory-npc-name').textContent = npcName;
            
            // 加载NPC的装备和物品
            loadNpcInventory(npcId);
            
            // 加载所有可用物品
            loadAvailableItems();
            
            // 显示装备标签
            document.querySelector('#npc-inventory-modal .tab[data-tab="equipped-items"]').click();
            
            inventoryModal.style.display = 'block';
        });
    });
    
    // 新增：标签切换
    document.querySelectorAll('#npc-inventory-modal .tab').forEach(function(tab) {
        tab.addEventListener('click', function() {
            // 移除所有标签和内容的活动状态
            document.querySelectorAll('#npc-inventory-modal .tab').forEach(t => t.classList.remove('active'));
            document.querySelectorAll('#npc-inventory-modal .tab-content').forEach(c => c.classList.remove('active'));
            
            // 设置当前标签和内容为活动状态
            this.classList.add('active');
            document.getElementById(this.dataset.tab).classList.add('active');
        });
    });
    
    // 新增：加载NPC装备和物品
    function loadNpcInventory(npcId) {
        fetch(`api_npcs.php?action=get_npc_inventory&npc_id=${npcId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // 清空所有装备槽
                    document.querySelectorAll('.slot-item').forEach(slot => {
                        slot.innerHTML = '<div class="empty-slot">空</div>';
                        slot.dataset.itemId = '';
                    });
                    
                    // 填充已装备物品
                    data.equipped.forEach(item => {
                        // 直接使用数据库中的槽位名称
                        const slotElement = document.getElementById(`slot-${item.slot}`);
                        if (slotElement) {
                            slotElement.innerHTML = `
                                <div class="item-card">
                                    <div class="item-name">${item.name}</div>
                                    <div class="item-actions">
                                        <button class="btn btn-sm btn-danger unequip-item" data-id="${item.id}">卸下</button>
                                    </div>
                                </div>
                            `;
                            slotElement.dataset.itemId = item.id;
                        }
                    });
                    
                    // 填充背包物品
                    const inventoryList = document.getElementById('npc-inventory-list');
                    inventoryList.innerHTML = '';
                    
                    if (data.inventory.length === 0) {
                        inventoryList.innerHTML = '<tr><td colspan="4" class="text-center">NPC背包中没有物品</td></tr>';
                    } else {
                        data.inventory.forEach(item => {
                            const row = document.createElement('tr');
                            row.innerHTML = `
                                <td>${item.name}</td>
                                <td>${getCategoryName(item.category)}</td>
                                <td>${item.quantity}</td>
                                <td>
                                    ${item.category === 'Equipment' ? 
                                        `<button class="btn btn-sm equip-item" data-id="${item.id}">装备</button>` : ''}
                                    <button class="btn btn-sm btn-danger remove-item" data-id="${item.id}">删除</button>
                                </td>
                            `;
                            inventoryList.appendChild(row);
                        });
                    }
                    
                    // 添加事件监听器
                    addInventoryEventListeners();
                } else {
                    showToast('加载NPC装备失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载NPC装备出错:', error);
                showToast('加载NPC装备出错，请查看控制台获取详细信息。', 'error');
            });
    }
    
    // 新增：加载可用物品
    function loadAvailableItems() {
        fetch('api_npcs.php?action=get_available_items')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const itemSelect = document.getElementById('item-select');
                    itemSelect.innerHTML = '<option value="">-- 选择物品 --</option>';
                    
                    data.items.forEach(item => {
                        const option = document.createElement('option');
                        option.value = item.id;
                        option.textContent = `${item.name} (${getSlotName(item.slot)})`;
                        option.dataset.slot = item.slot;
                        itemSelect.appendChild(option);
                    });
                    
                    // 添加筛选事件
                    document.getElementById('item-category-filter').addEventListener('change', filterItems);
                    document.getElementById('item-search').addEventListener('input', filterItems);
                    
                    // 添加物品选择事件，自动设置装备槽位
                    itemSelect.addEventListener('change', function() {
                        const selectedOption = this.options[this.selectedIndex];
                        if (selectedOption.dataset.slot) {
                            document.getElementById('item-slot').value = selectedOption.dataset.slot;
                        }
                    });
                } else {
                    showToast('加载物品列表失败：' + data.message, 'error');
                }
            })
            .catch(error => {
                console.error('加载物品列表出错:', error);
                showToast('加载物品列表出错，请查看控制台获取详细信息。', 'error');
            });
    }
    
    // 新增：筛选物品
    function filterItems() {
        const slotFilter = document.getElementById('item-category-filter').value;
        const searchFilter = document.getElementById('item-search').value.toLowerCase();
        const options = document.getElementById('item-select').options;
        
        for (let i = 1; i < options.length; i++) {
            const option = options[i];
            const matchesSlot = !slotFilter || option.dataset.slot === slotFilter;
            const matchesSearch = !searchFilter || option.text.toLowerCase().includes(searchFilter);
            
            option.style.display = matchesSlot && matchesSearch ? '' : 'none';
        }
    }
    
    // 获取装备位置的中文名称
    function getSlotName(slot) {
        const slotMap = {
            'Head': '头部',
            'Neck': '颈部',
            'Body': '身体',
            'Back': '背部',
            'Finger': '手指',
            'LeftHand': '左手',
            'RightHand': '右手',
            'TwoHanded': '双手'
        };
        return slotMap[slot] || slot;
    }
    
    // 新增：添加物品到NPC
    document.getElementById('add-item-to-npc').addEventListener('click', function() {
        const itemId = document.getElementById('item-select').value;
        const quantity = document.getElementById('item-quantity').value;
        const slot = document.getElementById('item-slot').value;
        
        if (!itemId) {
            showToast('请选择一个物品', 'warning');
            return;
        }
        
        const data = new FormData();
        data.append('action', 'add_item_to_npc');
        data.append('npc_id', currentNpcId);
        data.append('item_template_id', itemId);
        data.append('quantity', quantity);
        data.append('slot', slot);
        
        fetch('api_npcs.php', {
            method: 'POST',
            body: data
        })
        .then(response => response.json())
        .then(result => {
            if (result.success) {
                showToast('物品添加成功', 'success');
                loadNpcInventory(currentNpcId);
            } else {
                showToast('添加物品失败：' + result.message, 'error');
            }
        })
        .catch(error => {
            console.error('添加物品出错:', error);
            showToast('添加物品出错，请查看控制台获取详细信息。', 'error');
        });
    });
    
    // 新增：添加物品相关事件监听器
    function addInventoryEventListeners() {
        // 装备物品
        document.querySelectorAll('.equip-item').forEach(btn => {
            btn.addEventListener('click', function() {
                const inventoryId = this.dataset.id;
                
                // 创建装备槽位选择对话框
                const slotSelectionDialog = document.createElement('div');
                slotSelectionDialog.className = 'modal';
                slotSelectionDialog.style.display = 'block';
                slotSelectionDialog.innerHTML = `
                    <div class="modal-content" style="max-width: 400px;">
                        <span class="close">&times;</span>
                        <h3>选择装备槽位</h3>
                        <div class="form-group">
                            <label for="slot-selection">装备槽位:</label>
                            <select id="slot-selection" class="form-control">
                            <option value="Head">头部</option>
                            <option value="Neck">颈部</option>
                            <option value="Body">身体</option>
                            <option value="Back">背部</option>
                            <option value="Finger">手指</option>
                            <option value="LeftHand">左手</option>
                            <option value="RightHand">右手</option>
                            <option value="TwoHanded">双手</option>
                        </select>
                        </div>
                        <div class="form-actions" style="text-align: center; margin-top: 15px;">
                            <button id="confirm-slot-btn" class="btn btn-primary">确认</button>
                            <button class="btn modal-cancel">取消</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(slotSelectionDialog);
                
                // 关闭对话框的事件
                const closeDialog = () => {
                    slotSelectionDialog.remove();
                };
                
                // 点击关闭按钮
                slotSelectionDialog.querySelector('.close').addEventListener('click', closeDialog);
                slotSelectionDialog.querySelector('.modal-cancel').addEventListener('click', closeDialog);
                
                // 点击确认按钮
                slotSelectionDialog.querySelector('#confirm-slot-btn').addEventListener('click', () => {
                    const slot = document.getElementById('slot-selection').value;
                    closeDialog();
                
                    const data = new FormData();
                    data.append('action', 'equip_item');
                    data.append('inventory_id', inventoryId);
                    data.append('slot', slot);
                    
                    fetch('api_npcs.php', {
                        method: 'POST',
                        body: data
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showToast('物品装备成功', 'success');
                            loadNpcInventory(currentNpcId);
                        } else {
                            showToast('装备物品失败：' + result.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('装备物品出错:', error);
                        showToast('装备物品出错，请查看控制台获取详细信息。', 'error');
                    });
                });
            });
        });
        
        // 卸下装备
        document.querySelectorAll('.unequip-item').forEach(btn => {
            btn.addEventListener('click', function() {
                const inventoryId = this.dataset.id;
                
                const data = new FormData();
                data.append('action', 'unequip_item');
                data.append('inventory_id', inventoryId);
                
                fetch('api_npcs.php', {
                    method: 'POST',
                    body: data
                })
                .then(response => response.json())
                .then(result => {
                    if (result.success) {
                        showToast('物品卸下成功', 'success');
                        loadNpcInventory(currentNpcId);
                    } else {
                        showToast('卸下物品失败：' + result.message, 'error');
                    }
                })
                .catch(error => {
                    console.error('卸下物品出错:', error);
                    showToast('卸下物品出错，请查看控制台获取详细信息。', 'error');
                });
            });
        });
        
        // 删除物品
        document.querySelectorAll('.remove-item').forEach(btn => {
            btn.addEventListener('click', function() {
                // 创建确认对话框
                const confirmDialog = document.createElement('div');
                confirmDialog.className = 'modal';
                confirmDialog.style.display = 'block';
                confirmDialog.innerHTML = `
                    <div class="modal-content" style="max-width: 400px;">
                        <span class="close">&times;</span>
                        <h3>确认删除</h3>
                        <p>确定要删除此物品吗？此操作无法撤销。</p>
                        <div class="form-actions" style="text-align: center; margin-top: 15px;">
                            <button id="confirm-delete-btn" class="btn btn-danger">确认删除</button>
                            <button class="btn modal-cancel">取消</button>
                        </div>
                    </div>
                `;
                
                document.body.appendChild(confirmDialog);
                
                // 关闭对话框的事件
                const closeDialog = () => {
                    confirmDialog.remove();
                };
                
                // 点击关闭按钮
                confirmDialog.querySelector('.close').addEventListener('click', closeDialog);
                confirmDialog.querySelector('.modal-cancel').addEventListener('click', closeDialog);
                
                // 点击确认删除按钮
                confirmDialog.querySelector('#confirm-delete-btn').addEventListener('click', () => {
                    closeDialog();
                    
                    const inventoryId = this.dataset.id;
                    
                    const data = new FormData();
                    data.append('action', 'remove_item');
                    data.append('inventory_id', inventoryId);
                    
                    fetch('api_npcs.php', {
                        method: 'POST',
                        body: data
                    })
                    .then(response => response.json())
                    .then(result => {
                        if (result.success) {
                            showToast('物品删除成功', 'success');
                            loadNpcInventory(currentNpcId);
                        } else {
                            showToast('删除物品失败：' + result.message, 'error');
                        }
                    })
                    .catch(error => {
                        console.error('删除物品出错:', error);
                        showToast('删除物品出错，请查看控制台获取详细信息。', 'error');
                    });
                });
            });
        });
    }
    
    // 新增：获取物品分类名称
    function getCategoryName(category) {
        const categoryMap = {
            'Equipment': '装备',
            'Gem': '宝石',
            'Material': '材料',
            'Potion': '药水',
            'Rune': '符文',
            'Misc': '杂项',
            'Scroll': '卷轴'
        };
        return categoryMap[category] || category;
    }
    
    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            addModal.style.display = 'none';
            editModal.style.display = 'none';
            deleteModal.style.display = 'none';
            inventoryModal.style.display = 'none';
            addGroupModal.style.display = 'none';
            editGroupModal.style.display = 'none';
            deleteGroupModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == addModal) {
            addModal.style.display = 'none';
        }
        if (event.target == editModal) {
            editModal.style.display = 'none';
        }
        if (event.target == deleteModal) {
            deleteModal.style.display = 'none';
        }
        if (event.target == inventoryModal) {
            inventoryModal.style.display = 'none';
        }
        if (event.target == addGroupModal) {
            addGroupModal.style.display = 'none';
        }
        if (event.target == editGroupModal) {
            editGroupModal.style.display = 'none';
        }
        if (event.target == deleteGroupModal) {
            deleteGroupModal.style.display = 'none';
        }
    });
});

/**
 * 显示简单气泡提示 - 完全使用内联样式，不依赖外部CSS
 * @param {string} message 提示消息
 * @param {string} type 提示类型：success, error, info, warning
 * @param {number} duration 显示时长(毫秒)，默认3000ms
 */
function showSimpleToast(message, type = 'info', duration = 3000) {
    
    // 创建一个新的提示元素
    const toast = document.createElement('div');
    
    // 设置样式
    toast.style.position = 'fixed';
    toast.style.top = '20px';
    toast.style.right = '20px';
    toast.style.zIndex = '999999';  // 非常高的z-index确保显示在最上层
    toast.style.minWidth = '250px';
    toast.style.maxWidth = '80%';
    toast.style.padding = '15px';
    toast.style.borderRadius = '5px';
    toast.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
    toast.style.display = 'flex';
    toast.style.justifyContent = 'space-between';
    toast.style.alignItems = 'center';
    toast.style.fontFamily = 'Arial, sans-serif';
    toast.style.fontSize = '14px';
    toast.style.opacity = '0';
    toast.style.transform = 'translateX(100%)';
    toast.style.transition = 'opacity 0.3s, transform 0.3s';
    
    // 设置背景色
    switch(type) {
        case 'success':
            toast.style.backgroundColor = '#51a351';
            toast.style.color = 'white';
            break;
        case 'error':
            toast.style.backgroundColor = '#bd362f';
            toast.style.color = 'white';
            break;
        case 'warning':
            toast.style.backgroundColor = '#f89406';
            toast.style.color = 'white';
            break;
        default: // info
            toast.style.backgroundColor = '#2f96b4';
            toast.style.color = 'white';
    }
    
    // 添加消息
    toast.textContent = message;
    
    // 添加关闭按钮
    const closeBtn = document.createElement('span');
    closeBtn.textContent = '×';
    closeBtn.style.marginLeft = '10px';
    closeBtn.style.cursor = 'pointer';
    closeBtn.style.fontWeight = 'bold';
    closeBtn.style.fontSize = '20px';
    closeBtn.onclick = function() {
        removeSimpleToast(toast);
    };
    
    toast.appendChild(closeBtn);
    document.body.appendChild(toast);
    
    // 触发动画
    setTimeout(() => {
        toast.style.opacity = '1';
        toast.style.transform = 'translateX(0)';
    }, 10);
    
    // 自动关闭
    setTimeout(() => {
        removeSimpleToast(toast);
    }, duration);
    
    return toast;
}

/**
 * 移除简单气泡提示
 * @param {HTMLElement} toast 要移除的提示元素
 */
function removeSimpleToast(toast) {
    toast.style.opacity = '0';
    toast.style.transform = 'translateX(100%)';
    
    setTimeout(() => {
        if (toast && toast.parentNode) {
            toast.remove();
        }
    }, 300);
}

/**
 * 原始的显示气泡提示函数 - 保留以兼容现有代码
 */
function showToast(message, type = 'info', duration = 3000) {
    return showSimpleToast(message, type, duration);
}
</script>

<?php require_once 'layout_footer.php'; ?> 