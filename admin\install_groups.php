<?php
// admin/install_groups.php
// 安装脚本，用于创建和配置分组功能

require_once '../config/Database.php';

$success_messages = [];
$error_messages = [];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 开始事务
    $pdo->beginTransaction();
    
    // 1. 创建对话树分组表
    $sql = "
    CREATE TABLE IF NOT EXISTS `dialogue_groups` (
      `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL COMMENT '分组名称',
      `type` enum('main','side','other') NOT NULL DEFAULT 'other' COMMENT '分组类型：主线/支线/其他',
      `description` text COMMENT '分组描述',
      `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='对话树分组表';
    ";
    $pdo->exec($sql);
    $success_messages[] = "创建对话树分组表成功";
    
    // 2. 创建任务分组表
    $sql = "
    CREATE TABLE IF NOT EXISTS `quest_groups` (
      `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL COMMENT '分组名称',
      `type` enum('main','side','daily','achievement','hidden') NOT NULL DEFAULT 'side' COMMENT '分组类型',
      `description` text COMMENT '分组描述',
      `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='任务分组表';
    ";
    $pdo->exec($sql);
    $success_messages[] = "创建任务分组表成功";
    
    // 3. 创建NPC分组表
    $sql = "
    CREATE TABLE IF NOT EXISTS `npc_groups` (
      `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
      `name` varchar(100) NOT NULL COMMENT '分组名称',
      `type` enum('merchant','quest','boss','normal') NOT NULL DEFAULT 'normal' COMMENT '分组类型',
      `description` text COMMENT '分组描述',
      `sort_order` int(11) NOT NULL DEFAULT '0' COMMENT '排序顺序',
      `created_at` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
      `updated_at` datetime NULL DEFAULT NULL COMMENT '更新时间',
      PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='NPC分组表';
    ";
    $pdo->exec($sql);
    $success_messages[] = "创建NPC分组表成功";
    
    // 4. 修改对话树表，添加分组关联
    try {
        // 检查列是否已存在
        $stmt = $pdo->query("SHOW COLUMNS FROM `dialogue_trees` LIKE 'group_id'");
        if ($stmt->rowCount() == 0) {
            $sql = "ALTER TABLE `dialogue_trees` ADD COLUMN `group_id` int(10) UNSIGNED DEFAULT NULL AFTER `name`";
            $pdo->exec($sql);
            $sql = "ALTER TABLE `dialogue_trees` ADD CONSTRAINT `fk_dialogue_tree_group` FOREIGN KEY (`group_id`) REFERENCES `dialogue_groups` (`id`) ON DELETE SET NULL";
            $pdo->exec($sql);
            $success_messages[] = "修改对话树表添加分组关联成功";
        } else {
            $success_messages[] = "对话树表已有分组关联字段，无需修改";
        }
    } catch (PDOException $e) {
        $error_messages[] = "修改对话树表错误: " . $e->getMessage();
    }
    
    // 5. 修改任务表，添加分组关联
    try {
        // 检查列是否已存在
        $stmt = $pdo->query("SHOW COLUMNS FROM `quests` LIKE 'group_id'");
        if ($stmt->rowCount() == 0) {
            $sql = "ALTER TABLE `quests` ADD COLUMN `group_id` int(10) UNSIGNED DEFAULT NULL AFTER `title`";
            $pdo->exec($sql);
            $sql = "ALTER TABLE `quests` ADD CONSTRAINT `fk_quest_group` FOREIGN KEY (`group_id`) REFERENCES `quest_groups` (`id`) ON DELETE SET NULL";
            $pdo->exec($sql);
            $success_messages[] = "修改任务表添加分组关联成功";
        } else {
            $success_messages[] = "任务表已有分组关联字段，无需修改";
        }
    } catch (PDOException $e) {
        $error_messages[] = "修改任务表错误: " . $e->getMessage();
    }
    
    // 6. 修改NPC表，添加分组关联
    try {
        // 检查列是否已存在
        $stmt = $pdo->query("SHOW COLUMNS FROM `npc_templates` LIKE 'group_id'");
        if ($stmt->rowCount() == 0) {
            $sql = "ALTER TABLE `npc_templates` ADD COLUMN `group_id` int(10) UNSIGNED DEFAULT NULL AFTER `name`";
            $pdo->exec($sql);
            $sql = "ALTER TABLE `npc_templates` ADD CONSTRAINT `fk_npc_group` FOREIGN KEY (`group_id`) REFERENCES `npc_groups` (`id`) ON DELETE SET NULL";
            $pdo->exec($sql);
            $success_messages[] = "修改NPC表添加分组关联成功";
        } else {
            $success_messages[] = "NPC表已有分组关联字段，无需修改";
        }
    } catch (PDOException $e) {
        $error_messages[] = "修改NPC表错误: " . $e->getMessage();
    }
    
    // 7. 添加初始分组数据
    // 对话树分组
    $dialogueGroups = [
        ['name' => '主线剧情', 'type' => 'main', 'description' => '游戏主线故事相关的对话', 'sort_order' => 100],
        ['name' => '支线任务', 'type' => 'side', 'description' => '游戏支线任务相关的对话', 'sort_order' => 200],
        ['name' => 'NPC日常对话', 'type' => 'other', 'description' => '日常NPC互动对话', 'sort_order' => 300],
        ['name' => '教程对话', 'type' => 'other', 'description' => '游戏教程和指引对话', 'sort_order' => 400],
    ];
    
    foreach ($dialogueGroups as $group) {
        // 检查是否已存在同名分组
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM dialogue_groups WHERE name = ?");
        $stmt->execute([$group['name']]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            $stmt = $pdo->prepare("
                INSERT INTO dialogue_groups (name, type, description, sort_order)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $group['name'],
                $group['type'],
                $group['description'],
                $group['sort_order']
            ]);
        }
    }
    $success_messages[] = "添加对话树初始分组数据成功";
    
    // 任务分组
    $questGroups = [
        ['name' => '主线任务', 'type' => 'main', 'description' => '推动游戏主线剧情发展的任务', 'sort_order' => 100],
        ['name' => '城镇支线', 'type' => 'side', 'description' => '城镇地区的支线任务', 'sort_order' => 200],
        ['name' => '野外支线', 'type' => 'side', 'description' => '野外地区的支线任务', 'sort_order' => 300],
        ['name' => '每日任务', 'type' => 'daily', 'description' => '可重复完成的每日任务', 'sort_order' => 400],
        ['name' => '隐藏任务', 'type' => 'hidden', 'description' => '特殊条件触发的隐藏任务', 'sort_order' => 500],
    ];
    
    foreach ($questGroups as $group) {
        // 检查是否已存在同名分组
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM quest_groups WHERE name = ?");
        $stmt->execute([$group['name']]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            $stmt = $pdo->prepare("
                INSERT INTO quest_groups (name, type, description, sort_order)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $group['name'],
                $group['type'],
                $group['description'],
                $group['sort_order']
            ]);
        }
    }
    $success_messages[] = "添加任务初始分组数据成功";
    
    // NPC分组
    $npcGroups = [
        ['name' => '商人NPC', 'type' => 'merchant', 'description' => '提供物品买卖服务的NPC', 'sort_order' => 100],
        ['name' => '任务NPC', 'type' => 'quest', 'description' => '提供任务的NPC', 'sort_order' => 200],
        ['name' => 'BOSS', 'type' => 'boss', 'description' => '特殊强大的敌对NPC', 'sort_order' => 300],
        ['name' => '村民', 'type' => 'normal', 'description' => '普通村民NPC', 'sort_order' => 400],
        ['name' => '守卫', 'type' => 'normal', 'description' => '城镇和据点的守卫NPC', 'sort_order' => 500],
    ];
    
    foreach ($npcGroups as $group) {
        // 检查是否已存在同名分组
        $stmt = $pdo->prepare("SELECT COUNT(*) FROM npc_groups WHERE name = ?");
        $stmt->execute([$group['name']]);
        $exists = $stmt->fetchColumn();
        
        if (!$exists) {
            $stmt = $pdo->prepare("
                INSERT INTO npc_groups (name, type, description, sort_order)
                VALUES (?, ?, ?, ?)
            ");
            $stmt->execute([
                $group['name'],
                $group['type'],
                $group['description'],
                $group['sort_order']
            ]);
        }
    }
    $success_messages[] = "添加NPC初始分组数据成功";
    
    // 8. 尝试自动分组现有数据
    // 按照NPC类型自动分组
    $stmt = $pdo->prepare("
        UPDATE npc_templates nt 
        JOIN npc_groups ng ON 
        ((nt.is_merchant = 1 AND ng.type = 'merchant') OR 
         (nt.is_quest_giver = 1 AND ng.type = 'quest') OR
         (nt.is_merchant = 0 AND nt.is_quest_giver = 0 AND ng.type = 'normal' AND ng.name = '村民'))
        SET nt.group_id = ng.id
        WHERE nt.group_id IS NULL
    ");
    $stmt->execute();
    $success_messages[] = "尝试自动分组现有NPC数据";
    
    // 提交事务
    $pdo->commit();
    
    $success_messages[] = "所有操作已成功完成！";
    
} catch (PDOException $e) {
    // 回滚事务
    if ($pdo) {
        $pdo->rollBack();
    }
    $error_messages[] = "安装过程中发生错误: " . $e->getMessage();
}

// 页面显示
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分组功能安装</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1 { color: #333; }
        .success, .error { margin: 20px 0; padding: 10px; border-radius: 4px; }
        .success { background-color: #d4edda; color: #155724; }
        .error { background-color: #f8d7da; color: #721c24; }
        ul { margin-top: 10px; }
        .back-link { margin-top: 20px; display: inline-block; }
    </style>
</head>
<body>
    <h1>分组功能安装</h1>
    
    <?php if (!empty($success_messages)): ?>
        <div class="success">
            <h3>成功信息</h3>
            <ul>
                <?php foreach ($success_messages as $message): ?>
                    <li><?php echo htmlspecialchars($message); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <?php if (!empty($error_messages)): ?>
        <div class="error">
            <h3>错误信息</h3>
            <ul>
                <?php foreach ($error_messages as $message): ?>
                    <li><?php echo htmlspecialchars($message); ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php endif; ?>
    
    <a href="index.php" class="back-link">返回管理首页</a>
</body>
</html> 