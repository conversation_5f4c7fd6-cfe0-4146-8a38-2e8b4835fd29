<?php
// admin/api_items.php
require_once '../config/Database.php';
require_once 'auth.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '无效的操作'];

// Main execution block
try {
    $action = $_REQUEST['action'] ?? '';
    dispatch($action);
} catch (Exception $e) {
    // Make sure to get the connection from the Database class instance
    $db = Database::getInstance();
    $conn = $db->getConnection();
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    http_response_code(400);
    $response = ['success' => false, 'message' => $e->getMessage()];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function dispatch($action) {
    $isPost = $_SERVER['REQUEST_METHOD'] === 'POST';

    switch ($action) {
        case 'list':
            listItems();
            break;
        case 'get':
            getItem();
            break;
        case 'get_all':
            getAllItemsSimple();
            break;
        case 'create':
            if (!$isPost) throw new Exception("Create requires POST.");
            createItem();
            break;
        case 'update':
            if (!$isPost) throw new Exception("Update requires POST.");
            updateItem();
            break;
        case 'delete':
            if (!$isPost) throw new Exception("Delete requires POST.");
            deleteItem();
            break;
        case 'get_jobs':
            getJobs();
            break;
        default:
            throw new Exception('Invalid action.');
    }
}

function listItems() {
    $db = Database::getInstance();
    
    // Pagination, searching, and filtering parameters
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $search = $_GET['search'] ?? '';
    $category = $_GET['category'] ?? 'All';
    $slot = $_GET['slot'] ?? 'All';
    $equipmentType = $_GET['equipment_type'] ?? 'All';
    $itemsPerPage = 12; // Or get from a config file
    $offset = ($page - 1) * $itemsPerPage;

    // Base query
    $baseQuery = "FROM item_templates it
                  LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
                  LEFT JOIN jobs j_grants ON ed.grants_job_id = j_grants.id
                  LEFT JOIN jobs j_restriction ON ed.job_restriction = j_restriction.id";
    $countQuery = "SELECT COUNT(it.id) as total " . $baseQuery;
    $dataQuery = "SELECT it.*, ed.slot, ed.job_restriction, ed.stats, ed.sockets, ed.grants_job_id,
                  j_grants.name as granted_job_name, j_restriction.name as job_restriction_name,
                  COALESCE(it.equipment_type, 'player') as equipment_type " . $baseQuery;

    // Filters
    $whereClauses = [];
    $params = [];

    if (!empty($search)) {
        $whereClauses[] = "(it.name LIKE ? OR it.item_id LIKE ?)";
        $params[] = "%$search%";
        $params[] = "%$search%";
    }

    if ($category !== 'All' && !empty($category)) {
        $whereClauses[] = "it.category = ?";
        $params[] = $category;
    }

    if ($slot !== 'All' && !empty($slot)) {
        $whereClauses[] = "ed.slot = ?";
        $params[] = $slot;
    }

    if ($equipmentType !== 'All' && !empty($equipmentType)) {
        if ($equipmentType === 'player') {
            $whereClauses[] = "(it.equipment_type = 'player' OR it.equipment_type IS NULL)";
        } else {
            $whereClauses[] = "it.equipment_type = ?";
            $params[] = $equipmentType;
        }
    }

    if (!empty($whereClauses)) {
        $whereSql = " WHERE " . implode(" AND ", $whereClauses);
        $countQuery .= $whereSql;
        $dataQuery .= $whereSql;
    }

    // Get total items count for pagination
    $countStmt = $db->query($countQuery, $params);
    $totalItems = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
    $totalPages = ceil($totalItems / $itemsPerPage);

    // Get the data for the current page
    $dataQuery .= " ORDER BY it.id DESC LIMIT ? OFFSET ?";
    $params[] = $itemsPerPage;
    $params[] = $offset;

    $dataStmt = $db->query($dataQuery, $params);
    $items = $dataStmt->fetchAll(PDO::FETCH_ASSOC);

    // Prepare response
    $response = [
        'success' => true,
        'data' => $items,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'total_items' => $totalItems,
            'items_per_page' => $itemsPerPage
        ]
    ];

    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function getItem() {
    $id = $_GET['id'] ?? 0;
    if (!$id) throw new Exception("ID not provided.");
    
    $db = Database::getInstance();
    $stmt = $db->query("SELECT it.*, ed.slot, ed.job_restriction, ed.stats, ed.sockets, ed.grants_job_id,
                        j_grants.name as granted_job_name, j_restriction.name as job_restriction_name
                        FROM item_templates it
                        LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
                        LEFT JOIN jobs j_grants ON ed.grants_job_id = j_grants.id
                        LEFT JOIN jobs j_restriction ON ed.job_restriction = j_restriction.id
                        WHERE it.id = ?", [$id]);
    $item = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if ($item) {
        echo json_encode(['success' => true, 'item' => $item], JSON_UNESCAPED_UNICODE);
    } else {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => 'Item not found']);
    }
}

function getAllItemsSimple() {
    $db = Database::getInstance();
    $stmt = $db->query("SELECT id, item_id, name, category, is_consumable, stackable FROM item_templates ORDER BY id DESC");
    $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'data' => $items], JSON_UNESCAPED_UNICODE);
}

function getJobs() {
    $db = Database::getInstance();
    $stmt = $db->query("SELECT id, name FROM jobs ORDER BY id");
    $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    echo json_encode(['success' => true, 'jobs' => $jobs], JSON_UNESCAPED_UNICODE);
}

function updateItem() {
    $id = $_POST['id'] ?? 0;
    if (!$id) throw new Exception("Missing ID for update.");

    $data = parseItemData($_POST);
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    try {
        $conn->beginTransaction();

        $db->query(
            "UPDATE item_templates SET name=?, category=?, description=?, is_consumable=?, stackable=?, max_stack=?, buy_price=?, diamond_price=?, sell_price=?, effects=?, equipment_type=? WHERE id=?",
            [$data['name'], $data['category'], $data['description'], $data['is_consumable'], $data['stackable'], $data['max_stack'], $data['buy_price'], $data['diamond_price'], $data['sell_price'], $data['effects'], $data['equipment_type'], $id]
        );

        if ($data['category'] === 'Equipment') {
            // 验证item_template_id是否存在
            $checkStmt = $db->query("SELECT id FROM item_templates WHERE id = ?", [$id]);
            if (!$checkStmt->fetch()) {
                throw new Exception("要更新的item_template记录不存在，ID: {$id}");
            }
            $checkStmt->closeCursor();

            $stmt = $db->query("SELECT 1 FROM equipment_details WHERE item_template_id = ?", [$id]);
            if ($stmt->fetch()) {
                error_log("更新equipment_details: item_template_id={$id}");
                 $db->query(
                    "UPDATE equipment_details SET slot=?, job_restriction=?, stats=?, sockets=?, grants_job_id=? WHERE item_template_id=?",
                    [$data['slot'], $data['job_restriction'], $data['stats'], $data['sockets'], $data['grants_job_id'], $id]
                );
            } else {
                error_log("插入equipment_details: item_template_id={$id}");
                 $db->query(
                    "INSERT INTO equipment_details (item_template_id, slot, job_restriction, stats, sockets, grants_job_id) VALUES (?, ?, ?, ?, ?, ?)",
                    [$id, $data['slot'], $data['job_restriction'], $data['stats'], $data['sockets'], $data['grants_job_id']]
                );
            }
            $stmt->closeCursor();
        } else {
            $db->query("DELETE FROM equipment_details WHERE item_template_id = ?", [$id]);
        }
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Item updated successfully.'], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        if ($conn->inTransaction()) $conn->rollBack();
        throw $e;
    }
}

function createItem() {
    $data = parseItemData($_POST);

    $db = Database::getInstance();
    $conn = $db->getConnection();
    $pdo = $conn; // 使用同一个连接

    try {
        $conn->beginTransaction();

        // 获取下一个自增ID来生成item_id
        $stmt = $db->query("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'item_templates'");
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $nextId = $result['AUTO_INCREMENT'] ?? 1;
        $itemId = 'item_' . $nextId;

        // 添加调试信息
        error_log("准备插入item_templates: " . json_encode([
            'item_id' => $itemId,
            'name' => $data['name'],
            'category' => $data['category'],
            'description' => $data['description'],
            'is_consumable' => $data['is_consumable'],
            'stackable' => $data['stackable'],
            'max_stack' => $data['max_stack'],
            'buy_price' => $data['buy_price'],
            'diamond_price' => $data['diamond_price'],
            'sell_price' => $data['sell_price'],
            'effects' => $data['effects']
        ]));

        // 直接使用PDO连接来确保lastInsertId的正确性
        $stmt = $pdo->prepare("INSERT INTO item_templates (item_id, name, category, description, is_consumable, stackable, max_stack, buy_price, diamond_price, sell_price, effects, equipment_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
        $result = $stmt->execute([$itemId, $data['name'], $data['category'], $data['description'], $data['is_consumable'], $data['stackable'], $data['max_stack'], $data['buy_price'], $data['diamond_price'], $data['sell_price'], $data['effects'], $data['equipment_type']]);

        if (!$result) {
            $errorInfo = $stmt->errorInfo();
            throw new Exception("插入item_templates失败: " . $errorInfo[2]);
        }

        $lastId = $pdo->lastInsertId();
        error_log("item_templates插入成功，获得ID: {$lastId}");

        if (!$lastId || $lastId == 0) {
            throw new Exception("获取插入ID失败，lastInsertId返回: {$lastId}");
        }

        if ($data['category'] === 'Equipment') {
            // 添加调试信息
            error_log("准备插入equipment_details: item_template_id={$lastId}, slot={$data['slot']}, job_restriction={$data['job_restriction']}, stats={$data['stats']}, sockets={$data['sockets']}, grants_job_id={$data['grants_job_id']}");

            // 验证item_template_id是否存在（使用同一个PDO连接）
            $checkStmt = $pdo->prepare("SELECT id FROM item_templates WHERE id = ?");
            $checkStmt->execute([$lastId]);
            if (!$checkStmt->fetch()) {
                throw new Exception("刚创建的item_template记录不存在，ID: {$lastId}");
            }
            $checkStmt->closeCursor();

            // 插入equipment_details（使用同一个PDO连接）
            $equipStmt = $pdo->prepare("INSERT INTO equipment_details (item_template_id, slot, job_restriction, stats, sockets, grants_job_id) VALUES (?, ?, ?, ?, ?, ?)");
            $equipResult = $equipStmt->execute([$lastId, $data['slot'], $data['job_restriction'], $data['stats'], $data['sockets'], $data['grants_job_id']]);

            if (!$equipResult) {
                $errorInfo = $equipStmt->errorInfo();
                throw new Exception("插入equipment_details失败: " . $errorInfo[2]);
            }

            error_log("equipment_details插入成功");
        }
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => 'Item created successfully.', 'id' => $lastId, 'item_id' => $itemId], JSON_UNESCAPED_UNICODE);

    } catch (Exception $e) {
        if ($conn->inTransaction()) $conn->rollBack();
        throw $e;
    }
}

function deleteItem() {
    $id = $_POST['id'] ?? 0;
    if (!$id) throw new Exception("ID not provided for deletion.");

    $db = Database::getInstance();
    $conn = $db->getConnection();

    try {
        $conn->beginTransaction();
        
        $tables_to_delete_from = [
            'equipment_details', 
            'player_inventory', 
            'monster_equipment', 
            'loot_table_entries'
        ];
        foreach ($tables_to_delete_from as $table) {
            $stmt = $conn->prepare("DELETE FROM $table WHERE item_template_id = ?");
            $stmt->execute([$id]);
        }
        $stmt = $conn->prepare("DELETE FROM item_templates WHERE id = ?");
        $stmt->execute([$id]);
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => "Item ID {$id} and all related data have been deleted."], JSON_UNESCAPED_UNICODE);
    
    } catch (Exception $e) {
        if ($conn->inTransaction()) $conn->rollBack();
        throw $e;
    }
}

function parseItemData($post) {
    // 基础字段
    $data = [
        'name'            => trim($post['name'] ?? ''),
        'category'        => $post['category'] ?? 'Misc',
        'description'     => trim($post['description'] ?? ''),
        'is_consumable'   => (int)($post['is_consumable'] ?? 0),
        'stackable'       => (int)($post['stackable'] ?? 0),
        'max_stack'       => (int)($post['max_stack'] ?? 1),
        'buy_price'       => isset($post['buy_price']) && $post['buy_price'] !== '' ? (int)$post['buy_price'] : null,
        'diamond_price'   => isset($post['diamond_price']) && $post['diamond_price'] !== '' ? (int)$post['diamond_price'] : null,
        'sell_price'      => isset($post['sell_price']) && $post['sell_price'] !== '' ? (int)$post['sell_price'] : null,
        'effects'         => trim($post['effects'] ?? '{}'),
        'equipment_type'  => $post['equipment_type'] ?? 'player',
    ];

    // 装备专属字段
    if ($data['category'] === 'Equipment') {
        $data['slot']            = $post['slot'] ?? 'Head';
        $data['job_restriction'] = !empty($post['job_restriction']) ? (int)$post['job_restriction'] : null;
        $data['stats']           = trim($post['stats'] ?? '{}');
        $data['sockets']         = (int)($post['sockets'] ?? 0);
        $data['grants_job_id']   = !empty($post['grants_job_id']) ? (int)$post['grants_job_id'] : null;
    } else {
        // 非装备则清空这些值
        $data['slot'] = null;
        $data['job_restriction'] = null;
        $data['stats'] = null;
        $data['sockets'] = 0;
        $data['grants_job_id'] = null;
    }

    // 如果 effects 或 stats 是空对象 '{}'，则存为 null
    if ($data['effects'] === '{}' || $data['effects'] === '') {
        $data['effects'] = null;
    }
    if (isset($data['stats']) && ($data['stats'] === '{}' || $data['stats'] === '')) {
        $data['stats'] = null;
    }


    if (empty($data['name'])) {
        throw new Exception("物品名称不能为空");
    }

    return $data;
}

?> 