<?php
session_start();
header('Content-Type: application/json');

if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权的访问']);
    exit;
}

require_once '../config/Database.php';
require_once '../config/RedisManager.php'; // 引入Redis管理器

$response = ['success' => false, 'message' => '无效的操作'];
$db = Database::getInstance()->getConnection();

/**
 * 检查仓库建筑是否有玩家物品
 * @param PDO $db 数据库连接
 * @param array $buildingIds 要检查的建筑ID数组
 * @param int $sceneId 场景ID
 * @return array 包含有物品的仓库建筑ID和场景建筑ID的数组
 */
function checkWarehouseBuildingsForItems($db, $buildingIds, $sceneId) {
    if (empty($buildingIds)) {
        return [];
    }

    // 查找哪些是仓库类型的建筑
    $placeholders = implode(',', array_fill(0, count($buildingIds), '?'));
    $warehouseStmt = $db->prepare("
        SELECT id, name FROM buildings
        WHERE id IN ($placeholders) AND type = 'WAREHOUSE'
    ");
    $warehouseStmt->execute($buildingIds);
    $warehouseBuildings = $warehouseStmt->fetchAll(PDO::FETCH_ASSOC);
    $warehouseStmt->closeCursor();

    if (empty($warehouseBuildings)) {
        return [];
    }

    $warehouseBuildingIds = array_column($warehouseBuildings, 'id');
    $buildingsWithItems = [];

    // 检查每个仓库建筑是否有物品
    foreach ($warehouseBuildingIds as $warehouseBuildingId) {
        // 获取场景建筑ID
        $sceneBuildingStmt = $db->prepare("
            SELECT id FROM scene_buildings
            WHERE scene_id = ? AND building_id = ?
        ");
        $sceneBuildingStmt->execute([$sceneId, $warehouseBuildingId]);
        $sceneBuilding = $sceneBuildingStmt->fetch(PDO::FETCH_ASSOC);
        $sceneBuildingStmt->closeCursor();

        if ($sceneBuilding) {
            // 检查是否有物品
            $itemCheckStmt = $db->prepare("
                SELECT COUNT(*) as item_count
                FROM warehouse_storage
                WHERE scene_building_id = ?
            ");
            $itemCheckStmt->execute([$sceneBuilding['id']]);
            $itemCount = $itemCheckStmt->fetch(PDO::FETCH_ASSOC);
            $itemCheckStmt->closeCursor();

            if ($itemCount['item_count'] > 0) {
                // 获取建筑名称
                $buildingNameStmt = $db->prepare("SELECT name FROM buildings WHERE id = ?");
                $buildingNameStmt->execute([$warehouseBuildingId]);
                $buildingName = $buildingNameStmt->fetch(PDO::FETCH_ASSOC);
                $buildingNameStmt->closeCursor();

                $buildingsWithItems[] = [
                    'building_id' => $warehouseBuildingId,
                    'scene_building_id' => $sceneBuilding['id'],
                    'name' => $buildingName['name'] ?? '未知仓库'
                ];
            }
        }
    }

    return $buildingsWithItems;
}

// 函数：发布场景更新通知
function notify_server() {
    // 后台管理脚本在标准的Web服务器环境（如Nginx+PHP-FPM）下运行，
    // 而非Swoole的协程环境，因此不能使用为协程设计的Redis连接池。
    // 这里我们使用标准的phpredis扩展直接发送通知。
    // 这要求PHP环境中已安装phpredis扩展。
    if (!class_exists('Redis')) {
        error_log("phpredis extension is not installed. Cannot send scene update notification from admin panel.");
        return;
    }

    try {
        $redis = new Redis();
        // 使用与您现有Swoole Redis池相同的连接信息
        $redis->connect('127.0.0.1', 6379, 1.0); // 1秒超时
        // 如果您的Redis有密码，请在此处填写
        // $redis->auth('');
        
        $redis->publish('game-system-notifications', 'scenes_updated');
        $redis->close();

    } catch (Exception $e) {
        // 在生产环境中，最好只记录日志，不应中断用户操作
        error_log("Redis publish failed in api_scenes.php (using phpredis): " . $e->getMessage());
    }
}

// 数字转大写中文数字的函数
function numberToChinese($num) {
    $chineseNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十'];
    $chineseTens = ['', '十', '百', '千', '万'];
    
    if ($num < 0 || $num > 9999) {
        return '数字超出范围';
    }
    
    if ($num < 11) {
        return $chineseNumbers[$num];
    }
    
    $result = '';
    $numStr = (string)$num;
    
    for ($i = 0; $i < strlen($numStr); $i++) {
        $digit = (int)$numStr[$i];
        $place = strlen($numStr) - $i - 1;
        
        if ($digit !== 0) {
            $result .= ($digit !== 1 || $place !== 1 ? $chineseNumbers[$digit] : '') . $chineseTens[$place];
        }
    }
    
    return $result;
}

try {
    $action = $_POST['action'] ?? $_GET['action'] ?? '';

    switch ($action) {
        case 'get_all':
            $stmt = $db->prepare("
                SELECT s.*, sm.monster_template_id, sm.quantity, mt.name as monster_name
                FROM scenes s
                LEFT JOIN scene_monsters sm ON s.id = sm.scene_id
                LEFT JOIN monster_templates mt ON sm.monster_template_id = mt.id
                ORDER BY s.id, s.z, s.y, s.x
            ");
            $stmt->execute();
            $rows = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $scenes = [];
            foreach ($rows as $row) {
                $sceneId = $row['id'];
                if (!isset($scenes[$sceneId])) {
                    $scenes[$sceneId] = [
                        'id' => $row['id'],
                        'name' => $row['name'],
                        'description' => $row['description'],
                        'max_players' => (int)$row['max_players'],
                        'x' => (int)$row['x'],
                        'y' => (int)$row['y'],
                        'z' => (int)$row['z'],
                        'zone_id' => $row['zone_id'] ?? 'zone_default',
                        'is_safe_zone' => isset($row['is_safe_zone']) ? (int)$row['is_safe_zone'] : 1,
                        'monsters' => [],
                        'buildings' => []
                    ];
                }
                if ($row['monster_template_id']) {
                    $scenes[$sceneId]['monsters'][] = [
                        'id' => $row['monster_template_id'],
                        'name' => $row['monster_name'],
                        'quantity' => (int)$row['quantity']
                    ];
                }
            }
            
            // 加载场景关联的建筑
            $buildingStmt = $db->prepare("
                SELECT sb.id as scene_building_id, sb.scene_id, b.id as building_id, b.name, b.type
                FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                ORDER BY sb.scene_id
            ");
            $buildingStmt->execute();
            $buildingRows = $buildingStmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($buildingRows as $row) {
                $sceneId = $row['scene_id'];
                if (isset($scenes[$sceneId])) {
                    $scenes[$sceneId]['buildings'][] = [
                        'scene_building_id' => (int)$row['scene_building_id'],
                        'id' => $row['building_id'],
                        'name' => $row['name'],
                        'type' => $row['type']
                    ];
                }
            }
            
            // 加载传送点目的地数据
            $teleporterStmt = $db->prepare("
                SELECT td.id, td.scene_building_id, td.target_scene_id, td.sort_order, 
                       td.required_item_id, td.required_quantity, s.name as target_scene_name
                FROM teleporter_destinations td
                JOIN scenes s ON td.target_scene_id = s.id
                ORDER BY td.scene_building_id, td.sort_order
            ");
            $teleporterStmt->execute();
            $teleporterRows = $teleporterStmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 将传送点数据添加到对应的建筑中
            foreach ($teleporterRows as $row) {
                $sceneBuildingId = $row['scene_building_id'];
                // 找到对应的场景和建筑
                foreach ($scenes as &$scene) {
                    foreach ($scene['buildings'] as &$building) {
                        if (isset($building['scene_building_id']) && $building['scene_building_id'] == $sceneBuildingId) {
                            if (!isset($building['teleporter_destinations'])) {
                                $building['teleporter_destinations'] = [];
                            }
                            $building['teleporter_destinations'][] = [
                                'id' => (int)$row['id'],
                                'target_scene_id' => $row['target_scene_id'],
                                'target_scene_name' => $row['target_scene_name'],
                                'sort_order' => (int)$row['sort_order'],
                                'required_item_id' => $row['required_item_id'] ? (int)$row['required_item_id'] : null,
                                'required_quantity' => $row['required_quantity'] ? (int)$row['required_quantity'] : null
                            ];
                        }
                    }
                }
            }
            
            // 加载图层名称数据
            $layerStmt = $db->prepare("SELECT z, name, description FROM scene_layers ORDER BY z");
            $layerStmt->execute();
            $layerNames = $layerStmt->fetchAll(PDO::FETCH_ASSOC);

            // 调试输出
            error_log("API返回的场景数据: " . json_encode(array_values($scenes)));

            $response = [
                'success' => true,
                'scenes' => array_values($scenes),
                'layer_names' => $layerNames
            ];
            break;

        case 'get_zones':
            $stmt = $db->prepare("
                SELECT z.id, z.name, z.description, z.continent, z.min_level, z.max_level,
                       c.display_name as continent_name
                FROM zones z
                LEFT JOIN continents c ON z.continent = c.id
                ORDER BY c.sort_order, z.min_level, z.id
            ");
            $stmt->execute();
            $zones = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'zones' => $zones
            ];
            break;

        case 'create':
        case 'update':
            $data = json_decode(file_get_contents('php://input'), true);
            if (!$data) { throw new Exception("无效的JSON数据。"); }

            $isCreate = ($action === 'create');
            $db->beginTransaction();
            
            if ($isCreate) {
                $sceneId = 'scene_' . $data['x'] . '_' . $data['y'] . '_' . $data['z'];
                $sql = "INSERT INTO scenes (id, name, description, max_players, x, y, z, zone_id, is_safe_zone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)";
                $stmt = $db->prepare($sql);
                $stmt->execute([ $sceneId, $data['name'], $data['description'], $data['max_players'], $data['x'], $data['y'], $data['z'], $data['zone_id'] ?? 'zone_default', isset($data['is_safe_zone']) ? $data['is_safe_zone'] : 1 ]);
            } else {
                $sceneId = $data['id'];
                $sql = "UPDATE scenes SET name = ?, description = ?, max_players = ?, x = ?, y = ?, z = ?, zone_id = ?, is_safe_zone = ? WHERE id = ?";
                $stmt = $db->prepare($sql);
                $stmt->execute([ $data['name'], $data['description'], $data['max_players'], $data['x'], $data['y'], $data['z'], $data['zone_id'] ?? 'zone_default', isset($data['is_safe_zone']) ? $data['is_safe_zone'] : 1, $sceneId ]);
            }

            // 更新怪物
            $deleteMonstersStmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id = ?");
            $deleteMonstersStmt->execute([$sceneId]);
            
            $monsters = $data['monsters'] ?? [];
            if (!empty($monsters)) {
                $insertMonsterStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
                foreach ($monsters as $monster) {
                    $insertMonsterStmt->execute([$sceneId, $monster['id'], $monster['quantity']]);
                }
            }
            
            // 更新建筑
            // 获取当前场景已有的建筑
            $existingBuildingsStmt = $db->prepare("SELECT id, building_id FROM scene_buildings WHERE scene_id = ?");
            $existingBuildingsStmt->execute([$sceneId]);
            $existingBuildings = $existingBuildingsStmt->fetchAll(PDO::FETCH_ASSOC);
            $existingBuildingIds = [];
            $existingSceneBuildingMap = [];
            
            foreach ($existingBuildings as $existingBuilding) {
                $existingBuildingIds[] = (int)$existingBuilding['building_id'];
                $existingSceneBuildingMap[(int)$existingBuilding['building_id']] = (int)$existingBuilding['id'];
            }
            
            error_log("现有建筑ID: " . implode(", ", $existingBuildingIds));
            
            // 新建筑列表
            $buildings = $data['buildings'] ?? [];
            $newBuildingIds = [];
            foreach ($buildings as $building) {
                $newBuildingIds[] = (int)$building['id'];
            }
            
            error_log("新建筑ID: " . implode(", ", $newBuildingIds));
            
            // 要删除的建筑 = 现有建筑 - 新建筑
            $buildingsToRemove = array_diff($existingBuildingIds, $newBuildingIds);
            if (!empty($buildingsToRemove)) {
                // 检查要删除的建筑中是否有仓库包含玩家物品
                $buildingsWithItems = checkWarehouseBuildingsForItems($db, $buildingsToRemove, $sceneId);
                if (!empty($buildingsWithItems)) {
                    $buildingNames = array_column($buildingsWithItems, 'name');
                    throw new Exception("无法移除建筑：以下仓库中还有玩家物品，请先清空所有物品后再移除：" . implode(', ', $buildingNames));
                }

                $buildingPlaceholders = implode(',', array_fill(0, count($buildingsToRemove), '?'));
                $removeBuildingsStmt = $db->prepare("DELETE FROM scene_buildings WHERE scene_id = ? AND building_id IN ($buildingPlaceholders)");
                $params = array_merge([$sceneId], $buildingsToRemove);
                $removeBuildingsStmt->execute($params);

                error_log("删除的建筑ID: " . implode(", ", $buildingsToRemove));
            }
            
            // 要添加的建筑 = 新建筑 - 现有建筑
            $buildingsToAdd = array_diff($newBuildingIds, $existingBuildingIds);
            if (!empty($buildingsToAdd)) {
                $insertBuildingStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
                foreach ($buildingsToAdd as $buildingId) {
                    $insertBuildingStmt->execute([$sceneId, $buildingId]);
                }
                
                error_log("添加的建筑ID: " . implode(", ", $buildingsToAdd));
            }
            
            $db->commit();
            notify_server();
            $message = $isCreate ? '场景创建成功' : '场景更新成功';
            $response = ['success' => true, 'message' => $message];
            break;

        case 'batch_create':
            $data = json_decode(file_get_contents('php://input'), true);
            $scenesData = $data['scenes'] ?? null;
            $options = $data['options'] ?? [];

            if (!$scenesData || !is_array($scenesData)) {
                throw new Exception("批量创建失败：场景数据(scenes)不是有效的JSON。");
            }
            
            $namePrefix = $options['name'] ?? '新场景';
            $useSuffix = $options['use_suffix'] ?? false;
            $description = $options['description'] ?? '';
            $monsters = $options['monsters'] ?? [];
            $buildings = $options['buildings'] ?? [];
            $isSafeZone = isset($options['is_safe_zone']) ? (int)$options['is_safe_zone'] : 1;
            $zoneId = $options['zone_id'] ?? 'zone_default';

            $db->beginTransaction();
            $sceneStmt = $db->prepare("INSERT INTO scenes (id, name, x, y, z, description, max_players, zone_id, is_safe_zone) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)");
            $monsterStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
            $buildingStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
            
            $i = 1;
            foreach ($scenesData as $scene) {
                $sceneId = 'scene_' . $scene['x'] . '_' . $scene['y'] . '_' . $scene['z'];
                
                $sceneName = $namePrefix;
                if ($useSuffix) {
                    $chineseSuffix = numberToChinese($i);
                    $sceneName = $namePrefix . '（' . $chineseSuffix . '）';
                }

                $sceneStmt->execute([
                    $sceneId, $sceneName, $scene['x'], $scene['y'], $scene['z'],
                    $description, $scene['max_players'] ?? 10, $zoneId, $isSafeZone
                ]);

                if (!empty($monsters)) {
                    foreach ($monsters as $monster) {
                        $monsterStmt->execute([$sceneId, $monster['id'], $monster['quantity']]);
                    }
                }
                
                if (!empty($buildings)) {
                    foreach ($buildings as $building) {
                        $buildingStmt->execute([$sceneId, $building['id']]);
                    }
                }
                
                $i++;
            }
            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '成功批量创建 ' . count($scenesData) . ' 个场景。'];
            break;
            
        case 'batch_delete':
            $data = json_decode(file_get_contents('php://input'), true);
            $sceneIds = $data['ids'] ?? [];

            if (empty($sceneIds)) { throw new Exception("没有提供要删除的场景ID。"); }

            $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
            $db->beginTransaction();

            $stmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id IN ($placeholders)");
            $stmt->execute($sceneIds);

            $stmt = $db->prepare("DELETE FROM scenes WHERE id IN ($placeholders)");
            $stmt->execute($sceneIds);
            $deletedCount = $stmt->rowCount();

            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => "成功删除了 {$deletedCount} 个场景。"];
            break;

        case 'batch_update':
            $data = json_decode(file_get_contents('php://input'), true);
            $sceneIds = $data['ids'] ?? [];
            $updates = $data['updates'] ?? [];

            if (empty($sceneIds) || empty($updates)) { throw new Exception("缺少批量更新所需的数据。"); }
            
            $db->beginTransaction();

            // 批量更新名称
            if (isset($updates['name']) && !empty(trim($updates['name']))) {
                $namePrefix = trim($updates['name']);
                $useSuffix = $updates['use_suffix'] ?? false;
                
                $nameStmt = $db->prepare("UPDATE scenes SET name = ? WHERE id = ?");
                
                // 为了让后缀可预测，需要对场景进行排序。
                // 我们从数据库中获取它们的坐标来进行排序。
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $sortStmt = $db->prepare("SELECT id FROM scenes WHERE id IN ($placeholders) ORDER BY z, y, x");
                $sortStmt->execute($sceneIds);
                $sortedSceneIds = $sortStmt->fetchAll(PDO::FETCH_COLUMN);
                
                $i = 1;
                foreach ($sortedSceneIds as $sceneId) {
                    $sceneName = $namePrefix;
                    if ($useSuffix) {
                        $suffix = str_pad($i, 2, '0', STR_PAD_LEFT);
                        $sceneName .= ' ' . $suffix;
                    }
                    $nameStmt->execute([$sceneName, $sceneId]);
                    $i++;
                }
            }

            // 批量更新描述
            if (isset($updates['description'])) {
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $descStmt = $db->prepare("UPDATE scenes SET description = ? WHERE id IN ($placeholders)");
                // Important: The description parameter must come first, then all the IDs.
                $params = array_merge([$updates['description']], $sceneIds);
                $descStmt->execute($params);
            }
            
            // 批量更新安全区状态
            if (isset($updates['is_safe_zone']) && $updates['is_safe_zone'] !== '') {
                $isSafeZone = (int)$updates['is_safe_zone'];
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $safeZoneStmt = $db->prepare("UPDATE scenes SET is_safe_zone = ? WHERE id IN ($placeholders)");
                $params = array_merge([$isSafeZone], $sceneIds);
                $safeZoneStmt->execute($params);
            }

            // 批量更新区域ID
            if (isset($updates['zone_id']) && $updates['zone_id'] !== '') {
                $zoneId = $updates['zone_id'];
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $zoneStmt = $db->prepare("UPDATE scenes SET zone_id = ? WHERE id IN ($placeholders)");
                $params = array_merge([$zoneId], $sceneIds);
                $zoneStmt->execute($params);
            }
            
            // 批量更新怪物
            if (array_key_exists('monsters', $updates)) {
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $deleteStmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id IN ($placeholders)");
                $deleteStmt->execute($sceneIds);

                $monsters = $updates['monsters'];
                if (!empty($monsters)) {
                    $insertStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
                    foreach ($sceneIds as $sceneId) {
                        foreach ($monsters as $monster) {
                            $insertStmt->execute([$sceneId, $monster['id'], $monster['quantity']]);
                        }
                    }
                }
            }
            
            // 批量更新建筑
            if (array_key_exists('buildings', $updates)) {
                $buildings = $updates['buildings'];
                
                foreach ($sceneIds as $sceneId) {
                    // 获取当前场景已有的建筑
                    $existingBuildingsStmt = $db->prepare("SELECT id, building_id FROM scene_buildings WHERE scene_id = ?");
                    $existingBuildingsStmt->execute([$sceneId]);
                    $existingBuildings = $existingBuildingsStmt->fetchAll(PDO::FETCH_ASSOC);
                    $existingBuildingIds = array_column($existingBuildings, 'building_id');
                    
                    // 要配置的新建筑ID
                    $newBuildingIds = array_column($buildings, 'id');
                    
                    // 要删除的建筑 = 现有建筑 - 新建筑
                    $buildingsToRemove = array_diff($existingBuildingIds, $newBuildingIds);
                    if (!empty($buildingsToRemove)) {
                        // 检查要删除的建筑中是否有仓库包含玩家物品
                        $buildingsWithItems = checkWarehouseBuildingsForItems($db, $buildingsToRemove, $sceneId);
                        if (!empty($buildingsWithItems)) {
                            $buildingNames = array_column($buildingsWithItems, 'name');
                            throw new Exception("无法移除建筑：场景ID {$sceneId} 中以下仓库还有玩家物品，请先清空所有物品后再移除：" . implode(', ', $buildingNames));
                        }

                        $buildingPlaceholders = implode(',', array_fill(0, count($buildingsToRemove), '?'));
                        $removeBuildingsStmt = $db->prepare("DELETE FROM scene_buildings WHERE scene_id = ? AND building_id IN ($buildingPlaceholders)");
                        $params = array_merge([$sceneId], $buildingsToRemove);
                        $removeBuildingsStmt->execute($params);
                    }
                    
                    // 要添加的建筑 = 新建筑 - 现有建筑
                    $buildingsToAdd = array_diff($newBuildingIds, $existingBuildingIds);
                    if (!empty($buildingsToAdd)) {
                        $insertBuildingStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
                        foreach ($buildingsToAdd as $buildingId) {
                            $insertBuildingStmt->execute([$sceneId, $buildingId]);
                        }
                    }
                }
            }
            
            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '选中的场景已成功批量更新。'];
            break;

        case 'get_scene_details':
            if (!isset($_GET['id'])) {
                throw new Exception("缺少场景ID。");
            }
            // ... existing code ...

        case 'get_all_scenes':
            $stmt = $db->query("
                SELECT 
                    s.*, 
                    GROUP_CONCAT(mt.name SEPARATOR ',') as monsters
                FROM 
                    scenes s
                LEFT JOIN 
                    scene_monsters sm ON s.id = sm.scene_id
                LEFT JOIN 
                    monster_templates mt ON sm.monster_template_id = mt.id
                GROUP BY 
                    s.id
                ORDER BY
                    s.z, s.y, s.x
            ");
            $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $scenes];
            break;

        case 'batch_update_description':
            $sceneIds = json_decode($_POST['scene_ids'], true);
            $description = trim($_POST['description'] ?? '');

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($sceneIds) || empty($sceneIds)) {
                throw new Exception("无效的场景ID列表。");
            }
            
            $db->beginTransaction();
            
            $placeholders = rtrim(str_repeat('?,', count($sceneIds)), ',');
            $stmt = $db->prepare("UPDATE scenes SET description = ? WHERE id IN ($placeholders)");
            
            $params = array_merge([$description], array_values($sceneIds));
            $stmt->execute($params);
            
            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '选中的 ' . count($sceneIds) . ' 个场景描述已成功更新！'];
            break;

        case 'batch_update_name':
            $sceneIds = json_decode($_POST['scene_ids'], true);
            $name_prefix = trim($_POST['name_prefix'] ?? '');
            $startNumber = (int)$_POST['start_number'];

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($sceneIds) || empty($sceneIds)) {
                throw new Exception("无效的场景ID列表。");
            }
            if (empty($name_prefix)) {
                throw new Exception("场景名称前缀不能为空。");
            }

            $db->beginTransaction();

            // 更新名称
            $stmt = $db->prepare("UPDATE scenes SET name = ? WHERE id = ?");

            foreach ($sceneIds as $index => $sceneId) {
                $chineseSuffix = numberToChinese($startNumber + $index);
                $newName = "{$name_prefix}（{$chineseSuffix}）";
                $stmt->execute([$newName, $sceneId]);
            }

            // 如果有描述更新（只有当前端明确传递了description参数时才更新）
            if (isset($_POST['description'])) {
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $descStmt = $db->prepare("UPDATE scenes SET description = ? WHERE id IN ($placeholders)");
                $params = array_merge([$_POST['description']], $sceneIds);
                $descStmt->execute($params);
            }

            // 如果有安全区更新
            if (isset($_POST['is_safe_zone']) && $_POST['is_safe_zone'] !== '') {
                $isSafeZone = (int)$_POST['is_safe_zone'];
                $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                $safeZoneStmt = $db->prepare("UPDATE scenes SET is_safe_zone = ? WHERE id IN ($placeholders)");
                $params = array_merge([$isSafeZone], $sceneIds);
                $safeZoneStmt->execute($params);
            }

            // 如果有怪物更新
            if (isset($_POST['monsters'])) {
                $monsters = json_decode($_POST['monsters'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    $placeholders = implode(',', array_fill(0, count($sceneIds), '?'));
                    $deleteStmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id IN ($placeholders)");
                    $deleteStmt->execute($sceneIds);

                    if (!empty($monsters)) {
                        $insertStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
                        foreach ($sceneIds as $sceneId) {
                            foreach ($monsters as $monster) {
                                $insertStmt->execute([$sceneId, $monster['id'], $monster['quantity']]);
                            }
                        }
                    }
                }
            }

            // 如果有建筑更新
            if (isset($_POST['buildings'])) {
                $buildings = json_decode($_POST['buildings'], true);
                if (json_last_error() === JSON_ERROR_NONE) {
                    foreach ($sceneIds as $sceneId) {
                        // 获取当前场景已有的建筑
                        $existingBuildingsStmt = $db->prepare("SELECT id, building_id FROM scene_buildings WHERE scene_id = ?");
                        $existingBuildingsStmt->execute([$sceneId]);
                        $existingBuildings = $existingBuildingsStmt->fetchAll(PDO::FETCH_ASSOC);
                        $existingBuildingIds = array_column($existingBuildings, 'building_id');

                        // 要配置的新建筑ID
                        $newBuildingIds = array_column($buildings, 'id');

                        // 要删除的建筑 = 现有建筑 - 新建筑
                        $buildingsToRemove = array_diff($existingBuildingIds, $newBuildingIds);
                        if (!empty($buildingsToRemove)) {
                            // 检查要删除的建筑中是否有仓库包含玩家物品
                            $buildingsWithItems = checkWarehouseBuildingsForItems($db, $buildingsToRemove, $sceneId);
                            if (!empty($buildingsWithItems)) {
                                $buildingNames = array_column($buildingsWithItems, 'name');
                                throw new Exception("无法移除建筑：场景ID {$sceneId} 中以下仓库还有玩家物品，请先清空所有物品后再移除：" . implode(', ', $buildingNames));
                            }

                            $placeholders = implode(',', array_fill(0, count($buildingsToRemove), '?'));
                            $deleteStmt = $db->prepare("DELETE FROM scene_buildings WHERE scene_id = ? AND building_id IN ($placeholders)");
                            $params = array_merge([$sceneId], $buildingsToRemove);
                            $deleteStmt->execute($params);
                        }

                        // 要添加的建筑 = 新建筑 - 现有建筑
                        $buildingsToAdd = array_diff($newBuildingIds, $existingBuildingIds);
                        if (!empty($buildingsToAdd)) {
                            $insertStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
                            foreach ($buildingsToAdd as $buildingId) {
                                $insertStmt->execute([$sceneId, $buildingId]);
                            }
                        }
                    }
                }
            }

            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '选中的 ' . count($sceneIds) . ' 个场景已成功更新！'];
            break;

        case 'add_layer':
            $z = (int)($_POST['z'] ?? 0);
            $x = 0;
            $y = 0;
            
            $checkStmt = $db->prepare("SELECT id FROM scenes WHERE x = ? AND y = ? AND z = ?");
            $checkStmt->execute([$x, $y, $z]);
            if ($checkStmt->fetch()) {
                throw new Exception("图层 {$z} 的初始场景 (0,0) 已存在，无法重复创建。");
            }

            $scene_id = "layer_{$z}_0_0";
            $checkIdStmt = $db->prepare("SELECT id FROM scenes WHERE id = ?");
            $checkIdStmt->execute([$scene_id]);
            if ($checkIdStmt->fetch()) {
                $scene_id = "layer_{$z}_" . bin2hex(random_bytes(4));
            }

            $scene_name = "新图层 {$z} - 起点";
            $description = "图层 {$z} 的初始场景。";
            $max_players = 10;

            $stmt = $db->prepare("INSERT INTO scenes (id, name, description, max_players, x, y, z) VALUES (?, ?, ?, ?, ?, ?, ?)");
            $stmt->execute([$scene_id, $scene_name, $description, $max_players, $x, $y, $z]);

            notify_server();
            $response = ['success' => true, 'message' => "成功创建新图层 {$z} 及其位于(0,0)的初始场景！"];
            break;

        case 'get_scene_monsters':
            $scene_id = $_GET['scene_id'] ?? null;
            if (!$scene_id) throw new Exception("未提供场景ID。");

            $stmt = $db->prepare("
                SELECT sm.monster_template_id, sm.quantity, mt.name 
                FROM scene_monsters sm
                JOIN monster_templates mt ON sm.monster_template_id = mt.id
                WHERE sm.scene_id = ?
            ");
            $stmt->execute([$scene_id]);
            $monsters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $monsters];
            break;

        case 'update_scene_monsters':
            $scene_id = $_POST['scene_id'] ?? null;
            $monsters = isset($_POST['monsters']) ? json_decode($_POST['monsters'], true) : [];
            
            if (!$scene_id) throw new Exception("未提供场景ID。");
            if (json_last_error() !== JSON_ERROR_NONE) throw new Exception("无效的怪物数据格式。");

            $db->beginTransaction();

            // 1. 删除旧配置
            $deleteStmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id = ?");
            $deleteStmt->execute([$scene_id]);

            // 2. 插入新配置
            if (!empty($monsters)) {
                $insertStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
                foreach ($monsters as $monster) {
                    if (isset($monster['id']) && isset($monster['quantity']) && (int)$monster['quantity'] > 0) {
                        $insertStmt->execute([$scene_id, (int)$monster['id'], (int)$monster['quantity']]);
                    }
                }
            }

            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '场景怪物配置已更新！'];
            break;

        case 'update_scene_buildings':
            $scene_id = $_POST['scene_id'] ?? null;
            $buildings = isset($_POST['buildings']) ? json_decode($_POST['buildings'], true) : [];

            if (!$scene_id) throw new Exception("未提供场景ID。");
            if (json_last_error() !== JSON_ERROR_NONE) throw new Exception("无效的建筑数据格式。");

            // 检查要删除的仓库建筑是否有玩家物品
            $existingBuildingsStmt = $db->prepare("SELECT building_id FROM scene_buildings WHERE scene_id = ?");
            $existingBuildingsStmt->execute([$scene_id]);
            $existingBuildingIds = $existingBuildingsStmt->fetchAll(PDO::FETCH_COLUMN);
            $existingBuildingsStmt->closeCursor();

            $newBuildingIds = array_column($buildings, 'id');
            $buildingsToRemove = array_diff($existingBuildingIds, $newBuildingIds);

            if (!empty($buildingsToRemove)) {
                $buildingsWithItems = checkWarehouseBuildingsForItems($db, $buildingsToRemove, $scene_id);
                if (!empty($buildingsWithItems)) {
                    $buildingNames = array_column($buildingsWithItems, 'name');
                    throw new Exception("无法移除建筑：以下仓库中还有玩家物品，请先清空所有物品后再移除：" . implode(', ', $buildingNames));
                }
            }

            $db->beginTransaction();

            // 1. 删除旧配置
            $deleteStmt = $db->prepare("DELETE FROM scene_buildings WHERE scene_id = ?");
            $deleteStmt->execute([$scene_id]);

            // 2. 插入新配置
            if (!empty($buildings)) {
                $insertStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
                foreach ($buildings as $building) {
                    if (isset($building['id'])) {
                        $insertStmt->execute([$scene_id, $building['id']]);
                    }
                }
            }

            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => '场景建筑配置已更新！'];
            break;

        case 'update_scene':
            $scene_id = $_POST['scene_id'] ?? null;
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');

            if (empty($scene_id) || empty($name)) {
                throw new Exception("场景ID和名称不能为空。");
            }
            
            $stmt = $db->prepare("UPDATE scenes SET name = ?, description = ? WHERE id = ?");
            $stmt->execute([$name, $description, $scene_id]);
            
            notify_server();
            $response = ['success' => true, 'message' => '场景信息已成功更新！'];
            break;

        case 'delete_scene':
            $scene_id = $_POST['scene_id'] ?? null;
            if (empty($scene_id)) {
                throw new Exception("未提供有效的场景ID。");
            }
            
            $db->beginTransaction();

            $stmt1 = $db->prepare("DELETE FROM scene_monsters WHERE scene_id = ?");
            $stmt1->execute([$scene_id]);

            $stmt2 = $db->prepare("DELETE FROM scenes WHERE id = ?");
            $stmt2->execute([$scene_id]);
            
            $db->commit();
            
            notify_server();
            $response = ['success' => true, 'message' => "场景 '{$scene_id}' 已成功删除！受影响的玩家已被移动到初始场景。"];
            break;

        case 'batch_update_monsters':
            $sceneIds = json_decode($_POST['scene_ids'], true);
            $monsters = json_decode($_POST['monsters'], true);

            if (json_last_error() !== JSON_ERROR_NONE || !is_array($sceneIds) || !is_array($monsters) || empty($sceneIds)) {
                throw new Exception("无效的场景ID或怪物数据。");
            }
            
            $db->beginTransaction();

            // 1. 先删除所有选中场景的旧怪物配置
            $placeholders = rtrim(str_repeat('?,', count($sceneIds)), ',');
            $deleteStmt = $db->prepare("DELETE FROM scene_monsters WHERE scene_id IN ($placeholders)");
            $deleteStmt->execute(array_values($sceneIds));

            // 2. 插入新的怪物配置
            if (!empty($monsters)) {
                $insertStmt = $db->prepare("INSERT INTO scene_monsters (scene_id, monster_template_id, quantity) VALUES (?, ?, ?)");
                foreach ($sceneIds as $sceneId) {
                    foreach ($monsters as $monster) {
                        if (isset($monster['id']) && isset($monster['quantity']) && (int)$monster['quantity'] > 0) {
                            $insertStmt->execute([$sceneId, (int)$monster['id'], (int)$monster['quantity']]);
                        }
                    }
                }
            }

            $db->commit();
            notify_server();
            $response = ['success' => true, 'message' => "成功为 " . count($sceneIds) . " 个场景配置了怪物！"];
            break;

        case 'get_scene_npcs':
            try {
                $stmt = $db->prepare("
                    SELECT ni.id, ni.scene_id, ni.template_id, ni.position_desc, 
                           nt.name, nt.level, nt.is_merchant, nt.is_quest_giver, nt.avatar
                    FROM npc_instances ni
                    JOIN npc_templates nt ON ni.template_id = nt.id
                    ORDER BY ni.scene_id, nt.name
                ");
                $stmt->execute();
                $npcs = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $response = [
                    'success' => true,
                    'data' => $npcs
                ];
                break;
            } catch (PDOException $e) {
                $response = [
                    'success' => false,
                    'message' => '获取场景NPC数据失败: ' . $e->getMessage()
                ];
                break;
            }

        case 'set_layer_name':
            $z = (int)($_POST['z'] ?? 0);
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');

            if (empty($name)) {
                throw new Exception('图层名称不能为空');
            }

            if (strlen($name) > 100) {
                throw new Exception('图层名称不能超过100个字符');
            }

            // 使用 INSERT ... ON DUPLICATE KEY UPDATE 来处理新增或更新
            // MySQL 5.5.62 兼容版本，手动设置更新时间
            $stmt = $db->prepare("
                INSERT INTO scene_layers (z, name, description)
                VALUES (?, ?, ?)
                ON DUPLICATE KEY UPDATE
                name = VALUES(name),
                description = VALUES(description),
                updated_at = NOW()
            ");
            $stmt->execute([$z, $name, $description]);

            $response = [
                'success' => true,
                'message' => '图层名称设置成功'
            ];
            break;

        default:
            // No specific action matched, we fall back to the old POST-based actions
            if (isset($_POST['action'])) {
                // Here you can re-implement the old POST-based handlers if needed
            } else {
                 $response['message'] = "无效的操作";
            }
            break;
    }

} catch (Exception $e) {
    if ($db->inTransaction()) {
        $db->rollBack();
    }
    error_log("Scene API Error: " . $e->getMessage());
    $response['message'] = '操作失败: ' . $e->getMessage();
}

echo json_encode($response); 