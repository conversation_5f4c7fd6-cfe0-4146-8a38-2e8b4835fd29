// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadExpansionItems();
    loadExpansionRecords();
    loadItemFilterOptions();
});

// 标签页相关变量
let currentTab = 'expansion';
let currentWarehousePage = 1;
let totalWarehousePages = 1;
const warehousesPerPage = 12;

// 分页相关变量
let currentPage = 1;
let totalPages = 1;
const recordsPerPage = 20;

// 加载扩容物品列表
function loadExpansionItems() {
    fetch('api_warehouse_expansion.php?action=get_list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderExpansionItems(data.data);
            } else {
                showStatusMessage('加载扩容物品列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 渲染扩容物品列表
function renderExpansionItems(items) {
    const grid = document.getElementById('expansion-grid');
    
    if (items.length === 0) {
        grid.innerHTML = '<div class="expansion-item-card"><p style="text-align:center;">暂无扩容物品配置</p></div>';
        return;
    }
    
    let html = '';
    items.forEach(item => {
        const statusClass = item.is_active == 1 ? 'active' : 'inactive';
        const statusText = item.is_active == 1 ? '启用' : '禁用';
        const maxUsesText = item.max_uses ? `${item.max_uses}次` : '无限制';
        
        html += `
            <div class="expansion-item-card">
                <div class="expansion-item-header">
                    <div class="expansion-item-name">${item.item_name}</div>
                    <div class="expansion-status ${statusClass}">${statusText}</div>
                </div>
                <div class="expansion-details">
                    <div class="expansion-detail-item">
                        <span>+${item.expansion_amount} 空间 | ${maxUsesText}</span>
                    </div>
                </div>
                <div class="expansion-actions">
                    <button class="btn btn-sm btn-primary" onclick="editExpansionItem(${item.id})">编辑</button>
                    <button class="btn btn-sm btn-danger" onclick="deleteExpansionItem(${item.id}, '${item.item_name}')">删除</button>
                </div>
            </div>
        `;
    });
    
    grid.innerHTML = html;
}

// 显示创建模态框
function showCreateModal() {
    document.getElementById('modal-title').textContent = '添加扩容物品';
    document.getElementById('expansion-form').reset();
    document.getElementById('expansion-id').value = '';
    
    // 加载可用物品列表
    loadAvailableItems();
    
    document.getElementById('expansion-modal').style.display = 'flex';
}

// 编辑扩容物品
function editExpansionItem(id) {
    document.getElementById('modal-title').textContent = '编辑扩容物品';
    
    fetch(`api_warehouse_expansion.php?action=get&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const item = data.data;
                document.getElementById('expansion-id').value = item.id;
                document.getElementById('item-template-id').innerHTML = `<option value="${item.item_template_id}">${item.item_name}</option>`;
                document.getElementById('expansion-amount').value = item.expansion_amount;
                document.getElementById('max-uses').value = item.max_uses || '';
                document.getElementById('is-active').checked = item.is_active == 1;
                
                document.getElementById('expansion-modal').style.display = 'flex';
            } else {
                showStatusMessage('加载扩容物品数据失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 删除扩容物品
function deleteExpansionItem(id, itemName) {
    if (!confirm(`确定要删除扩容物品"${itemName}"的配置吗？`)) {
        return;
    }
    
    const formData = new FormData();
    formData.append('action', 'delete');
    formData.append('id', id);
    
    fetch('api_warehouse_expansion.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            loadExpansionItems();
        } else {
            showStatusMessage('删除失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 加载可用物品列表
function loadAvailableItems() {
    fetch('api_warehouse_expansion.php?action=get_available_items')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('item-template-id');
                select.innerHTML = '<option value="">请选择物品</option>';
                
                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.id;
                    option.textContent = `${item.name} [${getCategoryName(item.category)}]`;
                    select.appendChild(option);
                });
            } else {
                showStatusMessage('加载可用物品列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 处理表单提交
function handleFormSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    const isEdit = document.getElementById('expansion-id').value !== '';
    formData.append('action', isEdit ? 'update' : 'create');
    
    fetch('api_warehouse_expansion.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            hideModal();
            loadExpansionItems();
        } else {
            showStatusMessage('保存失败: ' + data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 隐藏模态框
function hideModal() {
    document.getElementById('expansion-modal').style.display = 'none';
}

// 获取分类中文名称
function getCategoryName(category) {
    const categoryMap = {
        'Equipment': '装备',
        'Gem': '宝石',
        'Material': '材料',
        'Potion': '药品',
        'Misc': '杂物',
        'Scroll': '书卷',
        'Rune': '符石',
        'Other': '其他'
    };
    return categoryMap[category] || category;
}

// 格式化日期时间
function formatDateTime(dateTimeString) {
    const date = new Date(dateTimeString);
    return date.toLocaleString('zh-CN');
}

// 显示状态消息 - 使用气泡样式
function showStatusMessage(message, type) {
    showToast(message, type);
}

// 显示Toast通知
function showToast(message, type = 'success', duration = 3000) {
    // 获取或创建Toast元素
    let toast = document.getElementById('toast');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'toast';
        toast.className = 'toast';
        document.body.appendChild(toast);
    }

    // 处理消息内容
    let displayMessage = message || '';

    // 限制消息长度，避免Toast过大
    if (displayMessage.length > 100) {
        displayMessage = displayMessage.substring(0, 97) + '...';
    }

    // 移除换行符，避免布局问题
    displayMessage = displayMessage.replace(/\n/g, ' ');

    // 移除多余的空格
    displayMessage = displayMessage.replace(/\s+/g, ' ').trim();

    // 设置Toast内容和样式
    toast.textContent = displayMessage;
    toast.className = `toast ${type}`;

    // 清除之前的定时器
    if (toast.hideTimer) {
        clearTimeout(toast.hideTimer);
    }

    // 显示Toast
    toast.classList.add('show');

    // 根据消息长度和类型调整显示时间
    if (type === 'error') {
        duration = Math.max(4000, displayMessage.length * 50); // 错误消息显示更久
    } else if (displayMessage.length > 50) {
        duration = 4000;
    }

    // 设置自动隐藏
    toast.hideTimer = setTimeout(() => {
        toast.classList.remove('show');
        toast.hideTimer = null;
    }, duration);

    return toast;
}

// 加载扩容记录
function loadExpansionRecords(page = 1) {
    currentPage = page;
    const playerSearch = document.getElementById('player-search').value.trim();
    const itemFilter = document.getElementById('item-filter').value;

    const params = new URLSearchParams({
        action: 'get_expansion_records',
        page: page,
        limit: recordsPerPage
    });

    if (playerSearch) {
        params.append('player_search', playerSearch);
    }

    if (itemFilter) {
        params.append('item_filter', itemFilter);
    }

    fetch(`api_warehouse_expansion.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderExpansionRecords(data.data.records);
                updatePagination(data.data.pagination);
            } else {
                showToast('加载扩容记录失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请重试', 'error');
        });
}

// 渲染扩容记录
function renderExpansionRecords(records) {
    const container = document.getElementById('expansion-records-container');

    if (records.length === 0) {
        container.innerHTML = '<div class="no-records"><p>暂无扩容记录</p></div>';
        return;
    }

    let html = `
        <table class="expansion-records-table">
            <thead>
                <tr>
                    <th>玩家名称</th>
                    <th>仓库位置</th>
                    <th>扩容物品</th>
                    <th>扩容数量</th>
                    <th>扩容后容量</th>
                    <th>扩容时间</th>
                </tr>
            </thead>
            <tbody>
    `;

    records.forEach(record => {
        html += `
            <tr>
                <td>${record.player_name}</td>
                <td>${record.scene_name || '未知场景'}</td>
                <td>${record.item_name}</td>
                <td>+${record.expansion_amount}</td>
                <td>${record.total_capacity}</td>
                <td>${formatDateTime(record.created_at)}</td>
            </tr>
        `;
    });

    html += `
            </tbody>
        </table>
    `;

    container.innerHTML = html;
}

// 更新分页信息
function updatePagination(pagination) {
    totalPages = pagination.total_pages;
    const paginationContainer = document.getElementById('records-pagination');
    const paginationInfo = document.getElementById('pagination-info');

    if (totalPages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'flex';
    paginationInfo.textContent = `第 ${currentPage} 页，共 ${totalPages} 页 (总计 ${pagination.total_records} 条记录)`;

    // 更新按钮状态
    const prevBtn = paginationContainer.querySelector('button:first-child');
    const nextBtn = paginationContainer.querySelector('button:last-child');

    prevBtn.disabled = currentPage <= 1;
    nextBtn.disabled = currentPage >= totalPages;
}

// 切换页面
function changePage(direction) {
    const newPage = currentPage + direction;
    if (newPage >= 1 && newPage <= totalPages) {
        loadExpansionRecords(newPage);
    }
}

// 加载物品筛选选项
function loadItemFilterOptions() {
    fetch('api_warehouse_expansion.php?action=get_expansion_items_for_filter')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const select = document.getElementById('item-filter');
                select.innerHTML = '<option value="">所有扩容物品</option>';

                data.data.forEach(item => {
                    const option = document.createElement('option');
                    option.value = item.item_template_id;
                    option.textContent = item.item_name;
                    select.appendChild(option);
                });
            }
        })
        .catch(error => {
            console.error('Error loading filter options:', error);
        });
}

// 清空筛选条件
function clearFilters() {
    document.getElementById('player-search').value = '';
    document.getElementById('item-filter').value = '';
    loadExpansionRecords(1);
}

// 处理搜索框回车键事件
function handleSearchKeyPress(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        loadExpansionRecords(1);
    }
}

// 标签页切换功能
function switchTab(tabName) {
    // 隐藏所有标签页内容
    document.querySelectorAll('.tab-content').forEach(tab => {
        tab.classList.remove('active');
    });

    // 移除所有标签按钮的激活状态
    document.querySelectorAll('.tab-button').forEach(btn => {
        btn.classList.remove('active');
    });

    // 显示选中的标签页
    document.getElementById(tabName + '-tab').classList.add('active');

    // 找到对应的按钮并激活
    document.querySelector(`.tab-button[onclick="switchTab('${tabName}')"]`).classList.add('active');

    currentTab = tabName;

    // 如果切换到仓库库存标签页，加载仓库数据
    if (tabName === 'inventory') {
        loadWarehouseInventory();
    }
}

// 加载仓库库存数据
function loadWarehouseInventory(page = 1) {
    currentWarehousePage = page;
    const warehouseSearch = document.getElementById('warehouse-search').value.trim();

    const params = new URLSearchParams({
        action: 'get_warehouse_inventory',
        page: page,
        limit: warehousesPerPage
    });

    if (warehouseSearch) {
        params.append('warehouse_search', warehouseSearch);
    }

    fetch(`api_warehouse_expansion.php?${params.toString()}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderWarehouseInventory(data.data.warehouses);
                updateWarehousePagination(data.data.pagination);
            } else {
                showToast('加载仓库库存失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请重试', 'error');
        });
}

// 渲染仓库库存列表
function renderWarehouseInventory(warehouses) {
    const container = document.getElementById('warehouse-list');

    if (warehouses.length === 0) {
        container.innerHTML = `
            <div class="warehouse-card" style="display: flex; justify-content: center; align-items: center; min-height: 150px;">
                <p style="text-align:center; font-size: 1.1em; color: #666;">
                    <i class="fas fa-inbox" style="margin-right: 10px; font-size: 1.5em;"></i><br>
                    暂无仓库数据
                </p>
            </div>
        `;
        return;
    }

    let html = '';
    warehouses.forEach(warehouse => {
        // 安全处理场景名称，避免引号问题
        const safeName = (warehouse.scene_name || '未知场景').replace(/'/g, '&#39;').replace(/"/g, '&quot;');

        html += `
            <div class="warehouse-card">
                <div class="warehouse-header">
                    <div class="warehouse-name" title="${safeName}">${safeName}</div>
                </div>
                <div class="warehouse-stats">
                    <div class="stat-item">
                        <span class="stat-label">存储玩家:</span>
                        <span class="stat-value">${warehouse.player_count} 人</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">物品种类:</span>
                        <span class="stat-value">${warehouse.item_types} 种</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">总物品数:</span>
                        <span class="stat-value">${warehouse.total_items} 个</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">玩家容量配置:</span>
                        <span class="stat-value">${warehouse.base_capacity} ~ ${warehouse.max_capacity}</span>
                    </div>
                </div>
                <div class="warehouse-actions">
                    <button class="btn btn-sm btn-primary" onclick="viewWarehouseItems(${warehouse.scene_building_id}, '${safeName}')">
                        <i class="fas fa-eye"></i> 查看物品
                    </button>
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 更新仓库分页信息
function updateWarehousePagination(pagination) {
    totalWarehousePages = pagination.total_pages;
    const paginationContainer = document.getElementById('warehouse-pagination');
    const paginationInfo = document.getElementById('warehouse-pagination-info');

    if (totalWarehousePages <= 1) {
        paginationContainer.style.display = 'none';
        return;
    }

    paginationContainer.style.display = 'flex';
    paginationInfo.textContent = `第 ${currentWarehousePage} 页，共 ${totalWarehousePages} 页 (总计 ${pagination.total_records} 个仓库)`;

    // 更新按钮状态
    const prevBtn = paginationContainer.querySelector('button:first-child');
    const nextBtn = paginationContainer.querySelector('button:last-child');

    prevBtn.disabled = currentWarehousePage <= 1;
    nextBtn.disabled = currentWarehousePage >= totalWarehousePages;
}

// 切换仓库页面
function changeWarehousePage(direction) {
    const newPage = currentWarehousePage + direction;
    if (newPage >= 1 && newPage <= totalWarehousePages) {
        loadWarehouseInventory(newPage);
    }
}

// 查看仓库物品详情
function viewWarehouseItems(sceneBuildingId, warehouseName) {
    document.getElementById('warehouse-modal-title').textContent = `${warehouseName} - 物品详情`;

    fetch(`api_warehouse_expansion.php?action=get_warehouse_items&scene_building_id=${sceneBuildingId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderWarehouseItems(data.data);
                document.getElementById('warehouse-items-modal').style.display = 'flex';
            } else {
                showToast('加载仓库物品失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('网络错误，请重试', 'error');
        });
}

// 渲染仓库物品详情
function renderWarehouseItems(items) {
    const container = document.getElementById('warehouse-items-container');

    if (items.length === 0) {
        container.innerHTML = `
            <div class="no-records" style="text-align: center; padding: 40px;">
                <i class="fas fa-box-open" style="font-size: 3em; color: #ddd; margin-bottom: 15px;"></i>
                <p style="font-size: 1.2em; color: #666;">该仓库暂无物品</p>
            </div>
        `;
        return;
    }

    // 按玩家分组物品
    const playerGroups = {};
    items.forEach(item => {
        if (!playerGroups[item.player_id]) {
            playerGroups[item.player_id] = {
                player_name: item.player_name,
                items: []
            };
        }
        playerGroups[item.player_id].items.push(item);
    });

    let html = `
        <div style="margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
            <div>
                <span style="font-weight: bold;">总物品数: </span>
                <span style="color: var(--primary-color); font-weight: bold;">${items.length} 件</span>
            </div>
            <div>
                <span style="font-weight: bold;">存储玩家数: </span>
                <span style="color: var(--primary-color); font-weight: bold;">${Object.keys(playerGroups).length} 人</span>
            </div>
        </div>
    `;

    // 为每个玩家创建一个物品表格
    Object.values(playerGroups).forEach(group => {
        html += `
            <div style="margin-bottom: 20px; border: 1px solid var(--border-color); border-radius: 8px; overflow: hidden;">
                <div style="background-color: var(--primary-color); color: white; padding: 10px 15px; font-weight: bold;">
                    <i class="fas fa-user"></i> 玩家: ${group.player_name} (${group.items.length} 件物品)
                </div>
                <table class="expansion-records-table" style="margin: 0;">
                    <thead>
                        <tr>
                            <th style="width: 40%;">物品名称</th>
                            <th style="width: 15%;">分类</th>
                            <th style="width: 15%;">数量</th>
                            <th style="width: 15%;">绑定状态</th>
                            <th style="width: 15%;">存入时间</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        group.items.forEach(item => {
            const bindingStatus = item.is_bound == 1 ?
                '<span style="color: #f44336;"><i class="fas fa-lock"></i> 已绑定</span>' :
                '<span style="color: #4CAF50;"><i class="fas fa-unlock"></i> 未绑定</span>';

            const categoryName = getCategoryName(item.category);

            html += `
                <tr>
                    <td>${item.item_name}</td>
                    <td>${categoryName}</td>
                    <td>${item.quantity}</td>
                    <td>${bindingStatus}</td>
                    <td>${formatDateTime(item.created_at)}</td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 隐藏仓库物品模态框
function hideWarehouseItemsModal() {
    document.getElementById('warehouse-items-modal').style.display = 'none';
}

// 清空仓库筛选条件
function clearWarehouseFilters() {
    document.getElementById('warehouse-search').value = '';
    loadWarehouseInventory(1);
}

// 处理仓库搜索框回车键事件
function handleWarehouseSearchKeyPress(event) {
    if (event.key === 'Enter') {
        event.preventDefault();
        loadWarehouseInventory(1);
    }
}
