<?php
// classes/TradeManager.php

require_once __DIR__ . '/../config/Database.php';
require_once __DIR__ . '/../config/RedisManager.php';

class TradeManager {
    private $db;
    private $redis;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->redis = RedisManager::getInstance();
    }
    
    /**
     * 验证交易条件
     * @param int $initiatorId 发起者ID
     * @param int $targetId 目标玩家ID
     * @return array 验证结果
     */
    public function validateTradeConditions($initiatorId, $targetId) {
        // 1. 获取双方玩家的场景ID
        $initiatorScene = $this->redis->with(function($redis) use ($initiatorId) {
            return $redis->get("player_scene:{$initiatorId}");
        });
        
        $targetScene = $this->redis->with(function($redis) use ($targetId) {
            return $redis->get("player_scene:{$targetId}");
        });
        
        // 2. 验证是否在同一场景
        if (!$initiatorScene || !$targetScene || $initiatorScene !== $targetScene) {
            return [
                'success' => false,
                'error' => '双方必须在同一场景才能进行交易'
            ];
        }
        
        // 3. 其他基础验证
        if ($initiatorId === $targetId) {
            return [
                'success' => false,
                'error' => '不能与自己交易'
            ];
        }
        
        // 4. 检查双方是否都存活
        if (!$this->isPlayerAlive($initiatorId) || !$this->isPlayerAlive($targetId)) {
            return [
                'success' => false,
                'error' => '交易双方必须都处于存活状态'
            ];
        }
        
        // 5. 检查是否已有进行中的交易
        if ($this->hasActiveTrade($initiatorId) || $this->hasActiveTrade($targetId)) {
            return [
                'success' => false,
                'error' => '交易双方不能有其他进行中的交易'
            ];
        }
        
        return [
            'success' => true,
            'scene_id' => $initiatorScene
        ];
    }
    
    /**
     * 创建交易会话
     * @param int $initiatorId 发起者ID
     * @param int $targetId 目标玩家ID
     * @param string $sceneId 场景ID
     * @return string|false 交易ID或失败
     */
    public function createTradeSession($initiatorId, $targetId, $sceneId) {
        try {
            $tradeId = $this->generateTradeId();
            $expiresAt = date('Y-m-d H:i:s', time() + 900); // 15分钟后过期
            
            $stmt = $this->db->query(
                "INSERT INTO player_trades (id, initiator_id, target_id, scene_id, status, expires_at) 
                 VALUES (?, ?, ?, ?, 'pending', ?)",
                [$tradeId, $initiatorId, $targetId, $sceneId, $expiresAt]
            );
            
            if ($stmt) {
                // 在Redis中缓存交易会话信息
                $this->redis->with(function($redis) use ($tradeId, $initiatorId, $targetId) {
                    $redis->setex("trade_session:{$tradeId}", 900, json_encode([
                        'initiator_id' => $initiatorId,
                        'target_id' => $targetId,
                        'status' => 'pending'
                    ]));
                    
                    // 标记玩家有活跃交易
                    $redis->setex("player_active_trade:{$initiatorId}", 900, $tradeId);
                    $redis->setex("player_active_trade:{$targetId}", 900, $tradeId);
                });
                
                return $tradeId;
            }
            
            return false;
        } catch (Exception $e) {
            error_log("TradeManager::createTradeSession Error: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 响应交易请求
     * @param string $tradeId 交易ID
     * @param int $playerId 响应玩家ID
     * @param bool $accepted 是否接受
     * @return array 处理结果
     */
    public function respondToTrade($tradeId, $playerId, $accepted) {
        try {
            // 获取交易信息
            $trade = $this->getTradeById($tradeId);
            if (!$trade) {
                return ['success' => false, 'error' => '交易不存在或已过期'];
            }
            
            // 验证响应者是目标玩家
            if ($trade['target_id'] != $playerId) {
                return ['success' => false, 'error' => '只有被邀请的玩家才能响应交易请求'];
            }
            
            // 验证交易状态
            if ($trade['status'] !== 'pending') {
                return ['success' => false, 'error' => '交易请求已失效'];
            }
            
            if ($accepted) {
                // 接受交易，更新状态为active
                $stmt = $this->db->query(
                    "UPDATE player_trades SET status = 'active' WHERE id = ?",
                    [$tradeId]
                );
                
                if ($stmt) {
                    // 更新Redis缓存
                    $this->redis->with(function($redis) use ($tradeId) {
                        $cacheKey = "trade_session:{$tradeId}";
                        $data = json_decode($redis->get($cacheKey), true);
                        if ($data) {
                            $data['status'] = 'active';
                            $redis->setex($cacheKey, 900, json_encode($data));
                        }
                    });
                    
                    return ['success' => true, 'status' => 'active'];
                }
            } else {
                // 拒绝交易，取消交易
                return $this->cancelTrade($tradeId, $playerId, '目标玩家拒绝了交易请求');
            }
            
            return ['success' => false, 'error' => '处理交易响应失败'];
        } catch (Exception $e) {
            error_log("TradeManager::respondToTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }
    
    /**
     * 取消交易
     * @param string $tradeId 交易ID
     * @param int $playerId 取消者ID
     * @param string $reason 取消原因
     * @return array 处理结果
     */
    public function cancelTrade($tradeId, $playerId, $reason = '玩家取消了交易') {
        try {
            $trade = $this->getTradeById($tradeId);
            if (!$trade) {
                return ['success' => false, 'error' => '交易不存在'];
            }
            
            // 更新交易状态
            $stmt = $this->db->query(
                "UPDATE player_trades SET status = 'cancelled', cancelled_reason = ? WHERE id = ?",
                [$reason, $tradeId]
            );
            
            if ($stmt) {
                // 清理Redis缓存
                $this->cleanupTradeCache($tradeId, $trade['initiator_id'], $trade['target_id']);
                
                return [
                    'success' => true,
                    'trade' => $trade,
                    'reason' => $reason
                ];
            }
            
            return ['success' => false, 'error' => '取消交易失败'];
        } catch (Exception $e) {
            error_log("TradeManager::cancelTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }
    
    /**
     * 检查玩家是否有活跃交易
     * @param int $playerId 玩家ID
     * @return bool
     */
    public function hasActiveTrade($playerId) {
        $tradeId = $this->redis->with(function($redis) use ($playerId) {
            return $redis->get("player_active_trade:{$playerId}");
        });
        
        if ($tradeId) {
            // 验证交易是否仍然有效
            $trade = $this->getTradeById($tradeId);
            return $trade && in_array($trade['status'], ['pending', 'active']);
        }
        
        return false;
    }
    
    /**
     * 获取玩家的活跃交易
     * @param int $playerId 玩家ID
     * @return array|null
     */
    public function getPlayerActiveTrade($playerId) {
        $tradeId = $this->redis->with(function($redis) use ($playerId) {
            return $redis->get("player_active_trade:{$playerId}");
        });
        
        if ($tradeId) {
            return $this->getTradeById($tradeId);
        }
        
        return null;
    }
    
    /**
     * 检查玩家是否存活
     * @param int $playerId 玩家ID
     * @return bool
     */
    private function isPlayerAlive($playerId) {
        $stmt = $this->db->query(
            "SELECT hp FROM player_attributes WHERE account_id = ?",
            [$playerId]
        );
        $player = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        return $player && $player['hp'] > 0;
    }
    
    /**
     * 根据ID获取交易信息
     * @param string $tradeId 交易ID
     * @return array|null
     */
    public function getTradeById($tradeId) {
        $stmt = $this->db->query(
            "SELECT * FROM player_trades WHERE id = ? AND expires_at > NOW()",
            [$tradeId]
        );
        $trade = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        return $trade ?: null;
    }
    
    /**
     * 生成交易ID
     * @return string
     */
    private function generateTradeId() {
        return sprintf('%04x%04x-%04x-%04x-%04x-%04x%04x%04x',
            mt_rand(0, 0xffff), mt_rand(0, 0xffff),
            mt_rand(0, 0xffff),
            mt_rand(0, 0x0fff) | 0x4000,
            mt_rand(0, 0x3fff) | 0x8000,
            mt_rand(0, 0xffff), mt_rand(0, 0xffff), mt_rand(0, 0xffff)
        );
    }
    
    /**
     * 添加物品到交易
     * @param string $tradeId 交易ID
     * @param int $playerId 玩家ID
     * @param int $inventoryId 库存物品ID
     * @param int $quantity 数量
     * @return array 处理结果
     */
    public function addItemToTrade($tradeId, $playerId, $inventoryId, $quantity) {
        try {
            // 验证交易状态
            $trade = $this->getTradeById($tradeId);
            if (!$trade || $trade['status'] !== 'active') {
                return ['success' => false, 'error' => '交易不存在或状态无效'];
            }

            // 验证玩家是交易参与者
            if ($trade['initiator_id'] != $playerId && $trade['target_id'] != $playerId) {
                return ['success' => false, 'error' => '你不是此交易的参与者'];
            }

            // 验证交易阶段和确认状态
            if ($trade['stage'] === 'final_confirm') {
                return ['success' => false, 'error' => '交易已进入最终确认阶段，无法修改物品'];
            }

            // 验证玩家确认状态
            $isInitiator = ($trade['initiator_id'] == $playerId);
            $playerConfirmed = $isInitiator ? $trade['initiator_confirmed'] : $trade['target_confirmed'];
            if ($playerConfirmed) {
                return ['success' => false, 'error' => '您已确认交易，无法添加物品'];
            }

            // 获取物品信息
            $stmt = $this->db->query(
                "SELECT pi.*, it.name as template_name, it.stackable
                 FROM player_inventory pi
                 JOIN item_templates it ON pi.item_template_id = it.id
                 WHERE pi.id = ? AND pi.player_id = ?",
                [$inventoryId, $playerId]
            );
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$item) {
                return ['success' => false, 'error' => '物品不存在或不属于你'];
            }

            // 检查物品是否绑定（绑定信息在player_inventory表中）
            if ($item['is_bound']) {
                return ['success' => false, 'error' => '绑定物品不能交易'];
            }

            // 检查数量
            if ($quantity > $item['quantity']) {
                return ['success' => false, 'error' => '交易数量超过拥有数量'];
            }

            // 检查物品是否已装备
            if ($item['is_equipped']) {
                return ['success' => false, 'error' => '已装备的物品不能交易'];
            }

            // 检查物品是否已经在交易中
            $stmt = $this->db->query(
                "SELECT SUM(quantity) as total_in_trade
                 FROM trade_items
                 WHERE trade_id = ? AND player_id = ? AND inventory_id = ?",
                [$tradeId, $playerId, $inventoryId]
            );
            $tradeCheck = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            $totalInTrade = $tradeCheck['total_in_trade'] ?? 0;
            $availableQuantity = $item['quantity'] - $totalInTrade;

            if ($totalInTrade > 0 && !$item['stackable']) {
                error_log("🚫 防作弊检测: 尝试重复添加不可堆叠物品 - 玩家ID: {$playerId}, 物品ID: {$inventoryId}, 物品名: {$item['name']}");
                return ['success' => false, 'error' => '该物品已经在交易中，不能重复添加'];
            }

            if ($quantity > $availableQuantity) {
                error_log("🚫 防作弊检测: 尝试添加超量物品 - 玩家ID: {$playerId}, 物品ID: {$inventoryId}, 请求数量: {$quantity}, 可用数量: {$availableQuantity}");
                return ['success' => false, 'error' => "可用数量不足，剩余可交易数量: {$availableQuantity}"];
            }

            if ($availableQuantity <= 0) {
                error_log("🚫 防作弊检测: 尝试添加已耗尽的物品 - 玩家ID: {$playerId}, 物品ID: {$inventoryId}, 已在交易中: {$totalInTrade}");
                return ['success' => false, 'error' => '该物品已全部添加到交易中'];
            }

            // 获取显示名称（如果有instance_data中的display_name则使用，否则使用模板名称）
            $displayName = $item['template_name']; // 默认使用模板名称
            if (!empty($item['instance_data'])) {
                try {
                    $instanceData = json_decode($item['instance_data'], true);
                    if (isset($instanceData['display_name']) && !empty($instanceData['display_name'])) {
                        $displayName = $instanceData['display_name'];
                    }
                } catch (Exception $e) {
                    error_log("解析instance_data失败: " . $e->getMessage());
                }
            }

            error_log("✅ 物品添加验证通过 - 玩家ID: {$playerId}, 物品: {$displayName}, 数量: {$quantity}, 可用: {$availableQuantity}");

            // 添加到交易物品表
            $stmt = $this->db->query(
                "INSERT INTO trade_items (trade_id, player_id, inventory_id, quantity, item_template_id, item_name, added_at)
                 VALUES (?, ?, ?, ?, ?, ?, NOW())",
                [$tradeId, $playerId, $inventoryId, $quantity, $item['item_template_id'], $displayName]
            );

            if ($stmt) {
                // 重置双方确认状态
                $this->resetTradeConfirmation($tradeId);

                return [
                    'success' => true,
                    'item' => [
                        'inventory_id' => $inventoryId,
                        'name' => $displayName,
                        'quantity' => $quantity,
                        'template_id' => $item['item_template_id']
                    ]
                ];
            }

            return ['success' => false, 'error' => '添加物品失败'];
        } catch (Exception $e) {
            error_log("TradeManager::addItemToTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }

    /**
     * 从交易中移除物品
     * @param string $tradeId 交易ID
     * @param int $playerId 玩家ID
     * @param int $tradeItemId 交易物品ID
     * @return array 处理结果
     */
    public function removeItemFromTrade($tradeId, $playerId, $tradeItemId) {
        try {
            // 验证交易状态
            $trade = $this->getTradeById($tradeId);
            if (!$trade || $trade['status'] !== 'active') {
                return ['success' => false, 'error' => '交易不存在或状态无效'];
            }

            // 验证交易阶段和确认状态
            if ($trade['stage'] === 'final_confirm') {
                return ['success' => false, 'error' => '交易已进入最终确认阶段，无法移除物品'];
            }

            // 验证玩家确认状态
            $isInitiator = ($trade['initiator_id'] == $playerId);
            $playerConfirmed = $isInitiator ? $trade['initiator_confirmed'] : $trade['target_confirmed'];
            if ($playerConfirmed) {
                return ['success' => false, 'error' => '您已确认交易，无法移除物品'];
            }

            // 删除交易物品
            $stmt = $this->db->query(
                "DELETE FROM trade_items WHERE id = ? AND trade_id = ? AND player_id = ?",
                [$tradeItemId, $tradeId, $playerId]
            );

            if ($stmt && $stmt->rowCount() > 0) {
                // 重置双方确认状态
                $this->resetTradeConfirmation($tradeId);

                return ['success' => true];
            }

            return ['success' => false, 'error' => '移除物品失败'];
        } catch (Exception $e) {
            error_log("TradeManager::removeItemFromTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }

    /**
     * 设置交易货币
     * @param string $tradeId 交易ID
     * @param int $playerId 玩家ID
     * @param int $goldAmount 金币数量
     * @param int $diamondAmount 钻石数量
     * @return array 处理结果
     */
    public function setTradeCurrency($tradeId, $playerId, $goldAmount, $diamondAmount) {
        try {
            // 验证交易状态
            $trade = $this->getTradeById($tradeId);
            if (!$trade || $trade['status'] !== 'active') {
                return ['success' => false, 'error' => '交易不存在或状态无效'];
            }

            // 验证交易阶段和确认状态
            if ($trade['stage'] === 'final_confirm') {
                return ['success' => false, 'error' => '交易已进入最终确认阶段，无法修改货币'];
            }

            // 验证玩家确认状态
            $isInitiator = ($trade['initiator_id'] == $playerId);
            $playerConfirmed = $isInitiator ? $trade['initiator_confirmed'] : $trade['target_confirmed'];
            if ($playerConfirmed) {
                return ['success' => false, 'error' => '您已确认交易，无法修改货币'];
            }

            // 验证玩家货币余额
            $stmt = $this->db->query(
                "SELECT gold, diamonds FROM player_attributes WHERE account_id = ?",
                [$playerId]
            );
            $playerCurrency = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$playerCurrency) {
                return ['success' => false, 'error' => '无法获取玩家货币信息'];
            }

            if ($goldAmount > $playerCurrency['gold']) {
                return ['success' => false, 'error' => '金币余额不足'];
            }

            if ($diamondAmount > $playerCurrency['diamonds']) {
                return ['success' => false, 'error' => '钻石余额不足'];
            }

            // 更新或插入交易货币记录
            $stmt = $this->db->query(
                "INSERT INTO trade_currencies (trade_id, player_id, gold_amount, diamond_amount, added_at)
                 VALUES (?, ?, ?, ?, NOW())
                 ON DUPLICATE KEY UPDATE
                 gold_amount = VALUES(gold_amount),
                 diamond_amount = VALUES(diamond_amount),
                 updated_at = NOW()",
                [$tradeId, $playerId, $goldAmount, $diamondAmount]
            );

            if ($stmt) {
                // 重置双方确认状态
                $this->resetTradeConfirmation($tradeId);

                return [
                    'success' => true,
                    'currency' => [
                        'gold' => $goldAmount,
                        'diamonds' => $diamondAmount
                    ]
                ];
            }

            return ['success' => false, 'error' => '设置交易货币失败'];
        } catch (Exception $e) {
            error_log("TradeManager::setTradeCurrency Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }

    /**
     * 重置交易确认状态
     * @param string $tradeId 交易ID
     */
    private function resetTradeConfirmation($tradeId) {
        $this->db->query(
            "UPDATE player_trades SET initiator_confirmed = 0, target_confirmed = 0 WHERE id = ?",
            [$tradeId]
        );
    }

    /**
     * 确认交易
     * @param string $tradeId 交易ID
     * @param int $playerId 玩家ID
     * @return array 处理结果
     */
    public function confirmTrade($tradeId, $playerId) {
        try {
            // 获取交易信息
            $trade = $this->getTradeById($tradeId);
            if (!$trade || $trade['status'] !== 'active') {
                return ['success' => false, 'error' => '交易不存在或状态无效'];
            }

            error_log("🔍 确认交易前状态: " . json_encode([
                'trade_id' => $tradeId,
                'player_id' => $playerId,
                'initiator_id' => $trade['initiator_id'],
                'target_id' => $trade['target_id'],
                'initiator_confirmed' => $trade['initiator_confirmed'],
                'target_confirmed' => $trade['target_confirmed']
            ], JSON_UNESCAPED_UNICODE));

            // 确定确认字段
            $isInitiator = ($trade['initiator_id'] == $playerId);
            $confirmField = $isInitiator ? 'initiator_confirmed' : 'target_confirmed';

            error_log("🎯 玩家角色判断: " . json_encode([
                'player_id' => $playerId,
                'is_initiator' => $isInitiator,
                'confirm_field' => $confirmField
            ], JSON_UNESCAPED_UNICODE));

            // 更新确认状态
            $stmt = $this->db->query(
                "UPDATE player_trades SET {$confirmField} = 1 WHERE id = ?",
                [$tradeId]
            );

            if (!$stmt) {
                return ['success' => false, 'error' => '确认交易失败'];
            }

            // 重新获取交易信息检查是否双方都确认
            $trade = $this->getTradeById($tradeId);

            error_log("✅ 确认交易后状态: " . json_encode([
                'trade_id' => $tradeId,
                'initiator_confirmed' => $trade['initiator_confirmed'],
                'target_confirmed' => $trade['target_confirmed'],
                'both_confirmed' => ($trade['initiator_confirmed'] && $trade['target_confirmed'])
            ], JSON_UNESCAPED_UNICODE));

            if ($trade['initiator_confirmed'] && $trade['target_confirmed']) {
                // 双方都初次确认，进入二次确认阶段
                error_log("🎯 双方都初次确认，进入二次确认阶段");
                $this->updateTradeStage($tradeId, 'final_confirm');
                $trade = $this->getTradeById($tradeId); // 重新获取更新后的数据
            }

            return [
                'success' => true,
                'both_confirmed' => false,
                'trade' => $trade
            ];
        } catch (Exception $e) {
            error_log("TradeManager::confirmTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }

    /**
     * 更新交易阶段
     * @param string $tradeId 交易ID
     * @param string $stage 新阶段
     * @return bool 是否成功
     */
    private function updateTradeStage($tradeId, $stage) {
        try {
            $stmt = $this->db->query(
                "UPDATE player_trades SET stage = ? WHERE id = ?",
                [$stage, $tradeId]
            );

            error_log("📝 更新交易阶段: 交易ID={$tradeId}, 新阶段={$stage}");
            return $stmt !== false;
        } catch (Exception $e) {
            error_log("TradeManager::updateTradeStage Error: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 最终确认交易
     * @param string $tradeId 交易ID
     * @param int $playerId 玩家ID
     * @return array 处理结果
     */
    public function finalConfirmTrade($tradeId, $playerId) {
        try {
            // 获取交易信息
            $trade = $this->getTradeById($tradeId);
            if (!$trade || $trade['status'] !== 'active') {
                return ['success' => false, 'error' => '交易不存在或状态无效'];
            }

            // 检查是否在二次确认阶段
            if ($trade['stage'] !== 'final_confirm') {
                return ['success' => false, 'error' => '交易不在二次确认阶段'];
            }

            error_log("🔍 最终确认交易前状态: " . json_encode([
                'trade_id' => $tradeId,
                'player_id' => $playerId,
                'initiator_id' => $trade['initiator_id'],
                'target_id' => $trade['target_id'],
                'initiator_final_confirmed' => $trade['initiator_final_confirmed'],
                'target_final_confirmed' => $trade['target_final_confirmed']
            ], JSON_UNESCAPED_UNICODE));

            // 确定确认字段
            $isInitiator = ($trade['initiator_id'] == $playerId);
            $confirmField = $isInitiator ? 'initiator_final_confirmed' : 'target_final_confirmed';

            error_log("🎯 玩家最终确认角色判断: " . json_encode([
                'player_id' => $playerId,
                'is_initiator' => $isInitiator,
                'confirm_field' => $confirmField
            ], JSON_UNESCAPED_UNICODE));

            // 更新最终确认状态
            $stmt = $this->db->query(
                "UPDATE player_trades SET {$confirmField} = 1 WHERE id = ?",
                [$tradeId]
            );

            if (!$stmt) {
                return ['success' => false, 'error' => '最终确认交易失败'];
            }

            // 重新获取交易信息检查是否双方都最终确认
            $trade = $this->getTradeById($tradeId);

            error_log("✅ 最终确认交易后状态: " . json_encode([
                'trade_id' => $tradeId,
                'initiator_final_confirmed' => $trade['initiator_final_confirmed'],
                'target_final_confirmed' => $trade['target_final_confirmed'],
                'both_final_confirmed' => ($trade['initiator_final_confirmed'] && $trade['target_final_confirmed'])
            ], JSON_UNESCAPED_UNICODE));

            if ($trade['initiator_final_confirmed'] && $trade['target_final_confirmed']) {
                // 双方都最终确认，执行交易
                error_log("🚀 双方都最终确认，执行交易");
                return $this->executeTrade($tradeId);
            }

            return [
                'success' => true,
                'both_final_confirmed' => false,
                'trade' => $trade
            ];
        } catch (Exception $e) {
            error_log("TradeManager::finalConfirmTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '系统错误'];
        }
    }

    /**
     * 执行交易
     * @param string $tradeId 交易ID
     * @return array 处理结果
     */
    public function executeTrade($tradeId) {
        try {
            $this->db->getConnection()->beginTransaction();

            // 获取交易信息
            $trade = $this->getTradeById($tradeId);
            if (!$trade) {
                $this->db->getConnection()->rollBack();
                return ['success' => false, 'error' => '交易不存在'];
            }

            // 获取交易物品
            $stmt = $this->db->query(
                "SELECT * FROM trade_items WHERE trade_id = ?",
                [$tradeId]
            );
            $tradeItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            // 获取交易货币
            $stmt = $this->db->query(
                "SELECT * FROM trade_currencies WHERE trade_id = ?",
                [$tradeId]
            );
            $tradeCurrencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            // 验证所有物品仍然有效
            foreach ($tradeItems as $tradeItem) {
                $stmt = $this->db->query(
                    "SELECT quantity, is_equipped, is_bound FROM player_inventory
                     WHERE id = ? AND player_id = ?",
                    [$tradeItem['inventory_id'], $tradeItem['player_id']]
                );
                $currentItem = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();

                if (!$currentItem ||
                    $currentItem['quantity'] < $tradeItem['quantity'] ||
                    $currentItem['is_equipped'] ||
                    $currentItem['is_bound']) {
                    $this->db->getConnection()->rollBack();
                    return ['success' => false, 'error' => '物品状态已改变，交易失败'];
                }
            }

            // 验证货币余额
            foreach ($tradeCurrencies as $currency) {
                $stmt = $this->db->query(
                    "SELECT gold, diamonds FROM player_attributes WHERE account_id = ?",
                    [$currency['player_id']]
                );
                $playerCurrency = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();

                if (!$playerCurrency ||
                    $playerCurrency['gold'] < $currency['gold_amount'] ||
                    $playerCurrency['diamonds'] < $currency['diamond_amount']) {
                    $this->db->getConnection()->rollBack();
                    return ['success' => false, 'error' => '货币余额不足，交易失败'];
                }
            }

            // 执行物品转移
            foreach ($tradeItems as $tradeItem) {
                $this->transferItem($tradeItem, $trade);
            }

            // 执行货币转移
            foreach ($tradeCurrencies as $currency) {
                $this->transferCurrency($currency, $trade);
            }

            // 更新交易状态
            $this->db->query(
                "UPDATE player_trades SET status = 'completed', completed_at = NOW() WHERE id = ?",
                [$tradeId]
            );

            // 记录交易历史
            $this->recordTradeHistory($trade, $tradeItems, $tradeCurrencies);

            $this->db->getConnection()->commit();

            // 检查任务进度更新（交易完成后获得物品）
            $this->checkQuestProgressAfterTrade($trade, $tradeItems);

            // 清理缓存
            $this->cleanupTradeCache($tradeId, $trade['initiator_id'], $trade['target_id']);

            return [
                'success' => true,
                'trade' => $trade,
                'items' => $tradeItems,
                'currencies' => $tradeCurrencies
            ];
        } catch (Exception $e) {
            $this->db->getConnection()->rollBack();
            error_log("TradeManager::executeTrade Error: " . $e->getMessage());
            return ['success' => false, 'error' => '交易执行失败'];
        }
    }

    /**
     * 转移物品
     * @param array $tradeItem 交易物品信息
     * @param array $trade 交易信息
     */
    private function transferItem($tradeItem, $trade) {
        $fromPlayerId = $tradeItem['player_id'];
        $toPlayerId = ($fromPlayerId == $trade['initiator_id']) ? $trade['target_id'] : $trade['initiator_id'];

        // 获取原物品的完整信息，包括instance_data
        $stmt = $this->db->query(
            "SELECT * FROM player_inventory WHERE id = ?",
            [$tradeItem['inventory_id']]
        );
        $originalItem = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if (!$originalItem) {
            error_log("错误: 无法找到要转移的物品 ID: {$tradeItem['inventory_id']}");
            return;
        }

        // 从原玩家减少物品
        if ($tradeItem['quantity'] == $originalItem['quantity']) {
            // 全部转移，直接更改所有者
            $this->db->query(
                "UPDATE player_inventory SET player_id = ?, is_bound = 0 WHERE id = ?",
                [$toPlayerId, $tradeItem['inventory_id']]
            );
            error_log("✅ 完整转移物品: ID={$tradeItem['inventory_id']}, 从玩家{$fromPlayerId}到玩家{$toPlayerId}");
        } else {
            // 部分转移，需要分割物品
            // 先减少原物品数量
            $this->db->query(
                "UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?",
                [$tradeItem['quantity'], $tradeItem['inventory_id']]
            );

            // 为目标玩家创建新物品记录，保留instance_data
            $this->addItemToPlayerWithData($toPlayerId, $originalItem, $tradeItem['quantity']);
            error_log("✅ 部分转移物品: 原ID={$tradeItem['inventory_id']}, 数量={$tradeItem['quantity']}, 从玩家{$fromPlayerId}到玩家{$toPlayerId}");
        }
    }

    /**
     * 转移货币
     * @param array $currency 货币信息
     * @param array $trade 交易信息
     */
    private function transferCurrency($currency, $trade) {
        $fromPlayerId = $currency['player_id'];
        $toPlayerId = ($fromPlayerId == $trade['initiator_id']) ? $trade['target_id'] : $trade['initiator_id'];

        // 从原玩家扣除货币
        if ($currency['gold_amount'] > 0) {
            $this->db->query(
                "UPDATE player_attributes SET gold = gold - ? WHERE account_id = ?",
                [$currency['gold_amount'], $fromPlayerId]
            );
        }

        if ($currency['diamond_amount'] > 0) {
            $this->db->query(
                "UPDATE player_attributes SET diamonds = diamonds - ? WHERE account_id = ?",
                [$currency['diamond_amount'], $fromPlayerId]
            );
        }

        // 给目标玩家添加货币
        if ($currency['gold_amount'] > 0) {
            $this->db->query(
                "UPDATE player_attributes SET gold = gold + ? WHERE account_id = ?",
                [$currency['gold_amount'], $toPlayerId]
            );
        }

        if ($currency['diamond_amount'] > 0) {
            $this->db->query(
                "UPDATE player_attributes SET diamonds = diamonds + ? WHERE account_id = ?",
                [$currency['diamond_amount'], $toPlayerId]
            );
        }
    }

    /**
     * 记录交易历史
     * @param array $trade 交易信息
     * @param array $tradeItems 交易物品
     * @param array $tradeCurrencies 交易货币
     */
    private function recordTradeHistory($trade, $tradeItems, $tradeCurrencies) {
        $itemsSummary = json_encode($tradeItems);
        $currencySummary = json_encode($tradeCurrencies);

        $this->db->query(
            "INSERT INTO trade_history (trade_id, initiator_id, target_id, scene_id, items_summary, currency_summary, completed_at)
             VALUES (?, ?, ?, ?, ?, ?, NOW())",
            [$trade['id'], $trade['initiator_id'], $trade['target_id'], $trade['scene_id'], $itemsSummary, $currencySummary]
        );
    }

    /**
     * 获取库存物品数量
     * @param int $inventoryId 库存ID
     * @return int
     */
    private function getInventoryItemQuantity($inventoryId) {
        $stmt = $this->db->query(
            "SELECT quantity FROM player_inventory WHERE id = ?",
            [$inventoryId]
        );
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        return $result ? $result['quantity'] : 0;
    }

    /**
     * 给玩家添加物品
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 数量
     */
    private function addItemToPlayer($playerId, $itemTemplateId, $quantity) {
        // 检查是否可堆叠
        $stmt = $this->db->query(
            "SELECT stackable FROM item_templates WHERE id = ?",
            [$itemTemplateId]
        );
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if ($item && $item['stackable']) {
            // 可堆叠物品，尝试合并
            $stmt = $this->db->query(
                "SELECT id, quantity FROM player_inventory
                 WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0
                 LIMIT 1",
                [$playerId, $itemTemplateId]
            );
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if ($existing) {
                // 合并到现有物品
                $this->db->query(
                    "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?",
                    [$quantity, $existing['id']]
                );
                return;
            }
        }

        // 创建新物品记录
        $this->db->query(
            "INSERT INTO player_inventory (player_id, item_template_id, quantity, is_equipped, is_bound)
             VALUES (?, ?, ?, 0, 0)",
            [$playerId, $itemTemplateId, $quantity]
        );
    }

    /**
     * 给玩家添加物品（保留instance_data）
     * @param int $playerId 玩家ID
     * @param array $originalItem 原物品数据
     * @param int $quantity 数量
     */
    private function addItemToPlayerWithData($playerId, $originalItem, $quantity) {
        // 检查是否可堆叠且没有instance_data
        $stmt = $this->db->query(
            "SELECT stackable FROM item_templates WHERE id = ?",
            [$originalItem['item_template_id']]
        );
        $itemTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 如果是可堆叠物品且没有特殊属性，尝试合并
        if ($itemTemplate && $itemTemplate['stackable'] && empty($originalItem['instance_data'])) {
            $stmt = $this->db->query(
                "SELECT id, quantity FROM player_inventory
                 WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND (instance_data IS NULL OR instance_data = '')
                 LIMIT 1",
                [$playerId, $originalItem['item_template_id']]
            );
            $existing = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if ($existing) {
                // 合并到现有物品
                $this->db->query(
                    "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?",
                    [$quantity, $existing['id']]
                );
                return;
            }
        }

        // 创建新物品记录，保留所有属性
        $this->db->query(
            "INSERT INTO player_inventory (player_id, item_template_id, quantity, is_equipped, is_bound, instance_data, created_at)
             VALUES (?, ?, ?, 0, 0, ?, NOW())",
            [$playerId, $originalItem['item_template_id'], $quantity, $originalItem['instance_data']]
        );

        error_log("✅ 创建新物品记录: 玩家ID={$playerId}, 模板ID={$originalItem['item_template_id']}, 数量={$quantity}, 保留instance_data");
    }

    /**
     * 获取交易详情
     * @param string $tradeId 交易ID
     * @return array|null
     */
    public function getTradeDetails($tradeId) {
        $trade = $this->getTradeById($tradeId);
        if (!$trade) {
            return null;
        }

        // 获取交易物品（包含完整的物品信息）
        $stmt = $this->db->query(
            "SELECT ti.id, ti.trade_id, ti.player_id, ti.inventory_id, ti.quantity,
                    ti.item_template_id, ti.item_name, ti.added_at,
                    it.name as template_name, it.category, it.description, it.stackable, it.effects,
                    ed.stats, ed.sockets, ed.slot, ed.job_restriction, ed.grants_job_id,
                    pi.instance_data, pi.is_bound, pi.is_equipped
             FROM trade_items ti
             JOIN item_templates it ON ti.item_template_id = it.id
             LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
             LEFT JOIN player_inventory pi ON ti.inventory_id = pi.id
             WHERE ti.trade_id = ?
             ORDER BY ti.player_id, ti.added_at",
            [$tradeId]
        );
        $tradeItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        error_log("🔍 获取交易物品详情: 交易ID={$tradeId}, 物品数量=" . count($tradeItems));
        foreach ($tradeItems as $item) {
            error_log("📦 物品: {$item['item_name']}, 分类={$item['category']}, inventory_id={$item['inventory_id']}, instance_data=" . ($item['instance_data'] ? '有' : '无') . ", stats=" . ($item['stats'] ? '有' : '无') . ", effects=" . ($item['effects'] ? '有' : '无'));
        }

        // 获取交易货币
        $stmt = $this->db->query(
            "SELECT * FROM trade_currencies WHERE trade_id = ?",
            [$tradeId]
        );
        $tradeCurrencies = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 获取玩家名称
        $stmt = $this->db->query(
            "SELECT id, username FROM accounts WHERE id IN (?, ?)",
            [$trade['initiator_id'], $trade['target_id']]
        );
        $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $playerNames = [];
        foreach ($players as $player) {
            $playerNames[$player['id']] = $player['username'];
        }

        return [
            'trade' => $trade,
            'items' => $tradeItems,
            'currencies' => $tradeCurrencies,
            'player_names' => $playerNames
        ];
    }

    /**
     * 清理过期交易
     * @return int 清理的交易数量
     */
    public function cleanupExpiredTrades() {
        try {
            // 获取过期的交易
            $stmt = $this->db->query(
                "SELECT id, initiator_id, target_id FROM player_trades
                 WHERE status IN ('pending', 'active') AND expires_at < NOW()"
            );
            $expiredTrades = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            $cleanedCount = 0;
            foreach ($expiredTrades as $trade) {
                // 更新交易状态为过期
                $this->db->query(
                    "UPDATE player_trades SET status = 'expired' WHERE id = ?",
                    [$trade['id']]
                );

                // 清理缓存
                $this->cleanupTradeCache($trade['id'], $trade['initiator_id'], $trade['target_id']);

                $cleanedCount++;
            }

            return $cleanedCount;
        } catch (Exception $e) {
            error_log("TradeManager::cleanupExpiredTrades Error: " . $e->getMessage());
            return 0;
        }
    }

    /**
     * 清理交易缓存
     * @param string $tradeId 交易ID
     * @param int $initiatorId 发起者ID
     * @param int $targetId 目标玩家ID
     */
    private function cleanupTradeCache($tradeId, $initiatorId, $targetId) {
        $this->redis->with(function($redis) use ($tradeId, $initiatorId, $targetId) {
            $redis->del("trade_session:{$tradeId}");
            $redis->del("player_active_trade:{$initiatorId}");
            $redis->del("player_active_trade:{$targetId}");
        });
    }

    /**
     * 检查交易完成后的任务进度更新
     * @param array $trade 交易信息
     * @param array $tradeItems 交易物品列表
     */
    private function checkQuestProgressAfterTrade($trade, $tradeItems) {
        try {
            // 引入QuestManager
            require_once __DIR__ . '/QuestManager.php';
            $questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });

            // 为每个玩家检查获得和失去的物品是否触发任务进度
            $playerGainedItems = [];  // 玩家获得的物品
            $playerLostItems = [];    // 玩家失去的物品

            // 整理每个玩家获得和失去的物品
            foreach ($tradeItems as $tradeItem) {
                $fromPlayerId = $tradeItem['player_id'];
                $toPlayerId = ($fromPlayerId == $trade['initiator_id']) ? $trade['target_id'] : $trade['initiator_id'];

                // 记录获得物品的玩家
                if (!isset($playerGainedItems[$toPlayerId])) {
                    $playerGainedItems[$toPlayerId] = [];
                }
                $playerGainedItems[$toPlayerId][] = [
                    'item_template_id' => $tradeItem['item_template_id'],
                    'quantity' => $tradeItem['quantity']
                ];

                // 记录失去物品的玩家
                if (!isset($playerLostItems[$fromPlayerId])) {
                    $playerLostItems[$fromPlayerId] = [];
                }
                $playerLostItems[$fromPlayerId][] = [
                    'item_template_id' => $tradeItem['item_template_id'],
                    'quantity' => $tradeItem['quantity']
                ];
            }

            // 检查获得物品的任务进度（收集任务）
            foreach ($playerGainedItems as $playerId => $items) {
                foreach ($items as $item) {
                    $itemTemplateId = $item['item_template_id'];
                    $quantity = $item['quantity'];

                    // 检查收集任务目标更新
                    $questUpdates = $questManager->checkQuestObjectiveUpdates(
                        $playerId,
                        'collect',
                        $itemTemplateId,
                        $quantity
                    );

                    if (!empty($questUpdates)) {
                        error_log("🎯 交易获得物品触发任务进度更新: 玩家ID={$playerId}, 物品ID={$itemTemplateId}, 数量={$quantity}, 更新任务数=" . count($questUpdates));
                        $this->publishQuestProgressUpdate($playerId, $questUpdates);
                    }
                }
            }

            // 检查失去物品的任务进度（收集任务的逆向操作）
            foreach ($playerLostItems as $playerId => $items) {
                foreach ($items as $item) {
                    $itemTemplateId = $item['item_template_id'];
                    $quantity = $item['quantity'];

                    // 检查收集任务目标更新（使用负数量表示失去物品）
                    $questUpdates = $questManager->checkQuestObjectiveUpdates(
                        $playerId,
                        'collect',
                        $itemTemplateId,
                        -$quantity  // 使用负数量表示物品减少
                    );

                    if (!empty($questUpdates)) {
                        error_log("🎯 交易失去物品触发任务进度更新: 玩家ID={$playerId}, 物品ID={$itemTemplateId}, 数量=-{$quantity}, 更新任务数=" . count($questUpdates));
                        $this->publishQuestProgressUpdate($playerId, $questUpdates);
                    }
                }
            }

        } catch (Exception $e) {
            error_log("TradeManager::checkQuestProgressAfterTrade Error: " . $e->getMessage());
            // 任务进度检查失败不应该影响交易完成，所以只记录错误不抛出异常
        }
    }

    /**
     * 通过Redis发布任务进度更新通知
     * @param int $playerId 玩家ID
     * @param array $questUpdates 任务更新数据
     */
    private function publishQuestProgressUpdate($playerId, $questUpdates) {
        try {
            $this->redis->with(function($redis) use ($playerId, $questUpdates) {
                $notificationData = [
                    'type' => 'quest_progress_update',
                    'player_id' => $playerId,
                    'quest_updates' => $questUpdates,
                    'source' => 'trade_system'
                ];

                // 发布到任务进度更新频道
                $redis->publish('quest-progress-updates', json_encode($notificationData));

                error_log("📢 发布任务进度更新通知: 玩家ID={$playerId}, 更新任务数=" . count($questUpdates));
            });
        } catch (Exception $e) {
            error_log("TradeManager::publishQuestProgressUpdate Error: " . $e->getMessage());
        }
    }
}
