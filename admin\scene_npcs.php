<?php
// admin/scene_npcs.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';
require_once '../config/RedisManager.php'; // 引入Redis管理器

// 函数：发布NPC更新通知
function notify_server() {
    // 后台管理脚本在标准的Web服务器环境（如Nginx+PHP-FPM）下运行，
    // 而非Swoole的协程环境，因此不能使用为协程设计的Redis连接池。
    // 这里我们使用标准的phpredis扩展直接发送通知。
    if (!class_exists('Redis')) {
        error_log("phpredis extension is not installed. Cannot send NPC update notification from admin panel.");
        return;
    }

    try {
        $redis = new Redis();
        // 使用与您现有Swoole Redis池相同的连接信息
        $redis->connect('127.0.0.1', 6379, 1.0); // 1秒超时
        // 如果您的Redis有密码，请在此处填写
        // $redis->auth('');
        
        $redis->publish('game-system-notifications', 'npcs_updated');
        $redis->close();

    } catch (Exception $e) {
        // 在生产环境中，最好只记录日志，不应中断用户操作
        error_log("Redis publish failed in scene_npcs.php (using phpredis): " . $e->getMessage());
    }
}

// 检查是否提供了场景ID
if (!isset($_GET['scene_id']) || empty($_GET['scene_id'])) {
    header('Location: scenes.php');
    exit;
}

$scene_id = $_GET['scene_id'];
$pageTitle = 'NPC场景配置';
$currentPage = 'scenes';
$extra_css = '
<style>
    .npc-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }
    .npc-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background: #fff;
    }
    .npc-card h4 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }
    .npc-card .position {
        color: #666;
        font-size: 0.9em;
    }
    .npc-card .actions {
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
    }
    .add-npc-container {
        margin-bottom: 20px;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 5px;
        border: 1px solid #ddd;
    }
    .form-row {
        display: flex;
        gap: 15px;
        margin-bottom: 15px;
    }
    .form-group.half {
        flex: 1;
    }
    .form-group {
        margin-bottom: 15px;
    }
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    .form-group select,
    .form-group input {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
    }
    #npc-filter-info {
        background: #e8f4fd;
        padding: 8px;
        border-radius: 4px;
        border-left: 4px solid #2196F3;
    }
</style>';

// 初始化变量
$success_message = '';
$error_message = '';
$scene = null;
$npcs = [];
$npc_templates = [];
$scene_npcs = [];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取场景信息
    $stmt = $pdo->prepare("SELECT * FROM scenes WHERE id = :id");
    $stmt->execute([':id' => $scene_id]);
    $scene = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$scene) {
        $error_message = "找不到指定的场景";
    } else {
        // 处理NPC添加/编辑/删除
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            if ($_POST['action'] === 'add_npc') {
                // 添加NPC到场景
                $template_id = (int)$_POST['template_id'];
                $position_desc = $_POST['position_desc'] ?? '';
                
                $stmt = $pdo->prepare("
                    INSERT INTO npc_instances 
                    (template_id, scene_id, position_desc)
                    VALUES 
                    (:template_id, :scene_id, :position_desc)
                ");
                $stmt->execute([
                    ':template_id' => $template_id,
                    ':scene_id' => $scene_id,
                    ':position_desc' => $position_desc
                ]);
                $success_message = 'NPC添加到场景成功！';
                notify_server(); // 添加通知
                
            } elseif ($_POST['action'] === 'update_npc' && isset($_POST['npc_id'])) {
                // 更新场景中的NPC
                $npc_id = (int)$_POST['npc_id'];
                $position_desc = $_POST['position_desc'] ?? '';
                
                $stmt = $pdo->prepare("
                    UPDATE npc_instances 
                    SET position_desc = :position_desc,
                        updated_at = NOW()
                    WHERE id = :id AND scene_id = :scene_id
                ");
                $stmt->execute([
                    ':position_desc' => $position_desc,
                    ':id' => $npc_id,
                    ':scene_id' => $scene_id
                ]);
                $success_message = 'NPC位置更新成功！';
                notify_server(); // 添加通知
                
            } elseif ($_POST['action'] === 'delete_npc' && isset($_POST['npc_id'])) {
                // 从场景中删除NPC
                $npc_id = (int)$_POST['npc_id'];
                $stmt = $pdo->prepare("DELETE FROM npc_instances WHERE id = :id AND scene_id = :scene_id");
                $stmt->execute([':id' => $npc_id, ':scene_id' => $scene_id]);
                $success_message = 'NPC从场景中移除成功！';
                notify_server(); // 添加通知
            }
        }
        
        // 获取所有NPC分组
        $stmt = $pdo->query("SELECT * FROM npc_groups ORDER BY sort_order, name");
        $npc_groups = $stmt->fetchAll(PDO::FETCH_ASSOC);

        // 获取所有NPC模板（包含分组信息）
        $stmt = $pdo->query("
            SELECT nt.*, ng.name as group_name, ng.type as group_type
            FROM npc_templates nt
            LEFT JOIN npc_groups ng ON nt.group_id = ng.id
            ORDER BY ng.sort_order, ng.name, nt.name
        ");
        $npc_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取当前场景中的所有NPC
        $stmt = $pdo->prepare("
            SELECT ni.*, nt.name, nt.level, nt.is_merchant, nt.is_quest_giver
            FROM npc_instances ni
            JOIN npc_templates nt ON ni.template_id = nt.id
            WHERE ni.scene_id = :scene_id
            ORDER BY ni.id
        ");
        $stmt->execute([':scene_id' => $scene_id]);
        $scene_npcs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 引入页面头部
require_once 'layout_header.php';
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <?php if ($scene): ?>
        <div class="content-header">
            <h2>场景NPC配置: <?php echo htmlspecialchars($scene['name']); ?></h2>
            <a href="scenes.php" class="btn">返回场景列表</a>
        </div>
        
        <div class="card">
            <div class="add-npc-container">
                <h3>添加NPC到场景</h3>
                <form method="post" action="scene_npcs.php?scene_id=<?php echo $scene_id; ?>">
                    <input type="hidden" name="action" value="add_npc">

                    <div class="form-row">
                        <div class="form-group half">
                            <label for="npc_group_filter">NPC分组筛选</label>
                            <select id="npc_group_filter">
                                <option value="">-- 显示所有分组 --</option>
                                <?php foreach ($npc_groups as $group): ?>
                                    <option value="<?php echo $group['id']; ?>">
                                        <?php echo htmlspecialchars($group['name']); ?>
                                        <?php if ($group['type']): ?>
                                            (<?php echo htmlspecialchars($group['type']); ?>)
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <div class="form-group half">
                            <label for="template_id">选择NPC</label>
                            <select id="template_id" name="template_id" required>
                                <option value="">-- 选择NPC --</option>
                                <?php foreach ($npc_templates as $template): ?>
                                    <option value="<?php echo $template['id']; ?>"
                                            data-group-id="<?php echo $template['group_id'] ?? ''; ?>"
                                            data-group-name="<?php echo htmlspecialchars($template['group_name'] ?? '未分组'); ?>">
                                        <?php if ($template['group_name']): ?>
                                            [<?php echo htmlspecialchars($template['group_name']); ?>]
                                        <?php endif; ?>
                                        <?php echo htmlspecialchars($template['name']); ?> (Lv.<?php echo $template['level']; ?>)
                                        <?php if ($template['is_merchant'] && $template['is_quest_giver']): ?>
                                            [商人/任务]
                                        <?php elseif ($template['is_merchant']): ?>
                                            [商人]
                                        <?php elseif ($template['is_quest_giver']): ?>
                                            [任务]
                                        <?php endif; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="position_desc">位置描述</label>
                            <input type="text" id="position_desc" name="position_desc" placeholder="如：东北角、城门旁等">
                        </div>
                    </div>

                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">添加NPC</button>
                    </div>
                </form>
            </div>
        </div>
        
        <div class="content-header">
            <h3>场景中的NPC (<?php echo count($scene_npcs); ?>)</h3>
        </div>
        
        <div class="npc-grid">
            <?php if (empty($scene_npcs)): ?>
                <p>此场景中还没有NPC</p>
            <?php else: ?>
                <?php foreach ($scene_npcs as $npc): ?>
                    <div class="npc-card" data-id="<?php echo $npc['id']; ?>">
                        <h4><?php echo htmlspecialchars($npc['name']); ?></h4>
                        <div>
                            <?php if ($npc['is_merchant']): ?><span class="badge">商人</span><?php endif; ?>
                            <?php if ($npc['is_quest_giver']): ?><span class="badge">任务</span><?php endif; ?>
                        </div>
                        <div class="position">
                            位置: <?php echo htmlspecialchars($npc['position_desc'] ?: '无位置描述'); ?>
                        </div>
                        <div class="actions">
                            <button class="btn btn-sm edit-npc" data-id="<?php echo $npc['id']; ?>" data-position="<?php echo htmlspecialchars($npc['position_desc'] ?: ''); ?>">编辑</button>
                            <form method="post" action="scene_npcs.php?scene_id=<?php echo $scene_id; ?>" class="inline-form" onsubmit="return confirm('确定要从场景中移除此NPC吗？');">
                                <input type="hidden" name="action" value="delete_npc">
                                <input type="hidden" name="npc_id" value="<?php echo $npc['id']; ?>">
                                <button type="submit" class="btn btn-sm btn-danger">移除</button>
                            </form>
                        </div>
                    </div>
                <?php endforeach; ?>
            <?php endif; ?>
        </div>
    <?php endif; ?>
</div>

<!-- NPC编辑模态框 -->
<div id="edit-npc-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑NPC位置</h3>
        
        <form method="post" action="scene_npcs.php?scene_id=<?php echo $scene_id; ?>" id="edit-npc-form">
            <input type="hidden" name="action" value="update_npc">
            <input type="hidden" name="npc_id" id="edit-npc-id" value="">
            
            <div class="form-group">
                <label for="edit-position-desc">位置描述</label>
                <input type="text" id="edit-position-desc" name="position_desc" placeholder="如：东北角、城门旁等">
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新位置</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var editModal = document.getElementById('edit-npc-modal');
    var groupFilter = document.getElementById('npc_group_filter');
    var npcSelect = document.getElementById('template_id');
    var allOptions = Array.from(npcSelect.options).slice(1); // 排除第一个"选择NPC"选项

    // NPC分组筛选功能
    groupFilter.addEventListener('change', function() {
        var selectedGroupId = this.value;

        // 清空NPC选择框（保留第一个选项）
        npcSelect.innerHTML = '<option value="">-- 选择NPC --</option>';

        // 根据选择的分组筛选NPC
        var filteredOptions = allOptions.filter(function(option) {
            if (selectedGroupId === '') {
                return true; // 显示所有NPC
            }
            return option.dataset.groupId === selectedGroupId;
        });

        // 添加筛选后的选项
        filteredOptions.forEach(function(option) {
            npcSelect.appendChild(option.cloneNode(true));
        });

        // 更新显示信息
        var totalCount = allOptions.length;
        var filteredCount = filteredOptions.length;
        var groupName = selectedGroupId ? groupFilter.options[groupFilter.selectedIndex].text : '所有分组';

        // 在NPC选择框下方显示筛选信息
        var infoElement = document.getElementById('npc-filter-info');
        if (!infoElement) {
            infoElement = document.createElement('div');
            infoElement.id = 'npc-filter-info';
            infoElement.style.cssText = 'font-size: 0.9em; color: #666; margin-top: 5px;';
            npcSelect.parentNode.appendChild(infoElement);
        }

        if (selectedGroupId) {
            infoElement.textContent = `显示 ${groupName} 分组的 ${filteredCount} 个NPC（总共 ${totalCount} 个）`;
        } else {
            infoElement.textContent = `显示所有 ${totalCount} 个NPC`;
        }
    });

    // 编辑按钮点击事件
    document.querySelectorAll('.edit-npc').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var npcId = this.dataset.id;
            var position = this.dataset.position;

            document.getElementById('edit-npc-id').value = npcId;
            document.getElementById('edit-position-desc').value = position;
            editModal.style.display = 'block';
        });
    });

    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            editModal.style.display = 'none';
        });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == editModal) {
            editModal.style.display = 'none';
        }
    });
});
</script>

<?php require_once 'layout_footer.php'; ?>