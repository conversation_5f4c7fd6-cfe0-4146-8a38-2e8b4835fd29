# Details

Date : 2025-07-09 16:20:59

Directory c:\\Users\\<USER>\\Desktop\\game

Total : 189 files,  62218 codes, 12214 comments, 11079 blanks, all 85511 lines

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)

## Files
| filename | language | code | comment | blank | total |
| :--- | :--- | ---: | ---: | ---: | ---: |
| [.user.ini](/.user.ini) | Ini | 1 | 0 | 0 | 1 |
| [404.html](/404.html) | HTML | 7 | 0 | 0 | 7 |
| [README.md](/README.md) | Markdown | 54 | 0 | 12 | 66 |
| [admin/api\_battle\_logs.php](/admin/api_battle_logs.php) | PHP | 346 | 43 | 68 | 457 |
| [admin/api\_buildings.php](/admin/api_buildings.php) | PHP | 204 | 16 | 47 | 267 |
| [admin/api\_gem\_recipes.php](/admin/api_gem_recipes.php) | PHP | 180 | 10 | 36 | 226 |
| [admin/api\_item\_templates.php](/admin/api_item_templates.php) | PHP | 28 | 3 | 7 | 38 |
| [admin/api\_items.php](/admin/api_items.php) | PHP | 252 | 14 | 51 | 317 |
| [admin/api\_jobs.php](/admin/api_jobs.php) | PHP | 18 | 1 | 5 | 24 |
| [admin/api\_loot\_tables.php](/admin/api_loot_tables.php) | PHP | 125 | 11 | 27 | 163 |
| [admin/api\_monsters.php](/admin/api_monsters.php) | PHP | 286 | 27 | 63 | 376 |
| [admin/api\_npcs.php](/admin/api_npcs.php) | PHP | 251 | 30 | 47 | 328 |
| [admin/api\_player\_inventory.php](/admin/api_player_inventory.php) | PHP | 307 | 26 | 62 | 395 |
| [admin/api\_pvp\_battle\_logs.php](/admin/api_pvp_battle_logs.php) | PHP | 184 | 19 | 41 | 244 |
| [admin/api\_quests.php](/admin/api_quests.php) | PHP | 269 | 30 | 47 | 346 |
| [admin/api\_recipes.php](/admin/api_recipes.php) | PHP | 137 | 5 | 22 | 164 |
| [admin/api\_scenes.php](/admin/api_scenes.php) | PHP | 560 | 42 | 126 | 728 |
| [admin/api\_shop.php](/admin/api_shop.php) | PHP | 75 | 3 | 16 | 94 |
| [admin/api\_skills.php](/admin/api_skills.php) | PHP | 96 | 8 | 15 | 119 |
| [admin/api\_teleporter.php](/admin/api_teleporter.php) | PHP | 202 | 17 | 37 | 256 |
| [admin/auth.php](/admin/auth.php) | PHP | 15 | 5 | 3 | 23 |
| [admin/battle\_logs.css](/admin/battle_logs.css) | PostCSS | 244 | 9 | 42 | 295 |
| [admin/battle\_logs.js](/admin/battle_logs.js) | JavaScript | 229 | 19 | 37 | 285 |
| [admin/battle\_logs.php](/admin/battle_logs.php) | PHP | 157 | 0 | 11 | 168 |
| [admin/buildings.js](/admin/buildings.js) | JavaScript | 263 | 32 | 35 | 330 |
| [admin/buildings.php](/admin/buildings.php) | PHP | 300 | 2 | 32 | 334 |
| [admin/clear\_cache.php](/admin/clear_cache.php) | PHP | 45 | 16 | 15 | 76 |
| [admin/clear\_server\_cache.php](/admin/clear_server_cache.php) | PHP | 25 | 6 | 6 | 37 |
| [admin/dashboard.php](/admin/dashboard.php) | PHP | 84 | 9 | 11 | 104 |
| [admin/dialogue\_editor.php](/admin/dialogue_editor.php) | PHP | 1,011 | 73 | 131 | 1,215 |
| [admin/dialogues.php](/admin/dialogues.php) | PHP | 689 | 39 | 91 | 819 |
| [admin/gem\_recipes.js](/admin/gem_recipes.js) | JavaScript | 342 | 9 | 12 | 363 |
| [admin/gem\_recipes.php](/admin/gem_recipes.php) | PHP | 222 | 10 | 29 | 261 |
| [admin/gem\_recipes\_enhanced.css](/admin/gem_recipes_enhanced.css) | PostCSS | 489 | 19 | 88 | 596 |
| [admin/index.php](/admin/index.php) | PHP | 71 | 1 | 6 | 78 |
| [admin/install\_groups.php](/admin/install_groups.php) | PHP | 226 | 24 | 27 | 277 |
| [admin/items.css](/admin/items.css) | PostCSS | 383 | 8 | 60 | 451 |
| [admin/items.js](/admin/items.js) | JavaScript | 475 | 130 | 78 | 683 |
| [admin/items.php](/admin/items.php) | PHP | 178 | 15 | 24 | 217 |
| [admin/layout\_footer.php](/admin/layout_footer.php) | PHP | 12 | 0 | 0 | 12 |
| [admin/layout\_header.php](/admin/layout_header.php) | PHP | 92 | 5 | 7 | 104 |
| [admin/logout.php](/admin/logout.php) | PHP | 13 | 2 | 4 | 19 |
| [admin/loot\_tables.js](/admin/loot_tables.js) | JavaScript | 28 | 1 | 3 | 32 |
| [admin/loot\_tables.php](/admin/loot_tables.php) | PHP | 336 | 9 | 45 | 390 |
| [admin/monsters.php](/admin/monsters.php) | PHP | 1,045 | 51 | 148 | 1,244 |
| [admin/npc\_dialogues.php](/admin/npc_dialogues.php) | PHP | 735 | 55 | 99 | 889 |
| [admin/npcs.php](/admin/npcs.php) | PHP | 1,376 | 90 | 190 | 1,656 |
| [admin/pinyin.js](/admin/pinyin.js) | JavaScript | 2 | 1 | 1 | 4 |
| [admin/player\_inventory.css](/admin/player_inventory.css) | PostCSS | 460 | 17 | 82 | 559 |
| [admin/player\_inventory.js](/admin/player_inventory.js) | JavaScript | 556 | 60 | 125 | 741 |
| [admin/player\_inventory.php](/admin/player_inventory.php) | PHP | 379 | 4 | 24 | 407 |
| [admin/pvp\_battle\_logs.css](/admin/pvp_battle_logs.css) | PostCSS | 303 | 8 | 54 | 365 |
| [admin/pvp\_battle\_logs.js](/admin/pvp_battle_logs.js) | JavaScript | 236 | 20 | 36 | 292 |
| [admin/pvp\_battle\_logs.php](/admin/pvp_battle_logs.php) | PHP | 158 | 0 | 11 | 169 |
| [admin/quest\_editor.php](/admin/quest_editor.php) | PHP | 682 | 42 | 94 | 818 |
| [admin/quests.php](/admin/quests.php) | PHP | 753 | 31 | 94 | 878 |
| [admin/recipes.css](/admin/recipes.css) | PostCSS | 93 | 4 | 17 | 114 |
| [admin/recipes.js](/admin/recipes.js) | JavaScript | 233 | 17 | 42 | 292 |
| [admin/recipes.php](/admin/recipes.php) | PHP | 242 | 21 | 35 | 298 |
| [admin/refine\_attribute\_bonuses.php](/admin/refine_attribute_bonuses.php) | PHP | 211 | 7 | 21 | 239 |
| [admin/refine\_combos.php](/admin/refine_combos.php) | PHP | 205 | 8 | 24 | 237 |
| [admin/refine\_elements.php](/admin/refine_elements.php) | PHP | 158 | 7 | 18 | 183 |
| [admin/refine\_scores.php](/admin/refine_scores.php) | PHP | 169 | 7 | 19 | 195 |
| [admin/refine\_tiers.php](/admin/refine_tiers.php) | PHP | 449 | 30 | 71 | 550 |
| [admin/scene\_npcs.php](/admin/scene_npcs.php) | PHP | 256 | 23 | 37 | 316 |
| [admin/scenes.css](/admin/scenes.css) | PostCSS | 756 | 29 | 94 | 879 |
| [admin/scenes.js](/admin/scenes.js) | JavaScript | 1,120 | 110 | 220 | 1,450 |
| [admin/scenes.php](/admin/scenes.php) | PHP | 250 | 0 | 19 | 269 |
| [admin/scenes\_bak.js](/admin/scenes_bak.js) | JavaScript | 740 | 56 | 148 | 944 |
| [admin/security\_violations.css](/admin/security_violations.css) | PostCSS | 333 | 30 | 68 | 431 |
| [admin/security\_violations.php](/admin/security_violations.php) | PHP | 290 | 14 | 27 | 331 |
| [admin/shop\_config\_modal.php](/admin/shop_config_modal.php) | PHP | 38 | 0 | 5 | 43 |
| [admin/skills.php](/admin/skills.php) | PHP | 779 | 30 | 94 | 903 |
| [admin/style.css](/admin/style.css) | PostCSS | 809 | 17 | 82 | 908 |
| [api/get\_players.php](/api/get_players.php) | PHP | 14 | 1 | 4 | 19 |
| [api/security\_violation.php](/api/security_violation.php) | PHP | 90 | 15 | 20 | 125 |
| [classes/BattleSystem.php](/classes/BattleSystem.php) | PHP | 1,021 | 263 | 234 | 1,518 |
| [classes/ChatHandler.php](/classes/ChatHandler.php) | PHP | 344 | 98 | 87 | 529 |
| [classes/CraftingManager.php](/classes/CraftingManager.php) | PHP | 336 | 110 | 59 | 505 |
| [classes/DialogueScriptProcessor.php](/classes/DialogueScriptProcessor.php) | PHP | 425 | 86 | 91 | 602 |
| [classes/GemCraftingManager.php](/classes/GemCraftingManager.php) | PHP | 388 | 117 | 69 | 574 |
| [classes/LootManager.php](/classes/LootManager.php) | PHP | 233 | 78 | 59 | 370 |
| [classes/MessageProtocol.php](/classes/MessageProtocol.php) | PHP | 166 | 24 | 14 | 204 |
| [classes/NPCManager.php](/classes/NPCManager.php) | PHP | 329 | 124 | 84 | 537 |
| [classes/PvpBattleSystem.php](/classes/PvpBattleSystem.php) | PHP | 1,093 | 243 | 223 | 1,559 |
| [classes/QuestManager.php](/classes/QuestManager.php) | PHP | 976 | 227 | 203 | 1,406 |
| [classes/RefineCalculator.php](/classes/RefineCalculator.php) | PHP | 167 | 34 | 46 | 247 |
| [classes/SceneManager.php](/classes/SceneManager.php) | PHP | 205 | 11 | 37 | 253 |
| [classes/SecureMessageProtocol.php](/classes/SecureMessageProtocol.php) | PHP | 34 | 13 | 13 | 60 |
| [composer.json](/composer.json) | JSON | 5 | 0 | 1 | 6 |
| [composer.lock](/composer.lock) | JSON | 82 | 0 | 1 | 83 |
| [config.ini](/config.ini) | Ini | 11 | 8 | 8 | 27 |
| [config/ConnectionPool.php](/config/ConnectionPool.php) | PHP | 91 | 6 | 18 | 115 |
| [config/Database.php](/config/Database.php) | PHP | 74 | 14 | 13 | 101 |
| [config/RedisManager.php](/config/RedisManager.php) | PHP | 56 | 19 | 10 | 85 |
| [config/config.php](/config/config.php) | PHP | 8 | 13 | 9 | 30 |
| [config/formulas.php](/config/formulas.php) | PHP | 464 | 248 | 140 | 852 |
| [css/c.css](/css/c.css) | PostCSS | 24 | 3 | 1 | 28 |
| [css/g.css](/css/g.css) | PostCSS | 1,096 | 26 | 158 | 1,280 |
| [css/n.css](/css/n.css) | PostCSS | 302 | 8 | 52 | 362 |
| [css/p.css](/css/p.css) | PostCSS | 802 | 20 | 132 | 954 |
| [css/q.css](/css/q.css) | PostCSS | 340 | 13 | 59 | 412 |
| [css/r.css](/css/r.css) | PostCSS | 59 | 0 | 12 | 71 |
| [database/deploy\_gem\_crafting.sql](/database/deploy_gem_crafting.sql) | MS SQL | 72 | 27 | 20 | 119 |
| [database/game\_battle.sql](/database/game_battle.sql) | MS SQL | 839 | 580 | 279 | 1,698 |
| [database/gem\_crafting\_tables.sql](/database/gem_crafting_tables.sql) | MS SQL | 64 | 13 | 9 | 86 |
| [database/security\_tables.sql](/database/security_tables.sql) | MS SQL | 55 | 5 | 9 | 69 |
| [game.html](/game.html) | HTML | 23 | 0 | 1 | 24 |
| [index.html](/index.html) | HTML | 55 | 0 | 3 | 58 |
| [js/MessageProtocol.js](/js/MessageProtocol.js) | JavaScript | 130 | 9 | 8 | 147 |
| [js/SecureMessageProtocol.js](/js/SecureMessageProtocol.js) | JavaScript | 103 | 11 | 19 | 133 |
| [js/UnencryptedMessageProtocol.js](/js/UnencryptedMessageProtocol.js) | JavaScript | 24 | 0 | 0 | 24 |
| [js/anti-debug-gentle.js](/js/anti-debug-gentle.js) | JavaScript | 231 | 76 | 57 | 364 |
| [js/building-manager.js](/js/building-manager.js) | JavaScript | 2,434 | 574 | 443 | 3,451 |
| [js/crypto-js.min.js](/js/crypto-js.min.js) | JavaScript | 1 | 0 | 0 | 1 |
| [js/debug-loader.php](/js/debug-loader.php) | PHP | 155 | 21 | 39 | 215 |
| [js/debug.js](/js/debug.js) | JavaScript | 2 | 1 | 1 | 4 |
| [js/gameclient.js](/js/gameclient.js) | JavaScript | 4,515 | 651 | 834 | 6,000 |
| [js/html.js](/js/html.js) | JavaScript | 469 | 29 | 25 | 523 |
| [js/js.php](/js/js.php) | PHP | 169 | 35 | 37 | 241 |
| [js/npc-manager.js](/js/npc-manager.js) | JavaScript | 567 | 228 | 136 | 931 |
| [js/pvp-manager.js](/js/pvp-manager.js) | JavaScript | 1,062 | 381 | 260 | 1,703 |
| [js/quest-manager.js](/js/quest-manager.js) | JavaScript | 716 | 242 | 165 | 1,123 |
| [js/secure-config.php](/js/secure-config.php) | PHP | 140 | 73 | 55 | 268 |
| [js/skill-manager.js](/js/skill-manager.js) | JavaScript | 301 | 102 | 83 | 486 |
| [js/updatejs.php](/js/updatejs.php) | PHP | 354 | 54 | 61 | 469 |
| [server/cache\_control.php](/server/cache_control.php) | PHP | 68 | 31 | 15 | 114 |
| [server/crafting\_handlers.php](/server/crafting_handlers.php) | PHP | 30 | 28 | 13 | 71 |
| [server/gem\_crafting\_handlers.php](/server/gem_crafting_handlers.php) | PHP | 31 | 28 | 14 | 73 |
| [server/improved\_websocket\_server.php](/server/improved_websocket_server.php) | PHP | 4,843 | 881 | 1,018 | 6,742 |
| [server/pvp\_handler.php](/server/pvp_handler.php) | PHP | 1,170 | 423 | 312 | 1,905 |
| [server/refine\_handlers.php](/server/refine_handlers.php) | PHP | 357 | 101 | 87 | 545 |
| [server/websocket\_server.php](/server/websocket_server.php) | PHP | 234 | 19 | 61 | 314 |
| [start\_game.sh](/start_game.sh) | Shell Script | 22 | 7 | 8 | 37 |
| [vendor/autoload.php](/vendor/autoload.php) | PHP | 20 | 1 | 5 | 26 |
| [vendor/composer/ClassLoader.php](/vendor/composer/ClassLoader.php) | PHP | 286 | 235 | 59 | 580 |
| [vendor/composer/InstalledVersions.php](/vendor/composer/InstalledVersions.php) | PHP | 178 | 133 | 49 | 360 |
| [vendor/composer/autoload\_classmap.php](/vendor/composer/autoload_classmap.php) | PHP | 6 | 1 | 4 | 11 |
| [vendor/composer/autoload\_namespaces.php](/vendor/composer/autoload_namespaces.php) | PHP | 5 | 1 | 4 | 10 |
| [vendor/composer/autoload\_psr4.php](/vendor/composer/autoload_psr4.php) | PHP | 6 | 1 | 4 | 11 |
| [vendor/composer/autoload\_real.php](/vendor/composer/autoload_real.php) | PHP | 25 | 4 | 10 | 39 |
| [vendor/composer/autoload\_static.php](/vendor/composer/autoload_static.php) | PHP | 28 | 1 | 8 | 37 |
| [vendor/composer/installed.json](/vendor/composer/installed.json) | JSON | 72 | 0 | 1 | 73 |
| [vendor/composer/installed.php](/vendor/composer/installed.php) | PHP | 32 | 0 | 1 | 33 |
| [vendor/composer/platform\_check.php](/vendor/composer/platform_check.php) | PHP | 21 | 1 | 5 | 27 |
| [vendor/workerman/workerman/.github/FUNDING.yml](/vendor/workerman/workerman/.github/FUNDING.yml) | YAML | 2 | 1 | 2 | 5 |
| [vendor/workerman/workerman/Autoloader.php](/vendor/workerman/workerman/Autoloader.php) | PHP | 32 | 32 | 5 | 69 |
| [vendor/workerman/workerman/Connection/AsyncTcpConnection.php](/vendor/workerman/workerman/Connection/AsyncTcpConnection.php) | PHP | 222 | 127 | 30 | 379 |
| [vendor/workerman/workerman/Connection/AsyncUdpConnection.php](/vendor/workerman/workerman/Connection/AsyncUdpConnection.php) | PHP | 115 | 71 | 18 | 204 |
| [vendor/workerman/workerman/Connection/ConnectionInterface.php](/vendor/workerman/workerman/Connection/ConnectionInterface.php) | PHP | 25 | 87 | 15 | 127 |
| [vendor/workerman/workerman/Connection/TcpConnection.php](/vendor/workerman/workerman/Connection/TcpConnection.php) | PHP | 517 | 380 | 87 | 984 |
| [vendor/workerman/workerman/Connection/UdpConnection.php](/vendor/workerman/workerman/Connection/UdpConnection.php) | PHP | 91 | 101 | 17 | 209 |
| [vendor/workerman/workerman/Events/Ev.php](/vendor/workerman/workerman/Events/Ev.php) | PHP | 108 | 68 | 14 | 190 |
| [vendor/workerman/workerman/Events/Event.php](/vendor/workerman/workerman/Events/Event.php) | PHP | 121 | 69 | 26 | 216 |
| [vendor/workerman/workerman/Events/EventInterface.php](/vendor/workerman/workerman/Events/EventInterface.php) | PHP | 17 | 78 | 13 | 108 |
| [vendor/workerman/workerman/Events/Libevent.php](/vendor/workerman/workerman/Events/Libevent.php) | PHP | 132 | 69 | 25 | 226 |
| [vendor/workerman/workerman/Events/React/Base.php](/vendor/workerman/workerman/Events/React/Base.php) | PHP | 131 | 106 | 28 | 265 |
| [vendor/workerman/workerman/Events/React/ExtEventLoop.php](/vendor/workerman/workerman/Events/React/ExtEventLoop.php) | PHP | 9 | 16 | 3 | 28 |
| [vendor/workerman/workerman/Events/React/ExtLibEventLoop.php](/vendor/workerman/workerman/Events/React/ExtLibEventLoop.php) | PHP | 10 | 16 | 2 | 28 |
| [vendor/workerman/workerman/Events/React/StreamSelectLoop.php](/vendor/workerman/workerman/Events/React/StreamSelectLoop.php) | PHP | 9 | 16 | 2 | 27 |
| [vendor/workerman/workerman/Events/Select.php](/vendor/workerman/workerman/Events/Select.php) | PHP | 218 | 107 | 33 | 358 |
| [vendor/workerman/workerman/Events/Swoole.php](/vendor/workerman/workerman/Events/Swoole.php) | PHP | 205 | 60 | 19 | 284 |
| [vendor/workerman/workerman/Events/Uv.php](/vendor/workerman/workerman/Events/Uv.php) | PHP | 148 | 90 | 23 | 261 |
| [vendor/workerman/workerman/Lib/Constants.php](/vendor/workerman/workerman/Lib/Constants.php) | PHP | 21 | 18 | 6 | 45 |
| [vendor/workerman/workerman/Lib/Timer.php](/vendor/workerman/workerman/Lib/Timer.php) | PHP | 3 | 18 | 1 | 22 |
| [vendor/workerman/workerman/Protocols/Frame.php](/vendor/workerman/workerman/Protocols/Frame.php) | PHP | 23 | 34 | 5 | 62 |
| [vendor/workerman/workerman/Protocols/Http.php](/vendor/workerman/workerman/Protocols/Http.php) | PHP | 212 | 89 | 23 | 324 |
| [vendor/workerman/workerman/Protocols/Http/Chunk.php](/vendor/workerman/workerman/Protocols/Http/Chunk.php) | PHP | 14 | 30 | 4 | 48 |
| [vendor/workerman/workerman/Protocols/Http/Request.php](/vendor/workerman/workerman/Protocols/Http/Request.php) | PHP | 412 | 237 | 46 | 695 |
| [vendor/workerman/workerman/Protocols/Http/Response.php](/vendor/workerman/workerman/Protocols/Http/Response.php) | PHP | 244 | 176 | 39 | 459 |
| [vendor/workerman/workerman/Protocols/Http/ServerSentEvents.php](/vendor/workerman/workerman/Protocols/Http/ServerSentEvents.php) | PHP | 31 | 30 | 3 | 64 |
| [vendor/workerman/workerman/Protocols/Http/Session.php](/vendor/workerman/workerman/Protocols/Http/Session.php) | PHP | 188 | 226 | 48 | 462 |
| [vendor/workerman/workerman/Protocols/Http/Session/FileSessionHandler.php](/vendor/workerman/workerman/Protocols/Http/Session/FileSessionHandler.php) | PHP | 92 | 76 | 15 | 183 |
| [vendor/workerman/workerman/Protocols/Http/Session/RedisClusterSessionHandler.php](/vendor/workerman/workerman/Protocols/Http/Session/RedisClusterSessionHandler.php) | PHP | 26 | 15 | 6 | 47 |
| [vendor/workerman/workerman/Protocols/Http/Session/RedisSessionHandler.php](/vendor/workerman/workerman/Protocols/Http/Session/RedisSessionHandler.php) | PHP | 80 | 55 | 20 | 155 |
| [vendor/workerman/workerman/Protocols/Http/Session/SessionHandlerInterface.php](/vendor/workerman/workerman/Protocols/Http/Session/SessionHandlerInterface.php) | PHP | 12 | 93 | 10 | 115 |
| [vendor/workerman/workerman/Protocols/ProtocolInterface.php](/vendor/workerman/workerman/Protocols/ProtocolInterface.php) | PHP | 9 | 39 | 5 | 53 |
| [vendor/workerman/workerman/Protocols/Text.php](/vendor/workerman/workerman/Protocols/Text.php) | PHP | 26 | 40 | 4 | 70 |
| [vendor/workerman/workerman/Protocols/Websocket.php](/vendor/workerman/workerman/Protocols/Websocket.php) | PHP | 395 | 127 | 41 | 563 |
| [vendor/workerman/workerman/Protocols/Ws.php](/vendor/workerman/workerman/Protocols/Ws.php) | PHP | 311 | 94 | 28 | 433 |
| [vendor/workerman/workerman/README.md](/vendor/workerman/workerman/README.md) | Markdown | 255 | 0 | 88 | 343 |
| [vendor/workerman/workerman/Timer.php](/vendor/workerman/workerman/Timer.php) | PHP | 112 | 85 | 24 | 221 |
| [vendor/workerman/workerman/Worker.php](/vendor/workerman/workerman/Worker.php) | PHP | 1,643 | 871 | 244 | 2,758 |
| [vendor/workerman/workerman/composer.json](/vendor/workerman/workerman/composer.json) | JSON | 38 | 0 | 1 | 39 |
| [游戏世界NPC设计.md](/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8CNPC%E8%AE%BE%E8%AE%A1.md) | Markdown | 434 | 0 | 48 | 482 |
| [游戏世界任务设计 - 副本.md](/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E4%BB%BB%E5%8A%A1%E8%AE%BE%E8%AE%A1%20-%20%E5%89%AF%E6%9C%AC.md) | Markdown | 1,815 | 0 | 186 | 2,001 |
| [游戏世界任务设计.md](/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E4%BB%BB%E5%8A%A1%E8%AE%BE%E8%AE%A1.md) | Markdown | 424 | 0 | 58 | 482 |
| [游戏世界地图设计.md](/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E5%9C%B0%E5%9B%BE%E8%AE%BE%E8%AE%A1.md) | Markdown | 438 | 0 | 97 | 535 |
| [游戏任务系统实现设计.md](/%E6%B8%B8%E6%88%8F%E4%BB%BB%E5%8A%A1%E7%B3%BB%E7%BB%9F%E5%AE%9E%E7%8E%B0%E8%AE%BE%E8%AE%A1.md) | Markdown | 371 | 0 | 63 | 434 |

[Summary](results.md) / Details / [Diff Summary](diff.md) / [Diff Details](diff-details.md)