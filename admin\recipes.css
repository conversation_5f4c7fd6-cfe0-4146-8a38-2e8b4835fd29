/* recipes.css */

/* --- Main Content --- */
.main-content-area {
    padding: 20px;
}

.search-and-actions {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 20px;
}

.search-input {
    flex-grow: 1;
}

.table-container {
    overflow-x: auto;
}

.pagination {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
    gap: 5px;
}

/* --- Modal Styles --- */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 600px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #888;
}

#recipe-form fieldset {
    border: 1px solid #ddd;
    padding: 10px;
    margin-top: 20px;
    border-radius: 4px;
}

#recipe-form legend {
    padding: 0 5px;
    font-weight: bold;
    color: #555;
}

.material-row {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
}

.material-row .material-select {
    flex-grow: 1;
}

.material-row input[type="number"] {
    width: 80px;
    flex-shrink: 0;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #eee;
    text-align: right;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Override Select2 width inside modal */
.select2-container {
    width: 100% !important;
} 