<?php
/**
 * 调试脚本 - 帮助诊断文件加载问题
 */

echo "<h2>🔍 安全JS加载器调试信息</h2>";

// 1. 检查配置文件
$configFile = __DIR__ . '/secure-config.php';
echo "<h3>📋 配置文件检查</h3>";
echo "配置文件路径: " . $configFile . "<br>";
echo "文件存在: " . (file_exists($configFile) ? '✅ 是' : '❌ 否') . "<br>";

if (file_exists($configFile)) {
    $config = include $configFile;
    echo "配置加载: ✅ 成功<br>";
    
    // 显示文件映射
    echo "<h4>🗂️ 文件映射字典</h4>";
    $fileMapping = $config['content']['file_mapping'] ?? [];
    if (empty($fileMapping)) {
        echo "❌ 文件映射为空<br>";
    } else {
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>代码</th><th>文件名</th><th>文件存在</th><th>在白名单</th><th>有权限</th></tr>";
        
        $allowedFiles = $config['content']['allowed_files'] ?? [];
        $filePermissions = $config['content']['file_permissions'] ?? [];
        
        foreach ($fileMapping as $code => $filename) {
            $filePath = __DIR__ . '/' . $filename;
            $exists = file_exists($filePath) ? '✅' : '❌';
            $inWhitelist = in_array($filename, $allowedFiles) ? '✅' : '❌';
            $hasPermission = (isset($filePermissions[$filename]) && in_array('read', $filePermissions[$filename])) ? '✅' : '❌';
            
            echo "<tr>";
            echo "<td>{$code}</td>";
            echo "<td>{$filename}</td>";
            echo "<td>{$exists}</td>";
            echo "<td>{$inWhitelist}</td>";
            echo "<td>{$hasPermission}</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
    
    // 显示白名单
    echo "<h4>📝 文件白名单</h4>";
    $allowedFiles = $config['content']['allowed_files'] ?? [];
    if (empty($allowedFiles)) {
        echo "❌ 白名单为空<br>";
    } else {
        foreach ($allowedFiles as $filename) {
            $filePath = __DIR__ . '/' . $filename;
            $exists = file_exists($filePath) ? '✅' : '❌';
            echo "{$exists} {$filename}<br>";
        }
    }
    
} else {
    echo "❌ 配置文件不存在<br>";
}

// 2. 测试文件访问
echo "<h3>🧪 文件访问测试</h3>";

$testCodes = ['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l'];

foreach ($testCodes as $code) {
    echo "<h4>测试代码: {$code}</h4>";
    
    // 模拟请求
    $_GET['f'] = $code;
    
    try {
        // 创建加载器实例进行测试
        ob_start();
        
        // 简化的测试类
        class TestLoader {
            private $config;
            
            public function __construct() {
                $configFile = __DIR__ . '/secure-config.php';
                if (file_exists($configFile)) {
                    $this->config = include $configFile;
                } else {
                    throw new Exception('配置文件不存在');
                }
            }
            
            public function testValidation($fileCode) {
                // 1. 检查文件代码格式
                if (!preg_match('/^[a-z]$/', $fileCode)) {
                    return ['success' => false, 'error' => '文件代码格式无效'];
                }
                
                // 2. 检查文件映射
                $fileMapping = $this->config['content']['file_mapping'] ?? [];
                if (!isset($fileMapping[$fileCode])) {
                    return ['success' => false, 'error' => '文件代码不在映射中'];
                }
                
                $realFileName = $fileMapping[$fileCode];
                
                // 3. 检查白名单
                $allowedFiles = $this->config['content']['allowed_files'] ?? [];
                if (!in_array($realFileName, $allowedFiles)) {
                    return ['success' => false, 'error' => '文件不在白名单中'];
                }
                
                // 4. 检查权限
                $filePermissions = $this->config['content']['file_permissions'] ?? [];
                if (!isset($filePermissions[$realFileName]) || 
                    !in_array('read', $filePermissions[$realFileName])) {
                    return ['success' => false, 'error' => '文件没有读取权限'];
                }
                
                // 5. 检查文件存在
                $fullPath = __DIR__ . '/' . $realFileName;
                if (!file_exists($fullPath)) {
                    return ['success' => false, 'error' => '文件不存在: ' . $fullPath];
                }
                
                // 6. 检查扩展名
                $allowedExtensions = ['js', 'json', 'txt'];
                $extension = strtolower(pathinfo($realFileName, PATHINFO_EXTENSION));
                if (!in_array($extension, $allowedExtensions)) {
                    return ['success' => false, 'error' => '文件扩展名不允许: ' . $extension];
                }
                
                return ['success' => true, 'filename' => $realFileName, 'path' => $fullPath];
            }
        }
        
        $testLoader = new TestLoader();
        $result = $testLoader->testValidation($code);
        
        if ($result['success']) {
            echo "✅ 验证通过<br>";
            echo "文件名: {$result['filename']}<br>";
            echo "路径: {$result['path']}<br>";
            
            // 检查文件大小
            $fileSize = filesize($result['path']);
            echo "文件大小: " . number_format($fileSize) . " 字节<br>";
            
            // 显示文件前几行
            $content = file_get_contents($result['path']);
            $lines = explode("\n", $content);
            $preview = array_slice($lines, 0, 3);
            echo "文件预览:<br>";
            echo "<pre style='background: #f5f5f5; padding: 10px; max-width: 500px; overflow: hidden;'>";
            echo htmlspecialchars(implode("\n", $preview));
            if (count($lines) > 3) {
                echo "\n... (还有 " . (count($lines) - 3) . " 行)";
            }
            echo "</pre>";
            
        } else {
            echo "❌ 验证失败: {$result['error']}<br>";
        }
        
        ob_end_clean();
        
    } catch (Exception $e) {
        ob_end_clean();
        echo "❌ 测试异常: " . $e->getMessage() . "<br>";
    }
    
    echo "<hr>";
}

// 3. 生成测试链接
echo "<h3>🔗 测试链接</h3>";
$baseUrl = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/js.php';

foreach ($testCodes as $code) {
    $testUrl = $baseUrl . '?f=' . $code;
    echo "<a href='{$testUrl}' target='_blank'>{$code} - {$testUrl}</a><br>";
}

// 4. 环境信息
echo "<h3>🌍 环境信息</h3>";
echo "PHP版本: " . PHP_VERSION . "<br>";
echo "当前目录: " . __DIR__ . "<br>";
echo "HTTP_HOST: " . ($_SERVER['HTTP_HOST'] ?? 'N/A') . "<br>";
echo "REQUEST_URI: " . ($_SERVER['REQUEST_URI'] ?? 'N/A') . "<br>";
echo "HTTP_REFERER: " . ($_SERVER['HTTP_REFERER'] ?? 'N/A') . "<br>";
echo "HTTP_USER_AGENT: " . ($_SERVER['HTTP_USER_AGENT'] ?? 'N/A') . "<br>";

// 5. 最近的错误日志
echo "<h3>📋 最近的错误日志</h3>";
$errorLog = ini_get('error_log');
if ($errorLog && file_exists($errorLog)) {
    $logContent = file_get_contents($errorLog);
    $logLines = explode("\n", $logContent);
    $recentLines = array_slice($logLines, -20); // 最近20行
    
    echo "<pre style='background: #f5f5f5; padding: 10px; max-height: 300px; overflow-y: auto;'>";
    foreach ($recentLines as $line) {
        if (strpos($line, 'SecureJS') !== false) {
            echo "<strong style='color: red;'>" . htmlspecialchars($line) . "</strong>\n";
        } else {
            echo htmlspecialchars($line) . "\n";
        }
    }
    echo "</pre>";
} else {
    echo "无法访问错误日志文件<br>";
}

echo "<p><strong>💡 提示:</strong> 如果某些文件验证失败，请检查文件是否存在、配置是否正确。</p>";
?>
