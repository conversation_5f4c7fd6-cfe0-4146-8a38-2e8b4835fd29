<?php
// server/refine_handlers.php

/**
 * 凝练系统服务器处理函数
 * 用于处理与凝练相关的WebSocket消息
 */

// 凝练值属性加成系数
define('REFINE_VALUE_BONUS_FACTOR', 0.05); // 每点凝练值增加5%的属性加成

/**
 * 处理获取凝练材料请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleGetRefineMaterials($fd, $data, $server) {
    if (!isset($data['scene_building_id']) || !isset($data['player_id'])) {
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整',
            'context' => 'get_refine_materials_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $sceneBuildingId = $data['scene_building_id'];
    
    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();
    
    // 只获取具有元素属性的凝练材料
    $materialsStmt = $conn->prepare(
        "SELECT 
            pi.id as inventory_id, pi.item_template_id, pi.quantity, pi.instance_data,
            it.name, it.category, it.description,
            me.element, me.tier as element_tier
         FROM player_inventory pi
         JOIN item_templates it ON pi.item_template_id = it.id
         JOIN material_elements me ON it.id = me.item_template_id
         WHERE pi.player_id = ? AND pi.is_equipped = 0 AND it.category = 'Material'
         ORDER BY me.element, it.name ASC" // 按元素属性和名称排序
    );
    $materialsStmt->execute([$playerId]);
    $materials = $materialsStmt->fetchAll(PDO::FETCH_ASSOC);
    $materialsStmt->closeCursor();
    
    // 获取玩家的金币
    $goldStmt = $conn->prepare("SELECT gold FROM player_attributes WHERE account_id = ?");
    $goldStmt->execute([$playerId]);
    $goldResult = $goldStmt->fetch(PDO::FETCH_ASSOC);
    $goldStmt->closeCursor();
    $playerGold = $goldResult ? (int)$goldResult['gold'] : 0;
    
    // 获取玩家可凝练的装备
    $equipmentStmt = $conn->prepare(
        "SELECT 
            pi.id as inventory_id, pi.item_template_id, pi.quantity, pi.instance_data,
            it.name, it.category, it.description
         FROM player_inventory pi
         JOIN item_templates it ON pi.item_template_id = it.id
         WHERE pi.player_id = ? AND pi.is_equipped = 0 AND it.category = 'Equipment'
         ORDER BY it.name ASC"
    );
    $equipmentStmt->execute([$playerId]);
    $equipment = $equipmentStmt->fetchAll(PDO::FETCH_ASSOC);
    $equipmentStmt->closeCursor();
    
    // 返回凝练材料数据
    return [MessageProtocol::S2C_REFINE_MATERIALS_DATA, [
        'materials' => $materials,
        'equipment' => $equipment,
        'player_gold' => $playerGold
    ]];
}

/**
 * 处理凝练费用预估请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleGetRefineCostEstimate($fd, $data, $server) {
    if (!isset($data['player_id']) || !isset($data['equipment_id']) || !isset($data['materials']) || count($data['materials']) !== 5) {
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整或材料数量不正确',
            'context' => 'refine_cost_estimate_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $equipmentId = $data['equipment_id'];
    $materials = $data['materials'];

    $conn = Database::getInstance()->getConnection();

    try {
        // 1. 验证装备是否存在且属于玩家
        $equipmentStmt = $conn->prepare(
            "SELECT pi.*, it.name, it.category
             FROM player_inventory pi
             JOIN item_templates it ON pi.item_template_id = it.id
             WHERE pi.id = ? AND pi.player_id = ? AND pi.is_equipped = 0 AND it.category = 'Equipment'"
        );
        $equipmentStmt->execute([$equipmentId, $playerId]);
        $equipment = $equipmentStmt->fetch(PDO::FETCH_ASSOC);
        $equipmentStmt->closeCursor();

        if (!$equipment) {
            return [MessageProtocol::S2C_ERROR, [
                'message' => '装备不存在或不可凝练',
                'context' => 'refine_cost_estimate_fail'
            ]];
        }

        // 2. 验证材料是否存在且属于玩家
        // 前端发送的是简单的材料ID数组，需要直接使用
        $materialIds = $materials;

        // 检查材料ID数组是否为空
        if (empty($materialIds)) {
            return [MessageProtocol::S2C_ERROR, [
                'message' => '材料列表为空',
                'context' => 'refine_cost_estimate_fail'
            ]];
        }

        // 去重获取唯一的材料ID
        $uniqueMaterialIds = array_unique($materialIds);

        $placeholders = implode(',', array_fill(0, count($uniqueMaterialIds), '?'));

        $materialStmt = $conn->prepare(
            "SELECT pi.id, pi.item_template_id, pi.quantity, me.element, me.tier as element_tier
             FROM player_inventory pi
             JOIN material_elements me ON pi.item_template_id = me.item_template_id
             WHERE pi.id IN ({$placeholders}) AND pi.player_id = ?"
        );
        $materialStmt->execute([...$uniqueMaterialIds, $playerId]);
        $availableMaterials = $materialStmt->fetchAll(PDO::FETCH_ASSOC);
        $materialStmt->closeCursor();

        if (count($availableMaterials) !== count($uniqueMaterialIds)) {
            return [MessageProtocol::S2C_ERROR, [
                'message' => '部分材料不存在或不可用',
                'context' => 'refine_cost_estimate_fail'
            ]];
        }

        // 3. 验证材料数量是否足够
        $materialMap = [];
        foreach ($availableMaterials as $material) {
            $materialMap[$material['id']] = $material;
        }

        // 统计每个材料ID的使用次数
        $materialUsage = array_count_values($materialIds);

        foreach ($materialUsage as $materialId => $requestedQuantity) {
            if (!isset($materialMap[$materialId]) || $materialMap[$materialId]['quantity'] < $requestedQuantity) {
                return [MessageProtocol::S2C_ERROR, [
                    'message' => '材料数量不足',
                    'context' => 'refine_cost_estimate_fail'
                ]];
            }
        }

        // 4. 使用RefineCalculator计算凝练结果（仅用于预估）
        $refineCalculator = new RefineCalculator(Database::getInstance());

        // 准备材料数据
        $materialData = [];
        foreach ($materialIds as $materialId) {
            $material = $materialMap[$materialId];
            $materialData[] = [
                'element' => $material['element'],
                'tier' => $material['element_tier']  // 修正字段名，RefineCalculator期望'tier'而不是'element_tier'
            ];
        }

        // 计算凝练结果
        $refineResult = $refineCalculator->calculateRefine($materialData, $equipment);

        // 5. 计算费用（与实际凝练相同的公式）
        $estimatedTier = $refineResult['tier'];
        $estimatedCost = $estimatedTier * 100;

        // 6. 返回预估结果
        return [MessageProtocol::S2C_REFINE_COST_ESTIMATE, [
            'success' => true,
            'estimated_cost' => $estimatedCost,
            'estimated_tier' => $estimatedTier,
            'estimated_tier_name' => $refineResult['prefix']['equipment'] ?? '未知',
            'estimated_rv' => $refineResult['final_rv']
        ]];

    } catch (Exception $e) {
        error_log("凝练费用预估错误: " . $e->getMessage());
        return [MessageProtocol::S2C_ERROR, [
            'message' => '费用预估失败: ' . $e->getMessage(),
            'context' => 'refine_cost_estimate_fail'
        ]];
    }
}

/**
 * 处理凝练装备请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleRefineItem($fd, $data, $server) {
    if (!isset($data['player_id']) || !isset($data['equipment_id']) || !isset($data['materials']) || count($data['materials']) !== 5) {
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整或材料数量不正确',
            'context' => 'refine_item_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $equipmentId = $data['equipment_id'];
    $materials = $data['materials'];
    
    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();
    
    try {
        // 开始事务
        $conn->beginTransaction();
        
        // 1. 验证玩家拥有该装备，并获取装备名称
        $equipStmt = $conn->prepare("
            SELECT pi.*, it.name, it.category
            FROM player_inventory pi
            JOIN item_templates it ON pi.item_template_id = it.id
            WHERE pi.id = ? AND pi.player_id = ?
        ");
        $equipStmt->execute([$equipmentId, $playerId]);
        $equipment = $equipStmt->fetch(PDO::FETCH_ASSOC);
        $equipStmt->closeCursor();
        
        if (!$equipment) {
            throw new Exception("装备不存在或不属于该玩家");
        }
        
        // 2. 验证玩家拥有所有指定的材料
        $materialData = [];
        foreach ($materials as $matId) {
            $matStmt = $conn->prepare("SELECT pi.*, it.name, it.category FROM player_inventory pi JOIN item_templates it ON pi.item_template_id = it.id WHERE pi.id = ? AND pi.player_id = ?");
            $matStmt->execute([$matId, $playerId]);
            $material = $matStmt->fetch(PDO::FETCH_ASSOC);
            $matStmt->closeCursor();
            
            if (!$material) {
                throw new Exception("材料不存在或不属于该玩家");
            }
            
            if ($material['category'] !== 'Material') {
                throw new Exception("只能使用材料类物品进行凝练");
            }
            
            if ($material['quantity'] < 1) {
                throw new Exception("材料数量不足");
            }
            
            // 获取材料的元素属性
            $elementStmt = $conn->prepare("SELECT element, tier FROM material_elements WHERE item_template_id = ?");
            $elementStmt->execute([$material['item_template_id']]);
            $elementData = $elementStmt->fetch(PDO::FETCH_ASSOC);
            $elementStmt->closeCursor();
            
            if (!$elementData) {
                throw new Exception("材料无元素属性，无法用于凝练");
            }
            
            $materialData[] = [
                'id' => $material['id'],
                'name' => $material['name'],
                'element' => $elementData['element'],
                'tier' => $elementData['tier']
            ];
        }
        
        // 3. 使用RefineCalculator计算凝练结果
        $refineCalculator = new RefineCalculator(Database::getInstance());
        $refineResult = $refineCalculator->calculateRefine($materialData, $equipment);
        
        // 4. 消耗材料
        foreach ($materials as $matId) {
            $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
            $updateStmt->execute([$matId]);
            
            // 如果数量为0，删除物品
            $deleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ? AND quantity <= 0");
            $deleteStmt->execute([$matId]);
        }
        
        // 5. 获取凝练等级的属性加成范围
        $tier = $refineResult['tier'];
        $bonusesStmt = $conn->prepare("SELECT attribute, min_bonus, max_bonus, bonus_type FROM refine_attribute_bonuses WHERE tier = ?");
        $bonusesStmt->execute([$tier]);
        $bonusRanges = $bonusesStmt->fetchAll(PDO::FETCH_ASSOC);
        $bonusesStmt->closeCursor();
        
        // 6. 随机生成具体的属性加成值
        $bonuses = [];
        
        // 如果没有找到属性加成范围数据，使用默认值
        if (empty($bonusRanges)) {
            // 记录警告
            error_log("Warning: No refine attribute bonuses found for tier {$tier}, using default values");
            
            // 默认属性加成范围（按照凝练等级递增）
            $defaultBonuses = [
                // 一级凝练默认属性
                1 => [
                    ['attribute' => 'attack', 'min_bonus' => 2, 'max_bonus' => 5, 'bonus_type' => 'flat'],
                    ['attribute' => 'defense', 'min_bonus' => 1, 'max_bonus' => 3, 'bonus_type' => 'flat'],
                    ['attribute' => 'max_hp', 'min_bonus' => 10, 'max_bonus' => 20, 'bonus_type' => 'flat'],
                ],
                // 二级凝练默认属性
                2 => [
                    ['attribute' => 'attack', 'min_bonus' => 5, 'max_bonus' => 10, 'bonus_type' => 'flat'],
                    ['attribute' => 'defense', 'min_bonus' => 3, 'max_bonus' => 6, 'bonus_type' => 'flat'],
                    ['attribute' => 'max_hp', 'min_bonus' => 20, 'max_bonus' => 40, 'bonus_type' => 'flat'],
                    ['attribute' => 'fire_damage', 'min_bonus' => 2, 'max_bonus' => 5, 'bonus_type' => 'flat'],
                ],
                // 三级凝练默认属性
                3 => [
                    ['attribute' => 'attack', 'min_bonus' => 10, 'max_bonus' => 15, 'bonus_type' => 'flat'],
                    ['attribute' => 'defense', 'min_bonus' => 6, 'max_bonus' => 9, 'bonus_type' => 'flat'],
                    ['attribute' => 'max_hp', 'min_bonus' => 40, 'max_bonus' => 60, 'bonus_type' => 'flat'],
                    ['attribute' => 'fire_damage', 'min_bonus' => 5, 'max_bonus' => 8, 'bonus_type' => 'flat'],
                    ['attribute' => 'ice_damage', 'min_bonus' => 5, 'max_bonus' => 8, 'bonus_type' => 'flat'],
                ],
            ];
            
            // 使用默认值（如果当前等级没有，则使用低一级的）
            $tierToUse = $tier;
            while ($tierToUse > 0 && !isset($defaultBonuses[$tierToUse])) {
                $tierToUse--;
            }
            
            if ($tierToUse > 0) {
                $bonusRanges = $defaultBonuses[$tierToUse];
            } else {
                // 最低默认值
                $bonusRanges = [
                    ['attribute' => 'attack', 'min_bonus' => 1, 'max_bonus' => 3, 'bonus_type' => 'flat'],
                    ['attribute' => 'max_hp', 'min_bonus' => 5, 'max_bonus' => 10, 'bonus_type' => 'flat'],
                ];
            }
        }
        
        // 确定属性项目数量
        // 基础数量：2项
        // 凝练等级影响：每增加一个凝练等级，至少增加1项属性
        // 凝练值影响：每2点凝练值有机会额外增加1项属性
        $minAttributes = max(2, $tier); // 确保至少有与凝练等级相同数量的属性
        
        // 凝练值影响：每4点凝练值可能额外增加1项属性（概率为凝练值的10%）
        $refineValue = $refineResult['final_rv'];
        $extraAttributeChance = min(100, $refineValue * 10); // 凝练值为10时，有100%的概率获得额外属性
        
        $extraAttributes = 0;
        // 每2点凝练值检查一次是否获得额外属性
        $checkTimes = floor($refineValue / 2);
        for ($i = 0; $i < $checkTimes; $i++) {
            if (mt_rand(1, 100) <= $extraAttributeChance) {
                $extraAttributes++;
            }
        }
        
        error_log("Refine value {$refineValue} gives {$extraAttributes} extra attributes with {$extraAttributeChance}% chance per check");
        
        // 最终属性数量 = 基础数量 + 额外属性数量，但不超过可用属性总数
        $maxPossibleAttributes = min(count($bonusRanges), $minAttributes + $extraAttributes);
        
        // 随机确定最终属性数量，但不低于基础数量
        // 修正：确保最小值不大于最大值
        if ($minAttributes > $maxPossibleAttributes) {
            $attributeCount = $maxPossibleAttributes;
            error_log("Warning: Required attributes ($minAttributes) exceed available attributes ($maxPossibleAttributes), using maximum available");
        } else {
            $attributeCount = mt_rand($minAttributes, $maxPossibleAttributes);
        }
        
        error_log("Refine tier {$tier}, value {$refineValue}: Selected {$attributeCount} attributes from ".count($bonusRanges)." available attributes (min: {$minAttributes}, max possible: {$maxPossibleAttributes})");
        
        // 随机选择属性
        // 先将属性按重要性排序，确保重要属性优先被选中
        $primaryAttributes = ['attack', 'defense', 'max_hp', 'max_mp', 'strength', 'agility', 'constitution', 'intelligence'];
        
        // 将属性分为主要属性和次要属性
        $primary = [];
        $secondary = [];
        
        foreach ($bonusRanges as $index => $range) {
            if (in_array($range['attribute'], $primaryAttributes)) {
                $primary[] = $range;
            } else {
                $secondary[] = $range;
            }
        }
        
        // 打乱主要属性和次要属性的顺序
        shuffle($primary);
        shuffle($secondary);
        
        // 合并属性，确保主要属性在前
        $shuffledRanges = array_merge($primary, $secondary);
        
        // 截取需要的属性数量
        $selectedRanges = array_slice($shuffledRanges, 0, $attributeCount);
        
        error_log("Selected attributes: " . json_encode(array_column($selectedRanges, 'attribute')));
        
        // 为选中的属性生成加成值
        $bonusTypes = []; // 保存每个属性的加成类型
        foreach ($selectedRanges as $range) {
            $attribute = $range['attribute'];
            $min = (float)$range['min_bonus'];
            $max = (float)$range['max_bonus'];
            $bonusType = $range['bonus_type']; // 获取加成类型

            // 随机生成属性加成值
            $bonusValue = mt_rand($min * 100, $max * 100) / 100;

            // 应用凝练值加成系数
            $bonusMultiplier = 1 + ($refineValue * REFINE_VALUE_BONUS_FACTOR);
            $bonusValue = $bonusValue * $bonusMultiplier;

            // 根据类型处理数值
            if ($bonusType === 'percentage') {
                // 百分比类型：保留小数，不四舍五入为整数
                $bonuses[$attribute] = round($bonusValue, 2);
            } else {
                // 固定值类型：四舍五入为整数
                $bonuses[$attribute] = round($bonusValue);
            }

            // 保存属性的加成类型
            $bonusTypes[$attribute] = $bonusType;

            // 记录属性加成计算过程
            error_log(sprintf(
                "Attribute bonus calculation for %s (%s): base value %.2f * multiplier %.2f (RV: %.2f) = final value %s",
                $attribute,
                $bonusType,
                $bonusValue / $bonusMultiplier,
                $bonusMultiplier,
                $refineValue,
                $bonuses[$attribute]
            ));
        }
        
        // 记录生成的属性加成，用于调试
        error_log("Generated refine bonuses for tier {$tier}: " . json_encode($bonuses));
        
        // 7. 更新装备的实例数据，添加凝练属性
        $instanceData = json_decode($equipment['instance_data'] ?: '{}', true);
        if (!is_array($instanceData)) {
            $instanceData = [];
        }
        
        // 获取装备前缀
        $prefix = $refineResult['prefix']['equipment'] ?? '未知';
        
        // 更新实例数据
        $instanceData['refined'] = true;
        $instanceData['refine_value'] = $refineResult['final_rv'];
        $instanceData['refine_tier'] = $refineResult['tier'];
        $instanceData['refine_prefix'] = $prefix;
        $instanceData['refine_timestamp'] = date('Y-m-d H:i:s');
        
        // 确保$bonuses不为空，否则使用默认加成
        if (empty($bonuses)) {
            error_log("Warning: Empty bonuses array, adding default bonus");
            $bonuses = ['attack' => 2, 'max_hp' => 10];
        }
        
        // 确保以关联数组的形式保存
        $instanceData['refine_bonuses'] = $bonuses;
        $instanceData['refine_bonus_types'] = $bonusTypes; // 保存每个属性的加成类型
        
        // 记录最终保存的实例数据
        error_log("Final instance data to save: " . json_encode($instanceData));
        
        // 获取原装备名称 (不包含任何前缀)
        $equipNameStmt = $conn->prepare("
            SELECT it.name 
            FROM player_inventory pi 
            JOIN item_templates it ON pi.item_template_id = it.id 
            WHERE pi.id = ?
        ");
        $equipNameStmt->execute([$equipmentId]);
        $equipNameResult = $equipNameStmt->fetch(PDO::FETCH_ASSOC);
        $equipNameStmt->closeCursor();
        
        $originalName = $equipNameResult ? $equipNameResult['name'] : '未知装备';
        
        // 添加装备名称前缀
        $equipmentName = $prefix . ' ' . $originalName;
        
        // 设置display_name，确保凝练前缀在所有界面都能显示
        // 检查是否有现有的display_name（保留宝石追加部分）
        if (isset($instanceData['display_name'])) {
            // 解析现有的display_name，提取宝石追加部分
            $existingDisplayName = $instanceData['display_name'];
            if (strpos($existingDisplayName, '§') !== false) {
                // 如果有宝石追加名称，将前缀添加到原始名称部分，保留宝石追加部分
                $gemSuffix = substr($existingDisplayName, strpos($existingDisplayName, '§'));
                $instanceData['display_name'] = $prefix . ' ' . $originalName . $gemSuffix;
            } else {
                // 如果没有宝石追加名称，直接使用新的装备名称
                $instanceData['display_name'] = $equipmentName;
            }
        } else {
            // 如果没有现有display_name，直接使用新的装备名称
            $instanceData['display_name'] = $equipmentName;
        }
        
        // 更新物品名称和实例数据
        $updateEquipStmt = $conn->prepare("UPDATE player_inventory SET instance_data = ? WHERE id = ?");
        $updateEquipStmt->execute([json_encode($instanceData), $equipmentId]);
        
        // 8. 扣除凝练费用 (按照凝练档位乘以100金币)
        $refineCost = $refineResult['tier'] * 100;
        $deductGoldStmt = $conn->prepare("UPDATE player_attributes SET gold = gold - ? WHERE account_id = ?");
        $deductGoldStmt->execute([$refineCost, $playerId]);
        
        // 提交事务
        $conn->commit();
        
        // 9. 返回凝练结果，包含属性加成
        return [MessageProtocol::S2C_REFINE_RESULT, [
            'success' => true,
            'equipment_id' => $equipmentId,
            'equipment_name' => $equipmentName,
            'refine_result' => $refineResult,
            'attribute_bonuses' => $bonuses,
            'attribute_count' => count($bonuses),
            'refine_bonus_multiplier' => 1 + ($refineResult['final_rv'] * REFINE_VALUE_BONUS_FACTOR),
            'cost' => $refineCost,
            'message' => "凝练成功! [{$originalName}] 已成为 [{$prefix}] 品质。" .
                        sprintf(" (凝练值 %.2f, 获得 %d 项属性)",
                        $refineResult['final_rv'],
                        count($bonuses))
        ]];
        
    } catch (Exception $e) {
        // 回滚事务
        $conn->rollBack();
        
        return [MessageProtocol::S2C_ERROR, [
            'message' => '凝练失败: ' . $e->getMessage(),
            'context' => 'refine_item_fail'
        ]];
    }
} 