<?php
// admin/announcements.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

require_once '../config/Database.php';

$pageTitle = '公告管理';
$currentPage = 'announcements';

// 处理POST请求
$message = '';
$messageType = '';

// 处理重定向后的成功消息
if (isset($_GET['success'])) {
    $message = $_GET['success'];
    $messageType = 'success';
}

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $action = $_POST['action'] ?? '';
    
    try {
        $pdo = Database::getInstance()->getConnection();
        
        switch ($action) {
            case 'create':
                $result = createAnnouncement($pdo, $_POST, $_FILES);
                if ($result['success']) {
                    // 成功创建后重定向到列表页面
                    header('Location: announcements.php?success=' . urlencode($result['message']));
                    exit;
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'update':
                $result = updateAnnouncement($pdo, $_POST, $_FILES);
                if ($result['success']) {
                    // 成功更新后重定向到列表页面
                    header('Location: announcements.php?success=' . urlencode($result['message']));
                    exit;
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'delete':
                $result = deleteAnnouncement($pdo, $_POST['id']);
                if ($result['success']) {
                    header('Location: announcements.php?success=' . urlencode($result['message']));
                    exit;
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;

            case 'toggle_status':
                $result = toggleAnnouncementStatus($pdo, $_POST['id']);
                if ($result['success']) {
                    header('Location: announcements.php?success=' . urlencode($result['message']));
                    exit;
                } else {
                    $message = $result['message'];
                    $messageType = 'error';
                }
                break;
        }
    } catch (Exception $e) {
        $message = '操作失败: ' . $e->getMessage();
        $messageType = 'error';
    }
}

// 获取公告列表
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 10;
$offset = ($page - 1) * $limit;

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取公告列表
    $stmt = $pdo->prepare("
        SELECT id, title, summary, is_active, is_important, view_count, created_at, updated_at, created_by
        FROM announcements
        ORDER BY is_important DESC, created_at DESC
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([$limit, $offset]);
    $announcements = $stmt->fetchAll();
    
    // 获取总数
    $countStmt = $pdo->query("SELECT COUNT(*) FROM announcements");
    $totalCount = $countStmt->fetchColumn();
    $totalPages = ceil($totalCount / $limit);
    
} catch (Exception $e) {
    $announcements = [];
    $totalCount = 0;
    $totalPages = 0;
    $message = '获取公告列表失败: ' . $e->getMessage();
    $messageType = 'error';
}

// 获取编辑的公告数据
$editAnnouncement = null;
if (isset($_GET['edit']) && is_numeric($_GET['edit'])) {
    try {
        $stmt = $pdo->prepare("SELECT * FROM announcements WHERE id = ?");
        $stmt->execute([$_GET['edit']]);
        $editAnnouncement = $stmt->fetch();
    } catch (Exception $e) {
        $message = '获取公告数据失败: ' . $e->getMessage();
        $messageType = 'error';
    }
}

require_once 'layout_header.php';

// 公告操作函数
function createAnnouncement($pdo, $postData, $files) {
    $title = trim($postData['title'] ?? '');
    $content = $postData['content'] ?? '';
    $summary = trim($postData['summary'] ?? '');
    $isImportant = isset($postData['is_important']) ? 1 : 0;
    $isActive = isset($postData['is_active']) ? 1 : 0;
    
    if (empty($title)) {
        return ['success' => false, 'message' => '标题不能为空'];
    }
    
    if (empty($content)) {
        return ['success' => false, 'message' => '内容不能为空'];
    }
    
    // 处理图片上传
    $content = processImageUploads($content, $files);
    
    // 安全过滤HTML内容
    $content = sanitizeHtmlContent($content);
    
    // 如果没有摘要，从内容中提取
    if (empty($summary)) {
        $summary = extractSummary($content);
    } else {
        // 确保手动输入的摘要不超过18个字
        if (mb_strlen($summary) > 18) {
            $summary = mb_substr($summary, 0, 18);
        }
    }
    
    $stmt = $pdo->prepare("
        INSERT INTO announcements (title, content, summary, is_active, is_important, created_by, updated_by) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    
    $adminUsername = $_SESSION['admin_username'] ?? 'admin';
    $result = $stmt->execute([$title, $content, $summary, $isActive, $isImportant, $adminUsername, $adminUsername]);
    
    if ($result) {
        return ['success' => true, 'message' => '公告创建成功'];
    } else {
        return ['success' => false, 'message' => '公告创建失败'];
    }
}

function updateAnnouncement($pdo, $postData, $files) {
    $id = intval($postData['id'] ?? 0);
    $title = trim($postData['title'] ?? '');
    $content = $postData['content'] ?? '';
    $summary = trim($postData['summary'] ?? '');
    $isImportant = isset($postData['is_important']) ? 1 : 0;
    $isActive = isset($postData['is_active']) ? 1 : 0;
    
    if ($id <= 0) {
        return ['success' => false, 'message' => '无效的公告ID'];
    }
    
    if (empty($title)) {
        return ['success' => false, 'message' => '标题不能为空'];
    }
    
    if (empty($content)) {
        return ['success' => false, 'message' => '内容不能为空'];
    }
    
    // 处理图片上传
    $content = processImageUploads($content, $files);
    
    // 安全过滤HTML内容
    $content = sanitizeHtmlContent($content);
    
    // 如果没有摘要，从内容中提取
    if (empty($summary)) {
        $summary = extractSummary($content);
    } else {
        // 确保手动输入的摘要不超过18个字
        if (mb_strlen($summary) > 18) {
            $summary = mb_substr($summary, 0, 18);
        }
    }
    
    $stmt = $pdo->prepare("
        UPDATE announcements 
        SET title = ?, content = ?, summary = ?, is_active = ?, is_important = ?, updated_at = NOW(), updated_by = ?
        WHERE id = ?
    ");
    
    $adminUsername = $_SESSION['admin_username'] ?? 'admin';
    $result = $stmt->execute([$title, $content, $summary, $isActive, $isImportant, $adminUsername, $id]);
    
    if ($result) {
        return ['success' => true, 'message' => '公告更新成功'];
    } else {
        return ['success' => false, 'message' => '公告更新失败'];
    }
}

function deleteAnnouncement($pdo, $id) {
    $id = intval($id);
    if ($id <= 0) {
        return ['success' => false, 'message' => '无效的公告ID'];
    }
    
    $stmt = $pdo->prepare("DELETE FROM announcements WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($result) {
        return ['success' => true, 'message' => '公告删除成功'];
    } else {
        return ['success' => false, 'message' => '公告删除失败'];
    }
}

function toggleAnnouncementStatus($pdo, $id) {
    $id = intval($id);
    if ($id <= 0) {
        return ['success' => false, 'message' => '无效的公告ID'];
    }
    
    $stmt = $pdo->prepare("UPDATE announcements SET is_active = 1 - is_active WHERE id = ?");
    $result = $stmt->execute([$id]);
    
    if ($result) {
        return ['success' => true, 'message' => '公告状态切换成功'];
    } else {
        return ['success' => false, 'message' => '公告状态切换失败'];
    }
}

function processImageUploads($content, $files) {
    // 图片上传通过AJAX处理，这里只需要返回内容
    // 实际的图片上传在upload_image.php中处理
    return $content;
}

function sanitizeHtmlContent($content) {
    // 基础HTML安全过滤
    // 移除危险标签和属性
    $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
    $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $content);
    $content = preg_replace('/javascript:/i', '', $content);
    $content = preg_replace('/on\w+\s*=/i', '', $content);
    
    return $content;
}

function extractSummary($content) {
    // 从HTML内容中提取纯文本摘要，限制在18个字以内
    $text = strip_tags($content);
    $text = preg_replace('/\s+/', '', $text); // 移除所有空白字符
    $text = trim($text);

    if (mb_strlen($text) > 18) {
        $text = mb_substr($text, 0, 18);
    }

    return $text;
}
?>

<div class="page-content">
    <?php if ($message): ?>
        <div class="alert alert-<?php echo $messageType; ?>" id="alertMessage">
            <?php echo htmlspecialchars($message); ?>
            <button type="button" class="alert-close" onclick="hideAlert()">&times;</button>
        </div>
    <?php endif; ?>
    
    <div class="action-buttons">
        <button class="btn btn-primary" onclick="showCreateForm()">新建公告</button>
        <a href="announcements.php" class="btn">刷新列表</a>
    </div>
    
    <!-- 公告列表 -->
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>标题</th>
                    <th>摘要</th>
                    <th>状态</th>
                    <th>重要</th>
                    <th>查看次数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($announcements as $announcement): ?>
                <tr>
                    <td><?php echo $announcement['id']; ?></td>
                    <td><?php echo htmlspecialchars($announcement['title']); ?></td>
                    <td><?php echo htmlspecialchars(mb_substr($announcement['summary'] ?? '', 0, 18)); ?></td>
                    <td>
                        <span class="status-badge <?php echo $announcement['is_active'] ? 'active' : 'inactive'; ?>">
                            <?php echo $announcement['is_active'] ? '启用' : '禁用'; ?>
                        </span>
                    </td>
                    <td><?php echo $announcement['is_important'] ? '是' : '否'; ?></td>
                    <td><?php echo $announcement['view_count']; ?></td>
                    <td><?php echo date('Y-m-d', strtotime($announcement['created_at'])); ?></td>
                    <td>
                        <a href="?edit=<?php echo $announcement['id']; ?>" class="btn btn-sm">编辑</a>
                        <form method="post" style="display: inline;" onsubmit="return confirm('确定要切换状态吗？')">
                            <input type="hidden" name="action" value="toggle_status">
                            <input type="hidden" name="id" value="<?php echo $announcement['id']; ?>">
                            <button type="submit" class="btn btn-sm btn-warning">切换状态</button>
                        </form>
                        <form method="post" style="display: inline;" onsubmit="return confirm('确定要删除这个公告吗？')">
                            <input type="hidden" name="action" value="delete">
                            <input type="hidden" name="id" value="<?php echo $announcement['id']; ?>">
                            <button type="submit" class="btn btn-sm btn-danger">删除</button>
                        </form>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
    <div class="pagination">
        <?php for ($i = 1; $i <= $totalPages; $i++): ?>
            <a href="?page=<?php echo $i; ?>" class="<?php echo $i === $page ? 'active' : ''; ?>">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
    </div>
    <?php endif; ?>
</div>

<!-- 创建/编辑公告表单 -->
<div id="announcementForm" class="modal" style="display: <?php echo $editAnnouncement ? 'block' : 'none'; ?>;">
    <div class="modal-content">
        <div class="modal-header">
            <h3><?php echo $editAnnouncement ? '编辑公告' : '新建公告'; ?></h3>
            <span class="close" onclick="hideForm()">&times;</span>
        </div>

        <form method="post" enctype="multipart/form-data" class="announcement-form">
            <input type="hidden" name="action" value="<?php echo $editAnnouncement ? 'update' : 'create'; ?>">
            <?php if ($editAnnouncement): ?>
                <input type="hidden" name="id" value="<?php echo $editAnnouncement['id']; ?>">
            <?php endif; ?>

            <div class="form-group">
                <label for="title">公告标题 *</label>
                <input type="text" id="title" name="title" required maxlength="200"
                       value="<?php echo htmlspecialchars($editAnnouncement['title'] ?? ''); ?>">
            </div>

            <div class="form-group">
                <label for="summary">公告摘要 (最多18个字)</label>
                <input type="text" id="summary" name="summary" maxlength="18"
                       placeholder="留空将自动从内容中提取前18个字"
                       value="<?php echo htmlspecialchars($editAnnouncement['summary'] ?? ''); ?>"
                       oninput="updateSummaryCounter()">
                <div class="summary-counter">
                    <span id="summaryCount">0</span>/18 字
                </div>
            </div>

            <div class="form-group">
                <label for="content">公告内容 *</label>
                <div class="editor-toolbar">
                    <button type="button" onclick="insertTag('strong')" title="粗体"><strong>B</strong> 粗体</button>
                    <button type="button" onclick="insertTag('em')" title="斜体"><em>I</em> 斜体</button>
                    <button type="button" onclick="insertTag('u')" title="下划线"><u>U</u> 下划线</button>
                    <button type="button" onclick="insertHeading()" title="插入标题">H 标题</button>
                    <button type="button" onclick="insertList()" title="插入列表">• 列表</button>
                    <button type="button" onclick="insertLink()" title="插入链接">🔗 链接</button>
                    <button type="button" onclick="showImageUpload()" title="上传图片">📷 图片</button>
                    <button type="button" onclick="showPreview()" title="预览公告效果 (Ctrl+P)" class="preview-btn">👁️ 预览</button>
                </div>
                <textarea id="content" name="content" rows="15" required
                          placeholder="支持HTML格式，可插入图片和链接"><?php echo htmlspecialchars($editAnnouncement['content'] ?? ''); ?></textarea>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_active" value="1"
                           <?php echo ($editAnnouncement['is_active'] ?? 1) ? 'checked' : ''; ?>>
                    启用显示
                </label>
            </div>

            <div class="form-group">
                <label>
                    <input type="checkbox" name="is_important" value="1"
                           <?php echo ($editAnnouncement['is_important'] ?? 0) ? 'checked' : ''; ?>>
                    重要公告（优先显示）
                </label>
            </div>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <?php echo $editAnnouncement ? '更新公告' : '创建公告'; ?>
                </button>
                <button type="button" class="btn" onclick="hideForm()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 图片上传模态框 -->
<div id="imageUploadModal" class="modal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>上传图片</h3>
            <span class="close" onclick="hideImageUpload()">&times;</span>
        </div>

        <div class="upload-area">
            <input type="file" id="imageFile" accept="image/jpeg,image/jpg,image/png,image/gif" onchange="previewImage()">
            <div class="upload-info">
                <p>支持格式：JPG, PNG, GIF</p>
                <p>最大大小：2MB</p>
            </div>
            <div id="imagePreview"></div>
        </div>

        <div class="form-actions">
            <button type="button" class="btn btn-primary" onclick="insertImage()">插入图片</button>
            <button type="button" class="btn" onclick="hideImageUpload()">取消</button>
        </div>
    </div>
</div>

<!-- 公告预览模态框 -->
<div id="previewModal" class="modal">
    <div class="modal-content preview-modal-content">
        <div class="modal-header">
            <h3>公告预览</h3>
            <span class="close" onclick="hidePreview()">&times;</span>
        </div>

        <div class="preview-container">
            <!-- 游戏界面预览 -->
            <div class="preview-section">
                <h4>游戏主界面显示效果</h4>
                <div class="game-interface-preview">
                    <div class="connection-status-demo">已连接到服务器</div>
                    <div class="announcement-bar-demo" id="previewAnnouncementBar">
                        [公告] 预览内容将在这里显示
                    </div>
                </div>
            </div>

            <!-- 详情界面预览 -->
            <div class="preview-section">
                <h4>公告详情界面显示效果</h4>
                <div class="announcement-detail-preview">
                    <div class="preview-title" id="previewTitle">公告标题</div>
                    <div class="preview-meta" id="previewMeta">发布时间: 2024-01-01 12:00:00 | 查看次数: 0</div>
                    <div class="preview-content" id="previewContent">公告内容将在这里显示</div>
                </div>
            </div>
        </div>

        <div class="form-actions">
            <button type="button" class="btn" onclick="hidePreview()">关闭预览</button>
        </div>
    </div>
</div>

<style>
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #fefefe;
    margin: 5% auto;
    padding: 0;
    border: 1px solid #888;
    width: 90%;
    max-width: 800px;
    border-radius: 5px;
}

.modal-header {
    padding: 18px 24px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-radius: 5px 5px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
    font-size: 18px;
    font-weight: 600;
}

.close {
    color: #6c757d;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
    line-height: 1;
}

.close:hover {
    color: #dc3545;
    background-color: #f8f9fa;
    transform: scale(1.1);
}

.announcement-form {
    padding: 20px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
}

.form-group input[type="text"],
.form-group textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
    background-color: #ffffff;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-group input[type="text"]:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

.summary-counter {
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    text-align: right;
}

.summary-counter.warning {
    color: #ffc107;
}

.summary-counter.danger {
    color: #dc3545;
}

.editor-toolbar {
    margin-bottom: 10px;
    padding: 12px;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px 4px 0 0;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.editor-toolbar button {
    margin-right: 8px;
    margin-bottom: 5px;
    padding: 8px 12px;
    border: 1px solid #6c757d;
    background-color: #ffffff;
    color: #495057;
    cursor: pointer;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    transition: all 0.2s ease;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.editor-toolbar button:hover {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.editor-toolbar button:active {
    transform: translateY(0);
    box-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.editor-toolbar .preview-btn {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.editor-toolbar .preview-btn:hover {
    background-color: #218838;
    border-color: #1e7e34;
}

#content {
    border-radius: 0 0 4px 4px;
    border-top: none;
}

.form-actions {
    margin-top: 25px;
    padding-top: 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
}

.form-actions .btn {
    margin-left: 10px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 500;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.form-actions .btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.form-actions .btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,123,255,0.3);
}

.form-actions .btn:not(.btn-primary) {
    background-color: #6c757d;
    border-color: #6c757d;
    color: white;
}

.form-actions .btn:not(.btn-primary):hover {
    background-color: #545b62;
    border-color: #545b62;
}

.upload-area {
    padding: 20px;
    text-align: center;
}

.upload-info {
    margin: 10px 0;
    color: #666;
}

#imagePreview {
    margin-top: 15px;
}

#imagePreview img {
    max-width: 300px;
    max-height: 200px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.status-badge {
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
}

.status-badge.active {
    background-color: #d4edda;
    color: #155724;
}

.status-badge.inactive {
    background-color: #f8d7da;
    color: #721c24;
}

.alert {
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
    position: relative;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.alert-close {
    background: none;
    border: none;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    color: inherit;
    opacity: 0.7;
    padding: 0;
    margin-left: 15px;
}

.alert-close:hover {
    opacity: 1;
}

.action-buttons {
    margin-bottom: 20px;
}

.pagination {
    text-align: center;
    margin-top: 20px;
}

.pagination a {
    display: inline-block;
    padding: 8px 12px;
    margin: 0 2px;
    text-decoration: none;
    border: 1px solid #ddd;
    color: #007bff;
}

.pagination a:hover,
.pagination a.active {
    background-color: #007bff;
    color: white;
}

/* 预览模态框样式 */
.preview-modal-content {
    max-width: 900px;
    width: 95%;
}

.preview-container {
    padding: 20px;
    max-height: 70vh;
    overflow-y: auto;
}

.preview-section {
    margin-bottom: 30px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
}

.preview-section h4 {
    margin: 0;
    padding: 12px 16px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    font-size: 14px;
    font-weight: 600;
    color: #495057;
}

/* 游戏界面预览样式 */
.game-interface-preview {
    padding: 15px;
    background-color: #2c3e50;
    color: #ecf0f1;
    font-family: 'Microsoft YaHei', sans-serif;
}

.connection-status-demo {
    padding: 5px 10px;
    background-color: #27ae60;
    color: white;
    border-radius: 3px;
    font-size: 14px;
    margin-bottom: 5px;
    display: inline-block;
}

.announcement-bar-demo {
    padding: 5px;
    background-color: #2c3e50;
    color: #ecf0f1;
    border-left: 4px solid #3498db;
    margin: 5px 0;
    font-size: 14px;
    cursor: pointer;
}

.announcement-bar-demo.important {
    border-left-color: #e74c3c;
}

/* 公告详情预览样式 */
.announcement-detail-preview {
    padding: 20px;
    background-color: #ffffff;
    border: 1px solid #dee2e6;
    margin: 15px;
    border-radius: 4px;
}

.preview-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
}

.preview-meta {
    color: #7f8c8d;
    font-size: 12px;
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ecf0f1;
}

.preview-content {
    line-height: 1.6;
    color: #2c3e50;
}

.preview-content img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    margin: 10px 0;
}

.preview-content h1, .preview-content h2, .preview-content h3,
.preview-content h4, .preview-content h5, .preview-content h6 {
    margin-top: 20px;
    margin-bottom: 10px;
}

.preview-content p {
    margin-bottom: 10px;
}

.preview-content ul, .preview-content ol {
    margin-bottom: 10px;
    padding-left: 20px;
}

.preview-content a {
    color: #007bff;
    text-decoration: none;
}

.preview-content a:hover {
    text-decoration: underline;
}
</style>

<script>
function showCreateForm() {
    document.getElementById('announcementForm').style.display = 'block';
}

function hideForm() {
    document.getElementById('announcementForm').style.display = 'none';
    // 无论是创建还是编辑模式，都返回到列表页面
    window.location.href = 'announcements.php';
}

function hideAlert() {
    const alert = document.getElementById('alertMessage');
    if (alert) {
        alert.style.display = 'none';
    }
}

function updateSummaryCounter() {
    const summaryInput = document.getElementById('summary');
    const counter = document.getElementById('summaryCount');
    const counterContainer = counter.parentElement;

    if (summaryInput && counter) {
        const length = summaryInput.value.length;
        counter.textContent = length;

        // 更新计数器颜色
        counterContainer.className = 'summary-counter';
        if (length >= 18) {
            counterContainer.classList.add('danger');
        } else if (length >= 15) {
            counterContainer.classList.add('warning');
        }
    }
}

function showImageUpload() {
    document.getElementById('imageUploadModal').style.display = 'block';
}

function hideImageUpload() {
    document.getElementById('imageUploadModal').style.display = 'none';
}

function showPreview() {
    const title = document.getElementById('title').value || '公告标题';
    const content = document.getElementById('content').value || '<p>公告内容</p>';
    const summary = document.getElementById('summary').value;
    const isImportant = document.querySelector('input[name="is_important"]').checked;

    // 生成摘要（如果没有手动输入）
    let displaySummary = summary;
    if (!displaySummary) {
        // 从内容中提取摘要
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        const textContent = tempDiv.textContent || tempDiv.innerText || '';
        displaySummary = textContent.replace(/\s+/g, '').substring(0, 18);
    }

    // 确保摘要不超过18字
    if (displaySummary.length > 18) {
        displaySummary = displaySummary.substring(0, 18);
    }

    // 更新游戏界面预览
    const announcementBar = document.getElementById('previewAnnouncementBar');
    announcementBar.textContent = `[公告] ${displaySummary}`;
    announcementBar.className = 'announcement-bar-demo' + (isImportant ? ' important' : '');

    // 更新详情界面预览
    document.getElementById('previewTitle').textContent = title;
    document.getElementById('previewMeta').textContent = `发布时间: ${new Date().toLocaleString('zh-CN')} | 查看次数: 0`;
    document.getElementById('previewContent').innerHTML = content;

    // 显示预览模态框
    document.getElementById('previewModal').style.display = 'block';
}

function hidePreview() {
    document.getElementById('previewModal').style.display = 'none';
}

function insertTag(tag) {
    const textarea = document.getElementById('content');
    const start = textarea.selectionStart;
    const end = textarea.selectionEnd;
    const selectedText = textarea.value.substring(start, end);
    const replacement = `<${tag}>${selectedText}</${tag}>`;

    textarea.value = textarea.value.substring(0, start) + replacement + textarea.value.substring(end);
    textarea.focus();
    textarea.setSelectionRange(start + tag.length + 2, start + tag.length + 2 + selectedText.length);
}

function insertHeading() {
    const level = prompt('请输入标题级别 (1-6):', '2');
    if (level && level >= 1 && level <= 6) {
        insertTag(`h${level}`);
    }
}

function insertList() {
    const textarea = document.getElementById('content');
    const start = textarea.selectionStart;
    const listHtml = '<ul>\n  <li>项目1</li>\n  <li>项目2</li>\n</ul>';

    textarea.value = textarea.value.substring(0, start) + listHtml + textarea.value.substring(start);
    textarea.focus();
}

function insertLink() {
    const url = prompt('请输入链接地址:', 'https://');
    const text = prompt('请输入链接文字:', '链接');

    if (url && text) {
        const textarea = document.getElementById('content');
        const start = textarea.selectionStart;
        const linkHtml = `<a href="${url}" target="_blank">${text}</a>`;

        textarea.value = textarea.value.substring(0, start) + linkHtml + textarea.value.substring(start);
        textarea.focus();
    }
}

function previewImage() {
    const file = document.getElementById('imageFile').files[0];
    const preview = document.getElementById('imagePreview');

    if (file) {
        // 检查文件大小 (2MB)
        if (file.size > 2 * 1024 * 1024) {
            alert('图片大小不能超过2MB');
            return;
        }

        // 检查文件类型
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
        if (!allowedTypes.includes(file.type)) {
            alert('只支持JPG、PNG、GIF格式的图片');
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            preview.innerHTML = `<img src="${e.target.result}" alt="预览">`;
        };
        reader.readAsDataURL(file);
    }
}

function insertImage() {
    const file = document.getElementById('imageFile').files[0];
    if (!file) {
        alert('请先选择图片');
        return;
    }

    // 显示上传进度
    const uploadBtn = event.target;
    const originalText = uploadBtn.textContent;
    uploadBtn.textContent = '上传中...';
    uploadBtn.disabled = true;

    // 创建FormData对象
    const formData = new FormData();
    formData.append('image', file);

    // 上传图片
    fetch('upload_image.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            const alt = prompt('请输入图片描述:', '图片') || '图片';

            const textarea = document.getElementById('content');
            const start = textarea.selectionStart;
            const imgHtml = `<img src="${data.url}" alt="${alt}" style="max-width: 100%; height: auto;" width="${data.width}" height="${data.height}">`;

            textarea.value = textarea.value.substring(0, start) + imgHtml + textarea.value.substring(start);
            textarea.focus();

            hideImageUpload();

            // 清除文件选择和预览
            document.getElementById('imageFile').value = '';
            document.getElementById('imagePreview').innerHTML = '';
        } else {
            alert('图片上传失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        alert('图片上传失败，请重试');
    })
    .finally(() => {
        // 恢复按钮状态
        uploadBtn.textContent = originalText;
        uploadBtn.disabled = false;
    });
}

// 点击模态框外部关闭
window.onclick = function(event) {
    const announcementModal = document.getElementById('announcementForm');
    const imageModal = document.getElementById('imageUploadModal');
    const previewModal = document.getElementById('previewModal');

    if (event.target === announcementModal) {
        hideForm();
    }
    if (event.target === imageModal) {
        hideImageUpload();
    }
    if (event.target === previewModal) {
        hidePreview();
    }
}

// 自动隐藏成功消息和初始化计数器
document.addEventListener('DOMContentLoaded', function() {
    const alertMessage = document.getElementById('alertMessage');
    if (alertMessage && alertMessage.classList.contains('alert-success')) {
        // 5秒后自动隐藏成功消息
        setTimeout(function() {
            alertMessage.style.opacity = '0';
            setTimeout(function() {
                alertMessage.style.display = 'none';
            }, 300);
        }, 5000);
    }

    // 初始化摘要字符计数器
    updateSummaryCounter();

    // 添加键盘快捷键支持
    document.addEventListener('keydown', function(event) {
        // ESC键关闭模态框
        if (event.key === 'Escape') {
            const previewModal = document.getElementById('previewModal');
            const imageModal = document.getElementById('imageUploadModal');
            const announcementModal = document.getElementById('announcementForm');

            if (previewModal.style.display === 'block') {
                hidePreview();
            } else if (imageModal.style.display === 'block') {
                hideImageUpload();
            } else if (announcementModal.style.display === 'block') {
                hideForm();
            }
        }

        // Ctrl+P 快速预览
        if (event.ctrlKey && event.key === 'p') {
            event.preventDefault();
            const announcementModal = document.getElementById('announcementForm');
            if (announcementModal.style.display === 'block') {
                showPreview();
            }
        }
    });
});
</script>

<?php require_once 'layout_footer.php'; ?>
