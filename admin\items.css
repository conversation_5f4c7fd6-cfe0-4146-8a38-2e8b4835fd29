/* 物品管理页面 - 卡片网格布局专用样式 */

.card {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 20px;
    overflow: hidden; /* Ensures the child elements conform to the border radius */
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.card-header h2 {
    margin: 0;
    font-size: 1.25rem;
    font-weight: 500;
}

.category-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 10px;
}

.slot-filters, .equipment-type-filters {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.equipment-type-filters {
    background-color: #e8f4fd;
    border: 1px solid #b3d9f2;
}

.filter-label {
    font-size: 0.9rem;
    font-weight: 600;
    color: #495057;
    margin-right: 10px;
    align-self: center;
}

.slot-filter-btn {
    background-color: #fff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.slot-filter-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.slot-filter-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: #fff;
}

.equipment-type-filter-btn {
    background-color: #fff;
    border: 1px solid #dee2e6;
    color: #495057;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

.equipment-type-filter-btn:hover {
    background-color: #e3f2fd;
    border-color: #90caf9;
}

.equipment-type-filter-btn.active {
    background-color: #2196f3;
    border-color: #2196f3;
    color: #fff;
}

/* 装备类型标识样式 */
.item-name-section {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.equipment-type-badge {
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 3px;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.monster-equipment {
    background-color: #dc3545;
    color: white;
    border: 1px solid #c82333;
}

.player-equipment {
    background-color: #28a745;
    color: white;
    border: 1px solid #1e7e34;
}

.search-and-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.search-and-actions .search-input {
    width: 250px; /* Adjust width as needed */
}

.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 20px;
    padding: 20px;
}

.item-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 0; /* 改为0，由内部元素控制padding */
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    transition: box-shadow 0.2s ease-in-out, transform 0.2s ease-in-out;
    display: flex;
    flex-direction: column;
    overflow: hidden; /* 防止内容溢出圆角 */
}

.item-card:hover {
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
    transform: translateY(-3px);
}

.item-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
}

.item-card-header h3 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #343a40;
}

.item-id-display {
    font-size: 0.8rem;
    color: #6c757d;
    font-family: monospace;
}

.item-card-body {
    padding: 15px 20px;
    font-size: 0.9rem;
    color: #495057;
    flex-grow: 1;
}

.item-card-body p {
    margin: 0 0 10px 0;
}

.item-card-body p:last-child {
    margin-bottom: 0;
}

.item-details {
    margin-bottom: 10px;
}

.item-details span {
    display: inline-block;
    margin-right: 15px;
    font-size: 0.85rem;
    color: #6c757d;
}

/* 装备职业信息样式 */
.item-jobs {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 5px;
}

.item-jobs span {
    display: inline-block;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 0.75rem;
    font-weight: 500;
    white-space: nowrap;
}

.job-grant {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.job-restriction {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.equipment-slot {
    background-color: #e2e3e5;
    color: #383d41;
    border: 1px solid #d6d8db;
}

.equipment-sockets {
    background-color: #cce7ff;
    color: #004085;
    border: 1px solid #b3d7ff;
}

.item-prices {
    margin-top: 10px;
    padding-top: 8px;
    border-top: 1px solid #e9ecef;
}

.item-prices span {
    display: inline-block;
    margin-right: 12px;
    margin-bottom: 4px;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

.price-gold {
    background-color: #fff3cd;
    color: #856404;
    border: 1px solid #ffeaa7;
}

.price-diamond {
    background-color: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

.price-sell {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.item-attributes {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px dashed #e9ecef;
    font-size: 0.85rem;
}

.attribute-group {
    margin-bottom: 5px;
}
.attribute-group:last-child {
    margin-bottom: 0;
}
.attribute-group strong {
    color: #343a40;
    margin-right: 5px;
}
.attribute-group span {
    background-color: #e9ecef;
    padding: 2px 6px;
    border-radius: 4px;
    margin-right: 5px;
    white-space: nowrap;
}

.item-card-body p strong {
    color: #343a40;
}

.item-id-mono {
    font-family: 'Courier New', Courier, monospace;
    background-color: #e9ecef;
    padding: 2px 4px;
    border-radius: 3px;
    font-size: 0.85rem;
}

.item-card-footer {
    padding: 10px 20px;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    background-color: #fff;
    border-top: 1px solid #e9ecef;
}

.item-card-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .items-grid {
        grid-template-columns: 1fr;
    }
}

/* 物品管理页面样式 */
body {
    font-family: 'Arial', 'Microsoft YaHei', sans-serif;
    background-color: #f4f4f4;
    line-height: 1.6;
}

.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 2px solid #0d1a26;
    padding-bottom: 10px;
}

.page-header h1 {
    color: #0d1a26;
    margin: 0;
    font-size: 24px;
}

.add-item-btn {
    background-color: #0d1a26;
    color: white;
    border: none;
    padding: 8px 15px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.add-item-btn:hover {
    background-color: #1a3347;
}

/* 保留原有的表格样式，以备不时之需 */
.items-table {
    display: none; /* 隐藏原表格 */
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
}

.pagination button {
    background-color: #0d1a26;
    color: white;
    border: none;
    padding: 8px 15px;
    margin: 0 5px;
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.pagination button:disabled {
    background-color: #6c8095;
    cursor: not-allowed;
}

.search-container {
    display: flex;
    margin-bottom: 15px;
}

.search-input {
    flex-grow: 1;
    padding: 8px 12px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.search-button {
    background-color: #0d1a26;
    color: white;
    border: none;
    padding: 8px 15px;
    margin-left: 10px;
    border-radius: 4px;
    cursor: pointer;
}

@media (max-width: 768px) {
    .page-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .add-item-btn {
        margin-top: 10px;
    }

    .items-table {
        font-size: 14px;
    }

    .items-table th, .items-table td {
        padding: 8px 10px;
    }
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: none; /* 默认隐藏 */
    justify-content: center;
    align-items: center;
    z-index: 1050;
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    box-shadow: 0 5px 15px rgba(0,0,0,.5);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h2 {
    margin: 0;
    font-size: 1.25rem;
}

.modal-close {
    border: none;
    background: transparent;
    font-size: 1.5rem;
    font-weight: 700;
    line-height: 1;
    opacity: .5;
    cursor: pointer;
    padding: 1rem;
    margin: -1rem -1rem -1rem auto;
}
.modal-close:hover {
    opacity: .75;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* 表单样式 */
#item-form .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

#item-form .form-group {
    display: flex;
    flex-direction: column;
}

#item-form .full-width {
    grid-column: 1 / -1;
}

.effects-builder {
    display: flex;
    gap: 10px;
    margin-bottom: 5px;
    align-items: center;
}
.effects-builder select {
    flex-grow: 1;
}
.effects-builder input {
    width: 100px;
}

/* Toast样式已移至全局style.css，避免冲突 */

/* 分页和动画 */
.pagination-info {
    padding: 0 15px;
    color: #6c757d;
}
@keyframes fadeIn { from { opacity: 0; } to { opacity: 1; } }

/* 动画定义已移至全局style.css */

.no-items-message {
    grid-column: 1 / -1;
    text-align: center;
    color: #6c757d;
    padding: 40px 20px;
    background-color: #f8f9fa;
    border-radius: 6px;
} 