<?php 
$pageTitle = '物品模板管理';
$currentPage = 'items';

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

// --- 动态获取并处理玩家属性 ---
$attribute_definitions = [];
try {
    // 1. 获取 player_attributes 表的所有列
    $stmt_cols = $db->query("DESCRIBE player_attributes");
    $columns = $stmt_cols->fetchAll(PDO::FETCH_COLUMN);
    $stmt_cols->closeCursor();

    // 2. 定义属性的中文名映射
    $name_map = [
        'level' => '等级', 'hp' => '生命', 'mp' => '魔力', 'max_hp' => '最大生命', 'max_mp' => '最大魔力',
        'experience' => '经验', 'experience_to_next_level' => '下一级经验',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智慧',
        'attack' => '攻击', 'defense' => '防御', 'attack_speed' => '攻速', 'karma' => '善恶',
        'potential_points' => '潜力点', 'knowledge_points' => '知识点',
        'fire_resistance' => '火抗', 'ice_resistance' => '冰抗', 'wind_resistance' => '风抗', 'electric_resistance' => '电抗',
        'fire_damage' => '火伤', 'ice_damage' => '冰冻', 'wind_damage' => '风裂', 'electric_damage' => '闪电'
    ];
    
    // 3. 定义哪些属性是瞬时效果 (通常由消耗品提供)
    $effect_only_keys = ['hp', 'mp', 'experience', 'potential_points', 'knowledge_points'];
    
    // 4. 定义需要从下拉菜单中排除的属性
    $excluded_keys = ['id', 'account_id', 'native_job_id', 'current_job_id', 'current_scene_id', 'experience_to_next_level'];

    // 5. 动态生成最终的属性定义
    foreach ($columns as $column) {
        if (in_array($column, $excluded_keys)) {
            continue;
        }
        $group = in_array($column, $effect_only_keys) ? 'effect' : 'all';
        $attribute_definitions[] = [
            'key' => $column,
            'group' => $group,
            'name' => $name_map[$column] ?? ucfirst(str_replace('_', ' ', $column)) // 如果没有中文名，则自动生成
        ];
    }
} catch (Exception $e) {
    // 如果查询失败，可以提供一个备用的基础列表，或者记录错误
    error_log("无法从数据库动态获取属性列表: " . $e->getMessage());
}


// 从 jobs 表动态获取职业列表
$stmt_jobs = $db->query("SELECT `id`, `name` FROM `jobs` ORDER BY `id`");
$jobs = $stmt_jobs->fetchAll(PDO::FETCH_ASSOC);
$stmt_jobs->closeCursor();

// 从 skills 表动态获取技能列表
$stmt_skills = $db->query("SELECT `id`, `name` FROM `skill_templates` ORDER BY `name`");
$all_skills = $stmt_skills->fetchAll(PDO::FETCH_ASSOC);
$stmt_skills->closeCursor();

ob_start();
?>
<link rel="stylesheet" href="items.css">
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="items.js" defer></script>
<?php
$extra_js = ob_get_clean();

require_once 'layout_header.php'; 

// --- 开始构建动态表单 ---
$form_fields_html = '<input type="hidden" id="item-id" name="id">';
$form_fields_html .= '<div class="form-grid">';

// ... (基础字段，如名称、ID、分类等)
$form_fields_html .= '
    <div class="form-group"><label for="item-name">名称</label><input type="text" id="item-name" name="name" required></div>
    <div class="form-group"><label for="item-item_id">物品ID (自动生成)</label><input type="text" id="item-item_id" name="item_id" readonly></div>
    <div class="form-group"><label for="item-category">分类</label><select id="item-category" name="category" required><option value="Equipment">装备</option><option value="Gem">宝石</option><option value="Material">材料</option><option value="Potion">药品</option><option value="Rune">符石</option><option value="Misc">杂物</option><option value="Scroll">书卷</option></select></div>
    <div class="form-group" id="equipment-type-group" style="display: none;"><label for="item-equipment_type">装备类型</label><select id="item-equipment_type" name="equipment_type"><option value="player">玩家装备</option><option value="monster">怪物装备</option></select></div>
    <div class="form-group"><label for="item-is_consumable">是否消耗品</label><select id="item-is_consumable" name="is_consumable"><option value="0">否</option><option value="1">是</option></select></div>
    <div class="form-group"><label for="item-stackable">可堆叠</label><select id="item-stackable" name="stackable"><option value="0">否</option><option value="1">是</option></select></div>
    <div class="form-group"><label for="item-max_stack">最大堆叠</label><input type="number" id="item-max_stack" name="max_stack" value="1" min="1"></div>
    <div class="form-group"><label for="item-buy_price">购买价格 (金币)</label><input type="number" id="item-buy_price" name="buy_price" placeholder="金币购买价格"></div>
    <div class="form-group"><label for="item-diamond_price">钻石价格</label><input type="number" id="item-diamond_price" name="diamond_price" placeholder="钻石购买价格"></div>
    <div class="form-group"><label for="item-sell_price">出售价格 (金币)</label><input type="number" id="item-sell_price" name="sell_price" placeholder="出售给商店的价格"></div>
    <div class="form-group full-width"><label for="item-description">描述</label><textarea id="item-description" name="description" rows="3"></textarea></div>
';

// --- JSON 构建器 (效果和属性) ---
$form_fields_html .= '
    <div class="form-group full-width json-builder-group" id="item-effects-group">
        <label for="item-effects">效果 (JSON格式)</label>
        <div class="json-builder-controls">
            <select id="effect-key-select" class="form-control"></select>
            <input type="number" id="effect-value-input" placeholder="值" class="form-control">
            <button type="button" class="btn btn-secondary btn-sm" id="add-effect-btn">添加</button>
            <button type="button" class="btn btn-danger btn-sm" id="clear-effects-btn">清空</button>
        </div>
        <textarea id="item-effects" name="effects" rows="3" class="form-control"></textarea>
        <div class="effects-help" style="font-size: 0.9em; color: #666; margin-top: 5px;">
            <p style="margin:0; font-weight:bold;">示例:</p>
            <ul style="margin: 5px 0 0 20px; padding: 0;">
                <li><b>药水:</b> <code>{"hp": 50, "mp": 20}</code></li>
                <li><b>永久增强道具:</b> <code>{"strength": 1}</code></li>
                <li><b>技能书:</b> <code>{"learn_skill_id": 123}</code> (123是技能ID)</li>
            </ul>
        </div>
        <div class="skillbook-helper" style="margin-top: 10px; border-top: 1px solid #eee; padding-top: 10px;">
            <label style="display: block; margin-bottom: 5px;">技能书助手:</label>
            <div style="display: flex; gap: 10px; align-items: center;">
                 <select id="skill-helper-select" class="form-control" style="flex-grow: 1;">
                     <option value="">-- 选择一个技能来创建技能书 --</option>
                 </select>
                 <button type="button" class="btn btn-info btn-sm" id="insert-skill-btn">设为技能书</button>
            </div>
        </div>
    </div>

    <div class="form-group full-width json-builder-group" id="item-stats-group">
        <label for="item-stats">基础属性 (JSON格式)</label>
        <div class="json-builder-controls">
            <select id="stat-key-select" class="form-control"></select>
            <input type="number" id="stat-value-input" placeholder="值" class="form-control">
            <button type="button" class="btn btn-secondary btn-sm" id="add-stat-btn">添加</button>
            <button type="button" class="btn btn-danger btn-sm" id="clear-stats-btn">清空</button>
        </div>
        <textarea id="item-stats" name="stats" rows="4" class="form-control" placeholder=\'例如: {"attack": 10, "defense": 5}\'></textarea>
    </div>
</div>';

// --- 装备专属字段 ---
$form_fields_html .= '
<div class="form-group full-width" id="equipment_fields" style="display:none;">
<fieldset>
    <legend>装备属性</legend>
    <div class="form-grid">
         <div class="form-group"><label for="item-slot">装备部位</label><select id="item-slot" name="slot" class="form-control"><option value="Head">头部</option><option value="Neck">颈部</option><option value="LeftHand">左手</option><option value="RightHand">右手</option><option value="TwoHanded">双手</option><option value="Body">身体</option><option value="Finger">手指</option><option value="Back">背部</option></select></div>
         <div class="form-group"><label for="item-job_restriction">职业限制</label><select id="item-job_restriction" name="job_restriction" class="form-control"><option value="None">无</option>';
foreach ($jobs as $job) {
    if ($job['id'] == 1) continue; // Skip "无业" or the base job if it shouldn't be a restriction
    $form_fields_html .= '<option value="' . htmlspecialchars($job['id']) . '">' . htmlspecialchars($job['name']) . '</option>';
}
$form_fields_html .= '</select></div>';
$form_fields_html .= '<div class="form-group"><label for="item-sockets">宝石插槽</label><input type="number" id="item-sockets" name="sockets" value="0" min="0" class="form-control"></div>';
// 动态生成"授予职业"下拉菜单
$form_fields_html .= '<div class="form-group"><label for="item-grants_job_id">授予职业</label><select id="item-grants_job_id" name="grants_job_id" class="form-control"><option value="">无</option>';
foreach ($jobs as $job) {
    if ($job['id'] == 1) continue;
    $form_fields_html .= '<option value="' . htmlspecialchars($job['id']) . '">' . htmlspecialchars($job['name']) . '</option>';
}
$form_fields_html .= '</select></div>';
$form_fields_html .= '</div></fieldset></div>';

// --- 模态框页脚 ---
$form_fields_html .= '
<div class="modal-footer">
    <button type="button" class="btn btn-secondary" onclick="hideItemModal()">取消</button>
    <button type="submit" class="btn btn-primary">保存</button>
</div>';

?>

<div class="main-content-area card">
    <div class="card-header">
        <div class="search-and-actions">
            <input type="text" class="form-control search-input" placeholder="按名称、ID或分类搜索...">
            <button class="btn btn-secondary search-button">搜索</button>
            <button id="add-new-item-btn" class="btn btn-primary">添加新物品</button>
        </div>
        <div class="category-filters">
            <button class="btn btn-sm category-filter-btn active" data-category="All">全部</button>
            <button class="btn btn-sm category-filter-btn" data-category="Equipment">装备</button>
            <button class="btn btn-sm category-filter-btn" data-category="Gem">宝石</button>
            <button class="btn btn-sm category-filter-btn" data-category="Material">材料</button>
            <button class="btn btn-sm category-filter-btn" data-category="Potion">药品</button>
            <button class="btn btn-sm category-filter-btn" data-category="Rune">符石</button>
            <button class="btn btn-sm category-filter-btn" data-category="Scroll">书卷</button>
            <button class="btn btn-sm category-filter-btn" data-category="Misc">杂物</button>
        </div>
        <div class="slot-filters" id="slot-filters" style="display: none;">
            <span class="filter-label">装备部位:</span>
            <button class="btn btn-sm slot-filter-btn active" data-slot="All">全部部位</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="Head">头部</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="Neck">颈部</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="LeftHand">左手</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="RightHand">右手</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="TwoHanded">双手</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="Body">身体</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="Finger">手指</button>
            <button class="btn btn-sm slot-filter-btn" data-slot="Back">背部</button>
        </div>
        <div class="equipment-type-filters" id="equipment-type-filters" style="display: none;">
            <span class="filter-label">装备类型:</span>
            <button class="btn btn-sm equipment-type-filter-btn active" data-equipment-type="All">全部类型</button>
            <button class="btn btn-sm equipment-type-filter-btn" data-equipment-type="player">玩家装备</button>
            <button class="btn btn-sm equipment-type-filter-btn" data-equipment-type="monster">怪物装备</button>
        </div>
    </div>
    <div class="items-grid">
        <!-- 物品卡片将在这里通过JS动态渲染 -->
    </div>
    <div class="pagination">
        <!-- 分页控件将在这里通过JS动态渲染 -->
    </div>
</div>

<!-- Item Modal -->
<div id="itemModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">物品信息</h2>
            <button class="modal-close" onclick="hideItemModal()">&times;</button>
        </div>
        <form id="item-form" style="padding: 20px; max-height: 70vh; overflow-y: auto;">
            <!-- Form fields will be injected here by JavaScript -->
        </form>
    </div>
</div>

<div id="toast" class="toast"></div>

<script>
    const ATTRIBUTE_DEFINITIONS = <?= json_encode($attribute_definitions) ?>;
    const FORM_FIELDS_HTML = <?= json_encode($form_fields_html) ?>;
    const ALL_JOBS = <?= json_encode($jobs) ?>;
    const ALL_SKILLS = <?= json_encode($all_skills) ?>;
</script>

<?php 
require_once 'layout_footer.php'; 
?> 