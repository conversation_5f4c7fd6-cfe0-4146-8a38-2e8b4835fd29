<?php
/**
 * 装备稀有度管理器
 * 负责装备品级识别、加成计算等功能
 */
class RarityManager {
    private array $config;
    private bool $debugEnabled;
    
    public function __construct() {
        // 使用已验证的配置文件路径
        $configPath = __DIR__ . '/../config/rarity_config.php';

        if (file_exists($configPath)) {
            error_log("[稀有度系统] 尝试加载配置文件: {$configPath}");
            try {
                $config = include $configPath;
                if (is_array($config) && !empty($config)) {
                    $this->config = $config;
                    error_log("[稀有度系统] 配置文件加载成功: {$configPath}");
                } else {
                    error_log("[稀有度系统] 配置文件格式错误，使用默认配置");
                    $this->config = $this->getDefaultConfig();
                }
            } catch (Exception $e) {
                error_log("[稀有度系统] 配置文件加载异常，使用默认配置: " . $e->getMessage());
                $this->config = $this->getDefaultConfig();
            }
        } else {
            error_log("[稀有度系统] 配置文件不存在: {$configPath}，使用默认配置");
            $this->config = $this->getDefaultConfig();
        }

        $this->debugEnabled = $this->config['debug']['enabled'] ?? false;
        $this->log("稀有度管理器初始化完成");
    }
    
    /**
     * 从装备名称中识别稀有度
     * @param string $equipmentName 装备名称
     * @return array 包含稀有度信息的数组
     */
    public function detectRarity(string $equipmentName): array {
        $pattern = $this->config['rarity_detection']['pattern'];
        $defaultRarity = $this->config['rarity_detection']['default_rarity'];

        $this->log("开始检测装备稀有度: '{$equipmentName}', 使用模式: {$pattern}");

        // 尝试正则匹配
        $regexMatched = false;
        if (preg_match($pattern, $equipmentName, $matches)) {
            $this->log("正则匹配成功，匹配结果: " . json_encode($matches, JSON_UNESCAPED_UNICODE));
            $rarity = $matches[1];
            $baseName = $matches[2];

            // 验证稀有度是否在配置中
            if (isset($this->config['rarity_levels'][$rarity])) {
                $this->log("检测到装备稀有度: {$equipmentName} -> 品级: {$rarity}, 基础名称: {$baseName}");

                return [
                    'rarity' => $rarity,
                    'base_name' => $baseName,
                    'has_rarity' => true,
                    'config' => $this->config['rarity_levels'][$rarity]
                ];
            } else {
                $this->log("稀有度 '{$rarity}' 不在配置中，可用稀有度: " . implode(', ', array_keys($this->config['rarity_levels'])));
            }
            $regexMatched = true;
        }

        // 如果正则匹配失败，尝试手动字符串匹配
        if (!$regexMatched) {
            $this->log("正则匹配失败，尝试手动字符串匹配");

            // 检查是否以 [X] 格式开头
            if (mb_strlen($equipmentName) >= 3 && mb_substr($equipmentName, 0, 1) === '[' && mb_substr($equipmentName, 2, 1) === ']') {
                $rarity = mb_substr($equipmentName, 1, 1);
                $baseName = mb_substr($equipmentName, 3);

                $this->log("手动匹配成功: 品级='{$rarity}', 基础名称='{$baseName}'");

                // 验证稀有度是否在配置中
                if (isset($this->config['rarity_levels'][$rarity])) {
                    $this->log("检测到装备稀有度(手动): {$equipmentName} -> 品级: {$rarity}, 基础名称: {$baseName}");

                    return [
                        'rarity' => $rarity,
                        'base_name' => $baseName,
                        'has_rarity' => true,
                        'config' => $this->config['rarity_levels'][$rarity]
                    ];
                } else {
                    $this->log("手动提取的稀有度 '{$rarity}' 不在配置中");
                }
            } else {
                $this->log("装备名称格式不符合 [X]名称 的模式");
            }
        }

        // 没有检测到稀有度或稀有度不在配置中
        $this->log("装备无稀有度标识或使用默认品级: {$equipmentName} -> 默认品级: {$defaultRarity}");

        return [
            'rarity' => $defaultRarity,
            'base_name' => $equipmentName,
            'has_rarity' => false,
            'config' => $this->config['rarity_levels'][$defaultRarity]
        ];
    }
    
    /**
     * 获取稀有度加成系数
     * @param string $equipmentName 装备名称
     * @return float 加成系数
     */
    public function getRarityMultiplier(string $equipmentName): float {
        if (!$this->config['refine_bonus']['enabled']) {
            return 1.0;
        }
        
        $rarityInfo = $this->detectRarity($equipmentName);
        $multiplier = $rarityInfo['config']['multiplier'];
        
        // 应用边界限制
        $minMultiplier = $this->config['refine_bonus']['min_multiplier'];
        $maxMultiplier = $this->config['refine_bonus']['max_multiplier'];
        $finalMultiplier = max($minMultiplier, min($maxMultiplier, $multiplier));
        
        $this->log("装备 {$equipmentName} 的稀有度加成系数: {$finalMultiplier} (原始: {$multiplier})");
        
        return $finalMultiplier;
    }
    
    /**
     * 获取稀有度详细信息
     * @param string $rarity 稀有度名称
     * @return array|null 稀有度配置信息
     */
    public function getRarityInfo(string $rarity): ?array {
        return $this->config['rarity_levels'][$rarity] ?? null;
    }
    
    /**
     * 获取所有稀有度配置
     * @return array 所有稀有度配置
     */
    public function getAllRarities(): array {
        return $this->config['rarity_levels'];
    }
    
    /**
     * 根据插槽数量推断稀有度
     * @param int $slotCount 插槽数量
     * @return string|null 稀有度名称
     */
    public function getRarityBySlots(int $slotCount): ?string {
        foreach ($this->config['rarity_levels'] as $rarity => $config) {
            if ($config['slots'] === $slotCount) {
                return $rarity;
            }
        }
        return null;
    }
    
    /**
     * 格式化装备名称（添加稀有度标识）
     * @param string $baseName 基础名称
     * @param string $rarity 稀有度
     * @return string 格式化后的名称
     */
    public function formatEquipmentName(string $baseName, string $rarity): string {
        if (!$this->config['display']['show_rarity_in_name']) {
            return $baseName;
        }
        
        $format = $this->config['display']['name_format'];
        return str_replace(['{rarity}', '{name}'], [$rarity, $baseName], $format);
    }
    
    /**
     * 获取稀有度颜色
     * @param string $rarity 稀有度名称
     * @return string 颜色代码
     */
    public function getRarityColor(string $rarity): string {
        return $this->config['rarity_levels'][$rarity]['color'] ?? '#FFFFFF';
    }
    
    /**
     * 验证稀有度是否有效
     * @param string $rarity 稀有度名称
     * @return bool 是否有效
     */
    public function isValidRarity(string $rarity): bool {
        return isset($this->config['rarity_levels'][$rarity]);
    }
    
    /**
     * 获取下一级稀有度
     * @param string $currentRarity 当前稀有度
     * @return string|null 下一级稀有度，如果已是最高级则返回null
     */
    public function getNextRarity(string $currentRarity): ?string {
        $rarities = array_keys($this->config['rarity_levels']);
        $currentIndex = array_search($currentRarity, $rarities);
        
        if ($currentIndex !== false && $currentIndex < count($rarities) - 1) {
            return $rarities[$currentIndex + 1];
        }
        
        return null;
    }
    
    /**
     * 记录调试日志
     * @param string $message 日志消息
     */
    private function log(string $message): void {
        if ($this->debugEnabled) {
            $prefix = $this->config['debug']['log_prefix'];
            error_log("{$prefix} {$message}");
        }
    }
    
    /**
     * 获取默认配置（当配置文件不存在或格式错误时使用）
     * @return array 默认配置数组
     */
    private function getDefaultConfig(): array {
        return [
            'rarity_levels' => [
                '普' => ['multiplier' => 1.0, 'slots' => 0, 'description' => '普通品质', 'color' => '#FFFFFF'],
                '凡' => ['multiplier' => 1.2, 'slots' => 1, 'description' => '凡级品质', 'color' => '#CCCCCC'],
                '良' => ['multiplier' => 1.4, 'slots' => 2, 'description' => '良级品质', 'color' => '#00FF00'],
                '优' => ['multiplier' => 1.6, 'slots' => 3, 'description' => '优级品质', 'color' => '#0080FF'],
                '珍' => ['multiplier' => 1.8, 'slots' => 4, 'description' => '珍级品质', 'color' => '#8000FF'],
                '极' => ['multiplier' => 2.0, 'slots' => 5, 'description' => '极级品质', 'color' => '#FF8000'],
                '玄' => ['multiplier' => 2.2, 'slots' => 6, 'description' => '玄级品质', 'color' => '#FF0080'],
                '灵' => ['multiplier' => 2.4, 'slots' => 7, 'description' => '灵级品质（未开放）', 'color' => '#FF0000'],
                '圣' => ['multiplier' => 2.6, 'slots' => 8, 'description' => '圣级品质（未开放）', 'color' => '#FFD700']
            ],
            'rarity_detection' => [
                'prefixes' => ['普', '凡', '良', '优', '珍', '极', '玄', '灵', '圣'],
                'pattern' => '/^\[([普凡良优珍极玄灵圣])\](.+)$/',
                'default_rarity' => '普',
                'strict_mode' => true
            ],
            'refine_bonus' => [
                'enabled' => true,
                'calculation_method' => 'multiply',
                'min_multiplier' => 1.0,
                'max_multiplier' => 3.0,
                'increment_per_level' => 0.2
            ],
            'display' => [
                'show_rarity_in_name' => true,
                'name_format' => '[{rarity}]{name}',
                'show_rarity_color' => true,
                'show_in_tooltip' => true
            ],
            'debug' => [
                'enabled' => true,
                'log_prefix' => '[稀有度系统]'
            ]
        ];
    }

    /**
     * 获取配置信息（用于调试）
     * @return array 配置数组
     */
    public function getConfig(): array {
        return $this->config;
    }
}
?>
