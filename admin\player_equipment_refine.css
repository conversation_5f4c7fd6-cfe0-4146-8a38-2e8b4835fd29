/* 玩家装备数据管理页面专用样式 */

/* 装备数据专用样式 */
.refine-info {
    background-color: rgba(76, 175, 80, 0.05);
    border-left: 3px solid #4caf50;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.base-stats-info {
    background-color: rgba(33, 150, 243, 0.05);
    border-left: 3px solid #2196f3;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.gem-info {
    background-color: rgba(156, 39, 176, 0.05);
    border-left: 3px solid #9c27b0;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.binding-info {
    background-color: rgba(255, 152, 0, 0.05);
    border-left: 3px solid #ff9800;
    padding: 10px;
    margin: 10px 0;
    border-radius: 4px;
}

.refine-tier {
    font-weight: bold;
    color: #4caf50;
}

.refine-value {
    color: #2196f3;
    font-weight: bold;
}

.refine-bonuses, .base-stats, .gem-stats {
    margin-top: 8px;
}

.refine-bonus-item, .stat-item-display, .gem-item-display {
    display: inline-block;
    background-color: #e8f5e8;
    padding: 2px 6px;
    margin: 2px;
    border-radius: 3px;
    font-size: 12px;
}

.stat-item-display {
    background-color: #e3f2fd;
}

.gem-item-display {
    background-color: #f3e5f5;
}

.bound-indicator {
    color: #ff9800;
    font-weight: bold;
}

.equipped-indicator {
    color: #4caf50;
    font-weight: bold;
}

.equipment-slot {
    font-weight: bold;
    color: #666;
}

.equipment-name {
    color: #333;
    font-weight: bold;
}

.equipment-name:hover {
    color: #0056b3 !important;
    text-decoration: underline;
}

.no-refine {
    color: #999;
    font-style: italic;
}

.player-info {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    margin-bottom: 15px;
}

.equipment-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
    gap: 15px;
}

.equipment-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
    transition: box-shadow 0.2s ease;
}

.equipment-card:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.equipment-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.search-filters {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.filter-row {
    display: flex;
    gap: 15px;
    align-items: center;
    margin-bottom: 10px;
}

.filter-row:last-child {
    margin-bottom: 0;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 5px;
}

.filter-group label {
    font-weight: 500;
    font-size: 14px;
}

.stats-summary {
    background-color: #e3f2fd;
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 20px;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.stat-item {
    text-align: center;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    color: #1976d2;
}

.stat-label {
    font-size: 14px;
    color: #666;
}

/* 分页样式 */
.pagination {
    margin-top: 20px;
    text-align: center;
}

.pagination .btn {
    margin: 0 2px;
    padding: 8px 12px;
    text-decoration: none;
    border-radius: 4px;
    display: inline-block;
}

.pagination .btn-primary {
    background-color: #007bff;
    color: white;
    border: 1px solid #007bff;
}

.pagination .btn-secondary {
    background-color: #6c757d;
    color: white;
    border: 1px solid #6c757d;
}

.pagination .btn:hover {
    opacity: 0.8;
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    text-decoration: none;
    display: inline-block;
    font-size: 14px;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-info {
    background-color: #17a2b8;
    color: white;
}

.btn-success {
    background-color: #28a745;
    color: white;
}

.btn:hover {
    opacity: 0.9;
    transform: translateY(-1px);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .equipment-grid {
        grid-template-columns: 1fr;
    }
    
    .filter-row {
        flex-direction: column;
        align-items: stretch;
    }
    
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .equipment-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }
}

/* 模态框样式增强 */
.equipment-detail-content h4 {
    color: #333;
    margin-bottom: 15px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
}

.equipment-detail-content p {
    margin: 8px 0;
    line-height: 1.4;
}

/* 加载状态 */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
