// js/skill-manager.js

class SkillManager {
    constructor(gameClient) {
        this.gameClient = gameClient;
        this.skills = []; // 玩家已学的技能列表
        this.skillLevels = {}; // 技能等级: { skillId: level }
    }

    /**
     * 初始化技能管理器
     */
    initialize() {
        // 这里可以添加获取技能列表的逻辑
        console.log("技能管理器初始化");
    }

    /**
     * 判断一个物品是否是技能书
     * @param {Object} item - 物品对象
     * @returns {boolean|Object} - 如果是技能书，返回提取的技能信息，否则返回false
     */
    isSkillBook(item) {
        // 1. 先确保有effects字段并且能够解析
        if (!item.effects) return false;
        
        let effects;
        try {
            if (typeof item.effects === 'string') {
                effects = JSON.parse(item.effects);
            } else {
                effects = item.effects;
            }
        } catch (e) {
            console.error("解析物品效果失败:", e, item.effects);
            return false;
        }
        
        // 2. 检查是否含有learn_skill_id字段
        if (!effects.learn_skill_id) return false;
        
        console.log("找到技能书:", item.name, "技能ID:", effects.learn_skill_id);
        
        // 3. 返回技能信息对象
        return {
            skillId: effects.learn_skill_id,
            bookName: item.name,
            inventoryId: item.inventory_id
        };
    }

    /**
     * 在物品详情界面中显示技能书信息
     * @param {Object} item - 物品对象
     * @param {HTMLElement} contentElement - 内容HTML元素
     */
    renderSkillBookDetails(item, contentElement) {
        const skillInfo = this.isSkillBook(item);
        if (!skillInfo) return false;
        
        // 添加特殊的技能书描述
        let skillHtml = `
            <hr class="section-divider">
            <div style="color: #4b0082; margin-top: 10px;">
                <b>技能书:</b> 使用后可学习新技能
            </div>
        `;
        
        // 将HTML插入到内容元素中
        if (contentElement.innerHTML) {
            contentElement.innerHTML += skillHtml;
        }
        
        return true;
    }
    
    /**
     * 特殊处理技能书的使用按钮样式
     * @param {Object} item - 物品对象
     * @param {HTMLElement} buttonElement - 按钮元素
     */
    styleSkillBookButton(item, buttonElement) {
        const skillInfo = this.isSkillBook(item);
        if (!skillInfo || !buttonElement) return false;
        
        // 为技能书的使用按钮添加特殊样式
        buttonElement.classList.add('skill-book-button');
        buttonElement.innerHTML = '[学习技能]';
        
        return true;
    }

    /**
     * 显示技能列表界面
     */
    showSkillsView() {
        // 隐藏主视图，显示技能视图
        document.getElementById('main-view').style.display = 'none';
        const skillsView = document.getElementById('skillsView');
        skillsView.style.display = 'block';

        // 调试信息
        console.log("正在发送获取技能列表请求...");

        // 向服务器请求技能列表
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_PLAYER_SKILLS);
    }

    /**
     * 隐藏技能列表界面
     */
    hideSkillsView() {
        // 隐藏技能视图，显示主视图
        document.getElementById('skillsView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    /**
     * 更新技能列表的显示
     * @param {Array} skills - 从服务器获取的技能数组
     */
    updateSkillsList(skills) {
        // 调试信息
        console.log("收到技能列表数据:", skills);

        this.skills = skills || [];
        this.currentJobFilter = 'all'; // 重置筛选状态

        const container = document.getElementById('skillsListContainer');
        if (!container) {
            console.error("找不到技能列表容器元素!");
            return;
        }

        if (!skills || skills.length === 0) {
            // 隐藏筛选按钮
            const filtersContainer = document.getElementById('skillJobFilters');
            if (filtersContainer) {
                filtersContainer.style.display = 'none';
            }

            container.innerHTML = `
                <div style="text-align: center; padding: 20px; color: #777;">
                    <div style="font-size: 18px; margin-bottom: 10px;">✨</div>
                    <div>你还没有学习任何技能</div>
                    <div style="font-size: 13px; margin-top: 5px;">可以通过使用技能书来学习新技能</div>
                </div>`;
            return;
        }

        // 生成职业筛选按钮
        this.generateJobFilters(skills);

        // 按职业排序，通用技能（无职业或职业为"通用"）排在最后，然后按技能名排序
        skills.sort((a, b) => {
            const profA = (a.required_job_name && a.required_job_name !== '通用') ? a.required_job_name : '通用';
            const profB = (b.required_job_name && b.required_job_name !== '通用') ? b.required_job_name : '通用';

            const isAUniversal = profA === '通用';
            const isBUniversal = profB === '通用';

            if (isAUniversal && !isBUniversal) return 1;
            if (!isAUniversal && isBUniversal) return -1;

            // 如果职业不同，按职业名称字母排序
            if (profA !== profB) {
                return profA.localeCompare(profB, 'zh-Hans-CN');
            }

            // 如果职业相同，按技能名称排序
            return a.name.localeCompare(b.name, 'zh-Hans-CN');
        });

        this.renderSkillsList(skills);
    }

    /**
     * 生成职业筛选按钮
     * @param {Array} skills - 技能数组
     */
    generateJobFilters(skills) {
        const filtersContainer = document.getElementById('skillJobFilters');
        if (!filtersContainer) return;

        // 获取所有职业类型
        const jobTypes = new Set();
        skills.forEach(skill => {
            const jobName = (skill.required_job_name && skill.required_job_name !== '通用') ? skill.required_job_name : '通用';
            jobTypes.add(jobName);
        });

        // 如果只有一种职业类型，不显示筛选按钮
        if (jobTypes.size <= 1) {
            filtersContainer.style.display = 'none';
            return;
        }

        // 显示筛选按钮容器
        filtersContainer.style.display = 'block';

        // 生成筛选按钮HTML
        let filtersHtml = '<button class="btn-primary skill-filter-btn active" data-job="all" style="margin: 2px;">全部</button>';

        // 按职业名称排序，通用排在最后
        const sortedJobs = Array.from(jobTypes).sort((a, b) => {
            if (a === '通用' && b !== '通用') return 1;
            if (a !== '通用' && b === '通用') return -1;
            return a.localeCompare(b, 'zh-Hans-CN');
        });

        sortedJobs.forEach(jobName => {
            filtersHtml += `<button class="btn-primary skill-filter-btn" data-job="${jobName}" style="margin: 2px;">${jobName}</button>`;
        });

        filtersContainer.innerHTML = filtersHtml;

        // 绑定筛选按钮事件
        filtersContainer.querySelectorAll('.skill-filter-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const jobFilter = e.target.dataset.job;
                this.filterSkillsByJob(jobFilter);

                // 更新按钮状态
                filtersContainer.querySelectorAll('.skill-filter-btn').forEach(b => b.classList.remove('active'));
                e.target.classList.add('active');
            });
        });
    }

    /**
     * 根据职业筛选技能
     * @param {string} jobFilter - 职业筛选条件，'all'表示显示全部
     */
    filterSkillsByJob(jobFilter) {
        this.currentJobFilter = jobFilter;

        let filteredSkills = this.skills;
        if (jobFilter !== 'all') {
            filteredSkills = this.skills.filter(skill => {
                const jobName = (skill.required_job_name && skill.required_job_name !== '通用') ? skill.required_job_name : '通用';
                return jobName === jobFilter;
            });
        }

        this.renderSkillsList(filteredSkills);
    }

    /**
     * 渲染技能列表
     * @param {Array} skills - 要显示的技能数组
     */
    renderSkillsList(skills) {
        const container = document.getElementById('skillsListContainer');
        if (!container) return;

        container.innerHTML = skills.map(skill => this.createSkillItemHtml(skill)).join('');
    }

    /**
     * 创建单个技能项目的HTML
     * @param {Object} skill - 技能对象
     * @returns {string} - HTML字符串
     */
    createSkillItemHtml(skill) {
        const rarityColor = {
            'common': '#6c757d',
            'uncommon': '#28a745',
            'rare': '#007bff',
            'epic': '#6f42c1',
            'legendary': '#fd7e14'
        };

        const skillName = `<span style="color: ${rarityColor[skill.rarity] || '#333'}; font-weight: bold;">${skill.name}</span>`;
        const skillLevel = `(Lv. ${skill.skill_level})`;
        const professionText = (skill.required_job_name && skill.required_job_name !== '通用') ? skill.required_job_name : '通用';
        const professionTag = ` <span style="color: #555; font-size: 0.9em;">[${professionText}]</span>`;
        
        // 右侧显示魔力消耗和知识点信息
        const mpText = skill.current_mp_cost > 0 ? `MP:${skill.current_mp_cost}` : '';
        const pointsText = skill.next_level_cost ? `升级:${skill.next_level_cost}点` : '';
        const rightInfo = [mpText, pointsText].filter(Boolean).join(' | ');
        
        return `
            <div class="scene-entity-item" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; padding: 5px; border-bottom: 1px solid #eee;">
                 <div>
                    <a href="#" onclick="event.preventDefault(); game.skillManager.showSkillDetail(${skill.skill_template_id})">${skill.icon || '✨'} ${skillName}${professionTag} ${skillLevel}</a>
                 </div>
                 <div style="font-size: 0.85em; color: #888; white-space: nowrap; padding-left: 10px;">
                    ${rightInfo}
                 </div>
            </div>
        `;
    }

    /**
     * 显示技能详情
     * @param {number} skillId - 技能模板ID
     */
    showSkillDetail(skillId) {
        const skill = this.skills.find(s => s.skill_template_id === skillId);
        if (!skill) return;

        // 定义一次稀有度颜色映射，供整个方法使用
        const rarityColor = {
            'common': '#6c757d',
            'uncommon': '#28a745',
            'rare': '#007bff',
            'epic': '#6f42c1',
            'legendary': '#fd7e14'
        };
        const color = rarityColor[skill.rarity] || '#333';

        // 设置技能名称标题
        const titleElement = document.getElementById('skillDetailName');
        if (titleElement) {
            titleElement.innerHTML = `<span style="color: ${color}">${skill.icon || '✨'} ${skill.name}</span> <span style="font-size: 14px;">(Lv. ${skill.skill_level})</span>`;
        }

        // 获取技能详情容器
        const detailContainer = document.getElementById('skillDetailContainer');
        if (!detailContainer) return;
        
        // 中文稀有度标签
        const rarityLabel = {
            'common': '普通',
            'uncommon': '优秀',
            'rare': '稀有',
            'epic': '史诗',
            'legendary': '传说'
        };
        
        // 中文技能类型标签
        const skillTypeLabel = {
            'COMBAT_INSTANT': '瞬时',
            'COMBAT_DELAYED': '吟唱',
            'BUFF': '增益',
            'DEBUFF': '减益',
            'PASSIVE': '被动'
        };
        
        // 中文目标类型标签
        const targetTypeLabel = {
            'SELF': '自身',
            'ENEMY': '敌方单体',
            'ALLY': '我方全体',
            'HOSTILE': '敌方全体'
        };
        
        const rarityText = rarityLabel[skill.rarity] || '未知';
        const skillTypeText = skillTypeLabel[skill.skill_type] || '被动';
        const targetTypeText = targetTypeLabel[skill.target_type] || '自身';
        
        // 构建技能详情HTML，格式与物品详情保持一致
        let html = '';
        
        // 基本属性部分
        html += `<div style="margin-bottom: 10px;">`;
        html += `<table style="width: 100%; border-spacing: 0; border-collapse: collapse;">`;
        
        // 添加技能属性
        html += `<tr><td style="padding: 3px 0; width: 35%;">稀有度:</td><td style="padding: 3px 0;"><span style="color: ${color};">${rarityText}</span></td></tr>`;
        html += `<tr><td style="padding: 3px 0;">类型:</td><td style="padding: 3px 0;">${skillTypeText}</td></tr>`;
        html += `<tr><td style="padding: 3px 0;">目标:</td><td style="padding: 3px 0;">${targetTypeText}</td></tr>`;
        
        // 显示当前等级的魔法消耗（从服务器返回的计算值）
        if (skill.current_mp_cost > 0) {
            html += `<tr><td style="padding: 3px 0;">魔法消耗:</td><td style="padding: 3px 0;">${skill.current_mp_cost} 点</td></tr>`;
        }
        
        if (skill.cooldown_turns) {
            html += `<tr><td style="padding: 3px 0;">冷却回合:</td><td style="padding: 3px 0;">${skill.cooldown_turns}</td></tr>`;
        }

        if (skill.delay_turns) {
            html += `<tr><td style="padding: 3px 0;">吟唱回合:</td><td style="padding: 3px 0;">${skill.delay_turns}</td></tr>`;
        }

        if (skill.duration_turns) {
            html += `<tr><td style="padding: 3px 0;">持续回合:</td><td style="padding: 3px 0;">${skill.duration_turns}</td></tr>`;
        }
        
        if (skill.required_job_name) {
            html += `<tr><td style="padding: 3px 0;">职业要求:</td><td style="padding: 3px 0;">${skill.required_job_name}</td></tr>`;
        }
        
        html += `</table></div>`;
        
        // 技能效果部分（使用描述作为效果内容）
        if (skill.description) {
            html += `<div style="margin-top: 10px; padding-top: 10px; border-top: 1px solid #eee;">`;
            html += `<div style="font-weight: bold; margin-bottom: 5px;">技能效果:</div>`;
            html += `<div style="color: #555;">${skill.description}</div>`;
            html += `</div>`;
        }
        
        // 更新技能详情并显示
        detailContainer.innerHTML = html;
        
        // 切换视图
        document.getElementById('skillsListView').style.display = 'none';
        document.getElementById('skillDetailView').style.display = 'block';
    }

    /**
     * 隐藏技能详情，返回技能列表
     */
    hideSkillDetail() {
        document.getElementById('skillDetailView').style.display = 'none';
        document.getElementById('skillsListView').style.display = 'block';
    }

    /**
     * 处理战斗中可用的技能列表
     * @param {Object} payload 技能数据
     */
    handleBattleSkillsList(payload) {
        const skillsContainer = document.getElementById('battleActionsContainer');
        if (!skillsContainer) return;

        // 移除任何现有的技能按钮和它们的换行符，以防止重复
        skillsContainer.querySelectorAll('.btn-skill, .skills-line-break').forEach(el => el.remove());

        // 如果有新技能要显示，在它们前面添加一个换行元素以确保它们从新行开始
        if (payload.skills && payload.skills.length > 0) {
            const lineBreak = document.createElement('div');
            lineBreak.className = 'skills-line-break';
            lineBreak.style.flexBasis = '100%';
            lineBreak.style.height = '5px'; // 在行之间创建一个小间隔
            skillsContainer.appendChild(lineBreak);

            // 添加新技能按钮
            payload.skills.forEach((skill, index) => {
                console.log(`SkillManager.handleBattleSkillsList - 技能 ${index}:`, skill);
                console.log(`SkillManager.handleBattleSkillsList - skill.skill_template_id: ${skill.skill_template_id}`);
                console.log(`SkillManager.handleBattleSkillsList - skill.id: ${skill.id}`);

                // 尝试多个可能的技能ID字段名
                const skillId = skill.skill_template_id || skill.id || skill.skill_id;
                console.log(`SkillManager.handleBattleSkillsList - 最终使用的skillId: ${skillId}`);

                const button = document.createElement('button');
                button.className = 'btn btn-skill';
                button.dataset.skillId = skillId; // 添加技能ID用于状态管理
                button.dataset.skillName = skill.name; // 保存技能名称用于恢复
                button.dataset.mpCost = skill.mp_cost || 0; // 保存MP消耗
                button.innerHTML = `${skill.name}<br><small>MP: ${skill.mp_cost || 0}</small>`;
                console.log(`SkillManager.handleBattleSkillsList - 创建按钮，dataset.skillId: ${button.dataset.skillId}`);
                button.onclick = () => {
                    // 检查技能是否可用（包括冷却、吟唱等状态）
                    if (!this.isSkillAvailable(skill)) {
                        return; // 如果技能不可用，直接返回
                    }

                    const mpCost = skill.mp_cost || 0;
                    if (this.gameClient.battleState.player.attributes.mp < mpCost) {
                        this.gameClient.addBattleLog(document.getElementById('battleLogContainer'), `法力不足，无法施放【${skill.name}】！`);
                        return;
                    }
                    this.gameClient.sendBattleAction('use_skill', { skill_id: skillId, skill_name: skill.name });
                };
                skillsContainer.appendChild(button);
            });

            // 立即更新技能按钮状态
            this.updatePveSkillButtonsState();
        }
    }

    /**
     * 检查技能是否可用（PVE模式）
     * @param {Object} skill 技能对象
     * @returns {boolean} 技能是否可用
     */
    isSkillAvailable(skill) {
        if (!this.gameClient.battleState) return true;

        const playerId = this.gameClient.currentPlayer.id;
        const battleState = this.gameClient.battleState;

        // 检查技能冷却
        const skillCooldowns = battleState.skill_cooldowns?.[playerId] || {};
        const cooldownTurns = skillCooldowns[skill.skill_template_id] || 0;

        // 调试信息：检查技能可用性
        console.log(`SkillManager.isSkillAvailable - 技能 ${skill.name} (ID: ${skill.skill_template_id})`);
        console.log(`SkillManager.isSkillAvailable - 技能冷却状态:`, skillCooldowns);
        console.log(`SkillManager.isSkillAvailable - 冷却回合数: ${cooldownTurns}`);

        if (cooldownTurns > 0) {
            console.log(`SkillManager.isSkillAvailable - 技能 ${skill.name} 在冷却中，剩余 ${cooldownTurns} 回合，阻止使用`);
            return false;
        }

        // 检查是否在吟唱中
        const player = battleState.player;
        if (player && player.casting_info) {
            return false;
        }

        // 检查是否在增益阶段（如果有相关状态）
        if (player && player.buff_phase) {
            return false;
        }

        return true;
    }

    /**
     * 更新PVE技能按钮状态（类似PVP的updateSkillButtonsState）
     */
    updatePveSkillButtonsState() {
        if (!this.gameClient.battleState) return;

        const playerId = this.gameClient.currentPlayer.id;
        const battleState = this.gameClient.battleState;
        const player = battleState.player;

        // 获取技能冷却信息
        const skillCooldowns = battleState.skill_cooldowns?.[playerId] || {};

        // 调试信息：检查技能冷却数据
        console.log('SkillManager.updatePveSkillButtonsState - 玩家ID:', playerId);
        console.log('SkillManager.updatePveSkillButtonsState - 战斗状态中的技能冷却:', battleState.skill_cooldowns);
        console.log('SkillManager.updatePveSkillButtonsState - 当前玩家的技能冷却:', skillCooldowns);

        // 检查玩家是否在吟唱中
        const isPlayerCasting = player && player.casting_info;

        // 检查玩家是否在增益阶段
        const isPlayerInBuffPhase = player && player.buff_phase;

        // 检查玩家是否被沉默
        const isPlayerSilenced = this.isPlayerSilenced(battleState, playerId);

        // 检查玩家是否被眩晕
        const isPlayerStunned = this.isPlayerStunned(battleState, playerId);

        // 更新所有技能按钮状态
        const skillButtons = document.querySelectorAll('.btn-skill');
        console.log(`SkillManager.updatePveSkillButtonsState - 找到 ${skillButtons.length} 个技能按钮`);

        skillButtons.forEach((button, index) => {
            const skillId = parseInt(button.dataset.skillId, 10);
            console.log(`SkillManager.updatePveSkillButtonsState - 按钮 ${index}: skillId=${skillId}, dataset.skillId="${button.dataset.skillId}"`);

            if (!skillId) {
                console.log(`SkillManager.updatePveSkillButtonsState - 跳过按钮 ${index}，无效的skillId`);
                return;
            }

            const cooldownTurns = skillCooldowns[skillId] || 0;
            const isCooldown = cooldownTurns > 0;

            // 调试信息：按钮状态更新
            console.log(`SkillManager.updatePveSkillButtonsState - 技能按钮 ${skillId}: 冷却${cooldownTurns}回合, 是否冷却中: ${isCooldown}, 当前disabled: ${button.disabled}`);

            // 如果玩家在吟唱中、增益阶段、被眩晕、被沉默或技能在冷却中，禁用按钮
            if (isPlayerCasting || isPlayerInBuffPhase || isPlayerStunned || isPlayerSilenced || isCooldown) {
                button.disabled = true;
                console.log(`SkillManager.updatePveSkillButtonsState - 禁用技能按钮 ${skillId}`);

                // 清除之前的状态样式
                button.classList.remove('cooldown', 'casting', 'buff-phase', 'silenced', 'stunned');

                // 添加对应的状态样式和提示（优先级：眩晕 > 沉默 > 冷却 > 吟唱 > 增益阶段）
                if (isPlayerStunned) {
                    button.classList.add('stunned');
                    button.title = '眩晕中';
                    button.innerHTML = button.innerHTML.replace(/^.*?<br>/, '眩晕<br>');
                    console.log(`SkillManager.updatePveSkillButtonsState - 技能 ${skillId} 添加眩晕样式`);
                } else if (isPlayerSilenced) {
                    button.classList.add('silenced');
                    button.title = '沉默中';
                    button.innerHTML = button.innerHTML.replace(/^.*?<br>/, '沉默<br>');
                    console.log(`SkillManager.updatePveSkillButtonsState - 技能 ${skillId} 添加沉默样式`);
                } else if (isCooldown) {
                    button.classList.add('cooldown');
                    button.title = `冷却中 (${cooldownTurns}回合)`;
                    console.log(`SkillManager.updatePveSkillButtonsState - 技能 ${skillId} 添加冷却样式`);
                } else if (isPlayerCasting) {
                    button.classList.add('casting');
                    button.title = '吟唱中';
                } else if (isPlayerInBuffPhase) {
                    button.classList.add('buff-phase');
                    button.title = '增益阶段';
                }
            } else {
                button.disabled = false;
                button.classList.remove('cooldown', 'casting', 'buff-phase', 'silenced', 'stunned');
                button.title = '';
                // 恢复原始技能名称（移除状态文字）
                const skillName = button.dataset.skillName;
                if (skillName) {
                    button.innerHTML = `${skillName}<br><small>MP: ${button.dataset.mpCost || 0}</small>`;
                }
                console.log(`SkillManager.updatePveSkillButtonsState - 启用技能按钮 ${skillId}`);
            }
        });
    }

    /**
     * 更新战斗中的状态效果显示
     * @param {Array} activeEffects 当前生效的状态效果
     * @param {string} playerId 玩家ID
     */
    updateBattleEffectsDisplay(activeEffects, playerId) {
        const effectsContainer = document.getElementById('battleEffectsContainer');
        if (!effectsContainer) return;

        // 筛选出属于当前玩家的效果
        const playerEffects = activeEffects.filter(effect => effect.target_id == playerId);

        if (playerEffects.length === 0) {
            effectsContainer.innerHTML = '';
            return;
        }

        let html = '<div style="margin-top: 5px; font-size: 12px;">';
        html += '<div style="color: #666; margin-bottom: 3px;">状态效果:</div>';

        playerEffects.forEach(effect => {
            const isDebuff = this.isDebuffEffect(effect.effects);
            const effectColor = isDebuff ? '#dc3545' : '#28a745';
            const effectIcon = isDebuff ? '🔻' : '🔺';

            html += `<div style="display: inline-block; margin: 2px; padding: 2px 6px; background: ${effectColor}20; border: 1px solid ${effectColor}; border-radius: 3px; color: ${effectColor}; font-size: 11px;">`;
            html += `${effectIcon} ${effect.source_skill_name} (${effect.remaining_turns})`;
            html += '</div>';
        });

        html += '</div>';
        effectsContainer.innerHTML = html;
    }

    /**
     * 判断效果是否为减益效果
     * @param {Object} effects 效果对象
     * @returns {boolean} 是否为减益效果
     */
    isDebuffEffect(effects) {
        // 检查是否包含负面效果
        for (const [stat, value] of Object.entries(effects)) {
            if (stat === 'silence' || stat === 'stun') {
                return true;
            }
            if (typeof value === 'number' && value < 0) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查玩家是否被沉默
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {boolean} 是否被沉默
     */
    isPlayerSilenced(battleState, playerId) {
        if (!battleState.active_effects) {
            return false;
        }

        for (const effect of battleState.active_effects) {
            if (effect.target_id == playerId && effect.effects.silence && effect.effects.silence > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查玩家是否被眩晕
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {boolean} 是否被眩晕
     */
    isPlayerStunned(battleState, playerId) {
        if (!battleState.active_effects) {
            return false;
        }

        for (const effect of battleState.active_effects) {
            if (effect.target_id == playerId && effect.effects.stun && effect.effects.stun > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 更新战斗中的常规按钮状态（攻击、逃跑、使用道具等）
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     */
    updateBattleActionButtons(battleState, playerId) {
        // 检查战斗容器是否存在
        const battleContainer = document.getElementById('battleActionsContainer');
        if (!battleContainer) {
            console.log('SkillManager.updateBattleActionButtons - battleActionsContainer 不存在，跳过更新');
            return;
        }

        // 检查玩家是否被眩晕
        const isPlayerStunned = this.isPlayerStunned(battleState, playerId);

        // 获取PVE战斗按钮（在battleActionsContainer中）
        const pveButtons = document.querySelectorAll('#battleActionsContainer .btn');

        // 获取PVP战斗按钮（在pvpBattleActionsContainer中或直接查找battle-action-btn）
        const pvpButtons = [
            ...document.querySelectorAll('#pvpBattleActionsContainer .battle-action-btn'),
            ...document.querySelectorAll('.battle-action-btn:not(.skill-btn)'), // 非技能的常规按钮
        ];

        // 获取所有可能的战斗相关按钮
        const allActionButtons = [
            ...pveButtons,
            ...pvpButtons,
            // 通过onclick属性查找的按钮
            ...document.querySelectorAll('[onclick*="sendBattleAction"]'),
            ...document.querySelectorAll('[onclick*="attack"]'),
            ...document.querySelectorAll('[onclick*="flee"]'),
            ...document.querySelectorAll('[onclick*="use_hp_potion"]'),
            ...document.querySelectorAll('[onclick*="use_mp_potion"]'),
            ...document.querySelectorAll('[onclick*="surrender"]'),
        ];

        // 去重
        const uniqueButtons = [...new Set(allActionButtons)];

        // 如果没有找到按钮，提前返回
        if (uniqueButtons.length === 0) {
            return;
        }

        uniqueButtons.forEach(button => {
            if (isPlayerStunned) {
                // 眩晕状态下禁用所有按钮
                button.disabled = true;
                button.classList.add('stunned-disabled');
                button.title = '眩晕中，无法行动';

                // 保存原始文字（如果还没保存）
                if (!button.dataset.originalText) {
                    button.dataset.originalText = button.textContent || button.innerHTML;
                }

                // 显示眩晕状态
                if (button.textContent && !button.textContent.includes('眩晕')) {
                    button.textContent = '眩晕';
                }
            } else {
                // 恢复正常状态
                button.disabled = false;
                button.classList.remove('stunned-disabled');
                button.title = '';

                // 恢复原始文字
                if (button.dataset.originalText) {
                    if (button.innerHTML !== button.textContent) {
                        button.innerHTML = button.dataset.originalText;
                    } else {
                        button.textContent = button.dataset.originalText;
                    }
                }
            }
        });
    }

}

// 导出技能管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SkillManager;
} 