"filename", "language", "Markdown", "Shell Script", "PHP", "HTML", "PostCSS", "JavaScript", "MS SQL", "Ini", "JSON", "YAML", "comment", "blank", "total"
"c:\Users\<USER>\Desktop\game\.user.ini", "Ini", 0, 0, 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\game\404.html", "HTML", 0, 0, 0, 7, 0, 0, 0, 0, 0, 0, 0, 0, 7
"c:\Users\<USER>\Desktop\game\README.md", "Markdown", 54, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12, 66
"c:\Users\<USER>\Desktop\game\admin\api_battle_logs.php", "PHP", 0, 0, 346, 0, 0, 0, 0, 0, 0, 0, 43, 68, 457
"c:\Users\<USER>\Desktop\game\admin\api_buildings.php", "PHP", 0, 0, 204, 0, 0, 0, 0, 0, 0, 0, 16, 47, 267
"c:\Users\<USER>\Desktop\game\admin\api_gem_recipes.php", "PHP", 0, 0, 180, 0, 0, 0, 0, 0, 0, 0, 10, 36, 226
"c:\Users\<USER>\Desktop\game\admin\api_item_templates.php", "PHP", 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 3, 7, 38
"c:\Users\<USER>\Desktop\game\admin\api_items.php", "PHP", 0, 0, 252, 0, 0, 0, 0, 0, 0, 0, 14, 51, 317
"c:\Users\<USER>\Desktop\game\admin\api_jobs.php", "PHP", 0, 0, 18, 0, 0, 0, 0, 0, 0, 0, 1, 5, 24
"c:\Users\<USER>\Desktop\game\admin\api_loot_tables.php", "PHP", 0, 0, 125, 0, 0, 0, 0, 0, 0, 0, 11, 27, 163
"c:\Users\<USER>\Desktop\game\admin\api_monsters.php", "PHP", 0, 0, 286, 0, 0, 0, 0, 0, 0, 0, 27, 63, 376
"c:\Users\<USER>\Desktop\game\admin\api_npcs.php", "PHP", 0, 0, 251, 0, 0, 0, 0, 0, 0, 0, 30, 47, 328
"c:\Users\<USER>\Desktop\game\admin\api_player_inventory.php", "PHP", 0, 0, 307, 0, 0, 0, 0, 0, 0, 0, 26, 62, 395
"c:\Users\<USER>\Desktop\game\admin\api_pvp_battle_logs.php", "PHP", 0, 0, 184, 0, 0, 0, 0, 0, 0, 0, 19, 41, 244
"c:\Users\<USER>\Desktop\game\admin\api_quests.php", "PHP", 0, 0, 269, 0, 0, 0, 0, 0, 0, 0, 30, 47, 346
"c:\Users\<USER>\Desktop\game\admin\api_recipes.php", "PHP", 0, 0, 137, 0, 0, 0, 0, 0, 0, 0, 5, 22, 164
"c:\Users\<USER>\Desktop\game\admin\api_scenes.php", "PHP", 0, 0, 560, 0, 0, 0, 0, 0, 0, 0, 42, 126, 728
"c:\Users\<USER>\Desktop\game\admin\api_shop.php", "PHP", 0, 0, 75, 0, 0, 0, 0, 0, 0, 0, 3, 16, 94
"c:\Users\<USER>\Desktop\game\admin\api_skills.php", "PHP", 0, 0, 96, 0, 0, 0, 0, 0, 0, 0, 8, 15, 119
"c:\Users\<USER>\Desktop\game\admin\api_teleporter.php", "PHP", 0, 0, 202, 0, 0, 0, 0, 0, 0, 0, 17, 37, 256
"c:\Users\<USER>\Desktop\game\admin\auth.php", "PHP", 0, 0, 15, 0, 0, 0, 0, 0, 0, 0, 5, 3, 23
"c:\Users\<USER>\Desktop\game\admin\battle_logs.css", "PostCSS", 0, 0, 0, 0, 244, 0, 0, 0, 0, 0, 9, 42, 295
"c:\Users\<USER>\Desktop\game\admin\battle_logs.js", "JavaScript", 0, 0, 0, 0, 0, 229, 0, 0, 0, 0, 19, 37, 285
"c:\Users\<USER>\Desktop\game\admin\battle_logs.php", "PHP", 0, 0, 157, 0, 0, 0, 0, 0, 0, 0, 0, 11, 168
"c:\Users\<USER>\Desktop\game\admin\buildings.js", "JavaScript", 0, 0, 0, 0, 0, 263, 0, 0, 0, 0, 32, 35, 330
"c:\Users\<USER>\Desktop\game\admin\buildings.php", "PHP", 0, 0, 300, 0, 0, 0, 0, 0, 0, 0, 2, 32, 334
"c:\Users\<USER>\Desktop\game\admin\clear_cache.php", "PHP", 0, 0, 45, 0, 0, 0, 0, 0, 0, 0, 16, 15, 76
"c:\Users\<USER>\Desktop\game\admin\clear_server_cache.php", "PHP", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 6, 6, 37
"c:\Users\<USER>\Desktop\game\admin\dashboard.php", "PHP", 0, 0, 84, 0, 0, 0, 0, 0, 0, 0, 9, 11, 104
"c:\Users\<USER>\Desktop\game\admin\dialogue_editor.php", "PHP", 0, 0, 1011, 0, 0, 0, 0, 0, 0, 0, 73, 131, 1215
"c:\Users\<USER>\Desktop\game\admin\dialogues.php", "PHP", 0, 0, 689, 0, 0, 0, 0, 0, 0, 0, 39, 91, 819
"c:\Users\<USER>\Desktop\game\admin\gem_recipes.js", "JavaScript", 0, 0, 0, 0, 0, 342, 0, 0, 0, 0, 9, 12, 363
"c:\Users\<USER>\Desktop\game\admin\gem_recipes.php", "PHP", 0, 0, 222, 0, 0, 0, 0, 0, 0, 0, 10, 29, 261
"c:\Users\<USER>\Desktop\game\admin\gem_recipes_enhanced.css", "PostCSS", 0, 0, 0, 0, 489, 0, 0, 0, 0, 0, 19, 88, 596
"c:\Users\<USER>\Desktop\game\admin\index.php", "PHP", 0, 0, 71, 0, 0, 0, 0, 0, 0, 0, 1, 6, 78
"c:\Users\<USER>\Desktop\game\admin\install_groups.php", "PHP", 0, 0, 226, 0, 0, 0, 0, 0, 0, 0, 24, 27, 277
"c:\Users\<USER>\Desktop\game\admin\items.css", "PostCSS", 0, 0, 0, 0, 383, 0, 0, 0, 0, 0, 8, 60, 451
"c:\Users\<USER>\Desktop\game\admin\items.js", "JavaScript", 0, 0, 0, 0, 0, 475, 0, 0, 0, 0, 130, 78, 683
"c:\Users\<USER>\Desktop\game\admin\items.php", "PHP", 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 15, 24, 217
"c:\Users\<USER>\Desktop\game\admin\layout_footer.php", "PHP", 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 0, 0, 12
"c:\Users\<USER>\Desktop\game\admin\layout_header.php", "PHP", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 5, 7, 104
"c:\Users\<USER>\Desktop\game\admin\logout.php", "PHP", 0, 0, 13, 0, 0, 0, 0, 0, 0, 0, 2, 4, 19
"c:\Users\<USER>\Desktop\game\admin\loot_tables.js", "JavaScript", 0, 0, 0, 0, 0, 28, 0, 0, 0, 0, 1, 3, 32
"c:\Users\<USER>\Desktop\game\admin\loot_tables.php", "PHP", 0, 0, 336, 0, 0, 0, 0, 0, 0, 0, 9, 45, 390
"c:\Users\<USER>\Desktop\game\admin\monsters.php", "PHP", 0, 0, 1045, 0, 0, 0, 0, 0, 0, 0, 51, 148, 1244
"c:\Users\<USER>\Desktop\game\admin\npc_dialogues.php", "PHP", 0, 0, 735, 0, 0, 0, 0, 0, 0, 0, 55, 99, 889
"c:\Users\<USER>\Desktop\game\admin\npcs.php", "PHP", 0, 0, 1376, 0, 0, 0, 0, 0, 0, 0, 90, 190, 1656
"c:\Users\<USER>\Desktop\game\admin\pinyin.js", "JavaScript", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 1, 1, 4
"c:\Users\<USER>\Desktop\game\admin\player_inventory.css", "PostCSS", 0, 0, 0, 0, 460, 0, 0, 0, 0, 0, 17, 82, 559
"c:\Users\<USER>\Desktop\game\admin\player_inventory.js", "JavaScript", 0, 0, 0, 0, 0, 556, 0, 0, 0, 0, 60, 125, 741
"c:\Users\<USER>\Desktop\game\admin\player_inventory.php", "PHP", 0, 0, 379, 0, 0, 0, 0, 0, 0, 0, 4, 24, 407
"c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.css", "PostCSS", 0, 0, 0, 0, 303, 0, 0, 0, 0, 0, 8, 54, 365
"c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.js", "JavaScript", 0, 0, 0, 0, 0, 236, 0, 0, 0, 0, 20, 36, 292
"c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.php", "PHP", 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 0, 11, 169
"c:\Users\<USER>\Desktop\game\admin\quest_editor.php", "PHP", 0, 0, 682, 0, 0, 0, 0, 0, 0, 0, 42, 94, 818
"c:\Users\<USER>\Desktop\game\admin\quests.php", "PHP", 0, 0, 753, 0, 0, 0, 0, 0, 0, 0, 31, 94, 878
"c:\Users\<USER>\Desktop\game\admin\recipes.css", "PostCSS", 0, 0, 0, 0, 93, 0, 0, 0, 0, 0, 4, 17, 114
"c:\Users\<USER>\Desktop\game\admin\recipes.js", "JavaScript", 0, 0, 0, 0, 0, 233, 0, 0, 0, 0, 17, 42, 292
"c:\Users\<USER>\Desktop\game\admin\recipes.php", "PHP", 0, 0, 242, 0, 0, 0, 0, 0, 0, 0, 21, 35, 298
"c:\Users\<USER>\Desktop\game\admin\refine_attribute_bonuses.php", "PHP", 0, 0, 211, 0, 0, 0, 0, 0, 0, 0, 7, 21, 239
"c:\Users\<USER>\Desktop\game\admin\refine_combos.php", "PHP", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 8, 24, 237
"c:\Users\<USER>\Desktop\game\admin\refine_elements.php", "PHP", 0, 0, 158, 0, 0, 0, 0, 0, 0, 0, 7, 18, 183
"c:\Users\<USER>\Desktop\game\admin\refine_scores.php", "PHP", 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 7, 19, 195
"c:\Users\<USER>\Desktop\game\admin\refine_tiers.php", "PHP", 0, 0, 449, 0, 0, 0, 0, 0, 0, 0, 30, 71, 550
"c:\Users\<USER>\Desktop\game\admin\scene_npcs.php", "PHP", 0, 0, 256, 0, 0, 0, 0, 0, 0, 0, 23, 37, 316
"c:\Users\<USER>\Desktop\game\admin\scenes.css", "PostCSS", 0, 0, 0, 0, 756, 0, 0, 0, 0, 0, 29, 94, 879
"c:\Users\<USER>\Desktop\game\admin\scenes.js", "JavaScript", 0, 0, 0, 0, 0, 1120, 0, 0, 0, 0, 110, 220, 1450
"c:\Users\<USER>\Desktop\game\admin\scenes.php", "PHP", 0, 0, 250, 0, 0, 0, 0, 0, 0, 0, 0, 19, 269
"c:\Users\<USER>\Desktop\game\admin\scenes_bak.js", "JavaScript", 0, 0, 0, 0, 0, 740, 0, 0, 0, 0, 56, 148, 944
"c:\Users\<USER>\Desktop\game\admin\security_violations.css", "PostCSS", 0, 0, 0, 0, 333, 0, 0, 0, 0, 0, 30, 68, 431
"c:\Users\<USER>\Desktop\game\admin\security_violations.php", "PHP", 0, 0, 290, 0, 0, 0, 0, 0, 0, 0, 14, 27, 331
"c:\Users\<USER>\Desktop\game\admin\shop_config_modal.php", "PHP", 0, 0, 38, 0, 0, 0, 0, 0, 0, 0, 0, 5, 43
"c:\Users\<USER>\Desktop\game\admin\skills.php", "PHP", 0, 0, 779, 0, 0, 0, 0, 0, 0, 0, 30, 94, 903
"c:\Users\<USER>\Desktop\game\admin\style.css", "PostCSS", 0, 0, 0, 0, 809, 0, 0, 0, 0, 0, 17, 82, 908
"c:\Users\<USER>\Desktop\game\api\get_players.php", "PHP", 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 1, 4, 19
"c:\Users\<USER>\Desktop\game\api\security_violation.php", "PHP", 0, 0, 90, 0, 0, 0, 0, 0, 0, 0, 15, 20, 125
"c:\Users\<USER>\Desktop\game\classes\BattleSystem.php", "PHP", 0, 0, 1021, 0, 0, 0, 0, 0, 0, 0, 263, 234, 1518
"c:\Users\<USER>\Desktop\game\classes\ChatHandler.php", "PHP", 0, 0, 344, 0, 0, 0, 0, 0, 0, 0, 98, 87, 529
"c:\Users\<USER>\Desktop\game\classes\CraftingManager.php", "PHP", 0, 0, 336, 0, 0, 0, 0, 0, 0, 0, 110, 59, 505
"c:\Users\<USER>\Desktop\game\classes\DialogueScriptProcessor.php", "PHP", 0, 0, 425, 0, 0, 0, 0, 0, 0, 0, 86, 91, 602
"c:\Users\<USER>\Desktop\game\classes\GemCraftingManager.php", "PHP", 0, 0, 388, 0, 0, 0, 0, 0, 0, 0, 117, 69, 574
"c:\Users\<USER>\Desktop\game\classes\LootManager.php", "PHP", 0, 0, 233, 0, 0, 0, 0, 0, 0, 0, 78, 59, 370
"c:\Users\<USER>\Desktop\game\classes\MessageProtocol.php", "PHP", 0, 0, 166, 0, 0, 0, 0, 0, 0, 0, 24, 14, 204
"c:\Users\<USER>\Desktop\game\classes\NPCManager.php", "PHP", 0, 0, 329, 0, 0, 0, 0, 0, 0, 0, 124, 84, 537
"c:\Users\<USER>\Desktop\game\classes\PvpBattleSystem.php", "PHP", 0, 0, 1093, 0, 0, 0, 0, 0, 0, 0, 243, 223, 1559
"c:\Users\<USER>\Desktop\game\classes\QuestManager.php", "PHP", 0, 0, 976, 0, 0, 0, 0, 0, 0, 0, 227, 203, 1406
"c:\Users\<USER>\Desktop\game\classes\RefineCalculator.php", "PHP", 0, 0, 167, 0, 0, 0, 0, 0, 0, 0, 34, 46, 247
"c:\Users\<USER>\Desktop\game\classes\SceneManager.php", "PHP", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 11, 37, 253
"c:\Users\<USER>\Desktop\game\classes\SecureMessageProtocol.php", "PHP", 0, 0, 34, 0, 0, 0, 0, 0, 0, 0, 13, 13, 60
"c:\Users\<USER>\Desktop\game\composer.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 5, 0, 0, 1, 6
"c:\Users\<USER>\Desktop\game\composer.lock", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 82, 0, 0, 1, 83
"c:\Users\<USER>\Desktop\game\config.ini", "Ini", 0, 0, 0, 0, 0, 0, 0, 11, 0, 0, 8, 8, 27
"c:\Users\<USER>\Desktop\game\config\ConnectionPool.php", "PHP", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 6, 18, 115
"c:\Users\<USER>\Desktop\game\config\Database.php", "PHP", 0, 0, 74, 0, 0, 0, 0, 0, 0, 0, 14, 13, 101
"c:\Users\<USER>\Desktop\game\config\RedisManager.php", "PHP", 0, 0, 56, 0, 0, 0, 0, 0, 0, 0, 19, 10, 85
"c:\Users\<USER>\Desktop\game\config\config.php", "PHP", 0, 0, 8, 0, 0, 0, 0, 0, 0, 0, 13, 9, 30
"c:\Users\<USER>\Desktop\game\config\formulas.php", "PHP", 0, 0, 464, 0, 0, 0, 0, 0, 0, 0, 248, 140, 852
"c:\Users\<USER>\Desktop\game\css\c.css", "PostCSS", 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 3, 1, 28
"c:\Users\<USER>\Desktop\game\css\g.css", "PostCSS", 0, 0, 0, 0, 1096, 0, 0, 0, 0, 0, 26, 158, 1280
"c:\Users\<USER>\Desktop\game\css\n.css", "PostCSS", 0, 0, 0, 0, 302, 0, 0, 0, 0, 0, 8, 52, 362
"c:\Users\<USER>\Desktop\game\css\p.css", "PostCSS", 0, 0, 0, 0, 802, 0, 0, 0, 0, 0, 20, 132, 954
"c:\Users\<USER>\Desktop\game\css\q.css", "PostCSS", 0, 0, 0, 0, 340, 0, 0, 0, 0, 0, 13, 59, 412
"c:\Users\<USER>\Desktop\game\css\r.css", "PostCSS", 0, 0, 0, 0, 59, 0, 0, 0, 0, 0, 0, 12, 71
"c:\Users\<USER>\Desktop\game\database\deploy_gem_crafting.sql", "MS SQL", 0, 0, 0, 0, 0, 0, 72, 0, 0, 0, 27, 20, 119
"c:\Users\<USER>\Desktop\game\database\game_battle.sql", "MS SQL", 0, 0, 0, 0, 0, 0, 839, 0, 0, 0, 580, 279, 1698
"c:\Users\<USER>\Desktop\game\database\gem_crafting_tables.sql", "MS SQL", 0, 0, 0, 0, 0, 0, 64, 0, 0, 0, 13, 9, 86
"c:\Users\<USER>\Desktop\game\database\security_tables.sql", "MS SQL", 0, 0, 0, 0, 0, 0, 55, 0, 0, 0, 5, 9, 69
"c:\Users\<USER>\Desktop\game\game.html", "HTML", 0, 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 1, 24
"c:\Users\<USER>\Desktop\game\index.html", "HTML", 0, 0, 0, 55, 0, 0, 0, 0, 0, 0, 0, 3, 58
"c:\Users\<USER>\Desktop\game\js\MessageProtocol.js", "JavaScript", 0, 0, 0, 0, 0, 130, 0, 0, 0, 0, 9, 8, 147
"c:\Users\<USER>\Desktop\game\js\SecureMessageProtocol.js", "JavaScript", 0, 0, 0, 0, 0, 103, 0, 0, 0, 0, 11, 19, 133
"c:\Users\<USER>\Desktop\game\js\UnencryptedMessageProtocol.js", "JavaScript", 0, 0, 0, 0, 0, 24, 0, 0, 0, 0, 0, 0, 24
"c:\Users\<USER>\Desktop\game\js\anti-debug-gentle.js", "JavaScript", 0, 0, 0, 0, 0, 231, 0, 0, 0, 0, 76, 57, 364
"c:\Users\<USER>\Desktop\game\js\building-manager.js", "JavaScript", 0, 0, 0, 0, 0, 2434, 0, 0, 0, 0, 574, 443, 3451
"c:\Users\<USER>\Desktop\game\js\crypto-js.min.js", "JavaScript", 0, 0, 0, 0, 0, 1, 0, 0, 0, 0, 0, 0, 1
"c:\Users\<USER>\Desktop\game\js\debug-loader.php", "PHP", 0, 0, 155, 0, 0, 0, 0, 0, 0, 0, 21, 39, 215
"c:\Users\<USER>\Desktop\game\js\debug.js", "JavaScript", 0, 0, 0, 0, 0, 2, 0, 0, 0, 0, 1, 1, 4
"c:\Users\<USER>\Desktop\game\js\gameclient.js", "JavaScript", 0, 0, 0, 0, 0, 4515, 0, 0, 0, 0, 651, 834, 6000
"c:\Users\<USER>\Desktop\game\js\html.js", "JavaScript", 0, 0, 0, 0, 0, 469, 0, 0, 0, 0, 29, 25, 523
"c:\Users\<USER>\Desktop\game\js\js.php", "PHP", 0, 0, 169, 0, 0, 0, 0, 0, 0, 0, 35, 37, 241
"c:\Users\<USER>\Desktop\game\js\npc-manager.js", "JavaScript", 0, 0, 0, 0, 0, 567, 0, 0, 0, 0, 228, 136, 931
"c:\Users\<USER>\Desktop\game\js\pvp-manager.js", "JavaScript", 0, 0, 0, 0, 0, 1062, 0, 0, 0, 0, 381, 260, 1703
"c:\Users\<USER>\Desktop\game\js\quest-manager.js", "JavaScript", 0, 0, 0, 0, 0, 716, 0, 0, 0, 0, 242, 165, 1123
"c:\Users\<USER>\Desktop\game\js\secure-config.php", "PHP", 0, 0, 140, 0, 0, 0, 0, 0, 0, 0, 73, 55, 268
"c:\Users\<USER>\Desktop\game\js\skill-manager.js", "JavaScript", 0, 0, 0, 0, 0, 301, 0, 0, 0, 0, 102, 83, 486
"c:\Users\<USER>\Desktop\game\js\updatejs.php", "PHP", 0, 0, 354, 0, 0, 0, 0, 0, 0, 0, 54, 61, 469
"c:\Users\<USER>\Desktop\game\server\cache_control.php", "PHP", 0, 0, 68, 0, 0, 0, 0, 0, 0, 0, 31, 15, 114
"c:\Users\<USER>\Desktop\game\server\crafting_handlers.php", "PHP", 0, 0, 30, 0, 0, 0, 0, 0, 0, 0, 28, 13, 71
"c:\Users\<USER>\Desktop\game\server\gem_crafting_handlers.php", "PHP", 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 28, 14, 73
"c:\Users\<USER>\Desktop\game\server\improved_websocket_server.php", "PHP", 0, 0, 4843, 0, 0, 0, 0, 0, 0, 0, 881, 1018, 6742
"c:\Users\<USER>\Desktop\game\server\pvp_handler.php", "PHP", 0, 0, 1170, 0, 0, 0, 0, 0, 0, 0, 423, 312, 1905
"c:\Users\<USER>\Desktop\game\server\refine_handlers.php", "PHP", 0, 0, 357, 0, 0, 0, 0, 0, 0, 0, 101, 87, 545
"c:\Users\<USER>\Desktop\game\server\websocket_server.php", "PHP", 0, 0, 234, 0, 0, 0, 0, 0, 0, 0, 19, 61, 314
"c:\Users\<USER>\Desktop\game\start_game.sh", "Shell Script", 0, 22, 0, 0, 0, 0, 0, 0, 0, 0, 7, 8, 37
"c:\Users\<USER>\Desktop\game\vendor\autoload.php", "PHP", 0, 0, 20, 0, 0, 0, 0, 0, 0, 0, 1, 5, 26
"c:\Users\<USER>\Desktop\game\vendor\composer\ClassLoader.php", "PHP", 0, 0, 286, 0, 0, 0, 0, 0, 0, 0, 235, 59, 580
"c:\Users\<USER>\Desktop\game\vendor\composer\InstalledVersions.php", "PHP", 0, 0, 178, 0, 0, 0, 0, 0, 0, 0, 133, 49, 360
"c:\Users\<USER>\Desktop\game\vendor\composer\autoload_classmap.php", "PHP", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 1, 4, 11
"c:\Users\<USER>\Desktop\game\vendor\composer\autoload_namespaces.php", "PHP", 0, 0, 5, 0, 0, 0, 0, 0, 0, 0, 1, 4, 10
"c:\Users\<USER>\Desktop\game\vendor\composer\autoload_psr4.php", "PHP", 0, 0, 6, 0, 0, 0, 0, 0, 0, 0, 1, 4, 11
"c:\Users\<USER>\Desktop\game\vendor\composer\autoload_real.php", "PHP", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 4, 10, 39
"c:\Users\<USER>\Desktop\game\vendor\composer\autoload_static.php", "PHP", 0, 0, 28, 0, 0, 0, 0, 0, 0, 0, 1, 8, 37
"c:\Users\<USER>\Desktop\game\vendor\composer\installed.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 72, 0, 0, 1, 73
"c:\Users\<USER>\Desktop\game\vendor\composer\installed.php", "PHP", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 0, 1, 33
"c:\Users\<USER>\Desktop\game\vendor\composer\platform_check.php", "PHP", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 1, 5, 27
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\.github\FUNDING.yml", "YAML", 0, 0, 0, 0, 0, 0, 0, 0, 0, 2, 1, 2, 5
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Autoloader.php", "PHP", 0, 0, 32, 0, 0, 0, 0, 0, 0, 0, 32, 5, 69
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\AsyncTcpConnection.php", "PHP", 0, 0, 222, 0, 0, 0, 0, 0, 0, 0, 127, 30, 379
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\AsyncUdpConnection.php", "PHP", 0, 0, 115, 0, 0, 0, 0, 0, 0, 0, 71, 18, 204
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\ConnectionInterface.php", "PHP", 0, 0, 25, 0, 0, 0, 0, 0, 0, 0, 87, 15, 127
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\TcpConnection.php", "PHP", 0, 0, 517, 0, 0, 0, 0, 0, 0, 0, 380, 87, 984
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\UdpConnection.php", "PHP", 0, 0, 91, 0, 0, 0, 0, 0, 0, 0, 101, 17, 209
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Ev.php", "PHP", 0, 0, 108, 0, 0, 0, 0, 0, 0, 0, 68, 14, 190
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Event.php", "PHP", 0, 0, 121, 0, 0, 0, 0, 0, 0, 0, 69, 26, 216
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\EventInterface.php", "PHP", 0, 0, 17, 0, 0, 0, 0, 0, 0, 0, 78, 13, 108
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Libevent.php", "PHP", 0, 0, 132, 0, 0, 0, 0, 0, 0, 0, 69, 25, 226
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\Base.php", "PHP", 0, 0, 131, 0, 0, 0, 0, 0, 0, 0, 106, 28, 265
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\ExtEventLoop.php", "PHP", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 16, 3, 28
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\ExtLibEventLoop.php", "PHP", 0, 0, 10, 0, 0, 0, 0, 0, 0, 0, 16, 2, 28
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\StreamSelectLoop.php", "PHP", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 16, 2, 27
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Select.php", "PHP", 0, 0, 218, 0, 0, 0, 0, 0, 0, 0, 107, 33, 358
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Swoole.php", "PHP", 0, 0, 205, 0, 0, 0, 0, 0, 0, 0, 60, 19, 284
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Uv.php", "PHP", 0, 0, 148, 0, 0, 0, 0, 0, 0, 0, 90, 23, 261
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Lib\Constants.php", "PHP", 0, 0, 21, 0, 0, 0, 0, 0, 0, 0, 18, 6, 45
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Lib\Timer.php", "PHP", 0, 0, 3, 0, 0, 0, 0, 0, 0, 0, 18, 1, 22
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Frame.php", "PHP", 0, 0, 23, 0, 0, 0, 0, 0, 0, 0, 34, 5, 62
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http.php", "PHP", 0, 0, 212, 0, 0, 0, 0, 0, 0, 0, 89, 23, 324
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Chunk.php", "PHP", 0, 0, 14, 0, 0, 0, 0, 0, 0, 0, 30, 4, 48
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Request.php", "PHP", 0, 0, 412, 0, 0, 0, 0, 0, 0, 0, 237, 46, 695
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Response.php", "PHP", 0, 0, 244, 0, 0, 0, 0, 0, 0, 0, 176, 39, 459
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\ServerSentEvents.php", "PHP", 0, 0, 31, 0, 0, 0, 0, 0, 0, 0, 30, 3, 64
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session.php", "PHP", 0, 0, 188, 0, 0, 0, 0, 0, 0, 0, 226, 48, 462
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\FileSessionHandler.php", "PHP", 0, 0, 92, 0, 0, 0, 0, 0, 0, 0, 76, 15, 183
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\RedisClusterSessionHandler.php", "PHP", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 15, 6, 47
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\RedisSessionHandler.php", "PHP", 0, 0, 80, 0, 0, 0, 0, 0, 0, 0, 55, 20, 155
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\SessionHandlerInterface.php", "PHP", 0, 0, 12, 0, 0, 0, 0, 0, 0, 0, 93, 10, 115
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\ProtocolInterface.php", "PHP", 0, 0, 9, 0, 0, 0, 0, 0, 0, 0, 39, 5, 53
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Text.php", "PHP", 0, 0, 26, 0, 0, 0, 0, 0, 0, 0, 40, 4, 70
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Websocket.php", "PHP", 0, 0, 395, 0, 0, 0, 0, 0, 0, 0, 127, 41, 563
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Ws.php", "PHP", 0, 0, 311, 0, 0, 0, 0, 0, 0, 0, 94, 28, 433
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\README.md", "Markdown", 255, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 88, 343
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Timer.php", "PHP", 0, 0, 112, 0, 0, 0, 0, 0, 0, 0, 85, 24, 221
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Worker.php", "PHP", 0, 0, 1643, 0, 0, 0, 0, 0, 0, 0, 871, 244, 2758
"c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\composer.json", "JSON", 0, 0, 0, 0, 0, 0, 0, 0, 38, 0, 0, 1, 39
"c:\Users\<USER>\Desktop\game\游戏世界NPC设计.md", "Markdown", 434, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 48, 482
"c:\Users\<USER>\Desktop\game\游戏世界任务设计 - 副本.md", "Markdown", 1815, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 186, 2001
"c:\Users\<USER>\Desktop\game\游戏世界任务设计.md", "Markdown", 424, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 58, 482
"c:\Users\<USER>\Desktop\game\游戏世界地图设计.md", "Markdown", 438, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 97, 535
"c:\Users\<USER>\Desktop\game\游戏任务系统实现设计.md", "Markdown", 371, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 63, 434
"Total", "-", 3791, 22, 35807, 85, 6493, 14779, 1030, 12, 197, 2, 12214, 11079, 85511