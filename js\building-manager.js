/**
 * BuildingManager.js
 * 管理游戏中的建筑系统，包括商店、铁匠铺等
 */

class BuildingManager {
    constructor(gameClient) {
        this.gameClient = gameClient; // 保存对主游戏客户端的引用
        this.currentBuilding = null; // 当前正在交互的建筑
        this.buildingView = null; // 建筑视图DOM元素
        this.buildingNameEl = null; // 建筑名称DOM元素
        this.buildingContentEl = null; // 建筑内容DOM元素
        this.sellMode = false; // 是否处于出售模式
        this.waitingForInventory = false; // 是否正在等待背包数据
        this.currentWarehouseTab = 'storage'; // 记录当前仓库标签页
        this.lastWarehouseAction = null; // 记录最后的仓库操作类型
        this.warehouseFilter = 'All'; // 仓库分类筛选
        this.depositFilter = 'All'; // 存入物品分类筛选
        this.lastWarehouseOperationTime = null; // 最后仓库操作时间
        
        // 分页和筛选相关
        this.currentPage = 1; // 当前页码
        this.itemsPerPage = 10; // 每页显示的物品数量
        this.currentCategory = 'all'; // 当前选中的分类
        this.sellNotification = null; // 出售通知消息
        
        // 分类映射表（英文到中文）
        this.categoryMap = {
            'all': '全部',
            'Equipment': '装备',
            'Gem': '宝石',
            'Material': '材料',
            'Potion': '药品',
            'Misc': '杂物',
            'Scroll': '书卷',
            'Rune': '符石',
            'Other': '其他'
        };
        
        // 合成建筑相关变量
        this.recipes = []; // 可用配方列表
        
        // 初始化DOM引用
        this.initDomReferences();
    }
    
    /**
     * 初始化DOM元素引用
     */
    initDomReferences() {
        this.buildingView = document.getElementById('buildingView');
        this.buildingNameEl = document.getElementById('buildingName');
        this.buildingContentEl = document.getElementById('buildingContent');
    }
    
    /**
     * 处理服务器发送的建筑数据
     * @param {Object} payload - 服务器发送的建筑数据
     */
    handleBuildingData(payload) {
        console.log('BuildingManager.handleBuildingData - 接收到建筑数据:', payload);
        
        if (!payload || !payload.type) {
            console.error('BuildingManager.handleBuildingData - 错误: 无效的建筑数据');
            return;
        }
        
        // 保存当前的状态，以便在刷新操作时恢复
        const previousPage = this.currentPage;
        const previousCategory = this.currentCategory;
        const previousSellMode = this.sellMode;
        
        this.currentBuilding = payload;
        
        // 只有在非刷新操作时才重置分页和筛选
        if (!payload.is_refresh) {
            this.currentPage = 1;
            this.currentCategory = 'all';
        } else {
            // 如果是刷新操作，保留当前页码和筛选状态
            this.currentPage = previousPage;
            this.currentCategory = previousCategory;
        }
        
        switch (payload.type) {
            case 'SHOP':
                console.log('BuildingManager.handleBuildingData - 处理商店数据');
                this.showShopView(payload);
                break;
            case 'DIAMOND_SHOP':
                console.log('BuildingManager.handleBuildingData - 处理钻石商店数据');
                this.showDiamondShopView(payload);
                break;
            case 'REVIVE_POINT':
                console.log('BuildingManager.handleBuildingData - 处理复活点数据');
                this.showRevivePointView(payload);
                break;
            case 'TELEPORTER':
                console.log('BuildingManager.handleBuildingData - 处理传送点数据');
                this.showTeleporterView(payload);
                break;
            case 'CRAFTING':
                console.log('BuildingManager.handleBuildingData - 处理合成建筑数据');
                this.requestRecipeData(payload);
                break;
            case 'GEM_CRAFTING':
                console.log('BuildingManager.handleBuildingData - 处理宝石合成建筑数据');
                this.requestGemRecipeData(payload);
                break;
            case 'REFINE':
                console.log('BuildingManager.handleBuildingData - 处理凝练建筑数据');
                this.requestRefineMaterials(payload);
                break;
            case 'WAREHOUSE':
                console.log('BuildingManager.handleBuildingData - 处理仓库建筑数据');
                this.showWarehouseView(payload);
                break;
            case 'ATTRIBUTE_RESET':
                console.log('BuildingManager.handleBuildingData - 处理属性重修建筑数据');
                this.showAttributeResetView(payload);
                break;
            default:
                console.error('BuildingManager.handleBuildingData - 错误: 未知的建筑类型:', payload.type);
                this.gameClient.addLog(`未知的建筑类型: ${payload.type}`);
        }
    }
    
    /**
     * 处理商店通知消息
     * @param {Object} payload - 通知数据
     */
    handleShopNotification(payload) {
        console.log('BuildingManager.handleShopNotification - 接收到商店通知:', payload);
        
        if (!this.buildingContentEl) return;
        
        // 保存通知内容，以便在切换页面或筛选时保持显示
        this.sellNotification = {
            message: payload.message,
            type: payload.type
        };
        
        // 移除旧的通知
        const oldNotification = this.buildingContentEl.querySelector('.shop-notification');
        if (oldNotification) {
            oldNotification.remove();
        }
        
        // 创建新的通知
        const notification = document.createElement('div');
        notification.className = 'shop-notification';
        notification.textContent = payload.message;
        
        if (payload.type === 'success') {
            notification.classList.add('shop-notification-success');
        } else if (payload.type === 'error') {
            notification.classList.add('shop-notification-error');
        }
        
        // 将通知插入到商店内容顶部
        if (this.buildingContentEl.firstChild) {
            this.buildingContentEl.insertBefore(notification, this.buildingContentEl.firstChild);
        } else {
            this.buildingContentEl.appendChild(notification);
        }
        
        // 5秒后自动移除通知
        setTimeout(() => {
            notification.remove();
            this.sellNotification = null;
        }, 5000);
    }
    
    /**
     * 处理购买结果消息
     * @param {string} message - 消息内容
     * @param {string} context - 消息上下文
     */
    handleBuyResult(message, context) {
        if (!this.buildingContentEl) return;
        
        // 保存通知内容，以便在切换页面或筛选时保持显示
        this.sellNotification = {
            message: message,
            type: context === 'item_buy_success' ? 'success' : 'error'
        };
        
        // 移除旧的通知
        const oldNotification = this.buildingContentEl.querySelector('.shop-notification');
        if (oldNotification) {
            oldNotification.remove();
        }
        
        // 创建新的通知
        const notification = document.createElement('div');
        notification.className = 'shop-notification';
        notification.textContent = message;
        
        if (context === 'item_buy_success') {
            notification.classList.add('shop-notification-success');
        } else if (context === 'item_buy_fail') {
            notification.classList.add('shop-notification-error');
        }
        
        // 将通知插入到商店内容顶部
        if (this.buildingContentEl.firstChild) {
            this.buildingContentEl.insertBefore(notification, this.buildingContentEl.firstChild);
        } else {
            this.buildingContentEl.appendChild(notification);
        }
        
        // 5秒后自动移除通知
        setTimeout(() => {
            notification.remove();
            this.sellNotification = null;
        }, 5000);
    }
    
    /**
     * 获取分类的中文名称
     * @param {string} category - 英文分类名
     * @returns {string} - 中文分类名
     */
    getCategoryDisplayName(category) {
        return this.categoryMap[category] || category;
    }
    
    /**
     * 显示钻石商店视图
     * @param {Object} data - 钻石商店数据
     */
    showDiamondShopView(data) {
        console.log('BuildingManager.showDiamondShopView - 显示钻石商店数据:', data);

        this.showBuildingView();

        this.buildingNameEl.textContent = data.name;

        // 钻石商店只有购买模式，没有出售功能
        let html = `
            <div class="diamond-shop-header">
                <div class="shop-title">💎 钻石商店 💎</div>
                <div class="shop-description">这里只接受钻石支付，不提供出售服务</div>
            </div>
        `;

        if (data.currencies) {
            html += `<div class="shop-currencies">
                <span class="currency-diamond">${data.currencies.diamonds} 钻石</span>
            </div>`;
        }

        // 如果有通知消息，显示它
        if (this.sellNotification) {
            const notificationClass = this.sellNotification.type === 'success' ? 'shop-notification-success' : 'shop-notification-error';
            html += `<div class="shop-notification ${notificationClass}">${this.sellNotification.message}</div>`;
        }

        // 只显示购买模式
        if (data.items && data.items.length > 0) {
            html += '<ul class="shop-items-list">';
            data.items.forEach(item => {
                const itemPrice = item.diamond_cost ? `${item.diamond_cost} 钻石` : '价格未知';
                const stockInfo = item.stock !== null ? `(库存: ${item.stock})` : '(库存: 无限)';

                // 构造物品数据用于详情查看
                const itemForDetail = {
                    name: item.name,
                    description: item.description || '暂无描述',
                    category: item.category || 'Misc',
                    effects: item.effects || null,
                    stats: item.stats || null,
                    slot: item.slot || null,
                    job_restriction: item.job_restriction || null,
                    job_restriction_name: item.job_restriction_name || null,
                    grants_job_id: item.grants_job_id || null,
                    granted_job_name: item.granted_job_name || null,
                    sockets: item.sockets || 0,
                    item_template_id: item.item_template_id,
                    diamond_cost: item.diamond_cost,
                    stock: item.stock
                };
                const itemJson = JSON.stringify(itemForDetail).replace(/"/g, '&quot;');

                html += `<li class="shop-item diamond-shop-item">
                    <div class="shop-item-info">
                        <span class="shop-item-name" style="cursor: pointer; color: #005a00;"
                              onclick="game.buildingManager.showShopItemDetail(${itemJson})">${item.name} ${stockInfo}</span>
                        <span class="shop-item-price diamond-price">${itemPrice}</span>
                    </div>
                    <div class="shop-item-actions">
                        <input type="number" min="1" max="${item.stock !== null ? item.stock : 99}" value="1"
                               class="shop-item-quantity" id="quantity-${item.item_template_id}" style="width: 40px;">
                        <button class="btn diamond-buy-btn" onclick="game.buildingManager.buyDiamondItem(${item.item_template_id},
                                  parseInt(document.getElementById('quantity-${item.item_template_id}').value || 1))">购买</button>
                    </div>
                </li>`;
            });
            html += '</ul>';
        } else {
            html += '<p>钻石商店目前没有商品。</p>';
        }

        this.buildingContentEl.innerHTML = html;
    }

    /**
     * 显示商店视图
     * @param {Object} data - 商店数据
     */
    showShopView(data) {
        console.log('BuildingManager.showShopView - 显示商店数据:', data);
        
        this.showBuildingView();
        
        this.buildingNameEl.textContent = data.name;
        
        // 切换按钮
        let html = `
            <div class="shop-mode-buttons">
                <button class="btn-primary buy-mode ${!this.sellMode ? 'active' : ''}" onclick="game.buildingManager.toggleShopMode(false)">购买</button>
                <button class="btn-primary sell-mode ${this.sellMode ? 'active' : ''}" onclick="game.buildingManager.toggleShopMode(true)">出售</button>
            </div>
            欢迎光临！${this.sellMode ? '想出售什么物品？' : '看看我们有什么好东西。'}
        `;
        
        if (data.currencies) {
            html += `<div class="shop-currencies">
                <span class="currency-gold">${data.currencies.gold} 金币</span>, <span class="currency-diamond">${data.currencies.diamonds} 钻石</span>
            </div>`;
        }
        
        // 如果有通知消息，显示它
        if (this.sellNotification) {
            const notificationClass = this.sellNotification.type === 'success' ? 'shop-notification-success' : 'shop-notification-error';
            html += `<div class="shop-notification ${notificationClass}">${this.sellNotification.message}</div>`;
        }
        
        if (!this.sellMode) {
            // 购买模式
            if (data.items && data.items.length > 0) {
                html += '<ul class="shop-items-list">';
                data.items.forEach(item => {
                    const itemPrice = item.gold_cost ? `${item.gold_cost} 金币` : '价格未知';
                    const stockInfo = item.stock !== null ? `(库存: ${item.stock})` : '(库存: 无限)';

                    // 构造物品数据用于详情查看
                    const itemForDetail = {
                        name: item.name,
                        description: item.description || '暂无描述',
                        category: item.category || 'Misc',
                        effects: item.effects || null,
                        stats: item.stats || null,
                        slot: item.slot || null,
                        job_restriction: item.job_restriction || null,
                        job_restriction_name: item.job_restriction_name || null,
                        grants_job_id: item.grants_job_id || null,
                        granted_job_name: item.granted_job_name || null,
                        sockets: item.sockets || 0,
                        item_template_id: item.item_template_id,
                        gold_cost: item.gold_cost,
                        stock: item.stock
                    };
                    const itemJson = JSON.stringify(itemForDetail).replace(/"/g, '&quot;');

                    html += `<li class="shop-item">
                        <div class="shop-item-info">
                            <span class="shop-item-name" style="cursor: pointer; color: #005a00;"
                                  onclick="game.buildingManager.showShopItemDetail(${itemJson})">${item.name} ${stockInfo}</span>
                            <span class="shop-item-price">${itemPrice}</span>
                        </div>
                        <div class="shop-item-actions">
                            <input type="number" min="1" max="${item.stock !== null ? item.stock : 99}" value="1"
                                   class="shop-item-quantity" id="quantity-${item.item_template_id}" style="width: 40px;">
                            <button class="btn" onclick="game.buildingManager.buyItem(${item.item_template_id},
                                      parseInt(document.getElementById('quantity-${item.item_template_id}').value || 1))">购买</button>
                        </div>
                    </li>`;
                });
                html += '</ul>';
            } else {
                html += '<p>商店目前没有商品。</p>';
            }
        } else {
            // 出售模式 - 显示玩家背包中可出售的物品
            // 获取玩家背包数据
            let inventory = [];
            if (this.gameClient.inventory) {
                if (this.gameClient.inventory.items && Array.isArray(this.gameClient.inventory.items)) {
                    inventory = this.gameClient.inventory.items;
                } else if (this.gameClient.inventory.backpack && Array.isArray(this.gameClient.inventory.backpack)) {
                    inventory = [
                        ...(this.gameClient.inventory.backpack || []),
                        ...(this.gameClient.inventory.equipped || [])
                    ];
                }
            }
            console.log('BuildingManager.showShopView - 玩家背包:', inventory);
            
            // 过滤掉已装备的物品，但允许出售装备类型的物品
            const sellableItems = inventory.filter(item => !item.is_equipped);
            
            if (sellableItems.length > 0) {
                // 获取所有可用的分类
                const uniqueCategories = [...new Set(sellableItems.map(item => item.category || 'Other'))];
                const categories = ['all', ...uniqueCategories];
                
                // 添加分类筛选
                html += '<div class="shop-category-filter" style="margin-bottom: 10px; text-align: center;">';
                categories.forEach(category => {
                    const isActive = this.currentCategory === category;
                    const displayName = this.getCategoryDisplayName(category);
                    html += `<button class="btn ${isActive ? 'active' : ''}" 
                             onclick="game.buildingManager.filterByCategory('${category}')">${displayName}</button> `;
                });
                html += '</div>';
                
                // 根据当前分类筛选物品
                let filteredItems = sellableItems;
                if (this.currentCategory !== 'all') {
                    filteredItems = sellableItems.filter(item => (item.category || 'Other') === this.currentCategory);
                }
                
                // 计算总页数
                const totalPages = Math.ceil(filteredItems.length / this.itemsPerPage);
                
                // 确保当前页码在有效范围内
                if (this.currentPage > totalPages) {
                    this.currentPage = totalPages;
                }
                if (this.currentPage < 1) {
                    this.currentPage = 1;
                }
                
                // 获取当前页的物品
                const startIndex = (this.currentPage - 1) * this.itemsPerPage;
                const endIndex = Math.min(startIndex + this.itemsPerPage, filteredItems.length);
                const currentPageItems = filteredItems.slice(startIndex, endIndex);
                
                if (currentPageItems.length > 0) {
                    html += '<ul class="shop-items-list">';
                    currentPageItems.forEach(item => {
                        // 确保有出售价格
                        if (!item.sell_price && item.item_template_id) {
                            // 尝试从模板中获取出售价格
                            const templateItem = this.gameClient.itemTemplates ? 
                                this.gameClient.itemTemplates.find(t => t.id === item.item_template_id) : null;
                            if (templateItem && templateItem.sell_price) {
                                item.sell_price = templateItem.sell_price;
                            }
                        }
                        
                        console.log('出售物品数据:', item);
                        const sellPrice = item.sell_price ? `${item.sell_price} 金币` : '不可出售';
                        const canSell = item.sell_price && !item.is_equipped; // 允许出售装备，但不能出售已装备的物品
                        const categoryText = item.category ? `[${this.getCategoryDisplayName(item.category)}]` : '';

                        
                        html += `<li class="shop-item">
                            <div class="shop-item-info">
                                <span class="shop-item-name">${categoryText} ${item.name} (拥有: ${item.quantity})</span>
                                <span class="shop-item-price">${sellPrice}</span>
                            </div>`;
                            
                        if (canSell) {
                            html += `<div class="shop-item-actions">
                                <input type="number" min="1" max="${item.quantity}" value="1" 
                                       class="shop-item-quantity" id="sell-quantity-${item.inventory_id}" style="width: 40px;">
                                <button class="btn" onclick="game.buildingManager.sellItem(${item.inventory_id}, 
                                          parseInt(document.getElementById('sell-quantity-${item.inventory_id}').value || 1))">出售</button>
                            </div>`;
                        } else {
                            html += `<div class="shop-item-actions">
                                <span class="shop-item-unsellable">不可出售</span>
                            </div>`;
                        }
                        
                        html += `</li>`;
                    });
                    html += '</ul>';
                    
                    // 添加分页控件
                    if (totalPages > 1) {
                        html += '<div class="shop-pagination" style="text-align: center; margin-top: 10px;">';
                        
                        // 上一页按钮
                        if (this.currentPage > 1) {
                            html += `<button class="btn" onclick="game.buildingManager.goToPage(${this.currentPage - 1})">上一页</button> `;
                        } else {
                            html += `<button class="btn" disabled>上一页</button> `;
                        }
                        
                        // 页码显示
                        html += `<span style="margin: 0 10px;">第 ${this.currentPage} 页 / 共 ${totalPages} 页</span>`;
                        
                        // 下一页按钮
                        if (this.currentPage < totalPages) {
                            html += `<button class="btn" onclick="game.buildingManager.goToPage(${this.currentPage + 1})">下一页</button>`;
                        } else {
                            html += `<button class="btn" disabled>下一页</button>`;
                        }
                        
                        html += '</div>';
                    }
                } else {
                    html += '<p>没有可用的材料。</p>';
                }
            } else {
                html += '<p>你的背包中没有可出售的物品。</p>';
            }
        }
        
        this.buildingContentEl.innerHTML = html;
    }
    
    /**
     * 切换商店模式（购买/出售）
     * @param {boolean} sellMode - 是否为出售模式
     */
    toggleShopMode(sellMode) {
        if (this.sellMode === sellMode) return; // 已经是该模式，无需切换
        
        this.sellMode = sellMode;
        // 重置分页和筛选
        this.currentPage = 1;
        this.currentCategory = 'all';
        
        console.log('BuildingManager.toggleShopMode - 切换到', sellMode ? '出售' : '购买', '模式');
        
        // 如果切换到出售模式，先获取最新的背包数据
        if (sellMode) {
            console.log('BuildingManager.toggleShopMode - 获取玩家背包数据');
            // 发送获取背包数据的请求
            this.gameClient.sendMessage(MessageProtocol.C2S_GET_INVENTORY, {
                player_id: this.gameClient.currentPlayer.id
            });
            
            // 显示加载中提示
            if (this.buildingContentEl) {
                this.buildingContentEl.innerHTML = '<p class="loading-text">正在加载背包数据...</p>';
            }
            
            // 设置一个标志，表示我们正在等待背包数据
            this.waitingForInventory = true;
            
            // 2秒后如果还没有收到背包数据，就使用当前数据显示
            setTimeout(() => {
                if (this.waitingForInventory) {
                    this.waitingForInventory = false;
                    if (this.currentBuilding) {
                        this.showShopView(this.currentBuilding);
                    }
                }
            }, 2000);
            
            return; // 不立即显示商店视图，等待背包数据
        }
        
        // 重新显示商店视图
        if (this.currentBuilding) {
            this.showShopView(this.currentBuilding);
        }
    }
    
    /**
     * 按分类筛选物品
     * @param {string} category - 物品分类
     */
    filterByCategory(category) {
        console.log('BuildingManager.filterByCategory - 筛选分类:', category);
        this.currentCategory = category;
        this.currentPage = 1; // 切换分类时重置页码
        
        if (this.currentBuilding) {
            this.showShopView(this.currentBuilding);
        }
    }
    
    /**
     * 跳转到指定页码
     * @param {number} page - 页码
     */
    goToPage(page) {
        console.log('BuildingManager.goToPage - 跳转到页码:', page);
        this.currentPage = page;
        
        if (this.currentBuilding) {
            this.showShopView(this.currentBuilding);
        }
    }
    
    /**
     * 处理背包数据更新
     * @param {Object} payload - 背包数据
     */
    handleInventoryUpdate(payload) {
        console.log('BuildingManager.handleInventoryUpdate - 接收到背包数据:', payload);

        // 如果我们正在等待背包数据，并且处于出售模式，则刷新商店视图
        if (this.waitingForInventory && this.sellMode && this.currentBuilding) {
            this.waitingForInventory = false;
            this.showShopView(this.currentBuilding);
        } else if (this.sellMode && this.currentBuilding && document.getElementById('buildingView').style.display === 'block') {
            // 如果当前正在显示商店的出售模式，则刷新视图以反映背包变化
            console.log('BuildingManager.handleInventoryUpdate - 刷新出售模式视图');
            this.showShopView(this.currentBuilding);
        }

        // 如果当前正在显示仓库界面，刷新存入物品列表
        if (this.currentBuilding && this.currentBuilding.type === 'WAREHOUSE' &&
            document.getElementById('buildingView').style.display === 'block') {

            // 保存当前提示消息状态
            const currentMessage = this.getCurrentWarehouseMessage();

            this.refreshWarehouseDepositTab();

            // 如果有待显示的仓库操作消息，显示它
            if (this.pendingWarehouseMessage) {
                setTimeout(() => {
                    this.showWarehouseMessage(this.pendingWarehouseMessage.message, this.pendingWarehouseMessage.type);
                    this.pendingWarehouseMessage = null;
                }, 100);
            } else if (currentMessage) {
                // 如果没有新消息但有当前消息，恢复它
                setTimeout(() => {
                    this.restoreWarehouseMessage(currentMessage);
                }, 100);
            }
        }
    }
    
    /**
     * 出售物品后处理
     * @param {number} inventoryId - 已出售物品的库存ID
     * @param {number} quantity - 出售的数量
     * @param {number} totalQuantity - 物品原有的总数量
     */
    handleItemSold(inventoryId, quantity, totalQuantity) {
        console.log('BuildingManager.handleItemSold - 物品已出售:', { inventoryId, quantity, totalQuantity });
        
        // 如果物品已全部出售（数量等于总数量），立即从界面移除该物品
        if (quantity === totalQuantity && this.buildingContentEl) {
            const itemElement = this.buildingContentEl.querySelector(`#sell-quantity-${inventoryId}`);
            if (itemElement && itemElement.closest('li.shop-item')) {
                const itemRow = itemElement.closest('li.shop-item');
                itemRow.style.transition = 'opacity 0.5s';
                itemRow.style.opacity = '0.3';
                
                // 添加"已售出"标记
                const infoDiv = itemRow.querySelector('.shop-item-info');
                if (infoDiv) {
                    const soldMark = document.createElement('div');
                    soldMark.className = 'item-sold-mark';
                    soldMark.textContent = '已售出';
                    soldMark.style.color = '#a02c2c';
                    soldMark.style.fontWeight = 'bold';
                    soldMark.style.position = 'absolute';
                    soldMark.style.right = '10px';
                    soldMark.style.top = '50%';
                    soldMark.style.transform = 'translateY(-50%)';
                    infoDiv.style.position = 'relative';
                    infoDiv.appendChild(soldMark);
                }
                
                // 禁用出售按钮和数量输入
                const quantityInput = itemRow.querySelector(`#sell-quantity-${inventoryId}`);
                const sellButton = itemRow.querySelector('button');
                if (quantityInput) quantityInput.disabled = true;
                if (sellButton) {
                    sellButton.disabled = true;
                    sellButton.textContent = '已售出';
                }
                
                // 2秒后移除该物品
                setTimeout(() => {
                    itemRow.style.transition = 'all 0.5s';
                    itemRow.style.height = '0';
                    itemRow.style.overflow = 'hidden';
                    itemRow.style.padding = '0';
                    itemRow.style.margin = '0';
                    itemRow.style.opacity = '0';
                    
                    // 再等0.5秒完全移除元素
                    setTimeout(() => {
                        itemRow.remove();
                    }, 500);
                }, 2000);
            }
        }
    }
    
    /**
     * 显示建筑视图（隐藏主视图）
     */
    showBuildingView() {
        document.getElementById('main-view').style.display = 'none';
        this.buildingView.style.display = 'block';

        // 确保仓库提示区域存在
        this.ensureWarehouseMessageArea();
    }
    
    /**
     * 隐藏建筑视图（显示主视图）
     */
    hideBuildingView() {
        this.buildingView.style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentBuilding = null;

        // 保持仓库标签页状态，下次打开时恢复用户的选择
        // 不重置 this.currentWarehouseTab，让用户的选择得以保持

        // 清理仓库提示
        this.clearWarehouseMessage();

        // 清理所有建筑特效
        this.clearAllBuildingEffects();
    }
    
    /**
     * 显示商店物品详情
     * @param {Object} item - 商店物品数据
     */
    showShopItemDetail(item) {
        console.log('BuildingManager.showShopItemDetail - 显示商店物品详情:', item);

        // 设置物品详情来源为商店
        this.gameClient.itemDetailOrigin = 'shop';

        // 隐藏建筑视图，显示物品详情视图
        document.getElementById('buildingView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'block';

        // 清除错误和成功消息
        const itemDetailError = document.getElementById('itemDetailError');
        const itemDetailSuccess = document.getElementById('itemDetailSuccess');
        itemDetailError.textContent = '';
        itemDetailError.style.borderColor = 'transparent';
        itemDetailSuccess.textContent = '';
        itemDetailSuccess.style.borderColor = 'transparent';

        // 显示物品名称
        const itemNameElement = document.getElementById('itemDetailName');
        itemNameElement.innerHTML = item.name;

        // 构建物品详情内容
        let contentHtml = '';

        // 显示分类
        const categoryDisplayName = this.gameClient.categoryMap[item.category] || item.category;
        contentHtml += `<p><b>分类:</b> ${categoryDisplayName}</p>`;

        // 显示物品描述
        if (item.description && item.description !== '暂无描述') {
            contentHtml += `<p style="color: #555;">${item.description}</p>`;
        }

        // 显示授予职业信息（如果有）
        if (item.granted_job_name) {
            contentHtml += `<p><b>授予职业:</b> <span style="color: #28a745; font-weight: bold;">${item.granted_job_name}</span></p>`;
        }

        // 显示职业限制信息（如果有）
        if (item.job_restriction_name) {
            contentHtml += `<p><b>职业要求:</b> <span style="color: #a02c2c; font-weight: bold;">[${item.job_restriction_name}]</span></p>`;
        }

        // 显示装备槽位信息（如果是装备）
        if (item.slot) {
            const slotMap = {
                'Head': '头部', 'Neck': '颈部', 'LeftHand': '左手', 'RightHand': '右手',
                'TwoHanded': '双手', 'Body': '身体', 'Finger': '手指', 'Back': '背部'
            };
            const slotName = slotMap[item.slot] || item.slot;
            contentHtml += `<p><b>装备部位:</b> <span style="color: #6f42c1; font-weight: bold;">${slotName}</span></p>`;
        }

        contentHtml += `<hr class="section-divider">`;

        // 显示装备属性（如果有stats）
        if (item.stats) {
            try {
                const stats = typeof item.stats === 'string' ? JSON.parse(item.stats) : item.stats;
                if (stats && Object.keys(stats).length > 0) {
                    contentHtml += '<b>装备属性:</b><ul style="margin-top: 5px; padding-left: 20px;">';
                    for (const [key, value] of Object.entries(stats)) {
                        const displayName = this.gameClient.playerAttributeMap[key] || key;
                        const sign = value > 0 ? '+' : '';
                        const color = value > 0 ? '#28a745' : '#dc3545';
                        contentHtml += `<li>${displayName}: <span style="color: ${color}; font-weight: bold;">${sign}${value}</span></li>`;
                    }
                    contentHtml += '</ul>';
                }
            } catch (e) {
                console.warn('解析装备属性失败:', e);
            }
        }

        // 显示插槽信息（如果有）
        if (item.sockets && item.sockets > 0) {
            contentHtml += `<p><b>宝石插槽:</b> <span style="color: #17a2b8; font-weight: bold;">${item.sockets} 个</span></p>`;
        }

        // 显示价格信息
        if (item.gold_cost) {
            contentHtml += `<p><b>价格:</b> <span style="color: #d4a017; font-weight: bold;">${item.gold_cost} 金币</span></p>`;
        } else if (item.diamond_cost) {
            contentHtml += `<p><b>价格:</b> <span style="color: #4169e1; font-weight: bold;">${item.diamond_cost} 钻石</span></p>`;
        }

        // 显示库存信息
        if (item.stock !== null) {
            const stockColor = item.stock > 10 ? '#28a745' : item.stock > 0 ? '#ffc107' : '#dc3545';
            contentHtml += `<p><b>库存:</b> <span style="color: ${stockColor}; font-weight: bold;">${item.stock}</span></p>`;
        } else {
            contentHtml += `<p><b>库存:</b> <span style="color: #28a745; font-weight: bold;">无限</span></p>`;
        }

        // 显示物品效果
        if (item.effects) {
            try {
                const effects = typeof item.effects === 'string' ? JSON.parse(item.effects) : item.effects;
                if (effects && Object.keys(effects).length > 0) {
                    contentHtml += '<hr class="section-divider"><b>物品效果:</b><ul style="margin-top: 5px; padding-left: 20px;">';
                    for (const [key, value] of Object.entries(effects)) {
                        const displayName = this.gameClient.playerAttributeMap[key] || key;
                        const sign = value > 0 ? '+' : '';
                        const color = value > 0 ? '#28a745' : '#dc3545';
                        contentHtml += `<li>${displayName}: <span style="color: ${color}; font-weight: bold;">${sign}${value}</span></li>`;
                    }
                    contentHtml += '</ul>';
                }
            } catch (e) {
                console.warn('解析物品效果失败:', e);
            }
        }

        document.getElementById('itemDetailContent').innerHTML = contentHtml;

        // 清空操作按钮区域（商店物品详情不需要操作按钮）
        document.getElementById('itemDetailActions').innerHTML = '';

        // 保存当前物品信息，以便返回时使用
        this.currentShopItem = item;
    }

    /**
     * 购买商品
     * @param {number} itemTemplateId - 物品模板ID
     * @param {number} quantity - 购买数量，默认为1
     */
    buyItem(itemTemplateId, quantity = 1) {
        console.log('BuildingManager.buyItem - 尝试购买物品:', { itemTemplateId, quantity });
        
        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.buyItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('购买失败：无法确定当前建筑');
            return;
        }
        
        // 发送购买请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            item_template_id: itemTemplateId,
            scene_building_id: this.currentBuilding.scene_building_id,
            quantity: quantity
        };
        
        console.log('BuildingManager.buyItem - 发送购买请求:', payload);
        this.gameClient.sendMessage(MessageProtocol.C2S_BUY_ITEM, payload);
    }

    /**
     * 购买钻石商品
     * @param {number} itemTemplateId - 物品模板ID
     * @param {number} quantity - 购买数量，默认为1
     */
    buyDiamondItem(itemTemplateId, quantity = 1) {
        console.log('BuildingManager.buyDiamondItem - 尝试购买钻石物品:', { itemTemplateId, quantity });

        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.buyDiamondItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('购买失败：无法确定当前建筑');
            return;
        }

        // 发送钻石购买请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            item_template_id: itemTemplateId,
            scene_building_id: this.currentBuilding.scene_building_id,
            quantity: quantity
        };

        console.log('BuildingManager.buyDiamondItem - 发送钻石购买请求:', payload);
        this.gameClient.sendMessage(MessageProtocol.C2S_BUY_DIAMOND_ITEM, payload);
    }

    /**
     * 出售物品
     * @param {number} inventoryId - 物品库存ID
     * @param {number} quantity - 出售数量，默认为1
     */
    sellItem(inventoryId, quantity = 1) {
        console.log('BuildingManager.sellItem - 尝试出售物品:', { inventoryId, quantity });
        
        // 检查物品ID是否有效
        if (!inventoryId || inventoryId === 'undefined' || inventoryId === undefined) {
            console.error('BuildingManager.sellItem - 错误: 无效的物品ID');
            this.gameClient.addLog('出售失败：无效的物品ID');
            
            // 显示错误通知
            const notification = document.createElement('div');
            notification.className = 'shop-notification shop-notification-error';
            notification.textContent = '出售失败：无效的物品ID';
            
            if (this.buildingContentEl) {
                if (this.buildingContentEl.firstChild) {
                    this.buildingContentEl.insertBefore(notification, this.buildingContentEl.firstChild);
                } else {
                    this.buildingContentEl.appendChild(notification);
                }
                
                setTimeout(() => notification.remove(), 5000);
            }
            return;
        }
        
        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.sellItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('出售失败：无法确定当前建筑');
            return;
        }
        
        // 获取物品当前总数量
        let totalQuantity = 0;
        const quantityInput = document.getElementById(`sell-quantity-${inventoryId}`);
        if (quantityInput) {
            totalQuantity = parseInt(quantityInput.max) || 0;
        }
        
        // 立即更新UI，标记物品为已售出状态
        if (quantity >= totalQuantity) {
            this.handleItemSold(inventoryId, quantity, totalQuantity);
        }
        
        // 发送出售请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            inventory_id: inventoryId,
            scene_building_id: this.currentBuilding.scene_building_id,
            quantity: quantity
        };
        
        console.log('BuildingManager.sellItem - 发送出售请求:', payload);
        this.gameClient.sendMessage(MessageProtocol.C2S_SELL_ITEM, payload);
    }
    
    /**
     * 更新场景中的建筑显示
     * @param {Object} scene - 场景数据
     */
    updateSceneBuildings(scene) {
        if (!scene) return;
        
        const buildingsEl = document.getElementById('sceneBuildings');
        const buildings = scene.buildings || [];
        buildingsEl.innerHTML = ''; // 清除之前的内容
        
        if (buildings.length > 0) {
            buildingsEl.style.display = 'block';
            let html = '<b style="vertical-align: middle;">这里有：</b>';
            
            html += buildings.map(building => {
                const buildingIcon = this.getBuildingIcon(building.type);
                const buildingLink = `<a href="#" onclick="event.preventDefault(); console.log('点击建筑:', ${building.scene_building_id}); game.sendMessage(MessageProtocol.C2S_GET_BUILDING_DATA, { scene_building_id: ${building.scene_building_id}, player_id: game.currentPlayer.id })" style="text-decoration: none;">${buildingIcon} ${building.name}</a>`;
                return `<span class="scene-building-group" style="display: inline-block; white-space: nowrap; margin: 0 4px; vertical-align: middle;">${buildingLink}</span>`;
            }).join(' ');
            
            buildingsEl.innerHTML = html;
            
            // 调试信息
            console.log('场景建筑数据:', buildings);
        } else {
            buildingsEl.style.display = 'none';
        }
    }
    
    /**
     * 根据建筑类型获取对应图标
     * @param {string} buildingType - 建筑类型
     * @returns {string} - 建筑图标
     */
    getBuildingIcon(buildingType) {
        switch (buildingType) {
            case 'SHOP':
                return '🏪';
            case 'DIAMOND_SHOP':
                return '💎';
            case 'FORGE':
                return '⚒️';
            case 'REVIVE_POINT':
                return '🔱';
            case 'TELEPORTER':
                return '🌀';
            case 'CRAFTING':
                return '🔨';
            case 'GEM_CRAFTING':
                return '💎';
            case 'REFINE':
                return '✨';
            case 'WAREHOUSE':
                return '📦';
            case 'ATTRIBUTE_RESET':
                return '🔄';
            default:
                return '🏢';
        }
    }
    
    /**
     * 显示复活点视图
     * @param {Object} data - 复活点数据
     */
    showRevivePointView(data) {
        console.log('BuildingManager.showRevivePointView - 显示复活点数据:', data);
        
        this.showBuildingView();
        
        this.buildingNameEl.textContent = data.name;
        
        // 构建复活点界面内容
        let html = `
            <div class="revive-point-container">
                <div class="blessing-message">
                    <p>${data.blessing_message}</p>
                </div>
                
                <div class="player-status">
                    <h3>你的状态</h3>
                    <div class="status-bars">
                        <div class="status-bar-container">
                            <div class="status-label">生命值:</div>
                            ${this.generateStatBarHtml(data.player_status.hp, data.player_status.max_hp, 'hp', 'revive-hp-bar')}
                        </div>
                        <div class="status-bar-container">
                            <div class="status-label">魔法值:</div>
                            ${this.generateStatBarHtml(data.player_status.mp, data.player_status.max_mp, 'mp', 'revive-mp-bar')}
                        </div>
                    </div>
                </div>`;
                
        // 如果玩家需要复活或者血量/魔法值不满，显示复活按钮
        if (data.needs_revival || data.player_status.hp < data.player_status.max_hp || data.player_status.mp < data.player_status.max_mp) {
            html += `
                <div class="revive-actions">
                    <button class="btn-primary" onclick="game.buildingManager.revivePlayer(${data.scene_building_id})">
                        ${data.needs_revival ? '[复活]' : '[恢复]'}
                    </button>
                </div>`;
        } else {
            html += `
                <div class="revive-message">
                    <p>你的状态已经是最佳的了，不需要复活或恢复。</p>
                </div>`;
        }
        
        html += `
        </div>`;
        this.buildingContentEl.innerHTML = html;
    }
    
    /**
     * 生成状态条HTML
     * @param {number} current - 当前值
     * @param {number} max - 最大值
     * @param {string} type - 类型（hp或mp）
     * @param {string} id - 元素ID
     * @returns {string} - 状态条HTML
     */
    generateStatBarHtml(current, max, type, id = '') {
        const perc = max > 0 ? Math.max(0, Math.min(100, (current / max) * 100)) : 0;
        const innerBarClass = type === 'hp' ? 'hp-bar-inner' : 'mp-bar-inner';
        const barId = id ? `id="${id}"` : '';
        return `
            <div class="hp-bar-container" ${barId} style="flex-grow: 1; margin-left: 10px;">
                <div class="${innerBarClass}" style="width: ${perc}%;"></div>
                <div class="hp-bar-text">${current} / ${max}</div>
            </div>
        `;
    }
    
    /**
     * 复活玩家
     * @param {number} sceneBuildingId - 场景建筑ID
     */
    revivePlayer(sceneBuildingId) {
        console.log('BuildingManager.revivePlayer - 尝试复活玩家:', { sceneBuildingId });
        
        if (!this.currentBuilding || this.currentBuilding.type !== 'REVIVE_POINT') {
            console.error('BuildingManager.revivePlayer - 错误: 不在复活点建筑中');
            return;
        }
        
        // 发送复活请求
        this.gameClient.sendMessage(MessageProtocol.C2S_REVIVE_IN_BUILDING, {
            scene_building_id: sceneBuildingId
        });
        
        // 显示复活动画
        this.showReviveAnimation();
    }
    
    /**
     * 显示复活动画
     */
    showReviveAnimation() {
        // 获取状态条元素
        const hpBar = document.getElementById('revive-hp-bar');
        const mpBar = document.getElementById('revive-mp-bar');
        
        if (hpBar && mpBar) {
            // 添加动画类
            hpBar.classList.add('healing');
            mpBar.classList.add('mana-restoring');
            
            // 创建恢复特效
            this.createHealingEffect(hpBar, 'hp');
            this.createHealingEffect(mpBar, 'mp');
            
            // 5秒后移除动画类
            setTimeout(() => {
                hpBar.classList.remove('healing');
                mpBar.classList.remove('mana-restoring');
            }, 5000);
        }
    }
    
    /**
     * 获取或创建独立的建筑特效容器
     */
    getOrCreateBuildingEffectContainer() {
        let effectContainer = document.getElementById('building-effect-container');
        if (!effectContainer) {
            effectContainer = document.createElement('div');
            effectContainer.id = 'building-effect-container';
            effectContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            `;
            document.body.appendChild(effectContainer);
            console.log('创建独立的建筑特效容器');
        }
        return effectContainer;
    }

    /**
     * 创建恢复特效
     * @param {HTMLElement} container - 容器元素
     * @param {string} type - 类型（hp或mp）
     */
    createHealingEffect(container, type) {
        // 获取独立的特效容器
        const effectContainer = this.getOrCreateBuildingEffectContainer();

        // 获取目标容器的绝对位置
        const containerRect = container.getBoundingClientRect();

        // 创建10个恢复粒子
        for (let i = 0; i < 10; i++) {
            setTimeout(() => {
                const particle = document.createElement('div');
                particle.className = type === 'hp' ? 'heal-particle' : 'mana-particle';

                // 计算相对于目标容器的随机位置
                const randomX = containerRect.left + Math.floor(Math.random() * containerRect.width);
                const randomY = containerRect.top + Math.floor(Math.random() * containerRect.height);

                // 设置样式
                particle.style.position = 'absolute';
                particle.style.left = `${randomX}px`;
                particle.style.top = `${randomY}px`;
                particle.style.width = '12px';
                particle.style.height = '12px';
                particle.style.borderRadius = '50%';
                particle.style.backgroundColor = type === 'hp' ? '#28a745' : '#007bff';
                particle.style.boxShadow = `0 0 8px ${type === 'hp' ? '#28a745' : '#007bff'}`;
                particle.style.opacity = '0.9';
                particle.style.pointerEvents = 'none';
                particle.style.transition = 'all 2s ease-out';
                particle.style.zIndex = '1001';

                // 添加到独立的特效容器
                effectContainer.appendChild(particle);

                // 开始动画
                setTimeout(() => {
                    particle.style.transform = 'translateY(-30px) scale(1.5)';
                    particle.style.opacity = '0';
                }, 100);

                // 移除粒子
                setTimeout(() => {
                    if (particle.parentNode === effectContainer) {
                        effectContainer.removeChild(particle);
                    }
                }, 2000);
            }, i * 150); // 每150毫秒创建一个粒子
        }
    }

    /**
     * 清理所有建筑特效
     */
    clearAllBuildingEffects() {
        const effectContainer = document.getElementById('building-effect-container');
        if (effectContainer) {
            const allEffects = effectContainer.querySelectorAll('.heal-particle, .mana-particle');
            allEffects.forEach(effect => {
                if (effect.parentNode) {
                    effect.parentNode.removeChild(effect);
                }
            });
            console.log(`已清理 ${allEffects.length} 个建筑特效`);
        }
    }

    /**
     * 测试建筑恢复特效（用于调试）
     */
    testBuildingHealingEffect() {
        console.log('测试建筑恢复特效...');

        // 测试HP恢复特效
        const hpBar = document.getElementById('revive-hp-bar');
        if (hpBar) {
            console.log('找到HP状态条，添加测试恢复特效');
            this.createHealingEffect(hpBar, 'hp');
        } else {
            console.log('未找到HP状态条');
        }

        // 测试MP恢复特效
        const mpBar = document.getElementById('revive-mp-bar');
        if (mpBar) {
            console.log('找到MP状态条，添加测试恢复特效');
            setTimeout(() => {
                this.createHealingEffect(mpBar, 'mp');
            }, 1000);
        } else {
            console.log('未找到MP状态条');
        }
    }

    /**
     * 显示传送点界面
     * @param {Object} data 传送点数据
     */
    showTeleporterView(data) {
        console.log('BuildingManager.showTeleporterView - 显示传送点界面:', data);
        this.showBuildingView();
        
        // 设置建筑名称
        this.buildingNameEl.textContent = data.name;
        
        let html = '<div class="teleporter-view">';
        html += `<p class="teleporter-description">请选择你想要传送的目的地：</p>`;
        html += '<div class="destinations-list">';
            
        if (data.destinations && data.destinations.length > 0) {
            data.destinations.forEach(dest => {
                // 检查是否有物品需求
                let requirementHtml = '';
                if (dest.required_item_id) {
                    requirementHtml = `<div class="item-requirement">
                        需要消耗: <span class="required-item">${dest.required_item_name} x${dest.required_quantity}</span>
                    </div>`;
                }
                
                html += `
                <div class="destination-item">
                    <h3>${dest.scene_name}</h3>
                    <!--<p>${dest.scene_description || '无描述'}</p>-->
                    ${requirementHtml}
                    <button class="btn-primary" onclick="game.buildingManager.teleportToDestination(${dest.scene_building_id}, '${dest.target_scene_id}')">传送</button>
                </div>`;
            });
        } else {
            html += `<p class="no-destinations">该传送点当前没有可用的目的地。</p>`;
        }
        
        html += '</div>';
        html += '</div>';
        
        this.buildingContentEl.innerHTML = html;
    }
    
    /**
     * 传送玩家到指定目的地
     * @param {number} sceneBuildingId 传送点建筑ID
     * @param {string} targetSceneId 目标场景ID
     */
    teleportToDestination(sceneBuildingId, targetSceneId) {
        console.log('BuildingManager.teleportToDestination - 传送玩家:', {sceneBuildingId, targetSceneId});
        
        if (!this.gameClient.isConnected()) {
            this.gameClient.addLog('无法连接服务器，请刷新页面重试。');
            return;
        }
        
        this.gameClient.sendMessage(MessageProtocol.C2S_TELEPORT, {
            player_id: this.gameClient.currentPlayer.id,
            scene_building_id: sceneBuildingId,
            target_scene_id: targetSceneId
        });
        
        this.hideBuildingView();
    }
    
    /**
     * 请求合成配方数据
     * @param {Object} data - 合成建筑数据
     */
    requestRecipeData(data) {
        console.log('BuildingManager.requestRecipeData - 请求合成配方数据');
        // 显示建筑基本信息
        this.showBuildingView();
        this.buildingNameEl.textContent = data.name;

        // 显示加载提示
        this.buildingContentEl.innerHTML = '<p class="loading-text">正在加载合成配方数据...</p>';

        // 请求可用配方列表
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_RECIPES, {
            scene_building_id: data.scene_building_id,
            player_id: this.gameClient.currentPlayer.id
        });
    }

    /**
     * 请求宝石合成配方数据
     * @param {Object} data - 宝石合成建筑数据
     */
    requestGemRecipeData(data) {
        console.log('BuildingManager.requestGemRecipeData - 请求宝石合成配方数据');
        // 显示建筑基本信息
        this.showBuildingView();
        this.buildingNameEl.textContent = data.name;

        // 显示加载提示
        this.buildingContentEl.innerHTML = '<p class="loading-text">正在加载宝石合成配方数据...</p>';

        // 请求可用宝石配方列表
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_GEM_RECIPES, {
            scene_building_id: data.scene_building_id,
            player_id: this.gameClient.currentPlayer.id
        });
    }
    
    /**
     * 处理服务器返回的配方数据
     * @param {Object} payload - 配方数据
     */
    handleRecipesData(payload) {
        console.log('BuildingManager.handleRecipesData - 接收到配方数据:', payload);

        this.recipes = payload.recipes || [];
        this.showCraftingView(payload);
    }

    /**
     * 处理服务器发送的宝石配方数据
     * @param {Object} payload - 服务器发送的宝石配方数据
     */
    handleGemRecipesData(payload) {
        console.log('BuildingManager.handleGemRecipesData - 接收到宝石配方数据:', payload);

        this.recipes = payload.recipes || [];
        this.showGemCraftingView(payload);
    }
    
    /**
     * 显示合成建筑界面
     * @param {Object} data - 配方及玩家数据
     */
    showCraftingView(data) {
        console.log('BuildingManager.showCraftingView - 显示合成界面:', data);
        
        // 保存配方数据
        this.recipes = data.recipes || [];
        const gold = data.player_gold || 0;
        
        // 构建合成界面内容
        let html = `
            <div class="crafting-view">
                <p>欢迎来到合成工坊，你可以在这里将材料合成为有用的物品。</p>
                <div class="shop-currencies">
                    <span class="currency-gold">${gold} 金币</span>
                </div>`;
        
        // 如果有合成消息，显示它
        if (this.sellNotification) {
            const notificationClass = this.sellNotification.type === 'success' ? 'shop-notification-success' : 'shop-notification-error';
            html += `<div class="shop-notification ${notificationClass}">${this.sellNotification.message}</div>`;
        }
        
        // 构建配方列表部分
        html += `
                <div class="recipes-section">
                    <h3>可用配方</h3>`;
        
        if (this.recipes.length > 0) {
            html += `<div class="recipes-list">`;
            
            this.recipes.forEach(recipe => {
                const recipeId = recipe.id;
                
                // 处理每个配方的材料信息
                let materialsAnalysis = [];
                recipe.materials.forEach(material => {
                    // 获取该材料的所有版本（绑定和不绑定）
                    const playerMaterialsForTemplate = data.player_materials.filter(pm => 
                        pm.item_template_id == material.material_item_id
                    );
                    
                    // 分别获取绑定和不绑定材料
                    const boundMaterials = playerMaterialsForTemplate.filter(pm => pm.is_bound == 1);
                    const unboundMaterials = playerMaterialsForTemplate.filter(pm => pm.is_bound == 0);
                    
                    // 计算绑定和不绑定材料的数量
                    const boundQuantity = boundMaterials.reduce((sum, pm) => sum + parseInt(pm.quantity || 0), 0);
                    const unboundQuantity = unboundMaterials.reduce((sum, pm) => sum + parseInt(pm.quantity || 0), 0);
                    
                    materialsAnalysis.push({
                        materialId: material.material_item_id,
                        name: material.name,
                        requiredQuantity: material.required_quantity,
                        boundQuantity: boundQuantity,
                        unboundQuantity: unboundQuantity,
                        hasBound: boundQuantity > 0,
                        hasUnbound: unboundQuantity > 0,
                        boundEnough: boundQuantity >= material.required_quantity,
                        unboundEnough: unboundQuantity >= material.required_quantity
                    });
                });
                
                // 检查是否可以使用不绑定材料合成
                const canCraftWithUnbound = materialsAnalysis.every(m => m.unboundEnough);
                
                // 检查是否可以使用绑定材料合成
                const canCraftWithBound = materialsAnalysis.every(m => m.boundEnough || m.unboundEnough);
                
                // 检查是否必须使用绑定材料（至少有一种材料只有绑定版本足够）
                const mustUseBound = canCraftWithBound && !canCraftWithUnbound;
                
                // 金币是否足够
                const hasEnoughGold = recipe.player_gold >= recipe.craft_fee;
                
                html += `
                    <div class="recipe-item">
                        <div class="recipe-header">
                            <span class="recipe-name">${recipe.name} (等级 ${recipe.craft_level})</span>
                            <span class="recipe-result">产出: ${recipe.result_item_name} x${recipe.result_quantity}</span>
                            <span class="recipe-fee">所需费用: ${recipe.craft_fee} 金币</span>
                        </div>`;
                
                // 如果金币不足，显示警告
                if (!hasEnoughGold) {
                    html += `<div class="recipe-warning">金币不足! 还需要 ${recipe.craft_fee - recipe.player_gold} 金币。</div>`;
                }
                
                // 创建两个合成选项卡：不绑定和绑定
                html += `
                    <div class="recipe-tabs">
                        <div class="tab-header">
                            <button class="tab-button active" onclick="game.buildingManager.switchCraftTab(this, 'unbound-${recipeId}')">使用不绑定材料</button>
                            <button class="tab-button" onclick="game.buildingManager.switchCraftTab(this, 'bound-${recipeId}')">使用绑定材料</button>
                        </div>
                        <div class="tab-content">`;
                
                // 不绑定材料选项卡
                html += `
                            <div id="unbound-${recipeId}" class="tab-pane active">
                                <div class="recipe-materials">
                                    <span class="recipe-materials-title">所需材料 (不绑定):</span>
                                    <ul class="recipe-materials-list">`;
                                    
                // 显示每种不绑定材料
                let hasInsufficientUnbound = false;
                materialsAnalysis.forEach(material => {
                    const materialClass = material.unboundEnough ? 'sufficient' : 'insufficient';
                    if (!material.unboundEnough) hasInsufficientUnbound = true;
                    
                    html += `
                        <li class="recipe-material ${materialClass}">
                            ${material.name}: ${material.unboundQuantity}/${material.requiredQuantity}
                        </li>`;
                });
                
                html += `
                                    </ul>
                                </div>`;
                                
                // 如果缺少不绑定材料，显示警告
                if (hasInsufficientUnbound) {
                    html += `<div class="recipe-warning">不绑定材料不足!</div>`;
                }
                
                // 不绑定合成按钮
                const unboundBtnClass = (canCraftWithUnbound && hasEnoughGold) ? 'btn-primary' : 'btn-disabled';
                const unboundBtnDisabled = (canCraftWithUnbound && hasEnoughGold) ? '' : 'disabled';
                
                html += `
                                <div class="recipe-actions">
                                    <button class="btn ${unboundBtnClass}" ${unboundBtnDisabled} 
                                        onclick="game.buildingManager.craftItem(${recipeId}, false)">
                                        合成(不绑定)
                                    </button>
                                </div>
                            </div>`;
                
                // 绑定材料选项卡
                html += `
                            <div id="bound-${recipeId}" class="tab-pane">
                                <div class="recipe-materials">
                                    <span class="recipe-materials-title">所需材料 (优先使用绑定):</span>
                                    <ul class="recipe-materials-list">`;
                                    
                // 显示每种材料（优先使用绑定）
                let hasInsufficientTotal = false;
                materialsAnalysis.forEach(material => {
                    // 显示绑定数量
                    if (material.hasBound) {
                        const boundSufficient = material.boundQuantity >= material.requiredQuantity;
                        const boundClass = boundSufficient ? 'sufficient' : 'partial';
                        
                        html += `
                            <li class="recipe-material ${boundClass}">
                                ${material.name}<span class="material-bound-tag">[绑]</span>: ${material.boundQuantity}/${material.requiredQuantity}
                            </li>`;
                            
                        // 如果绑定材料不足，但有不绑定材料，显示不绑定材料
                        if (!boundSufficient && material.hasUnbound) {
                            const remainingRequired = material.requiredQuantity - material.boundQuantity;
                            const unboundSufficient = material.unboundQuantity >= remainingRequired;
                            const totalSufficient = (material.boundQuantity + material.unboundQuantity) >= material.requiredQuantity;
                            
                            const unboundClass = unboundSufficient ? 'sufficient' : 'insufficient';
                            
                            if (!totalSufficient) hasInsufficientTotal = true;
                            
                            html += `
                                <li class="recipe-material-supplement ${unboundClass}">
                                    <span class="supplement-mark">+</span> ${material.name}: ${material.unboundQuantity}/${remainingRequired} (补充)
                                </li>`;
                        }
                    } else if (material.hasUnbound) {
                        // 只有不绑定材料
                        const unboundSufficient = material.unboundQuantity >= material.requiredQuantity;
                        const unboundClass = unboundSufficient ? 'sufficient' : 'insufficient';
                        
                        if (!unboundSufficient) hasInsufficientTotal = true;
                        
                        html += `
                            <li class="recipe-material ${unboundClass}">
                                ${material.name}: ${material.unboundQuantity}/${material.requiredQuantity}
                            </li>`;
                    } else {
                        // 没有该材料
                        hasInsufficientTotal = true;
                        html += `
                            <li class="recipe-material insufficient">
                                ${material.name}: 0/${material.requiredQuantity}
                            </li>`;
                    }
                });
                
                html += `
                                    </ul>
                                </div>`;
                                
                // 如果缺少材料，显示警告
                if (hasInsufficientTotal) {
                    html += `<div class="recipe-warning">材料不足！部分物品数量不够。</div>`;
                }
                
                // 如果会使用绑定材料，显示警告
                if (!hasInsufficientTotal) {
                    html += `<div class="recipe-bound-warning">警告: 使用绑定材料合成的物品也将被绑定</div>`;
                }
                
                // 绑定合成按钮
                const boundBtnClass = (canCraftWithBound && hasEnoughGold) ? 'btn-primary' : 'btn-disabled';
                const boundBtnDisabled = (canCraftWithBound && hasEnoughGold) ? '' : 'disabled';
                
                html += `
                                <div class="recipe-actions">
                                    <button class="btn ${boundBtnClass}" ${boundBtnDisabled} 
                                        onclick="game.buildingManager.craftItem(${recipeId}, true)">
                                        合成(绑定)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            });
            
            html += `</div>`;
        } else {
            html += `<p>没有可用的配方。</p>`;
        }
        
        html += `
                </div>
            </div>
        `;
        
        this.buildingContentEl.innerHTML = html;
        
        // 如果有合成消息，显示它并设置自动消失
        if (this.sellNotification) {
            const recipesSection = this.buildingContentEl.querySelector('.recipes-section');
            if (recipesSection) {
                // 我们需要找到已经渲染的通知元素
                const notificationEl = this.buildingContentEl.querySelector('.shop-notification');
                
                if (notificationEl) {
                    // 5秒后自动移除
                    setTimeout(() => {
                        notificationEl.style.transition = 'opacity 0.5s';
                        notificationEl.style.opacity = '0';
                        setTimeout(() => {
                            notificationEl.remove();
                        }, 500);
                    }, 5000);
                }
            }
            // 立即清除消息，防止刷新时再次显示
            this.sellNotification = null;
        }
        
        // 添加CSS样式
        this.ensureCraftingStyles();
    }

    /**
     * 显示宝石合成建筑界面
     * @param {Object} data - 宝石配方及玩家数据
     */
    showGemCraftingView(data) {
        console.log('BuildingManager.showGemCraftingView - 显示宝石合成界面:', data);

        // 保存配方数据
        this.recipes = data.recipes || [];
        const gold = data.player_gold || 0;

        // 构建宝石合成界面内容
        let html = `
            <div class="crafting-view">
                <p>欢迎来到宝石合成工坊，你可以在这里将低级宝石合成为高级宝石。</p>
                <div class="shop-currencies">
                    <span class="currency-gold">${gold} 金币</span>
                </div>`;

        // 如果有合成消息，显示它
        if (this.sellNotification) {
            const notificationClass = this.sellNotification.type === 'success' ? 'shop-notification-success' : 'shop-notification-error';
            html += `<div class="shop-notification ${notificationClass}">${this.sellNotification.message}</div>`;
        }

        // 构建配方列表部分
        html += `
                <div class="recipes-section">
                    <h3>可用配方</h3>`;

        if (this.recipes.length > 0) {
            html += `<div class="recipes-list">`;

            this.recipes.forEach(recipe => {
                const recipeId = recipe.id;

                // 处理每个配方的材料信息
                let materialsAnalysis = [];
                recipe.materials.forEach(material => {
                    // 获取该材料的所有版本（绑定和不绑定）
                    const playerMaterialsForTemplate = data.player_gems.filter(pm =>
                        pm.item_template_id == material.material_item_id
                    );

                    // 分别获取绑定和不绑定材料
                    const boundMaterials = playerMaterialsForTemplate.filter(pm => pm.is_bound == 1);
                    const unboundMaterials = playerMaterialsForTemplate.filter(pm => pm.is_bound == 0);

                    // 计算绑定和不绑定材料的数量
                    const boundQuantity = boundMaterials.reduce((sum, pm) => sum + parseInt(pm.quantity || 0), 0);
                    const unboundQuantity = unboundMaterials.reduce((sum, pm) => sum + parseInt(pm.quantity || 0), 0);

                    materialsAnalysis.push({
                        materialId: material.material_item_id,
                        name: material.name,
                        requiredQuantity: material.required_quantity,
                        boundQuantity: boundQuantity,
                        unboundQuantity: unboundQuantity,
                        hasBound: boundQuantity > 0,
                        hasUnbound: unboundQuantity > 0,
                        boundEnough: boundQuantity >= material.required_quantity,
                        unboundEnough: unboundQuantity >= material.required_quantity
                    });
                });

                // 检查是否可以使用不绑定材料合成
                const canCraftWithUnbound = materialsAnalysis.every(m => m.unboundEnough);

                // 检查是否可以使用绑定材料合成
                const canCraftWithBound = materialsAnalysis.every(m => m.boundEnough || m.unboundEnough);

                // 金币是否足够
                const hasEnoughGold = recipe.player_gold >= recipe.craft_fee;

                html += `
                    <div class="recipe-item">
                        <div class="recipe-header">
                            <span class="recipe-name">${recipe.name} (等级 ${recipe.craft_level})</span>
                            <span class="recipe-result">产出: ${recipe.result_item_name} x${recipe.result_quantity}</span>
                            <span class="recipe-fee">所需费用: ${recipe.craft_fee} 金币</span>
                        </div>`;

                // 如果金币不足，显示警告
                if (!hasEnoughGold) {
                    html += `<div class="recipe-warning">金币不足! 还需要 ${recipe.craft_fee - recipe.player_gold} 金币。</div>`;
                }

                // 创建两个合成选项卡：不绑定和绑定
                html += `
                    <div class="recipe-tabs">
                        <div class="tab-header">
                            <button class="tab-button active" onclick="game.buildingManager.switchCraftTab(this, 'unbound-${recipeId}')">使用不绑定宝石</button>
                            <button class="tab-button" onclick="game.buildingManager.switchCraftTab(this, 'bound-${recipeId}')">使用绑定宝石</button>
                        </div>
                        <div class="tab-content">`;

                // 不绑定材料选项卡
                html += `
                            <div id="unbound-${recipeId}" class="tab-pane active">
                                <div class="recipe-materials">
                                    <span class="recipe-materials-title">所需宝石 (不绑定):</span>
                                    <ul class="recipe-materials-list">`;

                // 显示每种不绑定材料
                let hasInsufficientUnbound = false;
                materialsAnalysis.forEach(material => {
                    const materialClass = material.unboundEnough ? 'sufficient' : 'insufficient';
                    if (!material.unboundEnough) hasInsufficientUnbound = true;

                    html += `
                        <li class="recipe-material ${materialClass}">
                            ${material.name}: ${material.unboundQuantity}/${material.requiredQuantity}
                        </li>`;
                });

                html += `
                                    </ul>
                                </div>`;

                // 如果缺少不绑定材料，显示警告
                if (hasInsufficientUnbound) {
                    html += `<div class="recipe-warning">不绑定宝石不足!</div>`;
                }

                // 不绑定合成按钮
                const unboundBtnClass = (canCraftWithUnbound && hasEnoughGold) ? 'btn-primary' : 'btn-disabled';
                const unboundBtnDisabled = (canCraftWithUnbound && hasEnoughGold) ? '' : 'disabled';

                html += `
                                <div class="recipe-actions">
                                    <button class="btn ${unboundBtnClass}" ${unboundBtnDisabled}
                                        onclick="game.buildingManager.craftGem(${recipeId}, false)">
                                        合成(不绑定)
                                    </button>
                                </div>
                            </div>`;

                // 绑定材料选项卡
                html += `
                            <div id="bound-${recipeId}" class="tab-pane">
                                <div class="recipe-materials">
                                    <span class="recipe-materials-title">所需宝石 (优先使用绑定):</span>
                                    <ul class="recipe-materials-list">`;

                // 显示每种材料（优先使用绑定）
                let hasInsufficientTotal = false;
                materialsAnalysis.forEach(material => {
                    // 显示绑定数量
                    if (material.hasBound) {
                        const boundSufficient = material.boundQuantity >= material.requiredQuantity;
                        const boundClass = boundSufficient ? 'sufficient' : 'partial';

                        html += `
                            <li class="recipe-material ${boundClass}">
                                ${material.name}<span class="material-bound-tag">[绑]</span>: ${material.boundQuantity}/${material.requiredQuantity}
                            </li>`;

                        // 如果绑定材料不足，但有不绑定材料，显示不绑定材料
                        if (!boundSufficient && material.hasUnbound) {
                            const remainingRequired = material.requiredQuantity - material.boundQuantity;
                            const unboundSufficient = material.unboundQuantity >= remainingRequired;
                            const totalSufficient = (material.boundQuantity + material.unboundQuantity) >= material.requiredQuantity;

                            const unboundClass = unboundSufficient ? 'sufficient' : 'insufficient';

                            if (!totalSufficient) hasInsufficientTotal = true;

                            html += `
                                <li class="recipe-material-supplement ${unboundClass}">
                                    <span class="supplement-mark">+</span> ${material.name}: ${material.unboundQuantity}/${remainingRequired} (补充)
                                </li>`;
                        }
                    } else if (material.hasUnbound) {
                        // 只有不绑定材料
                        const unboundSufficient = material.unboundQuantity >= material.requiredQuantity;
                        const unboundClass = unboundSufficient ? 'sufficient' : 'insufficient';

                        if (!unboundSufficient) hasInsufficientTotal = true;

                        html += `
                            <li class="recipe-material ${unboundClass}">
                                ${material.name}: ${material.unboundQuantity}/${material.requiredQuantity}
                            </li>`;
                    } else {
                        // 没有该材料
                        hasInsufficientTotal = true;
                        html += `
                            <li class="recipe-material insufficient">
                                ${material.name}: 0/${material.requiredQuantity}
                            </li>`;
                    }
                });

                html += `
                                    </ul>
                                </div>`;

                // 如果缺少材料，显示警告
                if (hasInsufficientTotal) {
                    html += `<div class="recipe-warning">宝石不足！部分宝石数量不够。</div>`;
                }

                // 如果会使用绑定材料，显示警告
                if (!hasInsufficientTotal) {
                    html += `<div class="recipe-bound-warning">警告: 使用绑定宝石合成的物品也将被绑定</div>`;
                }

                // 绑定合成按钮
                const boundBtnClass = (canCraftWithBound && hasEnoughGold) ? 'btn-primary' : 'btn-disabled';
                const boundBtnDisabled = (canCraftWithBound && hasEnoughGold) ? '' : 'disabled';

                html += `
                                <div class="recipe-actions">
                                    <button class="btn ${boundBtnClass}" ${boundBtnDisabled}
                                        onclick="game.buildingManager.craftGem(${recipeId}, true)">
                                        合成(绑定)
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>`;
            });

            html += `</div>`;
        } else {
            html += `<p>没有可用的配方。</p>`;
        }

        html += `
                </div>
            </div>
        `;

        this.buildingContentEl.innerHTML = html;

        // 如果有合成消息，显示它并设置自动消失
        if (this.sellNotification) {
            const recipesSection = this.buildingContentEl.querySelector('.recipes-section');
            if (recipesSection) {
                // 我们需要找到已经渲染的通知元素
                const notificationEl = this.buildingContentEl.querySelector('.shop-notification');

                if (notificationEl) {
                    // 5秒后自动移除
                    setTimeout(() => {
                        notificationEl.style.transition = 'opacity 0.5s';
                        notificationEl.style.opacity = '0';
                        setTimeout(() => {
                            notificationEl.remove();
                        }, 500);
                    }, 5000);
                }
            }
            // 立即清除消息，防止刷新时再次显示
            this.sellNotification = null;
        }

        // 添加CSS样式（确保宝石合成台也有完整的样式）
        this.ensureCraftingStyles();
    }

    /**
     * 合成宝石
     * @param {number} recipeId - 配方ID
     * @param {boolean} useBound - 是否使用绑定材料（优先绑定）
     */
    craftGem(recipeId, useBound = false) {
        console.log('BuildingManager.craftGem - 尝试合成宝石:', { recipeId, useBound });

        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.craftGem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('宝石合成失败：无法确定当前建筑');
            return;
        }

        // 发送宝石合成请求
        this.gameClient.sendMessage(MessageProtocol.C2S_CRAFT_GEM, {
            player_id: this.gameClient.currentPlayer.id,
            recipe_id: recipeId,
            scene_building_id: this.currentBuilding.scene_building_id,
            use_bound: useBound // 新增参数，指示是否使用绑定材料
        });
    }

    /**
     * 处理宝石合成结果
     * @param {Object} payload - 宝石合成结果数据
     */
    handleGemCraftResult(payload) {
        console.log('BuildingManager.handleGemCraftResult - 接收到宝石合成结果:', payload);

        // 保存通知内容，以便在切换页面或筛选时保持显示
        this.sellNotification = {
            message: payload.message,
            type: payload.success ? 'success' : 'error'
        };

        // 如果当前在宝石合成界面，刷新显示
        if (this.currentBuilding && this.currentBuilding.type === 'GEM_CRAFTING' && document.getElementById('buildingView').style.display === 'block') {
            // 重新请求宝石配方数据以更新界面
            this.gameClient.sendMessage(MessageProtocol.C2S_GET_GEM_RECIPES, {
                scene_building_id: this.currentBuilding.scene_building_id,
                player_id: this.gameClient.currentPlayer.id
            });
        }
    }
    
    /**
     * 切换合成选项卡
     * @param {HTMLElement} button - 被点击的选项卡按钮
     * @param {string} tabId - 目标选项卡的ID
     */
    switchCraftTab(button, tabId) {
        // 查找父级容器
        const tabsContainer = button.closest('.recipe-tabs');
        if (!tabsContainer) return;
        
        // 设置所有按钮为非活动状态
        const buttons = tabsContainer.querySelectorAll('.tab-button');
        buttons.forEach(btn => btn.classList.remove('active'));
        
        // 隐藏所有选项卡内容
        const panes = tabsContainer.querySelectorAll('.tab-pane');
        panes.forEach(pane => pane.classList.remove('active'));
        
        // 激活当前选项卡和内容
        button.classList.add('active');
        const targetPane = document.getElementById(tabId);
        if (targetPane) {
            targetPane.classList.add('active');
        }
    }
    
    /**
     * 合成物品
     * @param {number} recipeId - 配方ID
     * @param {boolean} useBound - 是否使用绑定材料（优先绑定）
     */
    craftItem(recipeId, useBound = false) {
        console.log('BuildingManager.craftItem - 尝试合成物品:', { recipeId, useBound });
        
        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.craftItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('合成失败：无法确定当前建筑');
            return;
        }
        
        // 发送合成请求
        this.gameClient.sendMessage(MessageProtocol.C2S_CRAFT_ITEM, {
            player_id: this.gameClient.currentPlayer.id,
            recipe_id: recipeId,
            scene_building_id: this.currentBuilding.scene_building_id,
            use_bound: useBound // 新增参数，指示是否使用绑定材料
        });
    }
    
    /**
     * 处理合成结果
     * @param {Object} payload - 合成结果数据
     */
    handleCraftResult(payload) {
        console.log('BuildingManager.handleCraftResult - 接收到合成结果:', payload);
        
        // 保存通知内容，以便在切换页面或筛选时保持显示
        this.sellNotification = {
            message: payload.message,
            type: payload.success ? 'success' : 'error'
        };
        
        // 如果当前在合成界面，刷新显示
        if (this.currentBuilding && this.currentBuilding.type === 'CRAFTING' && document.getElementById('buildingView').style.display === 'block') {
            // 重新请求配方数据以更新界面
            this.gameClient.sendMessage(MessageProtocol.C2S_GET_RECIPES, {
                scene_building_id: this.currentBuilding.scene_building_id,
                player_id: this.gameClient.currentPlayer.id
            });
        }
    }
    
    /**
     * 请求凝练材料数据
     * @param {Object} data - 建筑数据
     */
    requestRefineMaterials(data) {
        console.log('BuildingManager.requestRefineMaterials - 请求凝练材料数据:', data);
        
        // 保存建筑视图显示
        this.showBuildingView();
        this.buildingNameEl.textContent = data.name;
        
        // 显示加载提示
        this.buildingContentEl.innerHTML = '<p>正在加载凝练数据，请稍候...</p>';
        
        // 请求凝练材料数据
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_REFINE_MATERIALS, {
            scene_building_id: data.scene_building_id,
            player_id: this.gameClient.currentPlayer.id
        });
    }
    
    /**
     * 处理凝练材料数据
     * @param {Object} payload - 凝练材料数据
     */
    handleRefineMaterialsData(payload) {
        console.log('BuildingManager.handleRefineMaterialsData - 接收到凝练材料数据:', payload);
        
        // 保存当前凝练数据
        this.refineMaterials = payload.materials || [];
        this.refineEquipment = payload.equipment || [];
        this.playerGold = payload.player_gold || 0;
        
        // 显示凝练界面
        this.showRefineView();
    }
    
    /**
     * 显示凝练界面
     */
    showRefineView() {
        console.log('BuildingManager.showRefineView - 显示凝练界面');
        
        // 重置选中状态
        this.selectedMaterials = {};  // 改为对象形式，存储材料ID和数量
        this.materialAddOrder = [];   // 新增：记录材料添加顺序
        this.selectedEquipment = null;
        
        let html = `
            <div class="refine-view">
                <p>欢迎来到凝练工坊，你可以在这里使用材料凝练装备，提升装备品质。</p>
                <div class="shop-currencies">
                    <span class="currency-gold">${this.playerGold} 金币</span>
                </div>`;
        
        // 如果有通知消息，显示它
        if (this.sellNotification) {
            const notificationClass = this.sellNotification.type === 'success' ? 'shop-notification-success' : 'shop-notification-error';
            html += `<div class="shop-notification ${notificationClass}">${this.sellNotification.message}</div>`;
        }
        
        // 可展开/收缩的凝练说明
        html += `
            <div class="refine-instruction-container">
                <div class="refine-instruction-header" onclick="game.buildingManager.toggleRefineInstruction()">
                    <h3>凝练说明</h3>
                    <span class="toggle-icon">▼</span>
                </div>
                <div class="refine-instruction-content" style="display: none;">
                    <div style="display: grid; gap: 12px;">
                        <p><span style="color: #007bff; font-weight: bold;">①</span> 选择一件装备和5个材料进行凝练</p>
                        <p><span style="color: #007bff; font-weight: bold;">②</span> 材料的品级会影响凝练效果，品级越高效果越好</p>
                        <p><span style="color: #007bff; font-weight: bold;">③</span> 材料的搭配会产生不同的组合系数</p>
                        <p><span style="color: #007bff; font-weight: bold;">④</span> 凝练成功后，装备会获得前缀和属性加成</p>
                        <p><span style="color: #007bff; font-weight: bold;">⑤</span> 凝练费用根据获得的品质等级计算</p>
                        <div style="margin-top: 15px; padding: 10px; background-color: rgba(0, 123, 255, 0.1); border-left: 4px solid #007bff; border-radius: 4px;">
                            <p style="margin: 0; font-size: 0.9em; color: var(--main-color); font-style: italic;">
                                💡 提示：合理搭配不同元素的材料可以获得更好的凝练效果
                            </p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="refine-main-content">
                <div class="refine-section refine-equipment-section">
                    <h3>选择装备</h3>
                    <div class="equipment-list">`;
        
        if (this.refineEquipment.length > 0) {
            html += `<div class="equipment-grid">`;
            this.refineEquipment.forEach(equipment => {
                // 使用display_name替代name，如果有的话
                const displayName = equipment.instance_data ? 
                    (JSON.parse(equipment.instance_data).display_name || equipment.name) : 
                    equipment.name;
                html += `
                    <div class="equipment-item" onclick="game.buildingManager.selectEquipment(${equipment.inventory_id})">
                        <span class="equipment-name">${displayName}</span>
                    </div>`;
            });
            html += `</div>`;
        } else {
            html += `<p>你的背包中没有可凝练的装备。</p>`;
        }
        
        html += `</div></div>
                
                <div class="refine-section refine-materials-section">
                    <h3>选择材料 (需要总计5个)</h3>
                    
                    <!-- 添加材料说明提示 -->
                    <div class="materials-note">
                        <p>凝练只显示具有品级的材料。可以选择同一种材料多次。</p>
                    </div>
                    
                    <!-- 已选择材料区域 -->
                    <div class="selected-materials-container">
                        <h4>已选择的材料 (<span id="selected-count">0</span>/5)</h4>
                        <div id="selected-materials-list" class="selected-materials-list">
                            <p class="no-materials-selected">尚未选择任何材料</p>
                        </div>
                    </div>
                    
                    <!-- 材料列表 -->
                    <div class="materials-list">`;
            
        if (this.refineMaterials.length > 0) {
            html += `<div class="materials-grid">`;
            this.refineMaterials.forEach(material => {
                // 获取元素图标和颜色
                const elementInfo = this.getElementInfo(material.element);
                
                html += `
                    <div class="material-item has-element">
                        <div class="material-header">
                            <span class="material-name">${material.name}</span>
                            <span class="material-tier">品级: ${material.element_tier || 1}</span>
                        </div>
                        <div class="material-details">
                            <span class="material-quantity">数量: ${material.quantity}</span>
                        </div>
                        <div class="material-actions">
                            <div class="quantity-control">
                                <button class="quantity-btn" onclick="game.buildingManager.decreaseMaterialQuantity(${material.inventory_id})">-</button>
                                <input type="number" min="0" max="${material.quantity}" value="0" 
                                       class="material-quantity-input" id="quantity-${material.inventory_id}" 
                                       onchange="game.buildingManager.updateMaterialQuantity(${material.inventory_id}, this.value)">
                                <button class="quantity-btn" onclick="game.buildingManager.increaseMaterialQuantity(${material.inventory_id})">+</button>
                            </div>
                        </div>
                    </div>`;
            });
            
            html += `</div>`;
        } else {
            html += `<p>你的背包中没有凝练材料。</p>`;
        }
        
        html += `</div></div>
                
                <div class="refine-action">
                    <button class="btn btn-primary" onclick="game.buildingManager.refineItem()" disabled>开始凝练</button>
                </div>
            </div>
            </div>`;
        
        this.buildingContentEl.innerHTML = html;
        
        // 添加CSS样式
        const style = document.createElement('style');
        style.textContent = `
            .refine-view {
                padding: 10px 0;
                height: 100%;
                overflow: hidden;
                display: flex;
                flex-direction: column;
            }
            
            .refine-main-content {
                flex: 1;
                display: flex;
                flex-direction: column;
                overflow-y: auto;
                padding-right: 5px;
            }
            
            .refine-instruction-container {
                margin: 15px 0;
                background-color: rgba(255, 255, 255, 0.1);
                border-radius: 8px;
                border: 2px solid var(--border-color);
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
            .refine-instruction-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 15px 20px;
                cursor: pointer;
                transition: background-color 0.2s;
                border-radius: 8px 8px 0 0;
            }
            .refine-instruction-header:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            .refine-instruction-header h3 {
                margin: 0;
                color: var(--main-color);
                font-weight: 600;
                font-size: 1.1em;
            }
            .toggle-icon {
                font-size: 14px;
                transition: transform 0.3s;
                color: var(--main-color);
                font-weight: bold;
            }
            .refine-instruction-header.expanded .toggle-icon {
                transform: rotate(180deg);
            }
            .refine-instruction-content {
                padding: 15px 20px;
                border-top: 1px solid var(--border-color);
                background-color: rgba(255, 255, 255, 0.05);
            }
            .refine-instruction-content p {
                margin: 8px 0;
                color: var(--main-color);
                font-weight: 500;
                line-height: 1.6;
            }
            .refine-section {
                margin: 15px 0;
                padding-top: 15px;
                border-top: 1px solid var(--border-color);
            }
            
            .refine-equipment-section {
                margin-bottom: 20px;
            }
            
            .equipment-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 10px;
                max-height: 200px;
                overflow-y: auto;
            }
            
            .materials-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
                gap: 10px;
                max-height: 300px;
                overflow-y: auto;
            }
            
            .equipment-item, .material-item {
                padding: 8px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                transition: all 0.2s;
            }
            .equipment-item {
                cursor: pointer;
            }
            .equipment-item:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            .equipment-item.selected {
                background-color: rgba(76, 175, 80, 0.2);
                border-color: #4caf50;
            }
            .equipment-name {
                font-weight: bold;
                display: block;
            }
            .refine-action {
                margin-top: 20px;
                text-align: center;
                padding-bottom: 10px;
            }
            
            /* 材料元素样式和布局 */
            .material-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
            }
            .material-details {
                display: flex;
                justify-content: space-between;
                font-size: 0.85em;
                margin-bottom: 8px;
            }
            .material-name {
                font-weight: bold;
            }
            .material-element {
                font-size: 0.9em;
                font-weight: bold;
                white-space: nowrap;
            }
            .material-quantity, .material-tier {
                color: #aaa;
            }
            .material-item.has-element {
                background-color: rgba(76, 175, 80, 0.05);
            }
            
            /* 强化选中状态的视觉效果 */
            .equipment-item.selected {
                background-color: rgba(76, 175, 80, 0.2) !important;
                border-color: #4caf50 !important;
                box-shadow: 0 0 5px rgba(76, 175, 80, 0.5);
            }
            
            /* 已凝练装备的样式 */
            .equipment-item.refined-equipment {
                background-color: rgba(76, 175, 80, 0.1);
                border-left: 3px solid var(--primary-color);
            }
            .refined-name {
                color: var(--primary-color);
                font-weight: bold;
            }
            
            /* 材料说明提示 */
            .materials-note {
                margin-bottom: 10px;
                padding: 5px 10px;
                background-color: rgba(33, 150, 243, 0.1);
                border-left: 3px solid #2196f3;
                font-size: 0.9em;
            }
            .materials-note p {
                margin: 5px 0;
                color: #2196f3;
            }
            
            /* 材料数量控制 */
            .material-actions {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-top: 5px;
                border-top: 1px solid var(--border-color);
                padding-top: 5px;
            }
            .quantity-control {
                display: flex;
                align-items: center;
            }
            .material-quantity-input {
                width: 40px;
                text-align: center;
                margin: 0 5px;
                padding: 2px;
                border-radius: 3px;
                border: 1px solid var(--border-color);
                background-color: rgba(0, 0, 0, 0.2);
                color: #fff;
            }
            .quantity-btn {
                width: 24px;
                height: 24px;
                border-radius: 3px;
                border: 1px solid var(--border-color);
                background-color: rgba(0, 0, 0, 0.2);
                color: #fff;
                cursor: pointer;
                font-weight: bold;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .quantity-btn:hover {
                background-color: rgba(255, 255, 255, 0.1);
            }
            .btn-small {
                padding: 2px 8px;
                font-size: 0.85em;
            }
            
            /* 已选择材料列表 */
            .selected-materials-container {
                margin-bottom: 15px;
                padding: 10px;
                background-color: rgba(0, 0, 0, 0.1);
                border-radius: 5px;
                border: 1px solid var(--border-color);
            }
            .selected-materials-container h4 {
                margin: 0 0 10px 0;
                color: var(--primary-color);
                font-size: 1em;
            }
            .selected-materials-list {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;
                min-height: 40px;
            }
            .no-materials-selected {
                color: #aaa;
                font-style: italic;
                width: 100%;
                text-align: center;
            }
            .selected-material-item {
                background-color: rgba(76, 175, 80, 0.1);
                border: 1px solid #4caf50;
                border-radius: 4px;
                padding: 5px 10px;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
            }
            .selected-material-info {
                display: flex;
                flex-direction: column;
            }
            .selected-material-name {
                font-weight: bold;
                font-size: 0.9em;
            }
            .selected-material-element {
                font-size: 0.8em;
                color: #aaa;
            }
            .remove-material {
                color: #f44336;
                cursor: pointer;
                font-weight: bold;
                font-size: 1.2em;
                background: none;
                border: none;
                padding: 0;
                margin: 0;
            }
            .remove-material:hover {
                color: #d32f2f;
            }
            
            /* 修复滚动条样式 */
            .materials-grid::-webkit-scrollbar,
            .equipment-grid::-webkit-scrollbar,
            .refine-main-content::-webkit-scrollbar {
                width: 6px;
                height: 6px;
            }
            
            .materials-grid::-webkit-scrollbar-thumb,
            .equipment-grid::-webkit-scrollbar-thumb,
            .refine-main-content::-webkit-scrollbar-thumb {
                background: rgba(255, 255, 255, 0.2);
                border-radius: 3px;
            }
            
            .materials-grid::-webkit-scrollbar-thumb:hover,
            .equipment-grid::-webkit-scrollbar-thumb:hover,
            .refine-main-content::-webkit-scrollbar-thumb:hover {
                background: rgba(255, 255, 255, 0.3);
            }
            
            /* 禁用状态样式 */
            .btn-disabled, .quantity-btn[disabled] {
                opacity: 0.5;
                cursor: not-allowed;
            }
            .btn-disabled:hover, .quantity-btn[disabled]:hover {
                background-color: rgba(0, 0, 0, 0.2);
            }
            .material-disabled {
                opacity: 0.7;
                position: relative;
            }
            .material-disabled::after {
                content: "已达上限";
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.5);
                color: #f44336;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                border-radius: 4px;
                z-index: 1;
            }
        `;
        document.head.appendChild(style);
        
        // 初始化按钮状态
        this.updateRefineButton();
        // 初始化材料可用状态
        this.updateMaterialsAvailability();
    }
    
    /**
     * 选择装备
     * @param {number} equipmentId - 装备ID
     */
    selectEquipment(equipmentId) {
        console.log('BuildingManager.selectEquipment - 选择装备:', equipmentId);
        
        // 取消之前选中的装备
        const prevSelected = document.querySelector('.equipment-item.selected');
        if (prevSelected) {
            prevSelected.classList.remove('selected');
        }
        
        // 清除之前的费用显示
        this.clearRefineCostDisplay();

        // 如果是取消选择
        if (this.selectedEquipment === equipmentId) {
            this.selectedEquipment = null;
            this.showRefineStatusMessage('已取消选择装备');
        } else {
            // 选中新装备
            this.selectedEquipment = equipmentId;
            const equipmentEl = document.querySelector(`.equipment-item[onclick*="${equipmentId}"]`);
            if (equipmentEl) {
                equipmentEl.classList.add('selected');
                
                // 获取装备名称
                const nameEl = equipmentEl.querySelector('.equipment-name');
                const equipmentName = nameEl ? nameEl.textContent : '装备';
                
                // 显示已选择提示
                this.showRefineStatusMessage(`已选择装备: ${equipmentName}`, 'success');
            }
        }
        
        this.updateRefineButton();
    }
    
    /**
     * 选择材料
     * @param {number} materialId - 材料ID
     */
    selectMaterial(materialId) {
        console.log('BuildingManager.selectMaterial - 选择材料:', materialId);
        
        // 查找材料在选中列表中的索引
        const index = this.selectedMaterials.indexOf(materialId);
        
        // 查找材料对象
        const material = this.refineMaterials.find(m => m.inventory_id == materialId);
        if (!material) return;
        
        // 如果已经选中，则取消选择
        if (index !== -1) {
            this.selectedMaterials.splice(index, 1);
            const materialEl = document.querySelector(`.material-item[onclick*="${materialId}"]`);
            if (materialEl) {
                materialEl.classList.remove('selected');
            }
            // 显示取消选择提示
            this.showRefineStatusMessage('已取消选择材料');
        } 
        // 如果未选中且未达到5个上限，则选中
        else if (this.selectedMaterials.length < 5) {
            this.selectedMaterials.push(materialId);
            const materialEl = document.querySelector(`.material-item[onclick*="${materialId}"]`);
            if (materialEl) {
                materialEl.classList.add('selected');
            }
            
            // 显示已选择提示
            const elementInfo = this.getElementInfo(material.element);
            this.showRefineStatusMessage(
                `已选择材料: ${material.name} (品级 ${material.element_tier || 1}) (${this.selectedMaterials.length}/5)`, 
                'success'
            );
        }
        // 如果已达到5个上限，提示用户
        else {
            // 在界面上显示提示
            this.showRefineStatusMessage('最多只能选择5个材料进行凝练', 'error');
        }
        
        this.updateRefineButton();
    }
    
    /**
     * 显示凝练状态消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('info', 'success', 'error')
     */
    showRefineStatusMessage(message, type = 'info') {
        // 查找或创建状态消息容器
        let statusContainer = document.querySelector('.refine-status-message');
        if (!statusContainer) {
            statusContainer = document.createElement('div');
            statusContainer.className = 'refine-status-message';
            
            // 添加到凝练操作区域上方
            const actionArea = document.querySelector('.refine-action');
            if (actionArea) {
                actionArea.parentNode.insertBefore(statusContainer, actionArea);
            } else if (this.buildingContentEl) {
                this.buildingContentEl.appendChild(statusContainer);
            }
            
            // 添加状态消息样式
            const style = document.createElement('style');
            style.textContent = `
                .refine-status-message {
                    margin: 10px 0;
                    padding: 8px 10px;
                    border-radius: 4px;
                    text-align: center;
                    font-size: 0.9em;
                    transition: all 0.3s;
                }
                .refine-status-info {
                    background-color: rgba(33, 150, 243, 0.1);
                    color: #2196f3;
                }
                .refine-status-success {
                    background-color: rgba(76, 175, 80, 0.1);
                    color: #4caf50;
                }
                .refine-status-error {
                    background-color: rgba(244, 67, 54, 0.1);
                    color: #f44336;
                }
            `;
            document.head.appendChild(style);
        }
        
        // 更新状态消息
        statusContainer.textContent = message;
        statusContainer.className = 'refine-status-message';
        
        if (type === 'success') {
            statusContainer.classList.add('refine-status-success');
        } else if (type === 'error') {
            statusContainer.classList.add('refine-status-error');
        } else {
            statusContainer.classList.add('refine-status-info');
        }
        
        // 添加边框以增强可见性
        if (type === 'error') {
            statusContainer.style.border = '1px solid #f44336';
            statusContainer.style.fontWeight = 'bold';
        } else if (type === 'success') {
            statusContainer.style.border = '1px solid #4caf50';
        } else {
            statusContainer.style.border = '1px solid #2196f3';
        }
        
        // 确保消息显示在用户可见区域
        statusContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 动画效果
        statusContainer.style.opacity = '0';
        setTimeout(() => {
            statusContainer.style.opacity = '1';
        }, 10);
    }
    
    /**
     * 更新凝练按钮状态
     */
    updateRefineButton() {
        const refineBtn = document.querySelector('.refine-action .btn');
        if (!refineBtn) return;
        
        // 计算已选择的材料总数
        const selectedMaterials = Object.values(this.selectedMaterials);
        const totalCount = selectedMaterials.reduce((sum, item) => sum + item.quantity, 0);
        
        // 检查是否同时选择了装备和5个材料
        const canRefine = this.selectedEquipment !== null && totalCount === 5;
        
        refineBtn.disabled = !canRefine;
        
        // 更新按钮文本
        if (canRefine) {
            // 请求费用预估
            this.requestRefineCostEstimate();

            refineBtn.textContent = '开始凝练';
            refineBtn.classList.add('btn-primary');

            // 显示准备就绪提示
            this.showRefineStatusMessage('所有材料已准备就绪，可以开始凝练', 'success');
        } else {
            // 计算还需要的材料数量
            const neededMaterials = 5 - totalCount;
            
            if (!this.selectedEquipment) {
                refineBtn.textContent = '请选择装备和材料';
                
                if (totalCount > 0) {
                    this.showRefineStatusMessage(`请选择装备和${neededMaterials}个材料`, 'info');
                }
            } else if (neededMaterials > 0) {
                refineBtn.textContent = `还需选择${neededMaterials}个材料`;
                this.showRefineStatusMessage(`请再选择${neededMaterials}个材料`, 'info');
            }
        }
    }

    /**
     * 请求凝练费用预估
     */
    requestRefineCostEstimate() {
        if (!this.selectedEquipment || !this.materialAddOrder || this.materialAddOrder.length !== 5) {
            return;
        }

        // 发送费用预估请求
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_REFINE_COST_ESTIMATE, {
            player_id: this.gameClient.currentPlayer.id,
            equipment_id: this.selectedEquipment,
            materials: this.materialAddOrder,
            scene_building_id: this.currentBuilding.scene_building_id
        });
    }

    /**
     * 处理凝练费用预估响应
     * @param {Object} payload - 费用预估数据
     */
    handleRefineCostEstimate(payload) {
        console.log('BuildingManager.handleRefineCostEstimate - 接收到费用预估:', payload);

        if (payload.success) {
            // 更新费用显示
            this.updateRefineCostDisplay(payload.estimated_cost, payload.estimated_tier, payload.estimated_tier_name);
        } else {
            console.error('费用预估失败:', payload.message);
        }
    }

    /**
     * 更新凝练费用显示
     * @param {number} cost - 预估费用
     * @param {number} tier - 预估品质等级
     * @param {string} tierName - 预估品质名称
     */
    updateRefineCostDisplay(cost, tier, tierName) {
        // 查找或创建费用显示元素
        let costDisplay = document.querySelector('.refine-cost-display');
        if (!costDisplay) {
            // 在凝练按钮上方创建费用显示
            const refineAction = document.querySelector('.refine-action');
            if (refineAction) {
                costDisplay = document.createElement('div');
                costDisplay.className = 'refine-cost-display';
                refineAction.insertBefore(costDisplay, refineAction.firstChild);
            }
        }

        if (costDisplay) {
            // 如果有品质名称就显示名称，否则显示等级数字
            const tierDisplay = tierName ? tierName : `等级 ${tier}`;

            costDisplay.innerHTML = `
                <div class="cost-estimate">
                    <div class="cost-info">
                        <span class="cost-label">预估费用:</span>
                        <span class="cost-amount">${cost} 金币</span>
                    </div>
                    <div class="tier-info">
                        <span class="tier-label">预估品质:</span>
                        <span class="tier-level">${tierDisplay}</span>
                    </div>
                </div>
            `;

            // 检查玩家金币是否足够
            if (this.playerGold < cost) {
                costDisplay.classList.add('insufficient-gold');
                costDisplay.innerHTML += `<div class="warning">金币不足！还需要 ${cost - this.playerGold} 金币</div>`;
            } else {
                costDisplay.classList.remove('insufficient-gold');
            }
        }
    }

    /**
     * 清除凝练费用显示
     */
    clearRefineCostDisplay() {
        const costDisplay = document.querySelector('.refine-cost-display');
        if (costDisplay) {
            costDisplay.remove();
        }
    }

    /**
     * 执行凝练
     */
    refineItem() {
        console.log('BuildingManager.refineItem - 尝试凝练装备');
        
        // 检查是否有添加顺序记录
        if (!this.materialAddOrder || this.materialAddOrder.length !== 5) {
            const selectedMaterials = Object.values(this.selectedMaterials);
            const totalCount = selectedMaterials.reduce((sum, item) => sum + item.quantity, 0);
            
            if (!this.selectedEquipment || totalCount !== 5) {
                this.showRefineStatusMessage('凝练失败：请选择1件装备和总计5个材料', 'error');
                return;
            }
        }
        
        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.refineItem - 错误: 无法确定当前建筑');
            this.showRefineStatusMessage('凝练失败：无法确定当前建筑', 'error');
            return;
        }
        
        // 显示凝练中状态
        this.showRefineStatusMessage('凝练中，请稍候...', 'info');
        
        // 禁用凝练按钮，防止重复点击
        const refineBtn = document.querySelector('.refine-action .btn');
        if (refineBtn) {
            refineBtn.disabled = true;
            refineBtn.textContent = '凝练中...';
        }
        
        // 创建超时处理，确保无论如何按钮都会恢复
        this.refineTimeout = setTimeout(() => {
            this.resetRefineButton();
        }, 10000); // 10秒超时
        
        // 使用材料添加顺序发送请求
        console.log('使用有序材料列表进行凝练:', this.materialAddOrder);
        
        // 发送凝练请求
        this.gameClient.sendMessage(MessageProtocol.C2S_REFINE_ITEM, {
            player_id: this.gameClient.currentPlayer.id,
            equipment_id: this.selectedEquipment,
            materials: this.materialAddOrder,
            scene_building_id: this.currentBuilding.scene_building_id
        });
    }



    /**
     * 重置凝练按钮状态
     */
    resetRefineButton() {
        // 清除超时
        if (this.refineTimeout) {
            clearTimeout(this.refineTimeout);
            this.refineTimeout = null;
        }
        
        // 恢复按钮状态
        const refineBtn = document.querySelector('.refine-action .btn');
        if (refineBtn) {
            refineBtn.disabled = false;
            refineBtn.textContent = '开始凝练';
        }
    }
    
    /**
     * 处理凝练结果
     * @param {Object} payload - 凝练结果数据
     */
    handleRefineResult(payload) {
        console.log('BuildingManager.handleRefineResult - 接收到凝练结果:', payload);
        
        // 重置凝练按钮状态
        this.resetRefineButton();
        
        // 保存通知内容，以便在切换页面或筛选时保持显示
        this.sellNotification = {
            message: payload.message,
            type: payload.success ? 'success' : 'error'
        };
        
        // 如果当前在凝练界面，直接在界面上显示消息
        if (this.currentBuilding && this.currentBuilding.type === 'REFINE' && document.getElementById('buildingView').style.display === 'block') {
            // 先显示结果提示
            this.showRefineNotification(payload.message, payload.success ? 'success' : 'error');
            
            // 重新请求凝练材料数据以更新界面
            this.gameClient.sendMessage(MessageProtocol.C2S_GET_REFINE_MATERIALS, {
                scene_building_id: this.currentBuilding.scene_building_id,
                player_id: this.gameClient.currentPlayer.id
            });
        } else {
            // 如果不在凝练界面，添加到游戏日志
            this.gameClient.addLog(payload.message);
        }
    }
    

    
    /**
     * 在凝练界面显示通知消息
     * @param {string} message - 消息内容
     * @param {string} type - 消息类型 ('success' 或 'error')
     */
    showRefineNotification(message, type = 'success') {
        // 查找通知容器，如果不存在则创建
        let notificationContainer = document.querySelector('.refine-notification');
        if (!notificationContainer) {
            notificationContainer = document.createElement('div');
            notificationContainer.className = 'refine-notification';
            
            // 插入到凝练说明下方
            const instructionEl = document.querySelector('.refine-instruction-container');
            if (instructionEl && instructionEl.nextSibling) {
                instructionEl.parentNode.insertBefore(notificationContainer, instructionEl.nextSibling);
            } else if (this.buildingContentEl) {
                // 如果找不到凝练说明，直接添加到建筑内容顶部
                if (this.buildingContentEl.firstChild) {
                    this.buildingContentEl.insertBefore(notificationContainer, this.buildingContentEl.firstChild.nextSibling);
                } else {
                    this.buildingContentEl.appendChild(notificationContainer);
                }
            }
        }
        
        // 设置通知内容和样式
        notificationContainer.textContent = message;
        notificationContainer.className = 'refine-notification';
        if (type === 'success') {
            notificationContainer.classList.add('refine-notification-success');
        } else {
            notificationContainer.classList.add('refine-notification-error');
        }
        
        // 添加通知样式
        const style = document.createElement('style');
        style.textContent = `
            .refine-notification {
                margin: 10px 0;
                padding: 10px;
                border-radius: 4px;
                font-weight: bold;
                text-align: center;
                animation: fadeIn 0.5s;
            }
            .refine-notification-success {
                background-color: rgba(76, 175, 80, 0.2);
                border: 1px solid #4caf50;
                color: #4caf50;
            }
            .refine-notification-error {
                background-color: rgba(244, 67, 54, 0.2);
                border: 1px solid #f44336;
                color: #f44336;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // 自动滚动到通知位置
        notificationContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
        
        // 5秒后自动移除通知
        setTimeout(() => {
            if (notificationContainer) {
                // 添加淡出动画
                notificationContainer.style.transition = 'opacity 0.5s ease-out';
                notificationContainer.style.opacity = '0';
                
                // 动画结束后移除元素
                setTimeout(() => {
                    notificationContainer.remove();
                }, 500);
            }
            
            // 清除持久化通知，防止刷新时再次显示
            this.sellNotification = null;
        }, 5000); // 5秒后消失
        
        // 重置凝练状态，清除"凝练中..."的显示
        this.resetRefineButton();
        
        // 清除状态消息
        const statusMessage = document.querySelector('.refine-status-message');
        if (statusMessage) {
            statusMessage.textContent = '';
            statusMessage.style.opacity = '0';
            statusMessage.style.padding = '0';
            statusMessage.style.margin = '0';
            statusMessage.style.border = 'none';
        }
    }
    

    
    /**
     * 获取属性的显示名称
     * @param {string} attribute - 属性名
     * @returns {string} - 属性显示名
     */
    getAttributeDisplayName(attribute) {
        const attributeMap = {
            'attack': '攻击力',
            'defense': '防御力',
            'max_hp': '最大生命值',
            'max_mp': '最大魔法值',
            'strength': '力量',
            'agility': '敏捷',
            'constitution': '体质',
            'intelligence': '智力',
            'fire_damage': '火属性伤害',
            'ice_damage': '冰属性伤害',
            'wind_damage': '风属性伤害',
            'electric_damage': '雷属性伤害',
            'fire_resistance': '火属性抗性',
            'ice_resistance': '冰属性抗性',
            'wind_resistance': '风属性抗性',
            'electric_resistance': '雷属性抗性',
            'dodge_bonus': '闪避加成',
            'attack_speed': '攻击速度'
        };
        
        return attributeMap[attribute] || attribute;
    }
    

    
    /**
     * 切换凝练说明的展开/收缩状态
     */
    toggleRefineInstruction() {
        const header = document.querySelector('.refine-instruction-header');
        const content = document.querySelector('.refine-instruction-content');
        const toggleIcon = document.querySelector('.toggle-icon');
        
        if (!header || !content || !toggleIcon) return;
        
        // 切换显示状态
        if (content.style.display === 'none') {
            content.style.display = 'block';
            header.classList.add('expanded');
            toggleIcon.textContent = '▲';
        } else {
            content.style.display = 'none';
            header.classList.remove('expanded');
            toggleIcon.textContent = '▼';
        }
    }
    
    /**
     * 获取元素的信息（图标、颜色、名称）
     * @param {string} element - 元素类型
     * @returns {Object} - 元素信息
     */
    getElementInfo(element) {
        if (!element) {
            return {
                icon: '❓',
                color: '#999',
                name: '未知'
            };
        }
        
        const elementMap = {
            'gold': { icon: '🔶', color: '#FFD700', name: '金' },
            'wood': { icon: '🌿', color: '#228B22', name: '木' },
            'water': { icon: '💧', color: '#1E90FF', name: '水' },
            'fire': { icon: '🔥', color: '#FF4500', name: '火' },
            'earth': { icon: '🗿', color: '#8B4513', name: '土' },
            
            // 兼容英文和中文
            '金': { icon: '🔶', color: '#FFD700', name: '金' },
            '木': { icon: '🌿', color: '#228B22', name: '木' },
            '水': { icon: '💧', color: '#1E90FF', name: '水' },
            '火': { icon: '🔥', color: '#FF4500', name: '火' },
            '土': { icon: '🗿', color: '#8B4513', name: '土' }
        };
        
        return elementMap[element] || {
            icon: '❓',
            color: '#999',
            name: element
        };
    }
    
    /**
     * 增加材料数量
     * @param {number} materialId - 材料ID
     */
    increaseMaterialQuantity(materialId) {
        const input = document.getElementById(`quantity-${materialId}`);
        if (!input) return;
        
        // 获取当前已选择的材料总数
        const currentTotal = Object.values(this.selectedMaterials).reduce((sum, item) => sum + item.quantity, 0);
        
        // 如果已经达到5个上限，则不允许再添加
        if (currentTotal >= 5) {
            this.showRefineStatusMessage('最多只能选择5个材料进行凝练', 'error');
            return;
        }
        
        // 查找材料对象
        const material = this.refineMaterials.find(m => m.inventory_id == materialId);
        if (!material) return;
        
        // 获取当前选择的数量
        const currentSelectedQuantity = this.selectedMaterials[materialId] ? this.selectedMaterials[materialId].quantity : 0;
        
        // 如果这个材料已经选了5个，不允许再添加
        if (currentSelectedQuantity >= 5) {
            this.showRefineStatusMessage(`单种材料最多选择5个`, 'error');
            return;
        }
        
        // 检查库存数量
        const maxValue = parseInt(input.max) || 0;
        const currentValue = parseInt(input.value) || 0;
        
        if (currentValue < maxValue) {
            // 更新输入框值
            input.value = currentValue + 1;
            
            // 直接添加到凝练列表
            this.addMaterialToRefine(materialId, 1);
        }
    }
    
    /**
     * 减少材料数量
     * @param {number} materialId - 材料ID
     */
    decreaseMaterialQuantity(materialId) {
        const input = document.getElementById(`quantity-${materialId}`);
        if (!input) return;
        
        const currentValue = parseInt(input.value) || 0;
        
        if (currentValue > 0) {
            // 更新输入框值
            input.value = currentValue - 1;
            
            // 如果材料已在选择列表中，减少其数量
            if (this.selectedMaterials[materialId] && this.selectedMaterials[materialId].quantity > 0) {
                // 移除一个材料
                this.removeMaterialFromRefine(materialId, 1);
            }
        }
    }
    
    /**
     * 更新材料数量
     * @param {number} materialId - 材料ID
     * @param {number|string} value - 数量值
     */
    updateMaterialQuantity(materialId, value) {
        const input = document.getElementById(`quantity-${materialId}`);
        if (!input) return;
        
        // 获取材料对象
        const material = this.refineMaterials.find(m => m.inventory_id == materialId);
        if (!material) return;
        
        // 获取当前已选择的材料总数
        const currentTotal = Object.values(this.selectedMaterials).reduce((sum, item) => 
            item.id == materialId ? sum : sum + item.quantity, 0);
        
        // 确保值在有效范围内
        let numValue = parseInt(value) || 0;
        const maxValue = parseInt(input.max) || 0;
        
        if (numValue < 0) numValue = 0;
        if (numValue > maxValue) numValue = maxValue;
        
        // 检查是否超过总数限制
        const maxAllowed = Math.min(5 - currentTotal, 5);
        if (numValue > maxAllowed) {
            numValue = maxAllowed;
            this.showRefineStatusMessage(`最多只能选择5个材料进行凝练，已自动调整`, 'info');
        }
        
        // 更新输入框值
        input.value = numValue;
        
        // 获取当前在列表中的数量
        const currentInList = this.selectedMaterials[materialId] ? 
            this.selectedMaterials[materialId].quantity : 0;
        
        // 如果数量有变化，更新材料列表
        if (numValue !== currentInList) {
            if (numValue === 0) {
                // 如果新数量为0，从列表中移除
                if (currentInList > 0) {
                    this.removeMaterialFromRefine(materialId);
                }
            } else if (currentInList === 0) {
                // 如果之前没有，添加到列表
                this.addMaterialToRefine(materialId, numValue);
            } else {
                // 更新现有数量
                // 先从材料添加顺序中移除所有该材料
                this.materialAddOrder = this.materialAddOrder.filter(id => id != materialId);
                
                // 然后重新添加指定数量
                for (let i = 0; i < numValue; i++) {
                    this.materialAddOrder.push(materialId);
                }
                
                // 更新选中材料对象
                this.selectedMaterials[materialId].quantity = numValue;
                
                // 更新UI和状态
                this.updateSelectedMaterialsList();
                this.updateRefineButton();
                this.updateMaterialsAvailability();
                
                // 调试：打印当前材料添加顺序
                console.log('手动输入后的材料添加顺序:', this.materialAddOrder);
            }
        }
    }
    
    /**
     * 添加材料到凝练列表
     * @param {number} materialId - 材料ID
     * @param {number} quantity - 要添加的数量，默认为输入框中的值
     */
    addMaterialToRefine(materialId, quantity = null) {
        // 获取材料数量
        let addQuantity = quantity;
        if (addQuantity === null) {
            const input = document.getElementById(`quantity-${materialId}`);
            if (!input) return;
            addQuantity = parseInt(input.value) || 0;
        }
        
        if (addQuantity <= 0) {
            this.showRefineStatusMessage('请选择至少1个材料', 'error');
            return;
        }
        
        // 查找材料对象
        const material = this.refineMaterials.find(m => m.inventory_id == materialId);
        if (!material) return;
        
        // 计算当前已选择的材料总数
        const currentTotal = Object.values(this.selectedMaterials).reduce((sum, item) => sum + item.quantity, 0);
        
        // 检查是否超过5个上限
        if (currentTotal + addQuantity > 5) {
            // 计算可以添加的数量
            const canAddQuantity = 5 - currentTotal;
            if (canAddQuantity <= 0) {
                this.showRefineStatusMessage('最多只能选择5个材料进行凝练', 'error');
                return;
            }
            
            // 调整添加数量
            addQuantity = canAddQuantity;
            this.showRefineStatusMessage(`只能再添加${canAddQuantity}个材料，已自动调整`, 'info');
        }
        
        // 添加到已选择列表
        if (!this.selectedMaterials[materialId]) {
            this.selectedMaterials[materialId] = {
                id: materialId,
                name: material.name,
                element: material.element,
                element_tier: material.element_tier || 1,
                quantity: addQuantity
            };
            
            // 新增：记录材料添加顺序
            for (let i = 0; i < addQuantity; i++) {
                this.materialAddOrder.push(materialId);
            }
        } else {
            // 如果已存在，增加数量
            this.selectedMaterials[materialId].quantity += addQuantity;
            
            // 新增：记录材料添加顺序
            for (let i = 0; i < addQuantity; i++) {
                this.materialAddOrder.push(materialId);
            }
        }
        
        // 更新UI显示
        this.updateSelectedMaterialsList();
        
        // 如果是通过按钮添加的，重置输入框
        if (quantity === null) {
            const input = document.getElementById(`quantity-${materialId}`);
            if (input) input.value = 0;
        }
        
        // 显示提示
        this.showRefineStatusMessage(
            `已添加材料: ${material.name} x${addQuantity}`, 
            'success'
        );
        
        // 清除之前的费用显示
        this.clearRefineCostDisplay();

        // 更新凝练按钮状态
        this.updateRefineButton();

        // 更新所有材料的可用状态
        this.updateMaterialsAvailability();
        
        // 调试：打印当前材料添加顺序
        console.log('当前材料添加顺序:', this.materialAddOrder);
    }
    
    /**
     * 从凝练列表移除材料
     * @param {number} materialId - 材料ID
     * @param {number} quantity - 要移除的数量，默认为全部
     */
    removeMaterialFromRefine(materialId, quantity = null) {
        if (this.selectedMaterials[materialId]) {
            const materialName = this.selectedMaterials[materialId].name;
            const currentQuantity = this.selectedMaterials[materialId].quantity;
            
            // 确定要移除的数量
            const removeQuantity = (quantity === null || quantity >= currentQuantity) ? 
                currentQuantity : quantity;
            
            if (removeQuantity >= currentQuantity) {
                // 如果移除全部，从列表中删除
                delete this.selectedMaterials[materialId];
                
                // 显示提示
                this.showRefineStatusMessage(`已移除材料: ${materialName} x${currentQuantity}`, 'info');
            } else {
                // 减少数量
                this.selectedMaterials[materialId].quantity -= removeQuantity;
                
                // 显示提示
                this.showRefineStatusMessage(`已移除材料: ${materialName} x${removeQuantity}`, 'info');
            }
            
            // 新增：更新材料添加顺序
            // 从后往前移除指定数量的材料ID
            let removed = 0;
            for (let i = this.materialAddOrder.length - 1; i >= 0 && removed < removeQuantity; i--) {
                if (this.materialAddOrder[i] == materialId) {
                    this.materialAddOrder.splice(i, 1);
                    removed++;
                }
            }
            
            // 更新UI
            this.updateSelectedMaterialsList();

            // 清除之前的费用显示
            this.clearRefineCostDisplay();

            // 更新凝练按钮状态
            this.updateRefineButton();
            
            // 更新所有材料的可用状态
            this.updateMaterialsAvailability();
            
            // 调试：打印当前材料添加顺序
            console.log('更新后的材料添加顺序:', this.materialAddOrder);
        }
    }
    
    /**
     * 更新所有材料的可用状态
     */
    updateMaterialsAvailability() {
        // 获取当前已选择的材料总数
        const currentTotal = Object.values(this.selectedMaterials).reduce((sum, item) => sum + item.quantity, 0);
        
        // 获取所有材料项
        const materialItems = document.querySelectorAll('.material-item');
        
        // 如果已达到5个上限，禁用所有材料
        if (currentTotal >= 5) {
            materialItems.forEach(item => {
                // 禁用加号按钮
                const plusBtn = item.querySelector('.quantity-btn:nth-child(3)');
                
                if (plusBtn) {
                    plusBtn.disabled = true;
                    plusBtn.classList.add('btn-disabled');
                }
                
                // 添加禁用样式到整个材料项
                item.classList.add('material-disabled');
            });
        } else {
            // 否则检查每个材料
            materialItems.forEach(item => {
                // 获取材料ID
                const quantityControl = item.querySelector('.quantity-control');
                if (!quantityControl) return;
                
                const plusBtnOnclick = quantityControl.querySelector('.quantity-btn:nth-child(3)').getAttribute('onclick');
                const materialIdMatch = plusBtnOnclick.match(/increaseMaterialQuantity\((\d+)\)/);
                
                if (materialIdMatch) {
                    const materialId = parseInt(materialIdMatch[1]);
                    
                    // 获取当前选择的数量
                    const currentSelectedQuantity = this.selectedMaterials[materialId] ? 
                        this.selectedMaterials[materialId].quantity : 0;
                    
                    // 如果这个材料已经选了5个，禁用它
                    const isDisabled = currentSelectedQuantity >= 5;
                    
                    // 获取加号按钮
                    const plusBtn = item.querySelector('.quantity-btn:nth-child(3)');
                    
                    if (plusBtn) {
                        plusBtn.disabled = isDisabled;
                        plusBtn.classList.toggle('btn-disabled', isDisabled);
                    }
                    
                    // 更新整个材料项的禁用状态
                    item.classList.toggle('material-disabled', isDisabled);
                }
            });
        }
    }
    
    /**
     * 更新已选择材料列表的UI显示
     */
    updateSelectedMaterialsList() {
        const selectedList = document.getElementById('selected-materials-list');
        const selectedCount = document.getElementById('selected-count');
        
        if (!selectedList || !selectedCount) return;
        
        // 清空列表
        selectedList.innerHTML = '';
        
        // 获取所有已选择的材料
        const selectedMaterials = Object.values(this.selectedMaterials);
        const totalCount = selectedMaterials.reduce((sum, item) => sum + item.quantity, 0);
        
        // 更新计数
        selectedCount.textContent = totalCount;
        
        // 如果没有选择材料，显示提示
        if (selectedMaterials.length === 0) {
            selectedList.innerHTML = '<p class="no-materials-selected">尚未选择任何材料</p>';
            return;
        }
        
        // 显示材料添加顺序
        if (this.materialAddOrder && this.materialAddOrder.length > 0) {
            const orderDiv = document.createElement('div');
            orderDiv.className = 'materials-order-info';
            orderDiv.innerHTML = '<span class="order-title"></span>';
            
            // 创建每个材料的顺序标记
            this.materialAddOrder.forEach((materialId, index) => {
                const material = this.refineMaterials.find(m => m.inventory_id == materialId);
                if (!material) return;
                
                const orderSpan = document.createElement('span');
                orderSpan.className = 'material-order-item';
                orderSpan.textContent = `${index + 1}`;
                orderSpan.title = `第${index + 1}个: ${material.name}`;
                
                orderDiv.appendChild(orderSpan);
            });
            
            selectedList.appendChild(orderDiv);
            
            // 添加分隔线
            const divider = document.createElement('div');
            divider.className = 'materials-divider';
            selectedList.appendChild(divider);
        }
        
        // 添加每个已选择的材料
        selectedMaterials.forEach(material => {
            const elementInfo = this.getElementInfo(material.element);
            
            const materialItem = document.createElement('div');
            materialItem.className = 'selected-material-item';
            
            materialItem.innerHTML = `
                <div class="selected-material-info">
                    <span class="selected-material-name">${material.name} x${material.quantity}</span>
                    <span class="selected-material-element">品级: ${material.element_tier}</span>
                </div>
                <button class="remove-material" onclick="game.buildingManager.removeMaterialFromRefine(${material.id})">×</button>
            `;
            
            selectedList.appendChild(materialItem);
        });
        
        // 添加材料顺序样式
        const style = document.createElement('style');
        if (!document.getElementById('material-order-style')) {
            style.id = 'material-order-style';
            style.textContent = `
                .materials-order-info {
                    display: flex;
                    align-items: center;
                    flex-wrap: wrap;
                    margin-bottom: 10px;
                    padding: 5px;
                    background-color: rgba(0, 0, 0, 0.2);
                    border-radius: 4px;
                    width: 100%;
                }
                .order-title {
                    margin-right: 8px;
                    font-size: 0.9em;
                    color: #aaa;
                }
                .material-order-item {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    margin: 0 3px;
                    border-radius: 50%;
                    background-color: rgba(0, 0, 0, 0.3);
                    font-weight: bold;
                    cursor: help;
                }
                .materials-divider {
                    width: 100%;
                    height: 1px;
                    background-color: rgba(255, 255, 255, 0.1);
                    margin: 5px 0 10px;
                }
            `;
            document.head.appendChild(style);
        }
    }

    /**
     * 确保合成界面的CSS样式已加载
     * 这个方法确保材料合成台和宝石合成台都有相同的样式
     */
    ensureCraftingStyles() {
        // 检查是否已经添加过样式
        if (document.getElementById('crafting-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'crafting-styles';
        style.textContent = `
            .crafting-view {
                padding: 10px 0;
            }
            .crafting-section {
                margin: 15px 0;
                padding-top: 15px;
                border-top: 1px solid var(--border-color);
            }
            .materials-list, .recipes-list {
                max-height: 300px;
                overflow-y: auto;
            }
            .materials-list-items, .recipe-materials-list {
                padding-left: 0;
                list-style-type: none;
            }
            .material-item, .recipe-item {
                padding: 8px;
                margin-bottom: 8px;
                border: 1px solid var(--border-color);
                border-radius: 4px;
            }
            .recipe-item {
                background-color: rgba(255, 255, 255, 0.03);
            }
            .material-name, .recipe-name {
                font-weight: bold;
            }
            .material-quantity, .recipe-result, .recipe-fee {
                display: block;
                margin-top: 4px;
                color: #aaa;
            }
            /* 单独设置费用金币颜色，使其更加醒目 */
            .recipe-fee {
                color:rgb(176, 106, 27);
                font-weight: bold;
            }
            .recipe-header {
                margin-bottom: 10px;
            }
            .recipe-materials {
                margin: 10px 0;
            }
            .recipe-materials-title {
                font-weight: bold;
                margin-bottom: 5px;
                display: block;
            }
            .recipe-materials-list {
                margin: 5px 0;
            }
            .recipe-material {
                margin: 3px 0;
                padding-left: 10px;
            }
            .recipe-material-supplement {
                margin: 3px 0;
                padding-left: 25px;
                font-size: 0.9em;
                position: relative;
            }
            .supplement-mark {
                position: absolute;
                left: 15px;
                color: #aaa;
            }
            .recipe-material.sufficient {
                color: #4caf50;
            }
            .recipe-material.partial {
                color: #ff9800;
            }
            .recipe-material.insufficient, .recipe-material-supplement.insufficient {
                color: #f44336;
            }
            .recipe-material-supplement.sufficient {
                color: #4caf50;
            }
            .recipe-warning {
                color: #ff9800;
                margin: 5px 0;
                padding: 5px;
                border-left: 3px solid #ff9800;
            }
            .recipe-bound-warning {
                color: #e91e63;
                margin: 5px 0;
                padding: 5px;
                border-left: 3px solid #e91e63;
                font-weight: bold;
                font-size: 0.9em;
            }
            .recipe-actions {
                margin-top: 10px;
                text-align: right;
            }
            .material-bound-tag {
                color: #e91e63;
                font-weight: bold;
                margin-left: 2px;
            }

            /* 选项卡样式 */
            .recipe-tabs {
                margin: 10px 0;
                border: 1px solid var(--border-color);
                border-radius: 4px;
                overflow: hidden;
            }
            .tab-header {
                display: flex;
                background-color: rgba(0, 0, 0, 0.2);
                border-bottom: 1px solid var(--border-color);
            }
            .tab-button {
                flex: 1;
                padding: 8px;
                background: none;
                border: none;
                color: #ccc;
                cursor: pointer;
                outline: none;
                transition: all 0.3s;
            }
            .tab-button:hover {
                background-color: rgba(255, 255, 255, 0.05);
            }
            .tab-button.active {
                background-color: rgba(255, 255, 255, 0.1);
                color: var(--primary-color);
                font-weight: bold;
                border-bottom: 2px solid var(--primary-color);
            }
            .tab-content {
                padding: 10px;
            }
            .tab-pane {
                display: none;
            }
            .tab-pane.active {
                display: block;
                animation: fadeIn 0.3s;
            }
            @keyframes fadeIn {
                from { opacity: 0; }
                to { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    /**
     * 显示仓库建筑视图
     * @param {Object} data - 仓库数据
     */
    showWarehouseView(data) {
        console.log('BuildingManager.showWarehouseView - 显示仓库数据:', data);

        this.showBuildingView();
        this.buildingNameEl.textContent = data.name;

        // 智能选择标签页
        this.currentWarehouseTab = this.getSmartWarehouseTab();

        // 清除之前的仓库提示消息
        this.clearWarehouseMessage();

        // 确保背包数据已加载，如果没有或为空则请求加载
        console.log('BuildingManager.showWarehouseView - 检查背包数据:', {
            hasInventory: !!this.gameClient.inventory,
            hasBackpack: !!(this.gameClient.inventory && this.gameClient.inventory.backpack),
            backpackLength: this.gameClient.inventory && this.gameClient.inventory.backpack ? this.gameClient.inventory.backpack.length : 0
        });

        const needsInventoryRefresh = !this.gameClient.inventory ||
                                    !this.gameClient.inventory.backpack ||
                                    this.gameClient.inventory.backpack.length === 0;

        if (needsInventoryRefresh) {
            console.log('BuildingManager.showWarehouseView - 背包数据未加载或为空，请求加载背包数据');
            this.gameClient.sendMessage(MessageProtocol.C2S_GET_INVENTORY, {
                player_id: this.gameClient.currentPlayer.id
            });
        } else {
            console.log('BuildingManager.showWarehouseView - 背包数据已存在，物品数量:', this.gameClient.inventory.backpack.length);
        }

        // 构建仓库界面
        let html = `
            <div class="warehouse-container">
                <div class="warehouse-header">
                    <div class="warehouse-capacity">
                        <div class="capacity-info">
                            <span class="capacity-text">容量: ${data.capacity.used}/${data.capacity.current}</span>
                            <div class="capacity-bar">
                                <div class="capacity-fill" style="width: ${(data.capacity.used / data.capacity.current * 100)}%"></div>
                            </div>
                        </div>
                        ${data.capacity.current < data.capacity.max ?
                            `<button class="btn btn-small expand-btn" onclick="game.buildingManager.showExpandModal()">扩容</button>` :
                            '<span class="max-capacity-text">已达最大容量</span>'
                        }
                    </div>
                </div>

                <div class="warehouse-tabs">
                    <button class="warehouse-tab ${this.currentWarehouseTab === 'storage' ? 'active' : ''}" data-tab="storage" onclick="game.buildingManager.switchWarehouseTab('storage')">仓库物品</button>
                    <button class="warehouse-tab ${this.currentWarehouseTab === 'deposit' ? 'active' : ''}" data-tab="deposit" onclick="game.buildingManager.switchWarehouseTab('deposit')">存入物品</button>
                </div>

                <div class="warehouse-content">
                    <div id="warehouse-storage-tab" class="warehouse-tab-content ${this.currentWarehouseTab === 'storage' ? 'active' : ''}">
                        ${this.renderWarehouseCategoryFilter(data.items)}
                        ${this.renderWarehouseItems(data.items)}
                    </div>
                    <div id="warehouse-deposit-tab" class="warehouse-tab-content ${this.currentWarehouseTab === 'deposit' ? 'active' : ''}">
                        ${this.renderDepositCategoryFilter()}
                        ${this.renderDepositItems()}
                    </div>
                </div>
            </div>
        `;

        // 保存当前的提示消息状态
        const currentMessage = this.getCurrentWarehouseMessage();

        this.buildingContentEl.innerHTML = html;

        // 保存仓库数据
        this.currentWarehouseData = data;

        // 添加仓库样式
        this.addWarehouseStyles();

        // 恢复提示消息状态
        if (currentMessage) {
            setTimeout(() => {
                this.restoreWarehouseMessage(currentMessage);
            }, 50);
        }

        // 延迟刷新存入物品列表，确保DOM已渲染完成
        setTimeout(() => {
            console.log('BuildingManager.showWarehouseView - 延迟刷新存入物品列表');
            this.refreshWarehouseDepositTab();
        }, 100);
    }

    /**
     * 显示仓库操作提示
     * @param {string} message - 提示消息
     * @param {string} type - 提示类型 ('success' 或 'error')
     */
    showWarehouseMessage(message, type = 'success') {
        // 确保提示区域存在
        this.ensureWarehouseMessageArea();

        const messageEl = document.getElementById('warehouse-message');
        if (!messageEl) return;

        // 清除之前的定时器
        if (this.warehouseMessageTimer) {
            clearTimeout(this.warehouseMessageTimer);
        }

        // 检查是否是连续操作（避免页面跳动）
        const isAlreadyVisible = messageEl.style.display === 'block' && messageEl.style.opacity === '1';

        // 更新消息内容
        messageEl.textContent = message;
        messageEl.className = `warehouse-message ${type}`;

        if (!isAlreadyVisible) {
            // 首次显示或从隐藏状态显示
            messageEl.style.display = 'block';
            messageEl.style.opacity = '1';
            messageEl.style.transform = 'translateX(-50%) translateY(0)';
        }

        // 记录操作时间，用于判断是否为连续操作
        this.lastWarehouseOperationTime = Date.now();

        // 延长显示时间到12秒，减少频繁消失
        this.warehouseMessageTimer = setTimeout(() => {
            this.hideWarehouseMessageGradually();
        }, 12000);
    }

    /**
     * 渐进式隐藏仓库消息
     */
    hideWarehouseMessageGradually() {
        const messageEl = document.getElementById('warehouse-message');
        if (!messageEl) return;

        // 检查是否有最近的操作（3秒内），如果有则延迟隐藏
        if (this.lastWarehouseOperationTime && Date.now() - this.lastWarehouseOperationTime < 3000) {
            // 延迟5秒再尝试隐藏
            this.warehouseMessageTimer = setTimeout(() => {
                this.hideWarehouseMessageGradually();
            }, 5000);
            return;
        }

        // 执行隐藏动画
        messageEl.style.opacity = '0';
        messageEl.style.transform = 'translateX(-50%) translateY(-10px)';

        // 动画完成后隐藏元素
        setTimeout(() => {
            if (messageEl && messageEl.style.opacity === '0') { // 确保没有新消息显示
                messageEl.style.display = 'none';
            }
        }, 300);
    }

    /**
     * 获取强化信息HTML
     * @param {Object} instanceData - 物品实例数据
     * @returns {string} 强化信息HTML
     */
    getRefineInfoHtml(instanceData) {
        if (!instanceData.refine_prefix || !instanceData.refine_bonuses) {
            return '';
        }

        let html = `<div class="refined-info" style="background-color: rgba(76, 175, 80, 0.05); padding: 8px; border-left: 3px solid #4caf50; margin-top: 10px;">`;
        html += `<b style="color: #4caf50;">凝练信息:</b>`;
        html += `<div style="margin-left: 10px; margin-top: 5px;">`;
        html += `<p><span style="color: #555;">凝练等级:</span> <b>${instanceData.refine_prefix}</b></p>`;
        if (instanceData.refine_value !== undefined) {
            html += `<p><span style="color: #555;">凝练值:</span> <b>${instanceData.refine_value.toFixed(2)}</b></p>`;
        }

        // 显示凝练属性加成
        html += '<b>凝练属性加成:</b><ul style="margin-top: 5px; padding-left: 20px;">';
        for (const [key, value] of Object.entries(instanceData.refine_bonuses)) {
            const displayName = this.gameClient.playerAttributeMap[key] || key;
            html += `<li>${displayName}: <span style="color: #4caf50; font-weight: bold;">${value > 0 ? '+' : ''}${value}</span></li>`;
        }
        html += '</ul>';
        html += `</div></div>`;

        return html;
    }

    /**
     * 清除仓库提示消息
     */
    clearWarehouseMessage() {
        const messageEl = document.getElementById('warehouse-message');
        if (messageEl) {
            // 清除定时器
            if (this.warehouseMessageTimer) {
                clearTimeout(this.warehouseMessageTimer);
                this.warehouseMessageTimer = null;
            }

            // 立即隐藏消息
            messageEl.style.display = 'none';
            messageEl.style.opacity = '0';
            messageEl.style.transform = 'translateX(-50%) translateY(-10px)';
            messageEl.className = 'warehouse-message';
            messageEl.textContent = '';
        }
    }

    /**
     * 获取当前仓库提示消息状态
     */
    getCurrentWarehouseMessage() {
        const messageEl = document.getElementById('warehouse-message');
        if (messageEl && messageEl.style.display === 'block') {
            return {
                text: messageEl.textContent,
                type: messageEl.classList.contains('success') ? 'success' : 'error',
                timer: this.warehouseMessageTimer
            };
        }
        return null;
    }

    /**
     * 恢复仓库提示消息状态
     */
    restoreWarehouseMessage(messageState) {
        if (messageState && messageState.text) {
            // 清除旧的定时器
            if (this.warehouseMessageTimer) {
                clearTimeout(this.warehouseMessageTimer);
            }

            // 显示消息
            this.showWarehouseMessage(messageState.text, messageState.type);
        }
    }

    /**
     * 确保仓库提示区域存在
     */
    ensureWarehouseMessageArea() {
        // 检查是否已存在提示区域
        let messageEl = document.getElementById('warehouse-message');
        if (!messageEl) {
            // 创建独立的提示区域
            messageEl = document.createElement('div');
            messageEl.id = 'warehouse-message';
            messageEl.className = 'warehouse-message';
            messageEl.style.display = 'none';

            // 插入到建筑视图的开头
            this.buildingView.insertBefore(messageEl, this.buildingView.firstChild);
        }
    }

    /**
     * 渲染仓库分类筛选器
     * @param {Array} items - 仓库物品列表
     * @returns {string} - HTML字符串
     */
    renderWarehouseCategoryFilter(items) {
        if (!items || items.length === 0) {
            return '';
        }

        // 获取玩家仓库中实际拥有的分类
        const categories = ['All', ...new Set(items.map(item => item.category).filter(cat => cat))];

        let html = '<div class="warehouse-category-filter" style="margin-bottom: 15px; text-align: center;">';

        // 检查分类数量，超过6个时分两行显示
        if (categories.length > 6) {
            const firstRow = categories.slice(0, 3);
            const secondRow = categories.slice(3);

            html += '<div style="margin-bottom: 5px;">';
            html += firstRow.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.warehouseFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterWarehouseItems('${cat}')">${displayName}</button>`;
            }).join(' ');
            html += '</div>';

            html += '<div>';
            html += secondRow.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.warehouseFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterWarehouseItems('${cat}')">${displayName}</button>`;
            }).join(' ');
            html += '</div>';
        } else {
            // 分类少于等于6个时，使用单行居中显示
            html += categories.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.warehouseFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterWarehouseItems('${cat}')">${displayName}</button>`;
            }).join(' ');
        }

        html += '</div>';
        return html;
    }

    /**
     * 渲染仓库物品列表
     * @param {Array} items - 仓库物品列表
     * @returns {string} - HTML字符串
     */
    renderWarehouseItems(items) {
        if (!items || items.length === 0) {
            return '<div class="empty-warehouse"><p>仓库中没有物品</p></div>';
        }

        // 根据分类筛选物品
        const filteredItems = this.warehouseFilter === 'All'
            ? items
            : items.filter(item => item.category === this.warehouseFilter);

        if (filteredItems.length === 0) {
            return '<div class="empty-warehouse"><p>该分类下没有物品</p></div>';
        }

        let html = '<div class="warehouse-items-grid">';

        filteredItems.forEach(item => {
            const categoryText = item.category ? `[${this.getCategoryDisplayName(item.category)}]` : '';
            const boundText = item.is_bound == 1 ? '[绑]' : '';
            const boundClass = item.is_bound == 1 ? ' bound-item' : '';
            const itemJson = JSON.stringify(item).replace(/"/g, '&quot;');

            // 处理装备的前缀后缀显示名称
            let displayName = item.name;
            if (item.instance_data) {
                try {
                    const instanceData = JSON.parse(item.instance_data);
                    if (instanceData.display_name) {
                        displayName = instanceData.display_name;
                    }
                } catch (e) {
                    console.error("解析仓库物品实例数据失败:", e);
                }
            }

            html += `
                <div class="warehouse-item">
                    <div class="item-info">
                        <span class="item-name${boundClass}" onclick="game.buildingManager.showWarehouseItemDetail(${itemJson})"
                              title="${displayName}">${categoryText} ${displayName}${boundText}</span>
                    </div>
                    <div class="item-actions">
                        <span class="item-quantity">x${item.quantity}</span>
                        <div class="action-controls">
                            <input type="number" min="1" max="${item.quantity}" value="1"
                                   class="withdraw-quantity" id="withdraw-${item.id}">
                            <button class="btn btn-small" onclick="game.buildingManager.withdrawItem(${item.id},
                                      parseInt(document.getElementById('withdraw-${item.id}').value || 1))">取出</button>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * 渲染存入物品分类筛选器
     * @returns {string} - HTML字符串
     */
    renderDepositCategoryFilter() {
        if (!this.gameClient.inventory || !this.gameClient.inventory.backpack) {
            return '';
        }

        // 过滤掉已装备的物品，获取可存入物品的分类
        const depositableItems = this.gameClient.inventory.backpack.filter(item => !item.is_equipped);

        if (depositableItems.length === 0) {
            return '';
        }

        // 获取玩家背包中实际拥有的分类
        const categories = ['All', ...new Set(depositableItems.map(item => item.category).filter(cat => cat))];

        let html = '<div class="deposit-category-filter" style="margin-bottom: 15px; text-align: center;">';

        // 检查分类数量，超过6个时分两行显示
        if (categories.length > 6) {
            const firstRow = categories.slice(0, 3);
            const secondRow = categories.slice(3);

            html += '<div style="margin-bottom: 5px;">';
            html += firstRow.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.depositFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterDepositItems('${cat}')">${displayName}</button>`;
            }).join(' ');
            html += '</div>';

            html += '<div>';
            html += secondRow.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.depositFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterDepositItems('${cat}')">${displayName}</button>`;
            }).join(' ');
            html += '</div>';
        } else {
            // 分类少于等于6个时，使用单行居中显示
            html += categories.map(cat => {
                const displayName = cat === 'All' ? '全部' : (this.gameClient.categoryMap[cat] || cat);
                const isActive = this.depositFilter === cat;
                const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                return `<button class="btn-small" style="${style}" onclick="game.buildingManager.filterDepositItems('${cat}')">${displayName}</button>`;
            }).join(' ');
        }

        html += '</div>';
        return html;
    }

    /**
     * 渲染可存入物品列表
     * @returns {string} - HTML字符串
     */
    renderDepositItems() {
        if (!this.gameClient.inventory || !this.gameClient.inventory.backpack) {
            return '<div class="loading-text">正在加载背包数据...</div>';
        }

        // 过滤掉已装备的物品
        const depositableItems = this.gameClient.inventory.backpack.filter(item => !item.is_equipped);

        if (depositableItems.length === 0) {
            return '<div class="empty-backpack"><p>背包中没有可存入的物品</p></div>';
        }

        // 根据分类筛选物品
        const filteredItems = this.depositFilter === 'All'
            ? depositableItems
            : depositableItems.filter(item => item.category === this.depositFilter);

        if (filteredItems.length === 0) {
            return '<div class="empty-backpack"><p>该分类下没有可存入的物品</p></div>';
        }

        let html = '<div class="deposit-items-grid">';

        filteredItems.forEach(item => {
            const categoryText = item.category ? `[${this.getCategoryDisplayName(item.category)}]` : '';
            const boundText = item.is_bound == 1 ? '[绑]' : '';
            const boundClass = item.is_bound == 1 ? ' bound-item' : '';

            // 处理装备的前缀后缀显示名称
            let displayName = item.name;
            if (item.instance_data) {
                try {
                    const instanceData = JSON.parse(item.instance_data);
                    if (instanceData.display_name) {
                        displayName = instanceData.display_name;
                    }
                } catch (e) {
                    console.error("解析存入物品实例数据失败:", e);
                }
            }

            html += `
                <div class="deposit-item">
                    <div class="item-info">
                        <span class="item-name${boundClass}" title="${displayName}">${categoryText} ${displayName}${boundText}</span>
                    </div>
                    <div class="item-actions">
                        <span class="item-quantity">x${item.quantity}</span>
                        <div class="action-controls">
                            <input type="number" min="1" max="${item.quantity}" value="1"
                                   class="deposit-quantity" id="deposit-${item.inventory_id}">
                            <button class="btn btn-small" onclick="game.buildingManager.depositItem(${item.inventory_id},
                                      parseInt(document.getElementById('deposit-${item.inventory_id}').value || 1))">存入</button>
                        </div>
                    </div>
                </div>
            `;
        });

        html += '</div>';
        return html;
    }

    /**
     * 智能选择仓库标签页
     * 根据用户的操作历史和当前状态选择合适的标签页
     */
    getSmartWarehouseTab() {
        // 如果用户之前有选择，优先使用用户的选择
        if (this.currentWarehouseTab) {
            return this.currentWarehouseTab;
        }

        // 根据最近的操作类型智能选择
        if (this.lastWarehouseAction === 'deposit') {
            return 'deposit'; // 最近存入物品，可能还要继续存入
        } else if (this.lastWarehouseAction === 'withdraw') {
            return 'storage'; // 最近取出物品，可能还要继续取出
        }

        // 首次打开时，默认显示存入物品标签页（更常用）
        return 'deposit';
    }

    /**
     * 筛选存入物品
     * @param {string} category - 分类名称
     */
    filterDepositItems(category) {
        console.log('BuildingManager.filterDepositItems - 筛选分类:', category);

        // 更新筛选状态
        this.depositFilter = category;

        // 重新渲染存入物品列表
        const depositTab = document.getElementById('warehouse-deposit-tab');
        if (depositTab) {
            // 保存当前提示消息状态
            const currentMessage = this.getCurrentWarehouseMessage();

            // 重新渲染内容
            depositTab.innerHTML = this.renderDepositCategoryFilter() +
                                   this.renderDepositItems();

            // 恢复提示消息状态
            if (currentMessage) {
                setTimeout(() => {
                    this.restoreWarehouseMessage(currentMessage);
                }, 50);
            }
        }
    }

    /**
     * 筛选仓库物品
     * @param {string} category - 分类名称
     */
    filterWarehouseItems(category) {
        console.log('BuildingManager.filterWarehouseItems - 筛选分类:', category);

        // 更新筛选状态
        this.warehouseFilter = category;

        // 重新渲染仓库物品列表
        const storageTab = document.getElementById('warehouse-storage-tab');
        if (storageTab && this.currentWarehouseData) {
            // 保存当前提示消息状态
            const currentMessage = this.getCurrentWarehouseMessage();

            // 重新渲染内容
            storageTab.innerHTML = this.renderWarehouseCategoryFilter(this.currentWarehouseData.items) +
                                   this.renderWarehouseItems(this.currentWarehouseData.items);

            // 恢复提示消息状态
            if (currentMessage) {
                setTimeout(() => {
                    this.restoreWarehouseMessage(currentMessage);
                }, 50);
            }
        }
    }

    /**
     * 切换仓库标签页
     * @param {string} tabName - 标签页名称
     */
    switchWarehouseTab(tabName) {
        // 记录当前标签页
        this.currentWarehouseTab = tabName;

        // 清除之前的提示消息
        this.clearWarehouseMessage();

        // 更新标签按钮状态
        document.querySelectorAll('.warehouse-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');

        // 更新内容区域
        document.querySelectorAll('.warehouse-tab-content').forEach(content => {
            content.classList.remove('active');
        });
        document.getElementById(`warehouse-${tabName}-tab`).classList.add('active');

        // 如果切换到存入标签，刷新背包数据
        if (tabName === 'deposit') {
            const depositTab = document.getElementById('warehouse-deposit-tab');
            depositTab.innerHTML = this.renderDepositCategoryFilter() + this.renderDepositItems();
        }
    }

    /**
     * 存入物品到仓库
     * @param {number} inventoryId - 背包物品ID
     * @param {number} quantity - 存入数量
     */
    depositItem(inventoryId, quantity = 1) {
        console.log('BuildingManager.depositItem - 存入物品:', { inventoryId, quantity });

        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.depositItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('存入失败：无法确定当前建筑');
            return;
        }

        if (quantity <= 0) {
            this.gameClient.addLog('存入数量必须大于0');
            return;
        }

        // 发送存入请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            scene_building_id: this.currentBuilding.scene_building_id,
            inventory_id: inventoryId,
            quantity: quantity
        };

        console.log('BuildingManager.depositItem - 发送存入请求:', payload);

        // 记录操作类型
        this.lastWarehouseAction = 'deposit';

        this.gameClient.sendMessage(MessageProtocol.C2S_WAREHOUSE_DEPOSIT, payload);
    }

    /**
     * 从仓库取出物品
     * @param {number} warehouseItemId - 仓库物品ID
     * @param {number} quantity - 取出数量
     */
    withdrawItem(warehouseItemId, quantity = 1) {
        console.log('BuildingManager.withdrawItem - 取出物品:', { warehouseItemId, quantity });

        if (!this.currentBuilding || !this.currentBuilding.scene_building_id) {
            console.error('BuildingManager.withdrawItem - 错误: 无法确定当前建筑');
            this.gameClient.addLog('取出失败：无法确定当前建筑');
            return;
        }

        if (quantity <= 0) {
            this.gameClient.addLog('取出数量必须大于0');
            return;
        }

        // 发送取出请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            scene_building_id: this.currentBuilding.scene_building_id,
            warehouse_item_id: warehouseItemId,
            quantity: quantity
        };

        console.log('BuildingManager.withdrawItem - 发送取出请求:', payload);

        // 记录操作类型
        this.lastWarehouseAction = 'withdraw';

        this.gameClient.sendMessage(MessageProtocol.C2S_WAREHOUSE_WITHDRAW, payload);
    }

    /**
     * 显示仓库物品详情
     * @param {Object} item - 仓库物品数据
     */
    showWarehouseItemDetail(item) {
        console.log('BuildingManager.showWarehouseItemDetail - 显示仓库物品详情:', item);

        // 设置物品详情来源为仓库
        this.gameClient.itemDetailOrigin = 'warehouse';

        // 隐藏建筑视图，显示物品详情视图
        document.getElementById('buildingView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'block';

        // 清除错误和成功消息
        const itemDetailError = document.getElementById('itemDetailError');
        const itemDetailSuccess = document.getElementById('itemDetailSuccess');
        itemDetailError.textContent = '';
        itemDetailError.style.borderColor = 'transparent';
        itemDetailSuccess.textContent = '';
        itemDetailSuccess.style.borderColor = 'transparent';

        // 显示物品名称（支持前缀后缀）
        const itemNameElement = document.getElementById('itemDetailName');
        let displayName = item.name;
        if (item.instance_data) {
            try {
                const instanceData = JSON.parse(item.instance_data);
                if (instanceData.display_name) {
                    displayName = instanceData.display_name;
                    // 处理颜色标记
                    displayName = displayName.replace(/§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">');
                    displayName = displayName.replace(/§/g, '</span>');
                }
            } catch (e) {
                console.error("解析仓库物品实例数据失败:", e);
            }
        }
        itemNameElement.innerHTML = displayName;

        // 构建物品详情内容（参考商店物品详情的实现）
        let contentHtml = '';

        // 显示分类
        const categoryDisplayName = this.gameClient.categoryMap[item.category] || item.category;
        contentHtml += `<p><b>分类:</b> ${categoryDisplayName}</p>`;

        // 显示物品描述
        if (item.description && item.description !== '暂无描述') {
            contentHtml += `<p style="color: #555;">${item.description}</p>`;
        }

        // 显示存储数量
        contentHtml += `<p><b>存储数量:</b> <span style="color: #28a745; font-weight: bold;">${item.quantity}</span></p>`;

        // 显示存入时间
        if (item.created_at) {
            const createTime = new Date(item.created_at).toLocaleString('zh-CN');
            contentHtml += `<p><b>存入时间:</b> ${createTime}</p>`;
        }

        contentHtml += `<hr class="section-divider">`;

        // 显示装备属性（如果有stats）
        if (item.stats) {
            try {
                const stats = typeof item.stats === 'string' ? JSON.parse(item.stats) : item.stats;
                if (stats && Object.keys(stats).length > 0) {
                    contentHtml += '<b>装备属性:</b><ul style="margin-top: 5px; padding-left: 20px;">';
                    for (const [key, value] of Object.entries(stats)) {
                        const displayName = this.gameClient.playerAttributeMap[key] || key;
                        const sign = value > 0 ? '+' : '';
                        const color = value > 0 ? '#28a745' : '#dc3545';
                        contentHtml += `<li>${displayName}: <span style="color: ${color}; font-weight: bold;">${sign}${value}</span></li>`;
                    }
                    contentHtml += '</ul>';
                }
            } catch (e) {
                console.warn('解析装备属性失败:', e);
            }
        }

        // 显示强化属性（凝练信息）
        if (item.instance_data) {
            try {
                const instanceData = JSON.parse(item.instance_data);
                if (instanceData.refine_prefix && instanceData.refine_bonuses) {
                    contentHtml += this.getRefineInfoHtml(instanceData);
                }
            } catch (e) {
                console.warn('解析强化属性失败:', e);
            }
        }

        // 显示插槽信息（如果有）
        if (item.sockets && item.sockets > 0) {
            contentHtml += `<p><b>宝石插槽:</b> <span style="color: #17a2b8; font-weight: bold;">${item.sockets} 个</span></p>`;
        }

        // 显示物品效果
        let parsedEffects = null;
        if (item.effects) {
            try {
                parsedEffects = typeof item.effects === 'string' ? JSON.parse(item.effects) : item.effects;
            } catch (e) {
                console.warn('解析物品效果失败:', e);
            }
        }

        if (parsedEffects && Object.keys(parsedEffects).length > 0) {
            contentHtml += '<hr class="section-divider"><b>物品效果:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            for (const [key, value] of Object.entries(parsedEffects)) {
                if (key !== 'learn_skill_id') { // 不显示技能ID，参考背包物品详情的处理
                    const displayName = this.gameClient.playerAttributeMap[key] || key;
                    const sign = value > 0 ? '+' : '';
                    const color = value > 0 ? '#28a745' : '#dc3545';
                    contentHtml += `<li>${displayName}: <span style="color: ${color}; font-weight: bold;">${sign}${value}</span></li>`;
                }
            }
            contentHtml += '</ul>';
        }

        // 检查是否是技能书，参考背包物品详情的处理
        const isSkillBook = parsedEffects && parsedEffects.learn_skill_id;
        if (isSkillBook) {
            contentHtml += `<p style="color: #4b0082; margin-top: 10px;">可学习新技能</p>`;
        }

        document.getElementById('itemDetailContent').innerHTML = contentHtml;

        // 清空操作按钮区域（仓库物品详情不需要操作按钮）
        document.getElementById('itemDetailActions').innerHTML = '';

        // 保存当前物品信息，以便返回时使用
        this.currentWarehouseItem = item;
    }

    /**
     * 显示扩容模态框
     */
    showExpandModal() {
        if (!this.currentWarehouseData || !this.currentWarehouseData.expansion_items) {
            this.showWarehouseMessage('没有可用的扩容物品', 'error');
            return;
        }

        const expansionItems = this.currentWarehouseData.expansion_items;
        if (expansionItems.length === 0) {
            this.showWarehouseMessage('没有可用的扩容物品', 'error');
            return;
        }

        // 创建扩容模态框
        let modalHtml = `
            <div id="warehouse-expand-modal" class="modal-dialog" style="display: flex; background-color: rgba(0,0,0,0.5);">
                <div class="modal-content" style="max-width: 340px; width: 90%;">
                    <div class="modal-header">
                        <h3>仓库扩容</h3>
                        <button class="modal-close" onclick="game.buildingManager.hideExpandModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <p>选择扩容物品来增加仓库容量：</p>
                        <div class="expansion-items-list">
        `;

        expansionItems.forEach(item => {
            const remainingUses = item.max_uses ? (item.max_uses - item.current_usage) : '无限制';

            modalHtml += `
                <div class="expansion-item">
                    <div class="expansion-item-info">
                        <span class="expansion-item-name">${item.item_name}</span>
                        <span class="expansion-amount">+${item.expansion_amount} 空间</span>
                        <span class="remaining-uses">剩余使用: ${remainingUses}</span>
                    </div>
                    <button class="btn btn-primary" onclick="game.buildingManager.selectExpansionItem(${item.item_template_id})">选择</button>
                </div>
            `;
        });

        modalHtml += `
                        </div>
                    </div>
                </div>
            </div>
        `;

        // 添加到页面
        document.body.insertAdjacentHTML('beforeend', modalHtml);
    }

    /**
     * 隐藏扩容模态框
     */
    hideExpandModal() {
        const modal = document.getElementById('warehouse-expand-modal');
        if (modal) {
            modal.remove();
        }
    }

    /**
     * 选择扩容物品
     * @param {number} itemTemplateId - 扩容物品模板ID
     */
    selectExpansionItem(itemTemplateId) {
        console.log('BuildingManager.selectExpansionItem - 选择扩容物品:', itemTemplateId);

        // 查找玩家背包中的该物品
        if (!this.gameClient.inventory || !this.gameClient.inventory.backpack) {
            this.showWarehouseMessage('背包数据未加载', 'error');
            return;
        }

        // 查找匹配的物品
        const expansionItem = this.gameClient.inventory.backpack.find(item =>
            item.item_template_id == itemTemplateId && !item.is_equipped
        );

        if (!expansionItem) {
            this.showWarehouseMessage('背包中没有该扩容物品', 'error');
            return;
        }

        // 发送扩容请求
        const payload = {
            player_id: this.gameClient.currentPlayer.id,
            scene_building_id: this.currentBuilding.scene_building_id,
            inventory_id: expansionItem.inventory_id
        };

        console.log('BuildingManager.selectExpansionItem - 发送扩容请求:', payload);
        this.gameClient.sendMessage(MessageProtocol.C2S_WAREHOUSE_EXPAND, payload);

        // 隐藏模态框
        this.hideExpandModal();
    }

    /**
     * 刷新仓库存入物品标签页
     */
    refreshWarehouseDepositTab() {
        const depositTab = document.getElementById('warehouse-deposit-tab');
        if (depositTab) {
            // 保存当前提示消息状态
            const currentMessage = this.getCurrentWarehouseMessage();

            // 重新渲染存入物品标签页，包含分类筛选器和物品列表
            depositTab.innerHTML = this.renderDepositCategoryFilter() + this.renderDepositItems();

            // 恢复提示消息（如果存在且没有新的待显示消息）
            if (currentMessage && !this.pendingWarehouseMessage) {
                setTimeout(() => {
                    this.restoreWarehouseMessage(currentMessage);
                }, 50);
            }
        }
    }

    /**
     * 添加仓库样式
     */
    addWarehouseStyles() {
        if (document.getElementById('warehouse-styles')) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = 'warehouse-styles';
        style.textContent = `
            .warehouse-container {
                padding: 15px;
            }

            .warehouse-header {
                margin-bottom: 20px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                border: 1px solid #dee2e6;
            }

            .warehouse-message {
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                padding: 8px 16px;
                border-radius: 6px;
                font-size: 13px;
                font-weight: 500;
                text-align: center;
                box-shadow: 0 2px 8px rgba(0,0,0,0.15);
                z-index: 10000;
                transition: opacity 0.3s ease, transform 0.3s ease;
                opacity: 0;
                transform: translateX(-50%) translateY(-10px);
                pointer-events: none;
                max-width: 350px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .warehouse-message.success {
                background-color: #d4edda;
                color: #155724;
                border: 1px solid #c3e6cb;
            }

            .warehouse-message.error {
                background-color: #f8d7da;
                color: #721c24;
                border: 1px solid #f5c6cb;
            }

            .warehouse-message.visible {
                opacity: 1;
                transform: translateY(0);
            }

            .warehouse-capacity {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .capacity-info {
                flex: 1;
                margin-right: 15px;
            }

            .capacity-text {
                display: block;
                font-weight: bold;
                margin-bottom: 5px;
                color: #495057;
            }

            .capacity-bar {
                width: 100%;
                height: 20px;
                background-color: #e9ecef;
                border-radius: 10px;
                overflow: hidden;
            }

            .capacity-fill {
                height: 100%;
                background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #ffc107 80%, #dc3545 100%);
                transition: width 0.3s ease;
            }

            .expand-btn {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                cursor: pointer;
                font-size: 14px;
            }

            .expand-btn:hover {
                background-color: #0056b3;
            }

            .max-capacity-text {
                color: #6c757d;
                font-style: italic;
            }

            .warehouse-tabs {
                display: flex;
                margin-bottom: 20px;
                border-bottom: 2px solid #dee2e6;
            }

            .warehouse-tab {
                padding: 10px 20px;
                background: none;
                border: none;
                cursor: pointer;
                font-size: 16px;
                color: #6c757d;
                border-bottom: 2px solid transparent;
                transition: all 0.3s ease;
            }

            .warehouse-tab.active {
                color: #007bff;
                border-bottom-color: #007bff;
            }

            .warehouse-tab:hover {
                color: #007bff;
            }

            .warehouse-tab-content {
                display: none;
            }

            .warehouse-tab-content.active {
                display: block;
            }

            .warehouse-items-grid {
                display: flex;
                flex-direction: column;
                gap: 10px;
                max-height: 350px;
                overflow-y: auto;
                padding-right: 5px;
            }

            .warehouse-items-grid::-webkit-scrollbar {
                width: 6px;
            }

            .warehouse-items-grid::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .warehouse-items-grid::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            .warehouse-items-grid::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }

            .deposit-items-grid {
                display: flex;
                flex-direction: column;
                gap: 8px;
                max-height: 300px;
                overflow-y: auto;
                padding-right: 5px;
            }

            .deposit-items-grid::-webkit-scrollbar {
                width: 6px;
            }

            .deposit-items-grid::-webkit-scrollbar-track {
                background: #f1f1f1;
                border-radius: 3px;
            }

            .deposit-items-grid::-webkit-scrollbar-thumb {
                background: #c1c1c1;
                border-radius: 3px;
            }

            .deposit-items-grid::-webkit-scrollbar-thumb:hover {
                background: #a8a8a8;
            }

            .warehouse-item {
                padding: 10px 12px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background-color: #fff;
                transition: box-shadow 0.3s ease;
                min-height: 60px;
            }

            .deposit-item {
                padding: 10px 12px;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                background-color: #fff;
                transition: box-shadow 0.3s ease;
                min-height: 60px;
            }

            .warehouse-item:hover,
            .deposit-item:hover {
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }

            .warehouse-item .item-info,
            .deposit-item .item-info {
                margin-bottom: 8px;
            }

            .warehouse-item .item-name,
            .deposit-item .item-name {
                display: block;
                font-weight: bold;
                color: #495057;
                font-size: 14px;
                margin-bottom: 3px;
                line-height: 1.3;
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .warehouse-item .item-name {
                cursor: pointer;
            }

            .warehouse-category-filter,
            .deposit-category-filter {
                border-bottom: 1px solid #dee2e6;
                padding-bottom: 10px;
                margin-bottom: 15px;
            }

            .warehouse-category-filter .btn-small,
            .deposit-category-filter .btn-small {
                margin: 2px 1px;
                padding: 4px 8px;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                font-size: 12px;
                cursor: pointer;
                transition: all 0.2s ease;
            }

            .warehouse-category-filter .btn-small:hover,
            .deposit-category-filter .btn-small:hover {
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                transform: translateY(-1px);
            }

            /* 绑定物品样式 */
            .item-name.bound-item {
                color: #dc3545;
            }

            .item-name.bound-item::after {
                content: '';
                display: inline-block;
                width: 4px;
                height: 4px;
                background-color: #dc3545;
                border-radius: 50%;
                margin-left: 4px;
                vertical-align: middle;
            }

            .item-name:hover {
                color: #007bff;
            }

            .warehouse-item .item-quantity {
                color: #6c757d;
                font-size: 13px;
            }

            .deposit-item .item-quantity {
                color: #6c757d;
                font-size: 12px;
            }

            .warehouse-item .item-actions {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 8px;
            }

            .deposit-item .item-actions {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 6px;
            }

            .action-controls {
                display: flex;
                align-items: center;
                gap: 6px;
            }

            .withdraw-quantity,
            .deposit-quantity {
                padding: 3px 6px;
                border: 1px solid #ced4da;
                border-radius: 3px;
                font-size: 12px;
                width: 45px;
            }

            .deposit-item .btn {
                padding: 4px 8px;
                font-size: 12px;
                white-space: nowrap;
            }

            .warehouse-item .btn {
                padding: 4px 8px;
                font-size: 12px;
                white-space: nowrap;
            }

            /* 响应式设计 */
            @media (max-width: 360px) {
                .withdraw-quantity,
                .deposit-quantity {
                    width: 40px;
                }

                .warehouse-item .item-actions,
                .deposit-item .item-actions {
                    gap: 4px;
                }

                .action-controls {
                    gap: 4px;
                }
            }

            .empty-warehouse,
            .empty-backpack {
                text-align: center;
                padding: 40px;
                color: #6c757d;
            }

            .loading-text {
                text-align: center;
                padding: 40px;
                color: #6c757d;
            }

            .expansion-items-list {
                max-height: 300px;
                overflow-y: auto;
            }

            .expansion-item {
                display: flex;
                align-items: center;
                justify-content: space-between;
                padding: 15px;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                margin-bottom: 10px;
                background-color: #f8f9fa;
            }

            .expansion-item-info {
                flex: 1;
            }

            .expansion-item-name {
                display: block;
                font-weight: bold;
                color: #495057;
                margin-bottom: 5px;
            }

            .expansion-amount {
                display: block;
                color: #28a745;
                font-weight: bold;
                margin-bottom: 3px;
            }

            .remaining-uses {
                display: block;
                color: #6c757d;
                font-size: 12px;
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 显示属性重修建筑视图
     * @param {Object} data - 属性重修建筑数据
     */
    showAttributeResetView(data) {
        console.log('BuildingManager.showAttributeResetView - 显示属性重修数据:', data);

        this.showBuildingView();
        this.buildingNameEl.textContent = data.name;

        const config = data.config;
        const playerStatus = data.player_status;

        // 计算重修后的状态
        // 重修后潜力点 = 当前已分配的属性点 + 当前潜力点
        const afterResetPotentialPoints = playerStatus.current_allocated_points + playerStatus.potential_points;

        // 检查是否需要重修（四维属性都是5点就不需要重修）
        const isAlreadyReset = playerStatus.current_attributes.strength === 5 &&
                              playerStatus.current_attributes.agility === 5 &&
                              playerStatus.current_attributes.constitution === 5 &&
                              playerStatus.current_attributes.intelligence === 5;

        const canReset = playerStatus.has_required_item && !playerStatus.has_equipped_items && !isAlreadyReset;

        let html = `
            <div class="attribute-reset-container">
                ${!isAlreadyReset ? `
                <div class="reset-requirements">
                    <h4>重修要求：</h4>
                    <div class="requirement-item ${!playerStatus.has_equipped_items ? 'requirement-met' : 'requirement-not-met'}">
                        <span class="requirement-icon">${!playerStatus.has_equipped_items ? '✅' : '❌'}</span>
                        <span class="requirement-text">卸下所有装备</span>
                    </div>
                    <div class="requirement-item ${playerStatus.has_required_item ? 'requirement-met' : 'requirement-not-met'}">
                        <span class="requirement-icon">${playerStatus.has_required_item ? '✅' : '❌'}</span>
                        <span class="requirement-text">消耗 ${config.required_quantity} 个 ${config.item_name}</span>
                        <span class="requirement-detail">（拥有 ${playerStatus.player_item_count} 个）</span>
                    </div>
                </div>
                ` : `
                <div class="reset-status">
                    <div class="status-message">
                        <span class="status-icon">✅</span>
                        <span class="status-text">您的属性已经是初始状态，无需重修</span>
                    </div>
                </div>
                `}

                <div class="reset-preview">
                    <h4>重修后状态：</h4>
                    <div class="preview-result">
                        <div class="result-section">
                            <div class="result-value">力量、敏捷、体质、智力 各 5 点</div>
                        </div>
                        <div class="result-section highlight">
                            <div class="result-title">可分配潜力点</div>
                            <div class="result-value">${afterResetPotentialPoints} 点</div>
                        </div>
                    </div>
                </div>

                <div class="reset-actions">
                    ${isAlreadyReset ?
                        `<button class="btn btn-disabled" disabled>
                            属性已是初始状态
                        </button>` :
                        canReset ?
                        `<button class="btn btn-primary reset-btn" onclick="game.buildingManager.showResetConfirmation(${data.scene_building_id})">
                            重修属性
                        </button>` :
                        `<button class="btn btn-disabled" disabled>
                            无法重修（不满足条件）
                        </button>`
                    }
                </div>
            </div>
        `;

        this.buildingContentEl.innerHTML = html;

        // 添加样式
        this.addAttributeResetStyles();
    }

    /**
     * 显示重修确认界面
     * @param {number} sceneBuildingId - 场景建筑ID
     */
    showResetConfirmation(sceneBuildingId) {
        // 获取当前建筑数据
        const buildingData = this.currentBuilding;
        if (!buildingData) return;

        const config = buildingData.config;
        const playerStatus = buildingData.player_status;
        const afterResetPotentialPoints = playerStatus.current_allocated_points + playerStatus.potential_points;

        // 创建覆盖式确认界面
        const confirmationHtml = `
            <div class="reset-confirmation-overlay">
                <div class="confirmation-modal">
                    <div class="confirmation-header">
                        <h4>⚠️ 确认重修属性</h4>
                    </div>
                    <div class="confirmation-content">
                        <div class="warning-message">
                            <p><strong>此操作将：</strong></p>
                            <ul>
                                <li>重置所有属性为初始值（各5点）</li>
                                <li>恢复所有潜力点为 ${afterResetPotentialPoints} 点</li>
                                <li>消耗 ${config.required_quantity} 个 ${config.item_name}</li>
                                <li>重置最大生命值和魔力值为初始值</li>
                            </ul>
                            <p class="warning-note"><strong>注意：此操作不可撤销！</strong></p>
                        </div>
                    </div>
                    <div class="confirmation-actions">
                        <button class="btn btn-danger reset-execute-btn" onclick="game.buildingManager.executeAttributeReset(${sceneBuildingId})">
                            确认执行重修
                        </button>
                        <button class="btn btn-secondary" onclick="game.buildingManager.hideResetConfirmation()">
                            取消
                        </button>
                    </div>
                </div>
            </div>
        `;

        // 直接替换建筑内容
        this.buildingContentEl.innerHTML = confirmationHtml;
    }

    /**
     * 隐藏重修确认界面，返回原界面
     */
    hideResetConfirmation() {
        // 重新显示原来的建筑界面
        if (this.currentBuilding) {
            this.showAttributeResetView(this.currentBuilding);
        }
    }

    /**
     * 执行属性重修
     * @param {number} sceneBuildingId - 场景建筑ID
     */
    executeAttributeReset(sceneBuildingId) {
        // 发送重修请求
        this.gameClient.sendMessage(MessageProtocol.C2S_ATTRIBUTE_RESET, {
            player_id: this.gameClient.currentPlayer.id,
            scene_building_id: sceneBuildingId
        });

        // 显示处理中状态
        const executeBtn = document.querySelector('.reset-execute-btn');
        if (executeBtn) {
            executeBtn.disabled = true;
            executeBtn.textContent = '处理中...';
        }
    }

    /**
     * 添加属性重修相关样式
     */
    addAttributeResetStyles() {
        if (document.getElementById('attribute-reset-styles')) return;

        const style = document.createElement('style');
        style.id = 'attribute-reset-styles';
        style.textContent = `
            .attribute-reset-container {
                padding: 20px;
                max-width: 600px;
                margin: 0 auto;
            }



            .reset-requirements {
                margin-bottom: 25px;
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 8px;
                margin-top: 0;
            }

            .reset-requirements h4 {
                margin-bottom: 15px;
                color: #495057;
            }

            .reset-status {
                margin-bottom: 25px;
                padding: 15px;
                background-color: #d4edda;
                border-radius: 8px;
                border: 1px solid #c3e6cb;
            }

            .status-message {
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 10px;
            }

            .status-icon {
                margin-right: 10px;
                font-size: 18px;
            }

            .status-text {
                color: #155724;
                font-weight: 500;
                font-size: 16px;
            }

            .requirement-item {
                display: flex;
                align-items: center;
                margin-bottom: 10px;
                padding: 8px;
                border-radius: 5px;
            }

            .requirement-met {
                background-color: #d4edda;
                border: 1px solid #c3e6cb;
            }

            .requirement-not-met {
                background-color: #f8d7da;
                border: 1px solid #f5c6cb;
            }

            .requirement-icon {
                margin-right: 10px;
                font-size: 16px;
            }

            .requirement-text {
                font-weight: 500;
                flex: 1;
            }

            .requirement-detail {
                font-size: 12px;
                color: #6c757d;
                margin-left: 10px;
            }

            .reset-preview {
                margin-bottom: 25px;
                padding: 15px;
                background-color: #fff;
                border: 1px solid #dee2e6;
                border-radius: 8px;
            }

            .reset-preview h4 {
                margin-bottom: 15px;
                color: #495057;
            }

            .preview-result {
                display: flex;
                flex-direction: column;
                gap: 15px;
            }

            .result-section {
                padding: 15px;
                background-color: #f8f9fa;
                border-radius: 6px;
                border: 1px solid #e9ecef;
                text-align: center;
            }

            .result-section.highlight {
                background-color: #e3f2fd;
                border-color: #2196f3;
            }

            .result-title {
                font-size: 14px;
                color: #6c757d;
                margin-bottom: 8px;
                font-weight: 500;
            }

            .result-value {
                font-size: 18px;
                color: #495057;
                font-weight: 600;
            }

            .result-section.highlight .result-title {
                color: #1976d2;
            }

            .result-section.highlight .result-value {
                color: #1976d2;
                font-size: 20px;
            }



            .reset-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
                padding-top: 20px;
                border-top: 1px solid #e9ecef;
            }

            .reset-btn {
                background-color: #007bff;
                border-color: #007bff;
                color: white;
                padding: 10px 20px;
                font-weight: 500;
            }

            .reset-btn:hover:not(:disabled) {
                background-color: #0056b3;
                border-color: #004085;
            }

            .reset-confirmation-overlay {
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-color: rgba(0, 0, 0, 0.1);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 1000;
                animation: fadeIn 0.2s ease-out;
            }

            .confirmation-modal {
                background-color: #fff3cd;
                border: 2px solid #ffc107;
                border-radius: 8px;
                padding: 20px;
                max-width: 300px;
                overflow-y: auto;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
                animation: scaleIn 0.2s ease-out;
                margin: 20px;
            }

            .confirmation-header {
                margin-bottom: 15px;
                text-align: center;
            }

            .confirmation-header h4 {
                color: #856404;
                margin: 0;
            }

            .confirmation-content {
                margin-bottom: 20px;
            }

            .warning-message {
                background-color: #fff;
                padding: 15px;
                border-radius: 6px;
                border: 1px solid #ffeaa7;
            }

            .warning-message p {
                margin: 0 0 10px 0;
                color: #495057;
            }

            .warning-message ul {
                margin: 10px 0;
                padding-left: 20px;
                color: #495057;
            }

            .warning-message li {
                margin-bottom: 5px;
            }

            .warning-note {
                color: #dc3545 !important;
                font-weight: 600 !important;
                margin-top: 15px !important;
            }

            .confirmation-actions {
                display: flex;
                gap: 15px;
                justify-content: center;
            }

            .reset-execute-btn {
                background-color: #dc3545;
                border-color: #dc3545;
                color: white;
                padding: 10px 20px;
                font-weight: 500;
            }

            .reset-execute-btn:hover:not(:disabled) {
                background-color: #c82333;
                border-color: #bd2130;
            }

            .reset-execute-btn:disabled {
                background-color: #6c757d;
                border-color: #6c757d;
                cursor: not-allowed;
            }

            @keyframes fadeIn {
                from {
                    opacity: 0;
                }
                to {
                    opacity: 1;
                }
            }

            @keyframes scaleIn {
                from {
                    opacity: 0;
                    transform: scale(0.9);
                }
                to {
                    opacity: 1;
                    transform: scale(1);
                }
            }

            @media (max-width: 768px) {
                .reset-actions {
                    flex-direction: column;
                }

                .result-value {
                    font-size: 16px;
                }

                .result-section.highlight .result-value {
                    font-size: 18px;
                }

                .confirmation-modal {
                    margin: 10px;
                    padding: 15px;
                    max-height: 95vh;
                }

                .confirmation-actions {
                    flex-direction: column;
                    gap: 10px;
                }

                .confirmation-actions .btn {
                    width: 100%;
                }
            }
        `;

        document.head.appendChild(style);
    }
}