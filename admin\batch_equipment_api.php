<?php
// admin/batch_equipment_api.php - 批量装备创建API
require_once '../config/Database.php';
require_once 'auth.php';

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '无效的操作'];

try {
    $input = file_get_contents('php://input');
    $data = json_decode($input, true);
    
    if (!$data) {
        throw new Exception('无效的JSON数据');
    }
    
    $action = $data['action'] ?? '';
    
    switch ($action) {
        case 'create_batch':
            createBatchEquipment($data);
            break;
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    http_response_code(400);
    $response = ['success' => false, 'message' => $e->getMessage()];
    echo json_encode($response, JSON_UNESCAPED_UNICODE);
}

function createBatchEquipment($data) {
    $equipmentList = $data['equipment_data'] ?? [];
    
    if (empty($equipmentList)) {
        throw new Exception('没有提供装备数据');
    }
    
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    try {
        $conn->beginTransaction();
        
        $createdItems = [];
        $errors = [];
        
        foreach ($equipmentList as $index => $equipment) {
            try {
                // 调试：显示接收到的装备数据
                error_log("接收到的装备数据 #{$index}: " . json_encode($equipment, JSON_UNESCAPED_UNICODE));

                $itemId = createSingleEquipment($equipment, $db);
                $createdItems[] = [
                    'id' => $itemId,
                    'name' => $equipment['name']
                ];
            } catch (Exception $e) {
                $errors[] = "装备 '{$equipment['name']}' 创建失败: " . $e->getMessage();
                error_log("批量创建装备错误 - 索引 {$index}: " . $e->getMessage());
            }
        }
        
        if (empty($createdItems)) {
            throw new Exception('所有装备创建都失败了: ' . implode('; ', $errors));
        }
        
        $conn->commit();
        
        $message = "成功创建 " . count($createdItems) . " 件装备";
        if (!empty($errors)) {
            $message .= "，" . count($errors) . " 件失败";
        }
        
        echo json_encode([
            'success' => true,
            'message' => $message,
            'created_items' => $createdItems,
            'errors' => $errors
        ], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        if ($conn->inTransaction()) {
            $conn->rollBack();
        }
        throw $e;
    }
}

function createSingleEquipment($equipment, $db) {
    // 验证和清理数据
    validateEquipmentData($equipment);
    $equipment = sanitizeEquipmentData($equipment);

    // 获取PDO连接（参考现有API的做法）
    $conn = $db->getConnection();
    $pdo = $conn;

    // 生成唯一的item_id
    $stmt = $db->query("SELECT AUTO_INCREMENT FROM information_schema.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'item_templates'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    $nextId = $result['AUTO_INCREMENT'] ?? 1;
    $itemId = 'item_' . $nextId;
    $stmt->closeCursor();

    // 确定装备类型
    $equipmentType = isset($equipment['equipment_type']) ? $equipment['equipment_type'] : 'player';

    // 调试信息
    error_log("创建装备 {$equipment['name']} 的类型信息: " . json_encode([
        'equipment_type_from_data' => $equipment['equipment_type'] ?? 'not_set',
        'final_equipment_type' => $equipmentType
    ]));

    // 插入到item_templates表（使用PDO连接）
    $stmt = $pdo->prepare("INSERT INTO item_templates (item_id, name, category, description, is_consumable, stackable, max_stack, buy_price, diamond_price, sell_price, effects, equipment_type) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
    $result = $stmt->execute([
        $itemId,
        $equipment['name'],
        'Equipment',
        $equipment['description'],
        0, // is_consumable
        0, // stackable
        1, // max_stack
        $equipment['buy_price'],
        null, // diamond_price
        $equipment['sell_price'],
        null, // effects
        $equipmentType // equipment_type
    ]);

    if (!$result) {
        $errorInfo = $stmt->errorInfo();
        throw new Exception("插入item_templates失败: " . $errorInfo[2]);
    }

    $templateId = $pdo->lastInsertId();

    if (!$templateId) {
        throw new Exception("获取插入ID失败，lastInsertId返回: {$templateId}");
    }

    // 准备属性JSON数据
    $stats = $equipment['stats'] ?? [];
    $statsJson = !empty($stats) ? json_encode($stats, JSON_UNESCAPED_UNICODE) : null;

    // 验证item_template_id是否存在（使用同一个PDO连接）
    $checkStmt = $pdo->prepare("SELECT id FROM item_templates WHERE id = ?");
    $checkStmt->execute([$templateId]);
    if (!$checkStmt->fetch()) {
        throw new Exception("刚创建的item_template记录不存在，ID: {$templateId}");
    }
    $checkStmt->closeCursor();

    // 插入到equipment_details表（使用同一个PDO连接）
    $equipStmt = $pdo->prepare("INSERT INTO equipment_details (item_template_id, slot, job_restriction, stats, sockets, grants_job_id) VALUES (?, ?, ?, ?, ?, ?)");
    $equipResult = $equipStmt->execute([
        $templateId,
        $equipment['slot'],
        $equipment['job_restriction'],
        $statsJson,
        $equipment['sockets'],
        $equipment['grants_job_id']
    ]);

    if (!$equipResult) {
        $errorInfo = $equipStmt->errorInfo();
        throw new Exception("插入equipment_details失败: " . $errorInfo[2]);
    }

    // 记录调试信息
    error_log("成功创建装备: {$equipment['name']} (ID: {$templateId}, grants_job_id: {$equipment['grants_job_id']})");

    return $templateId;
}

function validateEquipmentData($equipment) {
    $errors = [];
    
    // 验证装备名称
    if (empty($equipment['name'])) {
        $errors[] = '装备名称不能为空';
    }
    
    // 验证装备部位
    $validSlots = ['Head', 'Neck', 'LeftHand', 'RightHand', 'TwoHanded', 'Body', 'Finger', 'Back'];
    if (!in_array($equipment['slot'], $validSlots)) {
        $errors[] = '无效的装备部位: ' . $equipment['slot'];
    }
    
    // 验证品质等级
    if (!isset($equipment['quality']) || $equipment['quality'] < 0 || $equipment['quality'] > 6) {
        $errors[] = '品质等级必须在0-6之间';
    }
    
    // 验证职业ID
    if (isset($equipment['grants_job_id']) && $equipment['grants_job_id'] !== null) {
        $validJobIds = [1, 2, 3, 4];
        if (!in_array((int)$equipment['grants_job_id'], $validJobIds)) {
            $errors[] = '无效的授予职业ID: ' . $equipment['grants_job_id'];
        }
    }
    
    if (isset($equipment['job_restriction']) && $equipment['job_restriction'] !== null) {
        $validJobIds = [1, 2, 3, 4];
        if (!in_array((int)$equipment['job_restriction'], $validJobIds)) {
            $errors[] = '无效的职业限制ID: ' . $equipment['job_restriction'];
        }
    }
    
    // 验证属性数据
    if (isset($equipment['stats']) && !is_array($equipment['stats'])) {
        $errors[] = '属性数据必须是数组格式';
    }
    
    if (!empty($errors)) {
        throw new Exception('数据验证失败: ' . implode(', ', $errors));
    }
    
    return true;
}

function sanitizeEquipmentData($equipment) {
    // 清理和标准化数据
    $sanitized = [];
    
    $sanitized['name'] = trim($equipment['name']);
    $sanitized['description'] = trim($equipment['description'] ?? '');
    $sanitized['slot'] = $equipment['slot'];
    $sanitized['quality'] = (int)$equipment['quality'];
    $sanitized['sockets'] = (int)($equipment['sockets'] ?? 0);
    $sanitized['buy_price'] = (int)($equipment['buy_price'] ?? 100);
    $sanitized['sell_price'] = (int)($equipment['sell_price'] ?? 30);
    
    // 处理职业字段
    $sanitized['job_restriction'] = null;
    if (isset($equipment['job_restriction']) && $equipment['job_restriction'] !== null) {
        $sanitized['job_restriction'] = (int)$equipment['job_restriction'];
    }
    
    $sanitized['grants_job_id'] = null;
    if (isset($equipment['grants_job_id']) && $equipment['grants_job_id'] !== null) {
        $sanitized['grants_job_id'] = (int)$equipment['grants_job_id'];
    }
    
    // 处理装备类型
    $sanitized['equipment_type'] = $equipment['equipment_type'] ?? 'player';

    // 处理属性数据
    $sanitized['stats'] = [];
    if (isset($equipment['stats']) && is_array($equipment['stats'])) {
        foreach ($equipment['stats'] as $key => $value) {
            if (is_numeric($value) && $value > 0) {
                $sanitized['stats'][$key] = (int)$value;
            }
        }
    }

    return $sanitized;
}

// 获取有效的属性键列表
function getValidAttributeKeys() {
    return [
        'level', 'hp', 'mp', 'max_hp', 'max_mp', 'experience', 'experience_to_next_level',
        'strength', 'agility', 'constitution', 'intelligence',
        'attack', 'defense', 'attack_speed', 'karma',
        'potential_points', 'knowledge_points',
        'fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance',
        'fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'
    ];
}
?>
