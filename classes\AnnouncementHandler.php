<?php
// classes/AnnouncementHandler.php

require_once __DIR__ . '/../config/Database.php';
require_once __DIR__ . '/MessageProtocol.php';

class AnnouncementHandler {
    private $server;
    
    public function __construct($server) {
        $this->server = $server;
    }
    
    /**
     * 获取最新公告
     */
    public function handleGetLatestAnnouncement($fd, $playerId, $payload) {
        try {
            $db = Database::getInstance();
            
            // 获取最新的启用公告
            $stmt = $db->query(
                "SELECT id, title, summary, is_important, view_count, DATE(created_at) as created_date
                 FROM announcements
                 WHERE is_active = 1
                 ORDER BY is_important DESC, created_at DESC
                 LIMIT 1"
            );
            $announcement = $stmt->fetch();
            
            if ($announcement) {
                $this->server->sendMessage($fd, MessageProtocol::S2C_LATEST_ANNOUNCEMENT, [
                    'announcement' => $announcement
                ]);
            } else {
                $this->server->sendMessage($fd, MessageProtocol::S2C_LATEST_ANNOUNCEMENT, [
                    'announcement' => null
                ]);
            }
            
        } catch (Exception $e) {
            error_log("获取最新公告错误：" . $e->getMessage());
            $this->server->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '获取公告失败',
                'context' => 'get_latest_announcement'
            ]);
        }
    }
    
    /**
     * 获取公告详情
     */
    public function handleGetAnnouncementDetail($fd, $playerId, $payload) {
        try {
            $announcementId = intval($payload['announcement_id'] ?? 0);
            
            if ($announcementId <= 0) {
                $this->server->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '无效的公告ID',
                    'context' => 'get_announcement_detail'
                ]);
                return;
            }
            
            $db = Database::getInstance();
            
            // 获取公告详情
            $stmt = $db->query(
                "SELECT id, title, content, summary, is_important, view_count, DATE(created_at) as created_date, updated_at
                 FROM announcements
                 WHERE id = ? AND is_active = 1",
                [$announcementId]
            );
            $announcement = $stmt->fetch();
            
            if (!$announcement) {
                $this->server->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '公告不存在或已下线',
                    'context' => 'get_announcement_detail'
                ]);
                return;
            }
            
            // 增加查看次数
            $db->query(
                "UPDATE announcements SET view_count = view_count + 1 WHERE id = ?",
                [$announcementId]
            );
            $announcement['view_count']++;
            
            // 过滤和安全处理HTML内容
            $announcement['content'] = $this->sanitizeHtmlContent($announcement['content']);
            
            $this->server->sendMessage($fd, MessageProtocol::S2C_ANNOUNCEMENT_DETAIL, [
                'announcement' => $announcement
            ]);
            
        } catch (Exception $e) {
            error_log("获取公告详情错误：" . $e->getMessage());
            $this->server->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '获取公告详情失败',
                'context' => 'get_announcement_detail'
            ]);
        }
    }
    
    /**
     * 获取公告列表
     */
    public function handleGetAnnouncementList($fd, $playerId, $payload) {
        try {
            $page = max(1, intval($payload['page'] ?? 1));
            $limit = min(20, max(5, intval($payload['limit'] ?? 10)));
            $offset = ($page - 1) * $limit;
            
            $db = Database::getInstance();
            
            // 获取公告列表
            $stmt = $db->query(
                "SELECT id, title, summary, is_important, view_count, DATE(created_at) as created_date
                 FROM announcements
                 WHERE is_active = 1
                 ORDER BY is_important DESC, created_at DESC
                 LIMIT ? OFFSET ?",
                [$limit, $offset]
            );
            $announcements = $stmt->fetchAll();
            
            // 获取总数
            $countStmt = $db->query("SELECT COUNT(*) FROM announcements WHERE is_active = 1");
            $total = $countStmt->fetchColumn();
            
            $this->server->sendMessage($fd, MessageProtocol::S2C_ANNOUNCEMENT_LIST, [
                'announcements' => $announcements,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $total,
                    'pages' => ceil($total / $limit)
                ]
            ]);
            
        } catch (Exception $e) {
            error_log("获取公告列表错误：" . $e->getMessage());
            $this->server->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '获取公告列表失败',
                'context' => 'get_announcement_list'
            ]);
        }
    }
    
    /**
     * 安全处理HTML内容，防止XSS攻击
     */
    private function sanitizeHtmlContent($content) {
        // 允许的HTML标签和属性
        $allowedTags = [
            'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
            'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'div', 'span'
        ];
        
        $allowedAttributes = [
            'a' => ['href', 'title', 'target'],
            'img' => ['src', 'alt', 'title', 'width', 'height', 'style'],
            'div' => ['style', 'class'],
            'span' => ['style', 'class']
        ];
        
        // 使用简单的HTML过滤（在生产环境中建议使用HTMLPurifier等专业库）
        $content = $this->basicHtmlFilter($content, $allowedTags, $allowedAttributes);
        
        return $content;
    }
    
    /**
     * 基础HTML过滤器
     */
    private function basicHtmlFilter($content, $allowedTags, $allowedAttributes) {
        // 移除script和style标签及其内容
        $content = preg_replace('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', '', $content);
        $content = preg_replace('/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/mi', '', $content);
        
        // 移除javascript:和data:协议的链接
        $content = preg_replace('/href\s*=\s*["\']?\s*javascript:/i', 'href="#"', $content);
        $content = preg_replace('/src\s*=\s*["\']?\s*data:/i', 'src="#"', $content);
        
        // 移除on*事件属性
        $content = preg_replace('/\s*on\w+\s*=\s*["\'][^"\']*["\']/i', '', $content);
        
        // 这里可以添加更多的过滤规则
        // 在生产环境中建议使用专业的HTML净化库
        
        return $content;
    }
    
    /**
     * HTML转义函数
     */
    private function escapeHtml($text) {
        return htmlspecialchars($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
}
