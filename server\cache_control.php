<?php
// server/cache_control.php
// 这个文件用于控制服务器端的缓存清理

require_once '../config/Database.php';
require_once '../config/RedisManager.php';
require_once '../classes/NPCManager.php';
require_once '../classes/SceneManager.php';

/**
 * 缓存控制类
 * 用于检查和清理服务器端的缓存
 */
class CacheControl {
    private $db;
    private $redis;
    private $npcManager;
    private $sceneManager;
    private $lastCheckTime = 0;
    private $checkInterval = 10; // 每10秒检查一次
    
    /**
     * 构造函数
     * @param Database $db 数据库实例
     * @param RedisManager $redisManager Redis管理器实例
     */
    public function __construct($db, $redisManager) {
        $this->db = $db;
        $this->redis = $redisManager;
        $this->npcManager = new NPCManager($db);
        $this->sceneManager = new SceneManager($db);
        
        // 设置初始检查时间
        $this->lastCheckTime = time();
        
        // 创建缓存标志文件
        $this->initCacheFlags();
    }
    
    /**
     * 初始化缓存标志
     */
    private function initCacheFlags() {
        $this->redis->with(function($redis) {
            // 初始化NPC缓存标志
            if (!$redis->exists('cache_flags:npc')) {
                $redis->set('cache_flags:npc', '0');
            }
            
            // 初始化对话缓存标志
            if (!$redis->exists('cache_flags:dialogue')) {
                $redis->set('cache_flags:dialogue', '0');
            }
            
            // 初始化场景缓存标志
            if (!$redis->exists('cache_flags:scene')) {
                $redis->set('cache_flags:scene', '0');
            }
        });
    }
    
    /**
     * 检查是否需要清除缓存
     * 此方法应该定期调用，例如在游戏主循环中
     */
    public function checkCacheFlags() {
        $currentTime = time();
        
        // 控制检查频率
        if ($currentTime - $this->lastCheckTime < $this->checkInterval) {
            return;
        }
        
        $this->lastCheckTime = $currentTime;
        
        $this->redis->with(function($redis) {
            // 检查NPC缓存标志
            $npcFlag = $redis->get('cache_flags:npc');
            if ($npcFlag === '1') {
                echo "检测到NPC缓存清理标志，正在清除NPC缓存...\n";
                $this->npcManager->clearCache();
                $redis->set('cache_flags:npc', '0');
            }
            
            // 检查对话缓存标志
            $dialogueFlag = $redis->get('cache_flags:dialogue');
            if ($dialogueFlag === '1') {
                echo "检测到对话缓存清理标志，正在清除对话缓存...\n";
                $this->npcManager->clearCache(); // 对话缓存也在NPCManager中
                $redis->set('cache_flags:dialogue', '0');
            }
            
            // 检查场景缓存标志
            $sceneFlag = $redis->get('cache_flags:scene');
            if ($sceneFlag === '1') {
                echo "检测到场景缓存清理标志，正在清除场景缓存...\n";
                $this->sceneManager->clearCache();
                $redis->set('cache_flags:scene', '0');
                echo "场景缓存已清除\n";
            }
        });
    }
    
    /**
     * 设置缓存清理标志
     * @param string $cacheType 缓存类型 (npc, dialogue, scene)
     */
    public static function setCacheFlag($cacheType) {
        $redisManager = RedisManager::getInstance();
        $redisManager->with(function($redis) use ($cacheType) {
            $redis->set("cache_flags:{$cacheType}", '1');
        });
    }
} 