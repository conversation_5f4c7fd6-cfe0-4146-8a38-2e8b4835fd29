<?php
$currentPage = 'balance_manager';
$pageTitle = '数值平衡管理';

require_once '../config/Database.php';
require_once '../config/rarity_config.php';

// 获取稀有度配置
$rarityConfig = include '../config/rarity_config.php';
$rarityLevels = $rarityConfig['rarity_levels'];

// 获取数据库连接
$db = Database::getInstance()->getConnection();

// 获取装备品质统计（通过装备名称中的品级标识来识别）
$stmt = $db->query("
    SELECT
        it.name,
        it.buy_price
    FROM item_templates it
    LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
    WHERE it.category = 'Equipment'
    ORDER BY it.name
");
$equipmentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 定义品质识别函数
function getQualityFromName($name) {
    // 根据装备名称中的品级标识来判断品质
    if (strpos($name, '[玄]') !== false) return 6; // 玄级
    if (strpos($name, '[极]') !== false) return 5; // 极级
    if (strpos($name, '[珍]') !== false) return 4; // 珍级
    if (strpos($name, '[优]') !== false) return 3; // 优级
    if (strpos($name, '[良]') !== false) return 2; // 良级
    if (strpos($name, '[凡]') !== false) return 1; // 凡级
    return 0; // 普级（无标识）
}

// 在PHP中统计品质
$qualityStats = [];
foreach ($equipmentData as $row) {
    $quality = getQualityFromName($row['name']);

    if (!isset($qualityStats[$quality])) {
        $qualityStats[$quality] = [
            'quality' => $quality,
            'count' => 0,
            'total_price' => 0,
            'price_count' => 0
        ];
    }

    $qualityStats[$quality]['count']++;
    if ($row['buy_price'] > 0) {
        $qualityStats[$quality]['total_price'] += $row['buy_price'];
        $qualityStats[$quality]['price_count']++;
    }
}

// 计算平均价格
foreach ($qualityStats as &$stat) {
    $stat['avg_price'] = $stat['price_count'] > 0 ? $stat['total_price'] / $stat['price_count'] : null;
}
unset($stat);

// 按品质排序
ksort($qualityStats);
$qualityStats = array_values($qualityStats);
// 获取装备部位统计（兼容MySQL 5.5，使用PHP解析JSON）
$stmt = $db->query("
    SELECT
        ed.slot,
        ed.stats
    FROM equipment_details ed
    JOIN item_templates it ON ed.item_template_id = it.id
    WHERE it.category = 'Equipment' AND ed.stats IS NOT NULL
    ORDER BY ed.slot
");
$slotData = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 在PHP中解析JSON并统计部位属性
$slotStats = [];
foreach ($slotData as $row) {
    $slot = $row['slot'];
    $stats = json_decode($row['stats'], true);

    if (!isset($slotStats[$slot])) {
        $slotStats[$slot] = [
            'slot' => $slot,
            'count' => 0,
            'total_attack' => 0,
            'total_defense' => 0
        ];
    }

    $slotStats[$slot]['count']++;
    $slotStats[$slot]['total_attack'] += isset($stats['attack']) ? (float)$stats['attack'] : 0;
    $slotStats[$slot]['total_defense'] += isset($stats['defense']) ? (float)$stats['defense'] : 0;
}

// 计算平均值
foreach ($slotStats as &$stat) {
    $stat['avg_attack'] = $stat['count'] > 0 ? $stat['total_attack'] / $stat['count'] : 0;
    $stat['avg_defense'] = $stat['count'] > 0 ? $stat['total_defense'] / $stat['count'] : 0;
}
unset($stat);

// 按部位排序
ksort($slotStats);
$slotStats = array_values($slotStats);

// 获取掉落表统计
$stmt = $db->query("
    SELECT
        lt.name as table_name,
        COUNT(lte.id) as item_count,
        AVG(lte.drop_chance) as avg_drop_rate,
        SUM(lte.drop_chance) as total_drop_rate
    FROM loot_tables lt
    LEFT JOIN loot_table_entries lte ON lt.id = lte.loot_table_id
    GROUP BY lt.id, lt.name
    ORDER BY total_drop_rate DESC
");
$lootStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取怪物等级分布
$stmt = $db->query("
    SELECT
        CASE
            WHEN level BETWEEN 1 AND 10 THEN '1-10级'
            WHEN level BETWEEN 11 AND 20 THEN '11-20级'
            WHEN level BETWEEN 21 AND 30 THEN '21-30级'
            WHEN level BETWEEN 31 AND 40 THEN '31-40级'
            ELSE '40级以上'
        END as level_range,
        COUNT(*) as monster_count,
        AVG(base_hp) as avg_hp,
        AVG(base_attack) as avg_attack,
        AVG(experience_reward) as avg_exp
    FROM monster_templates
    GROUP BY level_range
    ORDER BY MIN(level)
");
$monsterStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取玩家等级分布
$stmt = $db->query("
    SELECT
        CASE
            WHEN pa.level BETWEEN 1 AND 10 THEN '1-10级'
            WHEN pa.level BETWEEN 11 AND 20 THEN '11-20级'
            WHEN pa.level BETWEEN 21 AND 30 THEN '21-30级'
            WHEN pa.level BETWEEN 31 AND 40 THEN '31-40级'
            WHEN pa.level BETWEEN 41 AND 50 THEN '41-50级'
            ELSE '50级以上'
        END as level_range,
        COUNT(*) as player_count,
        AVG(pa.level) as avg_level,
        AVG(pa.gold) as avg_gold,
        AVG(pa.experience) as avg_exp
    FROM player_attributes pa
    JOIN accounts a ON pa.account_id = a.id
    GROUP BY level_range
    ORDER BY MIN(pa.level)
");
$playerLevelStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取玩家属性分布
$stmt = $db->query("
    SELECT
        AVG(pa.strength) as avg_strength,
        AVG(pa.agility) as avg_agility,
        AVG(pa.constitution) as avg_constitution,
        AVG(pa.intelligence) as avg_intelligence,
        AVG(pa.attack) as avg_attack,
        AVG(pa.defense) as avg_defense,
        AVG(pa.max_hp) as avg_max_hp,
        AVG(pa.max_mp) as avg_max_mp,
        COUNT(*) as total_players
    FROM player_attributes pa
    JOIN accounts a ON pa.account_id = a.id
");
$playerAttributeStats = $stmt->fetch(PDO::FETCH_ASSOC);

// 获取玩家经济分布
$stmt = $db->query("
    SELECT
        CASE
            WHEN pa.gold < 1000 THEN '0-1K'
            WHEN pa.gold < 10000 THEN '1K-10K'
            WHEN pa.gold < 100000 THEN '10K-100K'
            WHEN pa.gold < 1000000 THEN '100K-1M'
            ELSE '1M+'
        END as gold_range,
        COUNT(*) as player_count,
        AVG(pa.gold) as avg_gold
    FROM player_attributes pa
    JOIN accounts a ON pa.account_id = a.id
    GROUP BY gold_range
    ORDER BY MIN(pa.gold)
");
$playerEconomyStats = $stmt->fetchAll(PDO::FETCH_ASSOC);

include 'layout_header.php';
?>

<style>
.balance-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.balance-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.balance-card h3 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
}

.stat-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.stat-table th,
.stat-table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.stat-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.stat-table tr:hover {
    background-color: #f8f9fa;
}

.quality-indicator {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    margin-right: 8px;
}

.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.success-box {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
    padding: 15px;
    border-radius: 6px;
    margin-bottom: 20px;
}

.calculator-section {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
}

.calc-input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    margin: 0 5px;
    width: 100px;
}

.calc-result {
    background: #e8f4fd;
    border: 1px solid #b3d9f2;
    border-radius: 4px;
    padding: 15px;
    margin-top: 15px;
    display: none;
}

.btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    margin: 5px;
}

.btn:hover {
    background: #0056b3;
}

.metric-value {
    font-size: 1.2em;
    font-weight: bold;
    color: #007bff;
}
</style>

<div class="content-wrapper">
    <!-- 平衡性检查 -->
    <?php
    $warnings = [];

    // 分析单个掉落表的合理性
    $highDropRateTables = [];
    $lowDropRateTables = [];
    $totalDropRates = array_column($lootStats, 'total_drop_rate');
    $avgDropRate = !empty($totalDropRates) ? array_sum($totalDropRates) / count($totalDropRates) : 0;

    foreach ($lootStats as $stat) {
        $rate = $stat['total_drop_rate'];
        if ($rate > 30) {
            $highDropRateTables[] = $stat['table_name'] . "({$rate}%)";
        }
        if ($rate < 5) {
            $lowDropRateTables[] = $stat['table_name'] . "({$rate}%)";
        }
    }

    if (!empty($highDropRateTables)) {
        $warnings[] = "以下掉落表概率过高: " . implode(', ', $highDropRateTables);
    }

    if (!empty($lowDropRateTables)) {
        $warnings[] = "以下掉落表概率过低: " . implode(', ', $lowDropRateTables);
    }

    if ($avgDropRate > 20) {
        $warnings[] = "平均掉落率过高: " . round($avgDropRate, 2) . "%，建议控制在15-20%";
    }

    // 检查品质分布
    $totalEquipment = array_sum(array_column($qualityStats, 'count'));
    if ($totalEquipment > 0) {
        foreach ($qualityStats as $stat) {
            $percentage = ($stat['count'] / $totalEquipment) * 100;
            if ($stat['quality'] <= 1 && $percentage < 30) {
                $warnings[] = "低品质装备比例过低: " . round($percentage, 1) . "%";
            }
            if ($stat['quality'] >= 4 && $percentage > 10) {
                $warnings[] = "高品质装备比例过高: " . round($percentage, 1) . "%";
            }
        }
    }

    // 检查玩家等级分布
    $totalPlayers = array_sum(array_column($playerLevelStats, 'player_count'));
    if ($totalPlayers > 0) {
        $lowLevelPlayers = 0;
        $highLevelPlayers = 0;

        foreach ($playerLevelStats as $stat) {
            if (in_array($stat['level_range'], ['1-10级', '11-20级'])) {
                $lowLevelPlayers += $stat['player_count'];
            }
            if (in_array($stat['level_range'], ['41-50级', '50级以上'])) {
                $highLevelPlayers += $stat['player_count'];
            }
        }

        $lowLevelPercentage = ($lowLevelPlayers / $totalPlayers) * 100;
        if ($lowLevelPercentage > 70) {
            $warnings[] = "低等级玩家比例过高: " . round($lowLevelPercentage, 1) . "%，可能需要优化升级体验";
        }
    }

    // 检查玩家经济分布
    $totalPlayersEconomy = array_sum(array_column($playerEconomyStats, 'player_count'));
    if ($totalPlayersEconomy > 0) {
        $poorPlayers = 0;
        $richPlayers = 0;

        foreach ($playerEconomyStats as $stat) {
            if ($stat['gold_range'] === '0-1K') {
                $poorPlayers = $stat['player_count'];
            }
            if (in_array($stat['gold_range'], ['100K-1M', '1M+'])) {
                $richPlayers += $stat['player_count'];
            }
        }

        $poorPercentage = ($poorPlayers / $totalPlayersEconomy) * 100;
        if ($poorPercentage > 60) {
            $warnings[] = "贫困玩家比例过高: " . round($poorPercentage, 1) . "%，建议增加金币获取途径";
        }
    }

    if (!empty($warnings)): ?>
        <div class="warning-box">
            <strong>⚠️ 发现平衡性问题：</strong>
            <ul style="margin: 10px 0 0 20px;">
                <?php foreach ($warnings as $warning): ?>
                    <li><?= htmlspecialchars($warning) ?></li>
                <?php endforeach; ?>
            </ul>
        </div>
    <?php else: ?>
        <div class="success-box">
            <strong>✅ 当前数值配置通过基础平衡性检查</strong>
        </div>
    <?php endif; ?>

    <!-- 数值统计面板 -->
    <div class="balance-grid">
        <!-- 装备品质分布 -->
        <div class="balance-card">
            <h3>⚔️ 装备品质分布</h3>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>品质</th>
                        <th>数量</th>
                        <th>比例</th>
                        <th>平均价格</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $totalEquipment = array_sum(array_column($qualityStats, 'count'));
                    foreach ($qualityStats as $stat):
                        $qualityName = '';
                        $qualityColor = '#666';

                        // 根据品质等级获取名称和颜色
                        $qualityIndex = (int)$stat['quality'];
                        $rarityKeys = array_keys($rarityLevels);
                        if (isset($rarityKeys[$qualityIndex])) {
                            $rarityKey = $rarityKeys[$qualityIndex];
                            $qualityName = $rarityKey . '级';
                            $qualityColor = $rarityLevels[$rarityKey]['color'];
                        } else {
                            $qualityName = '品质' . $qualityIndex;
                        }

                        $percentage = ($stat['count'] / $totalEquipment) * 100;
                    ?>
                        <tr>
                            <td>
                                <span class="quality-indicator" style="background-color: <?= $qualityColor ?>"></span>
                                <?= $qualityName ?>
                            </td>
                            <td><?= $stat['count'] ?></td>
                            <td><?= round($percentage, 1) ?>%</td>
                            <td><?= $stat['avg_price'] ? number_format($stat['avg_price']) . '金' : '-' ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 装备部位统计 -->
        <div class="balance-card">
            <h3>🛡️ 装备部位统计</h3>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>部位</th>
                        <th>数量</th>
                        <th>平均攻击</th>
                        <th>平均防御</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($slotStats as $stat): ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['slot']) ?></td>
                            <td><?= $stat['count'] ?></td>
                            <td><?= round($stat['avg_attack'] ?? 0, 1) ?></td>
                            <td><?= round($stat['avg_defense'] ?? 0, 1) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 掉落表分析 -->
        <div class="balance-card">
            <h3>📦 掉落表分析</h3>

            <!-- 汇总统计 -->
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                <strong>汇总统计：</strong><br>
                掉落表数量: <?= count($lootStats) ?> 个<br>
                平均掉落率: <?= round($avgDropRate, 2) ?>%<br>
                最高掉落率: <?= !empty($totalDropRates) ? round(max($totalDropRates), 2) : 0 ?>%<br>
                最低掉落率: <?= !empty($totalDropRates) ? round(min($totalDropRates), 2) : 0 ?>%
            </div>

            <table class="stat-table">
                <thead>
                    <tr>
                        <th>掉落表</th>
                        <th>物品数</th>
                        <th>平均概率</th>
                        <th>总概率</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($lootStats as $stat): ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['table_name']) ?></td>
                            <td><?= $stat['item_count'] ?></td>
                            <td><?= round($stat['avg_drop_rate'] ?? 0, 2) ?>%</td>
                            <td style="color: <?= $stat['total_drop_rate'] > 20 ? '#dc3545' : '#28a745' ?>">
                                <?= round($stat['total_drop_rate'] ?? 0, 2) ?>%
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 怪物等级分布 -->
        <div class="balance-card">
            <h3>👹 怪物等级分布</h3>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>等级范围</th>
                        <th>数量</th>
                        <th>平均血量</th>
                        <th>平均攻击</th>
                        <th>平均经验</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($monsterStats as $stat): ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['level_range']) ?></td>
                            <td><?= $stat['monster_count'] ?></td>
                            <td><?= round($stat['avg_hp'] ?? 0) ?></td>
                            <td><?= round($stat['avg_attack'] ?? 0) ?></td>
                            <td><?= round($stat['avg_exp'] ?? 0) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 玩家等级分布 -->
        <div class="balance-card">
            <h3>👤 玩家等级分布</h3>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>等级范围</th>
                        <th>玩家数</th>
                        <th>平均等级</th>
                        <th>平均金币</th>
                        <th>平均经验</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($playerLevelStats as $stat): ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['level_range']) ?></td>
                            <td><?= $stat['player_count'] ?></td>
                            <td><?= round($stat['avg_level'] ?? 0, 1) ?></td>
                            <td><?= number_format($stat['avg_gold'] ?? 0) ?></td>
                            <td><?= number_format($stat['avg_exp'] ?? 0) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>

        <!-- 玩家属性统计 -->
        <div class="balance-card">
            <h3>💪 玩家属性统计</h3>
            <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; margin-bottom: 15px;">
                <strong>总玩家数：</strong><?= number_format($playerAttributeStats['total_players']) ?> 人
            </div>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>属性</th>
                        <th>平均值</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>力量</td>
                        <td><?= round($playerAttributeStats['avg_strength'] ?? 0, 1) ?></td>
                    </tr>
                    <tr>
                        <td>敏捷</td>
                        <td><?= round($playerAttributeStats['avg_agility'] ?? 0, 1) ?></td>
                    </tr>
                    <tr>
                        <td>体质</td>
                        <td><?= round($playerAttributeStats['avg_constitution'] ?? 0, 1) ?></td>
                    </tr>
                    <tr>
                        <td>智力</td>
                        <td><?= round($playerAttributeStats['avg_intelligence'] ?? 0, 1) ?></td>
                    </tr>
                    <tr style="border-top: 2px solid #dee2e6;">
                        <td>攻击力</td>
                        <td><?= round($playerAttributeStats['avg_attack'] ?? 0, 1) ?></td>
                    </tr>
                    <tr>
                        <td>防御力</td>
                        <td><?= round($playerAttributeStats['avg_defense'] ?? 0, 1) ?></td>
                    </tr>
                    <tr>
                        <td>最大生命</td>
                        <td><?= round($playerAttributeStats['avg_max_hp'] ?? 0) ?></td>
                    </tr>
                    <tr>
                        <td>最大法力</td>
                        <td><?= round($playerAttributeStats['avg_max_mp'] ?? 0) ?></td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 玩家经济分布 -->
        <div class="balance-card">
            <h3>💰 玩家经济分布</h3>
            <table class="stat-table">
                <thead>
                    <tr>
                        <th>金币范围</th>
                        <th>玩家数</th>
                        <th>比例</th>
                        <th>平均金币</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $totalPlayersEconomy = array_sum(array_column($playerEconomyStats, 'player_count'));
                    foreach ($playerEconomyStats as $stat):
                        $percentage = $totalPlayersEconomy > 0 ? ($stat['player_count'] / $totalPlayersEconomy) * 100 : 0;
                    ?>
                        <tr>
                            <td><?= htmlspecialchars($stat['gold_range']) ?></td>
                            <td><?= $stat['player_count'] ?></td>
                            <td><?= round($percentage, 1) ?>%</td>
                            <td><?= number_format($stat['avg_gold'] ?? 0) ?></td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
    <!-- 数值计算器 -->
    <div class="calculator-section">
        <h2>🧮 数值计算器</h2>

        <div style="margin-bottom: 20px;">
            <h3>装备属性预估</h3>
            <p>基于当前系统的装备属性计算公式进行预估</p>

            <label>装备部位:</label>
            <select id="calc-slot" class="calc-input">
                <option value="TwoHanded">双手武器</option>
                <option value="RightHand">右手武器</option>
                <option value="LeftHand">左手武器</option>
                <option value="Head">头部</option>
                <option value="Neck">颈部</option>
                <option value="Body">身体</option>
                <option value="Finger">手指</option>
                <option value="Back">背部</option>
            </select>

            <label>品质等级:</label>
            <select id="calc-quality" class="calc-input">
                <?php foreach ($rarityLevels as $key => $config): ?>
                    <option value="<?= array_search($key, array_keys($rarityLevels)) ?>"><?= $key ?>级</option>
                <?php endforeach; ?>
            </select>

            <label>装备等级:</label>
            <input type="number" id="calc-level" class="calc-input" value="1" min="1" max="60">

            <button onclick="calculateEquipment()" class="btn">计算属性</button>

            <div id="equipment-result" class="calc-result"></div>
        </div>

        <div style="margin-bottom: 20px;">
            <h3>掉落概率分析</h3>
            <p>分析掉落表的总体概率分布</p>

            <label>掉落表:</label>
            <select id="loot-table" class="calc-input" style="width: 200px;">
                <?php foreach ($lootStats as $stat): ?>
                    <option value="<?= htmlspecialchars($stat['table_name']) ?>">
                        <?= htmlspecialchars($stat['table_name']) ?>
                    </option>
                <?php endforeach; ?>
            </select>

            <button onclick="analyzeLootTable()" class="btn">分析概率</button>

            <div id="loot-result" class="calc-result"></div>
        </div>

        <div style="margin-bottom: 20px;">
            <h3>玩家等级经验计算</h3>
            <p>基于系统公式：每级所需经验 = 100 * (1.1)^(等级-1)</p>

            <label>目标等级:</label>
            <input type="number" id="target-level" class="calc-input" value="10" min="1" max="100">

            <button onclick="calculatePlayerExperience()" class="btn">计算经验</button>

            <div id="exp-result" class="calc-result"></div>
        </div>

        <div style="margin-bottom: 20px;">
            <h3>等级区间经验分析</h3>
            <p>分析指定等级区间的经验需求</p>

            <label>起始等级:</label>
            <input type="number" id="start-level" class="calc-input" value="1" min="1" max="99">

            <label>结束等级:</label>
            <input type="number" id="end-level" class="calc-input" value="20" min="2" max="100">

            <button onclick="calculateLevelRange()" class="btn">分析区间</button>

            <div id="range-result" class="calc-result"></div>
        </div>
    </div>
</div>

<?php include 'layout_footer.php'; ?>

<script>
function calculateEquipment() {
    const slot = document.getElementById('calc-slot').value;
    const quality = document.getElementById('calc-quality').value;
    const level = document.getElementById('calc-level').value;

    // 简单的属性计算逻辑（基于你的系统）
    const slotMultipliers = {
        'TwoHanded': { attack: 1.5, defense: 0.0 },
        'RightHand': { attack: 1.0, defense: 0.0 },
        'LeftHand': { attack: 0.3, defense: 0.8 },
        'Head': { attack: 0.0, defense: 0.8 },
        'Neck': { attack: 0.0, defense: 0.2 },
        'Body': { attack: 0.0, defense: 1.2 },
        'Finger': { attack: 0.0, defense: 0.1 },
        'Back': { attack: 0.0, defense: 0.4 }
    };

    const qualityMultipliers = [1.0, 1.2, 1.4, 1.6, 1.8, 2.0, 2.2];

    const baseValue = 10 + (parseInt(level) * 2);
    const slotMult = slotMultipliers[slot] || { attack: 0, defense: 0 };
    const qualityMult = qualityMultipliers[parseInt(quality)] || 1.0;

    const attack = Math.floor(baseValue * slotMult.attack * qualityMult);
    const defense = Math.floor(baseValue * slotMult.defense * qualityMult);

    const result = document.getElementById('equipment-result');
    result.innerHTML = `
        <strong>预估属性:</strong><br>
        攻击力: ${attack}<br>
        防御力: ${defense}<br>
        <small>*基于当前系统公式的预估值</small>
    `;
    result.style.display = 'block';
}

function analyzeLootTable() {
    const tableName = document.getElementById('loot-table').value;

    // 找到对应的掉落表数据
    const lootData = <?= json_encode($lootStats) ?>;
    const tableData = lootData.find(item => item.table_name === tableName);

    if (tableData) {
        const result = document.getElementById('loot-result');
        const totalRate = parseFloat(tableData.total_drop_rate);
        const avgRate = parseFloat(tableData.avg_drop_rate);
        const itemCount = parseInt(tableData.item_count);

        let analysis = '';
        if (totalRate > 30) {
            analysis = '<span style="color: #dc3545;">⚠️ 总概率过高，可能导致掉落过于频繁</span>';
        } else if (totalRate < 5) {
            analysis = '<span style="color: #ffc107;">⚠️ 总概率较低，玩家可能很难获得奖励</span>';
        } else {
            analysis = '<span style="color: #28a745;">✅ 概率设置合理</span>';
        }

        result.innerHTML = `
            <strong>掉落表分析:</strong><br>
            物品数量: ${itemCount}<br>
            平均概率: ${avgRate.toFixed(2)}%<br>
            总概率: ${totalRate.toFixed(2)}%<br>
            预期击杀次数: ${totalRate > 0 ? Math.ceil(100 / totalRate) : '∞'}<br>
            ${analysis}
        `;
        result.style.display = 'block';
    }
}

// 基于系统公式计算单级所需经验：100 * (1.1)^(level-1)
function calculateExperienceForLevel(level) {
    return Math.floor(100 * Math.pow(1.1, level - 1));
}

// 计算累积经验（从1级到指定等级的总经验）
function calculateTotalExperience(targetLevel) {
    let totalExp = 0;
    for (let level = 1; level < targetLevel; level++) {
        totalExp += calculateExperienceForLevel(level);
    }
    return totalExp;
}

function calculatePlayerExperience() {
    const targetLevel = parseInt(document.getElementById('target-level').value);

    if (targetLevel < 1 || targetLevel > 100) {
        alert('请输入1-100之间的等级');
        return;
    }

    const result = document.getElementById('exp-result');

    if (targetLevel === 1) {
        result.innerHTML = `
            <strong>等级经验分析:</strong><br>
            1级为初始等级，无需经验
        `;
    } else {
        const levelExp = calculateExperienceForLevel(targetLevel - 1); // 从(targetLevel-1)级升到targetLevel级所需经验
        const totalExp = calculateTotalExperience(targetLevel); // 从1级到targetLevel级的累积经验

        result.innerHTML = `
            <strong>等级经验分析:</strong><br>
            升到 ${targetLevel} 级需要累积经验: <span style="color: #007bff; font-weight: bold;">${totalExp.toLocaleString()}</span><br>
            从 ${targetLevel-1} 级升到 ${targetLevel} 级需要: <span style="color: #28a745; font-weight: bold;">${levelExp.toLocaleString()}</span><br>
            <small>*基于公式：每级所需经验 = 100 × (1.1)^(等级-1)</small>
        `;
    }

    result.style.display = 'block';
}

function calculateLevelRange() {
    const startLevel = parseInt(document.getElementById('start-level').value);
    const endLevel = parseInt(document.getElementById('end-level').value);

    if (startLevel < 1 || endLevel > 100 || startLevel >= endLevel) {
        alert('请输入有效的等级区间（起始等级 < 结束等级，范围1-100）');
        return;
    }

    const result = document.getElementById('range-result');

    const startTotalExp = calculateTotalExperience(startLevel);
    const endTotalExp = calculateTotalExperience(endLevel);
    const rangeExp = endTotalExp - startTotalExp;

    // 计算平均每级经验
    const levelCount = endLevel - startLevel;
    const avgExpPerLevel = rangeExp / levelCount;

    // 找出区间内经验需求最高和最低的等级
    let maxExp = 0, minExp = Infinity;
    let maxExpLevel = startLevel, minExpLevel = startLevel;

    for (let level = startLevel; level < endLevel; level++) {
        const exp = calculateExperienceForLevel(level);
        if (exp > maxExp) {
            maxExp = exp;
            maxExpLevel = level + 1; // 显示升到的等级
        }
        if (exp < minExp) {
            minExp = exp;
            minExpLevel = level + 1; // 显示升到的等级
        }
    }

    result.innerHTML = `
        <strong>等级区间分析 (${startLevel}-${endLevel}级):</strong><br>
        区间总经验需求: <span style="color: #007bff; font-weight: bold;">${rangeExp.toLocaleString()}</span><br>
        平均每级经验: <span style="color: #6f42c1; font-weight: bold;">${Math.round(avgExpPerLevel).toLocaleString()}</span><br>
        最高单级经验: <span style="color: #dc3545; font-weight: bold;">${maxExp.toLocaleString()}</span> (升到${maxExpLevel}级)<br>
        最低单级经验: <span style="color: #28a745; font-weight: bold;">${minExp.toLocaleString()}</span> (升到${minExpLevel}级)<br>
        经验增长倍数: <span style="color: #fd7e14; font-weight: bold;">${(maxExp / minExp).toFixed(2)}x</span><br>
        <small>*该区间跨越 ${levelCount} 个等级</small>
    `;

    result.style.display = 'block';
}
</script>
