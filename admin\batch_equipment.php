<?php 
$pageTitle = '批量装备创建器';
$currentPage = 'batch_equipment';

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

// 获取职业列表
$stmt_jobs = $db->query("SELECT `id`, `name` FROM `jobs` ORDER BY `id`");
$jobs = $stmt_jobs->fetchAll(PDO::FETCH_ASSOC);
$stmt_jobs->closeCursor();

// 属性名称映射（与items.php保持一致）
$attribute_name_map = [
    'level' => '等级', 'hp' => '生命', 'mp' => '魔力', 'max_hp' => '最大生命', 'max_mp' => '最大魔力',
    'experience' => '经验', 'experience_to_next_level' => '下一级经验',
    'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智慧',
    'attack' => '攻击', 'defense' => '防御', 'attack_speed' => '攻速', 'karma' => '善恶',
    'potential_points' => '潜力点', 'knowledge_points' => '知识点',
    'fire_resistance' => '火抗', 'ice_resistance' => '冰抗', 'wind_resistance' => '风抗', 'electric_resistance' => '电抗',
    'fire_damage' => '火伤', 'ice_damage' => '冰冻', 'wind_damage' => '风裂', 'electric_damage' => '闪电'
];

ob_start();
?>
<link rel="stylesheet" href="batch_equipment.css">
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="batch_equipment.js" defer></script>
<?php
$extra_js = ob_get_clean();

require_once 'layout_header.php'; 
?>

<div class="container">
    <!-- 品质数值配置 -->
    <div class="config-section">
        <h2>品质数值标准</h2>
        <div class="quality-config">
            <table class="config-table">
                <thead>
                    <tr>
                        <th>品质</th>
                        <th>插槽</th>
                        <th>攻击</th>
                        <th>元素伤/抗</th>
                        <th>攻速</th>
                        <th>四维</th>
                        <th>防御</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-quality="0">
                        <td>[普]</td>
                        <td><input type="number" value="0" data-attr="sockets"></td>
                        <td><input type="number" value="10" data-attr="attack"></td>
                        <td><input type="number" value="5" data-attr="element"></td>
                        <td><input type="number" value="0" data-attr="speed"></td>
                        <td><input type="number" value="5" data-attr="stats"></td>
                        <td><input type="number" value="5" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="1">
                        <td>[凡]</td>
                        <td><input type="number" value="1" data-attr="sockets"></td>
                        <td><input type="number" value="20" data-attr="attack"></td>
                        <td><input type="number" value="15" data-attr="element"></td>
                        <td><input type="number" value="5" data-attr="speed"></td>
                        <td><input type="number" value="8" data-attr="stats"></td>
                        <td><input type="number" value="10" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="2">
                        <td>[良]</td>
                        <td><input type="number" value="2" data-attr="sockets"></td>
                        <td><input type="number" value="30" data-attr="attack"></td>
                        <td><input type="number" value="25" data-attr="element"></td>
                        <td><input type="number" value="10" data-attr="speed"></td>
                        <td><input type="number" value="10" data-attr="stats"></td>
                        <td><input type="number" value="15" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="3">
                        <td>[优]</td>
                        <td><input type="number" value="3" data-attr="sockets"></td>
                        <td><input type="number" value="40" data-attr="attack"></td>
                        <td><input type="number" value="30" data-attr="element"></td>
                        <td><input type="number" value="15" data-attr="speed"></td>
                        <td><input type="number" value="12" data-attr="stats"></td>
                        <td><input type="number" value="20" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="4">
                        <td>[珍]</td>
                        <td><input type="number" value="4" data-attr="sockets"></td>
                        <td><input type="number" value="50" data-attr="attack"></td>
                        <td><input type="number" value="40" data-attr="element"></td>
                        <td><input type="number" value="20" data-attr="speed"></td>
                        <td><input type="number" value="15" data-attr="stats"></td>
                        <td><input type="number" value="25" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="5">
                        <td>[极]</td>
                        <td><input type="number" value="5" data-attr="sockets"></td>
                        <td><input type="number" value="60" data-attr="attack"></td>
                        <td><input type="number" value="50" data-attr="element"></td>
                        <td><input type="number" value="25" data-attr="speed"></td>
                        <td><input type="number" value="18" data-attr="stats"></td>
                        <td><input type="number" value="30" data-attr="defense"></td>
                    </tr>
                    <tr data-quality="6">
                        <td>[玄]</td>
                        <td><input type="number" value="6" data-attr="sockets"></td>
                        <td><input type="number" value="70" data-attr="attack"></td>
                        <td><input type="number" value="60" data-attr="element"></td>
                        <td><input type="number" value="30" data-attr="speed"></td>
                        <td><input type="number" value="20" data-attr="stats"></td>
                        <td><input type="number" value="35" data-attr="defense"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 装备部位系数配置 -->
    <div class="config-section">
        <h2>装备部位属性系数</h2>
        <div class="slot-config">
            <table class="config-table">
                <thead>
                    <tr>
                        <th>装备部位</th>
                        <th>攻击系数</th>
                        <th>元素伤系数</th>
                        <th>元素抗系数</th>
                        <th>攻速系数</th>
                        <th>四维系数</th>
                        <th>防御系数</th>
                    </tr>
                </thead>
                <tbody>
                    <tr data-slot="TwoHanded">
                        <td>双手武器</td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.6" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="0.4" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="RightHand">
                        <td>右手武器</td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="0.4" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="LeftHand">
                        <td>左手装备</td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.6" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="0.6" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="Head">
                        <td>头部装备</td>
                        <td><input type="number" step="0.1" value="0.2" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="Neck">
                        <td>颈部装备</td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="Body">
                        <td>身体装备</td>
                        <td><input type="number" step="0.1" value="0.2" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="Finger">
                        <td>手指装备</td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0.3" data-coeff="defense"></td>
                    </tr>
                    <tr data-slot="Back">
                        <td>背部装备</td>
                        <td><input type="number" step="0.1" value="0.2" data-coeff="attack"></td>
                        <td><input type="number" step="0.1" value="0.6" data-coeff="element_damage"></td>
                        <td><input type="number" step="0.1" value="1.0" data-coeff="element_resistance"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="attack_speed"></td>
                        <td><input type="number" step="0.1" value="0.8" data-coeff="stats"></td>
                        <td><input type="number" step="0.1" value="0.5" data-coeff="defense"></td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <!-- 批量创建区域 -->
    <div class="config-section">
        <h2>批量装备创建</h2>
        <div class="batch-creator">
            <div class="creator-controls">
                <div class="control-group">
                    <label>数据输入方式：</label>
                    <select id="data-source">
                        <option value="manual">手动输入</option>
                        <option value="template">玩家装备模板</option>
                        <option value="monster-template">怪物装备模板</option>
                        <option value="json">JSON导入</option>
                    </select>
                </div>
                <div class="control-group">
                    <button type="button" id="load-template-btn" class="btn btn-info">加载装备模板</button>
                    <button type="button" id="preview-btn" class="btn btn-secondary" onclick="handlePreviewClick()">预览生成</button>
                    <button type="button" id="create-batch-btn" class="btn btn-primary" onclick="handleCreateClick()">批量创建</button>
                </div>
            </div>

            <!-- 手动输入区域 -->
            <div id="manual-input" class="input-section">
                <h3>手动输入装备数据</h3>
                <p><strong>格式：</strong>装备名称|装备部位|职业设置|品质等级|装备描述|元素类型</p>
                <p><strong>说明：</strong>
                    部位：TwoHanded/RightHand/LeftHand/Head/Neck/Body/Finger/Back | 
                    职业：战士/法师/射手/通用 | 
                    品质：0-6（普凡良优珍极玄） | 
                    元素：fire/ice/wind/electric
                </p>
                <div class="example-box">
                    <strong>示例：</strong><br>
                    烈焰巨剑|TwoHanded|战士|2|长达两米的巨型双手剑，散发着炽热的光芒|fire<br>
                    星陨法杖|TwoHanded|法师|2|华丽的法杖，杖头镶嵌着星形宝石|ice<br>
                    风暴长弓|TwoHanded|射手|2|优雅的长弓，弓身刻有流动的风纹|wind<br>
                    雷鸣战锤|RightHand|战士|2|导电金属制成的战锤，闪烁着电光|electric<br>
                    平衡之戒|Finger|通用|3|平衡四维属性的万能戒指|
                </div>
                <textarea id="equipment-data" rows="15" placeholder="请按照上述格式输入装备数据，每行一件装备..."></textarea>
            </div>

            <!-- 模板选择区域 -->
            <div id="template-input" class="input-section" style="display: none;">
                <h3>选择装备模板</h3>
                <div class="template-filters">
                    <label>装备部位：</label>
                    <select id="template-slot-filter">
                        <option value="all">全部</option>
                        <option value="TwoHanded">双手武器</option>
                        <option value="RightHand">右手武器</option>
                        <option value="LeftHand">左手装备</option>
                        <option value="Head">头部装备</option>
                        <option value="Neck">颈部装备</option>
                        <option value="Body">身体装备</option>
                        <option value="Finger">手指装备</option>
                        <option value="Back">背部装备</option>
                    </select>
                    <label>职业类型：</label>
                    <select id="template-job-filter">
                        <option value="all">全部</option>
                        <option value="warrior">战士</option>
                        <option value="mage">法师</option>
                        <option value="archer">射手</option>
                        <option value="none">通用</option>
                    </select>
                    <label>品质等级：</label>
                    <select id="template-quality-filter">
                        <option value="all">全部</option>
                        <option value="0-1">普凡级(0-1)</option>
                        <option value="2-3">良优级(2-3)</option>
                        <option value="4-5">珍极级(4-5)</option>
                        <option value="6">玄级(6)</option>
                    </select>
                </div>
                <div class="batch-select-controls">
                    <button type="button" class="btn btn-sm btn-success" onclick="selectAllTemplates()">全选</button>
                    <button type="button" class="btn btn-sm btn-warning" onclick="selectNoneTemplates()">全不选</button>
                    <button type="button" class="btn btn-sm btn-info" onclick="selectBySlot()">按部位选择</button>
                    <button type="button" class="btn btn-sm btn-info" onclick="selectByJob()">按职业选择</button>
                    <button type="button" class="btn btn-sm btn-info" onclick="selectByQuality()">按品质选择</button>
                    <span class="selected-count">已选择: <span id="selected-count">0</span> 件</span>
                </div>
                <div id="template-list" class="template-list">
                    <!-- 模板列表将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- JSON导入区域 -->
            <div id="json-input" class="input-section" style="display: none;">
                <h3>导入JSON数据</h3>
                <textarea id="json-data" rows="15" placeholder="请粘贴JSON格式的装备数据..."></textarea>
                <div class="import-controls">
                    <button type="button" id="validate-json-btn" class="btn btn-secondary">验证JSON</button>
                    <input type="file" id="json-file" accept=".json" style="display: none;">
                    <button type="button" id="load-file-btn" class="btn btn-secondary">从文件加载</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 预览区域 -->
    <div id="preview-section" class="config-section" style="display: none;">
        <h2>生成预览</h2>
        <div id="preview-content" class="preview-content">
            <!-- 预览内容将通过JavaScript动态生成 -->
        </div>
        <div class="preview-controls">
            <button type="button" id="confirm-create-btn" class="btn btn-success">确认创建</button>
            <button type="button" id="cancel-preview-btn" class="btn btn-secondary">取消</button>
        </div>
    </div>

    <!-- 结果显示区域 -->
    <div id="result-section" class="config-section" style="display: none;">
        <h2>创建结果</h2>
        <div id="result-content" class="result-content">
            <!-- 结果内容将通过JavaScript动态生成 -->
        </div>
    </div>
</div>

<script>
    // 传递数据到JavaScript
    const ATTRIBUTE_NAME_MAP = <?= json_encode($attribute_name_map, JSON_UNESCAPED_UNICODE) ?>;
    const ALL_JOBS = <?= json_encode($jobs, JSON_UNESCAPED_UNICODE) ?>;
</script>

<?php require_once 'layout_footer.php'; ?>
