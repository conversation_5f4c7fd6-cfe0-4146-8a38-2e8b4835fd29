document.addEventListener('DOMContentLoaded', () => {
    // State
    let scenes = {};
    let allMonsters = [];
    let allBuildings = [];
    let allZones = []; // 存储所有区域数据
    let layers = new Set();
    let layerNames = new Map(); // 存储图层名称 z -> {name, description}
    let currentLayer = 0;
    let selectedCoords = new Set();

    // 键盘导航相关状态
    let viewCenter = { x: 0, y: 0 }; // 当前视野中心点
    let viewSize = { width: 21, height: 21 }; // 视野大小（奇数，确保有中心点）
    let keyboardNavigationEnabled = true;
    let isDragging = false;
    let lastCellDuringDrag = null;

    // 全局提示框元素
    const globalTooltip = document.getElementById('global-tooltip');

    // DOM Elements
    const minXInput = document.getElementById('min-x');
    const minYInput = document.getElementById('min-y');
    const maxXInput = document.getElementById('max-x');
    const maxYInput = document.getElementById('max-y');
    const mapGrid = document.getElementById('map-grid');
    const redrawButton = document.getElementById('redraw-map');
    const toggleNavigationBtn = document.getElementById('toggle-navigation');
    const keyboardHint = document.querySelector('.keyboard-hint');
    const layerSelector = document.getElementById('layer-selector');
    const addLayerBtn = document.getElementById('add-layer-btn');
    const layerNameModal = document.getElementById('layer-name-modal');
    const layerNameForm = document.getElementById('layer-name-form');
    const sceneModal = document.getElementById('scene-modal');
    const closeSceneModalBtn = sceneModal.querySelector('.close-button');
    const sceneForm = document.getElementById('scene-form');
    const toast = document.getElementById('toast');
    const monsterSelect = document.getElementById('monster-select');
    const addMonsterBtn = document.getElementById('add-monster-btn');
    const monsterList = document.getElementById('monster-list');
    const deleteSceneBtn = document.getElementById('delete-scene-btn');
    
    // 建筑相关DOM元素
    const buildingSelect = document.getElementById('building-select');
    const addBuildingBtn = document.getElementById('add-building-btn');
    const buildingList = document.getElementById('building-list');
    
    // Batch Ops Elements
    const batchActionsPanel = document.getElementById('batch-actions-panel');
    const selectionCountSpan = document.getElementById('selection-count');
    const batchCreateBtn = document.getElementById('batch-create-btn');
    const batchEditBtn = document.getElementById('batch-edit-btn');
    const batchDeleteBtn = document.getElementById('batch-delete-btn');
    const batchEditModal = document.getElementById('batch-edit-modal');
    const closeBatchEditModalBtn = batchEditModal.querySelector('.close-button'); 
    const batchEditForm = document.getElementById('batch-edit-form');
    const batchMonsterSelect = document.getElementById('batch-monster-select');
    const batchAddMonsterBtn = document.getElementById('batch-add-monster-btn');
    const batchMonsterList = document.getElementById('batch-monster-list');
    const batchBuildingSelect = document.getElementById('batch-building-select');
    const batchAddBuildingBtn = document.getElementById('batch-add-building-btn');
    const batchBuildingList = document.getElementById('batch-building-list');

    // Batch Create Modal elements
    const batchCreateModal = document.getElementById('batch-create-modal');
    const closeBatchCreateModalBtn = batchCreateModal.querySelector('.close-button');
    const batchCreateForm = document.getElementById('batch-create-form');
    const batchCreateMonsterSelect = document.getElementById('batch-create-monster-select');
    const batchCreateAddMonsterBtn = document.getElementById('batch-create-add-monster-btn');
    const batchCreateMonsterList = document.getElementById('batch-create-monster-list');
    const batchCreateBuildingSelect = document.getElementById('batch-create-building-select');
    const batchCreateAddBuildingBtn = document.getElementById('batch-create-add-building-btn');
    const batchCreateBuildingList = document.getElementById('batch-create-building-list');

    // Utility Functions
    const getLineCoords = (x0, y0, x1, y1) => {
        // Bresenham's line algorithm
        const points = [];
        const dx = Math.abs(x1 - x0);
        const dy = Math.abs(y1 - y0);
        const sx = (x0 < x1) ? 1 : -1;
        const sy = (y0 < y1) ? 1 : -1;
        let err = dx - dy;

        while (true) {
            points.push({ x: x0, y: y0 });
            if ((x0 === x1) && (y0 === y1)) break;
            let e2 = 2 * err;
            if (e2 > -dy) { err -= dy; x0 += sx; }
            if (e2 < dx) { err += dx; y0 += sy; }
        }
        return points;
    };

    const showToast = (message, isError = false) => {
        toast.textContent = message;
        toast.className = `show ${isError ? 'error' : 'success'}`;
        setTimeout(() => { toast.className = toast.className.replace('show', ''); }, 3000);
    };

    const getCoordsFromCell = (cell) => ({ x: parseInt(cell.dataset.x), y: parseInt(cell.dataset.y) });

    // 存储传送点目标场景的映射
    let teleportDestinations = new Map(); // scene_id -> [source_scenes]

    // 存储场景NPC数据
    let sceneNPCs = {};

    // 加载传送点目标场景数据
    const loadTeleportDestinations = async () => {
        try {
            console.log('开始加载传送点目标场景数据...');
            const response = await fetch('api_teleporter.php?action=get_all_destinations');
            const data = await response.json();
            console.log('传送点API返回数据:', data);
            
            if (data.success) {
                teleportDestinations.clear();
                data.data.forEach(dest => {
                    console.log('处理传送点目标:', dest);
                    const key = `${dest.x},${dest.y},${dest.z}`; // 使用坐标作为键
                    if (!teleportDestinations.has(key)) {
                        teleportDestinations.set(key, []);
                    }
                    teleportDestinations.get(key).push({
                        scene_id: dest.scene_building_id,
                        building_name: dest.building_name || '传送点',
                        source_scene_name: dest.source_scene_name || '未知场景'
                    });
                });
                console.log('处理后的传送点数据:', Array.from(teleportDestinations.entries()));
            }
        } catch (error) {
            console.error('加载传送点目标场景失败:', error);
        }
    };

    // 加载场景NPC数据
    const loadSceneNPCs = async () => {
        try {
            const response = await fetch('api_scenes.php?action=get_scene_npcs');
            const data = await response.json();
            
            if (data.success) {
                sceneNPCs = {};
                data.data.forEach(npc => {
                    if (!sceneNPCs[npc.scene_id]) {
                        sceneNPCs[npc.scene_id] = [];
                    }
                    sceneNPCs[npc.scene_id].push(npc);
                });
                console.log('场景NPC数据加载完成:', sceneNPCs);
            }
        } catch (error) {
            console.error('加载场景NPC数据失败:', error);
        }
    };

    // Map Drawing
    const drawMap = async (recalculateFromInputs = false) => {
        // 重新加载传送点目标场景数据
        await loadTeleportDestinations();

        // 加载场景NPC数据
        await loadSceneNPCs();

        let bounds;
        if (recalculateFromInputs || !keyboardNavigationEnabled) {
            // 使用输入框的值（保持原有逻辑）
            bounds = {
                minX: parseInt(minXInput.value),
                minY: parseInt(minYInput.value),
                maxX: parseInt(maxXInput.value),
                maxY: parseInt(maxYInput.value),
            };
        } else {
            // 使用视野中心计算边界
            const halfWidth = Math.floor(viewSize.width / 2);
            const halfHeight = Math.floor(viewSize.height / 2);
            bounds = {
                minX: viewCenter.x - halfWidth,
                minY: viewCenter.y - halfHeight,
                maxX: viewCenter.x + halfWidth,
                maxY: viewCenter.y + halfHeight,
            };

            // 同步更新输入框（但不触发重绘）
            minXInput.value = bounds.minX;
            minYInput.value = bounds.minY;
            maxXInput.value = bounds.maxX;
            maxYInput.value = bounds.maxY;
        }

        const cols = bounds.maxX - bounds.minX + 1;
        const rows = bounds.maxY - bounds.minY + 1;
        mapGrid.innerHTML = '';
        mapGrid.style.setProperty('--grid-cols', cols);

        for (let y = bounds.maxY; y >= bounds.minY; y--) {
            for (let x = bounds.minX; x <= bounds.maxX; x++) {
                const cell = document.createElement('div');
                cell.classList.add('map-cell');
                cell.dataset.x = x;
                cell.dataset.y = y;
                
                // 确保cell的position为relative
                cell.style.position = 'relative';
                
                const scene = scenes[`${x},${y},${currentLayer}`];
                
                const contentWrapper = document.createElement('div');
                contentWrapper.className = 'cell-content';
                
                if (scene) {
                    cell.classList.add('existing');
                    // 根据安全区标记添加对应的CSS类
                    if (scene.is_safe_zone === 1 || scene.is_safe_zone === undefined) {
                        cell.classList.add('safe');
                    } else {
                        cell.classList.add('unsafe');
                    }
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'cell-name';
                    nameDiv.textContent = scene.name;
                    contentWrapper.appendChild(nameDiv);
                    
                    const idDiv = document.createElement('div');
                    idDiv.className = 'cell-id';
                    idDiv.textContent = scene.id;
                    idDiv.title = `ID: ${scene.id}`;
                    contentWrapper.appendChild(idDiv);

                    if (scene.monsters && scene.monsters.length > 0) {
                        const monsterDiv = document.createElement('div');
                        monsterDiv.className = 'cell-monsters';
                        const monsterText = `M: ${scene.monsters.map(m => `${m.name}(${m.quantity})`).join(', ')}`;
                        monsterDiv.textContent = monsterText;
                        monsterDiv.title = monsterText;
                        contentWrapper.appendChild(monsterDiv);
                    }

                    // 显示场景中的建筑
                    if (scene.buildings && scene.buildings.length > 0) {
                        const buildingDiv = document.createElement('div');
                        buildingDiv.className = 'cell-buildings';
                        const buildingText = `B: ${scene.buildings.map(b => b.name).join(', ')}`;
                        buildingDiv.textContent = buildingText;
                        buildingDiv.title = buildingText;
                        contentWrapper.appendChild(buildingDiv);

                        // 检查是否有传送点建筑
                        const teleporterBuilding = scene.buildings.find(b => b.type.toLowerCase() === 'teleporter');
                        if (teleporterBuilding) {
                            const marker = document.createElement('div');
                            marker.className = 'teleport-source-marker';
                            marker.setAttribute('data-tooltip', `传送点: ${teleporterBuilding.name}`);
                            cell.appendChild(marker);
                        }
                    }
                    
                    // 添加NPC头像
                    if (sceneNPCs[scene.id] && sceneNPCs[scene.id].length > 0) {
                        const npcContainer = document.createElement('div');
                        npcContainer.className = 'npc-avatars-container';
                        
                        sceneNPCs[scene.id].forEach((npc, index) => {
                            const npcAvatar = document.createElement('div');
                            npcAvatar.className = 'npc-avatar';
                            
                            // 设置NPC类型标识
                            let npcType = '';
                            if (npc.is_merchant && npc.is_quest_giver) {
                                npcType = '商人/任务';
                                npcAvatar.classList.add('merchant-quest');
                            } else if (npc.is_merchant) {
                                npcType = '商人';
                                npcAvatar.classList.add('merchant');
                            } else if (npc.is_quest_giver) {
                                npcType = '任务';
                                npcAvatar.classList.add('quest-giver');
                            }
                            
                            // 设置提示信息
                            const tooltipText = `${npc.name}${npcType ? ' - ' + npcType : ''}`;
                            npcAvatar.setAttribute('data-tooltip', tooltipText);
                            
                            npcContainer.appendChild(npcAvatar);
                        });
                        
                        cell.appendChild(npcContainer);
                    }
                } else {
                    cell.classList.add('empty');
                }
                
                cell.appendChild(contentWrapper);
                
                // 添加坐标信息
                const coordDiv = document.createElement('div');
                coordDiv.className = 'cell-coords';
                coordDiv.textContent = `(${x}, ${y})`;
                cell.appendChild(coordDiv);
                
                // 检查是否是传送目标，如果是，添加标记 - 放在最后添加以确保它在最上层
                const coordKey = `${x},${y},${currentLayer}`;
                if (teleportDestinations.has(coordKey)) {
                    console.log(`为场景 ${coordKey} 添加传送目标标记`);
                    const sources = teleportDestinations.get(coordKey);
                    console.log('传送源:', sources);
                    
                    // 创建目标标记
                    const marker = document.createElement('div');
                    marker.className = 'teleport-destination-marker';
                    
                    // 移除这里的data-tooltip属性，改为使用全局提示框
                    const tooltipText = sources.map(s => 
                        `来自: ${s.building_name} (${s.source_scene_name})`
                    ).join('\n');
                    
                    // 不再直接设置data-tooltip，而是使用自定义属性
                    marker.setAttribute('data-tooltip-content', tooltipText);
                    
                    // 添加到cell中
                    cell.appendChild(marker);
                    console.log('已添加传送目标标记元素:', marker);
                }

                // 添加视野中心标记
                if (x === viewCenter.x && y === viewCenter.y && keyboardNavigationEnabled) {
                    const centerMarker = document.createElement('div');
                    centerMarker.className = 'view-center-marker';
                    centerMarker.title = '视野中心 (键盘导航)';
                    centerMarker.innerHTML = '⊕';
                    cell.appendChild(centerMarker);
                }

                mapGrid.appendChild(cell);
            }
        }
        updateSelectionVisuals();
    };
    
    // Layer Management
    const renderLayers = () => {
        console.log('开始渲染图层，当前图层集合:', [...layers]);
        layerSelector.innerHTML = '';
        const sortedLayers = [...layers].sort((a, b) => a - b);
        console.log('排序后的图层:', sortedLayers);

        sortedLayers.forEach(z => {
            const layerInfo = layerNames.get(z);
            const layerName = layerInfo ? layerInfo.name : `图层 ${z}`;
            console.log(`渲染图层 Z=${z}, 名称=${layerName}`);

            const btn = document.createElement('button');
            btn.textContent = layerName;
            btn.className = `btn btn-sm ${z === currentLayer ? 'btn-primary' : 'btn-secondary'}`;
            btn.title = layerInfo && layerInfo.description ? layerInfo.description : `图层 ${z}`;
            btn.addEventListener('click', () => switchLayer(z));

            // 添加右键菜单来设置图层名称
            btn.addEventListener('contextmenu', (e) => {
                e.preventDefault();
                openLayerNameModal(z);
            });

            layerSelector.appendChild(btn);
        });

        console.log(`图层渲染完成，共渲染了 ${sortedLayers.length} 个图层按钮`);
    };

    const switchLayer = (z) => {
        currentLayer = z;
        selectedCoords.clear();
        drawMap();
        renderLayers();
        updateBatchActionsVisibility();
    };

    const openLayerNameModal = (z) => {
        const layerInfo = layerNames.get(z) || { name: `图层 ${z}`, description: '' };

        document.getElementById('layer-z').value = z;
        document.getElementById('layer-name').value = layerInfo.name;
        document.getElementById('layer-description').value = layerInfo.description || '';
        document.getElementById('layer-modal-title').textContent = `设置图层 ${z} 名称`;

        layerNameModal.style.display = 'block';
    };

    // 键盘导航功能
    const moveView = async (deltaX, deltaY) => {
        if (!keyboardNavigationEnabled) return;

        viewCenter.x += deltaX;
        viewCenter.y += deltaY;

        await drawMap();
        updateSelectionVisuals();
    };

    const handleKeyboardNavigation = (e) => {
        // 如果正在编辑输入框、下拉列表或模态框打开，不处理键盘导航
        if (e.target.tagName === 'INPUT' ||
            e.target.tagName === 'TEXTAREA' ||
            e.target.tagName === 'SELECT' ||
            e.target.closest('.custom-select') ||
            e.target.closest('.modal') ||
            document.querySelector('.modal[style*="block"]')) {
            return;
        }

        let handled = false;
        const step = e.shiftKey ? 5 : 1; // Shift键加速移动

        switch (e.key) {
            case 'ArrowUp':
            case 'w':
            case 'W':
                moveView(0, step);
                handled = true;
                break;
            case 'ArrowDown':
            case 's':
            case 'S':
                moveView(0, -step);
                handled = true;
                break;
            case 'ArrowLeft':
            case 'a':
            case 'A':
                moveView(-step, 0);
                handled = true;
                break;
            case 'ArrowRight':
            case 'd':
            case 'D':
                moveView(step, 0);
                handled = true;
                break;
            case 'Home':
                // 回到原点
                viewCenter.x = 0;
                viewCenter.y = 0;
                drawMap();
                handled = true;
                break;
            case 'Escape':
                // 清除选择
                selectedCoords.clear();
                updateSelectionVisuals();
                handled = true;
                break;
        }

        if (handled) {
            e.preventDefault();
            e.stopPropagation();
        }
    };

    // 更新导航模式UI
    const updateNavigationUI = () => {
        if (keyboardNavigationEnabled) {
            toggleNavigationBtn.textContent = '🎮 键盘导航';
            toggleNavigationBtn.className = 'btn btn-sm btn-success';
            toggleNavigationBtn.title = '当前：键盘导航模式，点击切换到坐标输入模式';
            keyboardHint.style.display = 'inline';
            keyboardHint.textContent = '方向键/WASD移动，Shift加速，Home回原点，Esc清除选择';
        } else {
            toggleNavigationBtn.textContent = '📍 坐标输入';
            toggleNavigationBtn.className = 'btn btn-sm btn-warning';
            toggleNavigationBtn.title = '当前：坐标输入模式，点击切换到键盘导航模式';
            keyboardHint.style.display = 'none';
        }
    };

    // 切换导航模式
    const toggleNavigationMode = async () => {
        keyboardNavigationEnabled = !keyboardNavigationEnabled;
        updateNavigationUI();
        await drawMap();
    };

    // Selection
    const updateSelectionVisuals = () => {
        document.querySelectorAll('.map-cell').forEach(cell => {
            const { x, y } = getCoordsFromCell(cell);
            if (selectedCoords.has(`${x},${y}`)) {
                cell.classList.add('selected');
            } else {
                cell.classList.remove('selected');
            }
        });
        updateBatchActionsVisibility();
    };
    
    const updateBatchActionsVisibility = () => {
        const count = selectedCoords.size;
        if (count > 0) {
            selectionCountSpan.textContent = count;
            batchActionsPanel.style.display = 'flex';
        } else {
            batchActionsPanel.style.display = 'none';
        }
    };
    
    // Modal Handling
    const openModal = (sceneData) => {
        const modalTitle = document.getElementById('modal-title');
        const sceneIdInput = document.getElementById('scene-id');
        
        // Reset form
        sceneForm.reset();
        monsterList.innerHTML = '';
        buildingList.innerHTML = ''; // 清空建筑列表
        
        if (sceneData.id) { // Existing scene
            modalTitle.textContent = '编辑场景';
            sceneIdInput.value = sceneData.id;
            deleteSceneBtn.style.display = 'inline-block';
        } else { // New scene
            modalTitle.textContent = '创建新场景';
            sceneIdInput.value = '';
            deleteSceneBtn.style.display = 'none';
        }
        
        document.getElementById('name').value = sceneData.name || `新场景 ${sceneData.x},${sceneData.y}`;
        document.getElementById('description').value = sceneData.description || '';
        document.getElementById('max_players').value = sceneData.max_players || 10;
        document.getElementById('scene-x').value = sceneData.x;
        document.getElementById('scene-y').value = sceneData.y;
        document.getElementById('scene-z').value = sceneData.z;
        
        // 设置安全区选择值
        const isSafeZone = sceneData.is_safe_zone !== undefined ? sceneData.is_safe_zone : 1;
        document.getElementById('is_safe_zone').value = isSafeZone;

        // 设置区域选择值
        const zoneId = sceneData.zone_id || 'zone_default';
        document.getElementById('zone_id').value = zoneId;
        
        sceneData.monsters?.forEach(monster => {
            addMonsterToForm(monster.id, monster.quantity, 'single');
        });
        
        // 加载场景中的建筑
        sceneData.buildings?.forEach(building => {
            addBuildingToForm(building.id, 'single');
        });

        // 动态添加商店配置按钮
        const actionsContainer = sceneForm.querySelector('.form-actions');
        // 清理所有配置按钮
        actionsContainer.querySelectorAll('.shop-config-btn, .teleporter-config-btn, .npc-config-btn').forEach(btn => btn.remove());

        // 添加NPC配置按钮
        if (sceneData.id) {
            const npcBtn = document.createElement('button');
            npcBtn.type = 'button';
            npcBtn.className = 'btn btn-info npc-config-btn';
            npcBtn.textContent = `配置场景NPC`;
            npcBtn.onclick = () => window.location.href = `scene_npcs.php?scene_id=${sceneData.id}`;
            actionsContainer.insertBefore(npcBtn, actionsContainer.firstChild);
        }

        sceneData.buildings?.forEach(building => {
            // 根据建筑类型添加不同的配置按钮
            if (building.type.toLowerCase().includes('shop')) {
                const shopBtn = document.createElement('button');
                shopBtn.type = 'button';
                shopBtn.className = 'btn btn-info shop-config-btn';
                shopBtn.textContent = `配置[${building.name}]的商品`;
                shopBtn.onclick = () => openShopConfigModal(building.scene_building_id, sceneData.name, building.name);
                actionsContainer.insertBefore(shopBtn, actionsContainer.firstChild);
            } else if (building.type === 'TELEPORTER') {
                const teleporterBtn = document.createElement('button');
                teleporterBtn.type = 'button';
                teleporterBtn.className = 'btn btn-info teleporter-config-btn';
                teleporterBtn.textContent = `配置[${building.name}]的传送目标`;
                teleporterBtn.onclick = () => openTeleporterConfigModal(building.scene_building_id, sceneData.name, building.name, sceneData.id, building.id);
                actionsContainer.insertBefore(teleporterBtn, actionsContainer.firstChild);
            }
        });
        
        sceneModal.style.display = 'block';
    };

    const renderTeleporterDestinations = (destinations) => {
        const destinationList = document.getElementById('destination-list');
        destinationList.innerHTML = '';
        if (!destinations || destinations.length === 0) {
            destinationList.innerHTML = '<p>暂无目标场景。</p>';
            return;
        }
        destinations.forEach(dest => {
            const item = document.createElement('div');
            item.className = 'destination-item';
            item.dataset.id = dest.id; // This is teleporter_destinations.id

            // 创建场景信息显示
            const sceneInfo = document.createElement('span');
            sceneInfo.textContent = `${dest.scene_name} (${dest.scene_id})`;

            // 创建消耗物品信息显示
            const costInfo = document.createElement('div');
            costInfo.className = 'cost-info';
            if (dest.required_item_name && dest.required_quantity) {
                costInfo.innerHTML = `<small>消耗: ${dest.required_item_name} x${dest.required_quantity}</small>`;
            } else {
                costInfo.innerHTML = '<small>无消耗</small>';
            }

            // 创建配置按钮
            const configBtn = document.createElement('button');
            configBtn.type = 'button';
            configBtn.className = 'btn btn-sm btn-info config-cost-btn';
            configBtn.innerHTML = '⚙️';
            configBtn.title = '配置消耗物品';
            configBtn.onclick = () => openItemCostModal(dest.id, dest.scene_name, dest.required_item_id, dest.required_quantity);

            const removeBtn = document.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'remove-destination-btn';
            removeBtn.innerHTML = '&times;';
            removeBtn.title = '移除此目标';
            removeBtn.onclick = async () => {
                if (!confirm(`确定要移除目标场景 ${dest.scene_name} 吗？`)) return;
                try {
                    const response = await fetch('api_teleporter.php?action=delete_destination', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ id: dest.id })
                    });
                    const result = await response.json();
                    if (!result.success) throw new Error(result.message);
                    showToast('目标已移除');
                    item.remove();
                    if (destinationList.children.length === 1 && destinationList.firstChild.tagName === 'P') {
                         destinationList.innerHTML = '<p>暂无目标场景。</p>';
                    }
                } catch (err) {
                    showToast(`移除失败: ${err.message}`, true);
                }
            };

            item.appendChild(sceneInfo);
            item.appendChild(costInfo);
            item.appendChild(configBtn);
            item.appendChild(removeBtn);
            destinationList.appendChild(item);
        });
    };

    // 物品成本配置相关变量
    let allConsumableItems = [];

    // 加载可消耗物品列表
    const loadConsumableItems = async () => {
        try {
            const response = await fetch('api_teleporter.php?action=get_all_items');
            const data = await response.json();
            if (data.success) {
                allConsumableItems = data.data;
            }
        } catch (error) {
            console.error('加载可消耗物品失败:', error);
        }
    };

    // 打开物品成本配置模态框
    const openItemCostModal = async (destinationId, sceneName, currentItemId, currentQuantity) => {
        const modal = document.getElementById('item-cost-modal');
        const itemSelect = document.getElementById('required-item-select');
        const quantityInput = document.getElementById('required-quantity');

        // 设置模态框信息
        document.getElementById('item-cost-scene-name').textContent = sceneName;
        document.getElementById('item-cost-destination-id').value = destinationId;

        // 加载物品列表（如果还没有加载）
        if (allConsumableItems.length === 0) {
            await loadConsumableItems();
        }

        // 填充物品下拉列表
        itemSelect.innerHTML = '<option value="">无需消耗物品</option>';
        allConsumableItems.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} (${item.category})`;
            if (item.id == currentItemId) {
                option.selected = true;
            }
            itemSelect.appendChild(option);
        });

        // 设置当前数量
        quantityInput.value = currentQuantity || 1;

        // 确保模态框显示在最顶层
        modal.style.display = 'block';
        modal.style.zIndex = '1003';

        // 聚焦到物品选择框
        setTimeout(() => {
            itemSelect.focus();
        }, 100);
    };

    const openTeleporterConfigModal = async (sceneBuildingId, sceneName, buildingName, sceneId, buildingId) => {
        // 添加调试日志
        console.log('传送点配置参数:', {
            sceneBuildingId, // scene_buildings表的ID
            sceneName,
            buildingName,
            sceneId,  // 场景ID
            buildingId // 建筑模板ID
        });

        const modal = document.getElementById('teleporter-config-modal');
        const destinationSelect = document.getElementById('destination-scene-select');
        const continentFilter = document.getElementById('teleporter-continent-filter');
        const zoneFilter = document.getElementById('teleporter-zone-filter');
        const destinationList = document.getElementById('destination-list');

        // 1. Populate header and hidden inputs
        document.getElementById('teleporter-config-scene-name').textContent = sceneName;
        document.getElementById('teleporter-config-building-name').textContent = buildingName;
        document.getElementById('teleporter-config-scene-building-id').value = sceneBuildingId;
        document.getElementById('teleporter-config-scene-id').value = sceneId;
        document.getElementById('teleporter-config-building-id').value = buildingId;

        destinationList.innerHTML = '<p class="loading-text">正在加载目标...</p>';
        modal.style.display = 'block';

        // 存储大陆和区域数据
        let continents = [];
        let zones = [];

        const populateScenesDropdown = (continentId = '', zoneId = '') => {
            destinationSelect.innerHTML = '<option value="">选择目标场景...</option>';
            const sortedScenes = Object.values(scenes).sort((a,b) => a.name.localeCompare(b.name, 'zh-CN'));

            let addedCount = 0;
            sortedScenes.forEach(s => {
                let shouldInclude = true;

                // 如果选择了大陆，只显示该大陆的场景
                if (continentId) {
                    const sceneZone = zones.find(z => z.id === s.zone_id);
                    if (!sceneZone || sceneZone.continent !== continentId) {
                        shouldInclude = false;
                    }
                }

                // 如果选择了区域，只显示该区域的场景
                if (zoneId && s.zone_id !== zoneId) {
                    shouldInclude = false;
                }

                if (shouldInclude) {
                    const sceneZone = zones.find(z => z.id === s.zone_id);
                    const continent = continents.find(c => c.id === sceneZone?.continent);
                    const option = document.createElement('option');
                    option.value = s.id;
                    option.textContent = `${s.name} (${continent?.display_name || '未知大陆'} > ${sceneZone?.name || '未知区域'}) [${s.x},${s.y},${s.z}]`;
                    destinationSelect.appendChild(option);
                    addedCount++;
                }
            });

            console.log(`传送点配置：筛选条件 大陆=${continentId}, 区域=${zoneId} 添加了 ${addedCount} 个场景到下拉列表`);
        };

        const populateContinentFilter = () => {
            continentFilter.innerHTML = '<option value="">所有大陆</option>';
            continents.forEach(continent => {
                const option = document.createElement('option');
                option.value = continent.id;
                option.textContent = continent.display_name;
                continentFilter.appendChild(option);
            });
        };

        const populateZoneFilter = (continentId = '') => {
            zoneFilter.innerHTML = '<option value="">所有区域</option>';
            const filteredZones = continentId ? zones.filter(z => z.continent === continentId) : zones;
            filteredZones.forEach(zone => {
                const option = document.createElement('option');
                option.value = zone.id;
                option.textContent = zone.name;
                zoneFilter.appendChild(option);
            });
        };

        // 事件监听器
        continentFilter.addEventListener('change', () => {
            const selectedContinent = continentFilter.value;
            populateZoneFilter(selectedContinent);
            zoneFilter.value = ''; // 重置区域选择
            populateScenesDropdown(selectedContinent, '');
        });

        zoneFilter.addEventListener('change', () => {
            const selectedContinent = continentFilter.value;
            const selectedZone = zoneFilter.value;
            populateScenesDropdown(selectedContinent, selectedZone);
        });

        // 先加载大陆和区域数据
        try {
            const [continentsResponse, zonesResponse] = await Promise.all([
                fetch('api_continents.php?action=get_continents'),
                fetch('api_scenes.php?action=get_zones')
            ]);

            const continentsData = await continentsResponse.json();
            const zonesData = await zonesResponse.json();

            if (continentsData.success) {
                continents = continentsData.continents || [];
                populateContinentFilter();
            }

            if (zonesData.success) {
                zones = zonesData.zones || [];
                populateZoneFilter();
                populateScenesDropdown(); // 初始显示所有场景
            }
        } catch (error) {
            console.error('加载大陆和区域数据失败:', error);
            showToast('加载筛选数据失败', true);
        }

        // 然后异步加载当前的传送目标
        try {
            console.log('开始加载传送点目标数据...');
            const response = await fetch(`api_teleporter.php?action=get_destinations&scene_building_id=${sceneBuildingId}`);
            const data = await response.json();
            console.log('传送点目标数据加载完成:', data);
            if (!data.success) throw new Error(data.message);
            renderTeleporterDestinations(data.data);
        } catch (error) {
            console.error('加载传送点目标失败:', error);
            destinationList.innerHTML = `<p class="error-text">加载当前目标失败: ${error.message}。请确保后端API (api_teleporter.php) 已正确实现。</p>`;
        }
    };

    const addMonsterToForm = (monsterId, quantity, type) => {
        const targetList = type === 'batch' ? batchMonsterList : monsterList;
        const monster = allMonsters.find(m => m.id == monsterId);
        if (!monster) return;

        const monsterItem = document.createElement('div');
        monsterItem.className = 'monster-item';
        monsterItem.dataset.id = monsterId;
        monsterItem.dataset.quantity = quantity;
        monsterItem.innerHTML = `
            <span>${monster.name} (x${quantity})</span>
            <button type="button" class="remove-monster-btn">&times;</button>
        `;
        monsterItem.querySelector('.remove-monster-btn').addEventListener('click', () => monsterItem.remove());
        targetList.appendChild(monsterItem);
    };

    const hideBatchActions = () => { selectedCoords.clear(); updateSelectionVisuals(); };

    // API Calls
    const apiCall = async (action, body, isJson = true) => {
        try {
            const options = {
                method: 'POST',
                headers: {},
                body: null
            };

            if (isJson) {
                options.headers['Content-Type'] = 'application/json';
                options.body = JSON.stringify(body);
            } else { // form-data
                options.body = body;
            }
            
            const response = await fetch('api_scenes.php' + (isJson ? `?action=${action}` : ''), options);
            if (!response.ok) throw new Error(`HTTP error! status: ${response.status}`);
            const result = await response.json();
            if (!result.success) throw new Error(result.message);
            return result;
        } catch (error) {
            console.error('API Error:', error);
            showToast(error.message, true);
            return null;
        }
    };

    // 加载区域数据
    const loadZones = async () => {
        try {
            const response = await fetch('api_scenes.php', {
                method: 'POST',
                headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
                body: 'action=get_zones'
            });
            const result = await response.json();
            if (result.success) {
                allZones = result.zones || [];
                populateZoneSelects();
            }
        } catch (error) {
            console.error('Failed to load zones:', error);
        }
    };

    // 填充区域选择框
    const populateZoneSelects = () => {
        const zoneSelects = [
            document.getElementById('zone_id'),
            document.getElementById('batch-create-zone-id'),
            document.getElementById('batch-edit-zone-id')
        ];

        zoneSelects.forEach(select => {
            if (!select) return;

            // 保留第一个选项（"选择区域..."或"不修改"）
            const firstOption = select.firstElementChild;
            select.innerHTML = '';
            if (firstOption) {
                select.appendChild(firstOption);
            }

            allZones.forEach(zone => {
                const option = document.createElement('option');
                option.value = zone.id;
                const continentName = zone.continent_name || zone.continent;
                option.textContent = `${zone.name} (${continentName})`;
                select.appendChild(option);
            });
        });
    };

    // Event Listeners
    redrawButton.addEventListener('click', async () => await drawMap(true));
    toggleNavigationBtn.addEventListener('click', toggleNavigationMode);

    // 大陆区域管理按钮
    const manageContinentsBtn = document.getElementById('manage-continents-btn');
    if (manageContinentsBtn) {
        manageContinentsBtn.addEventListener('click', () => {
            window.location.href = 'continents.php';
        });
    }

    // 智能焦点管理和键盘导航
    const initializeKeyboardNavigation = () => {
        // 键盘导航事件监听
        document.addEventListener('keydown', handleKeyboardNavigation);

        // 智能焦点管理 - 只在点击地图区域时获得焦点
        document.addEventListener('click', (e) => {
            // 如果点击的是表单元素或其容器，不改变焦点
            if (e.target.tagName === 'INPUT' ||
                e.target.tagName === 'TEXTAREA' ||
                e.target.tagName === 'SELECT' ||
                e.target.tagName === 'BUTTON' ||
                e.target.closest('.modal') ||
                e.target.closest('.form-group') ||
                e.target.closest('.btn') ||
                e.target.closest('select')) {
                return;
            }

            // 只有点击地图区域或空白区域时才让body获得焦点
            if (e.target.closest('#map-grid') ||
                e.target.closest('#map-controls') ||
                e.target === document.body) {
                document.body.focus();
            }
        });
    };

    // 初始化键盘导航
    initializeKeyboardNavigation();

    // 页面加载完成后自动聚焦，确保键盘导航立即可用
    document.body.setAttribute('tabindex', '-1');
    document.body.focus();
    
    addLayerBtn.addEventListener('click', () => {
        const newLayer = layers.size > 0 ? Math.max(...layers) + 1 : 0;
        openLayerNameModal(newLayer);
    });

    // Selection Logic
    mapGrid.addEventListener('mousedown', (e) => {
        const cell = e.target.closest('.map-cell');
        if (!cell) return;
        isDragging = true;
        
        const { x, y } = getCoordsFromCell(cell);
        const coordKey = `${x},${y}`;

        if (!e.ctrlKey) {
            selectedCoords.clear();
        }
        
        // Always add, don't toggle, when starting a drag-based selection.
        selectedCoords.add(coordKey);
        
        lastCellDuringDrag = cell;
        updateSelectionVisuals();
    });

    document.addEventListener('mouseup', (e) => {
        isDragging = false;
        lastCellDuringDrag = null;
    });

    mapGrid.addEventListener('mousemove', (e) => {
        if (!isDragging || !lastCellDuringDrag) return;
        const currentCell = e.target.closest('.map-cell');
        if (!currentCell || currentCell === lastCellDuringDrag) return;

        // Draw a line segment from the PREVIOUS cell to the CURRENT cell.
        const startCoords = getCoordsFromCell(lastCellDuringDrag);
        const endCoords = getCoordsFromCell(currentCell);

        // Add the new segment's points to the existing selection.
        const linePoints = getLineCoords(startCoords.x, startCoords.y, endCoords.x, endCoords.y);
        linePoints.forEach(p => {
            const key = `${p.x},${p.y}`;
            // To allow "erasing" by drawing back over a line, we can toggle points.
            if (e.ctrlKey && selectedCoords.has(key)) {
                selectedCoords.delete(key)
            } else {
                selectedCoords.add(key)
            }
        });
        
        lastCellDuringDrag = currentCell;
        updateSelectionVisuals();
    });

    mapGrid.addEventListener('dblclick', (e) => {
        const cell = e.target.closest('.map-cell');
        if (!cell) return;
        
        const { x, y } = getCoordsFromCell(cell);
        const scene = scenes[`${x},${y},${currentLayer}`] || {
            x, y, z: currentLayer, monsters: []
        };
        openModal(scene);
    });

    closeSceneModalBtn.addEventListener('click', () => sceneModal.style.display = 'none');

    // Layer name modal events
    layerNameModal.querySelector('.close-button').addEventListener('click', () => {
        layerNameModal.style.display = 'none';
    });

    layerNameForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(layerNameForm);
        const z = parseInt(formData.get('z'));
        const name = formData.get('name').trim();
        const description = formData.get('description').trim();

        if (!name) {
            showToast('图层名称不能为空', true);
            return;
        }

        try {
            const response = await fetch('api_scenes.php', {
                method: 'POST',
                body: new URLSearchParams({
                    action: 'set_layer_name',
                    z: z,
                    name: name,
                    description: description
                })
            });

            const data = await response.json();
            if (!data.success) throw new Error(data.message);

            // 更新本地缓存
            layerNames.set(z, { name, description });

            // 如果是新图层，添加到图层集合并切换
            if (!layers.has(z)) {
                layers.add(z);
                switchLayer(z);
                showToast(`已创建新图层 "${name}" 并切换到该图层`);
            } else {
                renderLayers();
                showToast(`图层 "${name}" 名称已更新`);
            }

            layerNameModal.style.display = 'none';

        } catch (error) {
            console.error('设置图层名称失败:', error);
            showToast(`设置图层名称失败: ${error.message}`, true);
        }
    });
    sceneModal.addEventListener('click', (e) => {
        if (e.target === sceneModal) sceneModal.style.display = 'none';
    });

    addMonsterBtn.addEventListener('click', () => {
        const monsterId = monsterSelect.value;
        const quantity = document.getElementById('monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'single');
        }
    });

    // 添加建筑按钮点击事件
    addBuildingBtn.addEventListener('click', () => {
        const buildingId = buildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'single');
        }
    });

    // 添加建筑到表单
    const addBuildingToForm = (buildingId, type) => {
        let targetList;
        if (type === 'batch') {
            targetList = batchBuildingList;
        } else if (type === 'batch-create') {
            targetList = batchCreateBuildingList;
        } else {
            targetList = buildingList;
        }
        
        const building = allBuildings.find(b => b.id == buildingId);
        if (!building) return;

        // 检查是否已经添加了这个建筑
        const existingItem = targetList.querySelector(`.building-item[data-id="${buildingId}"]`);
        if (existingItem) {
            showToast('该建筑已添加到场景中', true);
            return;
        }

        const buildingItem = document.createElement('div');
        buildingItem.className = 'building-item';
        buildingItem.dataset.id = buildingId;
        buildingItem.innerHTML = `
            <span>${building.name} (${building.type})</span>
            <button type="button" class="remove-building-btn">&times;</button>
        `;
        buildingItem.querySelector('.remove-building-btn').addEventListener('click', () => buildingItem.remove());
        targetList.appendChild(buildingItem);
    };

    // Form Submissions
    sceneForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        const formData = new FormData(e.target);
        const sceneData = Object.fromEntries(formData.entries());
        
        sceneData.monsters = [];
        monsterList.querySelectorAll('.monster-item').forEach(item => {
            sceneData.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });
        
        // 收集建筑数据
        sceneData.buildings = [];
        buildingList.querySelectorAll('.building-item').forEach(item => {
            sceneData.buildings.push({
                id: item.dataset.id
            });
        });
        
        const action = sceneData.id ? 'update' : 'create';
        const result = await apiCall(action, sceneData);

        if (result) {
            showToast(result.message);
            sceneModal.style.display = 'none';
            await initializeApp(false);
        }
    });

    deleteSceneBtn.addEventListener('click', async () => {
        const sceneId = document.getElementById('scene-id').value;
        if (!sceneId || !confirm(`确定要删除场景 ${sceneId} 吗？此操作不可撤销。`)) return;

        const result = await apiCall('batch_delete', { ids: [sceneId] });

        if (result) {
            showToast(result.message);
            sceneModal.style.display = 'none';
            await initializeApp(false);
        }
    });

    // Batch Ops Handlers
    batchCreateBtn.addEventListener('click', () => {
        if (selectedCoords.size === 0) return;
        
        const scenesOnLayer = Object.values(scenes).filter(s => s.z === currentLayer);
        const scenesByCoords = new Map(scenesOnLayer.map(s => [`${s.x},${s.y}`, s]));
        const hasEmptyCells = [...selectedCoords].some(coord => !scenesByCoords.has(coord));

        if (!hasEmptyCells) {
            showToast("选中的所有单元格都已有场景，无法创建。", true);
            return;
        }

        batchCreateForm.reset();
        batchCreateMonsterList.innerHTML = '';
        batchCreateModal.style.display = 'block';
    });
    
    closeBatchCreateModalBtn.addEventListener('click', () => batchCreateModal.style.display = 'none');
    batchCreateModal.addEventListener('click', (e) => {
        if (e.target === batchCreateModal) batchCreateModal.style.display = 'none';
    });

    batchCreateForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (selectedCoords.size === 0) return;

        const scenesOnLayer = Object.values(scenes).filter(s => s.z === currentLayer);
        const scenesByCoords = new Map(scenesOnLayer.map(s => [`${s.x},${s.y}`, s]));

        const scenesToCreate = [...selectedCoords]
            .filter(coordKey => !scenesByCoords.has(coordKey))
            .map(coordKey => {
                const [x, y] = coordKey.split(',').map(Number);
                return { x, y, z: currentLayer };
            });

        if (scenesToCreate.length === 0) {
            showToast("没有可创建的空白单元格。", true);
            return;
        }
        
        const formData = new FormData(e.target);
        const options = {
            name: formData.get('name'),
            description: formData.get('description'),
            use_suffix: formData.has('use_suffix'),
            is_safe_zone: formData.get('is_safe_zone'),
            zone_id: formData.get('zone_id'),
            monsters: []
        };
        
        batchCreateMonsterList.querySelectorAll('.monster-item').forEach(item => {
            options.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });

        // 收集建筑数据
        options.buildings = [];
        batchCreateBuildingList.querySelectorAll('.building-item').forEach(item => {
            options.buildings.push({
                id: item.dataset.id
            });
        });

        const result = await apiCall('batch_create', { scenes: scenesToCreate, options });
        
        if (result) {
            showToast(result.message);
            batchCreateModal.style.display = 'none';
            hideBatchActions();
            await initializeApp(false);
        }
    });

    batchCreateAddMonsterBtn.addEventListener('click', () => {
        const monsterId = batchCreateMonsterSelect.value;
        const quantity = document.getElementById('batch-create-monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'batch-create');
        }
    });

    batchDeleteBtn.addEventListener('click', async () => {
        if (selectedCoords.size === 0 || !confirm(`确定要删除选中的 ${selectedCoords.size} 个场景吗？`)) return;

        const idsToDelete = [];
        selectedCoords.forEach(coordKey => {
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];
            if (scene) idsToDelete.push(scene.id);
        });

        if (idsToDelete.length === 0) {
            showToast('选中的都是空坐标，无需删除。');
            hideBatchActions();
            return;
        }

        const result = await apiCall('batch_delete', { ids: idsToDelete });
        if (result) {
            showToast(result.message);
            hideBatchActions();
            await initializeApp(false);
        }
    });

    // 控制开始数字输入框的显示和隐藏
    document.getElementById('batch-edit-suffix-toggle').addEventListener('change', function() {
        const startNumberContainer = document.getElementById('batch-edit-start-number').parentNode;
        startNumberContainer.style.display = this.checked ? 'flex' : 'none';
    });
    
    batchEditBtn.addEventListener('click', () => {
        if (selectedCoords.size === 0) return;
        
        // Reset form first
        batchEditForm.reset();
        batchMonsterList.innerHTML = '';
        batchBuildingList.innerHTML = '';
        
        // 初始化时根据复选框状态显示/隐藏开始数字
        const suffixToggle = document.getElementById('batch-edit-suffix-toggle');
        const startNumberContainer = document.getElementById('batch-edit-start-number').parentNode;
        startNumberContainer.style.display = suffixToggle.checked ? 'flex' : 'none';

        // If exactly one scene is selected, pre-fill its data for convenience.
        // For multiple scenes, we leave it blank because their data might differ.
        if (selectedCoords.size === 1) {
            const coordKey = selectedCoords.values().next().value;
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];

            if (scene) {
                // Pre-fill name and description
                document.getElementById('batch-edit-name').value = scene.name || '';
                document.getElementById('batch-edit-description').value = scene.description || '';
                // When loading a single scene's name, it's safer to disable suffixing by default.
                document.getElementById('batch-edit-suffix-toggle').checked = false;

                // Pre-fill monsters
                scene.monsters?.forEach(monster => {
                    addMonsterToForm(monster.id, monster.quantity, 'batch');
                });
                
                // Pre-fill buildings
                scene.buildings?.forEach(building => {
                    addBuildingToForm(building.id, 'batch');
                });
            }
        }
        
        batchEditModal.style.display = 'block';
    });
    
    closeBatchEditModalBtn.addEventListener('click', () => batchEditModal.style.display = 'none');
    batchEditModal.addEventListener('click', (e) => {
        if (e.target === batchEditModal) batchEditModal.style.display = 'none';
    });
    
    batchAddMonsterBtn.addEventListener('click', () => {
        const monsterId = batchMonsterSelect.value;
        const quantity = document.getElementById('batch-monster-quantity').value;
        if (monsterId && quantity > 0) {
            addMonsterToForm(monsterId, quantity, 'batch');
        }
    });

    batchEditForm.addEventListener('submit', async (e) => {
        e.preventDefault();
        if (selectedCoords.size === 0) return;
        
        const idsToUpdate = [];
        selectedCoords.forEach(coordKey => {
            const [x, y] = coordKey.split(',').map(Number);
            const scene = scenes[`${x},${y},${currentLayer}`];
            if (scene) idsToUpdate.push(scene.id);
        });

        if (idsToUpdate.length === 0) {
            showToast('没有已存在的场景被选中，无法修改。', true);
            return;
        }

        const updates = {};
        const newName = document.getElementById('batch-edit-name').value.trim();
        if (newName) {
            updates.name = newName;
            updates.use_suffix = document.getElementById('batch-edit-suffix-toggle').checked;
        }

        const newDescription = document.getElementById('batch-edit-description').value;
        console.log('=== 批量编辑描述调试 ===');
        console.log('原始值:', JSON.stringify(newDescription));
        console.log('长度:', newDescription.length);
        console.log('trim后:', JSON.stringify(newDescription.trim()));
        console.log('trim后长度:', newDescription.trim().length);
        console.log('判断条件 newDescription.trim() !== "":', newDescription.trim() !== '');

        // 只有当描述框有内容时才更新（允许用户通过输入空格等方式清空描述）
        if (newDescription.trim() !== '') {
            console.log('✓ 添加描述到updates');
            updates.description = newDescription;
        } else {
            console.log('✗ 描述为空，不添加到updates');
        }

        console.log('当前updates对象:', JSON.stringify(updates));
        
        // 获取安全区设置
        const safeZoneValue = document.getElementById('batch-edit-safe-zone').value;
        if (safeZoneValue !== '') {
            updates.is_safe_zone = safeZoneValue;
        }

        // 获取区域设置
        const zoneValue = document.getElementById('batch-edit-zone-id').value;
        if (zoneValue !== '') {
            updates.zone_id = zoneValue;
        }

        updates.monsters = [];
        batchMonsterList.querySelectorAll('.monster-item').forEach(item => {
            updates.monsters.push({
                id: item.dataset.id,
                quantity: item.dataset.quantity
            });
        });
        
        // 收集建筑数据
        updates.buildings = [];
        batchBuildingList.querySelectorAll('.building-item').forEach(item => {
            updates.buildings.push({
                id: item.dataset.id
            });
        });

        if (Object.keys(updates).length === 0) {
            showToast('没有提供任何修改内容。', true);
            return;
        }
        
        // 如果包含名称更新并勾选了后缀，使用batch_update_name API
        if (newName && updates.use_suffix) {
            const startNumber = parseInt(document.getElementById('batch-edit-start-number').value) || 1;
            const formData = new FormData();
            formData.append('action', 'batch_update_name');
            formData.append('scene_ids', JSON.stringify(idsToUpdate));
            formData.append('name_prefix', newName);
            formData.append('start_number', startNumber);

            // 如果有描述更新，也添加到FormData中
            if (updates.description !== undefined) {
                formData.append('description', updates.description);
            }

            // 如果有安全区更新，也添加到FormData中
            if (updates.is_safe_zone !== undefined) {
                formData.append('is_safe_zone', updates.is_safe_zone);
            }

            // 如果有怪物更新，也添加到FormData中
            if (updates.monsters !== undefined) {
                formData.append('monsters', JSON.stringify(updates.monsters));
            }

            // 如果有建筑更新，也添加到FormData中
            if (updates.buildings !== undefined) {
                formData.append('buildings', JSON.stringify(updates.buildings));
            }

            const result = await apiCall('batch_update_name', formData, false);
            if(result) {
                showToast(result.message);
                batchEditModal.style.display = 'none';
                hideBatchActions();
                await initializeApp(false);
            }
            return;
        }
        
        // 否则使用普通的批量更新
        console.log('发送批量更新请求，updates:', JSON.stringify(updates));
        const result = await apiCall('batch_update', { ids: idsToUpdate, updates });

        if(result) {
            showToast(result.message);
            batchEditModal.style.display = 'none';
            hideBatchActions();
            await initializeApp(false);
        }
    });

    // 批量编辑添加建筑按钮
    batchAddBuildingBtn.addEventListener('click', () => {
        const buildingId = batchBuildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'batch');
        }
    });
    
    // 批量创建添加建筑按钮
    batchCreateAddBuildingBtn.addEventListener('click', () => {
        const buildingId = batchCreateBuildingSelect.value;
        if (buildingId) {
            addBuildingToForm(buildingId, 'batch-create');
        }
    });

    // Initialization
    const initializeApp = async (isFirstLoad = true) => {
            console.log('开始初始化应用...');
        try {
            // 并行加载场景数据和区域数据
            const [scenesResponse, zonesResponse] = await Promise.all([
                fetch('api_scenes.php?action=get_all'),
                loadZones()
            ]);

            const data = await scenesResponse.json();
            if (!data.success) throw new Error(data.message);

            console.log('API返回的场景数据:', data.scenes);

            scenes = {};
            layers.clear();
            layerNames.clear();

            // 首先从场景数据中添加图层
            data.scenes.forEach(s => {
                scenes[`${s.x},${s.y},${s.z}`] = s;
                layers.add(s.z);
            });

            // 加载图层名称，并确保所有有名称的图层都被添加到layers集合中
            if (data.layer_names) {
                console.log('加载图层名称数据:', data.layer_names);
                data.layer_names.forEach(layer => {
                    layerNames.set(layer.z, {
                        name: layer.name,
                        description: layer.description || ''
                    });
                    // 重要：即使没有场景，也要添加有名称的图层
                    layers.add(layer.z);
                    console.log(`添加图层 Z=${layer.z}, 名称=${layer.name}`);
                });
            }

            // 确保至少有layer 0
            if (layers.size === 0) {
                layers.add(0);
            }

            console.log('最终的图层集合:', [...layers]);
            console.log('图层名称映射:', layerNames);

            if (isFirstLoad && data.scenes.length > 0) {
                // Find the lowest layer that has scenes, and switch to it.
                const layersWithScenes = [...new Set(data.scenes.map(s => s.z))].sort((a, b) => a - b);
                const targetLayer = layersWithScenes.length > 0 ? layersWithScenes[0] : 0;
                currentLayer = targetLayer;

                const scenesOnTargetLayer = data.scenes.filter(s => s.z === targetLayer);

                if (scenesOnTargetLayer.length > 0) {
                    const allX = scenesOnTargetLayer.map(s => s.x);
                    const allY = scenesOnTargetLayer.map(s => s.y);
                    const minSceneX = Math.min(...allX);
                    const maxSceneX = Math.max(...allX);
                    const minSceneY = Math.min(...allY);
                    const maxSceneY = Math.max(...allY);
                    const centerX = Math.round((minSceneX + maxSceneX) / 2);
                    const centerY = Math.round((minSceneY + maxSceneY) / 2);
                    const viewRadius = 5;

                    // 设置键盘导航的视野中心
                    viewCenter.x = centerX;
                    viewCenter.y = centerY;

                    minXInput.value = centerX - viewRadius;
                    maxXInput.value = centerX + (viewRadius * 2);
                    minYInput.value = centerY - (viewRadius * 2);
                    maxYInput.value = centerY + viewRadius;
                } else {
                    // 如果没有场景，设置视野中心为原点
                    viewCenter.x = 0;
                    viewCenter.y = 0;
                }
            }
            
            if(isFirstLoad) {
                const monsterResponse = await fetch('api_monsters.php?action=get_all_simple');
                const monsterData = await monsterResponse.json();
                if (!monsterData.success) throw new Error(monsterData.message);
                allMonsters = monsterData.monsters;
                
                // 加载建筑列表
                const buildingResponse = await fetch('api_buildings.php?action=get_for_list');
                const buildingData = await buildingResponse.json();
                if (!buildingData.success) throw new Error(buildingData.message);
                allBuildings = buildingData.data;
                
                console.log('加载的建筑数据:', allBuildings); // 添加调试信息
                
                // Populate monster selects
                const optionsHtml = allMonsters.map(m => `<option value="${m.id}">${m.name}</option>`).join('');
                monsterSelect.innerHTML = optionsHtml;
                batchMonsterSelect.innerHTML = optionsHtml;
                batchCreateMonsterSelect.innerHTML = optionsHtml;
                
                // 填充建筑选择器
                const buildingOptionsHtml = allBuildings.map(b => `<option value="${b.id}">${b.name} (${b.type})</option>`).join('');
                buildingSelect.innerHTML = buildingOptionsHtml;
                batchBuildingSelect.innerHTML = buildingOptionsHtml;
                batchCreateBuildingSelect.innerHTML = buildingOptionsHtml;
            }
            
            await drawMap();
            renderLayers();

            // 初始化为键盘导航模式
            keyboardNavigationEnabled = true;
            updateNavigationUI();

            console.log('地图绘制完成');

        } catch (error) {
            console.error('Initialization failed:', error);
            showToast(error.message, true);
        }
    };


    // 添加全局提示框事件处理
    document.addEventListener('mouseover', function(e) {
        const target = e.target;
        
        // 检查是否是带有data-tooltip属性的元素
        if (target.hasAttribute('data-tooltip') && 
            (target.classList.contains('npc-avatar') || 
             target.classList.contains('teleport-source-marker'))) {
            
            // 获取提示文本
            const tooltipText = target.getAttribute('data-tooltip');
            
            // 设置提示框内容
            globalTooltip.textContent = tooltipText;
            
            // 显示提示框
            globalTooltip.style.display = 'block';
            
            // 更新提示框位置
            updateTooltipPosition(e);
        }
        // 特殊处理传送目标标记
        else if (target.classList.contains('teleport-destination-marker') && 
                 target.hasAttribute('data-tooltip-content')) {
            
            // 获取提示文本
            const tooltipText = target.getAttribute('data-tooltip-content');
            
            // 设置提示框内容
            globalTooltip.textContent = tooltipText;
            
            // 显示提示框
            globalTooltip.style.display = 'block';
            
            // 更新提示框位置
            updateTooltipPosition(e);
        }
    });
    
    document.addEventListener('mouseout', function(e) {
        const target = e.target;
        
        // 检查是否是带有data-tooltip属性的元素或传送目标标记
        if ((target.hasAttribute('data-tooltip') && 
             (target.classList.contains('npc-avatar') || 
              target.classList.contains('teleport-source-marker'))) ||
            (target.classList.contains('teleport-destination-marker') && 
             target.hasAttribute('data-tooltip-content'))) {
            
            // 隐藏提示框
            globalTooltip.style.display = 'none';
        }
    });
    
    document.addEventListener('mousemove', function(e) {
        // 如果提示框可见，更新其位置
        if (globalTooltip.style.display === 'block') {
            updateTooltipPosition(e);
        }
    });
    
    // 更新提示框位置
    function updateTooltipPosition(e) {
        // 获取鼠标位置
        const x = e.clientX;
        const y = e.clientY;
        
        // 设置提示框位置，在鼠标上方显示
        globalTooltip.style.left = (x + 10) + 'px';
        globalTooltip.style.top = (y - 10 - globalTooltip.offsetHeight) + 'px';
    }

    const teleporterModal = document.getElementById('teleporter-config-modal');
    if (teleporterModal) {
        const closeBtn = teleporterModal.querySelector('.close-button');
        closeBtn.addEventListener('click', () => teleporterModal.style.display = 'none');
        teleporterModal.addEventListener('click', e => {
            if (e.target === teleporterModal) teleporterModal.style.display = 'none';
        });
    }

    document.getElementById('add-destination-btn').addEventListener('click', async () => {
        const sceneBuildingId = document.getElementById('teleporter-config-scene-building-id').value;
        const destinationSceneId = document.getElementById('destination-scene-select').value;
        
        if (!destinationSceneId) {
            showToast('请选择一个目标场景。', true);
            return;
        }

        try {
            const response = await fetch('api_teleporter.php?action=add_destination', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ 
                    scene_building_id: sceneBuildingId, 
                    destination_scene_id: destinationSceneId 
                })
            });
            const result = await response.json();
            if (!result.success) throw new Error(result.message);
            
            showToast('目标添加成功');
            
            const updatedResponse = await fetch(`api_teleporter.php?action=get_destinations&scene_building_id=${sceneBuildingId}`);
            const updatedData = await updatedResponse.json();
            if (updatedData.success) {
                renderTeleporterDestinations(updatedData.data);
            }
        } catch (err) {
            showToast(`添加失败: ${err.message}`, true);
        }
    });

    // ### Item Cost Config Modal Logic ###

    // 物品成本配置模态框事件监听器
    const itemCostModal = document.getElementById('item-cost-modal');
    if (itemCostModal) {
        const closeBtn = itemCostModal.querySelector('.close-button');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                itemCostModal.style.display = 'none';
                document.getElementById('item-cost-form').reset();
            });
        }

        itemCostModal.addEventListener('click', e => {
            if (e.target === itemCostModal) {
                itemCostModal.style.display = 'none';
                document.getElementById('item-cost-form').reset();
            }
        });

        // ESC键关闭模态框
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && itemCostModal.style.display === 'block') {
                itemCostModal.style.display = 'none';
                document.getElementById('item-cost-form').reset();
            }
        });
    }

    // 物品成本配置表单提交
    document.getElementById('item-cost-form').addEventListener('submit', async (e) => {
        e.preventDefault();

        const destinationId = document.getElementById('item-cost-destination-id').value;
        const requiredItemId = document.getElementById('required-item-select').value || null;
        const requiredQuantity = parseInt(document.getElementById('required-quantity').value) || 1;

        try {
            const response = await fetch('api_teleporter.php?action=update_destination', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    destination_id: destinationId,
                    required_item_id: requiredItemId,
                    required_quantity: requiredQuantity
                })
            });

            const result = await response.json();
            if (!result.success) throw new Error(result.message);

            showToast('消耗物品配置已更新');
            itemCostModal.style.display = 'none';

            // 重置表单
            document.getElementById('item-cost-form').reset();

            // 刷新传送点目标列表
            const sceneBuildingId = document.getElementById('teleporter-config-scene-building-id').value;
            if (sceneBuildingId) {
                const updatedResponse = await fetch(`api_teleporter.php?action=get_destinations&scene_building_id=${sceneBuildingId}`);
                const updatedData = await updatedResponse.json();
                if (updatedData.success) {
                    renderTeleporterDestinations(updatedData.data);
                }
            }
        } catch (err) {
            showToast(`配置失败: ${err.message}`, true);
        }
    });

    // ### Shop Config Modal Logic ###

    let allItems = []; // 缓存所有物品模板
    const categoryMap = {
        'material': '材料',
        'equipment': '装备',
        'consumable': '消耗品',
        'gem': '宝石',
        'quest': '任务物品',
        'key': '钥匙',
        'blueprint': '蓝图',
        // 可以根据需要添加更多映射
    };

    async function openShopConfigModal(sceneBuildingId, sceneName, buildingName) {
        if (allItems.length === 0) {
            try {
                const response = await fetch('api_item_templates.php?action=get_all_simple');
                const data = await response.json();
                if (!data.success) throw new Error(data.message);
                allItems = data.items;
            } catch (error) {
                showToast('加载物品列表失败: ' + error.message, true);
                return;
            }
        }

        const modal = document.getElementById('shop-config-modal');
        const itemSelect = document.getElementById('shop-item-select');
        
        // --- Category Filter Logic ---
        const categoryFilterContainerId = 'shop-item-category-filter';
        let categoryFilterContainer = document.getElementById(categoryFilterContainerId);
        if (!categoryFilterContainer) {
            categoryFilterContainer = document.createElement('div');
            categoryFilterContainer.id = categoryFilterContainerId;
            categoryFilterContainer.className = 'category-filters';
            
            // 为了确保分类筛选器位于表单顶部单独一行
            const formGroup = modal.querySelector('.form-group');
            if (formGroup) {
                // 在表单的第一个form-group之前插入
                formGroup.parentNode.insertBefore(categoryFilterContainer, formGroup);
            }
        }
        
        // 添加标题
        const filterTitle = document.createElement('h4');
        filterTitle.textContent = '按物品分类筛选:';
        filterTitle.style.marginBottom = '10px';
        categoryFilterContainer.innerHTML = '';
        categoryFilterContainer.appendChild(filterTitle);
        
        const categories = ['全部', ...new Set(allItems.map(item => item.category).filter(Boolean))];
        let currentCategory = '全部';

        const populateItemSelect = (category) => {
            const filteredItems = category === '全部' 
                ? allItems 
                : allItems.filter(item => item.category === category);
            
            if ($(itemSelect).data('select2')) {
                $(itemSelect).select2('destroy');
            }

            itemSelect.innerHTML = '<option value=""></option>' + filteredItems.map(item => `<option value="${item.id}">${item.name}</option>`).join('');
            
            $(itemSelect).select2({
                placeholder: '输入名称搜索物品...',
                allowClear: true,
                dropdownParent: modal
            });
        };

        categories.forEach(cat => {
            const btn = document.createElement('button');
            btn.type = 'button';
            // 使用映射转换分类名称，如果找不到则使用原名称
            const displayName = categoryMap[cat.toLowerCase()] || cat;
            btn.textContent = displayName;
            btn.className = 'btn btn-sm btn-secondary category-filter-btn';
            if (cat === currentCategory) {
                btn.classList.replace('btn-secondary', 'btn-primary');
            }
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                currentCategory = cat;
                categoryFilterContainer.querySelectorAll('button').forEach(b => b.classList.replace('btn-primary', 'btn-secondary'));
                btn.classList.replace('btn-secondary', 'btn-primary');
                populateItemSelect(currentCategory);
            });
            categoryFilterContainer.appendChild(btn);
        });

        // Initial population of dropdown
        populateItemSelect(currentCategory);
        
        // --- End Category Filter Logic ---

        document.getElementById('shop-config-scene-name').textContent = sceneName;
        document.getElementById('shop-config-building-name').textContent = buildingName;
        document.getElementById('shop-config-scene-building-id').value = sceneBuildingId;

        const itemListDiv = document.getElementById('shop-item-list');
        itemListDiv.innerHTML = '<p class="loading-text">正在加载商品...</p>';
        modal.style.display = 'block';

        try {
            const response = await fetch(`api_shop.php?action=get_shop_items&scene_building_id=${sceneBuildingId}`);
            const data = await response.json();
            if (!data.success) throw new Error(data.message);
            renderShopItems(data.data);
        } catch (error) {
            itemListDiv.innerHTML = `<p class="error-text">加载商品失败: ${error.message}</p>`;
        }
    }

    function renderShopItems(items) {
        const itemListDiv = document.getElementById('shop-item-list');
        if (items.length === 0) {
            itemListDiv.innerHTML = '<p>这个商店还没有商品。</p>';
            return;
        }

        itemListDiv.innerHTML = items.map(item => `
            <div class="shop-item-row" data-shop-item-id="${item.shop_item_id}">
                <div class="item-name">${item.item_name} (ID: ${item.item_template_id})</div>
                <div class="item-details">
                    <span>库存: ${item.stock ?? '无限'}</span>
                    <span>排序: ${item.sort_order}</span>
                </div>
                <button class="btn btn-sm btn-danger" onclick="removeShopItem(${item.shop_item_id})">移除</button>
            </div>
        `).join('');
    }

    // 移除商品
    async function removeShopItem(shopItemId) {
        if (!confirm('确定要移除这个商品吗？')) return;

        const formData = new FormData();
        formData.append('action', 'remove_item');
        formData.append('shop_item_id', shopItemId);

        try {
            const response = await fetch('api_shop.php', { method: 'POST', body: formData });
            const result = await response.json();

            if (result.success) {
                showToast(result.message, 'success');
                // 从DOM中直接移除元素，无需重新加载
                const itemRow = document.querySelector(`.shop-item-row[data-shop-item-id="${shopItemId}"]`);
                if (itemRow) {
                    itemRow.remove();
                }
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            showToast('移除商品失败: ' + error.message, true);
        }
    }

    // 在页面加载时，为shop-config-modal的关闭按钮和事件绑定
    const shopModal = document.getElementById('shop-config-modal');
    if (shopModal) {
        shopModal.querySelector('.close-button').addEventListener('click', () => {
            shopModal.style.display = 'none';
        });
        shopModal.addEventListener('click', (e) => {
            if (e.target === shopModal) {
                shopModal.style.display = 'none';
            }
        });

        // 添加商品按钮事件
        document.getElementById('shop-add-item-btn').addEventListener('click', async () => {
            const sceneBuildingId = document.getElementById('shop-config-scene-building-id').value;
            const itemId = document.getElementById('shop-item-select').value;
            const stock = document.getElementById('shop-item-stock').value;
            const sortOrder = document.getElementById('shop-item-sort-order').value;

            if (!itemId) {
                showToast('请选择一个物品', true);
                return;
            }

            const formData = new FormData();
            formData.append('action', 'add_item');
            formData.append('scene_building_id', sceneBuildingId);
            formData.append('item_template_id', itemId);
            formData.append('stock', stock);
            formData.append('sort_order', sortOrder);

            try {
                const response = await fetch('api_shop.php', { method: 'POST', body: formData });
                const result = await response.json();

                if (result.success) {
                    showToast(result.message, 'success');
                    // 刷新列表以显示新商品
                    const sceneName = document.getElementById('shop-config-scene-name').textContent;
                    const buildingName = document.getElementById('shop-config-building-name').textContent;
                    await openShopConfigModal(sceneBuildingId, sceneName, buildingName);
                    
                    // 清空输入以便连续添加
                    $('#shop-item-select').val(null).trigger('change');
                    document.getElementById('shop-item-stock').value = '';

                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                showToast('添加商品失败: ' + error.message, true);
            }
        });
    }

    // Need to hide batch actions panel using its function to also clear selections
    // this is a global function now because it is on the element itself
    window.hideBatchActions = hideBatchActions; 
    window.removeShopItem = removeShopItem; // 将函数暴露到全局作用域

    // 初始化应用
    initializeApp();
}); 