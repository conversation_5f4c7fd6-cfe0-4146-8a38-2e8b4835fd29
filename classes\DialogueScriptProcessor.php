<?php
// classes/DialogueScriptProcessor.php

require_once __DIR__ . '/QuestManager.php';

class DialogueScriptProcessor {
    private Database $db;
    private QuestManager $questManager;
    private array $playerCache = [];

    // 新增：允许在脚本中使用的玩家属性白名单
    private array $allowedAttributes = [
        'level', 'experience', 'hp', 'max_hp', 'mp', 'max_mp', 'attack', 'defense',
        'strength', 'agility', 'constitution', 'intelligence', 'potential_points',
        'knowledge_points', 'gold', 'diamonds', 'karma', 'rage', 'current_job_id'
    ];

    public function __construct(Database $db) {
        $this->db = $db;
        $this->questManager = new QuestManager(function() use ($db) {
            return $db->getConnection();
        });
    }

    /**
     * @param string $json
     * @param int $playerId
     * @return bool
     */
    public function evaluateConditions(string $json, int $playerId): bool {
        if (empty($json)) return true;

        $data = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !isset($data['conditions']) || !is_array($data['conditions'])) {
            // error_log("Invalid JSON or structure in condition script for player $playerId: $json");
            return false;
        }

        if (empty($data['conditions'])) {
            return true;
        }

        $logicalOperator = $data['logical_operator'] ?? 'AND';
        
        foreach ($data['conditions'] as $condition) {
            $result = $this->checkCondition($condition, $playerId);
            error_log("[对话树选择] 单个条件评估: " . json_encode($condition) . " 结果: " . ($result ? '通过' : '不通过'));
            if ($logicalOperator === 'AND' && !$result) {
                error_log("[对话树选择] AND逻辑下条件不通过，整体评估失败");
                return false;
            }
            if ($logicalOperator === 'OR' && $result) {
                error_log("[对话树选择] OR逻辑下条件通过，整体评估成功");
                return true;
            }
        }

        // 如果是AND逻辑，所有条件都通过了，返回true
        // 如果是OR逻辑，没有任何条件通过，返回false
        $finalResult = $logicalOperator === 'AND';
        error_log("[对话树选择] 所有条件评估完成，逻辑操作符=$logicalOperator，最终结果=" . ($finalResult ? '通过' : '不通过'));
        return $finalResult;
    }

    /**
     * @param array $condition
     * @param int $playerId
     * @return bool
     */
    private function checkCondition(array $condition, int $playerId): bool {
        $type = $condition['type'] ?? '';
        switch ($type) {
            case 'player_attribute':
                // error_log("checkPlayerAttribute");
                return $this->checkPlayerAttribute($playerId, $condition);
            case 'has_item':
                // error_log("checkPlayerItem");
                return $this->checkPlayerItem($playerId, $condition, true);
            case 'does_not_have_item':
                // 对于does_not_have_item，我们需要检查玩家是否真的没有该物品
                // 如果条件中指定了数量，则检查数量；否则只检查是否存在
                $quantityValue = (int)($condition['quantity'] ?? 0);
                $hasQuantityCheck = isset($condition['quantity']) && $quantityValue > 0;
                $hasItem = $this->checkPlayerItem($playerId, $condition, $hasQuantityCheck);
                $result = !$hasItem;
                error_log("[对话树选择] does_not_have_item检查: 物品ID={$condition['item_id']}, 数量=$quantityValue, hasQuantityCheck=" . ($hasQuantityCheck ? 'true' : 'false') . ", hasItem=" . ($hasItem ? 'true' : 'false') . ", 最终结果=" . ($result ? 'true' : 'false'));
                return $result;
            case 'quest_status':
                // error_log("checkQuestStatus");
                return $this->checkQuestStatus($playerId, $condition);
            default:
                // error_log("Unknown condition type: $type");
                return false;
        }
    }

    /**
     * @param string $json
     * @param int $playerId
     * @return array
     */
    public function executeActions(string $json, int $playerId): array {
        $results = [];
        if (empty($json)) return $results;

        $data = json_decode($json, true);
        if (json_last_error() !== JSON_ERROR_NONE || !isset($data['actions']) || !is_array($data['actions'])) {
            // error_log("Invalid JSON or structure in action script for player $playerId: $json");
            return $results;
        }

        foreach ($data['actions'] as $action) {
            $results[] = $this->executeAction($action, $playerId);
        }
        
        // Clear player cache after actions
        unset($this->playerCache[$playerId]);

        return array_filter($results);
    }

    /**
     * @param array $action
     * @param int $playerId
     * @return array|null
     */
    private function executeAction(array $action, int $playerId): ?array {
        $type = $action['type'] ?? '';
        switch ($type) {
            case 'give_item':
                return $this->manageItem($playerId, $action, 'give');
            case 'remove_item':
                return $this->manageItem($playerId, $action, 'remove');
            case 'give_gold':
                return $this->manageGold($playerId, $action, 'give');
            case 'remove_gold':
                 return $this->manageGold($playerId, $action, 'remove');
            case 'start_quest':
                return $this->manageQuest($playerId, $action, 'start');
            case 'update_quest_progress':
                return $this->manageQuestProgress($playerId, $action);
            default:
                // error_log("Unknown action type: $type");
                return null;
        }
    }
    
    // --- Condition Helper Methods ---

    private function getPlayerAttribute(int $playerId, string $attributeKey) {
        // 检查缓存是否存在或属性缓存是否为空
        if (!isset($this->playerCache[$playerId]) || empty($this->playerCache[$playerId]['attributes'])) {
            error_log("[对话树选择] getPlayerAttribute: 缓存不存在或属性为空，加载玩家 $playerId 的数据");
            $this->loadPlayerCache($playerId);
        }

        // 增加白名单检查
        if (!in_array($attributeKey, $this->allowedAttributes)) {
            error_log("[对话树选择] getPlayerAttribute: 属性 $attributeKey 不在白名单内");
            return null;
        }

        $value = $this->playerCache[$playerId]['attributes'][$attributeKey] ?? null;
        error_log("[对话树选择] getPlayerAttribute: 玩家 $playerId 的属性 $attributeKey = " . ($value ?? 'null') . "，缓存内容: " . json_encode($this->playerCache[$playerId]['attributes']));
        return $value;
    }
    
    private function loadPlayerCache(int $playerId) {
        $this->playerCache[$playerId] = ['attributes' => [], 'inventory' => []];

        // 【重构】从宽表获取所有属性
        error_log("[对话树选择] loadPlayerCache: 开始加载玩家 $playerId 的属性数据");

        try {
            // 确保playerId是整数类型
            $accountId = (int)$playerId;
            error_log("[对话树选择] loadPlayerCache: 查询参数 account_id = $accountId (原始值: $playerId)");

            $stmt = $this->db->query("SELECT * FROM player_attributes WHERE account_id = ?", [$accountId]);
            $attributes = $stmt->fetch(PDO::FETCH_ASSOC);

            if($attributes) {
                $this->playerCache[$playerId]['attributes'] = $attributes;
                error_log("[对话树选择] loadPlayerCache: 成功加载属性数据，level=" . ($attributes['level'] ?? 'null') . "，记录ID=" . ($attributes['id'] ?? 'null'));
            } else {
                error_log("[对话树选择] loadPlayerCache: 未找到玩家 $playerId 的属性数据");

                // 检查是否有任何player_attributes记录
                $countStmt = $this->db->query("SELECT COUNT(*) FROM player_attributes");
                $totalCount = $countStmt->fetchColumn();
                error_log("[对话树选择] loadPlayerCache: player_attributes表总记录数: $totalCount");

                // 检查是否有account_id为该值的记录
                $checkStmt = $this->db->query("SELECT account_id, level FROM player_attributes WHERE account_id = ?", [$playerId]);
                $checkResult = $checkStmt->fetch(PDO::FETCH_ASSOC);
                error_log("[对话树选择] loadPlayerCache: 直接查询结果: " . json_encode($checkResult));

                // 列出前几条记录看看数据格式
                $sampleStmt = $this->db->query("SELECT account_id, level FROM player_attributes LIMIT 3");
                $sampleData = $sampleStmt->fetchAll(PDO::FETCH_ASSOC);
                error_log("[对话树选择] loadPlayerCache: 样本数据: " . json_encode($sampleData));
            }
        } catch (Exception $e) {
            error_log("[对话树选择] loadPlayerCache: 数据库查询异常: " . $e->getMessage());
        }

        // 加载物品缓存
        $this->loadPlayerInventoryCache($playerId);
    }

    /**
     * 【新增】专门重新加载玩家物品缓存的方法
     * 解决物品丢弃、使用等操作后缓存不同步的问题
     */
    private function loadPlayerInventoryCache(int $playerId) {
        // 确保玩家缓存结构存在，但不覆盖已有的attributes
        if (!isset($this->playerCache[$playerId])) {
            $this->playerCache[$playerId] = ['attributes' => [], 'inventory' => []];
        } elseif (!isset($this->playerCache[$playerId]['inventory'])) {
            $this->playerCache[$playerId]['inventory'] = [];
        }

        // 【修正】使用SUM来统计同一物品模板的总数量，解决多行记录问题
        $stmt = $this->db->query("
            SELECT item_template_id, SUM(quantity) as total_quantity
            FROM player_inventory
            WHERE player_id = ?
            GROUP BY item_template_id
        ", [$playerId]);
        $inventory = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

        // 重置物品缓存
        $this->playerCache[$playerId]['inventory'] = $inventory ?: [];
    }

    private function checkPlayerAttribute(int $playerId, array $condition): bool {
        $attribute = $condition['attribute'] ?? '';
        $operator = $condition['operator'] ?? '==';
        $value = $condition['value'] ?? '';

        // 验证属性是否在白名单内
        if (!in_array($attribute, $this->allowedAttributes)) {
            error_log("[对话树选择] checkPlayerAttribute: 属性不在白名单内: $attribute");
            return false;
        }

        $playerValue = $this->getPlayerAttribute($playerId, $attribute);
        if ($playerValue === null) {
            error_log("[对话树选择] checkPlayerAttribute: 无法获取玩家属性值: $attribute");
            return false;
        }

        $result = false;
        switch ($operator) {
            case '==': $result = $playerValue == $value; break;
            case '!=': $result = $playerValue != $value; break;
            case '>': $result = $playerValue > $value; break;
            case '>=': $result = $playerValue >= $value; break;
            case '<': $result = $playerValue < $value; break;
            case '<=': $result = $playerValue <= $value; break;
            default:
                error_log("[对话树选择] checkPlayerAttribute: 未知操作符: $operator");
                return false;
        }

        error_log("[对话树选择] checkPlayerAttribute: 属性=$attribute, 玩家值=$playerValue, 操作符=$operator, 比较值=$value, 结果=" . ($result ? 'true' : 'false'));
        return $result;
    }

    private function checkPlayerItem(int $playerId, array $condition, bool $checkQuantity): bool {
        // 【修正】使用 item_template_id
        $itemTemplateId = (int)($condition['item_id'] ?? 0);
        if (!$itemTemplateId) {
            error_log("[对话树选择] checkPlayerItem: 物品ID无效");
            return false;
        }

        // 【修复缓存同步问题】每次检查物品时都重新加载物品缓存
        // 这确保了在物品丢弃、使用等操作后能获取到最新的物品状态
        $this->loadPlayerInventoryCache($playerId);

        $inventory = $this->playerCache[$playerId]['inventory'];
        $hasItem = isset($inventory[$itemTemplateId]);
        $currentQuantity = $hasItem ? $inventory[$itemTemplateId] : 0;

        error_log("[对话树选择] checkPlayerItem: 物品ID=$itemTemplateId, checkQuantity=" . ($checkQuantity ? 'true' : 'false') . ", hasItem=" . ($hasItem ? 'true' : 'false') . ", currentQuantity=$currentQuantity");

        if (!$checkQuantity) {
            error_log("[对话树选择] checkPlayerItem: 不检查数量，返回hasItem=" . ($hasItem ? 'true' : 'false'));
            return $hasItem;
        }

        if (!$hasItem) {
            error_log("[对话树选择] checkPlayerItem: 没有该物品，返回false");
            return false;
        }

        $quantity = (int)($condition['quantity'] ?? 1);
        $result = $inventory[$itemTemplateId] >= $quantity;
        error_log("[对话树选择] checkPlayerItem: 需要数量=$quantity, 实际数量=$currentQuantity, 结果=" . ($result ? 'true' : 'false'));
        return $result;
    }

    private function checkQuestStatus(int $playerId, array $condition): bool {
        $questId = (int)($condition['quest_id'] ?? 0);
        $status = $condition['status'] ?? '';
        if (!$questId || !$status) return false;

        // error_log("[任务状态检查] 玩家 {$playerId}, 任务ID {$questId}, 预期状态: '{$status}', 条件数据: " . json_encode($condition));
        
        $questData = $this->questManager->getQuestStatus($playerId, $questId);
        // error_log("[任务状态检查] getQuestStatus返回结果: " . json_encode($questData));
        
        // 如果检查"未开始"状态，且未找到任务数据，则条件成立
        if ($status === 'not_started') {
            $result = $questData === null;
            // error_log("[任务状态检查] 检查'未开始'状态, 结果: " . ($result ? '通过' : '不通过') . " (任务数据" . ($questData === null ? '不存在' : '存在') . ")");
            return $result;
        }
        
        // 确保任务数据存在且属于正确的任务
        if (!$questData) {
            // error_log("[任务状态检查] 任务数据不存在，检查失败");
            return false;
        }
        
        // 添加任务ID验证（如果questData中包含quest_id字段）
        if (isset($questData['quest_id']) && $questData['quest_id'] != $questId) {
            // error_log("[任务状态检查] 任务ID不匹配, 预期: {$questId}, 实际: {$questData['quest_id']}, 检查失败");
            return false;
        }
        
        // 检查任务状态
        if ($status === 'active') {
            $result = $questData['status'] === 'active';
            // error_log("[任务状态检查] 检查'进行中'状态, 预期: active, 实际: {$questData['status']}, 结果: " . ($result ? '通过' : '不通过'));
            return $result;
        }
        if ($status === 'completed') {
            $result = $questData['status'] === 'completed';
            // error_log("[任务状态检查] 检查'已完成'状态, 预期: completed, 实际: {$questData['status']}, 结果: " . ($result ? '通过' : '不通过'));
            return $result;
        }
        
        // error_log("[任务状态检查] 未知的状态值: {$status}, 检查失败");
        return false;
    }
    
    // --- Action Helper Methods ---
    
    private function manageItem(int $playerId, array $action, string $mode): ?array {
        // 【修正】使用 item_template_id
        $itemTemplateId = (int)($action['item_id'] ?? 0);
        $quantity = (int)($action['quantity'] ?? 1);
        if (!$itemTemplateId || $quantity <= 0) return null;

        $conn = $this->db->getConnection();
        $conn->beginTransaction();
        try {
            // 获取物品模板信息，包括堆叠属性
            $itemStmt = $this->db->query("SELECT name, stackable, max_stack FROM item_templates WHERE id = ?", [$itemTemplateId]);
            $itemTemplate = $itemStmt->fetch(PDO::FETCH_ASSOC);

            if (!$itemTemplate) {
                $conn->rollBack();
                return ['type' => 'error', 'message' => '物品模板不存在'];
            }

            $itemName = $itemTemplate['name'];
            $isStackable = (bool)$itemTemplate['stackable'];
            $maxStack = (int)$itemTemplate['max_stack'];

            if ($mode === 'remove') {
                $result = $this->removeItemFromInventory($playerId, $itemTemplateId, $quantity, $isStackable);
                if (!$result['success']) {
                    $conn->rollBack();
                    return ['type' => 'error', 'message' => $result['message']];
                }
            } else { // give
                $result = $this->addItemToInventory($playerId, $itemTemplateId, $quantity, $isStackable, $maxStack);
                if (!$result['success']) {
                    $conn->rollBack();
                    return ['type' => 'error', 'message' => $result['message']];
                }
            }

            $conn->commit();

            // 更新缓存
            if (isset($this->playerCache[$playerId])) {
                unset($this->playerCache[$playerId]['inventory']); // 清除缓存，强制重新加载
            }

            // 检查任务进度更新（仅在给予物品时检查）
            if ($mode === 'give') {
                $this->checkQuestProgressForItem($playerId, $itemTemplateId, $quantity);
            }

            $message = ($mode === 'give' ? '获得' : '失去') . " $quantity x $itemName";
            return ['type' => 'info', 'message' => $message, 'item_id' => $itemTemplateId, 'quantity_change' => ($mode === 'give' ? $quantity : -$quantity)];

        } catch (PDOException $e) {
            $conn->rollBack();
            // error_log("Failed to $mode item: " . $e->getMessage());
            return ['type' => 'error', 'message' => "Database error while managing item."];
        }
    }
    
    private function manageGold(int $playerId, array $action, string $mode): ?array {
        $amount = (int)($action['amount'] ?? 0);
        if ($amount <= 0) return null;

        $currentGold = (int)($this->getPlayerAttribute($playerId, 'gold') ?? 0);
        
        if ($mode === 'remove' && $currentGold < $amount) {
            return ['type' => 'error', 'message' => 'Insufficient gold.'];
        }

        $newGold = ($mode === 'give') ? $currentGold + $amount : $currentGold - $amount;
        
        // 【重构】使用UPDATE语句
        $stmt = $this->db->query(
            "UPDATE player_attributes SET gold = ? WHERE account_id = ?",
            [$newGold, $playerId]
        );

        if (isset($this->playerCache[$playerId])) {
            $this->playerCache[$playerId]['attributes']['gold'] = $newGold;
        }

        $message = ($mode === 'give' ? '获得' : '失去') . " $amount Gold";
        return ['type' => 'info', 'message' => $message, 'new_gold' => $newGold];
    }
    
    private function manageQuest(int $playerId, array $action, string $mode): ?array {
        $questId = (int)($action['quest_id'] ?? 0);
        if (!$questId) return null;

        if ($mode === 'start') {
            $result = $this->questManager->acceptQuest($playerId, $questId);
            if($result['success']){
                 $questName = $this->getQuestName($questId);
                 return ['type' => 'quest_started', 'message' => $result['message'], 'quest_id' => $questId];
            }
        }
        // Other quest actions like 'complete' or 'fail' can be added here
        return null;
    }
    
    private function getItemName(int $itemId): string {
        $stmt = $this->db->query("SELECT name FROM item_templates WHERE id = ?", [$itemId]);
        return $stmt->fetchColumn() ?: "Unknown Item";
    }

    private function getQuestName(int $questId): string {
        $stmt = $this->db->query("SELECT title FROM quests WHERE id = ?", [$questId]);
        return $stmt->fetchColumn() ?: "Unknown Quest";
    }

    private function manageQuestProgress(int $playerId, array $action): ?array {
        $questId = (int)($action['quest_id'] ?? 0);
        $objectiveId = (int)($action['objective_id'] ?? 0);
        $increment = (int)($action['increment'] ?? 1);
        
        if (!$questId || !$objectiveId) {
            return ['type' => 'error', 'message' => '任务ID或目标ID无效'];
        }
        
        // 调用QuestManager更新进度
        $result = $this->questManager->updateQuestProgress($playerId, $questId, $objectiveId, $increment);
        
        if (!$result['success']) {
            return ['type' => 'quest_info', 'message' => $result['message']];
        }
        
        $questName = $this->getQuestName($questId);
        $message = "任务「{$questName}」进度已更新";

        // 获取任务的接收NPC名称，用于完成提示
        $receiverNpcName = null;
        if ($result['can_complete'] ?? false) {
            try {
                $stmt = $this->db->query("
                    SELECT nt.name
                    FROM quests q
                    LEFT JOIN npc_templates nt ON q.receiver_npc_id = nt.id
                    WHERE q.id = ?
                ", [$questId]);
                $npcData = $stmt->fetch(PDO::FETCH_ASSOC);
                $receiverNpcName = $npcData['name'] ?? null;
            } catch (Exception $e) {
                // error_log("获取任务接收NPC名称失败: " . $e->getMessage());
            }
        }

        return [
            'type' => 'quest_progress',
            'message' => $message,
            'quest_id' => $questId,
            'objective_id' => $objectiveId,
            'new_progress' => $result['new_progress'],
            'target' => $result['target'],
            'can_complete' => $result['can_complete'] ?? false,
            'receiver_npc_name' => $receiverNpcName
        ];
    }

    /**
     * 从背包中移除物品（支持堆叠）
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 移除数量
     * @param bool $isStackable 是否可堆叠
     * @return array 操作结果
     */
    private function removeItemFromInventory(int $playerId, int $itemTemplateId, int $quantity, bool $isStackable): array {
        if ($isStackable) {
            // 可堆叠物品：获取所有相同物品的堆叠
            $stmt = $this->db->query("
                SELECT id, quantity FROM player_inventory
                WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0
                ORDER BY quantity ASC
            ", [$playerId, $itemTemplateId]);
            $inventoryItems = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $totalAvailable = array_sum(array_column($inventoryItems, 'quantity'));
            if ($totalAvailable < $quantity) {
                return ['success' => false, 'message' => '物品数量不足'];
            }

            $remainingToRemove = $quantity;
            foreach ($inventoryItems as $item) {
                if ($remainingToRemove <= 0) break;

                $itemId = $item['id'];
                $itemQuantity = (int)$item['quantity'];
                $toRemove = min($remainingToRemove, $itemQuantity);

                if ($toRemove >= $itemQuantity) {
                    // 删除整个堆叠
                    $stmt = $this->db->query("DELETE FROM player_inventory WHERE id = ?", [$itemId]);
                } else {
                    // 减少数量
                    $stmt = $this->db->query("UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?", [$toRemove, $itemId]);
                }

                $remainingToRemove -= $toRemove;
            }
        } else {
            // 不可堆叠物品：删除指定数量的条目
            $stmt = $this->db->query("
                SELECT id FROM player_inventory
                WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0
                LIMIT ?
            ", [$playerId, $itemTemplateId, $quantity]);
            $itemIds = $stmt->fetchAll(PDO::FETCH_COLUMN);

            if (count($itemIds) < $quantity) {
                return ['success' => false, 'message' => '物品数量不足'];
            }

            foreach ($itemIds as $itemId) {
                $stmt = $this->db->query("DELETE FROM player_inventory WHERE id = ?", [$itemId]);
            }
        }

        return ['success' => true];
    }

    /**
     * 向背包中添加物品（支持堆叠）
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 添加数量
     * @param bool $isStackable 是否可堆叠
     * @param int $maxStack 最大堆叠数量
     * @return array 操作结果
     */
    private function addItemToInventory(int $playerId, int $itemTemplateId, int $quantity, bool $isStackable, int $maxStack): array {
        if ($isStackable) {
            $remainingQuantity = $quantity;

            // 查找现有的未满堆叠
            $stmt = $this->db->query("
                SELECT id, quantity FROM player_inventory
                WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND quantity < ?
                ORDER BY quantity DESC
            ", [$playerId, $itemTemplateId, $maxStack]);
            $existingStacks = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 先填满现有堆叠
            foreach ($existingStacks as $stack) {
                if ($remainingQuantity <= 0) break;

                $spaceAvailable = $maxStack - $stack['quantity'];
                $amountToAdd = min($remainingQuantity, $spaceAvailable);

                $stmt = $this->db->query("UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?", [$amountToAdd, $stack['id']]);

                $remainingQuantity -= $amountToAdd;
            }

            // 创建新堆叠
            while ($remainingQuantity > 0) {
                $toAdd = min($remainingQuantity, $maxStack);
                $stmt = $this->db->query("
                    INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound)
                    VALUES (?, ?, ?, ?, 1)
                ", [$playerId, $itemTemplateId, $toAdd, null]);
                $remainingQuantity -= $toAdd;
            }
        } else {
            // 不可堆叠物品：为每个物品创建单独条目
            for ($i = 0; $i < $quantity; $i++) {
                $stmt = $this->db->query("
                    INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound)
                    VALUES (?, ?, 1, ?, 1)
                ", [$playerId, $itemTemplateId, null]);
            }
        }

        return ['success' => true];
    }

    /**
     * 检查物品相关的任务进度
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 物品数量变化
     */
    private function checkQuestProgressForItem(int $playerId, int $itemTemplateId, int $quantity): void {
        try {
            // 查找与该物品相关的收集任务目标
            $stmt = $this->db->query("
                SELECT DISTINCT pq.quest_id, qo.id as objective_id
                FROM player_quests pq
                JOIN quest_objectives qo ON pq.quest_id = qo.quest_id
                WHERE pq.player_id = ? AND pq.status = 'active'
                AND qo.type = 'collect' AND qo.target_id = ?
            ", [$playerId, $itemTemplateId]);
            $relatedObjectives = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 为每个相关目标更新进度
            foreach ($relatedObjectives as $objective) {
                $this->questManager->updateQuestProgress(
                    $playerId,
                    $objective['quest_id'],
                    $objective['objective_id'],
                    $quantity
                );
            }
        } catch (Exception $e) {
            // error_log("检查任务进度时出错: " . $e->getMessage());
        }
    }
}