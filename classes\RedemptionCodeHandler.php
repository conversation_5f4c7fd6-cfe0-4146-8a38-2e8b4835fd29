<?php
// classes/RedemptionCodeHandler.php

class RedemptionCodeHandler {
    private $db;
    private $redis;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->redis = RedisManager::getInstance();
    }
    
    /**
     * 处理兑换码兑换请求
     * @param int $playerId 玩家ID
     * @param string $code 兑换码
     * @param string $ipAddress 玩家IP地址
     * @return array 处理结果
     */
    public function redeemCode($playerId, $code, $ipAddress = null) {
        try {
            $conn = $this->db->getConnection();
            $conn->beginTransaction();
            
            // 1. 验证兑换码
            $codeInfo = $this->validateRedemptionCode($conn, $code);
            if (!$codeInfo['success']) {
                $conn->rollBack();
                return $codeInfo;
            }
            
            $redemptionCode = $codeInfo['code'];
            
            // 2. 检查玩家使用限制
            $usageCheck = $this->checkPlayerUsageLimit($conn, $redemptionCode['id'], $playerId);
            if (!$usageCheck['success']) {
                $conn->rollBack();
                return $usageCheck;
            }
            
            // 3. 检查总使用限制
            if ($redemptionCode['current_usage_count'] >= $redemptionCode['total_usage_limit']) {
                $conn->rollBack();
                return ['success' => false, 'message' => '兑换码使用次数已达上限'];
            }
            
            // 4. 获取兑换物品列表
            $items = $this->getRedemptionItems($conn, $redemptionCode['id']);
            if (empty($items)) {
                $conn->rollBack();
                return ['success' => false, 'message' => '兑换码配置错误：无可兑换物品'];
            }
            
            // 5. 发放物品
            $itemResults = [];
            foreach ($items as $item) {
                $result = $this->giveItemToPlayer(
                    $conn,
                    $playerId,
                    $item['item_template_id'],
                    $item['quantity'],
                    $item['is_bound']
                );

                if (!$result['success']) {
                    $conn->rollBack();
                    return ['success' => false, 'message' => '物品发放失败: ' . $result['message']];
                }

                $itemResults[] = [
                    'item_template_id' => $item['item_template_id'],
                    'item_name' => $item['item_name'],
                    'quantity' => $item['quantity'],
                    'is_bound' => $item['is_bound']
                ];
            }

            // 6. 记录使用记录
            $this->recordUsage($conn, $redemptionCode['id'], $playerId, $ipAddress);

            // 7. 更新兑换码使用次数
            $this->updateUsageCount($conn, $redemptionCode['id']);

            // 8. 检查任务进度（由WebSocket服务器处理）
            // 任务进度检查已移至WebSocket服务器的checkRedemptionCodeQuestProgress方法
            
            $conn->commit();
            
            return [
                'success' => true,
                'message' => '兑换成功！',
                'items' => $itemResults,
                'code_name' => $redemptionCode['name']
            ];
            
        } catch (Exception $e) {
            if ($conn->inTransaction()) {
                $conn->rollBack();
            }
            error_log("兑换码处理错误: " . $e->getMessage());
            return ['success' => false, 'message' => '系统错误，请稍后重试'];
        }
    }
    
    /**
     * 验证兑换码
     */
    private function validateRedemptionCode($conn, $code) {
        $stmt = $conn->prepare("
            SELECT * FROM redemption_codes 
            WHERE code = ? AND is_active = 1
        ");
        $stmt->execute([$code]);
        $redemptionCode = $stmt->fetch();
        
        if (!$redemptionCode) {
            return ['success' => false, 'message' => '兑换码不存在或已失效'];
        }
        
        // 检查是否过期
        if ($redemptionCode['expires_at'] && strtotime($redemptionCode['expires_at']) < time()) {
            return ['success' => false, 'message' => '兑换码已过期'];
        }
        
        return ['success' => true, 'code' => $redemptionCode];
    }
    
    /**
     * 检查玩家使用限制
     */
    private function checkPlayerUsageLimit($conn, $codeId, $playerId) {
        // 先获取兑换码的玩家使用限制
        $stmt = $conn->prepare("SELECT player_usage_limit FROM redemption_codes WHERE id = ?");
        $stmt->execute([$codeId]);
        $codeInfo = $stmt->fetch();

        if (!$codeInfo) {
            return ['success' => false, 'message' => '兑换码不存在'];
        }

        // 再查询该玩家对此兑换码的使用次数
        $stmt = $conn->prepare("
            SELECT COUNT(*) as usage_count
            FROM redemption_code_usage
            WHERE redemption_code_id = ? AND player_id = ?
        ");
        $stmt->execute([$codeId, $playerId]);
        $usageCount = $stmt->fetchColumn();

        if ($usageCount >= $codeInfo['player_usage_limit']) {
            return ['success' => false, 'message' => '您已达到该兑换码的使用次数上限'];
        }

        return ['success' => true];
    }
    
    /**
     * 获取兑换物品列表
     */
    private function getRedemptionItems($conn, $codeId) {
        $stmt = $conn->prepare("
            SELECT rci.*, it.name as item_name, it.stackable, it.max_stack
            FROM redemption_code_items rci
            JOIN item_templates it ON rci.item_template_id = it.id
            WHERE rci.redemption_code_id = ?
            ORDER BY rci.sort_order
        ");
        $stmt->execute([$codeId]);
        return $stmt->fetchAll();
    }
    

    
    /**
     * 给玩家发放物品
     */
    private function giveItemToPlayer($conn, $playerId, $itemTemplateId, $quantity, $isBound) {
        try {
            // 获取物品模板信息
            $stmt = $conn->prepare("SELECT stackable, max_stack FROM item_templates WHERE id = ?");
            $stmt->execute([$itemTemplateId]);
            $item = $stmt->fetch();

            if (!$item) {
                throw new Exception("物品模板不存在: {$itemTemplateId}");
            }

            $isStackable = (bool)$item['stackable'];
            $maxStack = (int)$item['max_stack'] ?: 999;
            $quantityToAdd = $quantity;

            if ($isStackable && $quantityToAdd > 0) {
                // 可堆叠物品：尝试合并到现有堆叠
                $stacksStmt = $conn->prepare("
                    SELECT id, quantity
                    FROM player_inventory
                    WHERE player_id = ? AND item_template_id = ? AND quantity < ? AND is_equipped = 0 AND is_bound = ?
                    ORDER BY quantity DESC FOR UPDATE
                ");
                $stacksStmt->execute([$playerId, $itemTemplateId, $maxStack, $isBound ? 1 : 0]);
                $existingStacks = $stacksStmt->fetchAll();

                // 尝试填充现有堆叠
                foreach ($existingStacks as $stack) {
                    if ($quantityToAdd <= 0) break;

                    $canAdd = $maxStack - $stack['quantity'];
                    $toAdd = min($canAdd, $quantityToAdd);

                    if ($toAdd > 0) {
                        $updateStmt = $conn->prepare("
                            UPDATE player_inventory
                            SET quantity = quantity + ?
                            WHERE id = ?
                        ");
                        $updateStmt->execute([$toAdd, $stack['id']]);
                        $quantityToAdd -= $toAdd;
                    }
                }

                // 如果还有剩余数量，创建新堆叠
                while ($quantityToAdd > 0) {
                    $toAdd = min($maxStack, $quantityToAdd);
                    $insertStmt = $conn->prepare("
                        INSERT INTO player_inventory (player_id, item_template_id, quantity, is_equipped, is_bound)
                        VALUES (?, ?, ?, 0, ?)
                    ");
                    $insertStmt->execute([$playerId, $itemTemplateId, $toAdd, $isBound ? 1 : 0]);
                    $quantityToAdd -= $toAdd;
                }
            } else {
                // 不可堆叠物品：创建多个单独的物品
                for ($i = 0; $i < $quantity; $i++) {
                    $insertStmt = $conn->prepare("
                        INSERT INTO player_inventory (player_id, item_template_id, quantity, is_equipped, is_bound)
                        VALUES (?, ?, 1, 0, ?)
                    ");
                    $insertStmt->execute([$playerId, $itemTemplateId, $isBound ? 1 : 0]);
                }
            }

            return ['success' => true];
        } catch (Exception $e) {
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }
    
    /**
     * 记录使用记录
     */
    private function recordUsage($conn, $codeId, $playerId, $ipAddress) {
        $stmt = $conn->prepare("
            INSERT INTO redemption_code_usage (redemption_code_id, player_id, ip_address) 
            VALUES (?, ?, ?)
        ");
        $stmt->execute([$codeId, $playerId, $ipAddress]);
    }
    
    /**
     * 更新使用次数
     */
    private function updateUsageCount($conn, $codeId) {
        $stmt = $conn->prepare("
            UPDATE redemption_codes 
            SET current_usage_count = current_usage_count + 1 
            WHERE id = ?
        ");
        $stmt->execute([$codeId]);
    }
    
    /**
     * 检查任务进度
     */
    private function checkQuestProgress($playerId, $items) {
        try {
            // 传递数据库连接工厂函数给QuestManager
            $questManager = new QuestManager(function() {
                return Database::getInstance()->getConnection();
            });
            
            foreach ($items as $item) {
                // 检查收集任务进度
                $questUpdates = $questManager->checkQuestObjectiveUpdates(
                    $playerId,
                    'collect',
                    $item['item_template_id'],
                    $item['quantity']
                );
                
                if (!empty($questUpdates)) {
                    // 通过Redis发布任务进度更新通知
                    $this->publishQuestProgressUpdate($playerId, $questUpdates);
                }
            }
        } catch (Exception $e) {
            // 任务进度检查失败不应该影响兑换，只记录错误
            error_log("兑换码任务进度检查错误: " . $e->getMessage());
        }
    }
    
    /**
     * 发布任务进度更新通知
     */
    private function publishQuestProgressUpdate($playerId, $questUpdates) {
        try {
            $this->redis->with(function($redis) use ($playerId, $questUpdates) {
                $notificationData = [
                    'type' => 'quest_progress_update',
                    'player_id' => $playerId,
                    'quest_updates' => $questUpdates,
                    'source' => 'redemption_code'
                ];
                
                $redis->publish('quest-progress-updates', json_encode($notificationData));
            });
        } catch (Exception $e) {
            error_log("发布任务进度更新通知失败: " . $e->getMessage());
        }
    }
}
?>
