<?php
session_start();
header('Content-Type: application/json');

// 调试日志函数
function debugLog($message) {
    error_log('[Building API Debug] ' . $message);
}

// 记录所有传入的数据
debugLog('全部 POST 数据: ' . print_r($_POST, true));
debugLog('全部 GET 数据: ' . print_r($_GET, true));
debugLog('Action: ' . ($_POST['action'] ?? $_GET['action'] ?? 'N/A'));

// 安全检查
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    debugLog('未授权访问');
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';

// 获取数据库连接
$db = Database::getInstance()->getConnection();

// 从 POST 和 GET 中获取 action
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$response = ['success' => false, 'message' => '无效的操作'];

// 记录动作
debugLog('处理动作: ' . $action);

try {
    switch ($action) {
        case 'get':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT) ?? filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
            debugLog('获取建筑 ID: ' . $id);
            if (!$id) {
                throw new Exception("无效的ID");
            }
            
            $stmt = $db->prepare("SELECT * FROM buildings WHERE id = ?");
            $stmt->execute([$id]);
            $building = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$building) {
                throw new Exception("找不到ID为 {$id} 的建筑");
            }

            // 获取该建筑关联的场景
            $sceneStmt = $db->prepare("
                SELECT sb.scene_id, s.name as scene_name 
                FROM scene_buildings sb
                JOIN scenes s ON sb.scene_id = s.id
                WHERE sb.building_id = ?
            ");
            $sceneStmt->execute([$id]);
            $building['scenes'] = $sceneStmt->fetchAll(PDO::FETCH_ASSOC);
            $sceneStmt->closeCursor();

            $response = ['success' => true, 'data' => $building];
            break;

        case 'create':
        case 'update':
            $data = $_POST;
            $isCreate = ($action === 'create');
            
            if (!$isCreate) {
                $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
                if (!$id) throw new Exception("更新操作需要一个有效的建筑ID");
            }

            $params = [
                ':name' => $data['name'] ?? '新建筑',
                ':type' => $data['type'] ?? '',
                ':description' => $data['description'] ?? ''
            ];

            if ($isCreate) {
                $sql = "INSERT INTO buildings (name, type, description) 
                        VALUES (:name, :type, :description)";
            } else {
                $params[':id'] = $id;
                $sql = "UPDATE buildings SET 
                            name = :name, 
                            type = :type, 
                            description = :description
                        WHERE id = :id";
            }
            
            $db->beginTransaction();
            try {
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $buildingId = $isCreate ? $db->lastInsertId() : $id;

                $db->commit();
                
                if ($isCreate) {
                    $response = ['success' => true, 'message' => '建筑创建成功', 'id' => $buildingId];
                } else {
                    $response = ['success' => true, 'message' => '建筑更新成功'];
                }
            } catch (Exception $e) {
                $db->rollBack();
                throw new Exception(($isCreate ? '创建' : '更新') . '建筑失败: ' . $e->getMessage());
            }
            break;

        case 'delete':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            if (!$id) {
                throw new Exception("无效的ID");
            }

            // 检查是否为仓库建筑，如果是则检查是否有玩家物品
            $buildingStmt = $db->prepare("SELECT type FROM buildings WHERE id = ?");
            $buildingStmt->execute([$id]);
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if ($building && $building['type'] === 'WAREHOUSE') {
                // 检查该仓库建筑是否有玩家物品
                $itemCheckStmt = $db->prepare("
                    SELECT COUNT(*) as item_count
                    FROM warehouse_storage ws
                    JOIN scene_buildings sb ON ws.scene_building_id = sb.id
                    WHERE sb.building_id = ?
                ");
                $itemCheckStmt->execute([$id]);
                $itemCount = $itemCheckStmt->fetch(PDO::FETCH_ASSOC);
                $itemCheckStmt->closeCursor();

                if ($itemCount['item_count'] > 0) {
                    throw new Exception("无法删除仓库建筑：仓库中还有玩家物品，请先清空所有物品后再删除");
                }
            }

            $db->beginTransaction();
            try {
                // 1. 删除场景关联
                $stmt = $db->prepare("DELETE FROM scene_buildings WHERE building_id = ?");
                $stmt->execute([$id]);

                // 2. 删除建筑本身
                $stmt = $db->prepare("DELETE FROM buildings WHERE id = ?");
                $stmt->execute([$id]);

                $db->commit();
                $response = ['success' => true, 'message' => '建筑及其所有关联配置已成功删除'];

            } catch (Exception $e) {
                $db->rollBack();
                throw new Exception('删除建筑失败，数据库操作被回滚: ' . $e->getMessage());
            }
            break;

        case 'get_for_list':
            // 获取简化的建筑列表，用于场景分配
            $stmt = $db->prepare("SELECT id, name, type, description FROM buildings ORDER BY name");
            $stmt->execute();
            $buildings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 获取每个建筑分配的场景
            foreach ($buildings as &$building) {
                $sceneStmt = $db->prepare("
                    SELECT s.id, s.name 
                    FROM scene_buildings sb
                    JOIN scenes s ON sb.scene_id = s.id
                    WHERE sb.building_id = ?
                    ORDER BY s.name
                ");
                $sceneStmt->execute([$building['id']]);
                $building['scenes'] = $sceneStmt->fetchAll(PDO::FETCH_ASSOC);
            }
            
            $response = ['success' => true, 'data' => $buildings];
            break;

        case 'assign_scenes':
            $buildingId = filter_input(INPUT_POST, 'building_id', FILTER_VALIDATE_INT);
            $sceneIds = isset($_POST['scenes']) && is_array($_POST['scenes'])
                      ? array_filter($_POST['scenes'], function($id) { return !empty($id); })
                      : [];

            debugLog('分配场景 - 建筑ID: ' . $buildingId);
            debugLog('分配场景 - 场景IDs: ' . print_r($sceneIds, true));

            if (!$buildingId) {
                throw new Exception("无效的建筑ID");
            }

            // 检查是否为仓库建筑
            $buildingStmt = $db->prepare("SELECT type FROM buildings WHERE id = ?");
            $buildingStmt->execute([$buildingId]);
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if ($building && $building['type'] === 'WAREHOUSE') {
                // 获取当前关联的场景
                $currentScenesStmt = $db->prepare("SELECT id, scene_id FROM scene_buildings WHERE building_id = ?");
                $currentScenesStmt->execute([$buildingId]);
                $currentScenes = $currentScenesStmt->fetchAll(PDO::FETCH_ASSOC);
                $currentScenesStmt->closeCursor();

                // 检查要移除的场景中是否有仓库物品
                foreach ($currentScenes as $sceneBuilding) {
                    // 如果当前场景不在新场景列表中，则检查是否有物品
                    if (!in_array($sceneBuilding['scene_id'], $sceneIds)) {
                        $itemCheckStmt = $db->prepare("
                            SELECT COUNT(*) as item_count
                            FROM warehouse_storage
                            WHERE scene_building_id = ?
                        ");
                        $itemCheckStmt->execute([$sceneBuilding['id']]);
                        $itemCount = $itemCheckStmt->fetch(PDO::FETCH_ASSOC);
                        $itemCheckStmt->closeCursor();

                        if ($itemCount['item_count'] > 0) {
                            throw new Exception("无法移除场景关联：场景ID {$sceneBuilding['scene_id']} 的仓库中还有玩家物品，请先清空所有物品后再移除");
                        }
                    }
                }
            }

            $db->beginTransaction();
            try {
                // 1. 删除旧的场景关联
                $stmt = $db->prepare("DELETE FROM scene_buildings WHERE building_id = ?");
                $stmt->execute([$buildingId]);
                debugLog('删除旧场景关联 - 影响行数: ' . $stmt->rowCount());

                // 2. 添加新的场景关联
                if (!empty($sceneIds)) {
                    $insertStmt = $db->prepare("INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)");
                    foreach ($sceneIds as $sceneId) {
                        debugLog('插入新场景关联 - 场景ID: ' . $sceneId . ', 建筑ID: ' . $buildingId);
                        $insertStmt->execute([$sceneId, $buildingId]);
                    }
                    debugLog('插入完成');
                } else {
                    debugLog('没有场景需要插入');
                }

                $db->commit();
                debugLog('事务提交成功');
                $response = ['success' => true, 'message' => '场景分配成功'];
            } catch (Exception $e) {
                $db->rollBack();
                debugLog('事务回滚 - 错误: ' . $e->getMessage());
                throw new Exception('分配场景失败: ' . $e->getMessage());
            }
            break;

        case 'get_scenes':
            // 获取所有场景，用于场景选择器
            $stmt = $db->query("SELECT id, name FROM scenes ORDER BY name ASC");
            $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $scenes];
            break;

        case 'get_building_scenes':
            // 获取特定建筑关联的场景
            $buildingId = filter_input(INPUT_GET, 'building_id', FILTER_VALIDATE_INT);
            if (!$buildingId) {
                throw new Exception("无效的建筑ID");
            }
            
            $stmt = $db->prepare("SELECT scene_id FROM scene_buildings WHERE building_id = ?");
            $stmt->execute([$buildingId]);
            $sceneIds = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            debugLog('获取建筑场景 - 建筑ID: ' . $buildingId);
            debugLog('获取建筑场景 - 场景IDs: ' . print_r($sceneIds, true));
            
            $response = ['success' => true, 'data' => $sceneIds];
            break;

        case 'remove_scene':
            $buildingId = filter_input(INPUT_POST, 'building_id', FILTER_VALIDATE_INT);
            $sceneId = $_POST['scene_id'] ?? '';

            debugLog('移除单个场景 - 建筑ID: ' . $buildingId);
            debugLog('移除单个场景 - 场景ID: ' . $sceneId);

            if (!$buildingId) {
                throw new Exception("无效的建筑ID");
            }

            if (empty($sceneId)) {
                throw new Exception("无效的场景ID");
            }

            // 检查是否为仓库建筑，如果是则检查是否有玩家物品
            $buildingStmt = $db->prepare("SELECT type FROM buildings WHERE id = ?");
            $buildingStmt->execute([$buildingId]);
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if ($building && $building['type'] === 'WAREHOUSE') {
                // 获取要删除的场景建筑ID
                $sceneBuildingStmt = $db->prepare("SELECT id FROM scene_buildings WHERE building_id = ? AND scene_id = ?");
                $sceneBuildingStmt->execute([$buildingId, $sceneId]);
                $sceneBuilding = $sceneBuildingStmt->fetch(PDO::FETCH_ASSOC);
                $sceneBuildingStmt->closeCursor();

                if ($sceneBuilding) {
                    // 检查该场景建筑是否有玩家物品
                    $itemCheckStmt = $db->prepare("
                        SELECT COUNT(*) as item_count
                        FROM warehouse_storage
                        WHERE scene_building_id = ?
                    ");
                    $itemCheckStmt->execute([$sceneBuilding['id']]);
                    $itemCount = $itemCheckStmt->fetch(PDO::FETCH_ASSOC);
                    $itemCheckStmt->closeCursor();

                    if ($itemCount['item_count'] > 0) {
                        throw new Exception("无法移除场景关联：该场景的仓库中还有玩家物品，请先清空所有物品后再移除");
                    }
                }
            }

            $db->beginTransaction();
            try {
                // 删除特定的场景关联
                $stmt = $db->prepare("DELETE FROM scene_buildings WHERE building_id = ? AND scene_id = ?");
                $stmt->execute([$buildingId, $sceneId]);
                debugLog('删除场景关联 - 影响行数: ' . $stmt->rowCount());

                $db->commit();
                $response = ['success' => true, 'message' => '场景关联已移除'];
            } catch (Exception $e) {
                $db->rollBack();
                debugLog('事务回滚 - 错误: ' . $e->getMessage());
                throw new Exception('移除场景关联失败: ' . $e->getMessage());
            }
            break;

        default:
            debugLog('未知操作: ' . $action);
            throw new Exception("未知操作: " . $action);
    }
} catch (Exception $e) {
    debugLog('错误: ' . $e->getMessage());
    $response = ['success' => false, 'message' => $e->getMessage()];
}

echo json_encode($response); 