<?php
// api/refine_leaderboard.php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/Database.php';

try {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // 获取装备凝练榜前10名
    $query = "
        SELECT 
            pi.id as inventory_id,
            pi.player_id,
            pi.instance_data,
            it.name as item_name,
            ed.slot,
            a.username
        FROM player_inventory pi
        JOIN item_templates it ON pi.item_template_id = it.id
        JOIN equipment_details ed ON it.id = ed.item_template_id
        JOIN accounts a ON pi.player_id = a.id
        WHERE it.category = 'Equipment' 
        AND pi.instance_data LIKE '%\"refined\":true%'
        ORDER BY pi.id
        LIMIT 100
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute();
    $equipmentData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 解析凝练数据并排序
    $refinedEquipment = [];
    foreach ($equipmentData as $equipment) {
        $instanceData = json_decode($equipment['instance_data'], true);
        if (isset($instanceData['refined']) && $instanceData['refined'] && isset($instanceData['refine_value'])) {
            $equipment['refine_value'] = floatval($instanceData['refine_value']);
            $equipment['refine_tier'] = $instanceData['refine_tier'] ?? 0;
            $equipment['refine_prefix'] = $instanceData['refine_prefix'] ?? '';
            $equipment['display_name'] = $instanceData['display_name'] ?? $equipment['item_name'];
            $refinedEquipment[] = $equipment;
        }
    }
    
    // 按凝练值降序排序
    usort($refinedEquipment, function($a, $b) {
        return $b['refine_value'] <=> $a['refine_value'];
    });
    
    // 取前10名
    $topEquipment = array_slice($refinedEquipment, 0, 10);
    
    // 返回数据
    echo json_encode([
        'success' => true,
        'equipment_list' => $topEquipment
    ], JSON_UNESCAPED_UNICODE);
    
} catch (Exception $e) {
    echo json_encode([
        'success' => false,
        'error' => $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
