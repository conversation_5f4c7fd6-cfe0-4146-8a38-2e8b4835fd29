<?php
session_start();
header('Content-Type: application/json');

// 权限验证
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';
$db = Database::getInstance()->getConnection();
$response = ['success' => false, 'message' => '无效的操作'];

// 从 POST 或 GET 获取 action
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$data = $_POST;

try {
    switch ($action) {
        // 获取所有掉落表的基本信息
        case 'get_tables':
            $stmt = $db->query("SELECT id, name FROM loot_tables ORDER BY name");
            $tables = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $tables];
            break;

        // 获取单个掉落表的详细信息（包括所有物品条目）
        case 'get_table_details':
            $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
            if (!$id) throw new Exception("无效的掉落表ID");

            $stmt = $db->prepare("SELECT id, name, description FROM loot_tables WHERE id = ?");
            $stmt->execute([$id]);
            $table = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$table) throw new Exception("找不到掉落表");

            $stmt = $db->prepare(
                "SELECT lte.id, lte.item_template_id, it.name, lte.drop_chance, lte.min_quantity, lte.max_quantity
                 FROM loot_table_entries lte
                 JOIN item_templates it ON lte.item_template_id = it.id
                 WHERE lte.loot_table_id = ? ORDER BY it.name"
            );
            $stmt->execute([$id]);
            $table['entries'] = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = ['success' => true, 'data' => $table];
            break;

        // 创建一个新的空掉落表
        case 'create_table':
            $name = $data['name'] ?? '新掉落表';
            $description = $data['description'] ?? '';
            if (empty($name)) throw new Exception("掉落表名称不能为空");
            
            $stmt = $db->prepare("INSERT INTO loot_tables (name, description) VALUES (?, ?)");
            $stmt->execute([$name, $description]);
            $newId = $db->lastInsertId();
            $response = ['success' => true, 'message' => '掉落表创建成功', 'id' => $newId, 'name' => $name, 'description' => $description];
            break;

        // 更新掉落表的名称
        case 'update_table_name':
             $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
             $name = $data['name'] ?? '';
             $description = $data['description'] ?? '';
             if (!$id) throw new Exception("无效的ID");
             if (empty($name)) throw new Exception("名称不能为空");
             
             $stmt = $db->prepare("UPDATE loot_tables SET name = ?, description = ? WHERE id = ?");
             $stmt->execute([$name, $description, $id]);
             $response = ['success' => true, 'message' => '名称更新成功'];
             break;

        // 删除一个掉落表（其所有条目会因外键约束的 ON DELETE CASCADE 自动删除）
        case 'delete_table':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            if (!$id) throw new Exception("无效的ID");
            
            $stmt = $db->prepare("DELETE FROM loot_tables WHERE id = ?");
            $stmt->execute([$id]);
            $response = ['success' => true, 'message' => '掉落表已删除'];
            break;

        // 向指定的掉落表添加一个物品条目
        case 'add_entry':
            $tableId = filter_input(INPUT_POST, 'loot_table_id', FILTER_VALIDATE_INT);
            $itemId = filter_input(INPUT_POST, 'item_template_id', FILTER_VALIDATE_INT);
            $chance = filter_input(INPUT_POST, 'drop_chance', FILTER_VALIDATE_FLOAT);
            $min = filter_input(INPUT_POST, 'min_quantity', FILTER_VALIDATE_INT);
            $max = filter_input(INPUT_POST, 'max_quantity', FILTER_VALIDATE_INT);

            if (!$tableId || !$itemId || $chance === false || $min === false || $max === false) {
                throw new Exception("所有字段均为必填项");
            }
            if ($min > $max) throw new Exception("最小数量不能大于最大数量");

            $stmt = $db->prepare(
                "INSERT INTO loot_table_entries (loot_table_id, item_template_id, drop_chance, min_quantity, max_quantity)
                 VALUES (?, ?, ?, ?, ?)"
            );
            $stmt->execute([$tableId, $itemId, $chance, $min, $max]);
            $newEntryId = $db->lastInsertId();

            // 获取物品名称以便返回给前端
            $stmt = $db->prepare("SELECT name FROM item_templates WHERE id = ?");
            $stmt->execute([$itemId]);
            $itemName = $stmt->fetchColumn();

            $response = [
                'success' => true, 
                'message' => '物品添加成功', 
                'new_entry' => [
                    'id' => $newEntryId,
                    'item_template_id' => $itemId,
                    'name' => $itemName,
                    'drop_chance' => $chance,
                    'min_quantity' => $min,
                    'max_quantity' => $max
                ]
            ];
            break;

        // 更新一个已存在的物品条目
        case 'update_entry':
            $entryId = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            $chance = filter_input(INPUT_POST, 'drop_chance', FILTER_VALIDATE_FLOAT);
            $min = filter_input(INPUT_POST, 'min_quantity', FILTER_VALIDATE_INT);
            $max = filter_input(INPUT_POST, 'max_quantity', FILTER_VALIDATE_INT);
            
            if (!$entryId || $chance === false || $min === false || $max === false) {
                 throw new Exception("所有字段均为必填项");
            }
            if ($min > $max) throw new Exception("最小数量不能大于最大数量");

            $stmt = $db->prepare(
                "UPDATE loot_table_entries 
                 SET drop_chance = ?, min_quantity = ?, max_quantity = ?
                 WHERE id = ?"
            );
            $stmt->execute([$chance, $min, $max, $entryId]);
            $response = ['success' => true, 'message' => '物品条目更新成功'];
            break;

        // 从掉落表删除一个物品条目
        case 'delete_entry':
            $entryId = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            if (!$entryId) throw new Exception("无效的条目ID");

            $stmt = $db->prepare("DELETE FROM loot_table_entries WHERE id = ?");
            $stmt->execute([$entryId]);
            $response = ['success' => true, 'message' => '物品条目已删除'];
            break;

        default:
            throw new Exception("未知的操作: " . htmlspecialchars($action));
    }
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response); 