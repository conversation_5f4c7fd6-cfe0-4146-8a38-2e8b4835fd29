<?php
header('Content-Type: application/json');

require_once '../config/Database.php';
require_once 'auth.php';

$db = Database::getInstance();
$conn = $db->getConnection();

$method = $_SERVER['REQUEST_METHOD'];

function send_json($data) {
    echo json_encode($data, JSON_UNESCAPED_UNICODE);
    exit;
}

function send_error($message, $code = 400) {
    http_response_code($code);
    send_json(['error' => $message]);
}

try {
    switch ($method) {
        case 'GET':
            if (isset($_GET['id'])) {
                // Get a single skill
                $stmt = $conn->prepare("SELECT * FROM skill_templates WHERE id = ?");
                $stmt->execute([$_GET['id']]);
                $skill = $stmt->fetch(PDO::FETCH_ASSOC);
                if (!$skill) {
                    send_error('Skill not found', 404);
                }
                // Decode JSON string to object for easier handling in frontend if needed
                $skill['effects'] = json_decode($skill['effects']);
                send_json($skill);
            } else {
                // Get all skills
                $stmt = $conn->query("SELECT * FROM skill_templates ORDER BY id DESC");
                $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
                send_json($skills);
            }
            break;

        case 'POST':
            $data = json_decode(file_get_contents('php://input'), true);
            if (!$data) {
                send_error('Invalid JSON data');
            }
            
            // Validate JSON in effects
            if (!is_string($data['effects']) || json_decode($data['effects']) === null) {
                send_error('技能效果(effects)必须是合法的JSON格式');
            }

            // Sanitize and prepare data
            $fields = [
                'name' => $data['name'],
                'description' => $data['description'],
                'battle_description' => $data['battle_description'] ?? null,
                'icon' => $data['icon'],
                'skill_type' => $data['skill_type'],
                'target_type' => $data['target_type'],
                'mp_cost' => intval($data['mp_cost']),
                'knowledge_points_cost' => intval($data['knowledge_points_cost'] ?? 1),
                'required_skill_book_id' => !empty($data['required_skill_book_id']) ? intval($data['required_skill_book_id']) : null,
                'required_level' => intval($data['required_level']),
                'required_job_id' => !empty($data['required_job_id']) ? intval($data['required_job_id']) : null,
                'delay_turns' => !empty($data['delay_turns']) ? intval($data['delay_turns']) : null,
                'duration_turns' => !empty($data['duration_turns']) ? intval($data['duration_turns']) : null,
                'cooldown_turns' => intval($data['cooldown_turns']),
                'effects' => $data['effects']
            ];

            if (empty($data['id'])) {
                // Create new skill
                $sql = "INSERT INTO skill_templates (name, description, battle_description, icon, skill_type, target_type, mp_cost, knowledge_points_cost, required_skill_book_id, required_level, required_job_id, delay_turns, duration_turns, cooldown_turns, effects, created_at) VALUES (:name, :description, :battle_description, :icon, :skill_type, :target_type, :mp_cost, :knowledge_points_cost, :required_skill_book_id, :required_level, :required_job_id, :delay_turns, :duration_turns, :cooldown_turns, :effects, NOW())";
                $stmt = $conn->prepare($sql);
            } else {
                // Update existing skill
                $fields['id'] = $data['id'];
                $sql = "UPDATE skill_templates SET name = :name, description = :description, battle_description = :battle_description, icon = :icon, skill_type = :skill_type, target_type = :target_type, mp_cost = :mp_cost, knowledge_points_cost = :knowledge_points_cost, required_skill_book_id = :required_skill_book_id, required_level = :required_level, required_job_id = :required_job_id, delay_turns = :delay_turns, duration_turns = :duration_turns, cooldown_turns = :cooldown_turns, effects = :effects WHERE id = :id";
                $stmt = $conn->prepare($sql);
            }
            
            if ($stmt->execute($fields)) {
                send_json(['success' => true]);
            } else {
                send_error('Database operation failed: ' . implode(", ", $stmt->errorInfo()));
            }
            break;

        case 'DELETE':
            if (!isset($_GET['id'])) {
                send_error('Skill ID is required');
            }
            $id = $_GET['id'];
            
            // Check if skill is in use by players
            $stmt = $conn->prepare("SELECT COUNT(*) FROM player_skills WHERE skill_template_id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() > 0) {
                send_error('Cannot delete skill because it has been learned by players.');
            }

            $stmt = $conn->prepare("DELETE FROM skill_templates WHERE id = ?");
            if ($stmt->execute([$id])) {
                send_json(['success' => true]);
            } else {
                send_error('Database operation failed: ' . implode(", ", $stmt->errorInfo()));
            }
            break;

        default:
            send_error('Invalid request method', 405);
            break;
    }
} catch (Exception $e) {
    send_error('An unexpected error occurred: ' . $e->getMessage(), 500);
} 