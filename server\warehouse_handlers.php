<?php
// server/warehouse_handlers.php

/**
 * 处理仓库取出物品请求
 * @param int $fd WebSocket连接标识符
 * @param array $payload 请求数据
 * @param object $server 服务器实例
 */
function handleWarehouseWithdraw($fd, $payload, $server) {
    $playerId = $payload['player_id'] ?? null;
    $sceneBuildingId = $payload['scene_building_id'] ?? null;
    $warehouseItemId = $payload['warehouse_item_id'] ?? null;
    $quantity = $payload['quantity'] ?? 1;

    if (!$playerId || !$sceneBuildingId || !$warehouseItemId) {
        return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '参数不完整']);
    }

    if ($quantity <= 0) {
        return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '取出数量必须大于0']);
    }

    $db = Database::getInstance();
    $conn = $db->getConnection();

    try {
        $conn->beginTransaction();

        // 1. 验证建筑类型是仓库
        $buildingStmt = $db->query(
            "SELECT b.type, b.name 
             FROM buildings b
             JOIN scene_buildings sb ON sb.building_id = b.id
             WHERE sb.id = ?",
            [$sceneBuildingId]
        );
        $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
        $buildingStmt->closeCursor();

        if (!$building || $building['type'] !== 'WAREHOUSE') {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '只能在仓库建筑中取出物品']);
        }

        // 2. 获取仓库中的物品
        $itemStmt = $db->query(
            "SELECT ws.*, it.name, it.stackable, it.max_stack, it.category
             FROM warehouse_storage ws
             JOIN item_templates it ON ws.item_template_id = it.id
             WHERE ws.id = ? AND ws.player_id = ? AND ws.scene_building_id = ?",
            [$warehouseItemId, $playerId, $sceneBuildingId]
        );
        $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
        $itemStmt->closeCursor();

        if (!$item) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '仓库中没有该物品']);
        }

        if ($quantity > $item['quantity']) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '取出数量超过仓库存储数量']);
        }

        // 3. 检查背包空间是否足够
        $backpackCheck = checkBackpackCapacity($playerId, $item, $quantity);
        if (!$backpackCheck['success']) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $backpackCheck['message']]);
        }

        // 4. 将物品添加到玩家背包
        if ($item['stackable']) {
            // 可堆叠物品：按绑定状态分别处理
            $remainingQuantity = $quantity;
            $maxStack = $item['max_stack'] ?? 999;

            while ($remainingQuantity > 0) {
                // 查找可以合并的现有堆叠（相同绑定状态和相同instance_data）
                // 只有当instance_data完全相同时才能合并
                if ($item['instance_data'] === null || $item['instance_data'] === '') {
                    // 仓库物品没有instance_data，可以与背包中同样没有instance_data的物品合并
                    $existingStmt = $db->query(
                        "SELECT id, quantity FROM player_inventory
                         WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND is_bound = ? AND quantity < ?
                         AND (instance_data IS NULL OR instance_data = '')
                         ORDER BY quantity DESC LIMIT 1",
                        [$playerId, $item['item_template_id'], $item['is_bound'], $maxStack]
                    );
                } else {
                    // 仓库物品有instance_data，只能与背包中有相同instance_data的物品合并
                    $existingStmt = $db->query(
                        "SELECT id, quantity FROM player_inventory
                         WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND is_bound = ? AND quantity < ?
                         AND instance_data = ?
                         ORDER BY quantity DESC LIMIT 1",
                        [$playerId, $item['item_template_id'], $item['is_bound'], $maxStack, $item['instance_data']]
                    );
                }
                $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);
                $existingStmt->closeCursor();

                if ($existing) {
                    // 计算可以合并的数量
                    $canMerge = min($remainingQuantity, $maxStack - $existing['quantity']);

                    // 合并到现有堆叠
                    $db->query(
                        "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?",
                        [$canMerge, $existing['id']]
                    );

                    $remainingQuantity -= $canMerge;
                } else {
                    // 创建新的背包记录
                    $newQuantity = min($remainingQuantity, $maxStack);

                    // 保留原始的instance_data，无论是否可堆叠
                    $db->query(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound)
                         VALUES (?, ?, ?, ?, ?)",
                        [$playerId, $item['item_template_id'], $newQuantity, $item['instance_data'], $item['is_bound']]
                    );

                    $remainingQuantity -= $newQuantity;
                }
            }
        } else {
            // 不可堆叠物品：为每个物品创建单独记录
            for ($i = 0; $i < $quantity; $i++) {
                $db->query(
                    "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound)
                     VALUES (?, ?, 1, ?, ?)",
                    [$playerId, $item['item_template_id'], $item['instance_data'], $item['is_bound']]
                );
            }
        }

        // 5. 从仓库中扣除物品
        if ($quantity >= $item['quantity']) {
            // 全部取出，删除仓库记录
            $db->query("DELETE FROM warehouse_storage WHERE id = ?", [$warehouseItemId]);
        } else {
            // 部分取出，减少仓库数量
            $db->query(
                "UPDATE warehouse_storage SET quantity = quantity - ?, updated_at = NOW() WHERE id = ?",
                [$quantity, $warehouseItemId]
            );
        }

        $conn->commit();

        // 6. 检查任务进度
        $server->checkWarehouseQuestProgress($fd, $playerId, 'withdraw', [
            'item_template_id' => $item['item_template_id'],
            'quantity' => $quantity,
            'item_name' => $item['name']
        ]);

        // 7. 发送成功响应
        $server->sendMessage($fd, MessageProtocol::S2C_SUCCESS, [
            'message' => "成功取出 {$item['name']} x{$quantity}"
        ]);

        // 8. 背包数据会通过 handleSuccessMessage 自动更新

    } catch (Exception $e) {
        $conn->rollBack();
        error_log("仓库取出物品失败: " . $e->getMessage());
        $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '取出失败: ' . $e->getMessage()]);
    }
}

/**
 * 检查背包容量是否足够
 * @param int $playerId 玩家ID
 * @param array $item 物品信息
 * @param int $quantity 数量
 * @return array 检查结果
 */
function checkBackpackCapacity($playerId, $item, $quantity) {
    // 这里可以实现背包容量检查逻辑
    // 目前简化处理，假设背包空间足够
    return ['success' => true];
}

/**
 * 处理仓库扩容请求
 * @param int $fd WebSocket连接标识符
 * @param array $payload 请求数据
 * @param object $server 服务器实例
 */
function handleWarehouseExpand($fd, $payload, $server) {
    $playerId = $payload['player_id'] ?? null;
    $sceneBuildingId = $payload['scene_building_id'] ?? null;
    $inventoryId = $payload['inventory_id'] ?? null; // 扩容物品的背包ID

    if (!$playerId || !$sceneBuildingId || !$inventoryId) {
        return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '参数不完整']);
    }

    $db = Database::getInstance();
    $conn = $db->getConnection();

    try {
        $conn->beginTransaction();

        // 1. 验证建筑类型是仓库
        $buildingStmt = $db->query(
            "SELECT b.type, b.name 
             FROM buildings b
             JOIN scene_buildings sb ON sb.building_id = b.id
             WHERE sb.id = ?",
            [$sceneBuildingId]
        );
        $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
        $buildingStmt->closeCursor();

        if (!$building || $building['type'] !== 'WAREHOUSE') {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '只能在仓库建筑中进行扩容']);
        }

        // 2. 获取玩家背包中的扩容物品
        $itemStmt = $db->query(
            "SELECT pi.*, it.name
             FROM player_inventory pi
             JOIN item_templates it ON pi.item_template_id = it.id
             WHERE pi.id = ? AND pi.player_id = ? AND pi.is_equipped = 0",
            [$inventoryId, $playerId]
        );
        $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
        $itemStmt->closeCursor();

        if (!$item) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '扩容物品不存在']);
        }

        // 3. 检查是否为有效的扩容物品
        $expansionStmt = $db->query(
            "SELECT wei.expansion_amount, wei.max_uses,
                    COALESCE(pwe_count.usage_count, 0) as current_usage
             FROM warehouse_expansion_items wei
             LEFT JOIN (
                 SELECT item_template_id, COUNT(*) as usage_count
                 FROM player_warehouse_expansions
                 WHERE player_id = ? AND scene_building_id = ?
                 GROUP BY item_template_id
             ) pwe_count ON wei.item_template_id = pwe_count.item_template_id
             WHERE wei.item_template_id = ? AND wei.is_active = 1",
            [$playerId, $sceneBuildingId, $item['item_template_id']]
        );
        $expansionConfig = $expansionStmt->fetch(PDO::FETCH_ASSOC);
        $expansionStmt->closeCursor();

        if (!$expansionConfig) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该物品不能用于仓库扩容']);
        }

        // 4. 检查使用次数限制
        // NULL 或 0 表示无限制，其他正数表示有限制
        if ($expansionConfig['max_uses'] !== null && $expansionConfig['max_uses'] > 0 &&
            $expansionConfig['current_usage'] >= $expansionConfig['max_uses']) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该扩容物品已达到最大使用次数']);
        }

        // 5. 检查是否已达到最大容量
        $configStmt = $db->query(
            "SELECT base_capacity, max_capacity FROM warehouse_config WHERE scene_building_id = ?",
            [$sceneBuildingId]
        );
        $config = $configStmt->fetch(PDO::FETCH_ASSOC);
        $configStmt->closeCursor();

        if (!$config) {
            $config = ['base_capacity' => 50, 'max_capacity' => 200];
        }

        $currentExpansionStmt = $db->query(
            "SELECT COALESCE(SUM(expansion_amount), 0) as total_expansion 
             FROM player_warehouse_expansions 
             WHERE player_id = ? AND scene_building_id = ?",
            [$playerId, $sceneBuildingId]
        );
        $currentExpansionData = $currentExpansionStmt->fetch(PDO::FETCH_ASSOC);
        $currentExpansionStmt->closeCursor();

        $currentCapacity = $config['base_capacity'] + ($currentExpansionData['total_expansion'] ?? 0);
        
        if ($currentCapacity >= $config['max_capacity']) {
            $conn->rollBack();
            return $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '仓库容量已达到最大值']);
        }

        // 6. 执行扩容
        $actualExpansion = min($expansionConfig['expansion_amount'], $config['max_capacity'] - $currentCapacity);
        $newCapacity = $currentCapacity + $actualExpansion;

        // 记录扩容历史
        $db->query(
            "INSERT INTO player_warehouse_expansions (player_id, scene_building_id, item_template_id, expansion_amount, total_capacity) 
             VALUES (?, ?, ?, ?, ?)",
            [$playerId, $sceneBuildingId, $item['item_template_id'], $actualExpansion, $newCapacity]
        );

        // 消耗扩容物品
        if ($item['quantity'] > 1) {
            $db->query(
                "UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?",
                [$inventoryId]
            );
        } else {
            $db->query("DELETE FROM player_inventory WHERE id = ?", [$inventoryId]);
        }

        $conn->commit();

        // 7. 检查仓库扩容任务进度
        $server->checkWarehouseQuestProgress($fd, $playerId, 'expand', [
            'expansion_item_id' => $item['item_template_id'],
            'new_capacity' => $newCapacity,
            'expansion_amount' => $actualExpansion
        ]);

        // 8. 发送成功响应
        $server->sendMessage($fd, MessageProtocol::S2C_SUCCESS, [
            'message' => "成功扩容 {$actualExpansion} 个空间，当前容量: {$newCapacity}"
        ]);

        // 9. 背包数据会通过 handleSuccessMessage 自动更新

    } catch (Exception $e) {
        $conn->rollBack();
        error_log("仓库扩容失败: " . $e->getMessage());
        $server->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '扩容失败: ' . $e->getMessage()]);
    }
}
?>
