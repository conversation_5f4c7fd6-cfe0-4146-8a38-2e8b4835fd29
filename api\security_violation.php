<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// 启用错误日志记录
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// 获取POST数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// 记录接收到的数据用于调试
error_log("Security violation API called with data: " . $input);

if (!$data) {
    error_log("Invalid JSON received: " . $input);
    http_response_code(400);
    echo json_encode(['error' => 'Invalid JSON', 'received' => $input]);
    exit;
}

// 验证必需字段
$required_fields = ['reason', 'timestamp', 'userAgent', 'url'];
foreach ($required_fields as $field) {
    if (!isset($data[$field])) {
        http_response_code(400);
        echo json_encode(['error' => "Missing field: $field"]);
        exit;
    }
}

try {
    // 数据库连接
    require_once '../config/Database.php';
    $pdo = Database::getInstance()->getConnection();
    
    // 获取客户端IP
    $client_ip = $_SERVER['HTTP_X_FORWARDED_FOR'] ?? $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    
    // 插入安全违规记录
    $sql = "INSERT INTO security_violations (
        reason, 
        client_ip, 
        user_agent, 
        url, 
        violation_time, 
        additional_data,
        created_at
    ) VALUES (?, ?, ?, ?, FROM_UNIXTIME(?), ?, NOW())";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([
        $data['reason'],
        $client_ip,
        $data['userAgent'],
        $data['url'],
        $data['timestamp'] / 1000, // 转换为秒
        json_encode($data), // 存储完整数据
    ]);
    
    // 检查是否需要自动封禁
    checkAndApplyBan($pdo, $client_ip);
    
    error_log("Security violation recorded successfully for IP: " . $client_ip);
    echo json_encode(['success' => true, 'message' => 'Violation recorded']);

} catch (Exception $e) {
    error_log("Security violation logging error: " . $e->getMessage());
    error_log("Stack trace: " . $e->getTraceAsString());
    http_response_code(500);
    echo json_encode(['error' => 'Internal server error', 'details' => $e->getMessage()]);
}

/**
 * 检查并应用自动封禁
 */
function checkAndApplyBan($pdo, $client_ip) {
    // 检查最近1小时内的违规次数
    $sql = "SELECT COUNT(*) as violation_count 
            FROM security_violations 
            WHERE client_ip = ? 
            AND created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$client_ip]);
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    
    $violation_count = $result['violation_count'];
    
    // 如果违规次数超过阈值，自动封禁
    if ($violation_count >= 5) {
        $ban_sql = "INSERT INTO ip_bans (
            ip_address, 
            reason, 
            banned_at, 
            expires_at, 
            is_active
        ) VALUES (?, ?, NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), 1)
        ON DUPLICATE KEY UPDATE 
            expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR),
            is_active = 1";
        
        $ban_stmt = $pdo->prepare($ban_sql);
        $ban_stmt->execute([
            $client_ip,
            "Auto-ban: Multiple security violations ($violation_count times)"
        ]);
        
        // 记录封禁日志
        error_log("Auto-banned IP $client_ip for $violation_count security violations");
    }
}
?>
