<?php
// 设置当前页面标识
$currentPage = 'attribute_reset_config';

// 页面标题和描述
$pageTitle = '属性重修建筑配置';
$page_description = '管理属性重修建筑的重修物品配置';

ob_start();
?>
<style>
    .building-card {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        background-color: var(--card-bg);
        transition: transform 0.2s, box-shadow 0.2s;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }

    .building-card:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: var(--primary-color);
        opacity: 0.7;
    }

    .building-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }

    .building-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        padding: 10px;
    }

    .building-card h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: var(--primary-color);
        border-bottom: 1px dashed rgba(0,0,0,0.1);
        padding-bottom: 8px;
    }

    .building-type {
        display: inline-block;
        background-color: var(--secondary-color);
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        margin-bottom: 10px;
    }

    .building-description {
        color: var(--text-color);
        margin-bottom: 15px;
        font-size: 0.9em;
    }

    .building-scenes {
        margin-bottom: 15px;
        font-size: 0.9em;
        border-top: 1px solid var(--border-color, #eee);
        padding-top: 10px;
    }

    .config-status {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        padding: 10px;
        border-radius: 5px;
    }

    .config-status.configured {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }

    .config-status.not-configured {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }

    .btn-icon.remove-scene {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 16px;
        padding: 2px 5px;
        border-radius: 3px;
        transition: all 0.2s;
    }

    .btn-icon.remove-scene:hover {
        color: #f44336;
        background-color: rgba(244, 67, 54, 0.1);
    }

    .header-description {
        margin-top: 5px;
        color: #6c757d;
        font-size: 0.9em;
    }

    .building-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 15px;
    }

    .config-item {
        margin-bottom: 8px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .config-label {
        font-weight: 500;
        color: #495057;
    }

    .config-value {
        color: #6c757d;
        font-weight: normal;
    }

    .status-icon {
        margin-right: 8px;
        font-size: 1.2em;
    }
    
    .config-details {
        margin-bottom: 15px;
    }
    
    .config-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 8px 0;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .config-item:last-child {
        border-bottom: none;
    }
    
    .config-label {
        font-weight: 500;
        color: #495057;
    }
    
    .config-value {
        color: #6c757d;
        text-align: right;
    }
    
    .building-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
    }
    
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-size: 0.9em;
        font-weight: 500;
        text-decoration: none;
        display: inline-block;
        text-align: center;
        transition: all 0.2s;
    }
    
    .btn-primary {
        background-color: #007bff;
        color: white;
    }
    
    .btn-primary:hover {
        background-color: #0056b3;
    }
    
    .btn-warning {
        background-color: #ffc107;
        color: #212529;
    }
    
    .btn-warning:hover {
        background-color: #e0a800;
    }
    
    .btn-danger {
        background-color: #dc3545;
        color: white;
    }
    
    .btn-danger:hover {
        background-color: #c82333;
    }
    
    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }
    
    .btn-secondary:hover {
        background-color: #545b62;
    }
    
    .empty-state {
        text-align: center;
        padding: 60px 20px;
        color: #6c757d;
    }
    
    .empty-state h3 {
        margin-bottom: 10px;
        color: #495057;
    }
    
    .loading {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    
    /* 状态消息样式 */
    #status-message-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1100;
    }

    .status-message {
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 4px;
        color: white;
        opacity: 1;
        transition: opacity 0.5s;
    }

    .status-message.success {
        background-color: #4CAF50;
    }

    .status-message.error {
        background-color: #F44336;
    }

    .status-message.fade-out {
        opacity: 0;
    }
    
    /* 模态框样式 */
    .modal-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }
    
    .modal-content {
        background: white;
        border-radius: 10px;
        width: 90%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
    }
    
    .modal-header {
        padding: 20px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .modal-header h2 {
        margin: 0;
        color: #495057;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-color);
    }
    
    .modal-body {
        padding: 20px;
    }
    
    .form-group {
        margin-bottom: 20px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: 500;
        color: #495057;
    }
    
    .form-control {
        width: 100%;
        padding: 10px;
        border: 1px solid #ced4da;
        border-radius: 5px;
        font-size: 1em;
        box-sizing: border-box;
    }
    
    .form-control:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }
    
    .form-actions {
        display: flex;
        gap: 10px;
        justify-content: flex-end;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }
    
    .item-search {
        position: relative;
        margin-bottom: 15px;
    }
    
    .search-results {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ced4da;
        border-top: none;
        border-radius: 0 0 5px 5px;
        max-height: 200px;
        overflow-y: auto;
        z-index: 10;
        display: none;
    }
    
    .search-result-item {
        padding: 10px;
        cursor: pointer;
        border-bottom: 1px solid #f1f3f4;
    }
    
    .search-result-item:hover {
        background-color: #f8f9fa;
    }
    
    .search-result-item:last-child {
        border-bottom: none;
    }
    
    .item-name {
        font-weight: 500;
        color: #495057;
    }
    
    .item-description {
        font-size: 0.9em;
        color: #6c757d;
        margin-top: 2px;
    }
</style>
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="attribute_reset_config.js" defer></script>
<?php
$extra_js = ob_get_clean();
require_once 'layout_header.php';
?>

<div class="main-content-area card">
    <div class="card-header">
        <div class="header-description">
            <small>管理属性重修建筑的重修物品配置，设置玩家进行属性重修所需的物品和数量</small>
        </div>
    </div>

    <!-- 卡片视图 -->
    <div id="card-view">
        <div id="buildings-container" class="building-grid">
            <!-- 建筑卡片将通过JavaScript动态添加 -->
            <div class="building-card loading">
                <p style="text-align:center;">正在加载属性重修建筑数据...</p>
            </div>
        </div>
    </div>

</div>

<!-- 配置模态框 -->
<div id="config-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title">配置属性重修</h2>
            <button class="modal-close" onclick="hideConfigModal()">&times;</button>
        </div>
        <div class="modal-body">
            <form id="config-form" onsubmit="handleConfigSubmit(event)">
                <input type="hidden" id="scene-building-id" name="scene_building_id">
                
                <div class="form-group">
                    <label for="item-search">重修物品:</label>
                    <div class="item-search">
                        <input type="text" id="item-search" class="form-control" 
                               placeholder="搜索物品..." autocomplete="off"
                               oninput="searchItems(this.value)" 
                               onfocus="showSearchResults()" 
                               onblur="hideSearchResults()">
                        <div id="search-results" class="search-results"></div>
                    </div>
                    <input type="hidden" id="selected-item-id" name="required_item_id" required>
                    <div id="selected-item-display" style="display: none; margin-top: 10px; padding: 10px; background-color: #e3f2fd; border-radius: 5px;">
                        <div id="selected-item-name" class="item-name"></div>
                        <div id="selected-item-description" class="item-description"></div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="required-quantity">需要数量:</label>
                    <input type="number" id="required-quantity" name="required_quantity" 
                           class="form-control" min="1" value="1" required>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">保存配置</button>
                    <button type="button" class="btn btn-secondary" onclick="hideConfigModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>确认删除</h2>
            <button class="modal-close" onclick="hideDeleteModal()">&times;</button>
        </div>
        <div class="modal-body">
            <p>确定要删除这个属性重修建筑的配置吗？</p>
            <p><strong>注意：</strong>删除后玩家将无法在该建筑进行属性重修，直到重新配置。</p>
            
            <div class="form-actions">
                <button type="button" class="btn btn-danger" onclick="confirmDelete()">确认删除</button>
                <button type="button" class="btn btn-secondary" onclick="hideDeleteModal()">取消</button>
            </div>
        </div>
    </div>
</div>

<?php require_once 'layout_footer.php'; ?>
