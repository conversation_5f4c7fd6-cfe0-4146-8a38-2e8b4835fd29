<?php
// admin/redemption_codes.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

require_once '../config/Database.php';

$pageTitle = '兑换码管理';
$currentPage = 'redemption_codes';

// 处理搜索参数
$search_code = trim($_GET['code'] ?? '');
$search_status = $_GET['status'] ?? 'all'; // all, active, expired, used_up
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    if (!empty($search_code)) {
        $where_conditions[] = "rc.code LIKE ?";
        $params[] = '%' . $search_code . '%';
    }
    
    // 状态过滤
    switch ($search_status) {
        case 'active':
            $where_conditions[] = "rc.is_active = 1 AND (rc.expires_at IS NULL OR rc.expires_at > NOW()) AND rc.current_usage_count < rc.total_usage_limit";
            break;
        case 'expired':
            $where_conditions[] = "rc.expires_at IS NOT NULL AND rc.expires_at <= NOW()";
            break;
        case 'used_up':
            $where_conditions[] = "rc.current_usage_count >= rc.total_usage_limit";
            break;
        case 'inactive':
            $where_conditions[] = "rc.is_active = 0";
            break;
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 查询兑换码列表
    $sql = "SELECT rc.*, 
                   COUNT(rci.id) as item_count,
                   CASE 
                       WHEN rc.is_active = 0 THEN 'inactive'
                       WHEN rc.expires_at IS NOT NULL AND rc.expires_at <= NOW() THEN 'expired'
                       WHEN rc.current_usage_count >= rc.total_usage_limit THEN 'used_up'
                       ELSE 'active'
                   END as status
            FROM redemption_codes rc
            LEFT JOIN redemption_code_items rci ON rc.id = rci.redemption_code_id
            $where_clause
            GROUP BY rc.id
            ORDER BY rc.created_at DESC
            LIMIT ? OFFSET ?";
    
    $params[] = $limit;
    $params[] = $offset;
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $codes = $stmt->fetchAll();
    
    // 获取总数
    $count_sql = "SELECT COUNT(DISTINCT rc.id) FROM redemption_codes rc $where_clause";
    $count_params = array_slice($params, 0, -2); // 移除limit和offset参数
    $count_stmt = $pdo->prepare($count_sql);
    $count_stmt->execute($count_params);
    $total_count = $count_stmt->fetchColumn();
    $total_pages = ceil($total_count / $limit);
    
    // 获取统计信息
    $stats_sql = "SELECT 
                    COUNT(*) as total_codes,
                    SUM(CASE WHEN is_active = 1 AND (expires_at IS NULL OR expires_at > NOW()) AND current_usage_count < total_usage_limit THEN 1 ELSE 0 END) as active_codes,
                    SUM(CASE WHEN expires_at IS NOT NULL AND expires_at <= NOW() THEN 1 ELSE 0 END) as expired_codes,
                    SUM(CASE WHEN current_usage_count >= total_usage_limit THEN 1 ELSE 0 END) as used_up_codes,
                    SUM(current_usage_count) as total_usage
                  FROM redemption_codes";
    $stats_stmt = $pdo->query($stats_sql);
    $stats = $stats_stmt->fetch();
    
} catch (Exception $e) {
    $codes = [];
    $total_count = 0;
    $total_pages = 0;
    $stats = ['total_codes' => 0, 'active_codes' => 0, 'expired_codes' => 0, 'used_up_codes' => 0, 'total_usage' => 0];
    $error_message = '获取兑换码列表失败: ' . $e->getMessage();
}

require_once 'layout_header.php';
?>

<div class="main-content-area card">
    <div class="card-header">
        <h2>兑换码管理</h2>
        <div class="header-description">
            <small>管理游戏兑换码，支持批量生成、物品配置和使用统计</small>
        </div>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-section" style="padding: 15px 20px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
        <div class="stats-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px;">
            <div class="stat-item">
                <div class="stat-number"><?php echo number_format($stats['total_codes']); ?></div>
                <div class="stat-label">总兑换码</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #28a745;"><?php echo number_format($stats['active_codes']); ?></div>
                <div class="stat-label">可用</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #ffc107;"><?php echo number_format($stats['expired_codes']); ?></div>
                <div class="stat-label">已过期</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #dc3545;"><?php echo number_format($stats['used_up_codes']); ?></div>
                <div class="stat-label">已用完</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" style="color: #17a2b8;"><?php echo number_format($stats['total_usage']); ?></div>
                <div class="stat-label">总使用次数</div>
            </div>
        </div>
    </div>
    
    <!-- 搜索和操作区域 -->
    <div class="search-and-actions" style="padding: 20px; border-bottom: 1px solid #dee2e6;">
        <form method="GET" class="search-form" style="display: flex; gap: 15px; align-items: end; flex-wrap: wrap;">
            <div class="form-group">
                <label for="code">兑换码</label>
                <input type="text" name="code" id="code" class="form-control" 
                       placeholder="搜索兑换码" value="<?php echo htmlspecialchars($search_code); ?>">
            </div>
            <div class="form-group">
                <label for="status">状态</label>
                <select name="status" id="status" class="form-control">
                    <option value="all" <?php echo $search_status === 'all' ? 'selected' : ''; ?>>全部</option>
                    <option value="active" <?php echo $search_status === 'active' ? 'selected' : ''; ?>>可用</option>
                    <option value="expired" <?php echo $search_status === 'expired' ? 'selected' : ''; ?>>已过期</option>
                    <option value="used_up" <?php echo $search_status === 'used_up' ? 'selected' : ''; ?>>已用完</option>
                    <option value="inactive" <?php echo $search_status === 'inactive' ? 'selected' : ''; ?>>已禁用</option>
                </select>
            </div>
            <div class="form-group">
                <button type="submit" class="btn btn-primary">搜索</button>
                <a href="redemption_codes.php" class="btn btn-secondary">重置</a>
            </div>
        </form>
        
        <div class="action-buttons" style="margin-top: 15px;">
            <button class="btn btn-success" onclick="showCreateModal()">创建兑换码</button>
            <button class="btn btn-info" onclick="showBatchCreateModal()">批量生成</button>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" style="margin: 20px;">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <!-- 兑换码列表 -->
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 100px;">兑换码</th>
                    <th>名称</th>
                    <th style="width: 80px;">物品数</th>
                    <th style="width: 100px;">使用情况</th>
                    <th style="width: 80px;">状态</th>
                    <th style="width: 120px;">过期时间</th>
                    <th style="width: 120px;">创建时间</th>
                    <th style="width: 150px;">操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($codes)): ?>
                <tr>
                    <td colspan="8" style="text-align: center; padding: 40px; color: #6c757d;">
                        暂无兑换码
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($codes as $code): ?>
                    <tr>
                        <td>
                            <code style="background: #f8f9fa; padding: 2px 6px; border-radius: 3px; font-weight: bold;">
                                <?php echo htmlspecialchars($code['code']); ?>
                            </code>
                        </td>
                        <td>
                            <div style="font-weight: 500;"><?php echo htmlspecialchars($code['name']); ?></div>
                        </td>
                        <td>
                            <span class="badge badge-info"><?php echo $code['item_count']; ?></span>
                        </td>
                        <td>
                            <div style="font-size: 13px;">
                                <div><?php echo $code['current_usage_count']; ?> / <?php echo $code['total_usage_limit']; ?></div>
                                <small class="text-muted">每人限<?php echo $code['player_usage_limit']; ?>次</small>
                            </div>
                        </td>
                        <td>
                            <?php
                            $status_colors = [
                                'active' => 'success',
                                'expired' => 'warning', 
                                'used_up' => 'danger',
                                'inactive' => 'secondary'
                            ];
                            $status_texts = [
                                'active' => '可用',
                                'expired' => '已过期',
                                'used_up' => '已用完', 
                                'inactive' => '已禁用'
                            ];
                            $status = $code['status'];
                            ?>
                            <span class="badge badge-<?php echo $status_colors[$status]; ?>">
                                <?php echo $status_texts[$status]; ?>
                            </span>
                        </td>
                        <td>
                            <?php if ($code['expires_at']): ?>
                                <small><?php echo date('Y-m-d H:i', strtotime($code['expires_at'])); ?></small>
                            <?php else: ?>
                                <small class="text-muted">永不过期</small>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small><?php echo date('Y-m-d H:i', strtotime($code['created_at'])); ?></small>
                        </td>
                        <td>
                            <div class="btn-group">
                                <button class="btn btn-sm btn-primary" onclick="viewCodeDetails(<?php echo $code['id']; ?>)">详情</button>
                                <button class="btn btn-sm btn-secondary" onclick="viewUsageHistory(<?php echo $code['id']; ?>)">使用记录</button>
                                <?php if ($code['status'] === 'active'): ?>
                                    <button class="btn btn-sm btn-warning" onclick="toggleCodeStatus(<?php echo $code['id']; ?>, 0)">禁用</button>
                                <?php elseif ($code['status'] === 'inactive'): ?>
                                    <button class="btn btn-sm btn-success" onclick="toggleCodeStatus(<?php echo $code['id']; ?>, 1)">启用</button>
                                <?php endif; ?>
                            </div>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php if ($total_pages > 1): ?>
    <div class="pagination" style="padding: 20px; text-align: center;">
        <?php
        $query_params = $_GET;
        
        // 上一页
        if ($page > 1):
            $query_params['page'] = $page - 1;
            $prev_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $prev_url; ?>" class="btn btn-secondary">上一页</a>
        <?php endif; ?>
        
        <?php
        // 页码显示逻辑
        $start_page = max(1, $page - 5);
        $end_page = min($total_pages, $page + 5);
        
        for ($i = $start_page; $i <= $end_page; $i++):
            $query_params['page'] = $i;
            $page_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $page_url; ?>" 
               class="btn <?php echo $i === $page ? 'btn-primary' : 'btn-outline-secondary'; ?>"
               style="margin: 0 2px;">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
        
        <?php
        // 下一页
        if ($page < $total_pages):
            $query_params['page'] = $page + 1;
            $next_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $next_url; ?>" class="btn btn-secondary">下一页</a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<style>
.stats-grid .stat-item {
    text-align: center;
    padding: 10px;
    background: white;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 5px;
}

.stat-label {
    font-size: 12px;
    color: #6c757d;
    text-transform: uppercase;
}

.search-form .form-group {
    display: flex;
    flex-direction: column;
    min-width: 120px;
}

.search-form label {
    font-size: 13px;
    margin-bottom: 5px;
    font-weight: 500;
}

.btn-group {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.badge-success { background-color: #28a745; color: white; }
.badge-warning { background-color: #ffc107; color: #212529; }
.badge-danger { background-color: #dc3545; color: white; }
.badge-secondary { background-color: #6c757d; color: white; }
.badge-info { background-color: #17a2b8; color: white; }

.text-muted { color: #6c757d !important; }

.alert {
    padding: 12px 20px;
    border-radius: 4px;
    border: 1px solid transparent;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
</style>

<script src="redemption_codes.js"></script>

<?php require_once 'layout_footer.php'; ?>
