<?php 
$pageTitle = 'PVP战斗日志管理';
$currentPage = 'pvp_battle_logs';
$extra_css = '
    <link rel="stylesheet" href="pvp_battle_logs.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
';
require_once 'layout_header.php'; 
?>

<div class="page-content">
    <div class="filter-section">
        <div class="form-row">
            <div class="form-group">
                <label for="battle-result-filter">战斗结果</label>
                <select id="battle-result-filter" class="form-control">
                    <option value="">全部</option>
                    <option value="challenger_win">挑战者胜利</option>
                    <option value="defender_win">防守者胜利</option>
                    <option value="draw">平局</option>
                    <option value="timeout">超时</option>
                    <option value="surrender">投降</option>
                </select>
            </div>
            <div class="form-group">
                <label for="date-range-start">开始日期</label>
                <input type="date" id="date-range-start" class="form-control">
            </div>
            <div class="form-group">
                <label for="date-range-end">结束日期</label>
                <input type="date" id="date-range-end" class="form-control">
            </div>
            <div class="form-group">
                <label for="scene-filter">场景</label>
                <select id="scene-filter" class="form-control">
                    <option value="">全部场景</option>
                    <!-- 场景选项将通过JS动态加载 -->
                </select>
            </div>
            <div class="form-group">
                <label for="player-search">玩家搜索</label>
                <input type="text" id="player-search" class="form-control" placeholder="输入玩家名称...">
            </div>
        </div>
        <div class="form-row">
            <button id="search-btn" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
            </button>
            <button id="reset-btn" class="btn btn-secondary">
                <i class="fas fa-redo"></i> 重置
            </button>
        </div>
    </div>

    <div class="cleanup-section">
        <div class="form-group">
            <label for="cleanup-date">清理此日期之前的所有日志:</label>
            <input type="date" id="cleanup-date" class="form-control">
        </div>
        <button id="cleanup-btn" class="btn btn-danger">
            <i class="fas fa-trash-alt"></i> 确认清理
        </button>
    </div>

    <div class="battle-logs-container">
        <div class="logs-header">
            <h3>PVP战斗日志列表</h3>
            <div class="pagination-info">
                显示 <span id="showing-from">0</span> - <span id="showing-to">0</span> 条，共 <span id="total-logs">0</span> 条
            </div>
        </div>
        
        <div class="logs-table-container">
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>战斗时间</th>
                        <th>场景</th>
                        <th>挑战者</th>
                        <th>防守者</th>
                        <th>结果</th>
                        <th>总伤害</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    <!-- 日志数据将通过JS动态加载 -->
                </tbody>
            </table>
        </div>
        
        <div class="pagination-controls">
            <!-- JS将动态生成分页按钮 -->
        </div>
    </div>
</div>

<!-- 战斗日志详情模态框 -->
<div id="logDetailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h2>PVP战斗详情 #<span id="modal-battle-id"></span></h2>
            <span class="modal-close-button">&times;</span>
        </div>
        <div class="modal-body">
            <div class="battle-info">
                <div class="battle-info-section">
                    <h3>基本信息</h3>
                    <table class="info-table">
                        <tr>
                            <td><strong>场景:</strong></td>
                            <td id="modal-scene-name"></td>
                            <td><strong>战斗结果:</strong></td>
                            <td id="modal-battle-result"></td>
                        </tr>
                        <tr>
                            <td><strong>开始时间:</strong></td>
                            <td id="modal-start-time"></td>
                            <td><strong>结束时间:</strong></td>
                            <td id="modal-end-time"></td>
                        </tr>
                        <tr>
                            <td><strong>总伤害:</strong></td>
                            <td id="modal-total-damage"></td>
                            <td><strong>持续时间:</strong></td>
                            <td id="modal-duration"></td>
                        </tr>
                    </table>
                </div>
                
                <div class="battle-info-section">
                    <h3>对战双方</h3>
                    <div class="pvp-players-info">
                        <div class="challenger-info">
                            <h4>挑战者</h4>
                            <p id="modal-challenger"></p>
                        </div>
                        <div class="vs-indicator">VS</div>
                        <div class="defender-info">
                            <h4>防守者</h4>
                            <p id="modal-defender"></p>
                        </div>
                    </div>
                </div>
                
                <div class="battle-info-section">
                    <h3>战斗日志</h3>
                    <div class="battle-log-container">
                        <div id="modal-battle-log"></div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary modal-close">关闭</button>
            <button id="export-log-btn" class="btn btn-primary">导出日志</button>
        </div>
    </div>
</div>

<div id="toast" class="toast"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="pvp_battle_logs.js"></script>

<?php
require_once 'layout_footer.php';
?> 