// 大陆区域管理页面 JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // 全局变量
    let continents = [];
    let zones = [];
    let scenes = [];
    let currentContinent = null;
    let currentZone = null;
    let editingItem = null;

    // DOM 元素
    const continentsList = document.getElementById('continents-list');
    const zonesSection = document.getElementById('zones-section');
    const zonesList = document.getElementById('zones-list');
    const zonesTitle = document.getElementById('zones-title');
    const scenesSection = document.getElementById('scenes-section');
    const scenesList = document.getElementById('scenes-list');
    const scenesTitle = document.getElementById('scenes-title');
    const scenesMap = document.getElementById('scenes-map');
    
    // 按钮事件
    document.getElementById('sync-layers-btn').addEventListener('click', syncLayers);
    document.getElementById('add-continent-btn').addEventListener('click', () => openContinentModal());
    document.getElementById('add-zone-btn').addEventListener('click', () => openZoneModal());
    document.getElementById('close-zones-btn').addEventListener('click', closeZonesSection);
    document.getElementById('close-scenes-btn').addEventListener('click', closeScenesSection);
    document.getElementById('redraw-scenes-map').addEventListener('click', drawScenesMap);
    document.getElementById('back-to-scenes-btn').addEventListener('click', () => {
        window.location.href = 'scenes.php';
    });

    // ID自动生成相关事件
    setupIdAutoGeneration();

    // 表单提交事件
    document.getElementById('continent-form').addEventListener('submit', handleContinentSubmit);
    document.getElementById('zone-form').addEventListener('submit', handleZoneSubmit);

    // 模态框关闭事件
    document.querySelectorAll('.close').forEach(closeBtn => {
        closeBtn.addEventListener('click', function() {
            const modal = this.closest('.modal');
            closeModal(modal.id);
        });
    });

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target.classList.contains('modal')) {
            closeModal(event.target.id);
        }
    });

    // API 调用函数
    async function apiCall(action, data = {}) {
        try {
            const formData = new FormData();
            formData.append('action', action);
            Object.entries(data).forEach(([key, value]) => {
                formData.append(key, value);
            });

            const response = await fetch('api_continents.php', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();
            if (!result.success) {
                showToast(result.message || '操作失败', 'error');
                return null;
            }
            return result;
        } catch (error) {
            console.error('API call failed:', error);
            showToast('网络错误', 'error');
            return null;
        }
    }

    // 加载大陆数据
    async function loadContinents() {
        const result = await apiCall('get_continents');
        if (result) {
            continents = result.continents || [];
            renderContinents();
        }
    }

    // 加载区域数据
    async function loadZones(continentId) {
        const result = await apiCall('get_zones', { continent_id: continentId });
        if (result) {
            zones = result.zones || [];
            renderZones();
        }
    }

    // 加载场景数据
    async function loadScenes(zoneId) {
        const result = await apiCall('get_scenes', { zone_id: zoneId });
        if (result) {
            scenes = result.scenes || [];
            renderScenes();
            autoAdjustMapRange();
            drawScenesMap();
        }
    }

    // 自动调整地图范围
    function autoAdjustMapRange() {
        if (scenes.length === 0) return;

        const xs = scenes.map(s => s.x);
        const ys = scenes.map(s => s.y);

        const minX = Math.min(...xs);
        const maxX = Math.max(...xs);
        const minY = Math.min(...ys);
        const maxY = Math.max(...ys);

        // 添加一些边距
        const padding = 2;

        document.getElementById('scene-min-x').value = minX - padding;
        document.getElementById('scene-max-x').value = maxX + padding;
        document.getElementById('scene-min-y').value = minY - padding;
        document.getElementById('scene-max-y').value = maxY + padding;
    }

    // 渲染大陆列表
    function renderContinents() {
        if (continents.length === 0) {
            continentsList.innerHTML = `
                <div class="empty-state">
                    <h3>暂无大陆</h3>
                    <p>点击"添加大陆"按钮创建第一个大陆</p>
                </div>
            `;
            return;
        }

        continentsList.innerHTML = continents.map(continent => `
            <div class="continent-card ${continent.is_active == 0 ? 'inactive' : ''}">
                <div class="continent-header">
                    <div>
                        <h3 class="continent-title">${escapeHtml(continent.display_name)}</h3>
                        <div class="continent-id">${escapeHtml(continent.id)}</div>
                    </div>
                    <div class="continent-color" style="background-color: ${continent.color}"></div>
                </div>
                <div class="continent-description">${escapeHtml(continent.description || '暂无描述')}</div>
                <div class="continent-stats">
                    <div class="stat-item">
                        <div class="stat-value">${continent.zone_count || 0}</div>
                        <div class="stat-label">区域数量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${continent.scene_count || 0}</div>
                        <div class="stat-label">场景数量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">Z=${continent.z_level || 0}</div>
                        <div class="stat-label">Z坐标层</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${continent.sort_order}</div>
                        <div class="stat-label">排序</div>
                    </div>
                </div>
                <div class="continent-actions">
                    <button class="btn btn-sm btn-primary" onclick="viewZones('${continent.id}', '${escapeHtml(continent.display_name)}')">
                        管理区域
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editContinent('${continent.id}')">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteContinent('${continent.id}', '${escapeHtml(continent.display_name)}')">
                        删除
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 渲染区域列表
    function renderZones() {
        if (zones.length === 0) {
            zonesList.innerHTML = `
                <div class="empty-state">
                    <h3>暂无区域</h3>
                    <p>点击"添加区域"按钮为此大陆创建第一个区域</p>
                </div>
            `;
            return;
        }

        zonesList.innerHTML = zones.map(zone => `
            <div class="zone-card">
                <div class="zone-header">
                    <div>
                        <h4 class="zone-title">${escapeHtml(zone.name)}</h4>
                        <div class="zone-id">${escapeHtml(zone.id)}</div>
                    </div>
                </div>
                <div class="zone-description">${escapeHtml(zone.description || '暂无描述')}</div>
                <div class="zone-level-range">
                    <span>等级范围:</span>
                    <span>${zone.min_level} - ${zone.max_level || '∞'}</span>
                </div>
                <div class="zone-stats">
                    <div class="stat-item">
                        <div class="stat-value">${zone.scene_count || 0}</div>
                        <div class="stat-label">场景数量</div>
                    </div>
                </div>
                <div class="zone-actions">
                    <button class="btn btn-sm btn-primary" onclick="viewScenes('${zone.id}', '${escapeHtml(zone.name)}')">
                        查看场景
                    </button>
                    <button class="btn btn-sm btn-warning" onclick="editZone('${zone.id}')">
                        编辑
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="deleteZone('${zone.id}', '${escapeHtml(zone.name)}')">
                        删除
                    </button>
                </div>
            </div>
        `).join('');
    }

    // 渲染场景列表
    function renderScenes() {
        // 更新统计信息
        updateScenesStats();

        if (scenes.length === 0) {
            scenesList.innerHTML = `
                <div class="empty-state">
                    <h3>暂无场景</h3>
                    <p>此区域还没有场景数据</p>
                </div>
            `;
            return;
        }

        scenesList.innerHTML = scenes.map(scene => `
            <div class="scene-item ${scene.is_safe_zone == 1 ? 'safe-zone' : ''}">
                <div class="scene-name">${escapeHtml(scene.name)}</div>
                <div class="scene-id">${escapeHtml(scene.id)}</div>
                <div class="scene-coordinates">坐标: (${scene.x}, ${scene.y}, ${scene.z})</div>
                <div class="scene-description">${escapeHtml(scene.description || '暂无描述')}</div>
            </div>
        `).join('');
    }

    // 更新场景统计信息
    function updateScenesStats() {
        const totalScenes = scenes.length;
        const safeZones = scenes.filter(scene => scene.is_safe_zone == 1).length;
        const layers = [...new Set(scenes.map(scene => scene.z))].length;

        document.getElementById('total-scenes').textContent = totalScenes;
        document.getElementById('safe-zones').textContent = safeZones;
        document.getElementById('layers-count').textContent = layers;
    }

    // 绘制场景地图
    function drawScenesMap() {
        if (scenes.length === 0) {
            scenesMap.innerHTML = `
                <div class="empty-state">
                    <h3>暂无场景</h3>
                    <p>此区域还没有场景数据</p>
                </div>
            `;
            return;
        }

        // 获取地图范围
        const minX = parseInt(document.getElementById('scene-min-x').value);
        const minY = parseInt(document.getElementById('scene-min-y').value);
        const maxX = parseInt(document.getElementById('scene-max-x').value);
        const maxY = parseInt(document.getElementById('scene-max-y').value);
        const currentZ = parseInt(document.getElementById('scene-layer-select').value);

        // 过滤当前图层的场景
        const currentLayerScenes = scenes.filter(scene => scene.z == currentZ);

        // 创建场景映射
        const sceneMap = new Map();
        currentLayerScenes.forEach(scene => {
            const key = `${scene.x},${scene.y}`;
            sceneMap.set(key, scene);
        });

        // 计算网格尺寸
        const width = maxX - minX + 1;
        const height = maxY - minY + 1;

        // 创建网格HTML
        let gridHTML = `<div class="scene-grid" style="grid-template-columns: repeat(${width}, 40px); grid-template-rows: repeat(${height}, 40px);">`;

        for (let y = maxY; y >= minY; y--) {
            for (let x = minX; x <= maxX; x++) {
                const key = `${x},${y}`;
                const scene = sceneMap.get(key);

                if (scene) {
                    const safeZoneClass = scene.is_safe_zone == 1 ? 'safe-zone' : '';
                    gridHTML += `
                        <div class="scene-cell has-scene ${safeZoneClass}"
                             data-x="${x}" data-y="${y}" data-z="${currentZ}"
                             data-scene-id="${scene.id}"
                             onclick="goToSceneManagement('${scene.id}')"
                             title="${escapeHtml(scene.name)} (${x},${y},${currentZ})">
                            <div class="scene-tooltip">
                                ${escapeHtml(scene.name)}<br>
                                (${x},${y},${currentZ})<br>
                                ${scene.is_safe_zone == 1 ? '安全区' : ''}<br>
                                <small>点击跳转到场景管理</small>
                            </div>
                        </div>
                    `;
                } else {
                    gridHTML += `
                        <div class="scene-cell"
                             data-x="${x}" data-y="${y}" data-z="${currentZ}"
                             title="空地 (${x},${y},${currentZ})">
                        </div>
                    `;
                }
            }
        }

        gridHTML += '</div>';
        scenesMap.innerHTML = gridHTML;

        // 更新图层选择器
        updateLayerSelector();
    }

    // 更新图层选择器
    function updateLayerSelector() {
        const layerSelect = document.getElementById('scene-layer-select');
        const layers = [...new Set(scenes.map(scene => scene.z))].sort((a, b) => a - b);

        const currentValue = layerSelect.value;
        layerSelect.innerHTML = '';

        layers.forEach(z => {
            const option = document.createElement('option');
            option.value = z;
            option.textContent = `Z=${z}`;
            layerSelect.appendChild(option);
        });

        // 恢复选中的图层，如果存在的话
        if (layers.includes(parseInt(currentValue))) {
            layerSelect.value = currentValue;
        } else if (layers.length > 0) {
            layerSelect.value = layers[0];
        }

        // 添加图层切换事件
        layerSelect.addEventListener('change', drawScenesMap);
    }

    // 查看区域
    function viewZones(continentId, continentName) {
        currentContinent = continentId;
        zonesTitle.textContent = `${continentName} - 区域管理`;
        zonesSection.style.display = 'block';
        loadZones(continentId);

        // 滚动到区域管理区域
        zonesSection.scrollIntoView({ behavior: 'smooth' });
    }
    window.viewZones = viewZones;

    // 关闭区域管理区域
    function closeZonesSection() {
        zonesSection.style.display = 'none';
        currentContinent = null;
        zones = [];
    }

    // 查看场景
    function viewScenes(zoneId, zoneName) {
        currentZone = zoneId;

        // 获取区域所属的大陆信息
        const zone = zones.find(z => z.id === zoneId);
        const continent = continents.find(c => c.id === zone?.continent);
        const continentName = continent?.display_name || '未知大陆';
        const zLevel = continent?.z_level || 0;

        scenesTitle.textContent = `${continentName} > ${zoneName} - 场景管理 (Z=${zLevel})`;
        scenesSection.style.display = 'block';
        loadScenes(zoneId);

        // 滚动到场景管理区域
        scenesSection.scrollIntoView({ behavior: 'smooth' });
    }
    window.viewScenes = viewScenes;

    // 关闭场景管理区域
    function closeScenesSection() {
        scenesSection.style.display = 'none';
        currentZone = null;
        scenes = [];
    }

    // 跳转到场景管理页面
    function goToSceneManagement(sceneId) {
        // 构建跳转URL，包含场景ID参数
        const url = `scenes.php?scene=${encodeURIComponent(sceneId)}`;
        window.open(url, '_blank');
    }
    window.goToSceneManagement = goToSceneManagement;

    // 打开大陆模态框
    function openContinentModal(continent = null) {
        editingItem = continent;
        const modal = document.getElementById('continent-modal');
        const title = document.getElementById('continent-modal-title');
        const form = document.getElementById('continent-form');

        if (continent) {
            title.textContent = '编辑大陆';
            fillContinentForm(continent);
        } else {
            title.textContent = '添加大陆';
            form.reset();
            document.getElementById('continent-color').value = '#007bff';
            document.getElementById('continent-sort-order').value = '0';
            document.getElementById('continent-is-active').value = '1';

            // 获取下一个可用的Z坐标作为提示
            loadNextZLevel();
        }

        modal.style.display = 'block';
    }

    // 加载下一个可用的Z坐标
    async function loadNextZLevel() {
        const result = await apiCall('get_next_z_level');
        if (result) {
            const zLevelInput = document.getElementById('continent-z-level');
            zLevelInput.placeholder = `留空自动分配 (建议: ${result.next_z_level})`;

            // 添加提示文本
            const hint = zLevelInput.parentNode.querySelector('.form-hint');
            if (hint) {
                hint.textContent = `留空则自动分配Z坐标 ${result.next_z_level}`;
            }
        }
    }

    // 同步大陆图层到场景管理系统
    async function syncLayers() {
        showConfirmDialog(
            '确定要同步所有大陆图层到场景管理系统吗？\n\n这将会：\n• 为每个大陆在scene_layers表中创建对应的图层记录\n• 更新现有图层的名称和描述\n• 确保场景管理界面能正确显示所有大陆层级',
            async () => {
                const result = await apiCall('sync_layers');
                if (result) {
                    showToast(result.message, 'success');

                    // 提示用户刷新场景管理页面
                    setTimeout(() => {
                        showConfirmDialog(
                            '图层同步完成！\n\n建议现在刷新场景管理页面以查看更新后的图层。\n\n是否要打开场景管理页面？',
                            () => {
                                window.open('scenes.php', '_blank');
                            }
                        );
                    }, 2000);
                }
            }
        );
    }

    // 打开区域模态框
    function openZoneModal(zone = null) {
        if (!currentContinent && !zone) {
            showToast('请先选择一个大陆', 'error');
            return;
        }
        
        editingItem = zone;
        const modal = document.getElementById('zone-modal');
        const title = document.getElementById('zone-modal-title');
        const form = document.getElementById('zone-form');
        
        if (zone) {
            title.textContent = '编辑区域';
            fillZoneForm(zone);
        } else {
            title.textContent = '添加区域';
            form.reset();
            document.getElementById('zone-continent').value = currentContinent;
            document.getElementById('zone-min-level').value = '1';
        }
        
        modal.style.display = 'block';
    }

    // 填充大陆表单
    function fillContinentForm(continent) {
        document.getElementById('continent-id').value = continent.id;
        document.getElementById('continent-name').value = continent.id;
        document.getElementById('continent-display-name').value = continent.display_name;
        document.getElementById('continent-description').value = continent.description || '';
        document.getElementById('continent-color').value = continent.color || '#007bff';
        document.getElementById('continent-z-level').value = continent.z_level || '';
        document.getElementById('continent-sort-order').value = continent.sort_order || 0;
        document.getElementById('continent-is-active').value = continent.is_active || 1;

        // 编辑时设置ID字段为只读但不禁用
        const idInput = document.getElementById('continent-name');
        idInput.readOnly = true;
        idInput.style.backgroundColor = '#f8f9fa';

        // 更新按钮状态
        const editBtn = document.getElementById('edit-continent-id');
        if (editBtn) {
            editBtn.textContent = '✏️ 手动编辑';
            editBtn.title = '手动编辑ID';
        }
    }

    // 填充区域表单
    function fillZoneForm(zone) {
        document.getElementById('zone-id').value = zone.id;
        document.getElementById('zone-name').value = zone.id;
        document.getElementById('zone-continent').value = zone.continent;
        document.getElementById('zone-display-name').value = zone.name;
        document.getElementById('zone-description').value = zone.description || '';
        document.getElementById('zone-min-level').value = zone.min_level || 1;
        document.getElementById('zone-max-level').value = zone.max_level || '';

        // 编辑时设置ID字段为只读但不禁用
        const idInput = document.getElementById('zone-name');
        idInput.readOnly = true;
        idInput.style.backgroundColor = '#f8f9fa';

        // 更新按钮状态
        const editBtn = document.getElementById('edit-zone-id');
        if (editBtn) {
            editBtn.textContent = '✏️ 手动编辑';
            editBtn.title = '手动编辑ID';
        }
    }

    // 处理大陆表单提交
    async function handleContinentSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());

        // 处理Z坐标：如果为空，则不发送该字段（让后端自动分配）
        if (!data.z_level || data.z_level.trim() === '') {
            delete data.z_level;
        } else {
            // 验证Z坐标是否为有效数字
            const zLevel = parseInt(data.z_level);
            if (isNaN(zLevel) || zLevel < 0) {
                showToast('Z坐标必须是大于等于0的整数', 'error');
                return;
            }
            data.z_level = zLevel;
        }

        const action = editingItem ? 'update_continent' : 'create_continent';
        const result = await apiCall(action, data);

        if (result) {
            showToast(editingItem ? '大陆更新成功' : '大陆创建成功', 'success');
            closeModal('continent-modal');
            loadContinents();
        }
    }

    // 处理区域表单提交
    async function handleZoneSubmit(e) {
        e.preventDefault();
        
        const formData = new FormData(e.target);
        const data = Object.fromEntries(formData.entries());
        
        const action = editingItem ? 'update_zone' : 'create_zone';
        const result = await apiCall(action, data);
        
        if (result) {
            showToast(editingItem ? '区域更新成功' : '区域创建成功', 'success');
            closeModal('zone-modal');
            loadZones(currentContinent);
            loadContinents(); // 刷新大陆统计
        }
    }

    // 编辑大陆
    function editContinent(continentId) {
        const continent = continents.find(c => c.id === continentId);
        if (continent) {
            openContinentModal(continent);
        }
    }
    window.editContinent = editContinent;

    // 编辑区域
    function editZone(zoneId) {
        const zone = zones.find(z => z.id === zoneId);
        if (zone) {
            openZoneModal(zone);
        }
    }
    window.editZone = editZone;

    // 删除大陆
    function deleteContinent(continentId, continentName) {
        const continent = continents.find(c => c.id === continentId);
        const zLevel = continent?.z_level || 0;
        const sceneCount = continent?.scene_count || 0;

        let message = `确定要删除大陆"${continentName}"吗？\n\n`;
        message += `• 大陆Z坐标层级: ${zLevel}\n`;
        message += `• 包含场景数量: ${sceneCount}\n\n`;

        if (sceneCount > 0) {
            // 使用简短的Toast消息
            showToast(`无法删除：该大陆包含 ${sceneCount} 个场景`, 'error');

            // 显示详细的确认对话框
            showConfirmDialog(
                `无法删除大陆"${continentName}"！\n\n• 大陆Z坐标层级: ${zLevel}\n• 包含场景数量: ${sceneCount}\n\n⚠️ 请先删除该Z层的所有场景，然后再删除大陆。`,
                () => {
                    // 用户点击确认后不执行任何操作，只是关闭对话框
                }
            );
            return;
        }

        message += `删除大陆将同时删除其下所有区域！`;

        showConfirmDialog(
            message,
            async () => {
                const result = await apiCall('delete_continent', { id: continentId });
                if (result) {
                    showToast('大陆删除成功', 'success');
                    loadContinents();
                    if (currentContinent === continentId) {
                        closeZonesSection();
                    }
                }
            }
        );
    }
    window.deleteContinent = deleteContinent;

    // 删除区域
    function deleteZone(zoneId, zoneName) {
        showConfirmDialog(
            `确定要删除区域"${zoneName}"吗？\n\n删除区域将同时删除其下所有场景！`,
            async () => {
                const result = await apiCall('delete_zone', { id: zoneId });
                if (result) {
                    showToast('区域删除成功', 'success');
                    loadZones(currentContinent);
                    loadContinents(); // 刷新大陆统计
                }
            }
        );
    }
    window.deleteZone = deleteZone;

    // 显示确认对话框
    function showConfirmDialog(message, onConfirm) {
        const modal = document.getElementById('confirm-modal');
        const messageEl = document.getElementById('confirm-message');
        const confirmBtn = document.getElementById('confirm-delete-btn');
        
        messageEl.textContent = message;
        
        // 移除之前的事件监听器
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
        
        // 添加新的事件监听器
        newConfirmBtn.addEventListener('click', () => {
            closeModal('confirm-modal');
            onConfirm();
        });
        
        modal.style.display = 'block';
    }

    // 关闭模态框
    function closeModal(modalId) {
        const modal = document.getElementById(modalId);
        modal.style.display = 'none';

        // 重置表单
        if (modalId === 'continent-modal') {
            document.getElementById('continent-form').reset();
            document.getElementById('continent-color').value = '#007bff';
            document.getElementById('continent-sort-order').value = '0';
            document.getElementById('continent-is-active').value = '1';

            // 重置ID字段状态
            const continentIdInput = document.getElementById('continent-name');
            continentIdInput.readOnly = true;
            continentIdInput.style.backgroundColor = '#f8f9fa';
            continentIdInput.placeholder = '将根据显示名称自动生成';

            // 重置按钮状态
            const editBtn = document.getElementById('edit-continent-id');
            if (editBtn) {
                editBtn.textContent = '✏️ 手动编辑';
                editBtn.title = '手动编辑ID';
            }

            editingItem = null;
        } else if (modalId === 'zone-modal') {
            document.getElementById('zone-form').reset();

            // 重置ID字段状态
            const zoneIdInput = document.getElementById('zone-name');
            zoneIdInput.readOnly = true;
            zoneIdInput.style.backgroundColor = '#f8f9fa';
            zoneIdInput.placeholder = '将根据区域名称自动生成';

            // 重置按钮状态
            const editBtn = document.getElementById('edit-zone-id');
            if (editBtn) {
                editBtn.textContent = '✏️ 手动编辑';
                editBtn.title = '手动编辑ID';
            }

            editingItem = null;
        }
    }
    window.closeModal = closeModal;

    // 显示Toast通知 - 使用全局版本
    function showToast(message, type = 'success') {
        // 如果全局Toast函数可用，使用它
        if (typeof window.globalShowToast === 'function') {
            return window.globalShowToast(message, type);
        }

        // 否则使用本地实现作为后备
        const toast = document.getElementById('toast');
        if (!toast) return;

        // 限制消息长度，避免Toast过大
        let displayMessage = message;
        if (message.length > 100) {
            displayMessage = message.substring(0, 97) + '...';
        }

        // 移除换行符，避免布局问题
        displayMessage = displayMessage.replace(/\n/g, ' ');

        toast.textContent = displayMessage;
        toast.className = `toast ${type}`;
        toast.classList.add('show');

        // 根据消息长度调整显示时间
        const duration = message.length > 50 ? 4000 : 3000;

        setTimeout(() => {
            toast.classList.remove('show');
        }, duration);
    }

    // HTML转义函数
    function escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Z坐标输入验证
    function setupZLevelValidation() {
        const zLevelInput = document.getElementById('continent-z-level');
        if (zLevelInput) {
            zLevelInput.addEventListener('input', async function() {
                const value = this.value.trim();
                const hint = this.parentNode.querySelector('.form-hint');

                if (value === '') {
                    // 空值时显示自动分配提示
                    const result = await apiCall('get_next_z_level');
                    if (result && hint) {
                        hint.textContent = `留空则自动分配Z坐标 ${result.next_z_level}`;
                        hint.style.color = '#6c757d';
                    }
                } else if (isNaN(value) || parseInt(value) < 0) {
                    // 无效值
                    if (hint) {
                        hint.textContent = 'Z坐标必须是大于等于0的整数';
                        hint.style.color = '#dc3545';
                    }
                } else {
                    // 检查Z坐标是否已被使用
                    const zLevel = parseInt(value);
                    const usedResult = await apiCall('get_used_z_levels');
                    if (usedResult) {
                        const isUsed = usedResult.used_z_levels.some(item =>
                            item.z_level == zLevel && (!editingItem || item.id !== editingItem.id)
                        );

                        if (hint) {
                            if (isUsed) {
                                const usedBy = usedResult.used_z_levels.find(item => item.z_level == zLevel);
                                hint.textContent = `Z坐标 ${zLevel} 已被大陆"${usedBy.display_name}"使用`;
                                hint.style.color = '#dc3545';
                            } else {
                                hint.textContent = `Z坐标 ${zLevel} 可用`;
                                hint.style.color = '#28a745';
                            }
                        }
                    }
                }
            });
        }
    }

    // 初始化页面
    loadContinents();

    // 设置Z坐标验证（延迟执行，确保DOM已加载）
    setTimeout(setupZLevelValidation, 100);
});

// ID自动生成功能
function setupIdAutoGeneration() {
    // 大陆ID自动生成
    const continentDisplayNameInput = document.getElementById('continent-display-name');
    const continentIdInput = document.getElementById('continent-name');
    const generateContinentIdBtn = document.getElementById('generate-continent-id');
    const editContinentIdBtn = document.getElementById('edit-continent-id');

    // 区域ID自动生成
    const zoneDisplayNameInput = document.getElementById('zone-display-name');
    const zoneIdInput = document.getElementById('zone-name');
    const generateZoneIdBtn = document.getElementById('generate-zone-id');
    const editZoneIdBtn = document.getElementById('edit-zone-id');

    // 大陆显示名称变化时自动生成ID
    if (continentDisplayNameInput && continentIdInput) {
        continentDisplayNameInput.addEventListener('input', function() {
            if (continentIdInput.readOnly && !editingItem) {
                continentIdInput.value = generateIdFromName(this.value, 'continent');
                // 自动验证生成的ID
                setTimeout(validateContinentId, 100);
            }
        });

        // 重新生成大陆ID按钮
        if (generateContinentIdBtn) {
            generateContinentIdBtn.addEventListener('click', function() {
                continentIdInput.value = generateIdFromName(continentDisplayNameInput.value, 'continent');
                validateContinentId();
            });
        }

        // 手动编辑大陆ID按钮
        if (editContinentIdBtn) {
            editContinentIdBtn.addEventListener('click', function() {
                toggleIdEditing(continentIdInput, this);
            });
        }
    }

    // 区域显示名称变化时自动生成ID
    if (zoneDisplayNameInput && zoneIdInput) {
        zoneDisplayNameInput.addEventListener('input', function() {
            if (zoneIdInput.readOnly && !editingItem) {
                zoneIdInput.value = generateIdFromName(this.value, 'zone');
                // 自动验证生成的ID
                setTimeout(validateZoneId, 100);
            }
        });

        // 重新生成区域ID按钮
        if (generateZoneIdBtn) {
            generateZoneIdBtn.addEventListener('click', function() {
                zoneIdInput.value = generateIdFromName(zoneDisplayNameInput.value, 'zone');
                validateZoneId();
            });
        }

        // 手动编辑区域ID按钮
        if (editZoneIdBtn) {
            editZoneIdBtn.addEventListener('click', function() {
                toggleIdEditing(zoneIdInput, this);
            });
        }
    }
}

// 根据名称生成ID
function generateIdFromName(name, type) {
    if (!name || name.trim() === '') {
        return '';
    }

    let id = name.trim()
        .toLowerCase()
        .replace(/[\u4e00-\u9fff]/g, '') // 移除中文字符
        .replace(/[^a-z0-9\s]/g, '') // 只保留字母、数字和空格
        .replace(/\s+/g, '_') // 空格替换为下划线
        .replace(/_{2,}/g, '_') // 多个下划线合并为一个
        .replace(/^_|_$/g, ''); // 移除开头和结尾的下划线

    // 大陆ID特殊处理：移除数字，只保留字母和下划线
    if (type === 'continent') {
        id = id.replace(/[0-9]/g, ''); // 移除所有数字
        id = id.replace(/_{2,}/g, '_').replace(/^_|_$/g, ''); // 重新清理下划线
    }

    // 如果生成的ID为空，使用默认前缀
    if (id === '') {
        if (type === 'continent') {
            // 大陆使用字母序列而不是数字
            const letters = 'abcdefghijklmnopqrstuvwxyz';
            const randomLetters = Array.from({length: 6}, () =>
                letters[Math.floor(Math.random() * letters.length)]
            ).join('');
            id = `continent_${randomLetters}`;
        } else {
            // 区域可以使用数字
            const timestamp = Date.now().toString().slice(-6);
            id = `zone_${timestamp}`;
        }
    }

    // 确保ID不超过50个字符
    if (id.length > 50) {
        if (type === 'continent') {
            // 大陆ID截断后添加随机字母
            const letters = 'abcdefghijklmnopqrstuvwxyz';
            const randomLetters = Array.from({length: 2}, () =>
                letters[Math.floor(Math.random() * letters.length)]
            ).join('');
            id = id.substring(0, 47) + '_' + randomLetters;
        } else {
            // 区域ID可以使用数字
            id = id.substring(0, 47) + '_' + Date.now().toString().slice(-2);
        }
    }

    return id;
}

// 切换ID编辑状态
function toggleIdEditing(idInput, button) {
    if (idInput.readOnly) {
        // 切换到编辑模式
        idInput.readOnly = false;
        idInput.style.backgroundColor = '#fff';
        idInput.focus();
        button.textContent = '🔒 锁定';
        button.title = '锁定ID，切换回自动生成模式';
    } else {
        // 切换到只读模式
        idInput.readOnly = true;
        idInput.style.backgroundColor = '#f8f9fa';
        button.textContent = '✏️ 手动编辑';
        button.title = '手动编辑ID';
    }
}

// 验证大陆ID
async function validateContinentId() {
    const idInput = document.getElementById('continent-name');
    const id = idInput.value.trim();

    if (!id) {
        showIdValidationResult(idInput, false, '请输入大陆ID');
        return;
    }

    // 检查ID格式
    if (!/^[a-z_]+$/.test(id)) {
        let errorMsg = 'ID格式不正确：';
        if (/[A-Z]/.test(id)) {
            errorMsg += '不能包含大写字母，';
        }
        if (/[0-9]/.test(id)) {
            errorMsg += '不能包含数字，';
        }
        if (/[^a-zA-Z0-9_]/.test(id)) {
            errorMsg += '不能包含特殊字符，';
        }
        errorMsg = errorMsg.replace(/，$/, '') + '。只能包含小写字母和下划线';
        showIdValidationResult(idInput, false, errorMsg);
        return;
    }

    // 检查ID是否已存在（编辑时排除当前项）
    const existingContinent = continents.find(c => c.id === id && (!editingItem || c.id !== editingItem.id));
    if (existingContinent) {
        showIdValidationResult(idInput, false, `ID "${id}" 已被大陆"${existingContinent.display_name}"使用`);
    } else {
        showIdValidationResult(idInput, true, `ID "${id}" 可用`);
    }
}

// 验证区域ID
async function validateZoneId() {
    const idInput = document.getElementById('zone-name');
    const id = idInput.value.trim();

    if (!id) return;

    // 检查ID格式
    if (!/^[a-z0-9_]+$/.test(id)) {
        showIdValidationResult(idInput, false, 'ID只能包含小写字母、数字和下划线');
        return;
    }

    // 检查ID是否已存在（编辑时排除当前项）
    const existingZone = zones.find(z => z.id === id && (!editingItem || z.id !== editingItem.id));
    if (existingZone) {
        showIdValidationResult(idInput, false, `ID "${id}" 已被区域"${existingZone.name}"使用`);
    } else {
        showIdValidationResult(idInput, true, `ID "${id}" 可用`);
    }
}

// 显示ID验证结果
function showIdValidationResult(input, isValid, message) {
    const hint = input.parentNode.parentNode.querySelector('.form-hint');
    if (hint) {
        hint.textContent = message;
        hint.style.color = isValid ? '#28a745' : '#dc3545';
    }
}
