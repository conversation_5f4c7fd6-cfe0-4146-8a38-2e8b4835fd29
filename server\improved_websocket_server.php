<?php
// server/improved_websocket_server.php

// 启用Swoole协程化
Swoole\Runtime::enableCoroutine(SWOOLE_HOOK_ALL);

require_once '../config/Database.php';
require_once '../config/RedisManager.php';
require_once '../classes/SceneManager.php';
require_once '../classes/BattleSystem.php';
require_once '../classes/MessageProtocol.php';
require_once '../classes/SecureMessageProtocol.php';
require_once '../classes/LootManager.php';
require_once '../classes/QuestManager.php';
require_once '../config/formulas.php';
require_once '../classes/NPCManager.php';
require_once '../classes/CraftingManager.php'; // 引入合成管理器
require_once '../classes/GemCraftingManager.php'; // 引入宝石合成管理器
require_once '../classes/RefineCalculator.php'; // 引入凝练计算器
require_once 'cache_control.php';
require_once 'crafting_handlers.php'; // 引入合成处理函数
require_once 'gem_crafting_handlers.php'; // 引入宝石合成处理函数
require_once 'refine_handlers.php'; // 引入凝练处理函数
require_once 'pvp_handler.php'; // 添加PVP处理模块
require_once '../classes/TradeManager.php'; // 引入交易管理器
require_once 'warehouse_handlers.php'; // 引入仓库处理函数

class SwooleWebSocketServer {
    private $server;
    private $db;
    private $sceneManager;
    private $battleSystem;
    private $lootManager;
    private $npcManager;
    private $questManager;
    private $cacheControl;
    private $running = true;
    private $redisSubscribeChannel;

    // 连接与认证状态管理
    private $fdInfo = []; // fd => ['player_id' => ..., 'secret_key' => ...]
    private $playerConnections = []; // playerId => fd
    
    // 战斗状态管理
    private $monsterBattles = [];    // monsterId => battleState
    private $playerBattleMap = [];   // playerId => monsterId
    private $pvpBattleTimers = []; // 存储PVP战斗定时器的数组
    private $battleTimers = []; // 存储战斗定时器的数组

    private $pvpHandler; // 添加PVP处理器属性
    private $chatHandler; // 添加聊天处理器属性
    private $tradeManager; // 添加交易管理器属性

    public function __construct($host = '0.0.0.0', $port = SERVER_PORT) {
        $this->server = new Swoole\WebSocket\Server($host, $port);
        $this->redisSubscribeChannel = new Co\Channel(1024);
        
        $this->server->set([
            'worker_num' => 1,
            'max_request' => 0,
            'heartbeat_check_interval' => 60,
            'heartbeat_idle_time' => 120,
        ]);
        
        // 信号处理在共享主机环境中通常被禁用，不再使用
        
        $this->server->on('Start', [$this, 'onStart']);
        $this->server->on('WorkerStart', [$this, 'onWorkerStart']);
        $this->server->on('WorkerStop', [$this, 'onWorkerStop']);
        $this->server->on('Open', [$this, 'onOpen']);
        $this->server->on('Message', [$this, 'onMessage']);
        $this->server->on('Close', [$this, 'onClose']);

        // 初始化PVP处理器
        $this->pvpHandler = new PvpHandler($this);
        
        // 初始化聊天处理器
        require_once __DIR__ . '/../classes/ChatHandler.php';
        $this->chatHandler = new ChatHandler($this);

        // 初始化公告处理器
        require_once __DIR__ . '/../classes/AnnouncementHandler.php';
        $this->announcementHandler = new AnnouncementHandler($this);
    }

    public function start() {
        echo "正在启动 Swoole WebSocket 服务器...\n";
        $this->server->start();
    }

    public function onStart(Swoole\WebSocket\Server $server) {
        echo "Swoole WebSocket 服务器启动在 {$server->host}:{$server->port}\n";
    }
    
    public function onWorkerStart(Swoole\WebSocket\Server $server, int $workerId) {
        echo "Worker {$workerId} 已启动，进程ID: " . getmypid() . "\n";
        $this->running = true;
        
        // 初始化数据库和服务
        $db = Database::getInstance();
        $this->db = $db; // 保存数据库连接到类属性
        $redisManager = RedisManager::getInstance();
        $this->sceneManager = new SceneManager($db);
        $this->npcManager = new NPCManager($db);
        $this->battleSystem = new BattleSystem();
        $this->lootManager = new LootManager($db, $redisManager);
        $this->cacheControl = new CacheControl($db, $redisManager);
        $this->tradeManager = new TradeManager(); // 初始化交易管理器
        
        try {
            $db->getConnection();
            echo "Worker {$workerId}: 数据库预连接成功\n";
            $redisManager->with(function($redis){ $redis->ping(); });
            echo "Worker {$workerId}: Redis 预连接成功\n";
        } catch (Exception $e) {
            echo "Worker {$workerId}: 预连接失败: " . $e->getMessage() . "\n";
            $server->shutdown();
            return;
        }
        
        // 0号工作进程启动额外任务
        if ($workerId == 0) {
            // 健康检查
            $server->tick(60000, function () { // 每60秒执行一次
                $this->performHealthCheck();
            });

            // 交易系统清理定时器
            $server->tick(300000, function () { // 每5分钟执行一次
                if ($this->tradeManager) {
                    $cleanedCount = $this->tradeManager->cleanupExpiredTrades();
                    if ($cleanedCount > 0) {
                        echo "清理了 {$cleanedCount} 个过期交易\n";
                    }
                }
            });

            // 场景掉落物品清理定时器
            $server->tick(1800000, function () { // 每30分钟执行一次
                $cleanedCount = $this->cleanupExpiredSceneItems();
                if ($cleanedCount > 0) {
                    echo "清理了 {$cleanedCount} 个超时掉落物品\n";
                }
            });

            // 定期清理未响应的连接
            $server->tick(300000, function () use ($server) { // 每5分钟执行一次
                $this->cleanupStaleConnections($server);
            });
            
            // 每天凌晨1点清理日常任务
            $server->tick(60000, function() { // 每分钟检查一次
                $now = time();
                $currentHour = (int)date('H', $now);
                $currentMinute = (int)date('i', $now);
                
                // 如果当前是凌晨1:00-1:05，执行清理
                if ($currentHour === 1 && $currentMinute < 5) {
                    // 使用Redis作为锁，确保一天只清理一次
                    $today = date('Y-m-d');
                    $cleaned = RedisManager::getInstance()->with(function($redis) use ($today) {
                        $lockKey = "daily_quest_cleanup:{$today}";
                        if (!$redis->exists($lockKey)) {
                            $redis->setex($lockKey, 86400, 1); // 设置锁，有效期一天
                            return false; // 还未清理
                        }
                        return true; // 已清理
                    });
                    
                    if (!$cleaned) {
                        echo "执行每日任务清理 - " . date('Y-m-d H:i:s') . "\n";
                        $this->cleanupDailyQuests();
                    }
                }
            });
            
            // 启动Redis订阅
            $this->startRedisSubscriber();
        }
    }
    
    /**
     * 清理长时间未响应的连接和过期会话
     */
    private function cleanupStaleConnections($server) {
        echo "开始检查过期连接...\n";
        $checkCount = 0;
        $cleanedCount = 0;
        
        // 1. 清理过期的Redis会话
        try {
            // 注意：Redis中的会话过期由TTL管理，无需手动清理
            
            // 2. 验证所有玩家连接的有效性
            foreach ($this->playerConnections as $playerId => $fd) {
                $checkCount++;
                
                // 检查连接是否仍然有效
                if (!isset($server->connections[$fd]) || !$server->isEstablished($fd)) {
                    echo "检测到无效连接: 玩家 {$playerId}, FD {$fd}\n";
                    
                    // 如果在战斗中，移除
                    if (isset($this->playerBattleMap[$playerId])) {
                        $this->removePlayerFromBattle($playerId, 'connection_lost');
                    }
                    
                    // 从场景中移除
                    $this->removePlayerFromScenes($playerId);
                    
                    // 清理映射
                    unset($this->playerConnections[$playerId]);
                    unset($this->fdInfo[$fd]);
                    
                    // 如果玩家在PVP战斗中
                    $battleId = $this->pvpHandler->getPlayerBattleId($playerId);
                    if ($battleId) {
                        $this->pvpHandler->handleSurrender($fd, $playerId, ['battle_id' => $battleId]);
                    }
                    
                    $cleanedCount++;
                }
            }
            
            // 3. 检查场景中的玩家列表，确保所有玩家都有有效连接
            $allScenes = $this->sceneManager->getAllScenes();
            foreach($allScenes as $scene) {
                $sceneId = $scene['id'];
                $playerIds = RedisManager::getInstance()->with(function($redis) use ($sceneId){
                    return $redis->smembers("scene_players:{$sceneId}");
                });
                
                foreach($playerIds as $playerId) {
                    // 如果玩家ID不在playerConnections中或连接无效，则从场景中移除
                    if(!isset($this->playerConnections[$playerId]) || 
                       !isset($server->connections[$this->playerConnections[$playerId]]) ||
                       !$server->isEstablished($this->playerConnections[$playerId])) {
                        
                        RedisManager::getInstance()->with(function($redis) use ($sceneId, $playerId){
                            $redis->srem("scene_players:{$sceneId}", $playerId);
                            $redis->del("player_scene:{$playerId}");
                        });
                        
                        echo "清理场景中的幽灵玩家: {$playerId} 从场景 {$sceneId}\n";
                        $cleanedCount++;
                    }
                }
            }
            
            echo "连接检查完成: 检查了 {$checkCount} 个连接，清理了 {$cleanedCount} 个无效连接。\n";
            
        } catch (Exception $e) {
            echo "清理过期连接时发生错误: " . $e->getMessage() . "\n";
        }
    }

    public function onWorkerStop(Swoole\WebSocket\Server $server, int $workerId) {
        echo "Worker #{$workerId} 正在停止。\n";
        $this->running = false;
        
        // 关闭Redis连接池
        RedisManager::getInstance()->close();
        
        // 关闭通道，这将导致相关协程退出
        $this->redisSubscribeChannel->close();
    }

    /**
     * 启动一个PVP战斗的定时器
     * @param string $battleId
     */
    public function startPvpBattleTimer(string $battleId) {
        if (isset($this->pvpBattleTimers[$battleId])) {
            swoole_timer_clear($this->pvpBattleTimers[$battleId]);
        }

        $this->pvpBattleTimers[$battleId] = swoole_timer_tick(100, function() use ($battleId) {
            if (!isset($this->pvpHandler)) {
                $this->stopPvpBattleTimer($battleId);
                return;
            }
            // pvpHandler 内部会检查战斗是否存在
            $this->pvpHandler->updatePvpBattle($battleId);
        });

        error_log("PVP战斗定时器已启动: {$battleId}");
    }

    /**
     * 停止一个PVP战斗的定时器
     * @param string $battleId
     */
    public function stopPvpBattleTimer(string $battleId) {
        if (isset($this->pvpBattleTimers[$battleId])) {
            swoole_timer_clear($this->pvpBattleTimers[$battleId]);
            unset($this->pvpBattleTimers[$battleId]);
            error_log("PVP战斗定时器已停止: {$battleId}");
        }
    }

    /**
     * 获取所有玩家连接
     * @return array 玩家ID => FD的映射
     */
    public function getPlayerConnections()
    {
        return $this->playerConnections;
    }

    /**
     * 获取玩家的FD
     * @param string $playerId
     * @return int|null
     */
    public function getPlayerFd(string $playerId): ?int
    {
        return $this->playerConnections[$playerId] ?? null;
    }

    private function startRedisSubscriber() {
        go(function() {
            $maxRetries = 3;
            $retryCount = 0;
            $retryDelay = 2; // 初始重试延迟（秒）

            while (true) {
                $redis = new Redis();
                try {
                    // 连接Redis
                    $connected = $redis->connect(redis_host, redis_port, 3);
                    if (!$connected) {
                        throw new Exception("Redis连接失败");
                    }
                    
                    // 设置读取超时为-1，表示永不超时
                    $redis->setOption(Redis::OPT_READ_TIMEOUT, -1);
                    
                    if ($retryCount > 0) {
                        error_log("[Redis] 重试成功 (尝试 {$retryCount}/{$maxRetries})");
                        $retryCount = 0; // 连接成功后重置重试计数
                    }
                    
                    error_log("[Redis] 订阅器已准备就绪");
                    echo "Redis订阅器已准备就绪。\n";
                    
                    // 订阅系统通知频道和任务进度更新频道
                    $redis->subscribe(['game-system-notifications', 'quest-progress-updates'], function ($redis, $channel, $message) {
                        Co\System::sleep(0.01);
                        error_log("[Server] Received notification on channel '{$channel}': {$message}");

                        if ($channel == 'game-system-notifications') {
                            switch ($message) {
                                case 'scenes_updated':
                                    error_log("[Server] scenes_updated notification received. Clearing SceneManager cache.");
                                    $this->sceneManager->clearCache();
                                    break;
                                case 'npcs_updated':
                                    error_log("[Server] npcs_updated notification received. Clearing NPCManager cache.");
                                    $this->npcManager->clearCache();
                                    break;
                                case 'quests_updated':
                                    error_log("[Server] quests_updated notification received. Clearing QuestManager cache.");
                                    if (!isset($this->questManager)) {
                                        $this->questManager = new QuestManager(function() {
                                            return $this->db->getConnection();
                                        });
                                    }
                                    $this->questManager->clearCache();
                                    break;
                                // ... 可以添加其他通知类型
                            }
                        } elseif ($channel == 'quest-progress-updates') {
                            // 处理任务进度更新通知
                            $this->handleQuestProgressNotification($message);
                        }
                    });
                    
                    // 如果订阅循环退出，记录日志
                    error_log("[Redis] 订阅循环已结束，将重新连接");
                    
                } catch (Exception $e) {
                    $retryCount++;
                    
                    if ($retryCount <= $maxRetries) {
                        $waitTime = $retryDelay * $retryCount; // 累进式延迟
                        error_log("[Redis] 连接失败: " . $e->getMessage() . " - 将在 {$waitTime} 秒后重试 ({$retryCount}/{$maxRetries})");
                        Co\System::sleep($waitTime); // 等待一段时间后重试
                    } else {
                        error_log("[Redis] 在 {$maxRetries} 次尝试后仍然无法连接: " . $e->getMessage());
                        echo "Redis订阅器启动失败: " . $e->getMessage() . "\n";
                        
                        // 等待一段较长时间后再次尝试整个连接过程
                        error_log("[Redis] 将在60秒后重新开始连接过程");
                        Co\System::sleep(60);
                        $retryCount = 0; // 重置重试计数
                    }
                } finally {
                    // 确保关闭连接
                    if (isset($redis)) {
                        try {
                            $redis->close();
                        } catch (Exception $e) {
                            // 忽略关闭连接时的错误
                        }
                    }
                    
                    // 短暂延迟后再次尝试连接
                    Co\System::sleep(1);
                }
            }
        });
    }

    public function onOpen(Swoole\WebSocket\Server $server, Swoole\Http\Request $request) {
        echo "WebSocket 握手成功 - 客户端 FD: {$request->fd}\n";
        echo "当前连接数: " . count($server->connections) . "\n";
    }

    /**
     * 处理兑换码兑换请求
     */
    private function handleRedemptionCode($fd, $playerId, $payload) {
        try {
            $code = trim($payload['code'] ?? '');

            if (empty($code)) {
                return $this->sendMessage($fd, MessageProtocol::S2C_REDEEM_CODE_RESULT, [
                    'success' => false,
                    'message' => '请输入兑换码'
                ]);
            }

            // 获取客户端IP地址
            $clientInfo = $this->server->getClientInfo($fd);
            $ipAddress = $clientInfo['remote_ip'] ?? null;

            // 使用兑换码处理类
            require_once __DIR__ . '/../classes/RedemptionCodeHandler.php';
            $redemptionHandler = new RedemptionCodeHandler();

            $result = $redemptionHandler->redeemCode($playerId, $code, $ipAddress);

            // 发送结果给客户端
            $this->sendMessage($fd, MessageProtocol::S2C_REDEEM_CODE_RESULT, $result);

            // 如果兑换成功，记录日志
            if ($result['success']) {
                error_log("玩家 {$playerId} 成功兑换码 {$code}");

                // 检查兑换码任务进度
                $this->checkRedemptionCodeQuestProgress($fd, $playerId, $code, $result);

                // 如果玩家在线，刷新背包数据
                $this->refreshPlayerInventory($fd, $playerId);
            }

        } catch (Exception $e) {
            error_log("兑换码处理错误: " . $e->getMessage());

            $this->sendMessage($fd, MessageProtocol::S2C_REDEEM_CODE_RESULT, [
                'success' => false,
                'message' => '系统错误，请稍后重试'
            ]);
        }
    }

    /**
     * 刷新玩家背包数据
     */
    private function refreshPlayerInventory($fd, $playerId) {
        try {
            // 获取更新后的背包数据
            $db = Database::getInstance();
            $stmt = $db->query(
                "SELECT pi.*, it.name, it.category, it.stackable, it.max_stack, it.description
                 FROM player_inventory pi
                 JOIN item_templates it ON pi.item_template_id = it.id
                 WHERE pi.player_id = ? AND pi.is_equipped = 0
                 ORDER BY it.category, it.name",
                [$playerId]
            );
            $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 发送背包更新消息
            $this->sendMessage($fd, MessageProtocol::S2C_INVENTORY_DATA, [
                'inventory' => $inventory
            ]);

        } catch (Exception $e) {
            error_log("刷新背包数据失败: " . $e->getMessage());
        }
    }

    /**
     * 检查兑换码相关的任务进度
     * @param int $fd WebSocket连接标识符
     * @param int $playerId 玩家ID
     * @param string $code 兑换码
     * @param array $result 兑换结果
     */
    private function checkRedemptionCodeQuestProgress($fd, $playerId, $code, $result) {
        try {
            // 检查兑换获得的物品相关的收集任务
            if (isset($result['items']) && is_array($result['items'])) {
                foreach ($result['items'] as $item) {
                    if (isset($item['item_template_id']) && isset($item['quantity'])) {
                        // 检查收集任务进度
                        $this->checkCollectQuestProgress($fd, $playerId, $item['item_template_id'], $item['quantity']);
                    }
                }
            }

            // 如果有QuestManager，检查兑换码相关的特殊任务
            if (class_exists('QuestManager')) {
                $db = Database::getInstance();
                $questManager = new QuestManager(function() use ($db) {
                    return $db->getConnection();
                });

                // 检查"使用兑换码"类型的任务
                $updatedQuests = $questManager->checkQuestObjectiveUpdates($playerId, 'redeem_code', 1, 1);

                // 检查特定兑换码的任务（如果有的话）
                if (!empty($code)) {
                    $codeSpecificQuests = $questManager->checkQuestObjectiveUpdates($playerId, 'redeem_specific_code', $code, 1);
                    $updatedQuests = array_merge($updatedQuests, $codeSpecificQuests);
                }

                // 如果有任务更新，发送通知
                if (!empty($updatedQuests)) {
                    // 发送任务进度消息
                    foreach ($updatedQuests as $update) {
                        $message = "【任务进度】{$update['title']}: {$update['description']} ({$update['new_progress']}/{$update['target']})";
                        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $message]);

                        // 如果任务可以完成，提示玩家
                        if ($update['can_complete']) {
                            $completeMessage = "【任务完成】任务\"{$update['title']}\"已经可以交付了，请前往{$update['receiver_npc_name']}处交付任务。";
                            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $completeMessage]);
                        }
                    }

                    // 发送任务更新通知
                    $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                        'action' => 'progress_updated',
                        'quests' => $updatedQuests
                    ]);

                    // 更新玩家任务列表
                    $this->handleGetQuestList($fd, ['player_id' => $playerId]);
                }
            }

        } catch (Exception $e) {
            error_log("检查兑换码任务进度失败: " . $e->getMessage());
        }
    }

    public function onClose(Swoole\WebSocket\Server $server, $fd) {
        echo "客户端 FD: {$fd} 关闭连接\n";
        
        // 记录所有会话清理操作
        $cleanupOperations = [];
        
        // 1. 首先检查玩家是否已登录(在fdInfo中)
        if (isset($this->fdInfo[$fd])) {
            $playerId = $this->fdInfo[$fd]['player_id'];
            echo "玩家 {$playerId} 断开连接\n";
            $cleanupOperations[] = "玩家ID: {$playerId} 已确认从fdInfo清理";

            // 清除Redis中的在线状态标记
            RedisManager::getInstance()->with(function($redis) use ($playerId) {
                $redis->del("player_fd:{$playerId}");
                echo "已清除玩家 {$playerId} 的在线状态标记\n";
            });
            $cleanupOperations[] = "玩家Redis在线状态标记已清除";

            // 如果玩家在战斗中，处理离线
            if (isset($this->playerBattleMap[$playerId])) {
                $this->removePlayerFromBattle($playerId, 'disconnect');
                $cleanupOperations[] = "玩家已从战斗中移除";
            }

            // 从场景中移除玩家
            $this->removePlayerFromScenes($playerId);
            $cleanupOperations[] = "玩家已从场景中移除";
            
            // 移除连接映射
            unset($this->playerConnections[$playerId]);
            unset($this->fdInfo[$fd]);
            $cleanupOperations[] = "玩家连接映射已清理";
        } else {
            echo "未登录的连接 FD: {$fd} 断开。\n";
            $cleanupOperations[] = "未登录连接断开";
        }
        
        // 2. 检查玩家是否在PVP战斗中(在fdToPlayerId中)
        if (isset($this->fdToPlayerId[$fd])) {
            $playerId = $this->fdToPlayerId[$fd];
            $cleanupOperations[] = "玩家ID: {$playerId} 已确认从fdToPlayerId清理";
            
            $battleId = $this->pvpHandler->getPlayerBattleId($playerId);
            
            if ($battleId) {
                // 玩家断线视为投降
                $this->pvpHandler->handleSurrender($fd, $playerId, ['battle_id' => $battleId]);
                $cleanupOperations[] = "玩家PVP战斗已作为投降处理";
            }
            
            // 清理映射
            unset($this->fdToPlayerId[$fd]);
        }
        
        // 3. 双重检查Redis场景数据，确保玩家已从所有场景中移除
        try {
            $allScenes = $this->sceneManager->getAllScenes();
            foreach($allScenes as $scene) {
                $sceneId = $scene['id'];
                $playerIds = RedisManager::getInstance()->with(function($redis) use ($sceneId){
                    return $redis->smembers("scene_players:{$sceneId}");
                });
                
                // 如果有玩家ID与此连接关联，强制移除
                foreach($playerIds as $checkId) {
                    if(isset($this->playerConnections[$checkId]) && $this->playerConnections[$checkId] == $fd) {
                        RedisManager::getInstance()->with(function($redis) use ($sceneId, $checkId){
                            $redis->srem("scene_players:{$sceneId}", $checkId);
                            $redis->del("player_scene:{$checkId}");
                        });
                        $cleanupOperations[] = "额外检查: 玩家 {$checkId} 从场景 {$sceneId} 强制清理";
                    }
                }
            }
        } catch (Exception $e) {
            echo "Redis额外清理失败: " . $e->getMessage() . "\n";
        }
        
        echo "连接清理操作: " . implode(", ", $cleanupOperations) . "\n";
        echo "当前连接数: " . count($server->connections) . "\n";
    }

    public function onMessage(Swoole\WebSocket\Server $server, Swoole\WebSocket\Frame $frame) {
        $fd = $frame->fd;
        $binaryMsg = $frame->data;

        if ($frame->opcode !== WEBSOCKET_OPCODE_BINARY || empty($binaryMsg)) {
            return;
        }

        if (isset($this->fdInfo[$fd])) {
            $this->handleAuthenticatedMessage($fd, $binaryMsg);
        } else {
            $this->handleUnauthenticatedMessage($fd, $binaryMsg);
        }
    }
    
    private function handleUnauthenticatedMessage($fd, $binaryMsg) {
        try {
            $data = MessageProtocol::decode($binaryMsg);
            if (!$data) { 
                // Invalid message format, close connection
                $this->server->close($fd);
                return;
            }
            
            $opcode = $data['type'];
            
            switch ($opcode) {
                case MessageProtocol::C2S_LOGIN:
                    $this->handleLogin($fd, $data);
                break;
                case MessageProtocol::C2S_REGISTER:
                    $this->handleRegister($fd, $data);
                break;
                case MessageProtocol::C2S_RESUME_SESSION:
                    $this->handleResumeSession($fd, $data);
                    break;
                case MessageProtocol::C2S_LOGOUT:
                    $this->handleLogout($fd);
                    break;
                default:
                    // Unknown opcode, close connection
                    $this->server->close($fd);
            }
        } catch(Throwable $t) {
            error_log("Error handling unauthenticated message from FD {$fd}: " . $t->getMessage() . " in " . $t->getFile() . ":" . $t->getLine());
            // Send a generic error to the client before closing
            $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => 'Server processing error.']);
            $this->server->close($fd);
        }
    }
    
    private function handleAuthenticatedMessage($fd, $binaryMsg) {
        try {
            $secretKey = $this->fdInfo[$fd]['secret_key'];
            $data = SecureMessageProtocol::decode($binaryMsg, $secretKey);
            
            if (!$data) { echo "解密失败 FD {$fd}\n"; return; }

            $opcode = $data['opcode'] ?? null;
            $payload = $data['payload'] ?? [];
            $playerId = $this->fdInfo[$fd]['player_id'];
            $payload['player_id'] = $playerId;

            if ($opcode !== MessageProtocol::PING) {
                echo "收到来自玩家 {$playerId} (FD {$fd}) 的加密消息 Opcode: 0x" . dechex($opcode) . "\n";
            }
            
            // 检查玩家是否在PVE战斗中
            if (isset($this->playerBattleMap[$playerId]) && 
                !in_array($opcode, [
                    MessageProtocol::C2S_BATTLE_ACTION, 
                    MessageProtocol::C2S_FLEE_BATTLE, 
                    MessageProtocol::C2S_MONSTER_ATTACK,
                    MessageProtocol::PING, 
                    MessageProtocol::C2S_ENTER_SCENE, 
                    MessageProtocol::C2S_MOVE
                ])) {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你正在战斗中，无法执行此操作！']);
                return;
            }
            
            // 检查玩家是否在PVP战斗中
            $pvpBattleId = $this->pvpHandler->getPlayerBattleId($playerId);
            if ($pvpBattleId && 
                !in_array($opcode, [
                    MessageProtocol::C2S_PVP_SET_INTENTION,
                    MessageProtocol::C2S_PVP_ACTION,
                    MessageProtocol::C2S_PVP_SURRENDER,
                    MessageProtocol::C2S_GET_BATTLE_SKILLS,
                    MessageProtocol::PING,
                    MessageProtocol::C2S_LOGOUT
                ])) {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你正在PVP战斗中，无法执行此操作！']);
                return;
            }
            


            switch ($opcode) {
                case MessageProtocol::PING:
                    $this->sendMessage($fd, MessageProtocol::PONG, ['timestamp' => time()]);
                    break;
                case MessageProtocol::C2S_MOVE:
                    $this->handleMove($fd, $payload);
                    break;
                case MessageProtocol::C2S_ENTER_SCENE:
                    list($responseOpcode, $responsePayload) = $this->handleEnterScene($fd, $payload);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;
                case MessageProtocol::C2S_HEAL:
                    $this->handleHeal($payload);
                    break;
                case MessageProtocol::C2S_GET_SCENES:
                    list($responseOpcode, $responsePayload) = $this->handleGetScenes();
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;
                case MessageProtocol::C2S_RESET_SCENE:
                    $this->handleResetScene($payload);
                    break;
                case MessageProtocol::C2S_REVIVE:
                    $this->handleReviveRequest($fd, $payload);
                    break;
                case MessageProtocol::C2S_REVIVE_CITY:
                    $this->handleRevivecityRequest($fd, $payload);
                    break;
                case MessageProtocol::C2S_REVIVE_IN_BUILDING:
                    $this->handleReviveInBuilding($fd, $payload);
                    break;
                case MessageProtocol::C2S_TELEPORT:
                    $this->handleTeleport($fd, $payload);
                    break;
                case MessageProtocol::C2S_START_BATTLE:
                    $this->handleStartBattle($fd, $payload);
                    break;
                case MessageProtocol::C2S_BATTLE_ACTION:
                    $this->handleBattleAction($fd, $payload);
                    break;
                case MessageProtocol::C2S_FLEE_BATTLE:
                    $this->handleFleeBattle($fd, $payload);
                    break;
                case MessageProtocol::C2S_MONSTER_ATTACK:
                    $this->handleMonsterAttackRequest($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_INVENTORY:
                    $this->handleGetInventory($fd, $payload);
                    break;
                case MessageProtocol::C2S_USE_ITEM:
                    $this->handleUseItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_EQUIP_ITEM:
                    $this->handleEquipItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_UNEQUIP_ITEM:
                    $this->handleUnequipItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_SOCKET_GEM:
                    $this->handleSocketGem($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_MONSTER_DETAILS:
                    $this->handleGetMonsterDetails($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_PLAYER_DETAILS:
                    $this->handleGetPlayerDetails($fd, $payload);
                    break;
                case MessageProtocol::C2S_PICKUP_ITEM:
                    $this->handlePickupItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_ALLOCATE_POTENTIAL_POINTS:
                    $this->handleAllocatePotentialPoints($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_COMBAT_POTION_CONFIG:
                    $this->handleGetCombatPotionConfig($fd, $payload);
                    break;
                case MessageProtocol::C2S_SET_COMBAT_POTION_CONFIG:
                    $this->handleSetCombatPotionConfig($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_PLAYER_SKILLS:
                    $this->handleGetPlayerSkills($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_BATTLE_SKILLS:
                    $this->handleGetBattleSkills($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_BUILDING_DATA:
                    $this->handleGetBuildingData($fd, $payload);
                    break;
                case MessageProtocol::C2S_BUY_ITEM:
                    $this->handleBuyItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_BUY_DIAMOND_ITEM:
                    $this->handleBuyDiamondItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_SELL_ITEM:
                    $this->handleSellItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_TALK_TO_NPC:
                    $this->handleTalkToNpc($fd, $payload);
                    break;
                case MessageProtocol::C2S_CONTINUE_DIALOGUE:
                    $this->handleContinueDialogue($fd, $payload);
                    break;
                case MessageProtocol::C2S_SELECT_DIALOGUE_OPTION:
                    $this->handleSelectDialogueOption($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_QUEST_LIST:
                    $this->handleGetQuestList($fd, $payload);
                    break;
                case MessageProtocol::C2S_ACCEPT_QUEST:
                    $this->handleAcceptQuest($fd, $payload);
                    break;
                case MessageProtocol::C2S_COMPLETE_QUEST:
                    $this->handleCompleteQuest($fd, $payload);
                    break;
                case MessageProtocol::C2S_ABANDON_QUEST:
                    $this->handleAbandonQuest($fd, $payload);
                    break;
                case MessageProtocol::C2S_VIEW_NPC_DETAIL:
                    $this->handleViewNpcDetail($fd, $payload);
                    break;
                case MessageProtocol::C2S_PRELOAD_DIALOGUE:
                    $this->handlePreloadDialogue($fd, $payload);
                    break;
                case MessageProtocol::C2S_BIND_ITEM:
                    $this->handleBindItem($fd, $payload);
                    break;
                case MessageProtocol::C2S_GET_RECIPES:
                    list($responseOpcode, $responsePayload) = handleGetRecipes($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;
                case MessageProtocol::C2S_CRAFT_ITEM:
                    list($responseOpcode, $responsePayload) = handleCraftItem($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);

                    // 如果合成成功，更新玩家背包和属性
                    if ($responseOpcode == MessageProtocol::S2C_CRAFT_RESULT && isset($responsePayload['success']) && $responsePayload['success']) {
                        $playerId = $payload['player_id'];
                        
                        // 获取玩家最新的背包数据
                        $inventoryData = $this->handleGetInventory($fd, ['player_id' => $playerId], true);
                        if ($inventoryData) {
                            $this->sendMessage($fd, MessageProtocol::S2C_INVENTORY_DATA, $inventoryData);
                        }
                        
                        // 获取玩家最新的属性数据（金币等）
                        $playerAttrs = $this->recalculatePlayerAttributes($playerId);
                        if ($playerAttrs) {
                            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, [
                                'attributes' => $playerAttrs
                            ]);
                        }
                    }
                    break;
                case MessageProtocol::C2S_GET_GEM_RECIPES:
                    list($responseOpcode, $responsePayload) = handleGetGemRecipes($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;
                case MessageProtocol::C2S_CRAFT_GEM:
                    list($responseOpcode, $responsePayload) = handleCraftGem($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);

                    // 如果宝石合成成功，更新玩家背包和属性
                    if ($responseOpcode == MessageProtocol::S2C_GEM_CRAFT_RESULT && isset($responsePayload['success']) && $responsePayload['success']) {
                        $playerId = $payload['player_id'];

                        // 获取玩家最新的背包数据
                        $inventoryData = $this->handleGetInventory($fd, ['player_id' => $playerId], true);
                        if ($inventoryData) {
                            $this->sendMessage($fd, MessageProtocol::S2C_INVENTORY_DATA, $inventoryData);
                        }

                        // 获取玩家最新的属性数据（金币等）
                        $playerAttrs = $this->recalculatePlayerAttributes($playerId);
                        if ($playerAttrs) {
                            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, [
                                'attributes' => $playerAttrs
                            ]);
                        }
                    }
                    break;
                case MessageProtocol::C2S_WAREHOUSE_DEPOSIT:
                    $this->handleWarehouseDeposit($fd, $payload);
                    break;
                case MessageProtocol::C2S_WAREHOUSE_WITHDRAW:
                    handleWarehouseWithdraw($fd, $payload, $this);
                    break;
                case MessageProtocol::C2S_WAREHOUSE_EXPAND:
                    handleWarehouseExpand($fd, $payload, $this);
                    break;

                case MessageProtocol::C2S_ATTRIBUTE_RESET:
                    $this->handleAttributeReset($fd, $payload);
                    break;

                // 公告相关处理
                case MessageProtocol::C2S_GET_LATEST_ANNOUNCEMENT:
                    $this->announcementHandler->handleGetLatestAnnouncement($fd, $playerId, $payload);
                    break;
                case MessageProtocol::C2S_GET_ANNOUNCEMENT_DETAIL:
                    $this->announcementHandler->handleGetAnnouncementDetail($fd, $playerId, $payload);
                    break;
                case MessageProtocol::C2S_GET_ANNOUNCEMENT_LIST:
                    $this->announcementHandler->handleGetAnnouncementList($fd, $playerId, $payload);
                    break;

                case MessageProtocol::C2S_REDEEM_CODE:
                    $this->handleRedemptionCode($fd, $playerId, $payload);
                    break;

                case MessageProtocol::C2S_GET_REFINE_LEADERBOARD:
                    $this->handleGetRefineLeaderboard($fd, $playerId, $payload);
                    break;

                case MessageProtocol::C2S_GET_REFINE_MATERIALS:
                    list($responseOpcode, $responsePayload) = handleGetRefineMaterials($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;

                case MessageProtocol::C2S_GET_REFINE_COST_ESTIMATE:
                    list($responseOpcode, $responsePayload) = handleGetRefineCostEstimate($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    break;

                case MessageProtocol::C2S_REFINE_ITEM:
                    list($responseOpcode, $responsePayload) = handleRefineItem($fd, $payload, $this);
                    if ($responseOpcode) $this->sendMessage($fd, $responseOpcode, $responsePayload);
                    
                    // 如果凝练成功，更新玩家背包和属性
                    if ($responseOpcode == MessageProtocol::S2C_REFINE_RESULT && isset($responsePayload['success']) && $responsePayload['success']) {
                        $playerId = $payload['player_id'];
                        
                        // 获取玩家最新的背包数据
                        $inventoryData = $this->handleGetInventory($fd, ['player_id' => $playerId], true);
                        if ($inventoryData) {
                            $this->sendMessage($fd, MessageProtocol::S2C_INVENTORY_DATA, $inventoryData);
                        }
                        
                        // 获取玩家最新的属性数据（金币等）
                        $playerAttrs = $this->recalculatePlayerAttributes($playerId);
                        if ($playerAttrs) {
                            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, [
                                'attributes' => $playerAttrs
                            ]);
                        }
                    }
                    break;
                
                case MessageProtocol::C2S_DROP_ITEM:
                    $this->handleDropItem($fd, $payload);
                    break;
                    
                case MessageProtocol::C2S_PVP_CHALLENGE:
                    $result = $this->pvpHandler->handleChallenge($fd, $playerId, $payload);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => $result['message'],
                            'type' => 'success'
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_challenge'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_PVP_ACCEPT_CHALLENGE:
                    $result = $this->pvpHandler->handleAcceptChallenge($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_accept_challenge'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_PVP_DECLINE_CHALLENGE:
                    $result = $this->pvpHandler->handleDeclineChallenge($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_decline_challenge'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_PVP_SET_INTENTION:
                    $result = $this->pvpHandler->handleSetIntention($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_set_intention'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_PVP_SURRENDER:
                    $result = $this->pvpHandler->handleSurrender($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_surrender'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_PVP_ACTION:
                    $result = $this->pvpHandler->handlePvpAction($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_action'
                        ]);
                    }
                    break;
                
                case MessageProtocol::C2S_GET_PVP_LEADERBOARD:
                    $this->pvpHandler->handleGetLeaderboard($fd, $playerId);
                    break;
                
                case MessageProtocol::C2S_GET_PVP_STATS:
                    $this->pvpHandler->handleGetStats($fd, $playerId);
                    break;
                
                case MessageProtocol::C2S_GET_LEVEL_RANKING:
                    $this->pvpHandler->handleGetLevelRanking($fd, $playerId);
                    break;
                    
                case MessageProtocol::C2S_GET_HERO_RANKING:
                    $this->pvpHandler->handleGetHeroRanking($fd, $playerId);
                    break;
                    
                case MessageProtocol::C2S_GET_VILLAIN_RANKING:
                    $this->pvpHandler->handleGetVillainRanking($fd, $playerId);
                    break;

                // 交易相关消息处理
                case MessageProtocol::C2S_TRADE_REQUEST:
                    $this->handleTradeRequest($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_RESPOND:
                    $this->handleTradeRespond($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_ADD_ITEM:
                    $this->handleTradeAddItem($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_REMOVE_ITEM:
                    $this->handleTradeRemoveItem($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_ADD_CURRENCY:
                    $this->handleTradeAddCurrency($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_CONFIRM:
                    $this->handleTradeConfirm($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_FINAL_CONFIRM:
                    $this->handleTradeFinalConfirm($fd, $payload);
                    break;

                case MessageProtocol::C2S_TRADE_CANCEL:
                    $this->handleTradeCancel($fd, $payload);
                    break;
                
                case MessageProtocol::C2S_PVP_DIRECT_ATTACK:
                    $result = $this->pvpHandler->handleDirectAttack($fd, $playerId, $payload);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                            'message' => $result['message'],
                            'type' => 'success'
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'pvp_direct_attack'
                        ]);
                    }
                    break;
                
                // 处理公聊消息
                case MessageProtocol::C2S_SEND_PUBLIC_CHAT:
                    $result = $this->chatHandler->handlePublicChat($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'public_chat'
                        ]);
                    }
                    break;
                    
                // 获取公聊历史消息
                case MessageProtocol::C2S_GET_PUBLIC_CHAT:
                    $result = $this->chatHandler->getRecentPublicMessages($playerId);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_PUBLIC_CHAT_HISTORY, [
                            'messages' => $result['messages']
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'get_public_chat'
                        ]);
                    }
                    break;
                // 处理私聊消息
                case MessageProtocol::C2S_SEND_PRIVATE_CHAT:
                    $result = $this->chatHandler->handlePrivateChat($fd, $playerId, $payload);
                    if (!$result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'private_chat'
                        ]);
                    }
                    break;

                // 获取私聊历史消息
                case MessageProtocol::C2S_GET_PRIVATE_CHAT:
                    $contactId = $payload['contact_id'] ?? '';
                    if (empty($contactId)) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => '联系人ID不能为空',
                            'context' => 'get_private_chat'
                        ]);
                        break;
                    }

                    $result = $this->chatHandler->getPrivateChatHistory($playerId, $contactId);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_PRIVATE_CHAT_HISTORY, [
                            'messages' => $result['messages'],
                            'contactId' => $contactId
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'get_private_chat'
                        ]);
                    }
                    break;

                // 获取聊天联系人列表
                case MessageProtocol::C2S_GET_CHAT_CONTACTS:
                    $result = $this->chatHandler->getChatContacts($playerId);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_CHAT_CONTACTS_LIST, [
                            'contacts' => $result['contacts']
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'get_chat_contacts'
                        ]);
                    }
                    break;

                // 删除聊天联系人
                case MessageProtocol::C2S_DELETE_CHAT_CONTACT:
                    $contactId = $payload['contact_id'] ?? '';
                    if (empty($contactId)) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => '联系人ID不能为空',
                            'context' => 'delete_chat_contact'
                        ]);
                        break;
                    }

                    $result = $this->chatHandler->deleteChatContact($playerId, $contactId);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_CHAT_CONTACT_UPDATED, [
                            'action' => 'deleted',
                            'contactId' => $contactId
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'delete_chat_contact'
                        ]);
                    }
                    break;

                // 标记消息为已读
                case MessageProtocol::C2S_MARK_MESSAGES_READ:
                    $contactId = $payload['contact_id'] ?? '';
                    if (empty($contactId)) {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => '联系人ID不能为空',
                            'context' => 'mark_messages_read'
                        ]);
                        break;
                    }

                    $result = $this->chatHandler->markMessagesAsRead($playerId, $contactId);
                    if ($result['success']) {
                        $this->sendMessage($fd, MessageProtocol::S2C_CHAT_CONTACT_UPDATED, [
                            'action' => 'marked_read',
                            'contactId' => $contactId,
                            'unreadCount' => 0
                        ]);
                    } else {
                        $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                            'message' => $result['message'],
                            'context' => 'mark_messages_read'
                        ]);
                    }
                    break;

                default:
                    $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未知或非法的操作']);
            }
                        } catch (Exception $e) {
            echo "处理认证消息失败 from FD {$fd}: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '服务器内部错误']);
        }
    }

    private function getMonsterWithAttributes($monsterTemplateId) {
        $db = Database::getInstance();
    
        // 1. Get monster base attributes from template
        $stmt = $db->query("SELECT * FROM monster_templates WHERE id = ?", [$monsterTemplateId]);
        $templateData = $stmt->fetch();
        if (!$stmt || !$templateData) return null;
        $stmt->closeCursor();
    
        // 2. Calculate derived stats from base attributes using Formulas
        $level = $templateData['level'] ?? 1;
        $totalStrength = $templateData['strength'] ?? 0;
        $totalAgility = $templateData['agility'] ?? 0;
        $totalConstitution = $templateData['constitution'] ?? 0;
        $totalIntelligence = $templateData['intelligence'] ?? 0;
    
        // Initialize final attributes array
        $finalAttrs = [
            'level' => $level,
            'hp' => $templateData['base_hp'] ?? Formulas::calculateMaxHp($totalConstitution, $level), // Use old hp as current, but default max_hp from formula
            'max_hp' => Formulas::calculateMaxHp($totalConstitution, $level),
            'max_mp' => Formulas::calculateMaxMp($totalIntelligence, $level),
            'attack' => Formulas::calculateAttack($totalStrength, $level),
            'defense' => Formulas::calculateDefense($totalConstitution, $totalAgility, $level),
            'attack_speed' => Formulas::calculateAttackSpeed($totalAgility),
            'experience_reward' => $templateData['experience_reward'] ?? 0,
            'fire_resistance' => $templateData['fire_resistance'] ?? 0,
            'ice_resistance' => $templateData['ice_resistance'] ?? 0,
            'wind_resistance' => $templateData['wind_resistance'] ?? 0,
            'electric_resistance' => $templateData['electric_resistance'] ?? 0,
            // 为公式评估添加基础属性
            'strength' => $totalStrength,
            'agility' => $totalAgility,
            'constitution' => $totalConstitution,
            'intelligence' => $totalIntelligence,
        ];

        // 3. Get monster's equipped items
        $stmt = $db->query(
            "SELECT it.name, ed.stats, ed.slot
             FROM monster_equipment me
             JOIN item_templates it ON me.item_template_id = it.id
             JOIN equipment_details ed ON it.id = ed.item_template_id
             WHERE me.monster_template_id = ?",
            [$monsterTemplateId]
        );
        $equippedItems = $stmt->fetchAll();
        $stmt->closeCursor();
    
        $weaponName = null;
    
        // 4. Sum stats from equipment
        foreach ($equippedItems as $item) {
            $itemStats = json_decode($item['stats'], true);
            if ($itemStats) {
                foreach ($itemStats as $key => $value) {
                    // Add all stats from items, even if the base attribute doesn't exist
                    $finalAttrs[$key] = ($finalAttrs[$key] ?? 0) + floatval($value);
                }
            }
            
            // Find weapon name
            if ($item['slot'] === 'RightHand' || $item['slot'] === 'TwoHanded') {
                $weaponName = $item['name'];
            }
        }
    
        // 5. Get monster skills
        $stmt = $db->query(
            "SELECT ms.skill_template_id, ms.cast_chance FROM monster_skills ms 
            INNER JOIN skill_templates st ON ms.skill_template_id = st.id
            WHERE ms.monster_template_id = ?",
            [$monsterTemplateId]
        );
        $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        return [
            'id' => $templateData['id'],
            'monster_template_id' => $monsterTemplateId, // 显式地保留模板ID
            'username' => $templateData['name'],
            'attributes' => $finalAttrs,
            'weapon_name' => $weaponName,
            'skills' => $skills
        ];
    }

    private function getFullMonsterDetails($monsterTemplateId) {
        $db = Database::getInstance();
    
        // 1. Get monster template data
        $stmt = $db->query("SELECT * FROM monster_templates WHERE id = ?", [$monsterTemplateId]);
        $templateData = $stmt->fetch();
        if (!$templateData) return null;
        $stmt->closeCursor();
    
        // 2. Get monster's equipped items' names and slots
        $stmt = $db->query(
            "SELECT 
                it.*,
                ed.slot, ed.job_restriction, ed.stats, ed.sockets, ed.grants_job_id,
                j_grant.name as granted_job_name,
                j_restrict.name as job_restriction_name
            FROM monster_equipment me
            JOIN item_templates it ON me.item_template_id = it.id
            LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
            LEFT JOIN jobs j_grant ON ed.grants_job_id = j_grant.id
            LEFT JOIN jobs j_restrict ON ed.job_restriction = j_restrict.id
            WHERE me.monster_template_id = ?",
            [$monsterTemplateId]
        );
        $equippedItems = $stmt->fetchAll();
        $stmt->closeCursor();
    
        return [
            'id' => $templateData['id'],
            'name' => $templateData['name'],
            'description' => $templateData['description'],
            'max_hp' => $templateData['base_max_hp'], // Base max HP
            // Current hp will be added from the instance data
            'equipment' => $equippedItems
        ];
    }

    private function getPlayerWithAttributes($accountId) {
        $db = Database::getInstance();
        $conn = $db->getConnection();

        // 1. Get base player attributes and join with jobs table.
        $stmt = $conn->prepare(
            "SELECT pa.*, j.name as job_name, j.description as job_description
             FROM player_attributes pa
             LEFT JOIN jobs j ON pa.current_job_id = j.id
             WHERE pa.account_id = ?"
        );
        $stmt->execute([$accountId]);
        $baseAttrs = $stmt->fetch(PDO::FETCH_ASSOC);
        if (!$baseAttrs) return null;
        $stmt->closeCursor();

        // 2. 计算包含装备加成的总属性（不保存到数据库）
        $finalAttrs = $this->calculatePlayerTotalAttributes($accountId, $baseAttrs, $conn);

        // 3. Get player base info (username)
        $stmt = $db->query("SELECT id, username FROM accounts WHERE id = ?", [$accountId]);
        $playerInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 3. Get weapon name
        $weaponName = null;
        $stmt = $db->query(
            "SELECT it.name, inv.instance_data
             FROM player_inventory inv
             JOIN item_templates it ON inv.item_template_id = it.id
             JOIN equipment_details ed ON it.id = ed.item_template_id
             WHERE inv.player_id = ? AND inv.is_equipped = 1 AND (ed.slot = 'RightHand' OR ed.slot = 'TwoHanded')",
            [$accountId]
        );
        $weapon = $stmt->fetch();
        $stmt->closeCursor();
    
        if ($weapon) {
            $instanceData = !empty($weapon['instance_data']) ? json_decode($weapon['instance_data'], true) : null;
            $weaponName = $instanceData['display_name'] ?? $weapon['name'];
        }

        // 4. Clean up and assemble the final object
        unset($finalAttrs['id'], $finalAttrs['account_id']);

        return [
            'id' => $playerInfo['id'],
            'username' => $playerInfo['username'],
            'attributes' => $finalAttrs,
            'weapon_name' => $weaponName
        ];
    }

    /**
     * 计算玩家的总属性（包括装备加成），但不保存基础属性到数据库
     */
    private function calculatePlayerTotalAttributes($playerId, $baseAttrs, $conn) {
        // 复制基础属性
        $playerAttrs = $baseAttrs;

        // 2. 获取所有已装备物品
        $stmt = $conn->prepare(
            "SELECT inv.instance_data, ed.stats
             FROM player_inventory inv
             JOIN item_templates it ON inv.item_template_id = it.id
             JOIN equipment_details ed ON it.id = ed.item_template_id
             WHERE inv.player_id = ? AND inv.is_equipped = 1"
        );
        $stmt->execute([$playerId]);
        $equippedItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 3. 计算来自装备和宝石的总属性加成
        $equipmentBonuses = [];
        $bonusStatKeys = ['strength', 'agility', 'constitution', 'intelligence', 'fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance', 'fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'];
        foreach($bonusStatKeys as $key) { $equipmentBonuses[$key] = 0; } // Initialize

        foreach ($equippedItems as $item) {
            // 来自装备本身的属性
            if ($item['stats']) {
                $itemStats = json_decode($item['stats'], true);
                foreach($itemStats as $key => $value) {
                    $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                }
            }

            // 来自宝石的属性
            $instanceData = json_decode($item['instance_data'], true);
            if (isset($instanceData['gem_stats']) && is_array($instanceData['gem_stats'])) {
                foreach($instanceData['gem_stats'] as $key => $value) {
                    $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                }
            }

            // 从凝练属性中获取加成
            if (isset($instanceData['refined']) && $instanceData['refined'] &&
                isset($instanceData['refine_bonuses']) && is_array($instanceData['refine_bonuses'])) {

                // 获取凝练属性的类型信息
                $refineBonusTypes = $instanceData['refine_bonus_types'] ?? [];

                foreach($instanceData['refine_bonuses'] as $key => $value) {
                    $bonusType = $refineBonusTypes[$key] ?? 'flat'; // 默认为固定值

                    if ($bonusType === 'percentage') {
                        // 百分比类型：暂时保存，稍后基于基础属性计算
                        if (!isset($equipmentBonuses['_percentage_bonuses'])) {
                            $equipmentBonuses['_percentage_bonuses'] = [];
                        }
                        $equipmentBonuses['_percentage_bonuses'][$key] = ($equipmentBonuses['_percentage_bonuses'][$key] ?? 0) + $value;
                    } else {
                        // 固定值类型：直接相加
                        $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                    }
                }
            }
        }

        // 4. 处理百分比加成
        if (isset($equipmentBonuses['_percentage_bonuses'])) {
            $percentageBonuses = $equipmentBonuses['_percentage_bonuses'];

            // 基于基础属性计算百分比加成
            foreach ($percentageBonuses as $attr => $percentage) {
                $baseValue = 0;

                // 获取基础值用于百分比计算
                if (isset($playerAttrs[$attr])) {
                    $baseValue = $playerAttrs[$attr];
                } elseif (in_array($attr, ['attack', 'defense', 'max_hp', 'max_mp', 'attack_speed'])) {
                    // 对于衍生属性，使用基础四维属性计算基础值
                    switch ($attr) {
                        case 'attack':
                            $baseValue = Formulas::calculateAttack($playerAttrs['strength'], $playerAttrs['level']);
                            break;
                        case 'defense':
                            $baseValue = Formulas::calculateDefense($playerAttrs['constitution'], $playerAttrs['agility'], $playerAttrs['level']);
                            break;
                        case 'max_hp':
                            $baseValue = Formulas::calculateMaxHp($playerAttrs['constitution'], $playerAttrs['level']);
                            break;
                        case 'max_mp':
                            $baseValue = Formulas::calculateMaxMp($playerAttrs['intelligence'], $playerAttrs['level']);
                            break;
                        case 'attack_speed':
                            $baseValue = Formulas::calculateAttackSpeed($playerAttrs['agility']);
                            break;
                    }
                }

                // 计算百分比加成并添加到装备加成中
                if ($baseValue > 0) {
                    $percentageBonus = round($baseValue * ($percentage / 100));
                    $equipmentBonuses[$attr] = ($equipmentBonuses[$attr] ?? 0) + $percentageBonus;

                    error_log("Percentage bonus calculation for {$attr}: base {$baseValue} * {$percentage}% = +{$percentageBonus}");
                }
            }

            // 清除临时的百分比数据
            unset($equipmentBonuses['_percentage_bonuses']);
        }

        // 5. 使用基础属性和装备加成计算最终的派生属性
        $totalStrength = $playerAttrs['strength'] + ($equipmentBonuses['strength'] ?? 0);
        $totalAgility = $playerAttrs['agility'] + ($equipmentBonuses['agility'] ?? 0);
        $totalConstitution = $playerAttrs['constitution'] + ($equipmentBonuses['constitution'] ?? 0);
        $totalIntelligence = $playerAttrs['intelligence'] + ($equipmentBonuses['intelligence'] ?? 0);

        // 6. 计算衍生属性
        $derivedAttrs = [
            'attack' => Formulas::calculateAttack($totalStrength, $playerAttrs['level']) + ($equipmentBonuses['attack'] ?? 0),
            'defense' => Formulas::calculateDefense($totalConstitution, $totalAgility, $playerAttrs['level']) + ($equipmentBonuses['defense'] ?? 0),
            'max_hp' => Formulas::calculateMaxHp($totalConstitution, $playerAttrs['level']) + ($equipmentBonuses['max_hp'] ?? 0),
            'max_mp' => Formulas::calculateMaxMp($totalIntelligence, $playerAttrs['level']) + ($equipmentBonuses['max_mp'] ?? 0),
            'attack_speed' => Formulas::calculateAttackSpeed($totalAgility) + ($equipmentBonuses['attack_speed'] ?? 0),
        ];

        // 7. 合并所有属性并返回
        $finalAttrs = array_merge($playerAttrs, $derivedAttrs, [
            'strength' => $totalStrength,
            'agility' => $totalAgility,
            'constitution' => $totalConstitution,
            'intelligence' => $totalIntelligence,
        ]);

        return $finalAttrs;
    }
    
    private function handleRegister($fd, $data) {
        $loginName = $data['login_name'] ?? null;
        $nickname = $data['nickname'] ?? null;
        $password = $data['password'] ?? null;

        if (empty($loginName) || empty($nickname) || empty($password) || strlen($password) < 6) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '账号、昵称和密码均不能为空(密码需大于6位)']);
        }

        // 验证登录账号 (3-10位, 仅限字母和数字)
        if (mb_strlen($loginName, 'UTF-8') > 10 || mb_strlen($loginName, 'UTF-8') < 3) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '登录账号长度必须在3-10个字符之间']);
        }
        if (!preg_match('/^[a-zA-Z0-9]+$/', $loginName)) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '登录账号只能包含英文字母和数字']);
        }
        
        // 验证游戏昵称 (最多10位, 中英文数字)
        if (mb_strlen($nickname, 'UTF-8') > 10) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '昵称长度不能超过10个字符']);
        }
        if (!preg_match('/^[\p{Han}a-zA-Z0-9]+$/u', $nickname)) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '昵称只能包含中英文和数字']);
        }
        
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();
        
        try {
            // 检查登录账号是否已存在
            $stmt = $conn->prepare("SELECT id FROM accounts WHERE login_name = ? FOR UPDATE");
            $stmt->execute([$loginName]);
        if ($stmt->fetch()) {
            $stmt->closeCursor();
                throw new Exception('该登录账号已被注册');
            }
            $stmt->closeCursor();

            // 检查昵称是否已存在 (username 字段现在用作昵称)
            $stmt = $conn->prepare("SELECT id FROM accounts WHERE username = ? FOR UPDATE");
            $stmt->execute([$nickname]);
            if ($stmt->fetch()) {
                $stmt->closeCursor();
                throw new Exception('该游戏昵称已被使用');
        }
            $stmt->closeCursor();
        
        $hashedPassword = password_hash($password, PASSWORD_DEFAULT);

            // 步骤1: 插入 accounts 表, 同时包含登录名和昵称
            $stmt = $conn->prepare("INSERT INTO accounts (login_name, username, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$loginName, $nickname, $hashedPassword]);
        
            // 步骤2: 获取新账户ID
            $accountId = $conn->lastInsertId();

            // 步骤3: 为新账户插入默认属性
            $stmt = $conn->prepare(
                "INSERT INTO player_attributes (account_id, native_job_id, current_job_id, level) VALUES (?, 1, 1, 1)"
            );
            $stmt->execute([$accountId]);

            // 步骤4: 计算初始属性
            $this->recalculatePlayerAttributes($accountId, $conn);
            
            // 步骤5: 设置满血满蓝
            $this->setPlayerToFullHealthAndMana($accountId, $conn);

            $conn->commit();

        return $this->sendUnencrypted($fd, MessageProtocol::S2C_REGISTER_SUCCESS, ['message' => '注册成功，请登录']);

        } catch (Exception $e) {
            $conn->rollBack();
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]);
        }
    }

    /**
     * 将指定玩家的生命值和魔力值设置为其最大值。
     * @param int $playerId 玩家ID
     * @param PDO $conn 数据库连接，必须在一个事务中调用
     */
    private function setPlayerToFullHealthAndMana($playerId, $conn, &$playerAttrsRef = null) {
        $stmt = $conn->prepare("SELECT max_hp, max_mp FROM player_attributes WHERE account_id = ?");
        $stmt->execute([$playerId]);
        $stats = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if ($stats) {
            $updateStmt = $conn->prepare("UPDATE player_attributes SET hp = ?, mp = ? WHERE account_id = ?");
            $updateStmt->execute([$stats['max_hp'], $stats['max_mp'], $playerId]);
            
            // 同时更新传入的属性数组引用，以防止数据不一致
            if ($playerAttrsRef !== null) {
                $playerAttrsRef['hp'] = $stats['max_hp'];
                $playerAttrsRef['mp'] = $stats['max_mp'];
            }
        }
    }
    
    private function handleLogin($fd, $data) {
        $loginName = $data['login_name'] ?? null;
        $password = $data['password'] ?? null;
            
            $db = Database::getInstance();
        $stmt = $db->query("SELECT id, password_hash FROM accounts WHERE login_name = ?", [$loginName]);
        $account = $stmt->fetch();
        $stmt->closeCursor();
            
        if (!$account || !password_verify($password, $account['password_hash'])) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '账号或密码错误']);
        }
        
        $player = $this->getPlayerWithAttributes($account['id']);
        if (!$player) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '无法加载角色数据']);
        }

        // **场景有效性验证和分配**
        $currentSceneId = $player['attributes']['current_scene_id'] ?? null;
        $sceneData = $currentSceneId ? $this->sceneManager->getSceneData($currentSceneId) : null;

        if (!$sceneData) { // 如果场景ID为空或无效
            $message = $currentSceneId ? "场景 {$currentSceneId} 无效" : "没有当前场景";
            error_log("玩家 {$player['id']} 的{$message}，将分配初始场景。");

                $initialSceneId = $this->sceneManager->getInitialSceneId();
            $player['attributes']['current_scene_id'] = $initialSceneId;
            $db->query("UPDATE player_attributes SET current_scene_id = ?, logindate = NOW() WHERE account_id = ?", [$initialSceneId, $player['id']]);
        } else {
            // 场景有效，只更新登录时间
            $db->query("UPDATE player_attributes SET logindate = NOW() WHERE account_id = ?", [$player['id']]);
        }

        if(isset($this->playerConnections[$player['id']])) {
            $oldFd = $this->playerConnections[$player['id']];
            echo "玩家 {$player['username']} 已在别处登录 (FD:{$oldFd})，正在将其踢下线。\n";
            $this->server->close($oldFd);
        }

        $secretKey = random_bytes(32);
        
        // 在Redis中存储会话密钥，有效期1小时
        RedisManager::getInstance()->with(function($redis) use ($player, $secretKey) {
            $redis->set("session:{$player['id']}", $secretKey, 3600);
        });
        
        $this->fdInfo[$fd] = ['player_id' => $player['id'], 'secret_key' => $secretKey];
        $this->playerConnections[$player['id']] = $fd;
        $this->fdToPlayerId[$fd] = $player['id'];

        echo "玩家 {$player['username']} (ID: {$player['id']}) 登录成功, FD: {$fd}\n";

// 在Redis中存储玩家在线状态标记
RedisManager::getInstance()->with(function($redis) use ($player) {
    // 存储玩家的连接标记，用于后台管理系统检测在线状态
    $redis->set("player_fd:{$player['id']}", 1, 3600); // 1小时过期时间，防止异常情况下标记无法清除
});

        return $this->sendUnencrypted($fd, MessageProtocol::S2C_LOGIN_SUCCESS, [
            'player' => $player,
            'session_key' => base64_encode($secretKey)
        ]);
    }
    
    private function handleEnterScene($fd, $data) {
            $playerId = $data['player_id'];
            $newSceneId = $data['scene_id'];
            
        // 当玩家在战斗中时，特殊处理
        $player = $this->getPlayerWithAttributes($playerId);
        if (!$player) return [MessageProtocol::S2C_ERROR, ['message' => '无法获取玩家数据']];

        if (isset($this->playerBattleMap[$playerId])) {
            $monsterId = $this->playerBattleMap[$playerId];
            
            if (!isset($this->monsterBattles[$monsterId])) {
                unset($this->playerBattleMap[$playerId]);
            } else {
                // 只有死亡玩家可以这样离开战斗
                if ($player['attributes']['hp'] <= 0) {
                    echo "死亡玩家 {$playerId} 请求离开战斗并返回场景。\n";
                    $this->removePlayerFromBattle($playerId, 'dead_left');
                } else {
                    return [MessageProtocol::S2C_ERROR, ['message' => '你还活着，必须先逃跑才能离开战斗！']];
                }
            }
        }

        // 检查玩家是否有进行中的交易，如果有则自动取消
        $activeTrade = $this->tradeManager->getPlayerActiveTrade($playerId);
        if ($activeTrade) {
            $this->tradeManager->cancelTrade($activeTrade['id'], $playerId, '玩家离开了场景');

            // 通知交易伙伴
            $partnerId = ($activeTrade['initiator_id'] == $playerId)
                ? $activeTrade['target_id']
                : $activeTrade['initiator_id'];

            $partnerFd = $this->playerConnections[$partnerId] ?? null;
            if ($partnerFd) {
                $this->sendMessage($partnerFd, MessageProtocol::S2C_TRADE_CANCELLED, [
                    'reason' => '交易伙伴离开了场景',
                    'trade_id' => $activeTrade['id']
                ]);
            }
        }

        try {
            $sceneResult = $this->sceneManager->playerEnterScene($playerId, $newSceneId);
            $sceneData = $sceneResult['scene_data'];
            $oldSceneId = $sceneResult['old_scene_id'];

            // 如果之前在另一个场景，广播离开事件
            if ($oldSceneId && $oldSceneId !== $newSceneId) {
                $oldScenePlayers = $this->sceneManager->getScenePlayers($oldSceneId);
                $this->broadcastToScene(
                    $oldSceneId, 
                    MessageProtocol::S2C_SCENE_PLAYER_CHANGE, 
                    [
                    'action' => 'leave',
                    'scene_id' => $oldSceneId,
                    'player_id' => $playerId,
                        'current_players' => $oldScenePlayers,
                        'current_monsters' => $this->sceneManager->getSceneMonsters($oldSceneId),
                        'current_items' => $this->sceneManager->getSceneItems($oldSceneId),
                        'current_npcs' => $this->npcManager->getSceneNpcs($oldSceneId) // 添加NPC数据
                    ],
                    [$playerId]
                );
            }

            // 广播进入新场景的事件
            $players = $this->sceneManager->getScenePlayers($newSceneId);
            $this->broadcastToScene(
                $newSceneId, 
                MessageProtocol::S2C_SCENE_PLAYER_CHANGE, 
                [
                'action' => 'enter',
                'scene_id' => $newSceneId,
                'player_id' => $playerId,
                    'current_players' => $players,
                    'current_monsters' => $this->sceneManager->getSceneMonsters($newSceneId),
                    'current_items' => $this->sceneManager->getSceneItems($newSceneId),
                    'current_npcs' => $this->npcManager->getSceneNpcs($newSceneId) // 添加NPC数据
                ],
                [$playerId]
            );
            
            // 为进入者计算并发送场景出口信息
            $exits = $this->sceneManager->getSceneExits($sceneData['x'], $sceneData['y'], $sceneData['z']);

            // 获取场景中的物品并标记保护状态
            $sceneItems = $this->sceneManager->getSceneItems($newSceneId);
            $sceneData['items'] = $sceneItems;
            $this->markProtectedItems($sceneData, $playerId);
            
            // 获取场景中的NPC（包含任务信息）
            if (!isset($this->questManager)) {
                $this->questManager = new QuestManager(function() {
                    return $this->db->getConnection();
                });
            }
            $sceneNpcs = $this->npcManager->getSceneNpcsWithQuestInfo($newSceneId, $playerId, $this->questManager);
            $sceneData['npcs'] = $sceneNpcs;

            return [MessageProtocol::S2C_SCENE_ENTERED, [
                'scene' => $sceneData,
                'players' => $players,
                'exits' => $exits
            ]];
            
        } catch (Exception $e) {
            return [MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]];
        }
    }
    
    private function handleHeal($data) {
            $playerId = $data['player_id'];
            
            $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });

        if (!$sceneId) {
            $this->sendMessage($this->playerConnections[$playerId], MessageProtocol::S2C_ERROR, ['message' => '你不在任何场景中，无法治疗。']);
            return;
        }
            
        $db = Database::getInstance();
        $stmt = $db->query("SELECT hp, max_hp FROM player_attributes WHERE account_id = ?", [$playerId]);
        $playerAttr = $stmt->fetch();
        $stmt->closeCursor();

        if (!$playerAttr) {
            $this->sendMessage($this->playerConnections[$playerId], MessageProtocol::S2C_ERROR, ['message' => '找不到玩家属性']);
            return;
        }

        if ($playerAttr['hp'] >= $playerAttr['max_hp']) {
            $this->sendMessage($this->playerConnections[$playerId], MessageProtocol::S2C_ERROR, ['message' => '你的生命值已满']);
            return;
        }

        // 此处可以加入治疗消耗、冷却等逻辑
        
        $db->query("UPDATE player_attributes SET hp = ? WHERE account_id = ?", [$playerAttr['max_hp'], $playerId]);

            $this->sendMessage(
                $this->playerConnections[$playerId], 
                MessageProtocol::S2C_PLAYER_HEALED, 
                [
                    'player_id' => $playerId,
                'current_hp' => $playerAttr['max_hp'],
                'max_hp' => $playerAttr['max_hp']
                ]
            );
            $this->broadcastPlayerUpdate($sceneId);
    }
    
    private function handleGetScenes() {
        try {
            return [MessageProtocol::S2C_SCENES_LIST, [
                'scenes' => $this->sceneManager->getAllScenes()
            ]];
        } catch (Exception $e) {
            echo "获取场景列表失败: " . $e->getMessage() . "\n";
            return [MessageProtocol::S2C_ERROR, ['message' => '获取场景列表失败']];
        }
    }
    
    private function handleResetScene($data) {
        $sceneId = $data['scene_id'];
        $playerId = $data['player_id'];
        $fd = $this->playerConnections[$playerId] ?? null;

        if (!$fd) {
            return;
        }

        $lastDeathTime = RedisManager::getInstance()->with(function($redis) use ($sceneId) {
            return $redis->get("scene_last_monster_death_time:{$sceneId}");
        });

        if ($lastDeathTime) {
            $cooldown = Formulas::$sceneResetCooldown;
            $timeSinceDeath = time() - (int)$lastDeathTime;

            if ($timeSinceDeath < $cooldown) {
                //$timeLeft = $cooldown - $timeSinceDeath;
                //$this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => "场景还不能重置，请等待 {$timeLeft} 秒。"]);
                echo "场景 {$sceneId} 还不能重置，请等待 {$timeSinceDeath} 秒。\n";
                return;
            }
        } else {
            echo "场景 {$sceneId} 没有清空，无需重置。\n";
            return;
        }

        $sceneData = $this->sceneManager->resetSceneMonsters($sceneId);
        if ($sceneData) {
            $this->broadcastToScene($sceneId, MessageProtocol::S2C_SCENE_RESET, ['scene' => $sceneData]);
            echo "场景 {$sceneId} 已重置并已向所有玩家广播。\n";
        }
    }
    
    /**
     * 寻找最近的拥有复活建筑的场景（基于区域ID+距离）
     * @param string $currentSceneId 当前场景ID
     * @return string 最近的复活点场景ID
     */
    private function findNearestRevivePoint($currentSceneId) {
        $db = Database::getInstance();

        // 获取当前场景信息（包括区域ID和坐标）
        $stmt = $db->query("SELECT x, y, z, zone_id FROM scenes WHERE id = ?", [$currentSceneId]);
        $currentScene = $stmt->fetch();
        $stmt->closeCursor();

        if (!$currentScene) {
            // echo "无法获取当前场景信息，使用默认复活点\n";
            return $this->getDefaultRevivePoint();
        }

        $currentZoneId = $currentScene['zone_id'] ?? 'zone_default';

        // 查询所有拥有复活建筑的场景（包括区域信息）
        $stmt = $db->query("
            SELECT s.id, s.x, s.y, s.z, s.zone_id, s.name
            FROM scenes s
            JOIN scene_buildings sb ON s.id = sb.scene_id
            JOIN buildings b ON sb.building_id = b.id
            WHERE b.type = 'REVIVE_POINT'
        ");
        $reviveScenes = $stmt->fetchAll();
        $stmt->closeCursor();

        if (empty($reviveScenes)) {
            // echo "未找到任何复活点，使用默认复活点\n";
            return $this->getDefaultRevivePoint();
        }

        // 按优先级分组复活点
        $sameZoneScenes = [];      // 同区域复活点
        $otherZoneScenes = [];     // 其他区域复活点

        foreach ($reviveScenes as $scene) {
            if ($scene['zone_id'] === $currentZoneId) {
                $sameZoneScenes[] = $scene;
            } else {
                $otherZoneScenes[] = $scene;
            }
        }

        // 优先选择同区域的复活点
        $targetScenes = !empty($sameZoneScenes) ? $sameZoneScenes : $otherZoneScenes;

        if (empty($targetScenes)) {
            return $this->getDefaultRevivePoint();
        }

        // 在目标场景中找到距离最近的
        $nearestScene = $this->findNearestSceneByDistance($currentScene, $targetScenes);

        // 记录复活点选择日志
        $logMessage = !empty($sameZoneScenes) ?
            "在同区域 {$currentZoneId} 中找到复活点: {$nearestScene['name']}" :
            "同区域无复活点，选择其他区域复活点: {$nearestScene['name']} (区域: {$nearestScene['zone_id']})";

        error_log("玩家复活点选择: {$logMessage}");

        return $nearestScene['id'];
    }

    /**
     * 获取默认复活点
     * @return string 默认复活点场景ID
     */
    private function getDefaultRevivePoint() {
        $db = Database::getInstance();

        // 尝试找到坐标(0,0)的场景
        $stmt = $db->query("SELECT id FROM scenes WHERE x = 0 AND y = 0 LIMIT 1");
        $defaultScene = $stmt->fetch();
        $stmt->closeCursor();

        if ($defaultScene) {
            return $defaultScene['id'];
        }

        // 如果没有(0,0)场景，返回任意一个安全区场景
        $stmt = $db->query("SELECT id FROM scenes WHERE is_safe_zone = 1 LIMIT 1");
        $safeScene = $stmt->fetch();
        $stmt->closeCursor();

        return $safeScene ? $safeScene['id'] : 'town_square';
    }

    /**
     * 在给定场景列表中找到距离最近的场景
     * @param array $currentScene 当前场景信息
     * @param array $targetScenes 目标场景列表
     * @return array 最近的场景信息
     */
    private function findNearestSceneByDistance($currentScene, $targetScenes) {
        $nearestScene = null;
        $minDistance = PHP_FLOAT_MAX;

        foreach ($targetScenes as $scene) {
            // 使用三维欧几里得距离计算
            $distance = sqrt(
                pow($scene['x'] - $currentScene['x'], 2) +
                pow($scene['y'] - $currentScene['y'], 2) +
                pow($scene['z'] - $currentScene['z'], 2)
            );

            if ($distance < $minDistance) {
                $minDistance = $distance;
                $nearestScene = $scene;
            }
        }

        return $nearestScene;
    }
    
    private function handleStartBattle($fd, $data) {
        $playerId = $data['player_id'];
        $monsterId = $data['monster_id'];

        $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
            return $redis->get("player_scene:{$playerId}");
        });
        if (!$sceneId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你不在任何场景中']);
        }

        if (isset($this->playerBattleMap[$playerId])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你已经在战斗中了']);
        }

        $player = $this->getPlayerWithAttributes($playerId);
        if (!$player || $player['attributes']['hp'] <= 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你已经倒下了，无法战斗']);
        }

        if (isset($this->monsterBattles[$monsterId])) {
            // 加入已有战斗
            $battleState = &$this->monsterBattles[$monsterId];

            // 关键修复：检查战斗是否已经结束但尚未清理
            if (!empty($battleState['is_over'])) {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你来晚了，战斗已经结束。']);
                
                // 确保客户端状态可以刷新，防止其被卡在错误的界面
                $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                    return $redis->get("player_scene:{$playerId}");
                });
                if ($sceneId) {
                    // 广播一个场景更新，让客户端有机会刷新
                    $this->broadcastPlayerUpdate($sceneId);
                }
                return;
            }
            
            echo "玩家 {$playerId} 加入了怪物 {$monsterId} 的战斗。\n";
            $battleState['players'][$playerId] = $player;
            $this->playerBattleMap[$playerId] = $monsterId;
            
            // 关键修复：当新玩家加入时，强制重新初始化ATB状态
            // 这会重新加载所有参战者的属性（包括新玩家的攻速）
            // 并为新玩家设置一个初始的ATB值
            $this->battleSystem->initializeAtbState($battleState);
            
            // 为新玩家准备一个简短的战斗状态摘要，而不是完整的历史日志
            $joinBattleLogs = [
                '你加入了战斗！',
                "敌人: {$battleState['monster']['username']} (HP: {$battleState['monster']['attributes']['hp']}/{$battleState['monster']['attributes']['max_hp']})"
            ];
            
            // 添加其他玩家的状态摘要
            foreach ($battleState['players'] as $pid => $pdata) {
                if ($pid !== $playerId) {
                    $joinBattleLogs[] = "玩家: {$pdata['username']} (HP: {$pdata['attributes']['hp']}/{$pdata['attributes']['max_hp']})";
                }
            }
            
            // 初始化新加入玩家的伤害贡献
            if (!isset($battleState['damage_contributions'])) {
                $battleState['damage_contributions'] = [];
            }
            $battleState['damage_contributions'][$playerId] = 0;
            
            // 初始化技能冷却记录
            if (!isset($battleState['skill_cooldowns'])) {
                $battleState['skill_cooldowns'] = [];
            }
            if (!isset($battleState['active_effects'])) {
                $battleState['active_effects'] = [];
            }
            if (!isset($battleState['player_locks'])) {
                $battleState['player_locks'] = [];
            }
            
            // 先发送战斗开始消息，只包含战斗摘要而非完整历史
            $joinBattleData = [
                'monster' => $battleState['monster'],
                'all_players' => array_values($battleState['players']),
                'atb_status' => $this->battleSystem->getAtbStatus($battleState),
                'log' => $joinBattleLogs, // 只发送战斗摘要，不发送完整历史
                'skills' => $this->_getBattleSkills($playerId), // 直接附带技能列表
                'active_effects' => $battleState['active_effects'] ?? [] // 添加状态效果
            ];

            error_log("[加入战斗] 向玩家 {$playerId} (FD {$fd}) 发送战斗开始消息");
            error_log("[加入战斗] 战斗玩家列表: " . json_encode(array_keys($battleState['players'])));

            $this->sendMessage($fd, MessageProtocol::S2C_BATTLE_STARTED, $joinBattleData);
            
            // 通知其他玩家有新玩家加入
            $logEntry = "{$player['username']} 加入了战斗！";
            //记录敌方和我方的战斗血量信息
            
            
            // 将新玩家加入的消息添加到战斗日志历史中
            if (!isset($battleState['battle_logs'])) {
                $battleState['battle_logs'] = [];
            }
            $battleState['battle_logs'][] = $logEntry;
            $this->monsterBattles[$monsterId] = $battleState;
            
            // 广播给其他玩家
            $this->broadcastToBattle($monsterId, MessageProtocol::S2C_BATTLE_LOG, ['log' => [$logEntry]], [$playerId]);
            
            return;
        }
        
        // 创建新战斗
        $monsterInstanceData = $this->sceneManager->getMonsterFromScene($sceneId, $monsterId);
        if (!$monsterInstanceData || $monsterInstanceData['hp'] <= 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '敌人不存在或已死亡']);
        }
        
        $monsterBattleData = $this->getMonsterWithAttributes($monsterInstanceData['monster_template_id']);
        if (!$monsterBattleData) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '失去敌人战斗信息']);
        }
        $monsterBattleData['attributes']['hp'] = $monsterInstanceData['hp'];
        $monsterBattleData['id'] = $monsterInstanceData['id'];
        
        echo "玩家 {$playerId} 在场景 {$sceneId} 开始与怪物 {$monsterId} 的新战斗。\n";

        // 构建初始战斗日志
        $monsterName = $monsterBattleData['username'];
        $monsterHp = $monsterBattleData['attributes']['hp'];
        $monsterMaxHp = $monsterBattleData['attributes']['max_hp'];
        $monsterMp = $monsterBattleData['attributes']['mp'] ?? 0;
        $monsterMaxMp = $monsterBattleData['attributes']['max_mp'];
        
        $playerName = $player['username'];
        $playerHp = $player['attributes']['hp'];
        $playerMaxHp = $player['attributes']['max_hp'];
        $playerMp = $player['attributes']['mp'];
        $playerMaxMp = $player['attributes']['max_mp'];

        $initialLog = "【战斗开始】你遭遇了 {$monsterName} (HP: {$monsterHp}/{$monsterMaxHp})。";
        $initialLog .= " 你的状态 (HP: {$playerHp}/{$playerMaxHp}, MP: {$playerMp}/{$playerMaxMp})。";

        $this->monsterBattles[$monsterId] = [
            'players' => [$playerId => $player],
            'monster' => $monsterBattleData,
            'scene_id' => $sceneId,
            'is_over' => false,
            'winner' => null,
            'battle_logs' => [$initialLog], // 使用新的初始日志
            'start_time' => time(), // 记录战斗开始时间
            'damage_contributions' => [$playerId => 0], // 初始化伤害贡献记录
            'player_intentions' => [], // 新增：用于存储玩家的行动意图
            'skill_cooldowns' => [], // 新增：用于跟踪技能冷却
            'player_locks' => [],    // 新增：用于锁定玩家行动指令
            'active_effects' => [] // 新增：用于跟踪增益/减益效果
        ];
        
        // 初始化ATB状态
        $this->battleSystem->initializeAtbState($this->monsterBattles[$monsterId]);
        
        $this->playerBattleMap[$playerId] = $monsterId;

        // 发送战斗开始消息
        $battleStartData = [
            'monster' => $monsterBattleData,
            'all_players' => array_values($this->monsterBattles[$monsterId]['players']),
            'atb_status' => $this->battleSystem->getAtbStatus($this->monsterBattles[$monsterId]),
            'log' => $this->monsterBattles[$monsterId]['battle_logs'], // 发送战斗日志历史
            'skills' => $this->_getBattleSkills($playerId) // 直接附带技能列表
        ];

        error_log("[战斗开始] 向玩家 {$playerId} (FD {$fd}) 发送战斗开始消息");
        error_log("[战斗开始] 战斗玩家列表: " . json_encode(array_keys($this->monsterBattles[$monsterId]['players'])));

        $this->sendMessage($fd, MessageProtocol::S2C_BATTLE_STARTED, $battleStartData);
        
        // 启动服务器端战斗循环
        $this->startBattleLoop($monsterId);
    }
    
    /**
     * 启动战斗循环，定期更新ATB状态并触发行动
     * @param string $monsterId 怪物ID
     */
    private function startBattleLoop($monsterId) {
        // 如果已经有定时器，先清除
        if (isset($this->battleTimers[$monsterId])) {
            swoole_timer_clear($this->battleTimers[$monsterId]);
        }
        
        // 创建新的定时器，每100毫秒更新一次ATB状态
        $this->battleTimers[$monsterId] = swoole_timer_tick(100, function() use ($monsterId) {
            // 检查战斗是否存在
            if (!isset($this->monsterBattles[$monsterId])) {
                swoole_timer_clear($this->battleTimers[$monsterId]);
                unset($this->battleTimers[$monsterId]);
                return;
            }
            
            $battleState = &$this->monsterBattles[$monsterId];
            
            // 如果战斗已结束，停止定时器
            if ($battleState['is_over']) {
                swoole_timer_clear($this->battleTimers[$monsterId]);
                unset($this->battleTimers[$monsterId]);
                return;
            }
            
            // 更新ATB状态并检查是否有角色可以行动
            $actionInfo = $this->battleSystem->updateAtbState($battleState);
            
            // 如果有角色可以行动，执行行动
            if ($actionInfo) {
                $this->executeAction($monsterId, $actionInfo);
            } else {
                // 没有行动，只广播ATB状态更新
                $this->broadcastAtbUpdate($monsterId);
            }
        });
    }
    
    /**
     * 执行战斗行动
     * @param string $monsterId 怪物ID
     * @param array $actionInfo 行动信息
     */
    private function executeAction($monsterId, $actionInfo) {
        if (!isset($this->monsterBattles[$monsterId])) {
            return;
        }
        
        $battleState = &$this->monsterBattles[$monsterId];
        
        // 增加对战斗是否已结束的检查
        if ($battleState['is_over']) {
            echo "战斗 {$monsterId} 已经结束，但仍有行动被触发。忽略此行动。\n";
            return;
        }
        
        $result = null;
        $effectLogs = [];
        
        // 1. 在行动者行动前，处理其身上的效果（BUFF/DEBUFF持续时间减少等）
        $actorType = $actionInfo['actor_type'];
        $actorId = $actionInfo['player_id'] ?? null;
        $effectResult = $this->battleSystem->processEffectsTurn($battleState, $actorType, $actorId);
        
        if ($effectResult) {
            $battleState = $effectResult['state']; // 使用处理完效果后的最新状态
            $effectLogs = $effectResult['log'];    // 存储效果到期等日志
        }
        
        // 2. 根据行动者类型执行不同的行动
        if ($actionInfo['actor_type'] === 'player') {
            $playerId = $actionInfo['player_id'];

            // 玩家回合开始，解除指令锁定
            if (isset($battleState['player_locks'][$playerId])) {
                unset($battleState['player_locks'][$playerId]);
            }

            // 技能冷却减少现在由BattleSystem::prepareAction中的decreaseSkillCooldowns方法处理
            // 移除了这里的重复冷却减少逻辑

            // 首先检查玩家是否正在吟唱
            if (isset($battleState['players'][$playerId]['casting_info'])) {
                // 如果玩家正在吟唱，则只处理吟唱进度
                $result = $this->battleSystem->handleCastingTurn($battleState, $playerId);
            } else {
                // 如果玩家不在吟唱状态，则执行正常的意图
                $intent = $battleState['player_intentions'][$playerId] ?? ['action' => 'attack']; // 默认为攻击
                
                // echo "执行玩家 {$playerId} 的行动，意图: " . json_encode($intent) . "\n";

                switch ($intent['action']) {
                case 'flee':
                    $result = $this->battleSystem->handleFlee($battleState, $playerId);
                    break;
                case 'use_skill':
                    $skillId = $intent['skill_id'] ?? null;
                    if ($skillId) {
                        $result = $this->battleSystem->handleUseSkill($battleState, $playerId, $skillId);
                    } else {
                        $result = $this->battleSystem->handleAttack($battleState, 'player', $playerId);
                    }
                    break;
                case 'use_hp_potion':
                case 'use_mp_potion':
                    $itemType = ($intent['action'] === 'use_hp_potion') ? 'hp' : 'mp';
                    $result = $this->battleSystem->handleUseItem($battleState, $playerId, $itemType);
                    break;
                case 'attack':
                default:
                    $result = $this->battleSystem->handleAttack($battleState, 'player', $playerId);
                    break;
                }
            }
        } else if ($actionInfo['actor_type'] === 'monster') {
            $result = $this->battleSystem->handleAttack($battleState, 'monster');
        }
        
        // 3. 处理行动结果
        if ($result) {
            // 将效果到期的日志合并到行动日志前面
            if (!empty($effectLogs)) {
                $result['log'] = array_merge($effectLogs, $result['log'] ?? []);
            }
            $this->processActionResult($monsterId, $result);

            // 检查行动是否已完成（例如吟唱结束或使用药水），如果是，则重置意图
            $actionCompleted = $result['action_completed'] ?? null;
            $isPotionUsed = in_array($intent['action'] ?? '', ['use_hp_potion', 'use_mp_potion']);

            if ($actionCompleted === 'cast_finished' || $actionCompleted === 'instant_skill_finished' || $isPotionUsed) {
                if (isset($this->monsterBattles[$monsterId])) { // 确保战斗在处理后仍然存在
                    $this->monsterBattles[$monsterId]['player_intentions'][$playerId] = ['action' => 'attack'];
                    $this->updateClientBattleIntention($monsterId, $playerId, 'attack');
                }
            }
        } else if (!empty($effectLogs)) {
            // 如果没有产生行动结果（例如，玩家被眩晕），但有效果日志，则单独处理这些日志
            $this->processActionResult($monsterId, ['state' => $battleState, 'log' => $effectLogs]);
        } else {
            // 如果没有产生结果（例如，无效的行动），也要更新战斗状态以保存冷却递减
            $this->monsterBattles[$monsterId] = $battleState;
            $this->broadcastAtbUpdate($monsterId);
        }
    }
    
    /**
     * 广播ATB状态更新
     * @param string $monsterId 怪物ID
     */
    private function broadcastAtbUpdate($monsterId) {
        if (!isset($this->monsterBattles[$monsterId])) {
            return;
        }
        
        $battleState = $this->monsterBattles[$monsterId];
        $atbStatus = $this->battleSystem->getAtbStatus($battleState);
        
        // 广播ATB状态更新，不包含日志
        $this->broadcastToBattle($monsterId, MessageProtocol::S2C_ATB_STATUS_UPDATE, [
            'monster' => $battleState['monster'],
            'all_players' => array_values($battleState['players']),
            'atb_status' => $atbStatus,
            'active_effects' => $battleState['active_effects'] ?? [] // 添加状态效果
        ]);
    }

    private function handleBattleAction($fd, $data) {
        $playerId = $data['player_id'];
        $action = $data['action']; // e.g., 'attack'
        $skillId = $data['skill_id'] ?? null;

        if (!isset($this->playerBattleMap[$playerId])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你不在战斗中']);
        }

        $monsterId = $this->playerBattleMap[$playerId];
        if (!isset($this->monsterBattles[$monsterId])) {
            unset($this->playerBattleMap[$playerId]);
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '战斗已不存在。']);
        }
        
        $battleState = &$this->monsterBattles[$monsterId];
        
        // 增加对战斗是否已结束的检查
        if ($battleState['is_over']) {
            echo "战斗 {$monsterId} 已经结束，但仍有行动被触发。忽略此行动。\n";
            return;
        }
        
        // // 检查玩家是否已被锁定行动
        // if (!empty($battleState['player_locks'][$playerId])) {
        //     return $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
        //         'message' => '你已下达指令，在角色行动前无法变更。战场瞬息万变，犹豫乃兵家大忌！',
        //         'context' => 'battle_action_locked'
        //     ]);
        // }
        
        $actingPlayer = $battleState['players'][$playerId] ?? null;
        if(!$actingPlayer || $actingPlayer['attributes']['hp'] <= 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你已无法行动']);
        }
        
        // 检查是否已经有药水使用意图
        $currentIntent = $battleState['player_intentions'][$playerId]['action'] ?? null;
        if (in_array($currentIntent, ['use_hp_potion', 'use_mp_potion']) && 
            in_array($action, ['use_hp_potion', 'use_mp_potion'])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '你已经准备使用药水，请等待执行完毕'
            ]);
        }
        
        // 记录玩家行动意图
        $battleState['player_intentions'][$playerId] = [
            'action' => $action,
            'skill_id' => $skillId
        ];
        
        // 锁定玩家行动，直到他们的回合执行
        $battleState['player_locks'][$playerId] = true;
        
        // // 构造并发送日志
        // $playerName = $actingPlayer['username'];
        // $logForSelf = '你敏锐地观察着对手，准备发起行动！';
        // $logForOthers = "{$playerName} 敏锐地观察着对手，准备发起行动！";

        // // 将通用日志添加到服务器的战斗历史中
        // if (!isset($battleState['battle_logs'])) { $battleState['battle_logs'] = []; }
        // $battleState['battle_logs'][] = $logForOthers;

        // // 给行动玩家发送第一人称版本
        // $this->sendMessage($fd, MessageProtocol::S2C_BATTLE_LOG, ['log' => [$logForSelf]]);

        // // 向其他玩家广播第三人称版本
        // $this->broadcastToBattle($monsterId, MessageProtocol::S2C_BATTLE_LOG, ['log' => [$logForOthers]], [$playerId]);
    }

    private function handleMonsterAttackRequest($fd, $data) {
        // 在服务端ATB系统中，这个方法不再需要
        // 怪物的攻击由服务器决定何时执行
        // 我们可以简单地返回一个信息，告知客户端系统已更改
        $playerId = $data['player_id'];
        
        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
            'message' => '战斗系统已更新，服务器现在控制战斗流程。',
            'context' => 'battle_system_update'
        ]);
    }

    private function processActionResult($monsterId, $result) {
        if (!isset($this->monsterBattles[$monsterId])) {
            return;
        }
        
        // 保存当前的意图状态
        $playerIntentions = isset($this->monsterBattles[$monsterId]['player_intentions']) ? 
            $this->monsterBattles[$monsterId]['player_intentions'] : [];
            
        // 更新服务器的战斗状态
        $this->monsterBattles[$monsterId] = $result['state'];
        $battleState = $this->monsterBattles[$monsterId];
        
        // 恢复意图状态，确保药水使用后的攻击意图被保留
        if (!empty($playerIntentions)) {
            if (!isset($battleState['player_intentions'])) {
                $battleState['player_intentions'] = [];
            }
            
            foreach ($playerIntentions as $pid => $intent) {
                // 只保留药水使用后设置的攻击意图
                if ($intent['action'] === 'attack' && 
                    isset($this->monsterBattles[$monsterId]['player_intentions'][$pid]) &&
                    in_array($this->monsterBattles[$monsterId]['player_intentions'][$pid]['action'], ['use_hp_potion', 'use_mp_potion'])) {
                    $battleState['player_intentions'][$pid] = $intent;
                    echo "保留玩家 {$pid} 的攻击意图\n";
                }
            }
            
            $this->monsterBattles[$monsterId]['player_intentions'] = $battleState['player_intentions'];
        }
        
        // 处理逃跑结果
        if (isset($result['fled_player_id'])) {
            // 逃跑成功
            $this->handlePlayerFled($result['fled_player_id'], $monsterId, $result['log']);
            // 注意：如果这是最后一个玩家，战斗可能已经结束了。
            // 需要检查战斗是否还存在。
            if (!isset($this->monsterBattles[$monsterId])) {
                return; // 战斗已结束，无需继续处理
            }
            $battleState = $this->monsterBattles[$monsterId]; // 重新获取状态
        } elseif (isset($result['log']) && is_array($result['log'])) {
            // 检查是否有逃跑失败的日志
            foreach ($result['log'] as $logEntry) {
                if (strpos($logEntry, '试图逃跑，但') !== false) {
                    // 提取玩家ID
                    $matches = [];
                    if (preg_match('/^(.+?) 试图逃跑/', $logEntry, $matches)) {
                        $playerName = $matches[1];
                        // 查找对应的玩家ID
                        foreach ($battleState['players'] as $pid => $pData) {
                            if ($pData['username'] === $playerName) {
                                // 设置下一轮行动为攻击
                                $battleState['player_intentions'][$pid] = ['action' => 'attack'];
                                echo "玩家 {$pid} ({$playerName}) 逃跑失败，下一轮将自动攻击\n";
                                
                                // 通知客户端行动已更改
                                $this->updateClientBattleIntention($monsterId, $pid, 'attack');
                                break;
                            }
                        }
                    }
                }
            }
        }
        
        // 更新数据库中的玩家HP以持久化
        $db = Database::getInstance();
        foreach ($battleState['players'] as $p_id => $p_data) {
            $db->query("UPDATE player_attributes SET hp = ? WHERE account_id = ?", [$p_data['attributes']['hp'], $p_id]);
        }
        
        // 将新的战斗日志添加到战斗日志历史中
        if (!isset($battleState['battle_logs'])) {
            $battleState['battle_logs'] = [];
        }
        if (isset($result['log']) && !empty($result['log'])) {
            $battleState['battle_logs'] = array_merge($battleState['battle_logs'], $result['log']);
            // 更新战斗状态中的日志历史
            $this->monsterBattles[$monsterId]['battle_logs'] = $battleState['battle_logs'];
        }
        
        // 如果是玩家攻击，记录伤害贡献
        if (isset($result['player_id']) && isset($result['damage']) && $result['damage'] > 0) {
            $playerId = $result['player_id'];
            
            // 初始化伤害贡献记录（如果不存在）
            if (!isset($battleState['damage_contributions'])) {
                $battleState['damage_contributions'] = [];
            }
            
            // 初始化该玩家的伤害贡献（如果不存在）
            if (!isset($battleState['damage_contributions'][$playerId])) {
                $battleState['damage_contributions'][$playerId] = 0;
            }
            
            // 累加玩家的伤害贡献
            $battleState['damage_contributions'][$playerId] += $result['damage'];
            
            // 更新战斗状态中的伤害贡献记录
            $this->monsterBattles[$monsterId]['damage_contributions'] = $battleState['damage_contributions'];
            
            // 调试输出
            // echo "玩家 {$playerId} 对怪物 {$monsterId} 造成了 {$result['damage']} 点伤害，累计贡献: {$battleState['damage_contributions'][$playerId]}\n";
        }
        
        // 确保药水使用后的攻击意图被保存
        if (isset($battleState['player_intentions'])) {
            foreach ($battleState['player_intentions'] as $pid => $intent) {
                $this->monsterBattles[$monsterId]['player_intentions'][$pid] = $intent;
            }
        }
        
        // 保存更新后的战斗状态
        $this->monsterBattles[$monsterId] = $battleState;

        // 注意：移除了立即的冷却减少，改为在下一回合开始时减少

        // 广播战斗更新
        $this->broadcastToBattle(
            $monsterId,
            MessageProtocol::S2C_BATTLE_UPDATE,
            [
                'monster' => $battleState['monster'],
                'all_players' => array_values($battleState['players']),
                'log' => $result['log'], // 只发送新的日志条目
                'atb_status' => $this->battleSystem->getAtbStatus($battleState),
                'skill_cooldowns' => $battleState['skill_cooldowns'] ?? [], // 添加技能冷却信息
                'active_effects' => $battleState['active_effects'] ?? [] // 添加状态效果
            ]
        );
        
        // 如果攻击结束了战斗
        if ($battleState['is_over']) {
            $reason = ($battleState['winner'] === 'players') ? 'victory' : 'defeated';
            // 在调用endBattle之前，再检查一次，防止重复调用
            if (empty($battleState['end_reason_dispatched'])) {
                $battleState['end_reason_dispatched'] = true; // 标记已处理
                $this->endBattle($battleState, $reason, $result['log']);
            }
            return; // 战斗已结束，立即返回
        }
    }

    private function endBattle(&$battleState, $reason, $finalLog = []) {
        if (!$battleState) return;

        $monsterId = $battleState['monster']['id'];
        $sceneId = $battleState['scene_id'];

        // 停止战斗定时器
        if (isset($this->battleTimers[$monsterId])) {
            swoole_timer_clear($this->battleTimers[$monsterId]);
            unset($this->battleTimers[$monsterId]);
        }

        // 将最终日志添加到战斗日志历史中
        if (!isset($battleState['battle_logs'])) {
            $battleState['battle_logs'] = [];
        }
        $battleState['battle_logs'] = array_merge($battleState['battle_logs'], $finalLog);
        
        // 如果是胜利，先处理战利品掉落并将信息添加到战斗日志中
        $droppedItems = [];
        if ($reason === 'victory') {
            $monsterWithContributions = $battleState['monster'];
            $monsterWithContributions['damage_contributions'] = $battleState['damage_contributions'] ?? [];
            $droppedItems = $this->lootManager->handleMonsterDrop($monsterWithContributions, $battleState['players'], $battleState['scene_id']);
            
            // 更新任务进度 - 处理击杀怪物类型的任务目标
            $monsterTemplateId = $battleState['monster']['monster_template_id'] ?? null;
            if ($monsterTemplateId) {
                if (!isset($this->questManager)) {
                    $this->questManager = new QuestManager(function() {
                        return $this->db->getConnection();
                    });
                }
                
                // 为参与战斗的每个玩家更新任务进度
                foreach ($battleState['players'] as $playerId => $playerData) {
                    // 只为存活的玩家更新任务进度
                    if ($playerData['attributes']['hp'] > 0) {
                        $updatedQuests = $this->questManager->checkQuestObjectiveUpdates($playerId, 'kill', $monsterTemplateId);
                        
                        // 发送任务进度更新通知
                        $fd = $this->playerConnections[$playerId] ?? null;
                        if ($fd && !empty($updatedQuests)) {
                            foreach ($updatedQuests as $update) {
                                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                                    'action' => 'progress_updated',
                                    'quest_id' => $update['quest_id'],
                                    'objective_id' => $update['objective_id'],
                                    'description' => $update['description'],
                                    'new_progress' => $update['new_progress'],
                                    'target' => $update['target'],
                                    'can_complete' => $update['can_complete'],
                                    'receiver_npc_name' => $update['receiver_npc_name']
                                ]);
                            }
                        }
                    }
                }
            }
            
            // 将物品掉落信息添加到战斗日志中
            if (!empty($droppedItems)) {
                $monsterName = $battleState['monster']['username'];
                $dropLogItems = [];
                $itemAssignments = []; // 记录物品分配给哪些玩家
                
                foreach ($droppedItems as $item) {
                    $quantityText = $item['quantity'] > 1 ? " (x{$item['quantity']})" : "";
                    $dropLogItems[] = "{$item['name']}{$quantityText}";
                    
                    // 如果物品有保护玩家，记录分配信息
                    if (!empty($item['protected_player_id'])) {
                        $playerId = $item['protected_player_id'];
                        $playerName = $battleState['players'][$playerId]['username'] ?? '未知玩家';
                        
                        if (!isset($itemAssignments[$playerId])) {
                            $itemAssignments[$playerId] = [
                                'name' => $playerName,
                                'items' => []
                            ];
                        }
                        
                        $itemAssignments[$playerId]['items'][] = "{$item['name']}{$quantityText}";
                    }
                }
                
                // 添加总体掉落日志
                $battleState['battle_logs'][] = "【战利品】{$monsterName} 掉落了: " . implode('、', $dropLogItems) . "。";
                
                // 添加分配信息
                foreach ($itemAssignments as $assignment) {
                    $playerName = $assignment['name'];
                    $items = implode('、', $assignment['items']);
                    $battleState['battle_logs'][] = "【分配】{$playerName} 获得了: {$items} 的拾取权。";
                }
            }
        }

        // 持久化战斗日志到数据库
        try {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            
            // 准备参与者信息
            $participants = [];
            foreach ($battleState['players'] as $playerId => $playerData) {
                $participants[] = [
                    'id' => $playerId,
                    'name' => $playerData['username']
                ];
            }
            
            // 压缩存储战斗日志
            $logData = json_encode($battleState['battle_logs']);
            
            // 插入战斗日志记录
            $stmt = $conn->prepare(
                "INSERT INTO battle_logs 
                (battle_id, scene_id, monster_id, log_data, start_time, end_time, battle_result, participants) 
                VALUES (?, ?, ?, ?, FROM_UNIXTIME(?), NOW(), ?, ?)"
            );
            
            // 获取战斗开始时间（如果没有记录，使用当前时间减去60秒作为估计值）
            $battleStartTime = $battleState['start_time'] ?? (time() - 60);
            
            $stmt->execute([
                $monsterId,
                $sceneId,
                $battleState['monster']['id'],
                $logData,
                $battleStartTime,
                $reason,
                json_encode($participants)
            ]);
            
            echo "已保存战斗日志，ID: " . $conn->lastInsertId() . "\n";
        } catch (Exception $e) {
            echo "保存战斗日志失败: " . $e->getMessage() . "\n";
        }

        // 新增：保存战斗后玩家的状态（HP/MP）到数据库
        try {
            $conn = $this->db->getConnection();
            $updateStmt = $conn->prepare(
                "UPDATE player_attributes SET hp = ?, mp = ? WHERE account_id = ?"
            );
            
            foreach ($battleState['players'] as $playerId => $playerData) {
                // 从战斗状态中获取最终的HP和MP
                $finalHp = max(0, $playerData['attributes']['hp']);
                $finalMp = max(0, $playerData['attributes']['mp']);
                
                $updateStmt->execute([$finalHp, $finalMp, $playerId]);
            }
            echo "已更新战斗后玩家的状态。\n";
        } catch (Exception $e) {
            echo "更新玩家战斗后状态失败: " . $e->getMessage() . "\n";
        }

        // 准备战斗结束时的奖励日志（经验值、掉落物等）
        $rewardLogs = [];
        
        // 处理战斗胜利奖励
        if ($reason === 'victory') {
            // 授予经验值
            $experienceGained = $battleState['monster']['attributes']['experience_reward'] ?? 0;
            if ($experienceGained > 0) {
                $livingPlayers = array_filter($battleState['players'], fn($p) => $p['attributes']['hp'] > 0);
                if (!empty($livingPlayers)) {
                    // 获取伤害贡献记录
                    $damageContributions = $battleState['damage_contributions'] ?? [];
                    $totalDamage = array_sum($damageContributions);
                    
                    // 如果没有伤害记录或总伤害为0，则平均分配经验值
                    if ($totalDamage <= 0) {
                        $experiencePerPlayer = floor($experienceGained / count($livingPlayers));
                        
                        foreach ($livingPlayers as $playerId => $playerData) {
                            $rewardLogs[] = "{$playerData['username']} 获得了 {$experiencePerPlayer} 点经验值。";
                            $this->grantExperience($playerId, $experiencePerPlayer);
                        }
                    } else {
                        // 根据伤害贡献比例分配经验值
                        foreach ($livingPlayers as $playerId => $playerData) {
                            $playerDamage = $damageContributions[$playerId] ?? 0;
                            $contributionRatio = $playerDamage / $totalDamage;
                            $playerExperience = max(1, floor($experienceGained * $contributionRatio)); // 至少给1点经验
                            
                            // 添加伤害贡献比例信息到日志
                            $damagePercentage = round($contributionRatio * 100);
                            $rewardLogs[] = "【经验奖励】{$playerData['username']} 贡献了 {$damagePercentage}% 的伤害，获得了 {$playerExperience} 点经验值！";
                            
                            $this->grantExperience($playerId, $playerExperience);
                        }
                    }
                }
            }
            
            // 注意：战利品掉落处理已经在持久化战斗日志之前完成
            
            // 为每个玩家创建个性化的掉落日志
            $playerRewardLogs = [];
            foreach ($battleState['players'] as $playerId => $playerData) {
                // 复制通用的奖励日志
                $playerRewardLogs[$playerId] = $rewardLogs;
            }
            
            // 处理掉落物品
            if (!empty($droppedItems)) {
                $monsterName = $battleState['monster']['username'];
                
                // 创建所有玩家都能看到的通用掉落信息
                $commonDropLog = "【战利品】{$monsterName} 掉落了一些物品！";
                
                foreach ($battleState['players'] as $playerId => $playerData) {
                    // 添加通用掉落信息
                    $playerRewardLogs[$playerId][] = $commonDropLog;
                    
                    // 只向获得物品保护的玩家显示具体物品信息
                    foreach ($droppedItems as $item) {
                        if ($item['protected_player_id'] == $playerId) {
                            $quantityText = $item['quantity'] > 1 ? " (x{$item['quantity']})" : "";
                            $playerRewardLogs[$playerId][] = "【掉落物品】你获得了 {$item['name']}{$quantityText} 的拾取权！";
                        }
                    }
                }
            }
        }

        // 持久化怪物最终状态
        $monsterInBattle = $battleState['monster'];
        try {
            RedisManager::getInstance()->with(function($redis) use ($sceneId, $monsterId, $monsterInBattle, $reason) {
                $existingMonsterJson = $redis->hget("scene_monsters:{$sceneId}", $monsterId);
            
                if ($existingMonsterJson) {
                    $monsterDataToUpdate = json_decode($existingMonsterJson, true);
                    $monsterDataToUpdate['hp'] = $monsterInBattle['attributes']['hp'];

                    // 如果怪物死亡，记录死亡时间
                    if ($monsterInBattle['attributes']['hp'] <= 0) {
                        $monsterDataToUpdate['death_time'] = time();
                    }

                    $redis->hset("scene_monsters:{$sceneId}", $monsterId, json_encode($monsterDataToUpdate));
                    echo "战斗结束, 已持久化怪物 {$monsterId} 的状态. HP: {$monsterDataToUpdate['hp']}\n";

                    // 如果是胜利，检查是否是最后一个怪物
                    if ($reason === 'victory') {
                        $anyMonsterAlive = false;
                        $allMonstersInScene = $redis->hgetall("scene_monsters:{$sceneId}");
                        foreach ($allMonstersInScene as $key => $monsterJson) {
                            if ($key === '_EMPTY_') continue;
                            
                            $monsterData = json_decode($monsterJson, true);
                            if (isset($monsterData['hp']) && $monsterData['hp'] > 0) {
                                $anyMonsterAlive = true;
                                break;
                            }
                        }

                        if (!$anyMonsterAlive) {
                            // 这是最后一个被击败的怪物
                            $redis->set("scene_last_monster_death_time:{$sceneId}", time());
                            echo "场景 {$sceneId} 的最后一个怪物被击败，记录清空时间。\n";
                        }
                    }

                } else {
                    error_log("Critical: Monster {$monsterId} not found in Redis cache during endBattle. Cannot persist HP state.");
                }
            });
        } catch (Exception $e) { 
            echo "持久化怪物状态失败: " . $e->getMessage() . "\n";
        }

        // 获取最终的场景数据
        $finalSceneData = $this->sceneManager->getSceneData($sceneId);
        $finalSceneItems = $this->sceneManager->getSceneItems($sceneId);
        
        // 获取战斗中的玩家列表，而不是场景中的所有玩家
        $battlePlayers = array_values($battleState['players']);
        
        // 向每个玩家发送个性化的战斗结束消息
        foreach ($battleState['players'] as $playerId => $playerData) {
            $fd = $this->playerConnections[$playerId] ?? null;
            if (!$fd) continue;
            
            // 标记物品保护状态
            $sceneDataCopy = $finalSceneData;
            $sceneDataCopy['items'] = $finalSceneItems;
            $this->markProtectedItems($sceneDataCopy, $playerId);
            
            // 使用玩家特定的奖励日志
            $playerSpecificLogs = $playerRewardLogs[$playerId] ?? $rewardLogs;
            
            $responsePayload = [
                'reason' => $reason,
                'log' => $playerSpecificLogs, // 使用玩家特定的日志
                'final_state' => [ 
                    'monster' => $monsterInBattle,
                    'players' => $battlePlayers,
                    'scene' => $sceneDataCopy,
                    'items' => $sceneDataCopy['items']
                ]
            ];
            
            $this->sendMessage($fd, MessageProtocol::S2C_BATTLE_ENDED, $responsePayload);
        }

        // 清理战场
        $participantIds = array_keys($battleState['players']);
        foreach($participantIds as $pid) {
            unset($this->playerBattleMap[$pid]);
        }
        
        // 通过引用直接销毁状态
        unset($this->monsterBattles[$monsterId]);
        $battleState = null;

        echo "怪物 {$monsterId} 的战斗已结束，原因: {$reason}。\n";
        $this->broadcastPlayerUpdate($sceneId);
    }
    
    private function removePlayerFromBattle($playerId, $reason) {
        if (!isset($this->playerBattleMap[$playerId])) return;
        
        $monsterId = $this->playerBattleMap[$playerId];
        $battleState = &$this->monsterBattles[$monsterId];
        $player = $battleState['players'][$playerId];

        unset($battleState['players'][$playerId]);
        unset($this->playerBattleMap[$playerId]);

        $logMessage = '';
        if ($reason === 'flee') {
            $logMessage = "{$player['username']} 逃跑了！";
        } elseif ($reason === 'disconnect') {
            $logMessage = "{$player['username']} 断线，退出了战斗。";
        } elseif ($reason === 'dead_left') {
            $logMessage = "{$player['username']} 在阵亡后离开了战场。";
        }

        // 将玩家离开的日志添加到战斗日志历史中
        if (!empty($logMessage)) {
            if (!isset($battleState['battle_logs'])) {
                $battleState['battle_logs'] = [];
            }
            $battleState['battle_logs'][] = $logMessage;
        }

        // 如果战场上还有人，则通知他们
        if (count($battleState['players']) > 0) {
            $this->broadcastToBattle(
                $monsterId, 
                MessageProtocol::S2C_BATTLE_TURN_UPDATE,
                [
                    'log' => [$logMessage],
                    'monster' => $battleState['monster'],
                    'all_players' => array_values($battleState['players']),
                    'atb_status' => $this->battleSystem->getAtbStatus($battleState)
                ]
            );
        } else {
            // 最后一人离开，战斗结束
            echo "最后一名玩家 {$playerId} 已离开，怪物 {$monsterId} 的战斗结束。\n";
            $this->endBattle($this->monsterBattles[$monsterId], 'last_player_left');
        }

        // 广播场景更新
        $this->broadcastPlayerUpdate($battleState['scene_id']);
    }

    private function broadcastPlayerUpdate($sceneId, $actorId = null, $action = null, $playerList = null) {
        $players = $playerList ?? $this->sceneManager->getScenePlayers($sceneId);
        $monsters = $this->sceneManager->getSceneMonsters($sceneId);
        $items = $this->sceneManager->getSceneItems($sceneId);
        $npcs = $this->npcManager->getSceneNpcs($sceneId);
        
        // 创建基础消息
        $basePayload = [
            'action' => $action,
            'scene_id' => $sceneId,
            'player_id' => $actorId,
            'current_players' => $players,
            'current_monsters' => $monsters,
            'current_items' => $items,
            'current_npcs' => $npcs
        ];
        
        // 广播给场景中的所有玩家
        $this->broadcastToScene(
            $sceneId,
            MessageProtocol::S2C_SCENE_PLAYER_CHANGE,
            $basePayload,
            $action === 'enter' ? [$actorId] : []
        );
    }
    
    private function broadcastToBattle($monsterId, $opcode, $payload, $excludePlayers = []) {
        if (!isset($this->monsterBattles[$monsterId])) return;

        $battleState = $this->monsterBattles[$monsterId];
        foreach ($battleState['players'] as $playerId => $playerData) {
            if (in_array($playerId, $excludePlayers)) continue;
            
            $fd = $this->playerConnections[$playerId] ?? null;
            if ($fd && isset($this->fdInfo[$fd])) {
                // 检查连接是否仍然有效
                if ($this->server->exist($fd) && $this->server->isEstablished($fd)) {
                    $this->sendMessage($fd, $opcode, $payload);
                } else {
                    echo "战斗中玩家 {$playerId} 的连接 FD: {$fd} 已失效，跳过广播\n";
                }
            }
        }
    }

    private function broadcastToScene($sceneId, $opcode, $payload, $excludePlayers = []) {
        $playerIds = $this->sceneManager->getScenePlayerIds($sceneId);
        
        foreach ($playerIds as $playerId) {
            if (in_array($playerId, $excludePlayers)) continue;
    
            $playerPayload = $payload; // 为每个玩家创建一个独立的 payload 副本
    
            // 检查消息是否需要进行物品保护标记
            $needsMarking = ($opcode === MessageProtocol::S2C_SCENE_PLAYER_CHANGE && isset($playerPayload['current_items'])) ||
                            ($opcode === MessageProtocol::S2C_SCENE_RESET && isset($playerPayload['scene']['items']));
    
            if ($needsMarking) {
                $itemsToMark = [];
                if ($opcode === MessageProtocol::S2C_SCENE_PLAYER_CHANGE) {
                    $itemsToMark = $playerPayload['current_items'];
                } else { // S2C_SCENE_RESET
                    $itemsToMark = $playerPayload['scene']['items'];
                }
    
                // 创建一个包装器来调用标记函数
                $sceneDataWrapper = ['items' => $itemsToMark];
                $this->markProtectedItems($sceneDataWrapper, $playerId);
                
                // 将标记后的物品列表放回 payload 副本中
                if ($opcode === MessageProtocol::S2C_SCENE_PLAYER_CHANGE) {
                    $playerPayload['current_items'] = $sceneDataWrapper['items'];
                } else { // S2C_SCENE_RESET
                    $playerPayload['scene']['items'] = $sceneDataWrapper['items'];
                }
            }
            
            // 发送最终处理过的 payload
            if ($fd = $this->playerConnections[$playerId] ?? null) {
                // 检查连接是否仍然有效
                if ($this->server->exist($fd) && $this->server->isEstablished($fd)) {
                    $this->sendMessage($fd, $opcode, $playerPayload);
                } else {
                    echo "玩家 {$playerId} 的连接 FD: {$fd} 已失效，跳过广播\n";
                }
            }
        }
    }
    
    private function sendUnencrypted($fd, $opcode, $payload = []) {
        try {
            // 检查连接是否存在且有效
            if (!$this->server->exist($fd)) {
                echo "连接 FD: {$fd} 已不存在，无法发送未加密消息\n";
                return false;
            }
            
            // 检查连接是否已建立WebSocket
            if (!$this->server->isEstablished($fd)) {
                echo "连接 FD: {$fd} WebSocket未建立，无法发送未加密消息\n";
                return false;
            }
            
            $message = MessageProtocol::encode($opcode, $payload);
            if ($message === false) {
                echo "未加密消息编码失败，无法发送到 FD: {$fd}\n";
                return false;
            }
            
            return $this->server->push($fd, $message, WEBSOCKET_OPCODE_BINARY);
        } catch (Throwable $e) {
            echo "发送未加密消息时发生错误: " . $e->getMessage() . "\n";
            return false;
        }
    }

        public function sendMessage($fd, $opcode, $payload = []) {
        try {
            // 检查连接是否存在且有效
            if (!$this->server->exist($fd)) {
                echo "连接 FD: {$fd} 已不存在，无法发送消息\n";
                return false;
            }
            
            // 检查连接是否已认证
            if (!isset($this->fdInfo[$fd])) {
                echo "连接 FD: {$fd} 未认证，无法发送加密消息\n";
                return false;
            }
            
            // 检查连接是否已建立WebSocket
            if (!$this->server->isEstablished($fd)) {
                echo "连接 FD: {$fd} WebSocket未建立，无法发送消息\n";
                return false;
            }
            
            $secretKey = $this->fdInfo[$fd]['secret_key'];
            $binaryMsg = SecureMessageProtocol::encode(['opcode' => $opcode, 'payload' => $payload], $secretKey);
            
            if ($binaryMsg === false) {
                echo "消息编码失败，无法发送到 FD: {$fd}\n";
                return false;
            }
            
            return $this->server->push($fd, $binaryMsg, WEBSOCKET_OPCODE_BINARY);
        } catch (Throwable $e) {
            echo "发送消息时发生错误: " . $e->getMessage() . "\n";
            return false;
        }
    }
            
    private function removePlayerFromScenes($playerId) {
        echo "开始为玩家 {$playerId} 处理场景移除。\n";
        try {
            $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId){
                return $redis->get("player_scene:{$playerId}");
            });
            
            if ($sceneId) {
                echo "玩家在场景 {$sceneId} 中，正在从Redis移除并广播。\n";
                // 仅从 Redis 中移除，不更新数据库，以保留玩家的最后位置
                RedisManager::getInstance()->with(function($redis) use ($sceneId, $playerId){
                    $redis->srem("scene_players:{$sceneId}", $playerId);
                    $redis->del("player_scene:{$playerId}");
                });
                
                // 获取更新后的场景玩家列表
                $players = $this->sceneManager->getScenePlayers($sceneId);
                
                // 广播场景更新
                $this->broadcastToScene(
                    $sceneId, 
                    MessageProtocol::S2C_SCENE_PLAYER_CHANGE, 
                    [
                    'action' => 'leave',
                    'scene_id' => $sceneId,
                    'player_id' => $playerId,
                        'current_players' => $players,
                        'current_monsters' => $this->sceneManager->getSceneMonsters($sceneId),
                        'current_items' => $this->sceneManager->getSceneItems($sceneId),
                        'current_npcs' => $this->npcManager->getSceneNpcs($sceneId)
                    ],
                    [$playerId] // 排除离开的玩家
                );

                echo "玩家 {$playerId} 已从场景 {$sceneId} 的在线列表移除并广播。\n";
            } else {
                echo "玩家 {$playerId} 不在任何场景中，无需广播。\n";
            }
        } catch (Exception $e) {
            echo "移除玩家场景信息失败: " . $e->getMessage() . "\n";
        }
    }
    
    public function performHealthCheck() {
        $timestamp = date('Y-m-d H:i:s');
        $connectionCount = ($this->server && isset($this->server->connections)) ? count($this->server->connections) : 0;

        // 数据库健康检查
        $dbStatus = 'OK';
        try {
            $db = Database::getInstance();
            $db->keepAlive(); // 使用新的保活方法
            $dbConnectionStatus = $db->getConnectionStatus();

            echo "数据库连接状态: 有效连接数={$dbConnectionStatus['total_connections']}, MySQL线程={$dbConnectionStatus['mysql_threads_connected']}, 当前连接ID={$dbConnectionStatus['current_connection_id']}\n";
        } catch (Exception $e) {
            $dbStatus = 'FAILED';
            echo "数据库健康检查失败: " . $e->getMessage() . "\n";

            try {
                Database::getInstance()->reconnect();
                echo "数据库重连尝试完成\n";

                // 重新测试连接
                Database::getInstance()->keepAlive();
                $dbStatus = 'RECOVERED';
                echo "数据库连接已恢复\n";
            } catch (Exception $e2) {
                echo "数据库重连失败: " . $e2->getMessage() . "\n";
            }
        }

        // Redis健康检查
        $redisStatus = 'OK';
        try {
            RedisManager::getInstance()->with(function($redis) {
                $redis->ping();
            });
        } catch (Exception $e) {
            $redisStatus = 'FAILED';
            echo "Redis健康检查失败: " . $e->getMessage() . "\n";
        }

        // 内存使用情况
        $memoryUsage = memory_get_usage(true);
        $memoryPeak = memory_get_peak_usage(true);
        $memoryUsageMB = round($memoryUsage / 1024 / 1024, 2);
        $memoryPeakMB = round($memoryPeak / 1024 / 1024, 2);

        echo "健康检查 [{$timestamp}] - WebSocket连接: {$connectionCount}, 数据库: {$dbStatus}, Redis: {$redisStatus}, 内存: {$memoryUsageMB}MB (峰值: {$memoryPeakMB}MB)\n";

        // 如果有严重问题，记录到错误日志
        if ($dbStatus === 'FAILED' || $redisStatus === 'FAILED') {
            error_log("Health check critical issues - DB: {$dbStatus}, Redis: {$redisStatus} at {$timestamp}");
        }
    }

    private function handleResumeSession($fd, $data) {
        $playerId = $data['player_id'] ?? null;
        $clientKeyBase64 = $data['session_key'] ?? null;

        if (!$playerId || !$clientKeyBase64) {
            return $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的会话恢复请求']);
        }

        $storedKey = RedisManager::getInstance()->with(function($redis) use ($playerId) {
            return $redis->get("session:{$playerId}");
        });

        if ($storedKey && hash_equals($storedKey, base64_decode($clientKeyBase64))) {
            echo "玩家 {$playerId} (FD {$fd}) 的会话恢复成功。\n";

            $player = $this->getPlayerWithAttributes($playerId);
            if (!$player) {
                $this->server->close($fd);
                return;
            }
            
            // **场景有效性验证和分配**
            $currentSceneId = $player['attributes']['current_scene_id'] ?? null;
            $sceneData = $currentSceneId ? $this->sceneManager->getSceneData($currentSceneId) : null;

            if (!$sceneData) { // 如果场景ID为空或无效
                 $message = $currentSceneId ? "场景 {$currentSceneId} 无效" : "没有当前场景";
                error_log("玩家 {$player['id']} 的{$message}，将重置到初始场景。");

                    $initialSceneId = $this->sceneManager->getInitialSceneId();
                $player['attributes']['current_scene_id'] = $initialSceneId;
                Database::getInstance()->query("UPDATE player_attributes SET current_scene_id = ?, logindate = NOW() WHERE account_id = ?", [$initialSceneId, $player['id']]);
            } else {
                // 场景有效，只更新登录时间
                Database::getInstance()->query("UPDATE player_attributes SET logindate = NOW() WHERE account_id = ?", [$playerId]);
            }

            $secretKey = random_bytes(32);
            RedisManager::getInstance()->with(function($redis) use ($playerId, $secretKey) {
                $redis->set("session:{$playerId}", $secretKey, 3600);
                
                // 设置玩家在线状态标记，用于后台管理系统
                $redis->set("player_fd:{$playerId}", 1, 3600); 
                echo "玩家 {$playerId} 的在线状态标记已恢复\n";
            });
            $this->fdInfo[$fd] = ['player_id' => $playerId, 'secret_key' => $secretKey];
            $this->playerConnections[$playerId] = $fd;
            
            $this->sendUnencrypted($fd, MessageProtocol::S2C_LOGIN_SUCCESS, [
                'player' => $player,
                'session_key' => base64_encode($secretKey)
            ]);
        } else {
            echo "玩家 {$playerId} 的会话恢复失败，密钥不匹配或已过期。\n";
            $this->sendUnencrypted($fd, MessageProtocol::S2C_ERROR, ['message' => '会话无效或已过期，请重新登录']);
        }
    }

    private function handleLogout($fd) {
        if (isset($this->fdInfo[$fd])) {
            $playerId = $this->fdInfo[$fd]['player_id'];
            echo "玩家 {$playerId} 主动退出。\n";
            RedisManager::getInstance()->with(function($redis) use ($playerId) {
                $redis->del("session:{$playerId}");
            });
        }
        $this->server->close($fd);
    }

    private function handleMove($fd, $data) {
        $playerId = $data['player_id'];
        $direction = $data['direction'];
        
        $currentSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
            return $redis->get("player_scene:{$playerId}");
        });
        
        if (!$currentSceneId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你当前不在任何场景中，无法移动。']);
        }

        $currentScene = $this->sceneManager->getSceneData($currentSceneId);
        if (!$currentScene) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取你所在的当前场景信息。']);
        }

        $exits = $this->sceneManager->getSceneExits($currentScene['x'], $currentScene['y'], $currentScene['z']);
        
        $targetSceneInfo = $exits[$direction] ?? null;

        if (!$targetSceneInfo || !isset($targetSceneInfo['id'])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '这个方向没有路。']);
        }
        
        $targetSceneId = $targetSceneInfo['id'];
        
        list($responseOpcode, $responsePayload) = $this->handleEnterScene($fd, ['player_id' => $playerId, 'scene_id' => $targetSceneId]);
        if ($responseOpcode) {
            $this->sendMessage($fd, $responseOpcode, $responsePayload);
        }
    }

    private function handleGetInventory($fd, $data, $returnData = false) {
        $playerId = $data['player_id'];
        $db = Database::getInstance();

        // 1. 获取货币信息
        $currencyStmt = $db->query("SELECT gold, diamonds FROM player_attributes WHERE account_id = ?", [$playerId]);
        $currencies = $currencyStmt->fetch(PDO::FETCH_ASSOC);
        $currencyStmt->closeCursor();
        if (!$currencies) {
            $currencies = ['gold' => 0, 'diamonds' => 0];
        }

        // 2. 获取物品信息
        $stmt = $db->query(
            "SELECT
                inv.id as inventory_id, inv.item_template_id, inv.quantity, inv.is_equipped, inv.instance_data, inv.is_bound,
                it.name, it.category, it.stackable, it.effects, it.description, it.is_consumable,
                it.sell_price,
                ed.slot, ed.job_restriction, ed.stats, ed.sockets, ed.grants_job_id,
                j_grant.name as granted_job_name,
                j_restrict.name as job_restriction_name
             FROM player_inventory inv
             JOIN item_templates it ON inv.item_template_id = it.id
             LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
             LEFT JOIN jobs j_grant ON ed.grants_job_id = j_grant.id
             LEFT JOIN jobs j_restrict ON ed.job_restriction = j_restrict.id
             WHERE inv.player_id = ?
             ORDER BY inv.is_equipped DESC, it.category, it.name",
            [$playerId]
        );
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $equipped = [];
        $backpack = [];
        foreach ($items as $item) {
            if ($item['is_equipped']) {
                $equipped[] = $item;
            } else {
                $backpack[] = $item;
            }
        }

        $inventoryData = [
            'equipped' => $equipped,
            'backpack' => $backpack,
            'currencies' => $currencies
        ];

        // 如果需要返回数据而不是发送消息
        if ($returnData) {
            return $inventoryData;
        }

        // 3. 将所有数据一起发送
        $this->sendMessage($fd, MessageProtocol::S2C_INVENTORY_DATA, $inventoryData);
    }

    private function handleUseItem($fd, $data) {
        $playerId = $data['player_id'];
        $inventoryId = $data['inventory_id'] ?? 0;

        if (!$inventoryId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的物品请求。']);
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();

        try {
            // 1. 获取物品信息，并锁定行以防止并发问题
            $stmt = $conn->prepare(
                "SELECT inv.id, inv.quantity, it.name, it.is_consumable, it.effects 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.id = ? AND inv.player_id = ? FOR UPDATE"
            );
            $stmt->execute([$inventoryId, $playerId]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$item) { throw new Exception('物品不在你的背包中。'); }

            $effects = !empty($item['effects']) ? json_decode($item['effects'], true) : [];
            if (json_last_error() !== JSON_ERROR_NONE) { throw new Exception('物品效果数据损坏。'); }

            // 检查是否是技能书（有learn_skill_id属性）
            $isSkillBook = isset($effects['learn_skill_id']);
            
            // 如果既不是消耗品也不是技能书，则不能使用
            if (!$item['is_consumable'] && !$isSkillBook) { 
                throw new Exception('这个物品不能被使用。'); 
            }

            // 获取玩家当前属性，检查是否需要使用药水
            $playerAttrStmt = $conn->prepare("SELECT hp, max_hp, mp, max_mp FROM player_attributes WHERE account_id = ? FOR UPDATE");
            $playerAttrStmt->execute([$playerId]);
            $playerAttrs = $playerAttrStmt->fetch(PDO::FETCH_ASSOC);
            $playerAttrStmt->closeCursor();
            if (!$playerAttrs) { throw new Exception('找不到玩家属性。'); }

            // 检查是否是回血药水且血量已满
            if (isset($effects['hp']) && $effects['hp'] > 0 && $playerAttrs['hp'] >= $playerAttrs['max_hp']) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => '你的生命值已满，无需使用回血药水。',
                    'context' => 'item_use_fail'
                ]);
            }

            // 检查是否是回蓝药水且魔力已满
            if (isset($effects['mp']) && $effects['mp'] > 0 && $playerAttrs['mp'] >= $playerAttrs['max_mp']) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => '你的魔力值已满，无需使用回蓝药水。',
                    'context' => 'item_use_fail'
                ]);
            }

            $experienceToGrant = null;
            $knowledgePointsToGrant = null;
            $otherEffectsApplied = false;


            // 2. 分离经验值效果和其他效果
            if (isset($effects['experience'])) {
                $experienceToGrant = (int)$effects['experience'];
                unset($effects['experience']);
            } else if (isset($effects['knowledge_points'])) {
                $knowledgePointsToGrant = (int)$effects['knowledge_points'];
                // unset($effects['knowledge_points']);
            }

            // 检查是否是学习技能的书
            if (isset($effects['learn_skill_id'])) {
                $skillIdToLearn = (int)$effects['learn_skill_id'];
                unset($effects['learn_skill_id']);

                // 调用学习技能的函数
                $this->learnSkill($fd, $playerId, $skillIdToLearn, $conn);
                
                // 如果书中除了学习技能外没有其他效果，则直接继续，否则继续处理其他效果
                if (empty($effects)) {
                    // 消耗物品并提交事务
                    if ($item['quantity'] > 1) {
                        $invUpdateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
                        $invUpdateStmt->execute([$inventoryId]);
                    } else {
                        $invDeleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                        $invDeleteStmt->execute([$inventoryId]);
                    }
                    $conn->commit();
                    $this->handleGetInventory($fd, ['player_id' => $playerId]);
                    return; // 学习完技能后直接返回，不再执行后续代码
                }
            }

            // 3. 处理除经验值外的其他简单属性效果
            if (!empty($effects)) {
            $playerAttrStmt = $conn->prepare("SELECT * FROM player_attributes WHERE account_id = ? FOR UPDATE");
            $playerAttrStmt->execute([$playerId]);
            $playerAttrs = $playerAttrStmt->fetch(PDO::FETCH_ASSOC);
            $playerAttrStmt->closeCursor();
            if (!$playerAttrs) { throw new Exception('找不到玩家属性。'); }

                $updatePayload = []; // This will hold the final values for the SET clause.

            // 应用效果
            foreach ($effects as $attr => $value) {
                if (array_key_exists($attr, $playerAttrs)) {
                        $otherEffectsApplied = true;
                        // 先在内存中更新玩家属性，以便后续计算（例如上限）
                    $playerAttrs[$attr] += $value;
                        
                        $finalValue = $playerAttrs[$attr];
                        // 处理血量/蓝量上限
                        if ($attr === 'hp' && $finalValue > $playerAttrs['max_hp']) {
                            $finalValue = $playerAttrs['max_hp'];
                    }
                        if ($attr === 'mp' && $finalValue > $playerAttrs['max_mp']) {
                            $finalValue = $playerAttrs['max_mp'];
                    }
                        $updatePayload[$attr] = $finalValue;
                }
            }

                // 将更新后的属性写回数据库
                if ($otherEffectsApplied && !empty($updatePayload)) {
                    $setClauses = [];
                    foreach ($updatePayload as $key => $val) {
                        $setClauses[] = "`$key` = :$key";
                    }
                
                    $updateQuery = "UPDATE player_attributes SET " . implode(', ', $setClauses) . " WHERE account_id = :account_id";
                $updateStmt = $conn->prepare($updateQuery);
                    
                    // Add the player id for the where clause
                    $updatePayload['account_id'] = $playerId;
                    $updateStmt->execute($updatePayload);
                }
            }

            // 4. 如果有经验值，调用 grantExperience 处理
            if ($experienceToGrant !== null && $experienceToGrant > 0) {
                $this->grantExperience($playerId, $experienceToGrant, $conn);
            }

            // 5. 更新物品库存
            if ($item['quantity'] > 1) {
                $invUpdateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
                $invUpdateStmt->execute([$inventoryId]);
            } else {
                $invDeleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                $invDeleteStmt->execute([$inventoryId]);
            }
            
            $conn->commit();
            
            // 6. 发送通知
            $infoMessage = "你使用了 {$item['name']}";
            if ($experienceToGrant > 0) {
                // 经验提示由 grantExperience 内部处理，这里可以补充
                $infoMessage .= "，获得了 {$experienceToGrant} 点经验值。";
            } else if ($knowledgePointsToGrant > 0) {
                $infoMessage .= "，获得了 {$knowledgePointsToGrant} 点知识点。";
            // } else if ($otherEffectsApplied) {
            //     $infoMessage .= "，感觉状态有所恢复。";
            } else {
                $infoMessage .= "。";
            }
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $infoMessage, 'context' => 'item_use_success']);

            // 如果物品只回血/蓝，没有经验，则需要手动发送属性更新
            if ($otherEffectsApplied && $experienceToGrant === null) {
                $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]);
            }

            // 更新物品收集任务进度（使用负增量，因为物品数量减少）
            if (!$isSkillBook && isset($item['item_template_id'])) {
                // 使用-1作为增量，表示物品数量减少
                $this->checkCollectQuestProgress($fd, $playerId, $item['item_template_id'], -1);
            }

            // 总是重新发送完整的背包数据
            $this->handleGetInventory($fd, ['player_id' => $playerId]);
    
        } catch (Exception $e) {
            $conn->rollBack();
            // 使用INFO_MESSAGE而不是ERROR，并将context参数
            // 这样客户端可以根据context来决定如何显示消息，例如，技能学习失败的提示可以更醒目
            $context = 'item_use_fail';
            if (strpos($e->getMessage(), '技能') !== false || strpos($e->getMessage(), '等级') !== false) {
                $context = 'skill_learn_fail';
            }
            return $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                'message' => $e->getMessage(),
                'context' => $context
            ]);
        }
    }

    private function getGemEffects($conn, $gemTemplateId) {
        $stmt = $conn->prepare("SELECT effects FROM item_templates WHERE id = ?");
        $stmt->execute([$gemTemplateId]);
        $effectsJson = $stmt->fetchColumn();
        $stmt->closeCursor();
        return $effectsJson ? json_decode($effectsJson, true) : [];
    }

    private function handleSocketGem($fd, $data) {
        $playerId = $data['player_id'];
        $weaponInventoryId = $data['weapon_inventory_id'] ?? 0;
        $gemInventoryId = $data['gem_inventory_id'] ?? 0;
        $socketIndex = $data['socket_index'] ?? -1;

        if (!$weaponInventoryId || !$gemInventoryId || $socketIndex < 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的镶嵌请求。']);
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();

        try {
            // 1. Lock and get weapon
            $stmt = $conn->prepare("SELECT inv.*, it.name as base_name, ed.sockets FROM player_inventory inv JOIN item_templates it ON inv.item_template_id = it.id JOIN equipment_details ed ON it.id = ed.item_template_id WHERE inv.id = ? AND inv.player_id = ? FOR UPDATE");
            $stmt->execute([$weaponInventoryId, $playerId]);
            $weapon = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            if (!$weapon) { throw new Exception("找不到要镶嵌的物品。"); }
            if ($weapon['sockets'] <= 0) { throw new Exception("该物品没有插槽。"); }

            // 2. Lock and get gem
            $stmt = $conn->prepare("SELECT inv.*, it.category, it.name FROM player_inventory inv JOIN item_templates it ON inv.item_template_id = it.id WHERE inv.id = ? AND inv.player_id = ? FOR UPDATE");
            $stmt->execute([$gemInventoryId, $playerId]);
            $gem = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            if (!$gem) { throw new Exception("找不到要镶嵌的宝石。"); }
            if ($gem['category'] !== 'Gem') { throw new Exception("所选物品不是宝石。"); }

            // 3. Initialize or migrate instance_data structure
            $instanceData = $weapon['instance_data'] ? json_decode($weapon['instance_data'], true) : [];
            if (!isset($instanceData['sockets']) || !is_array($instanceData['sockets']) || (isset($instanceData['sockets'][0]) && !is_array($instanceData['sockets'][0]))) {
                $newSockets = [];
                for ($i = 0; $i < $weapon['sockets']; $i++) {
                    $newSockets[] = ['gem_id' => null, 'gem_name' => null, 'attempts' => 0];
                }
                $instanceData['sockets'] = $newSockets;
                $instanceData['gem_stats'] = [];
            }
            $sockets = &$instanceData['sockets'];

            if (!array_key_exists($socketIndex, $sockets)) { throw new Exception("无效的插槽位置。"); }
            if ($sockets[$socketIndex]['gem_id'] !== null) { throw new Exception("该插槽已被占用。"); }

            // 4. Calculate success rate and attempt socketing
            $attempts = $sockets[$socketIndex]['attempts'];
            $successRate = Formulas::calculateSocketingSuccessRate($attempts);
            $roll = mt_rand(0, 100) / 100;
            
            $sockets[$socketIndex]['attempts']++;

            if ($roll > $successRate) {
                // --- Failure ---
                $stmt = $conn->prepare("UPDATE player_inventory SET instance_data = ? WHERE id = ?");
                $stmt->execute([json_encode($instanceData), $weaponInventoryId]);
                $conn->commit();
                
                $failMessage = sprintf("镶嵌失败！本次成功率: %.0f%%。", $successRate * 100);
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $failMessage]);
            }

            // --- Success ---
            $sockets[$socketIndex]['gem_id'] = $gem['item_template_id'];
            $sockets[$socketIndex]['gem_name'] = $gem['name'];
            
            // 5. Recalculate all gem stats and new display name
            $allGemEffects = [];
            $suffixParts = [];
            $effectMap = [
                'fire_damage' => '火',
                'fire_resistance' => '火抗',
                'ice_damage' => '冰',
                'ice_resistance' => '冰抗',
                'wind_damage' => '风',
                'wind_resistance' => '风抗',
                'electric_damage' => '电',
                'electric_resistance' => '电抗',
                'attack' => '攻击',
                'defense' => '防御'
            ];

            foreach ($sockets as $socket) {
                if ($socket['gem_id']) {
                    $gemEffects = $this->getGemEffects($conn, $socket['gem_id']);
                    foreach($gemEffects as $key => $value) {
                        $allGemEffects[$key] = ($allGemEffects[$key] ?? 0) + $value;
                    }
                }
            }

            foreach($allGemEffects as $key => $value) {
                if (isset($effectMap[$key]) && $value != 0) {
                     $suffixParts[] = $effectMap[$key] . ($value > 0 ? '+' : '') . $value;
                }
            }
            
            $instanceData['gem_stats'] = $allGemEffects;
            $instanceData['display_name'] = $weapon['base_name'] . (!empty($suffixParts) ? '§' . implode(' ', $suffixParts) . '§' : '');

            $stmt = $conn->prepare("UPDATE player_inventory SET instance_data = ? WHERE id = ?");
            $stmt->execute([json_encode($instanceData), $weaponInventoryId]);

            // 6. Consume gem
            if ($gem['quantity'] > 1) {
                $stmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
                $stmt->execute([$gemInventoryId]);
            } else {
                $stmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                $stmt->execute([$gemInventoryId]);
            }
            
            // 7. If weapon is equipped, update player stats
            if ($weapon['is_equipped']) {
                $playerAttrStmt = $conn->prepare("SELECT * FROM player_attributes WHERE account_id = ? FOR UPDATE");
                $playerAttrStmt->execute([$playerId]);
                $playerAttrs = $playerAttrStmt->fetch(PDO::FETCH_ASSOC);
                $playerAttrStmt->closeCursor();
                
                // Add JUST the new gem's effects. The existing ones are already on the player.
                $newGemEffects = $this->getGemEffects($conn, $gem['item_template_id']);
                 foreach ($newGemEffects as $key => $value) {
                    if (isset($playerAttrs[$key])) {
                        $playerAttrs[$key] += $value;
                    }
                }
                
                $setClauses = [];
                foreach ($playerAttrs as $key => $value) {
                    if ($key !== 'id' && $key !== 'account_id') $setClauses[] = "`$key` = :$key";
                }
                $updateQuery = "UPDATE player_attributes SET " . implode(', ', $setClauses) . " WHERE account_id = :account_id";
                $updateStmt = $conn->prepare($updateQuery);
                unset($playerAttrs['id']);
                $updateStmt->execute($playerAttrs);
            }

            $conn->commit();

            // 8. Notify client
            $this->sendMessage($fd, 0x84, ['message' => '镶嵌成功！']); // S2C_INFO_MESSAGE
            $this->handleGetInventory($fd, ['player_id' => $playerId]); // Resend inventory
            if ($weapon['is_equipped']) {
                $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]); // S2C_PLAYER_ATTRIBUTE_UPDATE
            }

        } catch (Exception $e) {
            $conn->rollBack();
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]);
        }
    }

    private function applyStats($playerAttrs, $stats, $multiplier = 1) {
        if ($stats) {
            foreach ($stats as $key => $value) {
                if (isset($playerAttrs[$key])) {
                    $playerAttrs[$key] += ($value * $multiplier);
                }
            }
        }
        return $playerAttrs;
    }

    private function applyGemStats($playerAttrs, $instanceDataJson, $multiplier = 1) {
        if (empty($instanceDataJson)) return $playerAttrs;

        $instanceData = json_decode($instanceDataJson, true);
        if (isset($instanceData['gem_stats']) && is_array($instanceData['gem_stats'])) {
            foreach($instanceData['gem_stats'] as $key => $value) {
                if (isset($playerAttrs[$key])) {
                    $playerAttrs[$key] += ($value * $multiplier);
                }
            }
        }
        return $playerAttrs;
    }

    private function handleGetMonsterDetails($fd, $data) {
        $playerId = $data['player_id'];
        $monsterInstanceId = $data['monster_id'];
        
        $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
            return $redis->get("player_scene:{$playerId}");
        });
        if (!$sceneId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法确定你所在的场景。']);
        }

        // Get monster instance from Redis
        $monsterInstanceJson = RedisManager::getInstance()->with(function($redis) use ($sceneId, $monsterInstanceId) {
            return $redis->hget("scene_monsters:{$sceneId}", $monsterInstanceId);
        });
        if (!$monsterInstanceJson) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '敌人已消失！']);
        }

        $monsterInstance = json_decode($monsterInstanceJson, true);
        $monsterTemplateId = $monsterInstance['monster_template_id'];

        $monsterDetails = $this->getFullMonsterDetails($monsterTemplateId);
        if (!$monsterDetails) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '失去敌人信息。']);
        }

        // Merge instance HP with template data
        $monsterDetails['hp'] = $monsterInstance['hp'];

        $this->sendMessage($fd, MessageProtocol::S2C_MONSTER_DETAILS, ['monster' => $monsterDetails]);
    }

    private function handleGetPlayerDetails($fd, $data) {
        $targetPlayerId = $data['player_id_to_view'] ?? 0;
    
        if (!$targetPlayerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的玩家ID']);
        }
    
        $playerData = $this->getPlayerWithAttributes($targetPlayerId);
        if (!$playerData) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '找不到该玩家的数据']);
        }
    
        $db = Database::getInstance();
        $stmt = $db->query(
            "SELECT 
                inv.id as inventory_id, inv.quantity, inv.is_equipped, inv.instance_data, 
                it.name, it.category, it.stackable, it.effects, it.description,
                ed.slot, ed.job_restriction, ed.stats, ed.sockets, ed.grants_job_id,
                j_grant.name as granted_job_name,
                j_restrict.name as job_restriction_name
             FROM player_inventory inv
             JOIN item_templates it ON inv.item_template_id = it.id
             LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
             LEFT JOIN jobs j_grant ON ed.grants_job_id = j_grant.id
             LEFT JOIN jobs j_restrict ON ed.job_restriction = j_restrict.id
             WHERE inv.player_id = ? AND inv.is_equipped = 1
             ORDER BY ed.slot",
            [$targetPlayerId]
        );
        $equippedItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        $responsePayload = [
            'player' => $playerData,
            'equipment' => $equippedItems
        ];
    
        $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_DETAILS, $responsePayload);
    }

    private function handlePickupItem($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $sceneItemId = $data['item_id'] ?? 0;

        if (!$playerId) {
            error_log("[PickupItem] No player ID found for fd {$fd}");
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }

        $player = $this->getPlayerWithAttributes($playerId);
        if (!$player) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '找不到您的角色数据。']);
        }

        if ($player['attributes']['hp'] <= 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你已经倒下了，无法拾取物品。']);
        }

        error_log("[DEBUG] Player {$playerId} attempting to pick up scene item {$sceneItemId}");
        $result = $this->lootManager->pickupItem($playerId, $sceneItemId);
        error_log("[DEBUG] pickupItem result: " . json_encode($result));

        if ($result['success']) {
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $result['message']]);
            // Refresh player's inventory
            $this->handleGetInventory($fd, ['player_id' => $playerId]);
            // Refresh the scene for everyone
            $this->broadcastPlayerUpdate($result['scene_id']);
            
            // 检查是否有与拾取物品相关的收集任务
            if (isset($result['item_template_id']) && $result['item_template_id'] > 0) {
                error_log("[DEBUG] Checking collect quest progress for player {$playerId}, item {$result['item_template_id']}, quantity " . (isset($result['quantity']) ? $result['quantity'] : 1));
                
                // 获取物品的详细信息
                $itemInfoQuery = $this->db->query(
                    "SELECT id, item_id, name FROM item_templates WHERE id = ?", 
                    [$result['item_template_id']]
                );
                $itemInfo = $itemInfoQuery->fetch(PDO::FETCH_ASSOC);
                error_log("[DEBUG] Item info: " . json_encode($itemInfo));
                
                // 查询是否有针对该物品的收集任务目标
                $objectivesQuery = $this->db->query(
                    "SELECT qo.id, qo.quest_id, qo.target_id, q.title 
                     FROM quest_objectives qo
                     JOIN quests q ON qo.quest_id = q.id
                     WHERE qo.type = 'collect' AND qo.target_id = ?", 
                    [$result['item_template_id']]
                );
                $objectives = $objectivesQuery->fetchAll(PDO::FETCH_ASSOC);
                
                if (count($objectives) > 0) {
                    error_log("[DEBUG] Found " . count($objectives) . " quests objectives for item ID {$result['item_template_id']}:");
                    foreach ($objectives as $obj) {
                        error_log("[DEBUG] Quest: {$obj['title']}, ObjectiveID: {$obj['id']}, TargetID: {$obj['target_id']}");
                    }
                } else {
                    error_log("[DEBUG] No quest objectives found for item ID {$result['item_template_id']}");
                }
                
                $this->checkCollectQuestProgress($fd, $playerId, $result['item_template_id'], isset($result['quantity']) ? $result['quantity'] : 1);
            } else {
                error_log("[ERROR] Missing item_template_id in pickupItem result: " . json_encode($result));
            }
        } else {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $result['message']]);
        }
    }
    
    /**
     * 公共的任务进度检测方法
     * @param int $fd WebSocket连接标识符
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 物品数量变化
     */
    public function checkQuestProgressForItem($fd, $playerId, $itemTemplateId, $quantity = 1) {
        $this->checkCollectQuestProgress($fd, $playerId, $itemTemplateId, $quantity);
    }

    /**
     * 检查仓库操作相关的任务进度
     * @param int $fd WebSocket连接标识符
     * @param int $playerId 玩家ID
     * @param string $action 操作类型 ('deposit', 'withdraw', 'expand')
     * @param array $data 操作数据
     */
    public function checkWarehouseQuestProgress($fd, $playerId, $action, $data = []) {
        try {
            // 检查物品相关的收集任务
            if (isset($data['item_template_id']) && isset($data['quantity'])) {
                $quantity = $action === 'deposit' ? -$data['quantity'] : $data['quantity'];
                $this->checkCollectQuestProgress($fd, $playerId, $data['item_template_id'], $quantity);
            }

            // 如果有QuestManager，检查其他类型的任务
            if (class_exists('QuestManager')) {
                $db = Database::getInstance();
                $questManager = new QuestManager(function() use ($db) {
                    return $db->getConnection();
                });

                // 检查仓库操作相关的特殊任务
                switch ($action) {
                    case 'deposit':
                        // 检查"存入物品"类型的任务
                        $updatedQuests = $questManager->checkQuestObjectiveUpdates($playerId, 'warehouse_deposit', $data['item_template_id'], $data['quantity']);
                        break;

                    case 'withdraw':
                        // 检查"取出物品"类型的任务
                        $updatedQuests = $questManager->checkQuestObjectiveUpdates($playerId, 'warehouse_withdraw', $data['item_template_id'], $data['quantity']);
                        break;

                    case 'expand':
                        // 检查"仓库扩容"类型的任务
                        $updatedQuests = $questManager->checkQuestObjectiveUpdates($playerId, 'warehouse_expand', 1, 1);
                        break;
                }

                // 如果有任务更新，发送通知
                if (!empty($updatedQuests)) {
                    $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                        'updated_quests' => $updatedQuests
                    ]);
                }
            }
        } catch (Exception $e) {
            error_log("[WarehouseQuest] Error checking quest progress: " . $e->getMessage());
        }
    }

    /**
     * 检查和更新收集任务进度
     * @param int $fd WebSocket连接标识符
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 物品数量
     */
    private function checkCollectQuestProgress($fd, $playerId, $itemTemplateId, $quantity = 1) {
        if (!isset($this->questManager)) {
            $this->questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });
            error_log("[DEBUG] Created new QuestManager instance for player {$playerId}");
        }
        
        try {
            error_log("[DEBUG] Calling checkQuestObjectiveUpdates for player {$playerId}, item {$itemTemplateId}, quantity {$quantity}");
            
            // 直接检查数据库中是否有包含这个物品作为目标的收集任务
            $conn = $this->db->getConnection();
            $stmt = $conn->prepare("
                SELECT qo.quest_id, qo.id as objective_id, q.title, qo.quantity as target_quantity
                FROM quest_objectives qo
                JOIN quests q ON qo.quest_id = q.id
                JOIN player_quests pq ON pq.quest_id = qo.quest_id
                WHERE pq.player_id = ? 
                AND pq.status = 'active'
                AND qo.type = 'collect'
                AND qo.target_id = ?
            ");
            $stmt->execute([$playerId, $itemTemplateId]);
            $collectObjectives = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            if (count($collectObjectives) > 0) {
                error_log("[DEBUG] Found " . count($collectObjectives) . " matching collect objectives in database:");
                foreach ($collectObjectives as $objective) {
                    error_log("[DEBUG] Quest: {$objective['title']}, Objective ID: {$objective['objective_id']}, Target: {$objective['target_quantity']}");
                    
                    // 获取当前进度
                    $progressStmt = $conn->prepare("
                        SELECT objectives_progress
                        FROM player_quests
                        WHERE player_id = ? AND quest_id = ? AND status = 'active'
                    ");
                    $progressStmt->execute([$playerId, $objective['quest_id']]);
                    $progressData = $progressStmt->fetch(PDO::FETCH_ASSOC);
                    
                    if ($progressData) {
                        $objectivesProgress = json_decode($progressData['objectives_progress'], true) ?: [];
                        $currentProgress = isset($objectivesProgress[$objective['objective_id']]) ? 
                            (int)$objectivesProgress[$objective['objective_id']] : 0;
                        
                        error_log("[DEBUG] Current progress in DB: {$currentProgress}/{$objective['target_quantity']}");
                    }
                }
            } else {
                error_log("[DEBUG] No matching collect objectives found in database for player {$playerId}, item {$itemTemplateId}");
            }
            
            // 调用QuestManager的checkQuestObjectiveUpdates方法检查任务进度
            $updatedQuests = $this->questManager->checkQuestObjectiveUpdates(
                $playerId, 
                'collect', 
                $itemTemplateId, 
                $quantity
            );
            
            error_log("[DEBUG] checkQuestObjectiveUpdates returned " . count($updatedQuests) . " updated quests for player {$playerId}");
            
            if (!empty($updatedQuests)) {
                error_log("[DEBUG] Quest updates found: " . json_encode($updatedQuests, JSON_UNESCAPED_UNICODE));
                
                // 发送任务更新通知
                foreach ($updatedQuests as $update) {
                    $message = "【任务进度】{$update['title']}: {$update['description']} ({$update['new_progress']}/{$update['target']})";
                    $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $message]);
                    
                    // 如果任务可以完成，提示玩家
                    if ($update['can_complete']) {
                        $completeMessage = "【任务完成】任务\"{$update['title']}\"已经可以交付了，请前往{$update['receiver_npc_name']}处交付任务。";
                        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $completeMessage]);
                    }
                }
                
                // 向客户端发送任务更新消息
                error_log("[DEBUG] Sending S2C_QUEST_UPDATE to player {$playerId}");
                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                    'action' => 'progress_updated',
                    'quests' => $updatedQuests
                ]);
                
                // 更新玩家任务列表
                error_log("[DEBUG] Updating quest list for player {$playerId}");
                $this->handleGetQuestList($fd, ['player_id' => $playerId]);
            } else {
                error_log("[DEBUG] No quest updates for player {$playerId} and item {$itemTemplateId}");
            }
        } catch (Exception $e) {
            error_log("[ERROR] 检查收集任务进度失败: " . $e->getMessage() . "\n" . $e->getTraceAsString());
        }
    }

    private function grantExperience($playerId, $amount, $conn = null) {
        $shouldManageTransaction = ($conn === null);
        if ($shouldManageTransaction) {
        $db = Database::getInstance();
        $conn = $db->getConnection();
            $conn->beginTransaction();
        }
    
        try {
            // Lock player attributes for update
            $stmt = $conn->prepare("SELECT * FROM player_attributes WHERE account_id = ? FOR UPDATE");
            $stmt->execute([$playerId]);
            $playerAttrs = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
    
            if (!$playerAttrs) {
                if ($shouldManageTransaction) $conn->rollBack();
                return;
            }
            
            $logMessages = [];
            $playerAttrs['experience'] += $amount;
    
            // Check for level up
            $leveledUp = false;
            while ($playerAttrs['experience'] >= $playerAttrs['experience_to_next_level']) {
                $leveledUp = true;
                $currentHp = $playerAttrs['hp'];
                $currentMp = $playerAttrs['mp'];

                $playerAttrs['experience'] -= $playerAttrs['experience_to_next_level'];
                $playerAttrs['level']++;
                $playerAttrs['potential_points'] = ($playerAttrs['potential_points'] ?? 0) + 5;
                $playerAttrs['experience_to_next_level'] = Formulas::calculateExperienceForNextLevel($playerAttrs['level']);
    
                $logMessages[] = "【等级提升】恭喜！你升到了 {$playerAttrs['level']} 级！你获得了5点潜力值！";

                // Get stat gains from formulas
                $statGains = Formulas::getStatGainsOnLevelUp();
                $gainMessages = [];
                foreach ($statGains as $stat => $gain) {
                    if (isset($playerAttrs[$stat])) {
                        $playerAttrs[$stat] += $gain;
                        // Map stat key to Chinese name for the log message
                        $statNameMap = ['strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智慧'];
                        $statName = $statNameMap[$stat] ?? $stat;
                        $gainMessages[] = "{$statName}+{$gain}";
                    }
                }
                if (!empty($gainMessages)) {
                    $logMessages[] = "【属性提升】你的基础属性提升了: " . implode('，', $gainMessages) . "。";
                }

                // 升级后，重新计算属性，然后回血回蓝
                // 注意：这里直接修改了 $playerAttrs 数组，但并未持久化，recalculate会做这件事
                $this->recalculatePlayerAttributes($playerId, $conn, $playerAttrs); 
                $this->setPlayerToFullHealthAndMana($playerId, $conn, $playerAttrs);
            }
            
            // Update database with final attributes after loop
            // 只更新数据库中实际存在的字段，排除total_*等计算字段
            $dbFields = ['experience', 'level', 'potential_points', 'experience_to_next_level',
                        'strength', 'agility', 'constitution', 'intelligence',
                        'hp', 'max_hp', 'mp', 'max_mp', 'attack', 'defense', 'attack_speed', 'gold',
                        'native_job_id', 'current_job_id', 'job', 'knowledge_points', 'diamonds',
                        'combat_hp_potion_id', 'combat_mp_potion_id', 'fire_resistance', 'ice_resistance',
                        'wind_resistance', 'electric_resistance', 'current_scene_id', 'karma',
                        'fire_damage', 'ice_damage', 'wind_damage', 'electric_damage', 'rage', 'dodge_bonus'];

            $setClauses = [];
            $updateParams = [];
            foreach ($playerAttrs as $key => $value) {
                if (in_array($key, $dbFields)) {
                    $setClauses[] = "`$key` = :$key";
                    $updateParams[$key] = $value;
                }
            }

            if (!empty($setClauses)) {
                $updateQuery = "UPDATE player_attributes SET " . implode(', ', $setClauses) . " WHERE account_id = :account_id";
                $updateStmt = $conn->prepare($updateQuery);
                $updateParams['account_id'] = $playerId;
                $updateStmt->execute($updateParams);
            }
    
            if ($leveledUp) {
                // 如果升级了，重新获取最新的完整属性
                $updatedPlayer = $this->getPlayerWithAttributes($playerId);
                // $logMessages[] = "【能力提升】你的能力获得了全面提升！";
            }
    
            if ($shouldManageTransaction) {
            $conn->commit();
            }
            
            // Notify client
            $fd = $this->playerConnections[$playerId] ?? null;
            if ($fd) {
                if (!empty($logMessages)) {
                foreach ($logMessages as $msg) {
                    $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $msg]);
                    }
                }
                // Send full attribute update
                $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]);
            }
    
        } catch (Exception $e) {
            if ($shouldManageTransaction) {
            $conn->rollBack();
            }
            error_log("Failed to grant experience to player {$playerId}: " . $e->getMessage());
        }
    }

    // =================================================================
    // NEW AND REFACTORED METHODS FOR ATTRIBUTE CALCULATION
    // =================================================================

    /**
     * 根据玩家分配的潜力点更新其基础属性。
     */
    private function handleAllocatePotentialPoints($fd, $payload) {
        $playerId = $payload['player_id'];
        $pointsToAllocate = $payload['points'] ?? [];

        if (empty($pointsToAllocate)) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未指定要分配的点数。']);
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();

        try {
            $stmt = $conn->prepare("SELECT potential_points, strength, agility, constitution, intelligence FROM player_attributes WHERE account_id = ? FOR UPDATE");
            $stmt->execute([$playerId]);
            $playerStats = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$playerStats) {
                throw new Exception("找不到玩家属性。");
            }

            $totalPointsToSpend = 0;
            foreach ($pointsToAllocate as $stat => $value) {
                if (!in_array($stat, ['strength', 'agility', 'constitution', 'intelligence'])) {
                    throw new Exception("无效的属性: {$stat}");
                }
                if (!is_numeric($value) || $value < 0) {
                    throw new Exception("属性点数必须为正数。");
                }
                $totalPointsToSpend += $value;
            }

            if ($totalPointsToSpend > $playerStats['potential_points']) {
                throw new Exception("潜力点不足。");
            }

            // 更新基础属性和潜力点
            $playerStats['potential_points'] -= $totalPointsToSpend;
            $playerStats['strength'] += ($pointsToAllocate['strength'] ?? 0);
            $playerStats['agility'] += ($pointsToAllocate['agility'] ?? 0);
            $playerStats['constitution'] += ($pointsToAllocate['constitution'] ?? 0);
            $playerStats['intelligence'] += ($pointsToAllocate['intelligence'] ?? 0);
            
            $updateQuery = "UPDATE player_attributes SET potential_points = :potential_points, strength = :strength, agility = :agility, constitution = :constitution, intelligence = :intelligence WHERE account_id = :account_id";
            $updateStmt = $conn->prepare($updateQuery);
            $updateStmt->execute([
                'potential_points' => $playerStats['potential_points'],
                'strength' => $playerStats['strength'],
                'agility' => $playerStats['agility'],
                'constitution' => $playerStats['constitution'],
                'intelligence' => $playerStats['intelligence'],
                'account_id' => $playerId
            ]);

            // 重新计算所有派生属性
            $this->recalculatePlayerAttributes($playerId, $conn);
            
            $conn->commit();

            // 通知客户端
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => '属性点分配成功！']);
            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, [
                'player' => $this->getPlayerWithAttributes($playerId),
                'allocated_points' => $pointsToAllocate  // 发送本次分配的点数
            ]);

        } catch (Exception $e) {
            $conn->rollBack();
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]);
        }
    }

    /**
     * (重构) 装备物品，并触发职业和属性重算。
     */
    private function handleEquipItem($fd, $data) {
        $playerId = $data['player_id'];
        $inventoryId = $data['inventory_id'] ?? 0;
    
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();
    
        try {
            // 0. Check player HP first
            $playerAttrStmt = $conn->prepare("SELECT hp FROM player_attributes WHERE account_id = ?");
            $playerAttrStmt->execute([$playerId]);
            $playerHp = $playerAttrStmt->fetchColumn();
            $playerAttrStmt->closeCursor();

            if ($playerHp !== false && $playerHp <= 0) {
                throw new Exception('你已经倒下了，无法更换装备。');
            }

            // 1. 获取要装备的物品
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.is_equipped, it.name, ed.slot, ed.job_restriction
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 JOIN equipment_details ed ON it.id = ed.item_template_id
                 WHERE inv.id = ? AND inv.player_id = ? FOR UPDATE"
            );
            $stmt->execute([$inventoryId, $playerId]);
            $itemToEquip = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
    
            if (!$itemToEquip) { throw new Exception('该物品不存在或无法装备。'); }
            if ($itemToEquip['is_equipped']) { throw new Exception('该物品已经装备。'); }
    
            // 2. 获取玩家属性并进行职业检查
            $playerAttrStmt = $conn->prepare("SELECT current_job_id FROM player_attributes WHERE account_id = ?");
            $playerAttrStmt->execute([$playerId]);
            $playerJobId = $playerAttrStmt->fetchColumn();
            $playerAttrStmt->closeCursor();
    
            if ($itemToEquip['job_restriction'] && $playerJobId != (int)$itemToEquip['job_restriction']) {
                $jobStmt = $conn->prepare("SELECT name FROM jobs WHERE id = ?");
                $jobStmt->execute([$itemToEquip['job_restriction']]);
                $requiredJobName = $jobStmt->fetchColumn() ?: "未知职业";
                $jobStmt->closeCursor();
                throw new Exception("你的职业不是[{$requiredJobName}]，无法装备 {$itemToEquip['name']}。");
            }

            // 3. 查找并卸下冲突的已装备物品
            $slotToEquip = $itemToEquip['slot'];
            $conflictSlots = [];
            if ($slotToEquip === 'TwoHanded') {
                $conflictSlots = ['LeftHand', 'RightHand', 'TwoHanded'];
            } elseif ($slotToEquip === 'LeftHand' || $slotToEquip === 'RightHand') {
                $conflictSlots = ['TwoHanded', $slotToEquip];
            } else {
                $conflictSlots = [$slotToEquip];
            }
            
            $placeholders = implode(',', array_fill(0, count($conflictSlots), '?'));
            $stmt = $conn->prepare(
                "UPDATE player_inventory SET is_equipped = 0
                 WHERE player_id = ? AND is_equipped = 1 AND item_template_id IN (
                     SELECT item_template_id FROM equipment_details WHERE slot IN ($placeholders)
                 )"
            );
            $stmt->execute(array_merge([$playerId], $conflictSlots));
    
            // 4. 装备新物品
            $conn->prepare("UPDATE player_inventory SET is_equipped = 1 WHERE id = ?")->execute([$inventoryId]);
            
            // 5. 重新计算职业和所有属性
            $this->updatePlayerJob($playerId, $conn);
            $this->recalculatePlayerAttributes($playerId, $conn);
    
            $conn->commit();
            
            // 6. 通知客户端
            $this->sendMessage($fd, 0x84, ['message' => "已装备 {$itemToEquip['name']}。", 'context' => 'item_action_success']);
            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]);
            $this->handleGetInventory($fd, ['player_id' => $playerId]);
    
        } catch (Exception $e) {
            $conn->rollBack();
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage(), 'context' => 'item_equip_fail']);
        }
    }

    /**
     * (重构) 卸下物品，并触发职业和属性重算。
     */
    private function handleUnequipItem($fd, $data) {
        $playerId = $data['player_id'];
        $inventoryId = $data['inventory_id'] ?? 0;
    
        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();
    
        try {
            // 0. Check player HP first
            $playerAttrStmt = $conn->prepare("SELECT hp FROM player_attributes WHERE account_id = ?");
            $playerAttrStmt->execute([$playerId]);
            $playerHp = $playerAttrStmt->fetchColumn();
            $playerAttrStmt->closeCursor();

            if ($playerHp !== false && $playerHp <= 0) {
                throw new Exception('你已经倒下了，无法更换装备。');
            }

            // 1. 获取要卸下的物品
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.is_equipped, it.name
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.id = ? AND inv.player_id = ? FOR UPDATE"
            );
            $stmt->execute([$inventoryId, $playerId]);
            $itemToUnequip = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$itemToUnequip) { throw new Exception('该物品不存在。'); }
            if (!$itemToUnequip['is_equipped']) { throw new Exception('该物品未装备。'); }
    
            // 2. 更新物品装备状态
            $conn->prepare("UPDATE player_inventory SET is_equipped = 0 WHERE id = ?")->execute([$inventoryId]);

            // 3. 重新计算职业和属性
            $this->updatePlayerJob($playerId, $conn);
            $this->recalculatePlayerAttributes($playerId, $conn);
            
            $conn->commit();
            
            // 4. 通知客户端
            $this->sendMessage($fd, 0x84, ['message' => "已卸下 {$itemToUnequip['name']}。", 'context' => 'item_action_success']);
            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]);
            $this->handleGetInventory($fd, ['player_id' => $playerId]);
    
        } catch (Exception $e) {
            $conn->rollBack();
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]);
        }
    }

    private function updatePlayerJob($playerId, $conn) {
        // 获取玩家原生职业ID
        $stmt = $conn->prepare("SELECT native_job_id FROM player_attributes WHERE account_id = ?");
        $stmt->execute([$playerId]);
        $nativeJobId = $stmt->fetchColumn();
        $stmt->closeCursor();

        // 获取所有已装备物品授予的职业
        $stmt = $conn->prepare(
            "SELECT ed.grants_job_id
             FROM player_inventory inv
             JOIN equipment_details ed ON inv.item_template_id = ed.item_template_id
             WHERE inv.player_id = ? AND inv.is_equipped = 1 AND ed.grants_job_id IS NOT NULL"
        );
        $stmt->execute([$playerId]);
        
        $finalJobId = $nativeJobId;
        while ($grantedJobId = $stmt->fetchColumn()) {
            $finalJobId = $grantedJobId; // 简单覆盖，未来可以加入优先级
        }
        $stmt->closeCursor();
        
        // 更新当前职业
        $updateStmt = $conn->prepare("UPDATE player_attributes SET current_job_id = ? WHERE account_id = ?");
        $updateStmt->execute([$finalJobId, $playerId]);
    }

    /**
     * 核心方法：根据玩家的基础属性和所有装备，重新计算并存储所有派生属性。
     * 可在现有事务中运行。
     * @param int $playerId
     * @param PDO|null $conn
     * @param array|null &$playerAttrs 如果提供，则直接在此数组上修改，避免重复查询
     */
    private function recalculatePlayerAttributes($playerId, $conn = null, &$playerAttrs = null) {
        $shouldManageTransaction = ($conn === null);
        if ($shouldManageTransaction) {
            $db = Database::getInstance();
            $conn = $db->getConnection();
            $conn->beginTransaction();
        }
        
        try {
            // 1. 获取玩家基础属性和等级 (如果未提供)
            if ($playerAttrs === null) {
                $stmt = $conn->prepare("SELECT * FROM player_attributes WHERE account_id = ? FOR UPDATE");
                $stmt->execute([$playerId]);
                $playerAttrs = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();
            }

            if (!$playerAttrs) throw new Exception("Recalc: Player not found.");

            // 2. 获取所有装备及其提供的属性（包括宝石）
            $stmt = $conn->prepare(
                "SELECT inv.instance_data, ed.stats 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 JOIN equipment_details ed ON it.id = ed.item_template_id
                 WHERE inv.player_id = ? AND inv.is_equipped = 1"
            );
            $stmt->execute([$playerId]);
            $equippedItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            // 3. 计算来自装备和宝石的总属性加成
            $equipmentBonuses = [];
            $bonusStatKeys = ['strength', 'agility', 'constitution', 'intelligence', 'fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance', 'fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'];
            foreach($bonusStatKeys as $key) { $equipmentBonuses[$key] = 0; } // Initialize

            foreach ($equippedItems as $item) {
                // 来自装备本身的属性
                if ($item['stats']) {
                    $itemStats = json_decode($item['stats'], true);
                    foreach($itemStats as $key => $value) {
                        $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                    }
                }
                
                // 来自镶嵌宝石的属性
                if ($item['instance_data']) {
                    $instanceData = json_decode($item['instance_data'], true);
                    if (isset($instanceData['gem_stats']) && is_array($instanceData['gem_stats'])) {
                        foreach($instanceData['gem_stats'] as $key => $value) {
                             $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                        }
                    }
                    
                    // 从凝练属性中获取加成
                    if (isset($instanceData['refined']) && $instanceData['refined'] &&
                        isset($instanceData['refine_bonuses']) && is_array($instanceData['refine_bonuses'])) {

                        // 获取凝练属性的类型信息
                        $refineBonusTypes = $instanceData['refine_bonus_types'] ?? [];

                        foreach($instanceData['refine_bonuses'] as $key => $value) {
                            $bonusType = $refineBonusTypes[$key] ?? 'flat'; // 默认为固定值

                            if ($bonusType === 'percentage') {
                                // 百分比类型：暂时保存，稍后基于基础属性计算
                                if (!isset($equipmentBonuses['_percentage_bonuses'])) {
                                    $equipmentBonuses['_percentage_bonuses'] = [];
                                }
                                $equipmentBonuses['_percentage_bonuses'][$key] = ($equipmentBonuses['_percentage_bonuses'][$key] ?? 0) + $value;
                            } else {
                                // 固定值类型：直接相加
                                $equipmentBonuses[$key] = ($equipmentBonuses[$key] ?? 0) + $value;
                            }
                        }
                    }
                }
            }
            
            // 4. 处理百分比加成
            if (isset($equipmentBonuses['_percentage_bonuses'])) {
                $percentageBonuses = $equipmentBonuses['_percentage_bonuses'];

                // 基于基础属性计算百分比加成
                foreach ($percentageBonuses as $attr => $percentage) {
                    $baseValue = 0;

                    // 获取基础值用于百分比计算
                    if (isset($playerAttrs[$attr])) {
                        $baseValue = $playerAttrs[$attr];
                    } elseif (in_array($attr, ['attack', 'defense', 'max_hp', 'max_mp', 'attack_speed'])) {
                        // 对于衍生属性，使用基础四维属性计算基础值
                        switch ($attr) {
                            case 'attack':
                                $baseValue = Formulas::calculateAttack($playerAttrs['strength'], $playerAttrs['level']);
                                break;
                            case 'defense':
                                $baseValue = Formulas::calculateDefense($playerAttrs['constitution'], $playerAttrs['agility'], $playerAttrs['level']);
                                break;
                            case 'max_hp':
                                $baseValue = Formulas::calculateMaxHp($playerAttrs['constitution'], $playerAttrs['level']);
                                break;
                            case 'max_mp':
                                $baseValue = Formulas::calculateMaxMp($playerAttrs['intelligence'], $playerAttrs['level']);
                                break;
                            case 'attack_speed':
                                $baseValue = Formulas::calculateAttackSpeed($playerAttrs['agility']);
                                break;
                        }
                    }

                    // 计算百分比加成并添加到装备加成中
                    if ($baseValue > 0) {
                        $percentageBonus = round($baseValue * ($percentage / 100));
                        $equipmentBonuses[$attr] = ($equipmentBonuses[$attr] ?? 0) + $percentageBonus;

                        error_log("Percentage bonus calculation for {$attr}: base {$baseValue} * {$percentage}% = +{$percentageBonus}");
                    }
                }

                // 清除临时的百分比数据
                unset($equipmentBonuses['_percentage_bonuses']);
            }

            // 5. 使用基础属性和装备加成计算最终的派生属性
            $totalStrength = $playerAttrs['strength'] + ($equipmentBonuses['strength'] ?? 0);
            $totalAgility = $playerAttrs['agility'] + ($equipmentBonuses['agility'] ?? 0);
            $totalConstitution = $playerAttrs['constitution'] + ($equipmentBonuses['constitution'] ?? 0);
            $totalIntelligence = $playerAttrs['intelligence'] + ($equipmentBonuses['intelligence'] ?? 0);

            $derivedAttrs = [];
            // 先从基础四维属性计算衍生值
            $derivedAttrs['attack'] = Formulas::calculateAttack($totalStrength, $playerAttrs['level']);
            $derivedAttrs['defense'] = Formulas::calculateDefense($totalConstitution, $totalAgility, $playerAttrs['level']);
            $derivedAttrs['max_hp'] = Formulas::calculateMaxHp($totalConstitution, $playerAttrs['level']);
            $derivedAttrs['max_mp'] = Formulas::calculateMaxMp($totalIntelligence, $playerAttrs['level']);
            $derivedAttrs['attack_speed'] = Formulas::calculateAttackSpeed($totalAgility);

            // 然后，加上来自装备的直接数值加成
            $derivedAttrs['attack'] += ($equipmentBonuses['attack'] ?? 0);
            $derivedAttrs['defense'] += ($equipmentBonuses['defense'] ?? 0);
            $derivedAttrs['max_hp'] += ($equipmentBonuses['max_hp'] ?? 0);
            $derivedAttrs['max_mp'] += ($equipmentBonuses['max_mp'] ?? 0);
            $derivedAttrs['attack_speed'] += ($equipmentBonuses['attack_speed'] ?? 0);

            // 最后处理小数和取整，攻速直接取整数
            $derivedAttrs['attack_speed'] = floor($derivedAttrs['attack_speed']);
            
            // 直接使用装备加成作为最终的元素属性
            $derivedAttrs['fire_resistance'] = $equipmentBonuses['fire_resistance'];
            $derivedAttrs['ice_resistance'] = $equipmentBonuses['ice_resistance'];
            $derivedAttrs['wind_resistance'] = $equipmentBonuses['wind_resistance'];
            $derivedAttrs['electric_resistance'] = $equipmentBonuses['electric_resistance'];
            $derivedAttrs['fire_damage'] = $equipmentBonuses['fire_damage'];
            $derivedAttrs['ice_damage'] = $equipmentBonuses['ice_damage'];
            $derivedAttrs['wind_damage'] = $equipmentBonuses['wind_damage'];
            $derivedAttrs['electric_damage'] = $equipmentBonuses['electric_damage'];

            // 5. 获取当前HP/MP，以防最大值变动
            // 如果 $playerAttrs 已经被传入，我们可以直接用里面的hp, mp
            $currentHp = $playerAttrs['hp'];
            $currentMp = $playerAttrs['mp'];

            $derivedAttrs['hp'] = min($currentHp, $derivedAttrs['max_hp']);
            if ($derivedAttrs['hp'] <= 0) $derivedAttrs['hp'] = 1; // 换装备不能致死
            $derivedAttrs['mp'] = min($currentMp, $derivedAttrs['max_mp']);

            // 将计算出的衍生属性合并回 playerAttrs 引用
            foreach($derivedAttrs as $key => $value) {
                $playerAttrs[$key] = $value;
            }

            // 更新内存中的总属性（用于返回给客户端）
            $playerAttrs['total_strength'] = $totalStrength;
            $playerAttrs['total_agility'] = $totalAgility;
            $playerAttrs['total_constitution'] = $totalConstitution;
            $playerAttrs['total_intelligence'] = $totalIntelligence;

            // 6. 构建并执行更新查询 (只更新派生属性，不更新基础属性到数据库)
            $params = $derivedAttrs;
            $params['account_id'] = $playerId;

            $setClauses = [];
            foreach ($derivedAttrs as $key => $value) {
                 $setClauses[] = "`$key` = :$key";
            }
            
            if (!empty($setClauses)) {
                $updateQuery = "UPDATE player_attributes SET " . implode(', ', $setClauses) . " WHERE account_id = :account_id";
                $updateStmt = $conn->prepare($updateQuery);
                $updateStmt->execute($params);
            }

            if ($shouldManageTransaction) {
                $conn->commit();
            }

        } catch (Exception $e) {
            if ($shouldManageTransaction) {
                $conn->rollBack();
            }
            error_log("Attribute recalculation for player {$playerId} failed: " . $e->getMessage() . " in " . $e->getFile() . ":" . $e->getLine());
            throw $e; // 重新抛出，以便外部事务可以捕获它
        }
    }

    private function handleFleeBattle($fd, $data) {
        $playerId = $data['player_id'];
        if (!isset($this->playerBattleMap[$playerId])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你不在战斗中']);
        }
        
        $monsterId = $this->playerBattleMap[$playerId];
        if (!isset($this->monsterBattles[$monsterId])) {
            unset($this->playerBattleMap[$playerId]);
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '战斗已不存在。']);
        }
        
        $this->monsterBattles[$monsterId]['player_intentions'][$playerId] = ['action' => 'flee'];
        
        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => '你将在下一回合尝试逃跑。']);
    }

    /**
     * 标记场景中哪些物品对当前玩家是受保护的
     * @param array &$sceneData 场景数据
     * @param string $playerId 玩家ID
     */
    private function markProtectedItems(&$sceneData, $playerId) {
        if (empty($sceneData['items'])) {
            return;
        }
        
        RedisManager::getInstance()->with(function($redis) use (&$sceneData, $playerId) {
            foreach ($sceneData['items'] as &$item) {
                $protectionKey = "scene_item_protection:{$item['id']}";
                
                if ($redis->exists($protectionKey)) {
                    // 检查是否有保护
                    $hasProtection = true;
                    
                    // 检查是否对当前玩家保护
                    $isProtectedForPlayer = $redis->sismember($protectionKey, $playerId);
                    
                    // 添加保护标记
                    $item['is_protected'] = $hasProtection;
                    $item['is_protected_for_you'] = $isProtectedForPlayer;
                    
                    // 如果是对当前玩家保护的，添加特殊标记
                    if ($isProtectedForPlayer) {
                        // 使用已有的display_name（如果存在），否则使用模板名称
                        $baseName = $item['display_name'] ?? $item['name'] ?? '';
                        $item['display_name'] = $baseName . ' [保护]';
                    }
                } else {
                    $item['is_protected'] = false;
                    $item['is_protected_for_you'] = false;
                }
            }
        });
    }

    private function handlePlayerFled($playerId, $monsterId, $log) {
        $battleState = &$this->monsterBattles[$monsterId];
        $fd = $this->playerConnections[$playerId] ?? null;

        // 1. 先将玩家从战斗状态中移除
        unset($battleState['players'][$playerId]);
        unset($this->playerBattleMap[$playerId]);
        
        // 2. 获取最新的场景数据
        $sceneData = $this->sceneManager->getSceneData($battleState['scene_id']);
        $sceneItems = $this->sceneManager->getSceneItems($battleState['scene_id']);
        $sceneData['items'] = $sceneItems;
        $this->markProtectedItems($sceneData, $playerId);
        
        // 3. 通知本人逃跑成功，战斗对他而言已结束
        if ($fd) {
            $this->sendMessage(
                $fd, 
                MessageProtocol::S2C_BATTLE_ENDED, 
                [
                    'reason' => 'flee', 
                    'log' => $log,
                    'final_state' => [
                        'monster' => $battleState['monster'],
                        'scene' => $sceneData,
                        'players' => [],
                        'items' => $sceneData['items']
                    ]
                ]
            );
        }

        // 4. 如果战场上还有人，则通知他们
        if (count($battleState['players']) > 0) {
            $this->broadcastToBattle(
                $monsterId,
                MessageProtocol::S2C_BATTLE_UPDATE,
                [
                    'log' => $log,
                    'monster' => $battleState['monster'],
                    'all_players' => array_values($battleState['players']),
                    'atb_status' => $this->battleSystem->getAtbStatus($battleState),
                    'skill_cooldowns' => $battleState['skill_cooldowns'] ?? [] // 添加技能冷却信息
                ]
            );
            $this->startBattleLoop($monsterId); // 重新启动循环以防万一
        } else {
            // 5. 如果这是最后一个玩家，结束战斗
            $this->endBattle($battleState, 'all_fled');
        }
        
        // 6. 广播场景更新
        $this->broadcastPlayerUpdate($battleState['scene_id']);
    }

    /**
     * 更新客户端的战斗行动意图显示
     * @param string $monsterId 怪物ID
     * @param string $playerId 玩家ID
     * @param string $newIntention 新的行动意图
     */
    private function updateClientBattleIntention($monsterId, $playerId, $newIntention) {
        $fd = $this->playerConnections[$playerId] ?? null;
        if (!$fd) return;
        
        // 添加关键日志来跟踪意图变更
        // echo "[INTENTION_CHANGE] Battle: {$monsterId}, Player: {$playerId}, New Intention: '{$newIntention}'\n";

        // 发送一个特殊的消息，通知客户端更新行动意图显示
        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
            'context' => 'battle_intention_update',
            'intention' => $newIntention
        ]);
    }
    
    /**
     * 获取行动意图的显示名称
     * @param string $action 行动意图
     * @return string 显示名称
     */
    private function getActionDisplayName($action) {
        switch ($action) {
            case 'attack': return '攻击';
            case 'use_hp_potion': return '使用回血药水';
            case 'use_mp_potion': return '使用回蓝药水';
            case 'flee': return '逃跑';
            default: return $action;
        }
    }

    private function handleGetCombatPotionConfig($fd, $payload) {
        $playerId = $payload['player_id'];
        $conn = Database::getInstance()->getConnection();
        
        // 1. 获取当前配置的药水信息
        $stmt = $conn->prepare(
            "SELECT pa.combat_hp_potion_id, pa.combat_mp_potion_id, 
                    hp_item.name as hp_potion_name, 
                    mp_item.name as mp_potion_name
             FROM player_attributes pa
             LEFT JOIN item_templates hp_item ON pa.combat_hp_potion_id = hp_item.id
             LEFT JOIN item_templates mp_item ON pa.combat_mp_potion_id = mp_item.id
             WHERE pa.account_id = ?"
        );
        $stmt->execute([$playerId]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        // 2. 获取玩家背包中符合条件的药水列表
        // 查询所有药水，并检查它们是否有特定效果
        $potionsStmt = $conn->prepare(
            "SELECT 
                inv.id as inventory_id, 
                it.id as item_template_id, 
                it.name, 
                it.effects
             FROM player_inventory inv
             JOIN item_templates it ON inv.item_template_id = it.id
             WHERE inv.player_id = ? 
               AND inv.is_equipped = 0
               AND it.category = 'Potion'
               AND it.effects IS NOT NULL"
        );
        $potionsStmt->execute([$playerId]);
        $allPotions = $potionsStmt->fetchAll(PDO::FETCH_ASSOC);
        $potionsStmt->closeCursor();
        
        // 3. 筛选合适的回血和回蓝药水
        $hpPotions = [];
        $mpPotions = [];
        
        foreach ($allPotions as $potion) {
            $effects = json_decode($potion['effects'], true);
            if (!$effects) continue;
            
            $hasHpEffect = isset($effects['hp']) && $effects['hp'] > 0;
            $hasMpEffect = isset($effects['mp']) && $effects['mp'] > 0;
            
            // 只有纯回血药水才能设为回血
            if ($hasHpEffect && !$hasMpEffect) {
                $hpPotions[] = [
                    'inventory_id' => $potion['inventory_id'],
                    'item_template_id' => $potion['item_template_id'],
                    'name' => $potion['name'],
                    'effect_value' => $effects['hp']
                ];
            }
            
            // 只有纯回蓝药水才能设为回蓝
            if ($hasMpEffect && !$hasHpEffect) {
                $mpPotions[] = [
                    'inventory_id' => $potion['inventory_id'],
                    'item_template_id' => $potion['item_template_id'],
                    'name' => $potion['name'],
                    'effect_value' => $effects['mp']
                ];
            }
        }

        // 4. 发送配置数据和可用药水列表
        if ($config) {
            $this->sendMessage($fd, MessageProtocol::S2C_COMBAT_POTION_CONFIG_DATA, [
                'hp_potion_id' => $config['combat_hp_potion_id'],
                'mp_potion_id' => $config['combat_mp_potion_id'],
                'hp_potion_name' => $config['hp_potion_name'],
                'mp_potion_name' => $config['mp_potion_name'],
                'available_hp_potions' => $hpPotions,
                'available_mp_potions' => $mpPotions
            ]);
        }
    }

    private function handleSetCombatPotionConfig($fd, $payload) {
        $playerId = $payload['player_id'];
        $inventoryId = $payload['item_template_id'] ?? null; // 实际上这是inventory_id
        $type = $payload['type'] ?? null; // 'hp' or 'mp'

        if (!$inventoryId || !in_array($type, ['hp', 'mp'])) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的配置请求。', 'context' => 'config_error']);
        }

        $conn = Database::getInstance()->getConnection();
        
        // 先获取物品模板ID和详细信息
        $stmt = $conn->prepare("SELECT inv.item_template_id, it.effects, it.category, it.name
                               FROM player_inventory inv 
                               JOIN item_templates it ON inv.item_template_id = it.id
                               WHERE inv.id = ? AND inv.player_id = ?");
        $stmt->execute([$inventoryId, $playerId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!$item || $item['category'] !== 'Potion') {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '所选物品不是药水。', 'context' => 'config_error']);
        }
        
        $itemTemplateId = $item['item_template_id'];
        $effectsJson = $item['effects'];

        if (!$effectsJson) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '所选物品不是药水。', 'context' => 'config_error']);
        }
        
        $effects = json_decode($effectsJson, true);
        
        // 检查药水是否有对应的效果
        $hasHpEffect = isset($effects['hp']) && $effects['hp'] > 0;
        $hasMpEffect = isset($effects['mp']) && $effects['mp'] > 0;
        
        // 严格验证药水类型
        if ($type === 'hp') {
            if (!$hasHpEffect) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该药水没有回血效果。', 'context' => 'config_error']);
            }
            if ($hasMpEffect) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '不能将同时具有回血和回蓝效果的药水设为回血药水。', 'context' => 'config_error']);
            }
        } else if ($type === 'mp') {
            if (!$hasMpEffect) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该药水没有回蓝效果。', 'context' => 'config_error']);
            }
            if ($hasHpEffect) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '不能将同时具有回血和回蓝效果的药水设为回蓝药水。', 'context' => 'config_error']);
            }
        }

        // 更新数据库
        $column = "combat_{$type}_potion_id";
        $stmt = $conn->prepare("UPDATE player_attributes SET {$column} = ? WHERE account_id = ?");
        $stmt->execute([$itemTemplateId, $playerId]);

        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
            'message' => "已将{$item['name']}设为战斗" . ($type === 'hp' ? '回血' : '回蓝') . "药水！", 
            'context' => 'potion_config_success'
        ]);
        
        // 重新获取并发送配置信息
        $this->handleGetCombatPotionConfig($fd, $payload);
    }

    /**
     * 玩家学习一个新技能.
     * @param int $fd 玩家的连接文件描述符
     * @param int $playerId 玩家ID
     * @param int $skillId 要学习的技能ID
     * @param PDO $conn 数据库连接 (应在一个事务中被调用)
     * @throws Exception 如果学习失败，将抛出异常，由外部的事务进行回滚
     */
    private function learnSkill($fd, $playerId, $skillId, $conn) {
        // 1. 获取技能模板信息
        $skillStmt = $conn->prepare("SELECT * FROM skill_templates WHERE id = ?");
        $skillStmt->execute([$skillId]);
        $skillTemplate = $skillStmt->fetch(PDO::FETCH_ASSOC);
        $skillStmt->closeCursor();

        if (!$skillTemplate) {
            throw new Exception("该技能不存在。");
        }

        // 2. 检查玩家是否已经学习该技能，并获取当前等级
        $playerSkillStmt = $conn->prepare("SELECT id, skill_level FROM player_skills WHERE player_id = ? AND skill_template_id = ? FOR UPDATE");
        $playerSkillStmt->execute([$playerId, $skillId]);
        $playerSkill = $playerSkillStmt->fetch(PDO::FETCH_ASSOC);
        $playerSkillStmt->closeCursor();

        $currentSkillLevel = $playerSkill ? (int)$playerSkill['skill_level'] : 0;
        
        // 3. 获取玩家的属性（等级、知识点和职业）
        $playerAttrStmt = $conn->prepare("SELECT level, knowledge_points, current_job_id FROM player_attributes WHERE account_id = ? FOR UPDATE");
        $playerAttrStmt->execute([$playerId]);
        $playerAttrs = $playerAttrStmt->fetch(PDO::FETCH_ASSOC);
        $playerAttrStmt->closeCursor();
        
        if (!$playerAttrs) {
            throw new Exception("找不到玩家数据。");
        }

        // 4. 检查学习/升级的前置条件
        // 4a. 职业检查 (最高优先级)
        if (isset($skillTemplate['required_job_id']) && $skillTemplate['required_job_id'] !== null) {
            if ($playerAttrs['current_job_id'] != $skillTemplate['required_job_id']) {
                $jobStmt = $conn->prepare("SELECT name FROM jobs WHERE id = ?");
                $jobStmt->execute([$skillTemplate['required_job_id']]);
                $requiredJobName = $jobStmt->fetchColumn() ?: '未知职业';
                $jobStmt->closeCursor();
                throw new Exception("你的职业不符，需要是【{$requiredJobName}】才能学习此技能。");
            }
        }

        // 4b. 等级检查 (仅在第一次学习时检查)
        if ($currentSkillLevel === 0 && $playerAttrs['level'] < $skillTemplate['required_level']) {
            throw new Exception("你的等级不足，需要达到 {$skillTemplate['required_level']} 级才能学习【{$skillTemplate['name']}】。");
        }
        
        // 5. 计算并检查知识点消耗
        $baseCost = $skillTemplate['knowledge_points_cost'];
        $rarity = $skillTemplate['rarity'];
        $requiredKnowledgePoints = Formulas::calculateKnowledgePointCost($baseCost, $currentSkillLevel, $rarity);

        if ($playerAttrs['knowledge_points'] < $requiredKnowledgePoints) {
            throw new Exception("知识点不足。升级【{$skillTemplate['name']}】到下一级需要 {$requiredKnowledgePoints} 点知识，你只有 {$playerAttrs['knowledge_points']} 点。");
        }

        // 6. 执行学习或升级操作
        $conn->prepare("UPDATE player_attributes SET knowledge_points = knowledge_points - ? WHERE account_id = ?")
             ->execute([$requiredKnowledgePoints, $playerId]);
        
        $successMessage = '';
        if ($currentSkillLevel === 0) {
            // 学习新技能
            $insertStmt = $conn->prepare("INSERT INTO player_skills (player_id, skill_template_id, skill_level, last_used_at) VALUES (?, ?, 1, NOW())");
            $insertStmt->execute([$playerId, $skillId]);
            $successMessage = "恭喜你！成功学会了新技能：【{$skillTemplate['name']}】！(消耗{$requiredKnowledgePoints}知识点)";
        } else {
            // 升级已有技能
            $newLevel = $currentSkillLevel + 1;
            $updateStmt = $conn->prepare("UPDATE player_skills SET skill_level = ? WHERE id = ?");
            $updateStmt->execute([$newLevel, $playerSkill['id']]);
            $successMessage = "技能升级！【{$skillTemplate['name']}】提升到了 {$newLevel} 级！(消耗{$requiredKnowledgePoints}知识点)";
        }

        // 7. 通知玩家操作成功
        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
            'message' => $successMessage,
            'context' => 'skill_learned'
        ]);

        // (未来可以发送更新后的技能列表和玩家属性数据)
        $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($playerId)]);
    }

    private function handleGetPlayerSkills($fd, $payload) {
        $playerId = $payload['player_id'];
        $db = Database::getInstance();
    
        $stmt = $db->query(
            "SELECT
                ps.skill_level,
                ps.last_used_at,
                st.id as skill_template_id,
                st.name,
                st.description,
                st.icon,
                st.skill_type,
                st.rarity,
                st.target_type,
                st.mp_cost,
                st.knowledge_points_cost,
                st.required_level,
                st.required_job_id,
                st.delay_turns,
                st.duration_turns,
                st.cooldown_turns,
                st.effects
             FROM player_skills ps
             JOIN skill_templates st ON ps.skill_template_id = st.id
             WHERE ps.player_id = ?
             ORDER BY st.required_level, st.id",
            [$playerId]
        );
    
        $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        $conn = $db->getConnection();
        $jobStmt = $conn->prepare("SELECT name FROM jobs WHERE id = ?");
        
        // 获取玩家属性用于计算魔力消耗
        $attrStmt = $conn->prepare("SELECT intelligence FROM player_attributes WHERE account_id = ?");
        $attrStmt->execute([$playerId]);
        $playerAttrs = $attrStmt->fetch(PDO::FETCH_ASSOC);
        $intelligence = $playerAttrs['intelligence'] ?? null;
        $attrStmt->closeCursor();
    
        foreach($skills as &$skill) {
            if (!empty($skill['required_job_id'])) {
                $jobStmt->execute([$skill['required_job_id']]);
                $skill['required_job_name'] = $jobStmt->fetchColumn() ?: '通用';
            } else {
                 $skill['required_job_name'] = '通用';
            }

            // 计算下一级升级所需的知识点
            $skill['next_level_cost'] = Formulas::calculateKnowledgePointCost(
                $skill['knowledge_points_cost'],
                $skill['skill_level'],
                $skill['rarity']
            );
            
            // 计算当前等级的技能魔力消耗（考虑技能等级和稀有度）
            $baseManaCost = $skill['mp_cost']; 
            $skill['current_mp_cost'] = Formulas::calculateSkillManaCost(
                $baseManaCost,
                $skill['skill_level'],
                $skill['rarity'],
                $intelligence
            );
        }
        unset($skill);
        $jobStmt->closeCursor();
    
        $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_SKILLS_LIST, [
            'skills' => $skills
        ]);
    }

    private function handleGetBattleSkills($fd, $payload) {
        $playerId = $payload['player_id'] ?? $this->playerConnections[$fd]['player_id'];
        $battleType = $payload['battle_type'] ?? 'pve';
        $db = Database::getInstance();
    
        // 1. Get player's current job
        $jobStmt = $db->query("SELECT current_job_id FROM player_attributes WHERE account_id = ?", [$playerId]);
        $currentJobId = $jobStmt->fetchColumn();
        $jobStmt->closeCursor();

        if ($currentJobId === false) {
            $skills = []; // Player not found, no skills
        } else {
            // 获取玩家属性用于计算魔力消耗
            $attrStmt = $db->query("SELECT intelligence FROM player_attributes WHERE account_id = ?", [$playerId]);
            $playerAttrs = $attrStmt->fetch(PDO::FETCH_ASSOC);
            $intelligence = $playerAttrs['intelligence'] ?? null;
            $attrStmt->closeCursor();
            
            // 2. Get skills matching the job
            $stmt = $db->query(
                "SELECT
                    ps.skill_level,
                    st.id as skill_template_id,
                    st.name,
                    st.mp_cost,
                    st.rarity
                 FROM player_skills ps
                 JOIN skill_templates st ON ps.skill_template_id = st.id
                 WHERE ps.player_id = ?
                   AND st.skill_type IN ('COMBAT_INSTANT', 'COMBAT_DELAYED', 'BUFF', 'DEBUFF')
                   AND (st.required_job_id IS NULL OR st.required_job_id = ?)
                 ORDER BY st.id",
                [$playerId, $currentJobId]
            );
        
            $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            // 计算每个技能的实际魔力消耗
            foreach ($skills as &$skill) {
                // 计算当前等级的技能魔力消耗
                $skill['current_mp_cost'] = Formulas::calculateSkillManaCost(
                    $skill['mp_cost'],
                    $skill['skill_level'],
                    $skill['rarity'],
                    $intelligence
                );
            }
            unset($skill);
        }
    
        $this->sendMessage($fd, MessageProtocol::S2C_BATTLE_SKILLS_LIST, [
            'skills' => $skills,
            'battle_type' => $battleType
        ]);
    }

    private function _getBattleSkills($playerId) {
        $db = Database::getInstance();

        // 1. Get player's current job
        $jobStmt = $db->query("SELECT current_job_id FROM player_attributes WHERE account_id = ?", [$playerId]);
        $currentJobId = $jobStmt->fetchColumn();
        $jobStmt->closeCursor();

        if ($currentJobId === false) {
            return []; // Player not found, no skills
        }
        
        // 获取玩家属性用于计算魔力消耗
        $attrStmt = $db->query("SELECT intelligence FROM player_attributes WHERE account_id = ?", [$playerId]);
        $playerAttrs = $attrStmt->fetch(PDO::FETCH_ASSOC);
        $intelligence = $playerAttrs['intelligence'] ?? null;
        $attrStmt->closeCursor();

        // 2. Get skills matching the job
        $stmt = $db->query(
            "SELECT
                ps.skill_level,
                st.id as skill_template_id,
                st.name,
                st.mp_cost,
                st.rarity
             FROM player_skills ps
             JOIN skill_templates st ON ps.skill_template_id = st.id
             WHERE ps.player_id = ?
               AND st.skill_type IN ('COMBAT_INSTANT', 'COMBAT_DELAYED', 'BUFF', 'DEBUFF')
               AND (st.required_job_id IS NULL OR st.required_job_id = ?)
             ORDER BY st.id",
            [$playerId, $currentJobId]
        );
        $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        // 计算每个技能的实际魔力消耗
        foreach ($skills as &$skill) {
            // 计算当前等级的技能魔力消耗
            $skill['current_mp_cost'] = Formulas::calculateSkillManaCost(
                $skill['mp_cost'],
                $skill['skill_level'],
                $skill['rarity'],
                $intelligence
            );
        }
        unset($skill);
        
        return $skills;
    }

    /**
     * 解析技能战斗描述，替换变量为实际值
     * @param string $description 战斗描述模板
     * @param array $attacker 攻击者数据
     * @param array $defender 防御者数据
     * @param array $skill 技能数据
     * @param array $damageResult 伤害计算结果
     * @return string 解析后的描述
     */
    private function parseBattleDescription($description, $attacker, $defender, $skill, $damageResult) {
        $weaponName = $attacker['weapon_name'] ?? '赤手空拳';
        
        // 如果武器名称包含 `+`，只取 `+` 前面的部分作为基础名称
        $plusPos = strpos($weaponName, ' +');
        if ($plusPos !== false) {
            $baseWeaponName = substr($weaponName, 0, $plusPos);
        } else {
            $baseWeaponName = $weaponName;
        }

        $patterns = [
            '/\[攻击者\]/' => $attacker['username'] ?? '未知攻击者',
            '/\[敌人\]/'   => $defender['username'] ?? '未知敌人',
            '/\[武器\]/'   => $weaponName,
            '/\[武器名\]/' => $baseWeaponName,
            '/\[技能\]/'   => $skill['name'] ?? '未知技能',
            '/\[伤害\]/'   => $damageResult['damage'] ?? 0,
            '/\[物理伤害\]/' => $damageResult['physical_damage'] ?? 0,
            '/\[元素伤害\]/' => $damageResult['elemental_damage'] ?? 0,
        ];
        
        $description = preg_replace(array_keys($patterns), array_values($patterns), $description);

        // 处理暴击标签
        if (strpos($description, '[暴击]') !== false) {
            if (!empty($damageResult['is_critical'])) {
                $description = str_replace('[暴击]', '【暴击！】', $description);
            } else {
                $description = str_replace('[暴击]', '', $description);
            }
        }
        
        return trim($description);
    }

    /**
     * 处理获取建筑数据请求
     * @param int $fd WebSocket连接标识符
     * @param array $payload 请求数据
     */
    private function handleGetBuildingData($fd, $payload) {
        $sceneBuildingId = $payload['scene_building_id'] ?? null;
        $playerId = $payload['player_id'] ?? null;
        $isRefresh = $payload['is_refresh'] ?? false;
        
        if (!$sceneBuildingId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的建筑ID。']);
        }

        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的玩家ID。']);
        }
        
        $db = Database::getInstance();
        $stmt = $db->query(
            "SELECT b.type, b.name 
             FROM scene_buildings sb
             JOIN buildings b ON sb.building_id = b.id
             WHERE sb.id = ?",
            [$sceneBuildingId]
        );
        $building = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!$building) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '找不到指定的建筑。']);
        }

        switch ($building['type']) {
            case 'SHOP':
                $this->handleGetShopData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            case 'DIAMOND_SHOP':
                $this->handleGetDiamondShopData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            case 'REVIVE_POINT':
                $this->handleGetRevivePointData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            case 'TELEPORTER':
                $this->handleGetTeleporterData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            case 'CRAFTING':
                // 发送合成建筑数据
                $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, [
                    'type' => 'CRAFTING',
                    'name' => $building['name'],
                    'scene_building_id' => $sceneBuildingId,
                    'is_refresh' => $isRefresh
                ]);
                break;
            case 'GEM_CRAFTING':
                // 发送宝石合成建筑数据
                $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, [
                    'type' => 'GEM_CRAFTING',
                    'name' => $building['name'],
                    'scene_building_id' => $sceneBuildingId,
                    'is_refresh' => $isRefresh
                ]);
                break;
            case 'REFINE':
                // 发送凝练建筑数据
                $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, [
                    'type' => 'REFINE',
                    'name' => $building['name'],
                    'scene_building_id' => $sceneBuildingId,
                    'is_refresh' => $isRefresh
                ]);
                break;
            case 'WAREHOUSE':
                $this->handleGetWarehouseData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            case 'ATTRIBUTE_RESET':
                $this->handleGetAttributeResetData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
                break;
            // Future building types can be handled here
            default:
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未知的建筑类型。']);
        }
    }

    /**
     * 处理获取商店数据
     * @param int $fd WebSocket连接标识符
     * @param int $sceneBuildingId 场景建筑ID
     * @param string $shopName 商店名称
     * @param int $playerId 玩家ID
     * @param bool $isRefresh 是否为刷新操作
     */
    private function handleGetShopData($fd, $sceneBuildingId, $shopName, $playerId, $isRefresh = false) {
        $db = Database::getInstance();
        
        // 1. Get items sold in the shop
        $stmt = $db->query(
                         "SELECT
                si.item_template_id,
                si.stock,
                si.sort_order,
                it.name,
                it.description,
                it.category,
                it.effects,
                it.buy_price as gold_cost,
                ed.stats,
                ed.slot,
                ed.job_restriction,
                ed.sockets,
                ed.grants_job_id,
                j_restriction.name as job_restriction_name,
                j_grants.name as granted_job_name
             FROM shop_items si
             JOIN item_templates it ON si.item_template_id = it.id
             LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
             LEFT JOIN jobs j_restriction ON ed.job_restriction = j_restriction.id
             LEFT JOIN jobs j_grants ON ed.grants_job_id = j_grants.id
             WHERE si.scene_building_id = ? AND (si.stock > 0 OR si.stock IS NULL) AND it.buy_price IS NOT NULL AND it.buy_price > 0
             ORDER BY si.sort_order ASC, it.name ASC",
            [$sceneBuildingId]
        );

        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        // 2. Get player's currencies
        $currencyStmt = $db->query("SELECT gold, diamonds FROM player_attributes WHERE account_id = ?", [$playerId]);
        $currencies = $currencyStmt->fetch(PDO::FETCH_ASSOC);
        $currencyStmt->closeCursor();
        
        $response = [
            'type' => 'SHOP',
            'name' => $shopName,
            'items' => $items,
            'currencies' => $currencies ?: ['gold' => 0, 'diamonds' => 0],
            'scene_building_id' => $sceneBuildingId,
            'is_refresh' => $isRefresh
        ];
        
        $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, $response);
    }

    /**
     * 处理获取钻石商店数据请求
     * @param int $fd WebSocket连接标识符
     * @param int $sceneBuildingId 场景建筑ID
     * @param string $shopName 商店名称
     * @param int $playerId 玩家ID
     * @param bool $isRefresh 是否为刷新操作
     */
    private function handleGetDiamondShopData($fd, $sceneBuildingId, $shopName, $playerId, $isRefresh = false) {
        $db = Database::getInstance();

        // 1. Get items sold in the diamond shop
        $stmt = $db->query(
            "SELECT
                si.item_template_id,
                si.stock,
                si.sort_order,
                it.name,
                it.description,
                it.category,
                it.effects,
                it.diamond_price as diamond_cost,
                ed.stats,
                ed.slot,
                ed.job_restriction,
                ed.sockets,
                ed.grants_job_id,
                j_restriction.name as job_restriction_name,
                j_grants.name as granted_job_name
             FROM shop_items si
             JOIN item_templates it ON si.item_template_id = it.id
             LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
             LEFT JOIN jobs j_restriction ON ed.job_restriction = j_restriction.id
             LEFT JOIN jobs j_grants ON ed.grants_job_id = j_grants.id
             WHERE si.scene_building_id = ? AND (si.stock > 0 OR si.stock IS NULL) AND it.diamond_price IS NOT NULL AND it.diamond_price > 0
             ORDER BY si.sort_order ASC, it.name ASC",
            [$sceneBuildingId]
        );

        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 2. Get player's currencies (focus on diamonds)
        $currencyStmt = $db->query("SELECT gold, diamonds FROM player_attributes WHERE account_id = ?", [$playerId]);
        $currencies = $currencyStmt->fetch(PDO::FETCH_ASSOC);
        $currencyStmt->closeCursor();

        $response = [
            'type' => 'DIAMOND_SHOP',
            'name' => $shopName,
            'items' => $items,
            'currencies' => $currencies ?: ['gold' => 0, 'diamonds' => 0],
            'scene_building_id' => $sceneBuildingId,
            'is_refresh' => $isRefresh
        ];

        $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, $response);
    }

    /**
     * 处理购买物品请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleBuyItem($fd, $data) {
        $playerId = $data['player_id'] ?? null;
        $itemTemplateId = $data['item_template_id'] ?? null;
        $sceneBuildingId = $data['scene_building_id'] ?? null;
        $quantity = $data['quantity'] ?? 1;
        
        // 验证参数
        if (!$playerId || !$itemTemplateId || !$sceneBuildingId) {
                 // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '购买请求缺少必要参数',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '购买请求缺少必要参数',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
        }
        
        // 验证数量
        if (!is_numeric($quantity) || $quantity < 1) {
            $quantity = 1;
        }
        
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        try {
            $conn->beginTransaction();
            
            // 1. 获取商品信息
            $shopItemStmt = $db->query(
                "SELECT si.stock, it.buy_price as gold_cost, it.name, it.category, it.stackable, it.effects, it.description
                 FROM shop_items si
                 JOIN item_templates it ON si.item_template_id = it.id
                 WHERE si.scene_building_id = ? AND si.item_template_id = ? AND (si.stock > 0 OR si.stock IS NULL) AND it.buy_price IS NOT NULL AND it.buy_price > 0",
                [$sceneBuildingId, $itemTemplateId]
            );
            $shopItem = $shopItemStmt->fetch(PDO::FETCH_ASSOC);
            $shopItemStmt->closeCursor();
            
            if (!$shopItem) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '该商店不出售此物品',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '该商店不出售此物品',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 2. 检查库存
            if ($shopItem['stock'] !== null && $shopItem['stock'] < $quantity) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '商品库存不足',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '商品库存不足',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 2.1 检查价格是否设置
            if ($shopItem['gold_cost'] === null) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '该商品价格未设置，无法购买',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '该商品价格未设置，无法购买',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 3. 获取玩家货币信息
            $playerCurrencyStmt = $db->query(
                "SELECT gold, diamonds FROM player_attributes WHERE account_id = ?",
                [$playerId]
            );
            $playerCurrency = $playerCurrencyStmt->fetch(PDO::FETCH_ASSOC);
            $playerCurrencyStmt->closeCursor();
            
            if (!$playerCurrency) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '无法获取玩家货币信息',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '无法获取玩家货币信息',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 4. 计算总价
            $totalCost = $shopItem['gold_cost'] * $quantity;
            
            // 5. 检查玩家是否有足够的金币
            if ($playerCurrency['gold'] < $totalCost) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '金币不足，无法购买',
                    'context' => 'item_buy_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '金币不足，无法购买',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 6. 扣除玩家金币
            $updateGoldStmt = $db->query(
                "UPDATE player_attributes SET gold = gold - ? WHERE account_id = ?",
                [$totalCost, $playerId]
            );
            
            // 7. 添加物品到玩家背包
            $instanceData = null; // 对于普通物品，实例数据为null
            
            // 检查玩家是否已有此物品（如果可堆叠）
            if ($shopItem['stackable']) {
                $existingItemStmt = $db->query(
                    "SELECT id, quantity FROM player_inventory 
                     WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0",
                    [$playerId, $itemTemplateId]
                );
                $existingItem = $existingItemStmt->fetch(PDO::FETCH_ASSOC);
                $existingItemStmt->closeCursor();
                
                if ($existingItem) {
                    // 更新现有物品数量
                    $db->query(
                        "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?",
                        [$quantity, $existingItem['id']]
                    );
                } else {
                    // 添加新物品
                    $db->query(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_equipped) 
                         VALUES (?, ?, ?, ?, 0)",
                        [$playerId, $itemTemplateId, $quantity, $instanceData]
                    );
                }
            } else {
                // 不可堆叠物品，每个都是单独的条目
                for ($i = 0; $i < $quantity; $i++) {
                    $db->query(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_equipped) 
                         VALUES (?, ?, 1, ?, 0)",
                        [$playerId, $itemTemplateId, $instanceData]
                    );
                }
            }
            
            // 8. 更新商品库存（如果需要）
            if ($shopItem['stock'] !== null) {
                $db->query(
                    "UPDATE shop_items SET stock = stock - ? 
                     WHERE scene_building_id = ? AND item_template_id = ?",
                    [$quantity, $sceneBuildingId, $itemTemplateId]
                );
            }
            
            $conn->commit();
            
            // 检查并更新收集物品相关的任务进度
            $this->checkCollectQuestProgress($fd, $playerId, $itemTemplateId, $quantity);
            
            // 9. 获取商店信息用于刷新
            $buildingStmt = $db->query(
                "SELECT b.type, b.name 
                 FROM scene_buildings sb
                 JOIN buildings b ON sb.building_id = b.id
                 WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();
            
            if ($building) {
                // 10. 发送成功消息（记录到系统日志）
                $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => "成功购买 {$quantity} 个 {$shopItem['name']}，花费 {$totalCost} 金币",
                    'context' => 'item_buy_success'
                ]);
                
                // 11. 发送商店数据，包含购买成功提示
                $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => "成功购买 {$quantity} 个 {$shopItem['name']}，花费 {$totalCost} 金币",
                    'type' => 'success',
                    'scene_building_id' => $sceneBuildingId
                ]);
                
                // 12. 刷新商店数据
                $this->handleGetShopData($fd, $sceneBuildingId, $building['name'], $playerId, $isRefresh);
            }
            
        } catch (Exception $e) {
            $conn->rollBack();
            error_log("购买物品错误: " . $e->getMessage());
            
            // 发送错误消息到系统日志
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '购买物品时发生错误',
                'context' => 'item_buy_fail'
            ]);
            
            // 发送错误通知到商店界面
            return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => '购买物品时发生错误',
                'type' => 'error',
                'scene_building_id' => $sceneBuildingId
            ]);
        }
    }

    /**
     * 处理钻石购买物品请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleBuyDiamondItem($fd, $data) {
        $playerId = $data['player_id'] ?? null;
        $itemTemplateId = $data['item_template_id'] ?? null;
        $sceneBuildingId = $data['scene_building_id'] ?? null;
        $quantity = $data['quantity'] ?? 1;

        // 验证参数
        if (!$playerId || !$itemTemplateId || !$sceneBuildingId) {
            // 发送错误消息到系统日志
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '钻石购买请求缺少必要参数',
                'context' => 'diamond_buy_fail'
            ]);

            // 发送错误通知到商店界面
            return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => '钻石购买请求缺少必要参数',
                'type' => 'error',
                'scene_building_id' => $sceneBuildingId
            ]);
        }

        // 验证数量
        if (!is_numeric($quantity) || $quantity < 1) {
            $quantity = 1;
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        try {
            $conn->beginTransaction();

            // 1. 验证建筑类型是钻石商店
            $buildingStmt = $db->query(
                "SELECT bs.type, bs.name 
                    FROM buildings bs
                    JOIN scene_buildings sb ON sb.building_id = bs.id
                    WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if (!$building || $building['type'] !== 'DIAMOND_SHOP') {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '只能在钻石商店购买钻石商品',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }

            // 2. 获取商品信息（钻石商店商品）
            $shopItemStmt = $db->query(
                "SELECT si.stock, it.diamond_price as diamond_cost, it.name, it.category, it.stackable, it.effects, it.description
                 FROM shop_items si
                 JOIN item_templates it ON si.item_template_id = it.id
                 WHERE si.scene_building_id = ? AND si.item_template_id = ? AND (si.stock > 0 OR si.stock IS NULL) AND it.diamond_price IS NOT NULL AND it.diamond_price > 0",
                [$sceneBuildingId, $itemTemplateId]
            );
            $shopItem = $shopItemStmt->fetch(PDO::FETCH_ASSOC);
            $shopItemStmt->closeCursor();

            if (!$shopItem) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '商品不存在或库存不足',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }

            // 3. 检查库存
            if ($shopItem['stock'] !== null && $shopItem['stock'] < $quantity) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '库存不足',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }

            // 4. 获取玩家钻石数量
            $playerCurrencyStmt = $db->query(
                "SELECT diamonds FROM player_attributes WHERE account_id = ?",
                [$playerId]
            );
            $playerCurrency = $playerCurrencyStmt->fetch(PDO::FETCH_ASSOC);
            $playerCurrencyStmt->closeCursor();

            if (!$playerCurrency) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '无法获取玩家钻石信息',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }

            // 5. 计算总价
            $totalCost = $shopItem['diamond_cost'] * $quantity;

            // 6. 检查玩家是否有足够的钻石
            if ($playerCurrency['diamonds'] < $totalCost) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '钻石不足，无法购买',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }

            // 7. 扣除玩家钻石
            $updateDiamondsStmt = $db->query(
                "UPDATE player_attributes SET diamonds = diamonds - ? WHERE account_id = ?",
                [$totalCost, $playerId]
            );

            // 8. 添加物品到玩家背包
            $instanceData = null; // 对于普通物品，实例数据为null

            if ($shopItem['stackable']) {
                // 可堆叠物品：尝试添加到现有堆叠
                $existingItemStmt = $db->query(
                    "SELECT id, quantity FROM player_inventory
                     WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND is_bound = 0
                     ORDER BY id LIMIT 1",
                    [$playerId, $itemTemplateId]
                );
                $existingItem = $existingItemStmt->fetch(PDO::FETCH_ASSOC);
                $existingItemStmt->closeCursor();

                if ($existingItem) {
                    // 更新现有物品数量
                    $updateItemStmt = $db->query(
                        "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ? AND is_bound = 0",
                        [$quantity, $existingItem['id']]
                    );
                } else {
                    // 创建新物品
                    $insertItemStmt = $db->query(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_equipped, is_bound)
                         VALUES (?, ?, ?, ?, 0, 0)",
                        [$playerId, $itemTemplateId, $quantity, $instanceData]
                    );
                }
            } else {
                // 不可堆叠物品：创建多个单独的物品
                for ($i = 0; $i < $quantity; $i++) {
                    $insertItemStmt = $db->query(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_equipped, is_bound)
                         VALUES (?, ?, 1, ?, 0, 0)",
                        [$playerId, $itemTemplateId, $instanceData]
                    );
                }
            }

            // 9. 更新商店库存（如果有限制）
            if ($shopItem['stock'] !== null) {
                $updateStockStmt = $db->query(
                    "UPDATE shop_items SET stock = stock - ? WHERE scene_building_id = ? AND item_template_id = ?",
                    [$quantity, $sceneBuildingId, $itemTemplateId]
                );
            }

            $conn->commit();

            // 10. 发送成功消息
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                'message' => "成功购买 {$quantity} 个 {$shopItem['name']}，花费 {$totalCost} 钻石",
                'context' => 'diamond_buy_success'
            ]);

            // 11. 发送商店通知
            $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => "成功购买 {$quantity} 个 {$shopItem['name']}，花费 {$totalCost} 钻石",
                'type' => 'success',
                'scene_building_id' => $sceneBuildingId
            ]);

            // 12. 刷新钻石商店数据
            $this->handleGetDiamondShopData($fd, $sceneBuildingId, $building['name'], $playerId, true);

            // 13. 刷新玩家背包
            $this->handleGetInventory($fd, ['player_id' => $playerId]);

        } catch (Exception $e) {
            $conn->rollBack();
            error_log("钻石购买错误: " . $e->getMessage());

            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '钻石购买失败：' . $e->getMessage(),
                'context' => 'diamond_buy_fail'
            ]);

            $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => '钻石购买失败，请稍后重试',
                'type' => 'error',
                'scene_building_id' => $sceneBuildingId
            ]);
        }
    }

    /**
     * 处理出售物品请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleSellItem($fd, $data) {
        $playerId = $data['player_id'] ?? null;
        $inventoryId = $data['inventory_id'] ?? null;
        $quantity = $data['quantity'] ?? 1;
        $sceneBuildingId = $data['scene_building_id'] ?? null;
        
        // 验证参数
        if (!$playerId || !$inventoryId || !$sceneBuildingId) {
            // 发送错误消息到系统日志
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '出售请求缺少必要参数',
                'context' => 'item_sell_fail'
            ]);
            
            // 发送错误通知到商店界面
            return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => '出售请求缺少必要参数',
                'type' => 'error',
                'scene_building_id' => $sceneBuildingId
            ]);
        }
        
        // 验证数量
        if (!is_numeric($quantity) || $quantity < 1) {
            $quantity = 1;
        }
        
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        try {
            $conn->beginTransaction();
            
            // 1. 获取物品信息
            $itemStmt = $db->query(
                "SELECT pi.id, pi.item_template_id, pi.quantity, pi.is_equipped, 
                        it.name, it.stackable, it.sell_price, it.category
                 FROM player_inventory pi
                 JOIN item_templates it ON pi.item_template_id = it.id
                 WHERE pi.id = ? AND pi.player_id = ?",
                [$inventoryId, $playerId]
            );
            $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
            $itemStmt->closeCursor();
            
            if (!$item) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '未找到该物品或该物品不属于你',
                    'context' => 'item_sell_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '未找到该物品或该物品不属于你',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 2. 检查物品是否已装备
            if ($item['is_equipped']) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '已装备的物品不能出售',
                    'context' => 'item_sell_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '已装备的物品不能出售',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 3. 检查物品是否有出售价格
            if ($item['sell_price'] === null) {
                $conn->rollBack();

                $errorMsg = '该物品不能出售';
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => $errorMsg,
                    'context' => 'item_sell_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => $errorMsg,
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 4. 检查出售数量是否合法
            if ($quantity > $item['quantity']) {
                $conn->rollBack();
                
                // 发送错误消息到系统日志
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                    'message' => '出售数量超过拥有数量',
                    'context' => 'item_sell_fail'
                ]);
                
                // 发送错误通知到商店界面
                return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => '出售数量超过拥有数量',
                    'type' => 'error',
                    'scene_building_id' => $sceneBuildingId
                ]);
            }
            
            // 5. 计算出售价格
            $sellPrice = $item['sell_price'] * $quantity;
            
            // 6. 更新玩家金币
            $updateGoldStmt = $db->query(
                "UPDATE player_attributes SET gold = gold + ? WHERE account_id = ?",
                [$sellPrice, $playerId]
            );
            
            // 7. 更新物品数量或删除物品
            if ($quantity < $item['quantity']) {
                // 减少物品数量
                $db->query(
                    "UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?",
                    [$quantity, $inventoryId]
                );
            } else {
                // 删除物品
                $db->query(
                    "DELETE FROM player_inventory WHERE id = ?",
                    [$inventoryId]
                );
            }
            
            $conn->commit();
            
            // 8. 获取商店信息用于刷新
            $buildingStmt = $db->query(
                "SELECT b.type, b.name 
                 FROM scene_buildings sb
                 JOIN buildings b ON sb.building_id = b.id
                 WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();
            
            if ($building) {
                // 9. 发送成功消息（记录到系统日志）
                $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => "成功出售 {$quantity} 个 {$item['name']}，获得 {$sellPrice} 金币",
                    'context' => 'item_sell_success'
                ]);
                
                // 10. 发送商店通知
                $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                    'message' => "成功出售 {$quantity} 个 {$item['name']}，获得 {$sellPrice} 金币",
                    'type' => 'success',
                    'scene_building_id' => $sceneBuildingId
                ]);
                
                // 更新收集任务进度（负数增量，表示物品减少）
                $this->checkCollectQuestProgress($fd, $playerId, $item['item_template_id'], -$quantity);
                
                // 11. 刷新商店数据
                $this->handleGetShopData($fd, $sceneBuildingId, $building['name'], $playerId, true);
                
                // 12. 刷新玩家背包
                $this->handleGetInventory($fd, ['player_id' => $playerId]);
            }
            
        } catch (Exception $e) {
            $conn->rollBack();
            error_log("出售物品错误: " . $e->getMessage());
            
            // 发送错误消息到系统日志
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '出售物品时发生错误',
                'context' => 'item_sell_fail'
            ]);
            
            // 发送错误通知到商店界面
            return $this->sendMessage($fd, MessageProtocol::S2C_SHOP_NOTIFICATION, [
                'message' => '出售物品时发生错误',
                'type' => 'error',
                'scene_building_id' => $sceneBuildingId
            ]);
        }
    }

    /**
     * 处理获取复活点数据
     * @param int $fd WebSocket连接标识符
     * @param int $sceneBuildingId 场景建筑ID
     * @param string $revivePointName 复活点名称
     * @param int $playerId 玩家ID
     * @param bool $isRefresh 是否为刷新操作
     */
    private function handleGetRevivePointData($fd, $sceneBuildingId, $revivePointName, $playerId, $isRefresh = false) {
        $db = Database::getInstance();
        
        // 获取玩家当前状态
        $stmt = $db->query("SELECT hp, max_hp, mp, max_mp FROM player_attributes WHERE account_id = ?", [$playerId]);
        $playerStatus = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!$playerStatus) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取玩家状态。']);
        }
        
        // 构建响应数据
        $response = [
            'type' => 'REVIVE_POINT',
            'name' => $revivePointName,
            'scene_building_id' => $sceneBuildingId,
            'is_refresh' => $isRefresh,
            'player_status' => $playerStatus,
            'needs_revival' => ($playerStatus['hp'] <= 0),
            'blessing_message' => $this->getRandomBlessingMessage()
        ];
        
        $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, $response);
    }
    
    /**
     * 获取随机祝福文案
     * @return string 随机祝福文案
     */
    private function getRandomBlessingMessage() {
        $blessings = [
            "愿圣光指引你的道路，勇士。你的灵魂在此得到净化与重生。",
            "生命的火焰从未熄灭，它只是暂时微弱。现在，让它重新燃烧吧！",
            "死亡不过是另一段旅程的开始。而今，你的旅程将继续。",
            "从灰烬中崛起，比以往更加强大。你的意志是不朽的。",
            "时间的河流洗去了你的伤痕，命运的织布为你编织新的篇章。",
            "这神圣之地将抚平你的创伤，治愈你的灵魂，让你重获新生。",
            "黑暗终将过去，黎明即将到来。站起来，迎接新的挑战！",
            "英雄的道路从不平坦，但真正的勇士永不言弃。",
            "生命的奇迹在此显现，让逝去的力量重新回到你的体内。",
            "众神保佑你，勇士。你的故事远未结束。"
        ];
        
        return $blessings[array_rand($blessings)];
    }
    
    /**
     * 处理复活请求（按钮复活）
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleReviveRequest($fd, $data) {
        try {
            $playerId = $data['player_id'];

            $db = Database::getInstance();
            $stmt = $db->query("SELECT hp, max_hp, current_scene_id FROM player_attributes WHERE account_id = ?", [$playerId]);
            $playerAttr = $stmt->fetch();
            $stmt->closeCursor();

            if (!$playerAttr) {
                 $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '玩家不存在']);
                return;
            }

            if ($playerAttr['hp'] > 0) {
                 $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '你还活着，无需复活']);
                return;
            }
            
            // echo "玩家 {$playerId} 尝试复活\n";
            
            // 获取当前场景ID
            $currentSceneId = $playerAttr['current_scene_id'];
            
            // 寻找最近的复活点
            $reviveSceneId = $this->findNearestRevivePoint($currentSceneId);
            
            // 只传送玩家到复活点，不恢复生命值
            $db->query("UPDATE player_attributes SET current_scene_id = ? WHERE account_id = ?", 
                       [$reviveSceneId, $playerId]);
            
            // 从当前场景移除玩家
            if ($currentSceneId) {
                RedisManager::getInstance()->with(function($redis) use ($playerId, $currentSceneId) {
                    $redis->sRem("scene_players:{$currentSceneId}", $playerId);
                });
                $this->broadcastPlayerUpdate($currentSceneId);
            }
            
            // 添加玩家到新场景
            RedisManager::getInstance()->with(function($redis) use ($playerId, $reviveSceneId) {
                $redis->set("player_scene:{$playerId}", $reviveSceneId);
                $redis->sAdd("scene_players:{$reviveSceneId}", $playerId);
            });
            
            $updatedPlayer = $this->getPlayerWithAttributes($playerId);
            
            // 获取新场景信息
            $sceneData = $this->sceneManager->getSceneData($reviveSceneId);
            
                         // 发送复活成功和场景进入消息
             $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_REVIVED, ['player' => $updatedPlayer]);
             $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                 'message' => "一束圣光过后，你已来到复活点。", 
                 'context' => 'system'
             ]);
            
            // 发送场景数据
            $sceneResponse = $this->handleEnterScene($fd, [
                'player_id' => $playerId,
                'scene_id' => $reviveSceneId
            ]);
            
            // 处理场景进入返回值
            if ($sceneResponse && is_array($sceneResponse)) {
                $this->sendMessage($fd, $sceneResponse[0], $sceneResponse[1]);
            }
            
                        //  echo "玩家 {$playerId} 已传送到复活点场景 {$reviveSceneId}，但未恢复生命值。\n";
            
        } catch (Exception $e) {
            echo "复活处理失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '复活失败: ' . $e->getMessage()]);
        }
    }


    /**
     * 处理回城请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleRevivecityRequest($fd, $data) {
        try {
            $playerId = $data['player_id'];

            $db = Database::getInstance();
            $stmt = $db->query("SELECT hp, max_hp, current_scene_id FROM player_attributes WHERE account_id = ?", [$playerId]);
            $playerAttr = $stmt->fetch();
            $stmt->closeCursor();

            if (!$playerAttr) {
                 $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '玩家不存在']);
                return;
            }

            // echo "玩家 {$playerId} 尝试复活\n";
            
            // 获取当前场景ID
            $currentSceneId = $playerAttr['current_scene_id'];
            
            // 寻找最近的复活点
            $reviveSceneId = $this->findNearestRevivePoint($currentSceneId);
            
            // 只传送玩家到复活点，不恢复生命值
            $db->query("UPDATE player_attributes SET current_scene_id = ? WHERE account_id = ?", 
                       [$reviveSceneId, $playerId]);
            
            // 从当前场景移除玩家
            if ($currentSceneId) {
                RedisManager::getInstance()->with(function($redis) use ($playerId, $currentSceneId) {
                    $redis->sRem("scene_players:{$currentSceneId}", $playerId);
                });
                $this->broadcastPlayerUpdate($currentSceneId);
            }
            
            // 添加玩家到新场景
            RedisManager::getInstance()->with(function($redis) use ($playerId, $reviveSceneId) {
                $redis->set("player_scene:{$playerId}", $reviveSceneId);
                $redis->sAdd("scene_players:{$reviveSceneId}", $playerId);
            });
            
            $updatedPlayer = $this->getPlayerWithAttributes($playerId);
            
            // 获取新场景信息
            $sceneData = $this->sceneManager->getSceneData($reviveSceneId);
            
                         // 发送复活成功和场景进入消息
             $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_REVIVED, ['player' => $updatedPlayer]);
             $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                 'message' => "一束圣光过后，你已来到复活点。", 
                 'context' => 'system'
             ]);
            
            // 发送场景数据
            $sceneResponse = $this->handleEnterScene($fd, [
                'player_id' => $playerId,
                'scene_id' => $reviveSceneId
            ]);
            
            // 处理场景进入返回值
            if ($sceneResponse && is_array($sceneResponse)) {
                $this->sendMessage($fd, $sceneResponse[0], $sceneResponse[1]);
            }
            
                        //  echo "玩家 {$playerId} 已传送到复活点场景 {$reviveSceneId}，但未恢复生命值。\n";
            
        } catch (Exception $e) {
            echo "复活处理失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '复活失败: ' . $e->getMessage()]);
        }
    }


    /**
     * 处理在复活建筑中的复活请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleReviveInBuilding($fd, $data) {
        try {
            $playerId = $data['player_id'];
            $sceneBuildingId = $data['scene_building_id'] ?? null;

            if (!$sceneBuildingId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的建筑ID']);
            }

            $db = Database::getInstance();
            
            // 验证建筑类型是否为复活点
            $stmt = $db->query(
                "SELECT b.type 
                 FROM scene_buildings sb
                 JOIN buildings b ON sb.building_id = b.id
                 WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $building = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$building || $building['type'] !== 'REVIVE_POINT') {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '这不是一个复活点建筑']);
            }

            // 获取玩家状态
            $stmt = $db->query("SELECT hp, max_hp, mp, max_mp FROM player_attributes WHERE account_id = ?", [$playerId]);
            $playerAttr = $stmt->fetch();
            $stmt->closeCursor();

            if (!$playerAttr) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '玩家不存在']);
            }

            // 恢复玩家生命值和魔法值
            $conn = $db->getConnection();
            $this->setPlayerToFullHealthAndMana($playerId, $conn);
            
            // 获取更新后的玩家数据
            $updatedPlayer = $this->getPlayerWithAttributes($playerId);
            
            // 发送复活成功消息
            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_REVIVED, ['player' => $updatedPlayer]);
            
            // 发送信息消息
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                'message' => "神圣的力量涌入你的身体，你感到精力充沛！", 
                'context' => 'system'
            ]);
            
            // 发送复活点数据更新
            $this->handleGetRevivePointData($fd, $sceneBuildingId, "", $playerId, true);
            
            // 广播玩家状态更新给场景中的其他玩家
            $sceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });
            
            if ($sceneId) {
                $this->broadcastPlayerUpdate($sceneId);
            }
            
            error_log("玩家 {$playerId} 在复活建筑中恢复了生命值和魔法值");
            
        } catch (Exception $e) {
            error_log("在复活建筑中复活失败: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '复活失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 处理传送请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTeleport($fd, $data) {
        try {
            $playerId = $data['player_id'];
            $targetSceneId = $data['target_scene_id'];
            $sceneBuildingId = $data['scene_building_id'];
            
            if (!$playerId || !$targetSceneId || !$sceneBuildingId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '传送请求参数不完整']);
            }

            $db = Database::getInstance();
            
            // 验证传送点和目标场景的对应关系，并检查所需物品
            $stmt = $db->query(
                "SELECT td.required_item_id, td.required_quantity, it.name as required_item_name
                 FROM teleporter_destinations td
                 LEFT JOIN item_templates it ON td.required_item_id = it.id
                 WHERE td.scene_building_id = ? AND td.target_scene_id = ?",
                [$sceneBuildingId, $targetSceneId]
            );
            $destinationData = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$destinationData) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的传送目标']);
            }
            
            // 检查是否需要消耗物品
            if ($destinationData['required_item_id']) {
                $requiredItemId = $destinationData['required_item_id'];
                $requiredQuantity = $destinationData['required_quantity'];
                $requiredItemName = $destinationData['required_item_name'];
                
                // 检查玩家是否有足够的物品
                $stmt = $db->query(
                    "SELECT id, quantity FROM player_inventory 
                     WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0",
                    [$playerId, $requiredItemId]
                );
                $inventoryItem = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();
                
                if (!$inventoryItem || $inventoryItem['quantity'] < $requiredQuantity) {
                    return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                        'message' => "传送失败，需要 {$requiredQuantity} 个 {$requiredItemName}",
                        'context' => 'teleport_item_required'
                    ]);
                }
                
                // 扣除物品
                $newQuantity = $inventoryItem['quantity'] - $requiredQuantity;
                if ($newQuantity > 0) {
                    // 如果还有剩余，更新数量
                    $db->query(
                        "UPDATE player_inventory SET quantity = ? WHERE id = ?",
                        [$newQuantity, $inventoryItem['id']]
                    );
                } else {
                    // 如果数量为0，删除物品
                    $db->query(
                        "DELETE FROM player_inventory WHERE id = ?",
                        [$inventoryItem['id']]
                    );
                }
                
                // 通知玩家物品已被消耗
                $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'message' => "消耗了 {$requiredQuantity} 个 {$requiredItemName} 进行传送", 
                    'context' => 'item_consumed'
                ]);
            }
            
            // 获取玩家当前场景
            $stmt = $db->query("SELECT current_scene_id FROM player_attributes WHERE account_id = ?", [$playerId]);
            $playerData = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$playerData) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '找不到玩家数据']);
            }
            
            $currentSceneId = $playerData['current_scene_id'];
            
            // 更新玩家所在场景
            $db->query("UPDATE player_attributes SET current_scene_id = ? WHERE account_id = ?", 
                      [$targetSceneId, $playerId]);
            
            // 从当前场景移除玩家
            if ($currentSceneId) {
                RedisManager::getInstance()->with(function($redis) use ($playerId, $currentSceneId) {
                    $redis->sRem("scene_players:{$currentSceneId}", $playerId);
                });
                $this->broadcastPlayerUpdate($currentSceneId);
            }
            
            // 添加玩家到新场景
            RedisManager::getInstance()->with(function($redis) use ($playerId, $targetSceneId) {
                $redis->set("player_scene:{$playerId}", $targetSceneId);
                $redis->sAdd("scene_players:{$targetSceneId}", $playerId);
            });
            
            // 发送传送成功消息
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                'message' => "传送成功，你已抵达目的地。", 
                'context' => 'system'
            ]);
            
            // 发送场景数据
            $sceneResponse = $this->handleEnterScene($fd, [
                'player_id' => $playerId,
                'scene_id' => $targetSceneId
            ]);
            
            // 处理场景进入返回值
            if ($sceneResponse && is_array($sceneResponse)) {
                $this->sendMessage($fd, $sceneResponse[0], $sceneResponse[1]);
            }
            
        } catch (Exception $e) {
            echo "传送处理失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '传送失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 处理获取传送点数据的请求
     * @param int $fd WebSocket连接标识符
     * @param int $sceneBuildingId 场景建筑ID
     * @param string $teleporterName 传送点名称
     * @param int $playerId 玩家ID
     * @param bool $isRefresh 是否为刷新请求
     */
    private function handleGetTeleporterData($fd, $sceneBuildingId, $teleporterName, $playerId, $isRefresh = false) {
        try {
            if (!$sceneBuildingId) {
                error_log("handleGetTeleporterData - 错误: 无效的建筑ID");
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的建筑ID。']);
            }

            if (!$playerId) {
                error_log("handleGetTeleporterData - 错误: 无效的玩家ID");
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的玩家ID。']);
            }

            $db = Database::getInstance();
            
            // 获取传送点可传送的目标场景列表
            $stmt = $db->query(
                "SELECT td.id, td.scene_building_id, td.target_scene_id, td.required_item_id,
                        td.required_quantity, s.name as scene_name, s.description as scene_description,
                        it.name as required_item_name, it.description as required_item_description 
                FROM teleporter_destinations td
                JOIN scenes s ON td.target_scene_id = s.id
                LEFT JOIN item_templates it ON td.required_item_id = it.id
                WHERE td.scene_building_id = ?
                ORDER BY td.sort_order ASC",
                [$sceneBuildingId]
            );
            $destinations = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (empty($destinations)) {
                error_log("handleGetTeleporterData - 错误: 该传送点没有配置目标场景");
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该传送点没有可用的传送目标。']);
            }

            // 构建响应数据
            $response = [
                'type' => 'TELEPORTER',
                'name' => $teleporterName,
                'scene_building_id' => $sceneBuildingId,
                'is_refresh' => $isRefresh,
                'destinations' => $destinations
            ];
            
            error_log("handleGetTeleporterData - 发送传送点数据: " . json_encode($response));
            
            $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, $response);
            
        } catch (Exception $e) {
            error_log("获取传送点数据失败: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '获取传送点数据失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 处理获取仓库数据请求
     * @param int $fd WebSocket连接标识符
     * @param int $sceneBuildingId 场景建筑ID
     * @param string $warehouseName 仓库名称
     * @param int $playerId 玩家ID
     * @param bool $isRefresh 是否为刷新操作
     */
    private function handleGetWarehouseData($fd, $sceneBuildingId, $warehouseName, $playerId, $isRefresh) {
        try {
            $db = Database::getInstance();

            // 1. 获取仓库配置
            $configStmt = $db->query(
                "SELECT base_capacity, max_capacity FROM warehouse_config WHERE scene_building_id = ?",
                [$sceneBuildingId]
            );
            $config = $configStmt->fetch(PDO::FETCH_ASSOC);
            $configStmt->closeCursor();

            // 如果没有配置，创建默认配置
            if (!$config) {
                $db->query(
                    "INSERT INTO warehouse_config (scene_building_id, base_capacity, max_capacity) VALUES (?, 50, 200)",
                    [$sceneBuildingId]
                );
                $config = ['base_capacity' => 50, 'max_capacity' => 200];
            }

            // 2. 计算玩家当前仓库容量（基础容量 + 扩容）
            $expansionStmt = $db->query(
                "SELECT COALESCE(SUM(expansion_amount), 0) as total_expansion
                 FROM player_warehouse_expansions
                 WHERE player_id = ? AND scene_building_id = ?",
                [$playerId, $sceneBuildingId]
            );
            $expansionData = $expansionStmt->fetch(PDO::FETCH_ASSOC);
            $expansionStmt->closeCursor();

            $currentCapacity = $config['base_capacity'] + ($expansionData['total_expansion'] ?? 0);
            $currentCapacity = min($currentCapacity, $config['max_capacity']); // 不能超过最大容量

            // 3. 获取仓库中的物品（按绑定状态、类别、名称排序）
            $itemsStmt = $db->query(
                "SELECT ws.id, ws.item_template_id, ws.quantity, ws.instance_data, ws.is_bound, ws.created_at,
                        it.name, it.category, it.stackable, it.max_stack, it.description, it.effects,
                        ed.stats, ed.slot, ed.job_restriction, ed.sockets
                 FROM warehouse_storage ws
                 JOIN item_templates it ON ws.item_template_id = it.id
                 LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
                 WHERE ws.player_id = ? AND ws.scene_building_id = ?
                 ORDER BY ws.is_bound ASC, it.category ASC, it.name ASC, ws.quantity DESC",
                [$playerId, $sceneBuildingId]
            );
            $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
            $itemsStmt->closeCursor();

            // 4. 计算当前使用的空间（按实际物品数量计算）
            $usedSpace = 0;
            foreach ($items as $item) {
                // 无论可堆叠还是不可堆叠，都按实际数量计算容量
                $usedSpace += $item['quantity'];
            }

            // 5. 获取可用的扩容物品
            $expansionItemsStmt = $db->query(
                "SELECT wei.item_template_id, wei.expansion_amount, wei.max_uses,
                        it.name as item_name,
                        COALESCE(pwe_count.usage_count, 0) as current_usage
                 FROM warehouse_expansion_items wei
                 JOIN item_templates it ON wei.item_template_id = it.id
                 LEFT JOIN (
                     SELECT item_template_id, COUNT(*) as usage_count
                     FROM player_warehouse_expansions
                     WHERE player_id = ? AND scene_building_id = ?
                     GROUP BY item_template_id
                 ) pwe_count ON wei.item_template_id = pwe_count.item_template_id
                 WHERE wei.is_active = 1
                 AND (wei.max_uses IS NULL OR COALESCE(pwe_count.usage_count, 0) < wei.max_uses)
                 ORDER BY wei.expansion_amount DESC",
                [$playerId, $sceneBuildingId]
            );
            $expansionItems = $expansionItemsStmt->fetchAll(PDO::FETCH_ASSOC);
            $expansionItemsStmt->closeCursor();

            // 6. 构建响应数据
            $response = [
                'type' => 'WAREHOUSE',
                'name' => $warehouseName,
                'scene_building_id' => $sceneBuildingId,
                'is_refresh' => $isRefresh,
                'capacity' => [
                    'current' => $currentCapacity,
                    'used' => $usedSpace,
                    'available' => $currentCapacity - $usedSpace,
                    'max' => $config['max_capacity']
                ],
                'items' => $items,
                'expansion_items' => $expansionItems
            ];

            $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, $response);

        } catch (Exception $e) {
            error_log("获取仓库数据失败: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '获取仓库数据失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 处理仓库存入物品请求
     * @param int $fd WebSocket连接标识符
     * @param array $payload 请求数据
     */
    private function handleWarehouseDeposit($fd, $payload) {
        $playerId = $payload['player_id'] ?? null;
        $sceneBuildingId = $payload['scene_building_id'] ?? null;
        $inventoryId = $payload['inventory_id'] ?? null;
        $quantity = $payload['quantity'] ?? 1;

        if (!$playerId || !$sceneBuildingId || !$inventoryId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '参数不完整']);
        }

        if ($quantity <= 0) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '存入数量必须大于0']);
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();

        try {
            $conn->beginTransaction();

            // 1. 验证建筑类型是仓库
            $buildingStmt = $db->query(
                "SELECT b.type, b.name
                 FROM buildings b
                 JOIN scene_buildings sb ON sb.building_id = b.id
                 WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if (!$building || $building['type'] !== 'WAREHOUSE') {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '只能在仓库建筑中存放物品']);
            }

            // 2. 获取玩家背包中的物品
            $itemStmt = $db->query(
                "SELECT pi.*, it.name, it.stackable, it.max_stack, it.category
                 FROM player_inventory pi
                 JOIN item_templates it ON pi.item_template_id = it.id
                 WHERE pi.id = ? AND pi.player_id = ? AND pi.is_equipped = 0",
                [$inventoryId, $playerId]
            );
            $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
            $itemStmt->closeCursor();

            if (!$item) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '物品不存在或已装备']);
            }

            if ($quantity > $item['quantity']) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '存入数量超过拥有数量']);
            }

            // 3. 检查仓库容量
            $capacityCheck = $this->checkWarehouseCapacity($playerId, $sceneBuildingId, $item, $quantity);
            if (!$capacityCheck['success']) {
                $conn->rollBack();
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $capacityCheck['message']]);
            }

            // 4. 处理存入逻辑
            if ($item['stackable']) {
                // 可堆叠物品：按绑定状态分别处理
                $remainingQuantity = $quantity;
                $maxStack = $item['max_stack'] ?? 999;



                while ($remainingQuantity > 0) {
                    // 查找可以合并的现有堆叠（相同绑定状态）
                    // 对于可堆叠物品，只要物品模板ID和绑定状态相同就可以合并
                    // 同时确保instance_data为NULL（可堆叠物品不应该有特殊实例数据）
                    $existingStmt = $db->query(
                        "SELECT id, quantity FROM warehouse_storage
                         WHERE player_id = ? AND scene_building_id = ? AND item_template_id = ?
                         AND is_bound = ? AND quantity < ? AND (instance_data IS NULL OR instance_data = '')
                         ORDER BY quantity DESC LIMIT 1",
                        [$playerId, $sceneBuildingId, $item['item_template_id'], $item['is_bound'], $maxStack]
                    );
                    $existing = $existingStmt->fetch(PDO::FETCH_ASSOC);
                    $existingStmt->closeCursor();

                    if ($existing) {
                        // 计算可以合并的数量
                        $canMerge = min($remainingQuantity, $maxStack - $existing['quantity']);



                        // 合并到现有堆叠
                        $db->query(
                            "UPDATE warehouse_storage SET quantity = quantity + ?, updated_at = NOW() WHERE id = ?",
                            [$canMerge, $existing['id']]
                        );

                        $remainingQuantity -= $canMerge;
                    } else {
                        // 创建新的存储记录
                        $newQuantity = min($remainingQuantity, $maxStack);



                        // 对于可堆叠物品，通常不需要保留instance_data，设置为NULL以便合并
                        $instanceData = $item['stackable'] ? null : $item['instance_data'];

                        $db->query(
                            "INSERT INTO warehouse_storage (player_id, scene_building_id, item_template_id, quantity, instance_data, is_bound)
                             VALUES (?, ?, ?, ?, ?, ?)",
                            [$playerId, $sceneBuildingId, $item['item_template_id'], $newQuantity, $instanceData, $item['is_bound']]
                        );

                        $remainingQuantity -= $newQuantity;
                    }
                }
            } else {
                // 不可堆叠物品：为每个物品创建单独记录
                for ($i = 0; $i < $quantity; $i++) {
                    $db->query(
                        "INSERT INTO warehouse_storage (player_id, scene_building_id, item_template_id, quantity, instance_data, is_bound)
                         VALUES (?, ?, ?, 1, ?, ?)",
                        [$playerId, $sceneBuildingId, $item['item_template_id'], $item['instance_data'], $item['is_bound']]
                    );
                }
            }

            // 5. 从玩家背包中扣除物品
            if ($quantity >= $item['quantity']) {
                // 全部存入，删除背包记录
                $db->query("DELETE FROM player_inventory WHERE id = ?", [$inventoryId]);
            } else {
                // 部分存入，减少背包数量
                $db->query(
                    "UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?",
                    [$quantity, $inventoryId]
                );
            }

            $conn->commit();

            // 6. 检查任务进度
            $this->checkWarehouseQuestProgress($fd, $playerId, 'deposit', [
                'item_template_id' => $item['item_template_id'],
                'quantity' => $quantity,
                'item_name' => $item['name']
            ]);

            // 7. 发送成功响应
            $this->sendMessage($fd, MessageProtocol::S2C_SUCCESS, [
                'message' => "成功存入 {$item['name']} x{$quantity}"
            ]);

            // 8. 背包数据会通过 handleSuccessMessage 自动更新

        } catch (Exception $e) {
            $conn->rollBack();
            error_log("仓库存入物品失败: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '存入失败: ' . $e->getMessage()]);
        }
    }

    /**
     * 检查仓库容量是否足够
     * @param int $playerId 玩家ID
     * @param int $sceneBuildingId 场景建筑ID
     * @param array $item 物品信息
     * @param int $quantity 存入数量
     * @return array 检查结果
     */
    private function checkWarehouseCapacity($playerId, $sceneBuildingId, $item, $quantity) {
        $db = Database::getInstance();

        try {
            // 1. 获取仓库配置
            $configStmt = $db->query(
                "SELECT base_capacity, max_capacity FROM warehouse_config WHERE scene_building_id = ?",
                [$sceneBuildingId]
            );
            $config = $configStmt->fetch(PDO::FETCH_ASSOC);
            $configStmt->closeCursor();

            if (!$config) {
                $config = ['base_capacity' => 50, 'max_capacity' => 200];
            }

            // 2. 计算当前容量
            $expansionStmt = $db->query(
                "SELECT COALESCE(SUM(expansion_amount), 0) as total_expansion
                 FROM player_warehouse_expansions
                 WHERE player_id = ? AND scene_building_id = ?",
                [$playerId, $sceneBuildingId]
            );
            $expansionData = $expansionStmt->fetch(PDO::FETCH_ASSOC);
            $expansionStmt->closeCursor();

            $currentCapacity = $config['base_capacity'] + ($expansionData['total_expansion'] ?? 0);
            $currentCapacity = min($currentCapacity, $config['max_capacity']);

            // 3. 计算当前使用的空间
            $itemsStmt = $db->query(
                "SELECT ws.quantity, it.stackable, it.max_stack
                 FROM warehouse_storage ws
                 JOIN item_templates it ON ws.item_template_id = it.id
                 WHERE ws.player_id = ? AND ws.scene_building_id = ?",
                [$playerId, $sceneBuildingId]
            );
            $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
            $itemsStmt->closeCursor();

            $usedSpace = 0;
            foreach ($items as $warehouseItem) {
                // 无论可堆叠还是不可堆叠，都按实际数量计算容量
                $usedSpace += $warehouseItem['quantity'];
            }

            // 4. 计算新物品需要的空间（按实际数量计算）
            $neededSpace = $quantity;

            // 5. 检查是否有足够空间
            $availableSpace = $currentCapacity - $usedSpace;
            if ($neededSpace > $availableSpace) {
                return [
                    'success' => false,
                    'message' => "仓库空间不足，需要 {$neededSpace} 个空间，但只有 {$availableSpace} 个可用空间"
                ];
            }

            return ['success' => true];

        } catch (Exception $e) {
            error_log("检查仓库容量失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '检查仓库容量失败'
            ];
        }
    }

    /**
     * 处理与NPC对话的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTalkToNpc($fd, $data) {
        $playerId = $data['player_id'];
        $npcId = $data['npc_id'];
        
        try {
            // 获取玩家当前场景
            $currentSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });
            
            if (!$currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取当前场景信息']);
            }
            
            // 检查NPC是否在当前场景中
            $npcDetails = $this->npcManager->getNpcDetails($npcId);
            if (!$npcDetails || $npcDetails['scene_id'] !== $currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该NPC不在当前场景中']);
            }
            
            // 开始对话
            $dialogueData = $this->npcManager->startDialogue($npcId, $playerId);
            
            if (isset($dialogueData['error'])) {
                // 使用对话响应格式发送错误，而不是一般的错误消息
                return $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, ['error' => $dialogueData['error'], 'npc' => $npcDetails]);
            }
            
            // 发送对话数据
            $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, $dialogueData);
            
        } catch (Exception $e) {
            echo "处理NPC对话请求失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理NPC对话请求失败']);
        }
    }
    
    /**
     * 处理继续对话的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleContinueDialogue($fd, $data) {
        $playerId = $data['player_id'];
        $npcId = $data['npc_id'];
        $nodeId = $data['node_id'];
        $history = $data['history'] ?? [];
        
        try {
            // 获取玩家当前场景
            $currentSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });
            
            if (!$currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取当前场景信息']);
            }
            
            // 检查NPC是否在当前场景中
            $npcDetails = $this->npcManager->getNpcDetails($npcId);
            if (!$npcDetails || $npcDetails['scene_id'] !== $currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该NPC不在当前场景中']);
            }
            
            // 获取NPC详情，以便在错误响应中使用
            $npcDetails = $this->npcManager->getNpcDetails($npcId);
            
            // 继续对话
            $dialogueData = $this->npcManager->continueDialogue($npcId, $nodeId, $playerId, $history);
            
            if (isset($dialogueData['error'])) {
                // 使用对话响应格式发送错误，而不是一般的错误消息
                return $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, [
                    'error' => $dialogueData['error'], 
                    'npc' => $npcDetails
                ]);
            }
            
            // 发送对话数据
            $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, $dialogueData);
            
        } catch (Exception $e) {
            echo "处理继续对话请求失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理继续对话请求失败']);
        }
    }
    
    /**
     * 处理选择对话选项的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleSelectDialogueOption($fd, $data) {
        $playerId = $data['player_id'];
        $npcId = $data['npc_id'];
        $nodeId = $data['node_id'];
        $optionId = $data['option_id'];
        $history = $data['history'] ?? [];
        
        try {
            // 获取玩家当前场景
            $currentSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });
            
            if (!$currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取当前场景信息']);
            }
            
            // 检查NPC是否在当前场景中
            $npcDetails = $this->npcManager->getNpcDetails($npcId);
            if (!$npcDetails || $npcDetails['scene_id'] !== $currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该NPC不在当前场景中']);
            }
            
            // 选择对话选项
            $dialogueData = $this->npcManager->selectDialogueOption($npcId, $nodeId, $optionId, $playerId, $history);
            
            if (isset($dialogueData['error'])) {
                // 使用对话响应格式发送错误，而不是一般的错误消息
                return $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, [
                    'error' => $dialogueData['error'], 
                    'npc' => $npcDetails
                ]);
            }
            
            // 发送对话数据
            $this->sendMessage($fd, MessageProtocol::S2C_NPC_DIALOGUE, $dialogueData);
            
        } catch (Exception $e) {
            echo "处理选择对话选项请求失败: " . $e->getMessage() . "\n";
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理选择对话选项请求失败']);
        }
    }
    
    /**
     * 处理获取任务列表的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleGetQuestList($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? $data['player_id'] ?? null;
        $npcId = $data['npc_id'] ?? null;
        
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }
        
        if (!isset($this->questManager)) {
            $this->questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });
        }
        
        try {
            if ($npcId) {
                // 获取特定NPC可提供的任务
                // 不再获取可接取的任务，只获取可完成的任务
                $completableQuests = $this->questManager->getCompletableQuestsForNPC($npcId, $playerId);
                
                // 为每个任务添加交付提示信息
                foreach ($completableQuests as &$quest) {
                    if (!empty($quest['receiver_npc_name']) && !isset($quest['turn_in_tip'])) {
                        $quest['turn_in_tip'] = "将任务交付给: {$quest['receiver_npc_name']}";
                    }
                }
                unset($quest);
                
                // 获取玩家所有活跃任务，筛选出与当前NPC相关的任务
                $activeQuests = $this->questManager->getPlayerQuests($playerId, 'active');
                $relatedActiveQuests = [];
                
                foreach ($activeQuests as $quest) {
                    // 检查当前NPC是否是任务的接收者
                    if (isset($quest['receiver_npc_id']) && $quest['receiver_npc_id']) {
                        // 获取当前NPC的模板ID
                        $stmt = $this->db->getConnection()->prepare("SELECT template_id FROM npc_instances WHERE id = ?");
                        $stmt->execute([$npcId]);
                        $npcTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($npcTemplate && $npcTemplate['template_id'] == $quest['receiver_npc_id']) {
                            $relatedActiveQuests[] = $quest;
                        }
                    }
                }
                
                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_LIST, [
                    'quests_from_npc' => true,
                    'npc_id' => $npcId,
                    'available_quests' => [], // 不再返回可接取的任务
                    'completable_quests' => $completableQuests,
                    'related_active_quests' => $relatedActiveQuests // 添加与NPC相关的进行中任务
                ]);
            } else {
                // 获取玩家所有任务列表
                $activeQuests = $this->questManager->getPlayerQuests($playerId, 'active');
                $completedQuests = $this->questManager->getPlayerQuests($playerId, 'completed');
                $abandonedQuests = $this->questManager->getPlayerQuests($playerId, 'abandoned');
                
                // 为每个任务添加交付提示信息
                foreach ($activeQuests as &$quest) {
                    if (!isset($quest['turn_in_tip'])) {
                        if (!empty($quest['receiver_npc_name'])) {
                            $quest['turn_in_tip'] = "将任务交付给: {$quest['receiver_npc_name']}";
                        }
                    }
                }
                unset($quest);
                
                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_LIST, [
                    'quests_from_npc' => false,
                    'active_quests' => $activeQuests,
                    'completed_quests' => $completedQuests,
                    'abandoned_quests' => $abandonedQuests
                ]);
            }
        } catch (Exception $e) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '获取任务列表失败']);
        }
    }
    
    /**
     * 处理接受任务的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleAcceptQuest($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $questId = $data['quest_id'];
        
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }
        
        if (!isset($this->questManager)) {
            $this->questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });
        }
        
        try {
            // 调用QuestManager的acceptQuest方法来保存任务
            $result = $this->questManager->acceptQuest($playerId, $questId);
            
            if ($result['success']) {
                // 获取任务详情
                $questDetails = $this->questManager->getQuestDetails($questId);
                
                if ($questDetails) {
                    // 获取交付NPC详情
                    if (!empty($questDetails['receiver_npc_id']) && !isset($questDetails['turn_in_tip'])) {
                        $stmt = $this->db->getConnection()->prepare("
                            SELECT nt.name, GROUP_CONCAT(DISTINCT s.name) as scene_names 
                            FROM npc_templates nt
                            LEFT JOIN npc_instances ni ON nt.id = ni.template_id
                            LEFT JOIN scenes s ON ni.scene_id = s.id
                            WHERE nt.id = ?
                            GROUP BY nt.id
                        ");
                        $stmt->execute([$questDetails['receiver_npc_id']]);
                        $npcInfo = $stmt->fetch(PDO::FETCH_ASSOC);
                        
                        if ($npcInfo) {
                            $questDetails['turn_in_tip'] = "完成后请将任务交付给: {$npcInfo['name']}";
                            
                            // 确保不会有重复信息
                            if (isset($questDetails['receiver_location'])) {
                                unset($questDetails['receiver_location']);
                            }
                        }
                    }
                    
                    // 发送任务更新通知
                    $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                        'action' => 'accepted',
                        'quest' => $questDetails,
                        'message' => $result['message']
                    ]);
                    
                    // 刷新任务列表
                    $this->handleGetQuestList($fd, ['player_id' => $playerId]);

                    // 任务接取后，刷新当前场景的NPC数据（更新任务标志）
                    $this->refreshCurrentSceneNpcs($fd, $playerId);
                } else {
                    $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '任务不存在']);
                }
            } else {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $result['message']]);
            }
        } catch (Exception $e) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '接受任务失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 处理完成任务的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleCompleteQuest($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $questId = $data['quest_id'];
        
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }
        
        if (!isset($this->questManager)) {
            $this->questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });
        }
        
        try {
            $result = $this->questManager->completeQuest($playerId, $questId);
            
            if ($result['success']) {
                // 发送任务更新通知
                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                    'action' => 'completed',
                    'quest_id' => $questId,
                    'rewards' => $result['rewards'] ?? [], // 确保rewards字段始终存在
                    'message' => $result['message']
                ]);

                // 刷新任务列表
                $this->handleGetQuestList($fd, ['player_id' => $playerId]);

                // 如果有物品奖励，立即刷新背包
                if (isset($result['rewards']['items']) && !empty($result['rewards']['items'])) {
                    $this->handleGetInventory($fd, ['player_id' => $playerId]);
                }

                // 如果给了经验，更新玩家属性并处理等级升级
                if (isset($result['rewards']['exp']) && $result['rewards']['exp'] > 0) {
                    // 调用grantExperience方法，第三个参数应该是数据库连接，保持为null以使方法内部创建自己的事务
                    $this->grantExperience($playerId, $result['rewards']['exp']);

                    // 经验处理完成后，再次刷新背包（以防升级过程中有任何影响）
                    $this->handleGetInventory($fd, ['player_id' => $playerId]);
                }

                // 任务完成后，刷新当前场景的NPC数据（更新任务标志）
                $this->refreshCurrentSceneNpcs($fd, $playerId);
            } else {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $result['message']]);
            }
        } catch (Exception $e) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '完成任务失败：' . $e->getMessage()]);
        }
    }
    
    /**
     * 刷新当前场景的NPC数据（更新任务标志）
     * @param int $fd WebSocket连接标识符
     * @param int $playerId 玩家ID
     */
    private function refreshCurrentSceneNpcs($fd, $playerId) {
        try {
            // 获取玩家当前场景
            $stmt = $this->db->getConnection()->prepare("SELECT current_scene_id FROM player_attributes WHERE account_id = ?");
            $stmt->execute([$playerId]);
            $playerData = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$playerData || !$playerData['current_scene_id']) {
                return; // 玩家没有当前场景信息
            }

            $currentSceneId = $playerData['current_scene_id'];

            // 获取更新后的场景NPC数据
            if (!isset($this->questManager)) {
                $this->questManager = new QuestManager(function() {
                    return $this->db->getConnection();
                });
            }
            $updatedNpcs = $this->npcManager->getSceneNpcsWithQuestInfo($currentSceneId, $playerId, $this->questManager);

            // 发送场景NPC更新消息
            $this->sendMessage($fd, MessageProtocol::S2C_SCENE_NPCS, [
                'scene_id' => $currentSceneId,
                'npcs' => $updatedNpcs
            ]);

            error_log("[场景NPC刷新] 玩家 {$playerId} 的场景 {$currentSceneId} NPC数据已刷新");

        } catch (Exception $e) {
            error_log("[场景NPC刷新] 刷新失败: " . $e->getMessage());
        }
    }

    /**
     * 处理放弃任务的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleAbandonQuest($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $questId = $data['quest_id'];
        
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }
        
        if (!isset($this->questManager)) {
            $this->questManager = new QuestManager(function() {
                return $this->db->getConnection();
            });
        }
        
        try {
            $result = $this->questManager->abandonQuest($playerId, $questId);
            
            if ($result['success']) {
                // 发送任务更新通知
                $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                    'action' => 'abandoned',
                    'quest_id' => $questId,
                    'message' => $result['message']
                ]);
                
                // 刷新任务列表
                $this->handleGetQuestList($fd, ['player_id' => $playerId]);
            } else {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $result['message']]);
            }
        } catch (Exception $e) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '放弃任务失败：' . $e->getMessage()]);
        }
    }

    /**
     * 处理查看NPC详情的请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleViewNpcDetail($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $npcId = $data['npc_id'];
        
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
        }
        
        try {
            // 获取玩家当前场景
            $currentSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
                return $redis->get("player_scene:{$playerId}");
            });
            
            if (!$currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取当前场景信息']);
            }
            
            // 检查NPC是否在当前场景中
            $npcDetails = $this->npcManager->getNpcDetails($npcId);
            if (!$npcDetails || $npcDetails['scene_id'] !== $currentSceneId) {
                return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该NPC不在当前场景中']);
            }
            
            // 检查NPC是否有对话功能 - 使用动态对话树检查
            $hasDialogue = false;
            try {
                // 调用预加载对话方法来检查是否有适用的对话树
                $preloadResult = $this->npcManager->preloadDialogue($npcId, $playerId);
                $hasDialogue = isset($preloadResult['success']) && $preloadResult['success'];
                error_log("[NPC详情] NPC {$npcId} 对话检查结果: " . ($hasDialogue ? '有对话' : '无对话'));
            } catch (Exception $e) {
                error_log("[NPC详情] 检查NPC对话功能时出错: " . $e->getMessage());
                $hasDialogue = false;
            }

            // 动态检查NPC是否有可交互的任务（无论NPC类型如何）
            $hasQuestsForPlayer = false;
            if (!isset($this->questManager)) {
                $this->questManager = new QuestManager(function() {
                    return $this->db->getConnection();
                });
            }

            // 获取NPC可提供的任务
            $availableQuests = $this->questManager->getAvailableQuestsFromNPC($npcId, $playerId);

            // 获取可以在该NPC处完成的任务
            $completableQuests = $this->questManager->getCompletableQuestsForNPC($npcId, $playerId);

            // 如果有可接或可完成的任务，设置标志为true
            if (!empty($availableQuests) || !empty($completableQuests)) {
                $hasQuestsForPlayer = true;
            }

            error_log("[NPC详情] NPC {$npcId} 任务检查结果: 可接任务" . count($availableQuests) . "个, 可完成任务" . count($completableQuests) . "个, has_quests_for_player=" . ($hasQuestsForPlayer ? 'true' : 'false'));
            
            // 准备NPC详情数据
            $npcDetailData = [
                'npc' => [
                    'id' => $npcDetails['id'],
                    'name' => $npcDetails['name'],
                    'description' => $npcDetails['description'] ?? '',
                    'level' => $npcDetails['level'],
                    'avatar' => $npcDetails['avatar'] ?? '',
                    'is_merchant' => $npcDetails['is_merchant'] ? true : false,
                    'is_quest_giver' => $npcDetails['is_quest_giver'], // 保留用于NPC列表的图标
                    'has_quests_for_player' => $hasQuestsForPlayer, // 新增的动态标志
                    'has_dialogue' => $hasDialogue
                ]
            ];
            
            // 添加NPC装备信息
            if (isset($npcDetails['inventory']) && !empty($npcDetails['inventory'])) {
                $npcDetailData['inventory'] = $npcDetails['inventory'];
            }
            
            // 发送NPC详情数据
            $this->sendMessage($fd, MessageProtocol::S2C_NPC_DETAIL, $npcDetailData);
            
        } catch (Exception $e) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理查看NPC详情请求失败']);
        }
    }
    
    /**
     * 处理预加载NPC对话请求
     * @param int $fd 客户端连接ID
     * @param array $data 请求数据
     */
    private function handlePreloadDialogue($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        $npcId = $data['npc_id'] ?? null;
        
        if (!$playerId || !$npcId) {
            return; // 静默失败，这只是预加载请求
        }
        
        try {
            // 调用预加载对话方法，只检查对话条件但不执行动作
            $result = $this->npcManager->preloadDialogue($npcId, $playerId);
            
            // 记录日志
            error_log("预加载对话完成，npcId={$npcId}, playerId={$playerId}, 结果: " . json_encode($result));
            
            // 如果预加载成功并找到了适用的对话树，向客户端发送对话可用状态
            if (isset($result['success']) && $result['success'] && isset($result['dialogue_tree_id'])) {
                $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                    'type' => 'dialogue_preload',
                    'npc_id' => $npcId,
                    'has_dialogue' => true
                ]);
            }
        } catch (Exception $e) {
            error_log("预加载NPC对话请求失败: " . $e->getMessage());
        }
    }

         /**
      * 处理丢弃物品的请求
      * @param int $fd WebSocket连接标识符
      * @param array $data 请求数据
      */
     private function handleDropItem($fd, $data) {
         $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
         $inventoryId = $data['inventory_id'] ?? null;
         $quantity = isset($data['quantity']) ? intval($data['quantity']) : 1;
         
         if (!$playerId) {
             return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
         }
         
         if (!$inventoryId) {
             return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的物品ID']);
         }
         
         if ($quantity <= 0) {
             return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '丢弃数量必须大于0']);
         }
         
         try {
             $db = Database::getInstance();
             
             // 获取玩家当前场景
             $stmt = $db->query("SELECT current_scene_id FROM player_attributes WHERE account_id = ?", [$playerId]);
             $playerData = $stmt->fetch();
             $stmt->closeCursor();
             
             if (!$playerData || !$playerData['current_scene_id']) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取当前场景信息']);
             }
             
             $currentSceneId = $playerData['current_scene_id'];
             
             // 检查物品是否在玩家背包中
             $stmt = $db->query("
                SELECT pi.*, it.name, it.category, it.item_id as template_item_id, it.stackable 
                FROM player_inventory pi
                JOIN item_templates it ON pi.item_template_id = it.id
                WHERE pi.id = ? AND pi.player_id = ?", 
                [$inventoryId, $playerId]
             );
             $item = $stmt->fetch();
             $stmt->closeCursor();
             
             if (!$item) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '物品不在背包中']);
             }
             
             // 检查物品是否可丢弃（已装备或者是装备类型物品）
             if ($item['is_equipped'] == 1) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '装备中的物品无法丢弃']);
             }
             
             // 检查物品是否是装备类型
            //  if ($item['category'] == 'Equipment') {
            //      return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '装备类物品无法丢弃']);
            //  }
             
             // 检查丢弃数量是否超过拥有数量
             if ($quantity > $item['quantity']) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '丢弃数量超过拥有数量']);
             }
             
             $remainingQuantity = $item['quantity'] - $quantity;
             $isBound = isset($item['is_bound']) && $item['is_bound'] == 1;
             
             // 处理物品丢弃逻辑
             if ($remainingQuantity > 0) {
                 // 如果还有剩余，更新背包中的数量
                 $stmt = $db->query(
                     "UPDATE player_inventory SET quantity = ? WHERE id = ? AND player_id = ?",
                     [$remainingQuantity, $inventoryId, $playerId]
                 );
                 
                 if ($stmt->rowCount() == 0) {
                     return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '更新物品数量失败']);
                 }
             } else {
                 // 如果全部丢弃，从背包中删除
                 $stmt = $db->query("DELETE FROM player_inventory WHERE id = ? AND player_id = ?", [$inventoryId, $playerId]);
                 if ($stmt->rowCount() == 0) {
                     return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '移除物品失败']);
                 }
             }
             
             // 如果物品已绑定，则直接消失，不添加到场景
             if ($isBound) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ITEM_DROPPED, [
                    'success' => true,
                    'message' => "你丢弃了 {$item['name']}" . ($quantity > 1 ? " (x{$quantity})" : "")
                ]);
             } else {
                 // 未绑定的物品添加到场景地面
                 $stmt = $db->query(
                     "INSERT INTO scene_items (scene_id, item_template_id, quantity, instance_data) VALUES (?, ?, ?, ?)",
                     [$currentSceneId, $item['item_template_id'], $quantity, $item['instance_data']]
                 );
                 
                 if ($stmt->rowCount() == 0) {
                     return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '添加物品到场景失败']);
                 }
             }
             
             $droppedItemId = $db->lastInsertId();
             
             // 发送丢弃成功消息
             $this->sendMessage($fd, MessageProtocol::S2C_ITEM_DROPPED, [
                 'success' => true,
                 'message' => "你丢弃了 {$item['name']}" . ($quantity > 1 ? " (x{$quantity})" : "")
             ]);
             
             // 更新场景中的物品信息
             $this->broadcastToScene($currentSceneId, MessageProtocol::S2C_INFO_MESSAGE, [
                 'message' => "你看到有人丢弃了 {$item['name']}" . ($quantity > 1 ? " (x{$quantity})" : "") . " 在地上!",
                 'type' => 'info'
             ], [$playerId]);
             
             // 刷新场景物品列表 - 使用SceneManager来正确解析display_name
             $sceneItems = $this->sceneManager->getSceneItems($currentSceneId);
             
             // 获取场景名称
             $stmt = $db->query("SELECT name FROM scenes WHERE id = ?", [$currentSceneId]);
             $sceneData = $stmt->fetch();
             $sceneName = $sceneData['name'] ?? '';
             $stmt->closeCursor();
             
             // 发送场景更新到所有玩家
             $this->broadcastToScene($currentSceneId, MessageProtocol::S2C_SCENE_RESET, [
                 'scene' => [
                     'id' => $currentSceneId,
                     'name' => $sceneName, // 添加场景名称
                     'items' => $sceneItems
                 ]
             ]);
             
             // 更新收集任务进度（负数增量，表示物品减少）
             $this->checkCollectQuestProgress($fd, $playerId, $item['item_template_id'], -$quantity);

             // 刷新背包数据
             $this->handleGetInventory($fd, ['player_id' => $playerId]);
         } catch (Exception $e) {
             error_log("物品丢弃错误: " . $e->getMessage());
             $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理丢弃物品请求失败: ' . $e->getMessage()]);
         }
     }
     
     /**
      * 处理绑定物品的请求
      * @param int $fd WebSocket连接标识符
      * @param array $data 请求数据
      */
     private function handleBindItem($fd, $data) {
         $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
         $inventoryId = $data['inventory_id'] ?? null;
         
         if (!$playerId) {
             return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '未登录']);
         }
         
         if (!$inventoryId) {
             return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无效的物品ID']);
         }
         
         try {
             $db = Database::getInstance();
             
             // 检查物品是否在玩家背包中
             $stmt = $db->query("
                 SELECT pi.*, it.name, it.category, it.item_id as template_item_id 
                 FROM player_inventory pi
                 JOIN item_templates it ON pi.item_template_id = it.id
                 WHERE pi.id = ? AND pi.player_id = ?", 
                 [$inventoryId, $playerId]
             );
             $item = $stmt->fetch();
             $stmt->closeCursor();
             
             if (!$item) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '物品不在背包中']);
             }
             
             // 如果物品已经绑定，返回错误
             if ($item['is_bound'] == 1) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '物品已经绑定']);
             }
             
             // 更新物品为绑定状态
             $stmt = $db->query(
                 "UPDATE player_inventory SET is_bound = 1 WHERE id = ? AND player_id = ?",
                 [$inventoryId, $playerId]
             );
             
             if ($stmt->rowCount() == 0) {
                 return $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '绑定物品失败']);
             }
             
             // 发送绑定成功消息
             $this->sendMessage($fd, MessageProtocol::S2C_ITEM_BOUND, [
                 'success' => true,
                 'message' => "{$item['name']} 已成功绑定！绑定后的物品无法交易或掉落。",
                 'item_id' => $inventoryId
             ]);
             
         } catch (Exception $e) {
             error_log("物品绑定错误: " . $e->getMessage());
             $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '处理绑定物品请求失败: ' . $e->getMessage()]);
         }
     }
     
    /**
     * 清理所有可重复任务（日常任务）
     */
    private function cleanupDailyQuests() {
        try {
            echo "开始清理可重复任务（日常任务）...\n";
            
            // 获取数据库连接
            $db = $this->db;
            $conn = $db->getConnection();
            
            // 查询所有已完成的可重复任务记录
            $stmt = $conn->prepare("
                DELETE FROM player_quests
                WHERE status = 'completed'
                AND quest_id IN (
                    SELECT id FROM quests WHERE is_repeatable = 1
                )
            ");
            $stmt->execute();
            $deletedRows = $stmt->rowCount();
            
            echo "清理完成：删除了 {$deletedRows} 条已完成的可重复任务记录。\n";
            
            // 记录到系统日志
            error_log("[系统] 日常任务清理：已删除 {$deletedRows} 条已完成的可重复任务记录");
            
            return true;
        } catch (Exception $e) {
            echo "清理可重复任务时发生错误: " . $e->getMessage() . "\n";
            error_log("[错误] 清理日常任务失败: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 清理超过1小时的场景掉落物品
     * @return int 清理的物品数量
     */
    private function cleanupExpiredSceneItems() {
        try {
            echo "开始清理超时场景掉落物品...\n";

            // 获取数据库连接
            $db = $this->db;
            $conn = $db->getConnection();

            // 查询超过1小时的掉落物品
            $stmt = $conn->prepare("
                SELECT id, scene_id, item_template_id, quantity, dropped_at
                FROM scene_items
                WHERE dropped_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
            ");
            $stmt->execute();
            $expiredItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (empty($expiredItems)) {
                echo "没有需要清理的超时掉落物品。\n";
                return 0;
            }

            $cleanedCount = 0;
            $affectedScenes = []; // 记录受影响的场景

            // 清理Redis中的物品保护信息和数据库中的物品记录
            RedisManager::getInstance()->with(function($redis) use ($conn, $expiredItems, &$cleanedCount, &$affectedScenes) {
                $redisPipe = $redis->pipeline();

                foreach ($expiredItems as $item) {
                    $sceneItemId = $item['id'];
                    $sceneId = $item['scene_id'];

                    // 清理Redis中的保护信息
                    $protectionKey = "scene_item_protection:{$sceneItemId}";
                    $redisPipe->del($protectionKey);

                    // 从数据库中删除物品
                    $deleteStmt = $conn->prepare("DELETE FROM scene_items WHERE id = ?");
                    $deleteStmt->execute([$sceneItemId]);

                    if ($deleteStmt->rowCount() > 0) {
                        $cleanedCount++;
                        $affectedScenes[$sceneId] = true; // 标记场景受影响
                        echo "清理超时掉落物品: ID={$sceneItemId}, 场景={$sceneId}, 物品模板ID={$item['item_template_id']}, 掉落时间={$item['dropped_at']}\n";
                    }
                }

                $redisPipe->exec();
            });

            // 通知受影响场景的玩家场景数据已更新
            foreach (array_keys($affectedScenes) as $sceneId) {
                try {
                    // 获取更新后的场景数据
                    $sceneData = $this->sceneManager->getSceneData($sceneId);
                    if ($sceneData) {
                        // 广播场景重置消息给场景中的所有玩家
                        $this->broadcastToScene($sceneId, MessageProtocol::S2C_SCENE_RESET, [
                            'scene' => $sceneData
                        ]);
                        echo "已通知场景 {$sceneId} 的玩家场景物品已更新\n";
                    }
                } catch (Exception $e) {
                    echo "通知场景 {$sceneId} 更新时发生错误: " . $e->getMessage() . "\n";
                }
            }

            echo "清理完成：删除了 {$cleanedCount} 个超时掉落物品。\n";

            // 记录到系统日志
            error_log("[系统] 场景掉落物品清理：已删除 {$cleanedCount} 个超过1小时的掉落物品");

            return $cleanedCount;
        } catch (Exception $e) {
            echo "清理超时掉落物品时发生错误: " . $e->getMessage() . "\n";
            error_log("[错误] 清理场景掉落物品失败: " . $e->getMessage());
            return 0;
        }
    }

    // ==================== 交易系统处理函数 ====================

    /**
     * 处理交易请求
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeRequest($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $targetPlayerId = $data['target_player_id'] ?? null;
        if (!$targetPlayerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '目标玩家ID无效']);
        }

        // 验证交易条件
        $validation = $this->tradeManager->validateTradeConditions($playerId, $targetPlayerId);
        if (!$validation['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $validation['error']
            ]);
        }

        // 创建交易会话
        $tradeId = $this->tradeManager->createTradeSession($playerId, $targetPlayerId, $validation['scene_id']);
        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => '创建交易会话失败'
            ]);
        }

        // 获取玩家名称
        $playerName = $this->getPlayerName($playerId);

        // 向目标玩家发送交易请求
        $targetFd = $this->playerConnections[$targetPlayerId] ?? null;
        if ($targetFd) {
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_REQUEST_RECEIVED, [
                'trade_id' => $tradeId,
                'initiator_id' => $playerId,
                'initiator_name' => $playerName,
                'scene_id' => $validation['scene_id']
            ]);
        }

        // 向发起者确认请求已发送
        $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
            'message' => '交易请求已发送，等待对方响应...',
            'context' => 'trade_request_sent'
        ]);
    }

    /**
     * 处理交易响应
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeRespond($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        $accepted = $data['accepted'] ?? false;

        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '交易ID无效']);
        }

        $result = $this->tradeManager->respondToTrade($tradeId, $playerId, $accepted);

        if (!$result['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        if ($accepted) {
            // 交易被接受，通知双方开始交易
            $tradeDetails = $this->tradeManager->getTradeDetails($tradeId);
            if ($tradeDetails) {
                $trade = $tradeDetails['trade'];

                // 调试：检查交易开始时的确认状态
                error_log("🚀 交易开始时的状态: " . json_encode([
                    'trade_id' => $tradeId,
                    'initiator_id' => $trade['initiator_id'],
                    'target_id' => $trade['target_id'],
                    'initiator_confirmed' => $trade['initiator_confirmed'],
                    'target_confirmed' => $trade['target_confirmed'],
                    'status' => $trade['status']
                ], JSON_UNESCAPED_UNICODE));

                // 临时修复：强制重置确认状态
                if ($trade['initiator_confirmed'] || $trade['target_confirmed']) {
                    error_log("⚠️ 检测到错误的确认状态，强制重置");
                    $this->db->query(
                        "UPDATE player_trades SET initiator_confirmed = 0, target_confirmed = 0 WHERE id = ?",
                        [$tradeId]
                    );
                    // 重新获取交易数据
                    $trade = $this->tradeManager->getTradeById($tradeId);
                }

                // 通知发起者
                $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
                if ($initiatorFd) {
                    $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_STARTED, [
                        'trade_id' => $tradeId,
                        'partner_id' => $trade['target_id'],
                        'partner_name' => $tradeDetails['player_names'][$trade['target_id']] ?? '未知玩家',
                        'is_initiator' => true  // 明确告诉前端角色
                    ]);
                }

                // 通知目标玩家
                $this->sendMessage($fd, MessageProtocol::S2C_TRADE_STARTED, [
                    'trade_id' => $tradeId,
                    'partner_id' => $trade['initiator_id'],
                    'partner_name' => $tradeDetails['player_names'][$trade['initiator_id']] ?? '未知玩家',
                    'is_initiator' => false  // 明确告诉前端角色
                ]);
            }
        } else {
            // 交易被拒绝，通知发起者
            $trade = $result['trade'] ?? null;
            if ($trade) {
                $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
                if ($initiatorFd) {
                    $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_CANCELLED, [
                        'reason' => $result['reason'] ?? '对方拒绝了交易请求',
                        'trade_id' => $tradeId
                    ]);
                }
            }
        }
    }

    /**
     * 处理添加交易物品
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeAddItem($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        $inventoryId = $data['inventory_id'] ?? null;
        $quantity = $data['quantity'] ?? 1;

        if (!$tradeId || !$inventoryId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '参数无效']);
        }

        $result = $this->tradeManager->addItemToTrade($tradeId, $playerId, $inventoryId, $quantity);

        if (!$result['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        // 广播交易更新给双方
        $this->broadcastTradeUpdate($tradeId);
    }

    /**
     * 处理移除交易物品
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeRemoveItem($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        $tradeItemId = $data['trade_item_id'] ?? null;

        if (!$tradeId || !$tradeItemId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '参数无效']);
        }

        $result = $this->tradeManager->removeItemFromTrade($tradeId, $playerId, $tradeItemId);

        if (!$result['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        // 广播交易更新给双方
        $this->broadcastTradeUpdate($tradeId);
    }

    /**
     * 处理添加交易货币
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeAddCurrency($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        $goldAmount = max(0, intval($data['gold_amount'] ?? 0));
        $diamondAmount = max(0, intval($data['diamond_amount'] ?? 0));

        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '交易ID无效']);
        }

        $result = $this->tradeManager->setTradeCurrency($tradeId, $playerId, $goldAmount, $diamondAmount);

        if (!$result['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        // 广播交易更新给双方
        $this->broadcastTradeUpdate($tradeId);
    }

    /**
     * 处理交易确认
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeConfirm($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '交易ID无效']);
        }

        error_log("🔒 处理交易确认: 玩家ID={$playerId}, 交易ID={$tradeId}");

        $result = $this->tradeManager->confirmTrade($tradeId, $playerId);

        error_log("📊 交易确认结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));

        if (!$result['success']) {
            error_log("❌ 交易确认失败: " . $result['error']);
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        if (isset($result['both_confirmed']) && $result['both_confirmed'] === false) {
            // 单方确认，广播确认状态更新
            error_log("🔄 单方确认，广播确认状态更新");
            $this->broadcastTradeConfirmation($tradeId, $result['trade']);
        } else {
            // 双方都确认，交易完成
            error_log("✅ 双方确认，交易完成");
            $this->broadcastTradeCompleted($tradeId, $result);
        }
    }

    /**
     * 处理最终确认交易
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeFinalConfirm($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '交易ID无效']);
        }

        error_log("🔒 处理最终确认交易: 玩家ID={$playerId}, 交易ID={$tradeId}");

        $result = $this->tradeManager->finalConfirmTrade($tradeId, $playerId);

        error_log("📊 最终确认交易结果: " . json_encode($result, JSON_UNESCAPED_UNICODE));

        if (!$result['success']) {
            error_log("❌ 最终确认交易失败: " . $result['error']);
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        if (isset($result['both_final_confirmed']) && $result['both_final_confirmed'] === false) {
            // 单方最终确认，广播最终确认状态更新
            error_log("🔄 单方最终确认，广播最终确认状态更新");
            $this->broadcastTradeFinalConfirmation($tradeId, $result['trade']);
        } else {
            // 双方都最终确认，交易完成
            error_log("✅ 双方都最终确认，交易完成");
            $this->broadcastTradeCompleted($tradeId, $result);
        }
    }

    /**
     * 广播最终确认状态
     * @param string $tradeId 交易ID
     * @param array $trade 交易信息
     */
    private function broadcastTradeFinalConfirmation($tradeId, $trade) {
        $confirmData = [
            'trade_id' => $tradeId,
            'initiator_final_confirmed' => $trade['initiator_final_confirmed'],
            'target_final_confirmed' => $trade['target_final_confirmed']
        ];

        error_log("📢 广播最终确认状态: " . json_encode([
            'trade_id' => $tradeId,
            'initiator_id' => $trade['initiator_id'],
            'target_id' => $trade['target_id'],
            'initiator_final_confirmed' => $trade['initiator_final_confirmed'],
            'target_final_confirmed' => $trade['target_final_confirmed']
        ], JSON_UNESCAPED_UNICODE));

        // 发送给发起者
        $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
        if ($initiatorFd) {
            error_log("📤 发送最终确认状态给发起者 (玩家ID: {$trade['initiator_id']}, FD: {$initiatorFd})");
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_FINAL_CONFIRMED, $confirmData);
        } else {
            error_log("⚠️ 发起者连接不存在 (玩家ID: {$trade['initiator_id']})");
        }

        // 发送给目标玩家
        $targetFd = $this->playerConnections[$trade['target_id']] ?? null;
        if ($targetFd) {
            error_log("📤 发送最终确认状态给目标玩家 (玩家ID: {$trade['target_id']}, FD: {$targetFd})");
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_FINAL_CONFIRMED, $confirmData);
        } else {
            error_log("⚠️ 目标玩家连接不存在 (玩家ID: {$trade['target_id']})");
        }
    }

    /**
     * 处理取消交易
     * @param int $fd WebSocket连接标识符
     * @param array $data 请求数据
     */
    private function handleTradeCancel($fd, $data) {
        $playerId = $this->fdInfo[$fd]['player_id'] ?? null;
        if (!$playerId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '未登录']);
        }

        $tradeId = $data['trade_id'] ?? null;
        if (!$tradeId) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, ['message' => '交易ID无效']);
        }

        $result = $this->tradeManager->cancelTrade($tradeId, $playerId, '玩家取消了交易');

        if (!$result['success']) {
            return $this->sendMessage($fd, MessageProtocol::S2C_TRADE_ERROR, [
                'message' => $result['error']
            ]);
        }

        // 通知双方交易已取消
        $trade = $result['trade'];
        $reason = $result['reason'];

        $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
        $targetFd = $this->playerConnections[$trade['target_id']] ?? null;

        if ($initiatorFd) {
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_CANCELLED, [
                'reason' => $reason,
                'trade_id' => $tradeId
            ]);
        }

        if ($targetFd) {
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_CANCELLED, [
                'reason' => $reason,
                'trade_id' => $tradeId
            ]);
        }
    }

    /**
     * 广播交易更新
     * @param string $tradeId 交易ID
     */
    private function broadcastTradeUpdate($tradeId) {
        $tradeDetails = $this->tradeManager->getTradeDetails($tradeId);
        if (!$tradeDetails) {
            return;
        }

        $trade = $tradeDetails['trade'];
        $items = $tradeDetails['items'];
        $currencies = $tradeDetails['currencies'];

        // 组织数据
        $updateData = [
            'trade_id' => $tradeId,
            'trade' => $trade,
            'items' => $items,
            'currencies' => $currencies
        ];

        // 发送给发起者
        $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
        if ($initiatorFd) {
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_UPDATE, $updateData);
        }

        // 发送给目标玩家
        $targetFd = $this->playerConnections[$trade['target_id']] ?? null;
        if ($targetFd) {
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_UPDATE, $updateData);
        }
    }

    /**
     * 广播交易确认状态
     * @param string $tradeId 交易ID
     * @param array $trade 交易信息
     */
    private function broadcastTradeConfirmation($tradeId, $trade) {
        $confirmData = [
            'trade_id' => $tradeId,
            'initiator_confirmed' => $trade['initiator_confirmed'],
            'target_confirmed' => $trade['target_confirmed'],
            'stage' => $trade['stage']  // 添加阶段信息
        ];

        error_log("📢 广播交易确认状态: " . json_encode([
            'trade_id' => $tradeId,
            'initiator_id' => $trade['initiator_id'],
            'target_id' => $trade['target_id'],
            'initiator_confirmed' => $trade['initiator_confirmed'],
            'target_confirmed' => $trade['target_confirmed'],
            'stage' => $trade['stage']
        ], JSON_UNESCAPED_UNICODE));

        // 发送给发起者
        $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
        if ($initiatorFd) {
            error_log("📤 发送确认状态给发起者 (玩家ID: {$trade['initiator_id']}, FD: {$initiatorFd})");
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_CONFIRMED, $confirmData);
        } else {
            error_log("⚠️ 发起者连接不存在 (玩家ID: {$trade['initiator_id']})");
        }

        // 发送给目标玩家
        $targetFd = $this->playerConnections[$trade['target_id']] ?? null;
        if ($targetFd) {
            error_log("📤 发送确认状态给目标玩家 (玩家ID: {$trade['target_id']}, FD: {$targetFd})");
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_CONFIRMED, $confirmData);
        } else {
            error_log("⚠️ 目标玩家连接不存在 (玩家ID: {$trade['target_id']})");
        }
    }

    /**
     * 广播交易完成
     * @param string $tradeId 交易ID
     * @param array $result 交易结果
     */
    private function broadcastTradeCompleted($tradeId, $result) {
        $trade = $result['trade'];
        $completedData = [
            'trade_id' => $tradeId,
            'items' => $result['items'] ?? [],
            'currencies' => $result['currencies'] ?? []
        ];

        // 发送给发起者
        $initiatorFd = $this->playerConnections[$trade['initiator_id']] ?? null;
        if ($initiatorFd) {
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_TRADE_COMPLETED, $completedData);
            // 刷新库存和属性
            $this->handleGetInventory($initiatorFd, ['player_id' => $trade['initiator_id']]);
            $this->sendMessage($initiatorFd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($trade['initiator_id'])]);
        }

        // 发送给目标玩家
        $targetFd = $this->playerConnections[$trade['target_id']] ?? null;
        if ($targetFd) {
            $this->sendMessage($targetFd, MessageProtocol::S2C_TRADE_COMPLETED, $completedData);
            // 刷新库存和属性
            $this->handleGetInventory($targetFd, ['player_id' => $trade['target_id']]);
            $this->sendMessage($targetFd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, ['player' => $this->getPlayerWithAttributes($trade['target_id'])]);
        }
    }

    /**
     * 获取玩家名称
     * @param int $playerId 玩家ID
     * @return string
     */
    private function getPlayerName($playerId) {
        try {
            $stmt = $this->db->query("SELECT username FROM accounts WHERE id = ?", [$playerId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            return $result ? $result['username'] : '未知玩家';
        } catch (Exception $e) {
            return '未知玩家';
        }
    }

    /**
     * 处理任务进度更新通知
     * @param string $message Redis消息内容
     */
    private function handleQuestProgressNotification($message) {
        try {
            $data = json_decode($message, true);
            if (!$data || !isset($data['type']) || $data['type'] !== 'quest_progress_update') {
                return;
            }

            $playerId = $data['player_id'];
            $questUpdates = $data['quest_updates'];
            $source = $data['source'] ?? 'unknown';

            error_log("📢 处理任务进度更新通知: 玩家ID={$playerId}, 来源={$source}, 更新任务数=" . count($questUpdates));

            // 获取玩家的WebSocket连接
            $fd = $this->playerConnections[$playerId] ?? null;
            if (!$fd) {
                error_log("⚠️ 玩家 {$playerId} 未在线，跳过任务进度通知");
                return;
            }

            // 发送任务进度更新通知（使用与其他地方相同的格式）
            foreach ($questUpdates as $update) {
                // 发送进度更新消息
                $progressMessage = "【任务进度】{$update['title']}: {$update['description']} ({$update['new_progress']}/{$update['target']})";
                $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $progressMessage]);

                // 如果任务可以完成，提示玩家
                if ($update['can_complete']) {
                    $completeMessage = "【任务完成】任务\"{$update['title']}\"已经可以交付了，请前往{$update['receiver_npc_name']}处交付任务。";
                    $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, ['message' => $completeMessage]);
                }
            }

            // 发送标准的任务更新消息
            $this->sendMessage($fd, MessageProtocol::S2C_QUEST_UPDATE, [
                'action' => 'progress_updated',
                'quests' => $questUpdates
            ]);

            // 更新玩家任务列表
            $this->handleGetQuestList($fd, ['player_id' => $playerId]);

            error_log("✅ 任务进度通知发送完成: 玩家ID={$playerId}");

        } catch (Exception $e) {
            error_log("❌ 处理任务进度通知失败: " . $e->getMessage());
        }
    }

    /**
     * 处理获取属性重修建筑数据
     */
    private function handleGetAttributeResetData($fd, $sceneBuildingId, $buildingName, $playerId, $isRefresh = false) {
        $db = Database::getInstance();

        try {
            // 获取重修配置
            $stmt = $db->query(
                "SELECT arc.required_item_id, arc.required_quantity, it.name as item_name, it.description as item_description
                 FROM attribute_reset_config arc
                 JOIN item_templates it ON arc.required_item_id = it.id
                 WHERE arc.scene_building_id = ?",
                [$sceneBuildingId]
            );
            $config = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$config) {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '该属性重修建筑尚未配置重修物品。']);
                return;
            }

            // 获取玩家当前属性
            $stmt = $db->query(
                "SELECT level, strength, agility, constitution, intelligence, potential_points
                 FROM player_attributes WHERE account_id = ?",
                [$playerId]
            );
            $playerAttrs = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$playerAttrs) {
                $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '无法获取玩家属性信息。']);
                return;
            }

            // 检查玩家是否有足够的重修物品
            $stmt = $db->query(
                "SELECT SUM(quantity) as total_quantity
                 FROM player_inventory
                 WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0",
                [$playerId, $config['required_item_id']]
            );
            $playerItemCount = $stmt->fetchColumn() ?: 0;
            $stmt->closeCursor();

            // 检查玩家是否装备了任何装备
            $stmt = $db->query(
                "SELECT COUNT(*) as equipped_count
                 FROM player_inventory
                 WHERE player_id = ? AND is_equipped = 1",
                [$playerId]
            );
            $equippedCount = $stmt->fetchColumn() ?: 0;
            $stmt->closeCursor();

            // 计算重修后的属性分配
            $totalAttributePoints = ($playerAttrs['level'] - 1) * 5; // 每级5点
            $currentAllocatedPoints = ($playerAttrs['strength'] - 5) +
                                    ($playerAttrs['agility'] - 5) +
                                    ($playerAttrs['constitution'] - 5) +
                                    ($playerAttrs['intelligence'] - 5);

            $this->sendMessage($fd, MessageProtocol::S2C_BUILDING_DATA, [
                'type' => 'ATTRIBUTE_RESET',
                'name' => $buildingName,
                'scene_building_id' => $sceneBuildingId,
                'is_refresh' => $isRefresh,
                'config' => [
                    'required_item_id' => $config['required_item_id'],
                    'required_quantity' => $config['required_quantity'],
                    'item_name' => $config['item_name'],
                    'item_description' => $config['item_description']
                ],
                'player_status' => [
                    'level' => $playerAttrs['level'],
                    'current_attributes' => [
                        'strength' => $playerAttrs['strength'],
                        'agility' => $playerAttrs['agility'],
                        'constitution' => $playerAttrs['constitution'],
                        'intelligence' => $playerAttrs['intelligence']
                    ],
                    'potential_points' => $playerAttrs['potential_points'],
                    'total_attribute_points' => $totalAttributePoints,
                    'current_allocated_points' => $currentAllocatedPoints,
                    'has_required_item' => $playerItemCount >= $config['required_quantity'],
                    'player_item_count' => $playerItemCount,
                    'has_equipped_items' => $equippedCount > 0,
                    'equipped_count' => $equippedCount
                ]
            ]);

        } catch (Exception $e) {
            error_log("获取属性重修数据失败: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '获取属性重修数据失败。']);
        }
    }

    /**
     * 处理属性重修请求
     */
    private function handleAttributeReset($fd, $payload) {
        $playerId = $payload['player_id'] ?? 0;
        $sceneBuildingId = $payload['scene_building_id'] ?? 0;

        if (!$playerId || !$sceneBuildingId) {
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => '缺少必要参数。']);
            return;
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();
        $conn->beginTransaction();

        try {
            // 1. 获取重修配置
            $stmt = $conn->prepare(
                "SELECT required_item_id, required_quantity
                 FROM attribute_reset_config
                 WHERE scene_building_id = ?"
            );
            $stmt->execute([$sceneBuildingId]);
            $config = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$config) {
                throw new Exception('该建筑尚未配置重修物品。');
            }

            // 2. 检查玩家是否装备了任何装备
            $stmt = $conn->prepare(
                "SELECT COUNT(*) FROM player_inventory WHERE player_id = ? AND is_equipped = 1"
            );
            $stmt->execute([$playerId]);
            $equippedCount = $stmt->fetchColumn();
            $stmt->closeCursor();

            if ($equippedCount > 0) {
                throw new Exception('请先卸下所有装备才能进行属性重修。');
            }

            // 3. 检查玩家是否有足够的重修物品
            $stmt = $conn->prepare(
                "SELECT id, quantity FROM player_inventory
                 WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0 AND quantity > 0
                 ORDER BY id LIMIT 1"
            );
            $stmt->execute([$playerId, $config['required_item_id']]);
            $playerItem = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$playerItem || $playerItem['quantity'] < $config['required_quantity']) {
                throw new Exception('您没有足够的重修物品。');
            }

            // 4. 获取玩家当前属性
            $stmt = $conn->prepare(
                "SELECT level, strength, agility, constitution, intelligence, potential_points
                 FROM player_attributes WHERE account_id = ? FOR UPDATE"
            );
            $stmt->execute([$playerId]);
            $playerAttrs = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$playerAttrs) {
                throw new Exception('无法获取玩家属性信息。');
            }

            // 5. 消耗重修物品
            if ($playerItem['quantity'] == $config['required_quantity']) {
                // 删除物品
                $stmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                $stmt->execute([$playerItem['id']]);
            } else {
                // 减少数量
                $stmt = $conn->prepare(
                    "UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?"
                );
                $stmt->execute([$config['required_quantity'], $playerItem['id']]);
            }

            // 6. 重置属性到初始值
            // 计算重修后的潜力点：当前已分配的属性点 + 当前潜力点
            // 初始属性值为5点，所以已分配点数 = 当前属性值 - 5
            $initialAttributeValue = 5; // 1级玩家的初始属性值
            $currentAllocatedPoints = ($playerAttrs['strength'] - $initialAttributeValue) +
                                    ($playerAttrs['agility'] - $initialAttributeValue) +
                                    ($playerAttrs['constitution'] - $initialAttributeValue) +
                                    ($playerAttrs['intelligence'] - $initialAttributeValue);
            $totalPotentialPoints = $currentAllocatedPoints + $playerAttrs['potential_points'];

            $stmt = $conn->prepare(
                "UPDATE player_attributes SET
                 strength = ?,
                 agility = ?,
                 constitution = ?,
                 intelligence = ?,
                 hp = 100,
                 max_hp = 100,
                 mp = 50,
                 max_mp = 50,
                 attack = 10,
                 defense = 5,
                 attack_speed = 1.00,
                 potential_points = ?
                 WHERE account_id = ?"
            );
            $stmt->execute([
                $initialAttributeValue, // strength
                $initialAttributeValue, // agility
                $initialAttributeValue, // constitution
                $initialAttributeValue, // intelligence
                $totalPotentialPoints,
                $playerId
            ]);

            // 7. 直接恢复初始值，不用计算派生。
            // $this->recalculatePlayerAttributes($playerId, $conn);

            $conn->commit();

            // 8. 通知客户端成功
            $this->sendMessage($fd, MessageProtocol::S2C_INFO_MESSAGE, [
                'message' => '属性重修成功！'
            ]);

            // 9. 发送更新后的玩家属性
            $this->sendMessage($fd, MessageProtocol::S2C_PLAYER_ATTRIBUTE_UPDATE, [
                'player' => $this->getPlayerWithAttributes($playerId)
            ]);

            // 10. 刷新背包数据
            $this->handleGetInventory($fd, ['player_id' => $playerId]);

            // 11. 刷新建筑数据
            $this->handleGetBuildingData($fd, [
                'scene_building_id' => $sceneBuildingId,
                'player_id' => $playerId
            ]);

        } catch (Exception $e) {
            $conn->rollBack();
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, ['message' => $e->getMessage()]);
        }
    }

    /**
     * 处理获取装备凝练榜请求
     */
    private function handleGetRefineLeaderboard($fd, $playerId, $payload) {
        try {
            $conn = Database::getInstance()->getConnection();

            // 获取装备凝练榜前10名
            $query = "
                SELECT
                    pi.id as inventory_id,
                    pi.player_id,
                    pi.instance_data,
                    it.name as item_name,
                    ed.slot,
                    a.username
                FROM player_inventory pi
                JOIN item_templates it ON pi.item_template_id = it.id
                JOIN equipment_details ed ON it.id = ed.item_template_id
                JOIN accounts a ON pi.player_id = a.id
                WHERE it.category = 'Equipment'
                AND pi.instance_data LIKE '%\"refined\":true%'
                ORDER BY pi.id
                LIMIT 100
            ";

            $stmt = $conn->prepare($query);
            $stmt->execute();
            $equipmentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // 解析凝练数据并排序
            $refinedEquipment = [];
            foreach ($equipmentData as $equipment) {
                $instanceData = json_decode($equipment['instance_data'], true);
                if (isset($instanceData['refined']) && $instanceData['refined'] && isset($instanceData['refine_value'])) {
                    $equipment['refine_value'] = floatval($instanceData['refine_value']);
                    $equipment['refine_tier'] = $instanceData['refine_tier'] ?? 0;
                    $equipment['refine_prefix'] = $instanceData['refine_prefix'] ?? '';
                    $equipment['display_name'] = $instanceData['display_name'] ?? $equipment['item_name'];
                    $refinedEquipment[] = $equipment;
                }
            }

            // 按凝练值降序排序
            usort($refinedEquipment, function($a, $b) {
                return $b['refine_value'] <=> $a['refine_value'];
            });

            // 取前10名
            $topEquipment = array_slice($refinedEquipment, 0, 10);

            // 发送响应
            $this->sendMessage($fd, MessageProtocol::S2C_REFINE_LEADERBOARD, [
                'success' => true,
                'equipment_list' => $topEquipment
            ]);

        } catch (Exception $e) {
            error_log("获取装备凝练榜错误: " . $e->getMessage());
            $this->sendMessage($fd, MessageProtocol::S2C_ERROR, [
                'message' => '获取神兵榜失败，请稍后重试'
            ]);
        }
    }
}

// 启动服务器
try {
    echo "检查Swoole扩展是否加载...\n";
    if (!extension_loaded('swoole')) {
        throw new Exception("Swoole 扩展未加载，请确保已在 php.ini 中启用。");
    }
    echo "Swoole扩展已加载。\n";

    $server = new SwooleWebSocketServer();
    $server->start();
} catch (Throwable $e) {
    echo "服务器启动失败: " . $e->getMessage() . "\n";
    echo "文件: " . $e->getFile() . " 第 " . $e->getLine() . " 行\n";
    exit(1);
}