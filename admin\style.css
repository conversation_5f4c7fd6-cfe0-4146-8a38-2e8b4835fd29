/* admin/style.css */
html, body.login-page {
    height: 100%;
    margin: 0;
}

body.login-page {
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f0f2f5;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
}

.login-container {
    padding: 40px;
    background-color: #fff;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    width: 100%;
    max-width: 400px;
    text-align: center;
}

.login-container h2 {
    margin-bottom: 24px;
    font-size: 24px;
    color: #333;
}

.login-container .form-group {
    text-align: left;
    margin-bottom: 20px;
}

.login-container .form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
}

.login-container .btn {
    width: 100%;
    padding: 12px;
    font-size: 16px;
    font-weight: 600;
}

.login-container .error-message {
    margin-top: 15px;
    color: #e74c3c;
    background-color: #fdd;
    border: 1px solid #e74c3c;
    padding: 10px;
    border-radius: 4px;
}

.login-container .success-message {
    margin-top: 15px;
    color: #27ae60;
    background-color: #d4edda;
    border: 1px solid #27ae60;
    padding: 10px;
    border-radius: 4px;
}

body {
    background-color: #f4f7f6;
    margin: 0;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
}

/* 新的顶部导航栏样式 */
.top-nav {
    background-color: #fff;
    border-bottom: 1px solid #ddd;
    padding: 0 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
}

.top-nav-logo a {
    font-size: 1.25rem;
    font-weight: 600;
    color: #333;
    text-decoration: none;
}

.top-nav-links ul {
    margin: 0;
    padding: 0;
    list-style: none;
    display: flex;
}

.top-nav-links li {
    margin: 0 15px;
}

.top-nav-links a {
    color: #555;
    text-decoration: none;
    font-size: 1rem;
    padding: 20px 0;
    border-bottom: 2px solid transparent;
    transition: color 0.2s, border-bottom-color 0.2s;
}

.top-nav-links a:hover,
.top-nav-links li.active a {
    color: #007bff;
    border-bottom-color: #007bff;
}

.top-nav-user a {
    color: #555;
    text-decoration: none;
}

/* 主内容区域调整 */
.main-content {
    padding: 20px;
    margin-top: 60px; /* 为固定的顶部导航栏留出空间 */
}

/* 移除旧的侧边栏相关样式 */
header, main, .content {
    all: unset; /* 重置所有继承的样式 */
    display: block; /* 确保它们仍然是块级元素 */
}

.container {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

header h1 {
    margin: 0;
    font-size: 24px;
    color: #2c3e50;
}

header nav a {
    color: #3498db;
    text-decoration: none;
    margin-left: 20px;
}

header nav a:hover {
    text-decoration: underline;
}

.sidebar h2 {
    font-size: 18px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-top: 0;
}

.sidebar ul {
    list-style-type: none;
    padding: 0;
    margin: 0;
}

.sidebar ul li a {
    display: block;
    padding: 10px 15px;
    color: #34495e;
    text-decoration: none;
    border-radius: 4px;
    margin-bottom: 5px;
}

.sidebar ul li a:hover, .sidebar ul li.active a {
    background-color: #ecf0f1;
    color: #2980b9;
}

.sidebar button {
    display: block;
    width: 100%;
    padding: 10px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    margin-top: 20px;
    font-size: 16px;
}

.sidebar button:hover {
    background-color: #2980b9;
}

.content h2 {
    margin-top: 0;
    font-size: 22px;
    color: #2c3e50;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input[type="text"],
.form-group input[type="number"],
.form-group input[type="password"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
    font-size: 14px;
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
}

button {
    padding: 10px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    margin-right: 10px;
}

button[type="submit"] {
    background-color: #2ecc71;
    color: white;
}

button[type="submit"]:hover {
    background-color: #27ae60;
}

button[type="button"] {
    background-color: #e74c3c;
    color: white;
}

button[type="button"]:hover {
    background-color: #c0392b;
}

#status-message {
    margin-top: 20px;
    padding: 10px;
    border-radius: 4px;
    display: none; /* Hidden by default */
}

#status-message.success {
    background-color: #dff0d8;
    color: #3c763d;
    border: 1px solid #d6e9c6;
    display: block;
}

#status-message.error {
    background-color: #f2dede;
    color: #a94442;
    border: 1px solid #ebccd1;
    display: block;
}

hr {
    border: none;
    border-top: 1px solid #eee;
    margin: 25px 0;
}

.page-content h1 {
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    margin-top: 0;
}

.card-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.card {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.card h3 {
    margin-top: 0;
}

.btn {
    display: inline-block;
    background-color: #007bff;
    color: white;
    padding: 10px 15px;
    border-radius: 4px;
    text-decoration: none;
    margin-top: 10px;
    border: none;
    cursor: pointer;
}
.btn:hover {
    background-color: #0056b3;
}
.btn-primary {
     background-color: #007bff;
}
.btn-primary:hover{
     background-color: #0069d9;
}
.btn-secondary {
    background-color: #6c757d;
}
.btn-secondary:hover {
    background-color: #5a6268;
}
.btn-danger {
    background-color: #dc3545;
}
.btn-danger:hover {
    background-color: #c82333;
}
.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

/* Stats Styling */
.stats-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
    text-align: center;
}

.stat-card h4 {
    margin: 0 0 10px 0;
    color: #6c757d;
    font-size: 1rem;
}

.stat-card p {
    margin: 0;
    font-size: 2rem;
    font-weight: bold;
    color: #343a40;
}

/* Dashboard Grid Layout */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 20px;
    margin: 30px 0;
}

.dashboard-section {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
}

.dashboard-section h3 {
    margin: 0 0 15px 0;
    color: #343a40;
    font-size: 1.2rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

/* Mini Stats */
.mini-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
    gap: 15px;
}

.mini-stat {
    text-align: center;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 6px;
}

.mini-stat-label {
    display: block;
    font-size: 0.8rem;
    color: #6c757d;
    margin-bottom: 5px;
}

.mini-stat-value {
    display: block;
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
}

/* Level Distribution */
.level-distribution {
    space-y: 10px;
}

.level-bar {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
}

.level-label {
    width: 80px;
    font-size: 0.9rem;
    color: #495057;
}

.level-progress {
    flex: 1;
    height: 20px;
    background-color: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.level-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    transition: width 0.3s ease;
}

.level-count {
    width: 40px;
    text-align: right;
    font-weight: bold;
    color: #495057;
}

.level-summary {
    margin-top: 15px;
    padding-top: 15px;
    border-top: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    font-size: 0.9rem;
    color: #6c757d;
}

/* Job Distribution */
.job-distribution {
    space-y: 8px;
}

.job-item {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    gap: 10px;
}

.job-name {
    width: 80px;
    font-size: 0.9rem;
    color: #495057;
    font-weight: 500;
}

.job-progress {
    flex: 1;
    height: 16px;
    background-color: #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.job-fill {
    height: 100%;
    background: linear-gradient(90deg, #007bff, #6610f2);
    transition: width 0.3s ease;
}

.job-count {
    width: 30px;
    text-align: right;
    font-weight: bold;
    color: #495057;
    font-size: 0.9rem;
}

/* Activity Chart */
.activity-chart {
    display: flex;
    align-items: end;
    gap: 8px;
    height: 120px;
    padding: 10px 0;
}

.activity-day {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.activity-bar {
    width: 100%;
    height: 80px;
    background-color: #e9ecef;
    border-radius: 4px 4px 0 0;
    position: relative;
    overflow: hidden;
}

.activity-fill {
    position: absolute;
    bottom: 0;
    width: 100%;
    background: linear-gradient(180deg, #17a2b8, #007bff);
    transition: height 0.3s ease;
    border-radius: 4px 4px 0 0;
}

.activity-date {
    font-size: 0.75rem;
    color: #6c757d;
}

.activity-count {
    font-size: 0.8rem;
    font-weight: bold;
    color: #495057;
}

.no-data {
    text-align: center;
    color: #6c757d;
    font-style: italic;
    padding: 40px 0;
}

/* Alert Styles */
.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

/* Quick Actions */
.quick-actions {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.08);
    margin: 20px 0;
}

.quick-actions h3 {
    margin: 0 0 15px 0;
    color: #343a40;
    font-size: 1.2rem;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 8px;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 15px;
}

.quick-action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 10px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    text-decoration: none;
    color: #495057;
    transition: all 0.3s ease;
}

.quick-action-btn:hover {
    background-color: #e9ecef;
    border-color: #007bff;
    color: #007bff;
    text-decoration: none;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 8px;
}

.quick-action-text {
    font-size: 0.9rem;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* Dashboard Responsive Design */
@media (max-width: 768px) {
    .stats-container {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }

    .stat-card {
        padding: 15px;
    }

    .stat-card p {
        font-size: 1.5rem;
    }

    .dashboard-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .mini-stats {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .level-bar, .job-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .level-label, .job-name {
        width: auto;
        text-align: center;
    }

    .level-count, .job-count {
        width: auto;
        text-align: center;
    }

    .activity-chart {
        height: 100px;
    }

    .activity-bar {
        height: 60px;
    }

    .card-container {
        grid-template-columns: 1fr;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 10px;
    }

    .quick-action-btn {
        padding: 12px 8px;
    }

    .quick-action-icon {
        font-size: 1.5rem;
        margin-bottom: 5px;
    }

    .quick-action-text {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .stats-container {
        grid-template-columns: 1fr;
    }

    .mini-stats {
        grid-template-columns: 1fr;
    }

    .level-summary {
        flex-direction: column;
        gap: 5px;
        text-align: center;
    }

    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Toast Notification */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #333;
    color: #fff;
    padding: 12px 20px;
    border-radius: 4px;
    font-size: 14px;
    font-weight: bold;
    max-width: 300px;
    min-width: 200px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 1060;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    /* 文本处理 */
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.toast.show {
    transform: translateX(0);
    opacity: 1;
}
.toast.success {
    background-color: #28a745;
}
.toast.error {
    background-color: #dc3545;
}
.toast.warning {
    background-color: #ffc107;
    color: #212529;
}

/* Toast 移动端适配 */
@media (max-width: 768px) {
    .toast {
        right: 10px;
        left: 10px;
        max-width: none;
        min-width: auto;
        transform: translateY(-100px);
    }

    .toast.show {
        transform: translateY(0);
    }
}

/* 重复的toast样式已合并到上面的主要定义中 */

/* Table Styles */
.table-container {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    padding: 20px;
}
.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}
.data-table {
    width: 100%;
    border-collapse: collapse;
}
.data-table th, .data-table td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    text-align: left;
}
.data-table th {
    background-color: #f8f9fa;
    font-weight: 600;
}
.data-table tbody tr:hover {
    background-color: #f5f5f5;
}

/* Modal Styles */
.modal {
    display: none; 
    position: fixed; 
    z-index: 1050; 
    left: 0; 
    top: 0;
    width: 100%; 
    height: 100%; 
    overflow: auto; 
    background-color: rgba(0,0,0,0.4);
}
.modal-content {
    background-color: #fefefe; 
    margin: 5% auto; 
    padding: 25px;
    border: 1px solid #888; 
    width: 80%; 
    max-width: 700px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.close-button { 
    color: #aaa; 
    float: right; 
    font-size: 28px; 
    font-weight: bold; 
    cursor: pointer; 
}
.close-button:hover, .close-button:focus { 
    color: black; 
    text-decoration: none; 
    cursor: pointer; 
}
#item-form .form-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}
#item-form .form-group {
    display: flex;
    flex-direction: column;
}
#item-form .full-width {
    grid-column: 1 / -1;
}
.effects-builder {
    display: flex; 
    gap: 10px; 
    margin-bottom: 5px;
}
.effects-builder select { 
    flex-grow: 1; 
}
.effects-builder input { 
    width: 80px; 
}
.form-actions {
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #eee;
    text-align: right;
}
.form-actions button {
    margin-left: 10px;
}

/* Scenes Page Specifics */
.action-buttons-container { 
    display: flex; 
    gap: 10px; 
}
#table-filters-container { 
    margin-bottom: 15px; 
    display: flex; 
    flex-wrap: wrap; 
    gap: 8px; 
}
#table-filters-container .filter-btn { 
    padding: 6px 12px; 
    border: 1px solid #ccc; 
    background-color: #f8f9fa; 
    cursor: pointer; 
    border-radius: 4px; 
    font-size: 14px;
}
#table-filters-container .filter-btn.active { 
    background-color: #007bff; 
    color: white; 
    border-color: #007bff; 
}
.coord-group {
    display: flex;
    gap: 10px;
}

/* Map Editor Styles */
.modal-full .modal-content-full { 
    width: 95%; 
    height: 90vh; 
    max-width: none; 
    margin: 2.5vh auto; 
    display: flex; 
    flex-direction: column; 
}
.map-editor-header { 
    padding-bottom: 15px; 
    border-bottom: 1px solid #ccc; 
}
.map-editor-body { 
    flex-grow: 1; 
    overflow: hidden; 
    display: flex; 
    flex-direction: column; 
    padding-top: 10px;
}
.map-editor-body > p {
    margin-top: 0;
    flex-shrink: 0;
}
.layer-selector {
    margin-top: 10px;
}
.layer-selector .btn { 
    margin-right: 5px; 
    margin-bottom: 5px; 
}
#map-grid-wrapper { 
    flex-grow: 1; 
    overflow: auto; 
    padding: 10px; 
    background-color: #f0f0f0; 
    border: 1px solid #ccc; 
    border-radius: 4px; 
}
#map-grid { 
    display: inline-block; 
    white-space: nowrap; 
    background-color: #e0e0e0; 
    position: relative; 
    user-select: none; 
}
.map-row { 
    display: flex; 
}
.map-cell { 
    flex-shrink: 0; 
    width: 140px; 
    height: 90px; 
    border: 1px solid #c5c5c5; 
    display: inline-block; 
    vertical-align: top; 
    padding: 5px; 
    font-size: 11px; 
    overflow: hidden; 
    text-overflow: ellipsis; 
    position: relative; 
    cursor: pointer; 
    transition: background-color 0.2s, border-color 0.2s;
}
.map-cell.exists { background-color: #e8f5e9; }
.map-cell.exists:hover { background-color: #d0edd4; border-color: #007bff; }
.map-cell.empty { background-color: #f5f5f5; }
.map-cell.empty:hover { background-color: #e9e9e9; }
.map-cell .coords { 
    position: absolute; 
    bottom: 2px; 
    right: 4px; 
    color: #999; 
    font-size: 10px; 
}
.map-cell .scene-name { 
    font-weight: bold; 
    color: #333; 
    font-size: 12px; 
    margin-bottom: 3px; 
}
.map-cell .scene-id { color: #666; }
.map-cell .monsters-info { 
    font-size: 10px; 
    color: #c43a3a; 
    position: absolute; 
    bottom: 18px; 
    left: 5px; 
    right: 5px; 
    white-space: nowrap; 
    overflow: hidden; 
    text-overflow: ellipsis; 
}
.map-cell.selected { 
    position: relative; 
}
.map-cell.selected::after { 
    content: ''; 
    position: absolute; 
    top: 0; 
    left: 0; 
    width: 100%; 
    height: 100%; 
    background-color: rgba(0, 123, 255, 0.3); 
    border: 2px solid #007bff; 
    box-sizing: border-box; 
    pointer-events: none;
}

/* Floating Batch Actions Box */
#batch-actions-floating { 
    position: fixed; 
    bottom: 30px; 
    right: 30px; 
    background-color: #fff; 
    border-radius: 8px; 
    box-shadow: 0 4px 12px rgba(0,0,0,0.15); 
    padding: 15px; 
    z-index: 1060; 
    display: none; 
    flex-direction: column; 
    width: 220px;
}
#batch-actions-floating .batch-close-btn { 
    position: absolute; 
    top: 5px; 
    right: 8px; 
    background: none; 
    border: none; 
    font-size: 24px; 
    cursor: pointer; 
    color: #6c757d; 
}
#batch-actions-floating .batch-selection-info { 
    font-size: 13px; 
    color: #6c757d; 
    margin-bottom: 10px; 
    text-align: center; 
    border-bottom: 1px solid #eee; 
    padding-bottom: 8px; 
}
#batch-actions-floating .btn { 
    width: 100%;
    margin-top: 5px; 
    box-sizing: border-box;
}

/* Monster Selector Buttons */
.monster-btn { 
    background-color: #f0f0f0; 
    border: 1px solid #ccc; 
    border-radius: 4px; 
    padding: 2px 6px; 
    margin-right: 4px; 
    margin-bottom: 4px; 
    cursor: pointer; 
    font-size: 12px; 
    display: inline-block; 
}
.monster-btn.selected { 
    background-color: #007bff; 
    color: white; 
    border-color: #007bff; 
}

/* Monster Page Specific Layout */
.page-content-grid {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    height: 100%;
}

.sidebar-secondary {
    background-color: #fff;
    padding: 20px;
    border-radius: 5px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    height: fit-content;
}

.sidebar-secondary h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.sidebar-secondary ul {
    list-style: none;
    padding: 0;
    margin: 0 0 20px 0;
    max-height: 400px;
    overflow-y: auto;
}

.sidebar-secondary li a {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #337ab7;
    border-radius: 4px;
}

.sidebar-secondary li.active a,
.sidebar-secondary li a:hover {
    background-color: #f5f5f5;
    font-weight: bold;
}

.sidebar-secondary .btn {
    width: 100%;
}

.main-content-area.card {
    padding: 20px;
}

.form-container h2 {
    margin-top: 0;
    font-size: 20px;
}

.list-group-item.active {
    z-index: 2;
    color: #ffffff; /* 白色文字 */
    background-color: #4a90e2; /* 更鲜明的蓝色背景 */
    border-color: #4a90e2;
}

main {
    /* ... existing code ... */
} 

/* 修复凝练系统下拉菜单样式 */
.top-nav-links .dropdown {
    position: relative;
    display: inline-block;
}

.top-nav-links .dropdown-toggle {
    cursor: pointer;
    padding-right: 15px;
}

.top-nav-links .dropdown-toggle:after {
    content: '▼';
    font-size: 9px;
    margin-left: 5px;
    vertical-align: middle;
}

.top-nav-links .dropdown-menu {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 160px;
    background-color: #fff;
    box-shadow: 0 5px 10px rgba(0,0,0,0.2);
    z-index: 1000;
    border-radius: 4px;
    margin-top: 2px;
    padding: 5px 0;
    border: 1px solid rgba(0,0,0,0.15);
}

.top-nav-links .dropdown:hover .dropdown-menu {
    display: block;
}

.top-nav-links .dropdown-menu li {
    display: block;
    width: 100%;
    padding: 0;
    margin: 0;
    float: none;
}

.top-nav-links .dropdown-menu li a {
    display: block;
    padding: 8px 15px;
    color: #333;
    text-decoration: none;
    white-space: nowrap;
    clear: both;
    font-weight: normal;
    line-height: 1.42857143;
}

.top-nav-links .dropdown-menu li a:hover {
    background-color: #f5f5f5;
    color: #262626;
    text-decoration: none;
}

.top-nav-links .dropdown-menu li.active a {
    background-color: #e0f2f1;
    color: #00796b;
    font-weight: bold;
}

/* 确保下拉菜单内容清晰可见 */
.top-nav-links .dropdown-menu:before,
.top-nav-links .dropdown-menu:after {
    content: '';
    display: block;
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

.top-nav-links .dropdown-menu:before {
    top: -7px;
    left: 9px;
    border-width: 0 7px 7px;
    border-color: transparent transparent rgba(0,0,0,0.15);
}

.top-nav-links .dropdown-menu:after {
    top: -6px;
    left: 10px;
    border-width: 0 6px 6px;
    border-color: transparent transparent #fff;
}

/* 凝练系统中的RV滑块样式 */
#rv-slider {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
}
.rv-tier {
    position: absolute;
    height: 80px;
    top: 10px;
    border-right: 3px solid #ccc;
    border-left: 3px solid #ccc;
    background-color: rgba(0, 123, 255, 0.1);
    border-radius: 4px;
    transition: background-color 0.2s;
    cursor: move;
}
.rv-tier:hover {
    background-color: rgba(0, 123, 255, 0.2);
}
.rv-tier-label {
    position: absolute;
    top: -25px;
    width: 100%;
    text-align: center;
    font-weight: bold;
    color: #495057;
}
.rv-tier-value {
    position: absolute;
    bottom: -25px;
    width: 100%;
    text-align: center;
    font-size: 0.8em;
    color: #6c757d;
}
.ui-resizable-handle {
    width: 10px;
    background-color: #6c757d;
    opacity: 0.5;
}
.ui-resizable-handle:hover {
    opacity: 1;
} 