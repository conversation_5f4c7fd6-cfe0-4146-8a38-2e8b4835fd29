<?php
$pageTitle = "凝练元素管理";
$currentPage = "refine_elements";

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

include 'layout_header.php';

// 获取未设置元素的材料物品，排除碎片
$items_query = "SELECT it.id, it.item_id, it.name, it.category 
                FROM item_templates it
                LEFT JOIN material_elements me ON it.id = me.item_template_id
                WHERE it.category = 'Material' AND me.id IS NULL AND it.name NOT LIKE '%碎片%'
                ORDER BY it.name";
$items_stmt = $db->prepare($items_query);
$items_stmt->execute();
$items = $items_stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取已设置元素的物品
$elements_query = "SELECT me.id, me.item_template_id, me.element, me.tier, it.name as item_name 
                   FROM material_elements me 
                   JOIN item_templates it ON me.item_template_id = it.id 
                   ORDER BY it.name";
$elements_stmt = $db->prepare($elements_query);
$elements_stmt->execute();
$elements = $elements_stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['add_element'])) {
        $item_id = $_POST['item_id'];
        $element = $_POST['element'];
        $tier = $_POST['tier'];
        
        // 检查是否已存在
        $check_query = "SELECT id FROM material_elements WHERE item_template_id = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$item_id]);
        
        if ($check_stmt->rowCount() > 0) {
            // 更新
            $update_query = "UPDATE material_elements SET element = ?, tier = ? WHERE item_template_id = ?";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([$element, $tier, $item_id]);
            echo "<div class='alert alert-success'>物品元素信息已更新</div>";
        } else {
            // 插入
            $insert_query = "INSERT INTO material_elements (item_template_id, element, tier) VALUES (?, ?, ?)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([$item_id, $element, $tier]);
            echo "<div class='alert alert-success'>物品元素信息已添加</div>";
        }
        
        // 刷新页面数据
        header("Location: refine_elements.php");
        exit;
    }
    
    if (isset($_POST['delete_element']) && isset($_POST['element_id'])) {
        $element_id = $_POST['element_id'];
        $delete_query = "DELETE FROM material_elements WHERE id = ?";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->execute([$element_id]);
        
        echo "<div class='alert alert-success'>物品元素信息已删除</div>";
        header("Location: refine_elements.php");
        exit;
    }
}
?>

<div class="container-fluid">
    <p>设置物品的五行元素属性和等级，用于装备凝练系统。</p>
    
    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-plus mr-1"></i>
                    添加/编辑物品元素
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="item_id">选择物品:</label>
                            <select class="form-control" id="item_id" name="item_id" required>
                                <option value="">-- 请选择物品 --</option>
                                <?php foreach ($items as $item): ?>
                                <option value="<?= $item['id'] ?>"><?= htmlspecialchars($item['name']) ?> (<?= $item['category'] ?>)</option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="element">元素属性:</label>
                            <select class="form-control" id="element" name="element" required>
                                <option value="gold">金</option>
                                <option value="wood">木</option>
                                <option value="water">水</option>
                                <option value="fire">火</option>
                                <option value="earth">土</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="tier">材料等级 (1-5):</label>
                            <select class="form-control" id="tier" name="tier" required>
                                <option value="1">1级</option>
                                <option value="2">2级</option>
                                <option value="3">3级</option>
                                <option value="4">4级</option>
                                <option value="5">5级</option>
                            </select>
                        </div>
                        
                        <button type="submit" name="add_element" class="btn btn-primary">保存</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table mr-1"></i>
                    已设置元素的物品
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table id="dataTable" class="display" style="width:100%">
                            <thead>
                                <tr>
                                    <th>物品名称</th>
                                    <th>元素</th>
                                    <th>等级</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($elements as $element): ?>
                                <tr>
                                    <td><?= htmlspecialchars($element['item_name']) ?></td>
                                    <td>
                                        <?php 
                                        switch($element['element']) {
                                            case 'gold': echo '金'; break;
                                            case 'wood': echo '木'; break;
                                            case 'water': echo '水'; break;
                                            case 'fire': echo '火'; break;
                                            case 'earth': echo '土'; break;
                                        }
                                        ?>
                                    </td>
                                    <td><?= $element['tier'] ?>级</td>
                                    <td>
                                        <form method="post" action="" style="display:inline">
                                            <input type="hidden" name="element_id" value="<?= $element['id'] ?>">
                                            <button type="submit" name="delete_element" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个元素设置吗？')">删除</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#dataTable').DataTable({
        "language": {
            "sProcessing": "处理中...",
            "sLengthMenu": "显示 _MENU_ 项结果",
            "sZeroRecords": "没有匹配结果",
            "sInfo": "显示第 _START_ 至 _END_ 项结果，共 _TOTAL_ 项",
            "sInfoEmpty": "显示第 0 至 0 项结果，共 0 项",
            "sInfoFiltered": "(由 _MAX_ 项结果过滤)",
            "sInfoPostFix": "",
            "sSearch": "搜索:",
            "sUrl": "",
            "sEmptyTable": "表中数据为空",
            "sLoadingRecords": "载入中...",
            "sInfoThousands": ",",
            "oPaginate": {
                "sFirst": "首页",
                "sPrevious": "上页",
                "sNext": "下页",
                "sLast": "末页"
            },
            "oAria": {
                "sSortAscending": ": 以升序排列此列",
                "sSortDescending": ": 以降序排列此列"
            }
        }
    });
});
</script>

<?php include 'layout_footer.php'; ?> 