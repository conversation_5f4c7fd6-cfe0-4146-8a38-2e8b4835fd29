<?php
/**
 * JavaScript文件批量混淆脚本
 * 执行此脚本将混淆所有配置的JS文件
 */

session_start();
// 设置当前页面标识
$currentPage = 'updatejs';

// 页面标题和描述
$pageTitle = 'JS文件混淆';

require_once '../admin/auth.php';
// require_once '../admin/layout_header.php';

//用户验证
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权的访问']);
    exit;
}


// 处理调试模式切换
if (isset($_POST['toggle_debug'])) {
    $configFile = '../config.ini';
    $currentDebugMode = false;

    // 读取当前配置
    if (file_exists($configFile)) {
        $config = parse_ini_file($configFile, true);
        $currentDebugMode = isset($config['debug']['debug_mode']) ? $config['debug']['debug_mode'] : false;
    }

    // 切换调试模式
    $newDebugMode = !$currentDebugMode;
    $debugValue = $newDebugMode ? 'true' : 'false';

    // 更新配置文件
    $configContent = "[debug]\ndebug_mode = $debugValue\n";
    file_put_contents($configFile, $configContent);

    echo json_encode(['success' => true, 'debug_mode' => $newDebugMode]);
    exit;
}



// 获取当前调试模式状态
function getCurrentDebugMode() {
    $configFile = '../config.ini';
    $config = parse_ini_file($configFile, true);
    return $config['debug']['debug_mode'];
}

class JSObfuscator {
    private $sourceFiles;
    private $obfuscatorPath;
    private $outputDir;

    public function __construct() {
        // 源文件映射
        $this->sourceFiles = [
            'a' => 'anti-debug-gentle.js',
            'b' => 'building-manager.js',
            'c' => 'crypto-js.min.js',
            'd' => 'MessageProtocol.js',
            'e' => 'UnencryptedMessageProtocol.js',
            'f' => 'SecureMessageProtocol.js',
            'g' => 'gameclient.js',
            'h' => 'html.js',
            'i' => 'skill-manager.js',
            'j' => 'npc-manager.js',
            'k' => 'quest-manager.js',
            'l' => 'pvp-manager.js',
            'u' => 'debug.js'
        ];

        // 混淆器路径
        $this->obfuscatorPath = '/www/server/nodejs/v18.20.4/bin/javascript-obfuscator';

        // 输出目录
        $this->outputDir = __DIR__ . '/';

        // 创建输出目录
        if (!is_dir($this->outputDir)) {
            mkdir($this->outputDir, 0755, true);
        }
    }

    /**
     * 混淆单个文件
     */
    private function obfuscateFile($inputFile) {
        $inputPath = __DIR__ . '/' . $inputFile;
        $outputFile = $this->outputDir . pathinfo($inputFile, PATHINFO_FILENAME) . '_of.js';

        // 检查输入文件是否存在
        if (!file_exists($inputPath)) {
            throw new Exception("输入文件不存在: $inputPath");
        }

        // 构建混淆命令
        $command = sprintf(
            '"%s" "%s" --output "%s" --control-flow-flattening true --dead-code-injection true --string-array-encoding base64 2>&1',
            $this->obfuscatorPath,
            $inputPath,
            $outputFile
        );

        echo "正在混淆: $inputFile<br>";
        echo "命令: $command<br>";

        // 执行混淆
        $output = shell_exec($command);

        // 检查结果
        if (file_exists($outputFile)) {
            $originalSize = filesize($inputPath);

            // 读取混淆后的内容并进行中文转义
            $obfuscatedContent = file_get_contents($outputFile);
            $escapedContent = $this->escapeChinese($obfuscatedContent);

            // 将转义后的内容写回文件
            file_put_contents($outputFile, $escapedContent);

            $finalSize = filesize($outputFile);

            echo "✅ 混淆成功!<br>";
            echo "   原始大小: " . number_format($originalSize) . " 字节<br>";
            echo "   混淆后大小: " . number_format($finalSize) . " 字节<br>";
            echo "   已应用中文转义<br>";
            echo "   输出文件: $outputFile<br>";

            return $outputFile;
        } else {
            echo "❌ 混淆失败!<br>";
            echo "   错误输出: $output<br>";
            return null;
        }
    }

    /**
     * 转义JS内容中的中文字符
     */
    private function escapeChinese($content) {
        // 将中文字符转换为Unicode转义序列
        return preg_replace_callback('/[\x{4e00}-\x{9fff}]/u', function($matches) {
            return '\u' . str_pad(dechex(mb_ord($matches[0], 'UTF-8')), 4, '0', STR_PAD_LEFT);
        }, $content);
    }

    /**
     * 获取字符的Unicode码点
     */
    private function mb_ord($char, $encoding = 'UTF-8') {
        if (function_exists('mb_ord')) {
            return mb_ord($char, $encoding);
        }

        // 兼容旧版本PHP
        $bytes = mb_convert_encoding($char, 'UTF-32BE', $encoding);
        return unpack('N', $bytes)[1];
    }

    /**
     * 显示文件选择界面
     */
    public function showFileSelection() {
        if (!isset($_POST['selected_files'])) {
            // 显示选择界面
            echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>JavaScript文件混淆器</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .file-list { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 20px; }
        .file-item { display: flex; align-items: center; padding: 10px; border-bottom: 1px solid #eee; }
        .file-item:last-child { border-bottom: none; }
        .file-item input[type="checkbox"] { margin-right: 10px; }
        .file-code { font-weight: bold; color: #007bff; width: 30px; }
        .file-name { flex: 1; margin-left: 10px; }
        .file-status { font-size: 0.9em; color: #666; }
        .controls { margin: 20px 0; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
        .btn-primary { background: #007bff; color: white; }
        .btn-secondary { background: #6c757d; color: white; }
        .btn-success { background: #28a745; color: white; }
        .btn:hover { opacity: 0.9; }
        .select-all { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 JavaScript文件混淆器</h1>
        <p>选择需要混淆的JavaScript文件，支持多选和全选</p>
        <div style="margin-top: 15px;">
            <button type="button" id="debugToggle" class="btn btn-secondary">
                🐛 调试模式: <span id="debugStatus">' . (getCurrentDebugMode() ? '开启' : '关闭') . '</span>
            </button>
            <button type="button" class="btn btn-secondary" onclick="window.location.href = \'../admin/index.php\'">返回管理首页</button>
        </div>
    </div>

    <form method="POST">
        <div class="file-list">
            <div class="select-all">
                <label>
                    <input type="checkbox" id="selectAll">
                    <strong>全选/取消全选</strong>
                </label>
            </div>
            <hr>';

            foreach ($this->sourceFiles as $code => $filename) {
                $filePath = __DIR__ . '/' . $filename;
                $exists = file_exists($filePath);
                $size = $exists ? filesize($filePath) : 0;
                $status = $exists ? '✅ 存在 (' . number_format($size) . ' 字节)' : '❌ 不存在';

                echo '<div class="file-item">
                    <input type="checkbox" name="selected_files[]" value="' . $code . '" id="file_' . $code . '"' . ($exists ? '' : ' disabled') . '>
                    <span class="file-code">[' . $code . ']</span>
                    <label for="file_' . $code . '" class="file-name">' . $filename . '</label>
                    <span class="file-status">' . $status . '</span>
                </div>';
            }

            echo '</div>

        <div class="controls">
            <button type="submit" class="btn btn-success">🚀 开始混淆选中的文件</button>
            <button type="button" class="btn btn-secondary" onclick="window.location.reload()">🔄 刷新页面</button>
        </div>
    </form>

    <script>
        // 全选/取消全选功能
        document.getElementById("selectAll").addEventListener("change", function() {
            const checkboxes = document.querySelectorAll("input[name=\"selected_files[]\"]");
            checkboxes.forEach(checkbox => {
                if (!checkbox.disabled) {
                    checkbox.checked = this.checked;
                }
            });
        });

        // 检查是否有文件被选中
        document.querySelector("form").addEventListener("submit", function(e) {
            const selected = document.querySelectorAll("input[name=\"selected_files[]\"]:checked");
            if (selected.length === 0) {
                alert("请至少选择一个文件进行混淆！");
                e.preventDefault();
            }
        });

        // 调试模式切换功能
        document.getElementById("debugToggle").addEventListener("click", function() {
            const button = this;
            // const status = document.getElementById("debugStatus");

            button.disabled = true;
            button.textContent = "切换中...";

            fetch("", {
                method: "POST",
                headers: {
                    "Content-Type": "application/x-www-form-urlencoded",
                },
                body: "toggle_debug=1"
            })
            .then(response => response.json())
            .then(data => {
                try {
                    if (data.success) {
                        const isEnabled = data.debug_mode;
                        button.textContent = "🐛 调试模式: " + (isEnabled ? "开启" : "关闭");
                        // status.textContent = isEnabled ? "🟢 调试模式已开启" : "🔴 调试模式已关闭";
                        // alert("调试模式已" + (isEnabled ? "开启" : "关闭"));
                    }
                } catch (error) {
                    console.error("Error:", error);
                    alert("切换失败，请重试2");
                }
            })
            .catch(error => {
                console.error("Error:", error);
                alert("切换失败，请重试1");
            })
            .finally(() => {
                button.disabled = false;
            });
        });
        
    </script>
</body>
</html>';
            return false;
        }

        return true;
    }

    /**
     * 混淆选中的文件
     */
    public function obfuscateSelected($selectedFiles) {
        echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>混淆处理结果</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 1000px; margin: 0 auto; padding: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .result-item { background: #fff; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 15px; }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .processing { border-left: 4px solid #ffc107; }
        .summary { background: #e9ecef; padding: 15px; border-radius: 8px; margin-top: 20px; }
        .btn { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; }
        .btn-primary { background: #007bff; color: white; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔧 混淆处理结果</h1>
        <p>正在处理选中的 ' . count($selectedFiles) . ' 个文件...</p>
    </div>';

        $successCount = 0;
        $failCount = 0;
        $results = [];

        foreach ($selectedFiles as $code) {
            if (!isset($this->sourceFiles[$code])) {
                continue;
            }

            $filename = $this->sourceFiles[$code];

            echo '<div class="result-item processing">
                <h3>🔄 处理文件: [' . $code . '] ' . $filename . '</h3>';

            try {
                $outputFile = $this->obfuscateFile($filename);
                if ($outputFile) {
                    echo '<p>✅ <strong>混淆成功!</strong></p>';
                    echo '<p>输出文件: <code>' . basename($outputFile) . '</code></p>';
                    $results[$code] = ['status' => 'success', 'output' => $outputFile];
                    $successCount++;
                } else {
                    throw new Exception('混淆失败，未生成输出文件');
                }
            } catch (Exception $e) {
                echo '<p>❌ <strong>混淆失败:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';
                $results[$code] = ['status' => 'failed', 'error' => $e->getMessage()];
                $failCount++;
            }

            echo '</div>';
            flush(); // 实时输出结果
        }

        // 显示总结
        echo '<div class="summary">
            <h3>📊 处理完成统计</h3>
            <p>✅ 成功: <strong>' . $successCount . '</strong> 个文件</p>
            <p>❌ 失败: <strong>' . $failCount . '</strong> 个文件</p>
            <p>📁 输出目录: <code>' . $this->outputDir . '</code></p>
        </div>

        <div style="margin-top: 20px;">
            <a href="?" class="btn btn-primary">🔙 返回文件选择</a>
        </div>

    </body>
    </html>';

        return $results;
    }

    /**
     * 清理输出目录
     */
    public function cleanOutput() {
        if (is_dir($this->outputDir)) {
            $files = glob($this->outputDir . '*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>清理完成</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center; }
        .success { background: #d4edda; color: #155724; padding: 20px; border-radius: 8px; border: 1px solid #c3e6cb; }
        .btn { padding: 10px 20px; margin: 10px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; background: #007bff; color: white; }
    </style>
</head>
<body>
    <div class="success">
        <h2>🗑️ 清理完成</h2>
        <p>输出目录已清理完毕</p>
        <a href="?" class="btn">返回首页</a>
    </div>
</body>
</html>';
        } else {
            echo '输出目录不存在';
        }
    }
}

// 主执行逻辑
try {
    $obfuscator = new JSObfuscator();

    // 检查是否需要清理输出目录
    if (isset($_GET['clean'])) {
        $obfuscator->cleanOutput();
        header('Location: ?');
        exit;
    }

    // 显示文件选择界面或处理选中的文件
    if (!$obfuscator->showFileSelection()) {
        // 显示了选择界面，脚本结束
        exit;
    }

    // 处理选中的文件
    if (isset($_POST['selected_files']) && is_array($_POST['selected_files'])) {
        $selectedFiles = $_POST['selected_files'];
        $obfuscator->obfuscateSelected($selectedFiles);
    } else {
        // 没有选中任何文件，重定向到选择界面
        header('Location: ?');
        exit;
    }

} catch (Exception $e) {
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>错误</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .error { background: #f8d7da; color: #721c24; padding: 20px; border-radius: 8px; border: 1px solid #f5c6cb; }
    </style>
</head>
<body>
    <div class="error">
        <h2>💥 发生错误</h2>
        <p>' . htmlspecialchars($e->getMessage()) . '</p>
        <a href="?" style="color: #007bff;">返回首页</a>
    </div>
</body>
</html>';
    exit(1);
}
?>