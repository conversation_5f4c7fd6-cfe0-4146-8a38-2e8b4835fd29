<?php
session_start();

// 设置会话超时时间（30分钟 = 1800秒）
$session_timeout = 1800;

// 检查会话是否超时
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    if (isset($_SESSION['last_activity'])) {
        $inactive_time = time() - $_SESSION['last_activity'];
        if ($inactive_time > $session_timeout) {
            // 会话超时，清除会话并重定向
            session_destroy();
            error_log('管理员会话超时 - 用户: ' . ($_SESSION['admin_username'] ?? 'unknown'));

            // 检查是否是AJAX请求
            $is_ajax = (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ||
                       (isset($_SERVER['HTTP_ACCEPT']) && strpos(strtolower($_SERVER['HTTP_ACCEPT']), 'application/json') !== false);

            if ($is_ajax) {
                header('Content-Type: application/json');
                http_response_code(401);
                echo json_encode(['success' => false, 'message' => '会话已超时，请重新登录。', 'redirect' => 'index.php']);
                exit;
            } else {
                header('Location: /admin/index.php?message=' . urlencode('会话已超时，请重新登录'));
                exit;
            }
        }
    }

    // 更新最后活动时间
    $_SESSION['last_activity'] = time();
}

// 检查用户是否已登录，否则重定向到登录页面
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // 检查是否是AJAX请求
    // 现代的fetch API可能不发送 'X-Requested-With'，因此我们检查 'Accept' 头是否包含 'application/json'
    $is_ajax = (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') ||
               (isset($_SERVER['HTTP_ACCEPT']) && strpos(strtolower($_SERVER['HTTP_ACCEPT']), 'application/json') !== false);

    if ($is_ajax) {
        // 对于AJAX请求，返回一个JSON错误
        header('Content-Type: application/json');
        http_response_code(401); // Unauthorized
        echo json_encode(['success' => false, 'message' => '会话已过期或未登录，请重新登录。', 'redirect' => 'index.php']);
    } else {
        // 对于普通页面请求，执行重定向
        header('Location: /admin/index.php');
    }
    exit;
} 

$adminUsername = $_SESSION['admin_username'] ?? '管理员'; 