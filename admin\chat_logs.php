<?php
// admin/chat_logs.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: index.php');
    exit;
}

require_once '../config/Database.php';

$pageTitle = '聊天记录管理';
$currentPage = 'chat_logs';

// 处理搜索参数
$search_type = $_GET['type'] ?? 'all'; // all, public, private
$search_keyword = trim($_GET['keyword'] ?? '');
$search_player = trim($_GET['player'] ?? '');
$search_date_from = $_GET['date_from'] ?? '';
$search_date_to = $_GET['date_to'] ?? '';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 50;
$offset = ($page - 1) * $limit;

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 构建查询条件
    $where_conditions = [];
    $params = [];
    
    // 日期范围过滤
    if (!empty($search_date_from)) {
        $where_conditions[] = "sent_at >= ?";
        $params[] = $search_date_from . ' 00:00:00';
    }
    if (!empty($search_date_to)) {
        $where_conditions[] = "sent_at <= ?";
        $params[] = $search_date_to . ' 23:59:59';
    }
    
    // 玩家名称过滤
    if (!empty($search_player)) {
        if ($search_type === 'public') {
            $where_conditions[] = "player_name LIKE ?";
            $params[] = '%' . $search_player . '%';
        } else if ($search_type === 'private') {
            $where_conditions[] = "(sender_name LIKE ? OR receiver_name LIKE ?)";
            $params[] = '%' . $search_player . '%';
            $params[] = '%' . $search_player . '%';
        } else {
            // 全部类型时需要在UNION查询中分别处理
        }
    }
    
    // 关键词过滤
    if (!empty($search_keyword)) {
        $where_conditions[] = "message LIKE ?";
        $params[] = '%' . $search_keyword . '%';
    }
    
    $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';
    
    // 根据类型构建查询
    if ($search_type === 'public') {
        // 只查询公聊
        $sql = "SELECT 'public' as chat_type, id, player_id as sender_id, player_name as sender_name, 
                       NULL as receiver_id, NULL as receiver_name, message, sent_at, filtered
                FROM public_chat_messages 
                $where_clause
                ORDER BY sent_at DESC 
                LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $count_sql = "SELECT COUNT(*) FROM public_chat_messages $where_clause";
        
    } else if ($search_type === 'private') {
        // 只查询私聊
        $sql = "SELECT 'private' as chat_type, id, sender_id, sender_name, receiver_id, receiver_name, 
                       message, sent_at, filtered
                FROM private_chat_messages 
                $where_clause
                ORDER BY sent_at DESC 
                LIMIT ? OFFSET ?";
        $params[] = $limit;
        $params[] = $offset;
        
        $count_sql = "SELECT COUNT(*) FROM private_chat_messages $where_clause";
        
    } else {
        // 查询全部（公聊+私聊）
        $public_where = $where_clause;
        $private_where = $where_clause;
        $public_params = $params;
        $private_params = $params;
        
        // 处理玩家名称过滤
        if (!empty($search_player)) {
            $public_where = str_replace($where_clause, '', $public_where);
            $private_where = str_replace($where_clause, '', $private_where);
            
            $public_conditions = $where_conditions;
            $private_conditions = $where_conditions;
            
            // 移除玩家名称条件，重新添加
            $public_conditions = array_filter($public_conditions, function($cond) {
                return !str_contains($cond, 'player_name') && !str_contains($cond, 'sender_name');
            });
            $private_conditions = array_filter($private_conditions, function($cond) {
                return !str_contains($cond, 'player_name') && !str_contains($cond, 'sender_name');
            });
            
            $public_conditions[] = "player_name LIKE ?";
            $private_conditions[] = "(sender_name LIKE ? OR receiver_name LIKE ?)";
            
            $public_where = !empty($public_conditions) ? 'WHERE ' . implode(' AND ', $public_conditions) : '';
            $private_where = !empty($private_conditions) ? 'WHERE ' . implode(' AND ', $private_conditions) : '';
            
            // 重新构建参数
            $public_params = array_slice($params, 0, -1); // 移除最后的玩家名称参数
            $private_params = array_slice($params, 0, -1);
            
            $public_params[] = '%' . $search_player . '%';
            $private_params[] = '%' . $search_player . '%';
            $private_params[] = '%' . $search_player . '%';
        }
        
        $sql = "(SELECT 'public' as chat_type, id, player_id as sender_id, player_name as sender_name, 
                        NULL as receiver_id, NULL as receiver_name, message, sent_at, filtered
                 FROM public_chat_messages $public_where)
                UNION ALL
                (SELECT 'private' as chat_type, id, sender_id, sender_name, receiver_id, receiver_name, 
                        message, sent_at, filtered
                 FROM private_chat_messages $private_where)
                ORDER BY sent_at DESC 
                LIMIT ? OFFSET ?";
        
        $all_params = array_merge($public_params, $private_params);
        $all_params[] = $limit;
        $all_params[] = $offset;
        $params = $all_params;
        
        $count_sql = "(SELECT COUNT(*) FROM public_chat_messages $public_where) + 
                      (SELECT COUNT(*) FROM private_chat_messages $private_where)";
    }
    
    // 执行查询
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $chat_logs = $stmt->fetchAll();
    
    // 获取总数
    if ($search_type === 'all') {
        $count_params = array_merge($public_params ?? [], $private_params ?? []);
        $count_stmt = $pdo->prepare("SELECT ($count_sql) as total");
        $count_stmt->execute($count_params);
    } else {
        $count_params = array_slice($params, 0, -2); // 移除limit和offset参数
        $count_stmt = $pdo->prepare($count_sql);
        $count_stmt->execute($count_params);
    }
    $total_count = $count_stmt->fetchColumn();
    $total_pages = ceil($total_count / $limit);

    // 获取统计信息
    $stats = [];

    // 今日消息统计
    $today = date('Y-m-d');
    $stats_sql = "
        SELECT
            (SELECT COUNT(*) FROM public_chat_messages WHERE DATE(sent_at) = ?) as today_public,
            (SELECT COUNT(*) FROM private_chat_messages WHERE DATE(sent_at) = ?) as today_private,
            (SELECT COUNT(*) FROM public_chat_messages WHERE filtered = 1 AND DATE(sent_at) = ?) as today_filtered_public,
            (SELECT COUNT(*) FROM private_chat_messages WHERE filtered = 1 AND DATE(sent_at) = ?) as today_filtered_private
    ";
    $stats_stmt = $pdo->prepare($stats_sql);
    $stats_stmt->execute([$today, $today, $today, $today]);
    $daily_stats = $stats_stmt->fetch();

    $stats['today_total'] = $daily_stats['today_public'] + $daily_stats['today_private'];
    $stats['today_public'] = $daily_stats['today_public'];
    $stats['today_private'] = $daily_stats['today_private'];
    $stats['today_filtered'] = $daily_stats['today_filtered_public'] + $daily_stats['today_filtered_private'];
    
} catch (Exception $e) {
    $chat_logs = [];
    $total_count = 0;
    $total_pages = 0;
    $error_message = '获取聊天记录失败: ' . $e->getMessage();
}

require_once 'layout_header.php';
?>

<div class="main-content-area card">
    <div class="card-header">
        <h2>聊天记录管理</h2>
        <div class="header-description">
            <small>查看和管理玩家的公聊和私聊记录，支持关键词搜索和时间范围过滤</small>
        </div>
    </div>
    
    <!-- 搜索过滤器 -->
    <div class="search-filters" style="padding: 20px; border-bottom: 1px solid #dee2e6;">
        <form method="GET" class="filter-form">
            <div class="row">
                <div class="col-md-2">
                    <label for="type">聊天类型</label>
                    <select name="type" id="type" class="form-control">
                        <option value="all" <?php echo $search_type === 'all' ? 'selected' : ''; ?>>全部</option>
                        <option value="public" <?php echo $search_type === 'public' ? 'selected' : ''; ?>>公聊</option>
                        <option value="private" <?php echo $search_type === 'private' ? 'selected' : ''; ?>>私聊</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <label for="keyword">关键词</label>
                    <input type="text" name="keyword" id="keyword" class="form-control" 
                           placeholder="搜索消息内容" value="<?php echo htmlspecialchars($search_keyword); ?>">
                </div>
                <div class="col-md-2">
                    <label for="player">玩家名称</label>
                    <input type="text" name="player" id="player" class="form-control" 
                           placeholder="玩家名称" value="<?php echo htmlspecialchars($search_player); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_from">开始日期</label>
                    <input type="date" name="date_from" id="date_from" class="form-control" 
                           value="<?php echo htmlspecialchars($search_date_from); ?>">
                </div>
                <div class="col-md-2">
                    <label for="date_to">结束日期</label>
                    <input type="date" name="date_to" id="date_to" class="form-control" 
                           value="<?php echo htmlspecialchars($search_date_to); ?>">
                </div>
                <div class="col-md-2">
                    <label>&nbsp;</label>
                    <div>
                        <button type="submit" class="btn btn-primary">搜索</button>
                        <a href="chat_logs.php" class="btn btn-secondary">重置</a>
                    </div>
                </div>
            </div>

            <!-- 快速时间范围选择 -->
            <div class="quick-time-range" style="margin-top: 15px; padding-top: 15px; border-top: 1px solid #dee2e6;">
                <label style="margin-right: 10px; font-size: 13px; color: #6c757d;">快速选择:</label>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setTimeRange(1)">今天</button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setTimeRange(7)">最近7天</button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setTimeRange(30)">最近30天</button>
                <button type="button" class="btn btn-sm btn-outline-secondary" onclick="setTimeRange(90)">最近90天</button>
            </div>
        </form>
    </div>

    <!-- 高级搜索提示 -->
    <div class="search-tips" style="padding: 10px 20px; background-color: #e9ecef; font-size: 12px; color: #6c757d;">
        <strong>搜索提示:</strong>
        关键词搜索支持模糊匹配 |
        玩家名称在私聊模式下会同时搜索发送者和接收者 |
        日期范围为闭区间（包含开始和结束日期）
    </div>
            </div>
        </form>
    </div>
    
    <!-- 统计信息 -->
    <div class="stats-section" style="padding: 15px 20px; background-color: #f8f9fa; border-bottom: 1px solid #dee2e6;">
        <div class="stats-row" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
            <div class="search-stats">
                <span>共找到 <strong><?php echo number_format($total_count); ?></strong> 条聊天记录</span>
                <span style="margin-left: 20px;">当前显示第 <strong><?php echo $page; ?></strong> 页，共 <strong><?php echo $total_pages; ?></strong> 页</span>
            </div>
            <div class="export-actions">
                <button onclick="exportChatLogs()" class="btn btn-sm btn-success">导出当前结果</button>
            </div>
        </div>
        <div class="daily-stats" style="display: flex; gap: 20px; font-size: 13px; color: #6c757d;">
            <span>今日消息: <strong><?php echo number_format($stats['today_total']); ?></strong></span>
            <span>公聊: <strong><?php echo number_format($stats['today_public']); ?></strong></span>
            <span>私聊: <strong><?php echo number_format($stats['today_private']); ?></strong></span>
            <span>被过滤: <strong><?php echo number_format($stats['today_filtered']); ?></strong></span>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger" style="margin: 20px;">
            <?php echo htmlspecialchars($error_message); ?>
        </div>
    <?php endif; ?>
    
    <!-- 聊天记录列表 -->
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th style="width: 80px;">类型</th>
                    <th style="width: 120px;">发送者</th>
                    <th style="width: 120px;">接收者</th>
                    <th>消息内容</th>
                    <th style="width: 80px;">状态</th>
                    <th style="width: 140px;">发送时间</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($chat_logs)): ?>
                <tr>
                    <td colspan="6" style="text-align: center; padding: 40px; color: #6c757d;">
                        暂无聊天记录
                    </td>
                </tr>
                <?php else: ?>
                    <?php foreach ($chat_logs as $log): ?>
                    <tr>
                        <td>
                            <span class="badge <?php echo $log['chat_type'] === 'public' ? 'badge-primary' : 'badge-info'; ?>">
                                <?php echo $log['chat_type'] === 'public' ? '公聊' : '私聊'; ?>
                            </span>
                        </td>
                        <td>
                            <div class="player-info">
                                <strong><?php echo htmlspecialchars($log['sender_name']); ?></strong>
                                <small class="text-muted d-block">ID: <?php echo htmlspecialchars($log['sender_id']); ?></small>
                            </div>
                        </td>
                        <td>
                            <?php if ($log['chat_type'] === 'private'): ?>
                                <div class="player-info">
                                    <strong><?php echo htmlspecialchars($log['receiver_name']); ?></strong>
                                    <small class="text-muted d-block">ID: <?php echo htmlspecialchars($log['receiver_id']); ?></small>
                                </div>
                            <?php else: ?>
                                <span class="text-muted">-</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <div class="message-content"
                                 onclick="showMessageDetail('<?php echo htmlspecialchars($log['message'], ENT_QUOTES); ?>', '<?php echo $log['chat_type']; ?>', '<?php echo htmlspecialchars($log['sender_name'], ENT_QUOTES); ?>', '<?php echo $log['sent_at']; ?>')"
                                 style="cursor: pointer;"
                                 title="点击查看完整消息">
                                <?php
                                $message = htmlspecialchars($log['message']);
                                if (mb_strlen($message) > 50) {
                                    echo mb_substr($message, 0, 50) . '...';
                                } else {
                                    echo $message;
                                }
                                ?>
                            </div>
                        </td>
                        <td>
                            <?php if ($log['filtered']): ?>
                                <span class="badge badge-warning">已过滤</span>
                            <?php else: ?>
                                <span class="badge badge-success">正常</span>
                            <?php endif; ?>
                        </td>
                        <td>
                            <small><?php echo date('Y-m-d H:i:s', strtotime($log['sent_at'])); ?></small>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
    
    <!-- 分页 -->
    <?php if ($total_pages > 1): ?>
    <div class="pagination" style="padding: 20px; text-align: center;">
        <?php
        $query_params = $_GET;
        
        // 上一页
        if ($page > 1):
            $query_params['page'] = $page - 1;
            $prev_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $prev_url; ?>" class="btn btn-secondary">上一页</a>
        <?php endif; ?>
        
        <?php
        // 页码显示逻辑
        $start_page = max(1, $page - 5);
        $end_page = min($total_pages, $page + 5);
        
        for ($i = $start_page; $i <= $end_page; $i++):
            $query_params['page'] = $i;
            $page_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $page_url; ?>" 
               class="btn <?php echo $i === $page ? 'btn-primary' : 'btn-outline-secondary'; ?>"
               style="margin: 0 2px;">
                <?php echo $i; ?>
            </a>
        <?php endfor; ?>
        
        <?php
        // 下一页
        if ($page < $total_pages):
            $query_params['page'] = $page + 1;
            $next_url = '?' . http_build_query($query_params);
        ?>
            <a href="<?php echo $next_url; ?>" class="btn btn-secondary">下一页</a>
        <?php endif; ?>
    </div>
    <?php endif; ?>
</div>

<!-- 消息详情模态框 -->
<div id="messageModal" class="modal-overlay" style="display: none;">
    <div class="modal-content" style="max-width: 600px;">
        <div class="modal-header">
            <h3>消息详情</h3>
            <button class="modal-close" onclick="hideMessageDetail()">&times;</button>
        </div>
        <div class="modal-body" style="padding: 20px;">
            <div class="message-info" style="margin-bottom: 15px; padding: 10px; background-color: #f8f9fa; border-radius: 4px;">
                <div><strong>类型:</strong> <span id="modalChatType"></span></div>
                <div><strong>发送者:</strong> <span id="modalSenderName"></span></div>
                <div><strong>时间:</strong> <span id="modalSentTime"></span></div>
            </div>
            <div class="message-text" style="padding: 15px; border: 1px solid #dee2e6; border-radius: 4px; background-color: white; word-wrap: break-word; line-height: 1.5;">
                <div id="modalMessageContent"></div>
            </div>
        </div>
        <div class="modal-footer" style="padding: 15px 20px; text-align: right; border-top: 1px solid #dee2e6;">
            <button class="btn btn-secondary" onclick="hideMessageDetail()">关闭</button>
        </div>
    </div>
</div>

<style>
.filter-form .row {
    display: flex;
    gap: 15px;
    align-items: end;
}

.filter-form .col-md-2 {
    flex: 1;
    min-width: 150px;
}

.filter-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
}

.badge-primary {
    background-color: #007bff;
    color: white;
}

.badge-info {
    background-color: #17a2b8;
    color: white;
}

.badge-warning {
    background-color: #ffc107;
    color: #212529;
}

.badge-success {
    background-color: #28a745;
    color: white;
}

.player-info strong {
    color: #495057;
}

.message-content {
    max-width: 300px;
    word-wrap: break-word;
    line-height: 1.4;
}

.stats-bar {
    font-size: 14px;
    color: #495057;
}

.alert {
    padding: 12px 20px;
    border-radius: 4px;
    border: 1px solid transparent;
}

.alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.text-muted {
    color: #6c757d !important;
}

.d-block {
    display: block !important;
}

.btn-sm {
    padding: 4px 8px;
    font-size: 12px;
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-outline-secondary {
    color: #6c757d;
    border-color: #6c757d;
    background-color: transparent;
}

.btn-outline-secondary:hover {
    color: white;
    background-color: #6c757d;
    border-color: #6c757d;
}

.export-actions {
    display: flex;
    gap: 10px;
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-close:hover {
    color: #495057;
}

.message-content:hover {
    background-color: #f8f9fa;
}
</style>

<script>
function exportChatLogs() {
    // 获取当前搜索参数
    const urlParams = new URLSearchParams(window.location.search);
    urlParams.set('export', '1');

    // 创建导出链接
    const exportUrl = 'chat_logs_export.php?' + urlParams.toString();

    // 打开新窗口下载
    window.open(exportUrl, '_blank');
}

// 自动设置今天的日期作为默认结束日期
document.addEventListener('DOMContentLoaded', function() {
    const dateToInput = document.getElementById('date_to');
    const dateFromInput = document.getElementById('date_from');

    if (!dateToInput.value && !dateFromInput.value) {
        // 如果没有设置日期，默认显示最近7天的记录
        const today = new Date();
        const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

        dateToInput.value = today.toISOString().split('T')[0];
        dateFromInput.value = weekAgo.toISOString().split('T')[0];
    }
});

// 快速时间范围选择
function setTimeRange(days) {
    const today = new Date();
    const startDate = new Date(today.getTime() - days * 24 * 60 * 60 * 1000);

    document.getElementById('date_from').value = startDate.toISOString().split('T')[0];
    document.getElementById('date_to').value = today.toISOString().split('T')[0];
}

// 显示消息详情
function showMessageDetail(message, chatType, senderName, sentTime) {
    document.getElementById('modalChatType').textContent = chatType === 'public' ? '公聊' : '私聊';
    document.getElementById('modalSenderName').textContent = senderName;
    document.getElementById('modalSentTime').textContent = sentTime;
    document.getElementById('modalMessageContent').textContent = message;

    document.getElementById('messageModal').style.display = 'flex';
}

// 隐藏消息详情
function hideMessageDetail() {
    document.getElementById('messageModal').style.display = 'none';
}

// 点击模态框外部关闭
document.addEventListener('click', function(event) {
    const modal = document.getElementById('messageModal');
    if (event.target === modal) {
        hideMessageDetail();
    }
});

// ESC键关闭模态框
document.addEventListener('keydown', function(event) {
    if (event.key === 'Escape') {
        hideMessageDetail();
    }
});
</script>

<?php require_once 'layout_footer.php'; ?>
