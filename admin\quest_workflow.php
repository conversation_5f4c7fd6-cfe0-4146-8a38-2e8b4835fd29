<?php
// admin/quest_workflow.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 页面标题和当前页面标识
$pageTitle = '任务流程可视化';
$currentPage = 'quest_workflow';

// 获取物品、任务和玩家属性数据，用于脚本编辑器
$all_items = [];
$all_quests = [];
$player_attributes = [];
$attributeNames = [];

try {
    $pdo = Database::getInstance()->getConnection();

    // 获取所有物品，用于物品选择器
    $all_items = $pdo->query("SELECT id, name, category FROM item_templates ORDER BY category, name")->fetchAll(PDO::FETCH_ASSOC);

    // 获取所有任务，用于任务状态条件
    $all_quests = $pdo->query("SELECT id, title FROM quests ORDER BY title")->fetchAll(PDO::FETCH_ASSOC);

    // 获取玩家属性表的所有字段 - 完全参考对话树编辑页面
    $columns_query = "SHOW COLUMNS FROM player_attributes";
    $columns_stmt = $pdo->prepare($columns_query);
    $columns_stmt->execute();
    $table_columns_info = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);

    $allowed_types = ['int', 'decimal', 'bigint', 'float', 'double'];
    $excluded_columns = [
        'id', 'account_id', 'native_job_id', 'current_job_id', 'level',
        'experience', 'experience_to_next_level', 'potential_points',
        'knowledge_points', 'gold', 'diamonds', 'combat_hp_potion_id',
        'combat_mp_potion_id', 'hp', 'mp', 'karma', 'current_scene_id', 'job'
    ];

    foreach ($table_columns_info as $column) {
        $is_allowed = false;
        foreach ($allowed_types as $type) {
            if (strpos(strtolower($column['Type']), $type) !== false) {
                $is_allowed = true;
                break;
            }
        }

        if ($is_allowed && !in_array($column['Field'], $excluded_columns)) {
            $player_attributes[] = $column['Field'];
        }
    }

    // 属性中文名对照 - 完全参考对话树编辑页面
    $attributeNames = [
        'attack' => '攻击力', 'defense' => '防御力', 'max_hp' => '最大生命值', 'max_mp' => '最大法力值',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智力',
        'fire_damage' => '火系伤害', 'fire_resistance' => '火系抗性', 'ice_damage' => '冰系伤害', 'ice_resistance' => '冰系抗性',
        'wind_damage' => '风系伤害', 'wind_resistance' => '风系抗性', 'electric_damage' => '电系伤害', 'electric_resistance' => '电系抗性',
        'attack_speed' => '攻击速度', 'rage' => '怒气', 'dodge_bonus' => '闪避加成'
    ];

} catch (Exception $e) {
    error_log("获取脚本编辑器数据失败: " . $e->getMessage());
    $all_items = [];
    $all_quests = [];
    $player_attributes = [];
    $attributeNames = [];
}

// 添加额外的CSS和JS
$extra_css = '
<style>
    .workflow-container {
        display: flex;
        min-height: calc(100vh - 120px);
        gap: 10px;
    }

    .sidebar {
        width: 320px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border: 1px solid #dee2e6;
        border-radius: 12px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        display: flex;
        flex-direction: column;
        max-height: calc(100vh - 140px);
    }

    .sidebar.collapsed {
        width: 60px;
    }

    .sidebar-header {
        background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
        color: white;
        padding: 15px 20px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 12px 12px 0 0;
        position: relative;
    }

    .sidebar-title {
        font-size: 16px;
        font-weight: 600;
        margin: 0;
        transition: opacity 0.3s ease;
    }

    .sidebar.collapsed .sidebar-title {
        opacity: 0;
        pointer-events: none;
    }

    .sidebar-toggle {
        background: rgba(255,255,255,0.2);
        border: none;
        border-radius: 8px;
        width: 32px;
        height: 32px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
    }

    .sidebar-toggle:hover {
        background: rgba(255,255,255,0.3);
        transform: scale(1.1);
    }

    .sidebar-toggle-icon {
        width: 16px;
        height: 16px;
        fill: white;
        transition: transform 0.3s ease;
    }

    .sidebar.collapsed .sidebar-toggle-icon {
        transform: rotate(180deg);
    }

    .sidebar-content {
        padding: 20px;
        transition: all 0.3s ease;
        flex: 1;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .sidebar.collapsed .sidebar-content {
        opacity: 0;
        pointer-events: none;
        padding: 0;
        overflow: hidden;
    }

    /* 自定义滚动条样式 */
    .sidebar-content::-webkit-scrollbar {
        width: 6px;
    }

    .sidebar-content::-webkit-scrollbar-track {
        background: transparent;
    }

    .sidebar-content::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.2);
        border-radius: 3px;
    }

    .sidebar-content::-webkit-scrollbar-thumb:hover {
        background: rgba(0,0,0,0.3);
    }

    .main-view {
        flex: 1;
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        position: relative;
        overflow: hidden;
    }

    .view-tabs {
        display: flex;
        background: #f8f9fa;
        border-bottom: 1px solid #ddd;
    }

    .view-tab {
        padding: 10px 20px;
        cursor: pointer;
        border-right: 1px solid #ddd;
        background: #f8f9fa;
        transition: background-color 0.2s;
    }

    .view-tab:hover {
        background: #e9ecef;
    }

    .view-tab.active {
        background: white;
        border-bottom: 1px solid white;
        margin-bottom: -1px;
    }

    .view-content {
        height: calc(100% - 50px);
        padding: 20px;
        overflow: hidden;
    }

    .view-panel {
        display: none;
        height: 100%;
        overflow: auto;
    }

    .view-panel.active {
        display: block;
    }

    /* 地图视图样式 */
    .map-container {
        position: relative;
        width: 100%;
        height: 600px; /* 设置固定高度，确保可以滚动 */
        background: #f0f8ff;
        border-radius: 4px;
        overflow: auto;
        cursor: grab;
        user-select: none; /* 防止拖拽时选择文本 */
    }

    .map-container:active {
        cursor: grabbing;
    }

    .map-container.dragging {
        cursor: grabbing !important;
    }

    .map-viewport {
        position: relative;
        min-width: 100%;
        min-height: 100%;
        transform-origin: 0 0;
        transition: transform 0.2s ease;
    }

    .map-controls {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 100;
        display: flex;
        flex-direction: column;
        gap: 5px;
        background: rgba(255,255,255,0.9);
        padding: 8px;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .map-control-btn {
        width: 32px;
        height: 32px;
        border: none;
        border-radius: 4px;
        background: #007bff;
        color: white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16px;
        font-weight: bold;
        transition: all 0.2s ease;
    }

    .map-control-btn:hover {
        background: #0056b3;
        transform: scale(1.1);
    }

    .map-control-btn:disabled {
        background: #ccc;
        cursor: not-allowed;
        transform: none;
    }

    .zoom-level {
        font-size: 12px;
        color: #666;
        text-align: center;
        margin: 2px 0;
    }

    .scene-node {
        position: absolute;
        width: 120px;
        height: 80px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        color: white;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        transition: all 0.3s ease;
        font-size: 12px;
        text-align: center;
        padding: 5px;
    }

    .scene-node:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.25);
    }

    .scene-node.has-npcs {
        border: 2px solid #ffd700;
    }

    .scene-node.has-quests {
        border: 2px solid #ff6b6b;
    }

    .scene-node.has-both {
        border: 2px solid #4ecdc4;
    }

    .npc-marker {
        position: absolute;
        width: 20px;
        height: 20px;
        background: #ffd700;
        border-radius: 50%;
        border: 2px solid white;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        font-weight: bold;
        color: #333;
        box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }

    .npc-marker.merchant {
        background: #28a745;
    }

    .npc-marker.quest-giver {
        background: #fd7e14;
    }

    .npc-marker.both {
        background: linear-gradient(45deg, #28a745 50%, #fd7e14 50%);
    }

    /* 任务流程图样式 */
    .quest-flow-container {
        width: 100%;
        height: 100%;
        position: relative;
        min-height: 600px;
        background: #f8f9fa;
        border-radius: 8px;
        overflow: hidden;
    }

    .quest-flow-toolbar {
        position: sticky;
        top: 0;
        background: white;
        border-bottom: 1px solid #ddd;
        padding: 10px 15px;
        z-index: 100;
        display: flex;
        align-items: center;
        gap: 15px;
        flex-wrap: wrap;
    }

    .quest-flow-content {
        position: relative;
        padding: 0;
        min-height: calc(100% - 60px);
        overflow: hidden; /* 改为hidden，禁用滚动条 */
        height: calc(100% - 60px);
        width: 100%;
        cursor: grab; /* 显示可拖动光标 */
        user-select: none; /* 禁用文本选择 */
    }

    .quest-flow-content:active {
        cursor: grabbing; /* 拖动时的光标 */
    }

    .quest-flow-viewport {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        transition: transform 0.1s ease-out;
    }

    .quest-node {
        position: absolute;
        width: 200px;
        min-height: 100px;
        background: white;
        border: 2px solid #ddd;
        border-radius: 15px;
        padding: 15px;
        cursor: pointer;
        box-shadow: 0 6px 12px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        z-index: 20;
        font-size: 14px;
        display: flex;
        flex-direction: column;
        justify-content: center;
        text-align: center;
    }

    .quest-node:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    }

    .quest-node.dialogue {
        border-color: #28a745;
        background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    }

    .quest-node.collection {
        border-color: #ffc107;
        background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    }

    .quest-node.kill {
        border-color: #dc3545;
        background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    }

    .quest-node.escort {
        border-color: #6f42c1;
        background: linear-gradient(135deg, #e2d9f3 0%, #d1c4e9 100%);
    }

    .quest-node.exploration {
        border-color: #17a2b8;
        background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    }

    /* 任务分组样式 */
    .quest-group-title {
        font-family: "Microsoft YaHei", sans-serif;
        user-select: none;
        pointer-events: none;
    }

    .quest-group-background {
        pointer-events: none;
    }

    /* 主线任务特殊样式 */
    .quest-node.main {
        border-width: 3px;
        box-shadow: 0 6px 12px rgba(255, 107, 107, 0.2);
    }

    .quest-node.main:hover {
        transform: translateY(-3px);
        box-shadow: 0 8px 16px rgba(255, 107, 107, 0.3);
    }

    /* 支线任务特殊样式 */
    .quest-node.side {
        border-style: dashed;
        border-width: 2px;
    }

    /* 日常任务特殊样式 */
    .quest-node.daily {
        border-radius: 50%;
        width: 160px;
        height: 160px;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    /* 连接线样式优化 */
    .quest-connections line {
        stroke-width: 2;
        opacity: 0.7;
    }

    .quest-connections .main-connection {
        stroke: #ff6b6b;
        stroke-width: 3;
        opacity: 0.8;
    }

    .quest-connections .side-connection {
        stroke: #4ecdc4;
        stroke-width: 2;
        stroke-dasharray: 5,5;
    }

    .quest-connections .daily-connection {
        stroke: #45b7d1;
        stroke-width: 1;
        opacity: 0.5;
    }

    /* 分支树特有样式 */
    .quest-branch-group-title {
        font-family: "Microsoft YaHei", sans-serif;
        user-select: none;
        pointer-events: none;
    }

    .quest-branch-title {
        font-family: "Microsoft YaHei", sans-serif;
        user-select: none;
        pointer-events: none;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .quest-branch-legend {
        font-family: "Microsoft YaHei", sans-serif;
        user-select: none;
    }

    /* 根节点特殊样式 */
    .quest-node.root {
        border-width: 3px;
        box-shadow: 0 8px 16px rgba(0,0,0,0.15);
        background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
    }

    .quest-node.root::before {
        content: "★";
        position: absolute;
        top: -10px;
        right: -10px;
        font-size: 20px;
        z-index: 1;
        color: #ffd700;
    }

    /* 叶子节点样式 */
    .quest-node.leaf {
        border-style: dashed;
        opacity: 0.9;
    }

    .quest-node.leaf::after {
        content: "●";
        position: absolute;
        bottom: -8px;
        right: -8px;
        font-size: 16px;
        z-index: 1;
        color: #28a745;
    }

    /* 分支节点样式 */
    .quest-node.branch {
        background: linear-gradient(135deg, #fff 0%, #f0f8ff 100%);
        border-color: #007bff;
    }

    .quest-node.branch::before {
        content: "◆";
        position: absolute;
        top: -8px;
        left: -8px;
        font-size: 16px;
        z-index: 1;
        color: #007bff;
    }

    /* 连接线增强 */
    .quest-connections path {
        filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
    }

    /* 悬停效果增强 */
    .quest-node:hover {
        transform: translateY(-4px) scale(1.02);
        box-shadow: 0 12px 24px rgba(0,0,0,0.15);
        z-index: 30;
    }

    /* 任务类型特殊标识 */
    .quest-node.main .quest-title::before {
        content: "■ ";
        color: #ff6b6b;
        font-weight: bold;
    }

    .quest-node.side .quest-title::before {
        content: "▲ ";
        color: #4ecdc4;
        font-weight: bold;
    }

    .quest-node.daily .quest-title::before {
        content: "● ";
        color: #45b7d1;
        font-weight: bold;
    }

    .quest-title {
        font-weight: bold;
        margin-bottom: 3px;
        font-size: 13px;
        line-height: 1.2;
    }

    .quest-type {
        display: inline-block;
        padding: 1px 4px;
        border-radius: 2px;
        font-size: 10px;
        font-weight: bold;
        color: white;
        margin-bottom: 3px;
    }

    .quest-type.dialogue { background: #28a745; }
    .quest-type.collection { background: #ffc107; color: #333; }
    .quest-type.kill { background: #dc3545; }
    .quest-type.escort { background: #6f42c1; }
    .quest-type.exploration { background: #17a2b8; }

    .quest-level {
        font-size: 11px;
        color: #666;
        margin-bottom: 3px;
    }

    .quest-npcs {
        font-size: 10px;
        color: #555;
        line-height: 1.1;
    }

    .quest-rewards {
        font-size: 11px;
        color: #007bff;
        margin-top: 2px;
        line-height: 1.1;
        font-weight: 500;
    }

    .quest-prerequisites {
        font-size: 8px;
        color: #fd7e14;
        margin-top: 2px;
    }

    .quest-status {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 8px;
        height: 8px;
        border-radius: 50%;
        border: 1px solid white;
    }

    .quest-status.available { background: #28a745; }
    .quest-status.in-progress { background: #ffc107; }
    .quest-status.completed { background: #6c757d; }
    .quest-status.locked { background: #dc3545; }

    .quest-flow-legend {
        position: absolute;
        top: 10px;
        right: 10px;
        background: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        z-index: 50;
    }

    .legend-item {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        font-size: 12px;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        margin-right: 8px;
    }

    /* 连接线样式 */
    .quest-connection {
        position: absolute;
        pointer-events: none;
        z-index: 1;
    }

    .quest-connection line {
        stroke: #666;
        stroke-width: 2;
        marker-end: url(#arrowhead);
    }

    .quest-connection.prerequisite line {
        stroke: #007bff;
        stroke-dasharray: 5,5;
    }

    /* 侧边栏样式 */
    .filter-group {
        margin-bottom: 20px;
        background: white;
        border-radius: 10px;
        padding: 16px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        border: 1px solid #e9ecef;
    }

    .filter-group-title {
        display: block;
        margin-bottom: 12px;
        font-weight: 600;
        color: #495057;
        font-size: 14px;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .filter-group select,
    .filter-group input {
        width: 100%;
        padding: 10px 12px;
        border: 2px solid #e9ecef;
        border-radius: 8px;
        font-size: 14px;
        transition: all 0.3s ease;
        background: #fff;
        box-sizing: border-box;
    }

    .filter-group select:focus,
    .filter-group input:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 3px rgba(0,123,255,0.1);
    }

    .checkbox-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
        gap: 8px;
        margin-top: 8px;
    }

    .checkbox-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #f8f9fa;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.3s ease;
        border: 2px solid transparent;
        font-size: 13px;
    }

    .checkbox-item:hover {
        background: #e9ecef;
        transform: translateY(-1px);
    }

    .checkbox-item input {
        width: auto;
        margin-right: 6px;
        accent-color: #007bff;
    }

    .checkbox-item.checked {
        background: #e3f2fd;
        border-color: #007bff;
        color: #0056b3;
    }

    .range-inputs {
        display: grid;
        grid-template-columns: 1fr auto 1fr;
        gap: 8px;
        align-items: center;
    }

    .range-separator {
        color: #6c757d;
        font-weight: 500;
        text-align: center;
    }

    .search-box {
        position: relative;
    }

    .search-box input {
        padding-left: 40px;
    }

    .search-icon {
        position: absolute;
        left: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: #6c757d;
        font-size: 16px;
    }

    .legend {
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 10px;
        margin-top: 20px;
    }

    .legend h5 {
        margin-bottom: 10px;
        color: #333;
    }

    .legend-item {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 5px;
        font-size: 12px;
    }

    .legend-color {
        width: 16px;
        height: 16px;
        border-radius: 3px;
        border: 1px solid #ddd;
    }

    /* 详情面板样式 */
    .details-panel {
        position: fixed; /* 改为fixed定位，跟随视口 */
        top: 20px;
        right: 20px;
        width: 300px;
        max-height: calc(100vh - 40px); /* 限制最大高度，避免超出视口 */
        background: white;
        border: 1px solid #ddd;
        border-radius: 4px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        padding: 15px;
        z-index: 1000;
        display: none;
        overflow-y: auto; /* 内容过多时可滚动 */
        opacity: 0;
        transform: translateY(-10px);
        transition: all 0.3s ease;
    }

    .details-panel.show {
        display: block;
        opacity: 1;
        transform: translateY(0);
    }

    .details-panel.hiding {
        opacity: 0;
        transform: translateY(-10px);
    }

    .details-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
    }

    .details-title {
        font-weight: bold;
        color: #333;
    }

    .details-close {
        cursor: pointer;
        color: #999;
        font-size: 18px;
    }

    .details-close:hover {
        color: #333;
    }

    .details-content {
        font-size: 13px;
        line-height: 1.4;
    }

    .details-section {
        margin-bottom: 15px;
    }

    .details-section h6 {
        margin-bottom: 5px;
        color: #555;
        font-weight: 600;
    }

    .details-list {
        list-style: none;
        padding: 0;
        margin: 0;
    }

    .details-list li {
        padding: 3px 0;
        border-bottom: 1px solid #f0f0f0;
    }

    .details-list li:last-child {
        border-bottom: none;
    }

    /* 按钮样式 */
    .btn {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        text-decoration: none;
        display: inline-block;
        transition: background-color 0.2s;
    }

    .btn-primary {
        background-color: #007bff;
        color: white;
    }

    .btn-primary:hover {
        background-color: #0056b3;
    }

    .btn-secondary {
        background-color: #6c757d;
        color: white;
    }

    .btn-secondary:hover {
        background-color: #545b62;
    }

    /* 模态框样式 */
    .modal-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 10000;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .modal-content {
        background: white;
        border-radius: 8px;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        max-width: 90vw;
        max-height: 90vh;
        overflow-y: auto;
        animation: modalFadeIn 0.3s ease;
    }

    .quest-edit-modal {
        width: 800px;
    }

    .dialogue-edit-modal {
        width: 900px;
    }

    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 24px 16px;
        border-bottom: 1px solid #e9ecef;
    }

    .modal-header h3 {
        margin: 0;
        color: #333;
        font-size: 1.25rem;
    }

    .modal-body {
        padding: 24px;
    }

    .modal-footer {
        display: flex;
        justify-content: flex-end;
        gap: 12px;
        padding: 16px 24px 20px;
        border-top: 1px solid #e9ecef;
        background: #f8f9fa;
    }

    .close-btn {
        background: none;
        border: none;
        font-size: 24px;
        color: #666;
        cursor: pointer;
        padding: 0;
        width: 30px;
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        transition: all 0.2s ease;
    }

    .close-btn:hover {
        background: #f0f0f0;
        color: #333;
    }

    .form-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        margin-bottom: 16px;
    }

    .form-group {
        margin-bottom: 16px;
    }

    .form-group label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #333;
    }

    .form-group input,
    .form-group select,
    .form-group textarea {
        width: 100%;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 14px;
        transition: border-color 0.2s ease;
    }

    .form-group input:focus,
    .form-group select:focus,
    .form-group textarea:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
    }

    .form-group textarea {
        resize: vertical;
        min-height: 80px;
    }

    .rewards-section {
        margin-top: 24px;
        padding-top: 20px;
        border-top: 1px solid #e9ecef;
    }

    .rewards-section h4 {
        margin: 0 0 16px 0;
        color: #333;
        font-size: 1.1rem;
    }

    .nodes-list {
        max-height: 300px;
        overflow-y: auto;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        background: #f8f9fa;
    }

    .node-item {
        padding: 12px;
        border-bottom: 1px solid #e9ecef;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .node-item:last-child {
        border-bottom: none;
    }

    .node-item:hover {
        background: #e9ecef;
    }

    .node-content {
        flex: 1;
        margin-right: 12px;
    }

    .node-id {
        font-weight: bold;
        color: #007bff;
        margin-right: 8px;
    }

    .node-text {
        color: #666;
        font-size: 14px;
    }

    .node-edit-section {
        margin-top: 20px;
        padding: 20px;
        border: 1px solid #e9ecef;
        border-radius: 8px;
        background: #f8f9fa;
    }

    .node-edit-section h4 {
        margin: 0 0 16px 0;
        color: #007bff;
        font-size: 1.1rem;
    }

    /* 脚本构建器样式 - 参考对话树编辑页面 */
    .script-builder {
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        background: #f8f9fa;
        margin-bottom: 15px;
    }

    .logical-operator-group {
        margin-bottom: 15px;
    }

    .logical-operator-group select {
        width: 100%;
        max-width: 300px;
        padding: 8px 12px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
    }

    .script-list {
        margin-bottom: 15px;
        min-height: 50px;
    }

    .script-item {
        background: white;
        border: 1px solid #ddd;
        border-radius: 6px;
        padding: 15px;
        margin-bottom: 10px;
        position: relative;
    }

    .script-item:last-child {
        margin-bottom: 0;
    }

    .script-item .condition-type,
    .script-item .action-type {
        width: 100%;
        max-width: 200px;
        padding: 6px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .script-item-params {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 10px;
        margin-bottom: 10px;
    }

    .script-item-params > div {
        display: flex;
        flex-direction: column;
    }

    .script-item-params label {
        font-size: 12px;
        color: #666;
        margin-bottom: 4px;
        font-weight: 500;
    }

    .script-item-params input,
    .script-item-params select,
    .script-item-params textarea {
        padding: 6px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        font-size: 13px;
    }

    .script-item-params textarea {
        min-height: 60px;
        resize: vertical;
    }

    .btn-add-script {
        background: #28a745;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 13px;
        display: inline-flex;
        align-items: center;
        gap: 6px;
    }

    .btn-add-script:hover {
        background: #218838;
    }

    .btn-remove-script {
        position: absolute;
        top: 10px;
        right: 10px;
        background: #dc3545;
        color: white;
        border: none;
        padding: 4px 8px;
        border-radius: 3px;
        cursor: pointer;
        font-size: 11px;
    }

    .btn-remove-script:hover {
        background: #c82333;
    }

    /* 物品选择器样式 */
    .item-selector {
        position: relative;
        width: 100%;
    }

    .item-selector-input {
        width: 100%;
        padding: 6px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        cursor: pointer;
    }

    .item-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ddd;
        border-top: none;
        border-radius: 0 0 4px 4px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }

    .item-selector-search {
        width: 100%;
        padding: 8px 12px;
        border: none;
        border-bottom: 1px solid #eee;
        outline: none;
    }

    .item-selector-categories {
        padding: 5px 0;
    }

    .category-header {
        background: #f8f9fa;
        padding: 6px 12px;
        font-weight: bold;
        font-size: 12px;
        color: #666;
        border-bottom: 1px solid #eee;
    }

    .item-option {
        padding: 8px 12px;
        cursor: pointer;
        font-size: 13px;
        border-bottom: 1px solid #f8f9fa;
    }

    .item-option:hover {
        background: #f8f9fa;
    }

    .item-option.selected {
        background: #007bff;
        color: white;
    }

    .node-edit-buttons {
        display: flex;
        gap: 12px;
        margin-top: 16px;
        align-items: center;
    }

    .node-item {
        cursor: pointer;
        transition: background-color 0.2s ease;
    }

    .node-item:hover {
        background: #e9ecef;
    }

    .node-item.editing {
        background: #cce5ff;
        border-left: 4px solid #007bff;
    }

    .node-actions {
        display: flex;
        gap: 8px;
    }

    .node-action-btn {
        padding: 4px 8px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.2s ease;
    }

    .node-action-btn.edit {
        background: #007bff;
        color: white;
    }

    .node-action-btn.edit:hover {
        background: #0056b3;
    }

    .node-action-btn.delete {
        background: #dc3545;
        color: white;
    }

    .node-action-btn.delete:hover {
        background: #c82333;
    }

    @keyframes modalFadeIn {
        from {
            opacity: 0;
            transform: scale(0.9);
        }
        to {
            opacity: 1;
            transform: scale(1);
        }
    }

    /* 响应式设计 */
    @media (max-width: 1200px) {
        .sidebar {
            width: 250px;
        }

        .details-panel {
            width: 250px;
            max-height: calc(100vh - 40px); /* 保持响应式高度限制 */
        }

        .quest-edit-modal,
        .dialogue-edit-modal {
            width: 95vw;
        }

        .form-row {
            grid-template-columns: 1fr;
        }
    }

    @media (max-width: 768px) {
        .workflow-container {
            flex-direction: column;
            height: auto;
        }

        .sidebar {
            width: 100%;
            height: 200px;
        }

        .main-view {
            height: 600px;
        }

        .details-panel {
            position: fixed; /* 保持fixed定位 */
            top: 10px;
            left: 10px;
            right: 10px;
            width: auto; /* 自适应宽度 */
            max-height: calc(100vh - 20px); /* 适应小屏幕高度 */
            margin-top: 0;
        }
    }

    /* 对话树视图样式 */
    .dialogue-tree-selector {
        background: #f8f9fa;
        border-bottom: 1px solid #ddd;
        padding: 15px;
    }

    .dialogue-tree-selector select {
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        background: white;
        min-width: 200px;
    }

    .dialogue-tree-selector button {
        margin-left: 10px;
        padding: 5px 15px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        transition: background-color 0.2s;
    }

    .dialogue-tree-selector button:hover {
        background: #0056b3;
    }

    .dialogue-info-panel {
        margin-bottom: 20px;
    }

    .dialogue-info-panel h3 {
        margin-top: 0;
        color: #333;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
    }

    .dialogue-flow-container {
        position: relative;
        min-height: 600px;
        height: auto;
        background: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 20px;
        overflow: hidden;
        width: 100%;
    }

    .dialogue-node {
        position: absolute;
        width: 250px;
        min-height: 80px;
        background: white;
        border-radius: 8px;
        padding: 12px;
        cursor: pointer;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        z-index: 2;
    }

    .dialogue-node:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0,0,0,0.2);
    }

    .dialogue-node-type {
        display: inline-block;
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 10px;
        font-weight: bold;
        color: white;
        margin-right: 8px;
    }

    .dialogue-node-content {
        font-size: 13px;
        line-height: 1.4;
        margin-bottom: 8px;
        color: #333;
    }

    .dialogue-node-condition {
        font-size: 11px;
        color: #fd7e14;
        margin-bottom: 4px;
    }

    .dialogue-node-action {
        font-size: 11px;
        color: #6f42c1;
        margin-bottom: 4px;
    }
</style>
';

// 引入页面头部
require_once 'layout_header.php';
?>

<div class="content-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
    <div></div>
    <div class="header-actions">
        <button id="test-dialogue" class="btn btn-primary" style="margin-right: 10px;">测试对话树</button>
        <button id="refresh-data" class="btn btn-primary">刷新数据</button>
        <button id="export-view" class="btn btn-secondary">导出视图</button>
    </div>
</div>

    <div class="workflow-container">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h3 class="sidebar-title">筛选工具</h3>
                <button class="sidebar-toggle" id="sidebar-toggle" title="收起/展开工具栏">
                    <svg class="sidebar-toggle-icon" viewBox="0 0 24 24">
                        <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                    </svg>
                </button>
            </div>
            <div class="sidebar-content">

                <div class="filter-group">
                    <label class="filter-group-title" for="continent-filter">大陆筛选</label>
                    <select id="continent-filter">
                        <option value="">全部大陆</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-group-title" for="zone-filter">区域筛选</label>
                    <select id="zone-filter">
                        <option value="">全部区域</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-group-title" for="quest-group-filter">任务分组</label>
                    <select id="quest-group-filter">
                        <option value="">全部分组</option>
                    </select>
                </div>

                <div class="filter-group">
                    <label class="filter-group-title">任务类型</label>
                    <div class="checkbox-grid" id="quest-type-filter">
                        <label class="checkbox-item">
                            <input type="checkbox" value="dialogue" checked>
                            <span>对话</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" value="collection" checked>
                            <span>收集</span>
                        </label>
                        <label class="checkbox-item">
                            <input type="checkbox" value="kill" checked>
                            <span>击杀</span>
                        </label>
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-group-title">等级范围</label>
                    <div class="range-inputs">
                        <input type="number" id="min-level" placeholder="最低" min="1">
                        <span class="range-separator">至</span>
                        <input type="number" id="max-level" placeholder="最高" min="1">
                    </div>
                </div>

                <div class="filter-group">
                    <label class="filter-group-title" for="npc-filter">相关NPC</label>
                    <select id="npc-filter">
                        <option value="">全部NPC</option>
                    </select>
                </div>



                <div class="filter-group">
                    <label class="filter-group-title" for="search-input">搜索</label>
                    <div class="search-box">
                        <span class="search-icon">🔍</span>
                        <input type="text" id="search-input" placeholder="搜索任务名称或描述...">
                    </div>
                </div>


            </div>
        </div>

        <!-- 主视图区域 -->
        <div class="main-view">
            <div class="view-tabs">
                <div class="view-tab active" data-view="map">地图视图</div>
                <div class="view-tab" data-view="quest-flow">任务流程</div>
                <div class="view-tab" data-view="dialogue-tree">对话关系</div>
                <div class="view-tab" data-view="statistics">统计分析</div>
            </div>

            <div class="view-content">
                <!-- 地图视图 -->
                <div class="view-panel active" id="map-view">
                    <div class="map-container" id="map-container">
                        <div class="map-controls">
                            <button class="map-control-btn" id="zoom-in" title="放大">+</button>
                            <div class="zoom-level" id="zoom-level">100%</div>
                            <button class="map-control-btn" id="zoom-out" title="缩小">-</button>
                            <button class="map-control-btn" id="zoom-reset" title="重置缩放">⌂</button>
                        </div>
                        <div class="map-viewport" id="map-viewport">
                            <!-- 地图场景和NPC将通过JavaScript动态生成 -->
                        </div>
                    </div>
                </div>

                <!-- 任务流程视图 -->
                <div class="view-panel" id="quest-flow-view">
                    <div class="quest-flow-container" id="quest-flow-container">
                        <!-- SVG连接线容器 -->
                        <svg class="quest-connections" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1;">
                            <defs>
                                <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#666" />
                                </marker>
                                <marker id="arrowhead-blue" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
                                    <polygon points="0 0, 10 3.5, 0 7" fill="#007bff" />
                                </marker>
                            </defs>
                        </svg>
                        <!-- 任务节点将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 对话关系视图 -->
                <div class="view-panel" id="dialogue-tree-view">
                    <div class="dialogue-tree-container" id="dialogue-tree-container">
                        <!-- 对话树关系图将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- 统计分析视图 -->
                <div class="view-panel" id="statistics-view">
                    <div class="statistics-container" id="statistics-container">
                        <!-- 统计图表将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>

            <!-- 详情面板 -->
            <div class="details-panel" id="details-panel">
                <div class="details-header">
                    <div class="details-title" id="details-title">详细信息</div>
                    <div class="details-close" id="details-close">&times;</div>
                </div>
                <div class="details-content" id="details-content">
                    <!-- 详细信息内容将通过JavaScript动态生成 -->
                </div>
            </div>

            <!-- 任务编辑模态框 -->
            <div id="quest-edit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content quest-edit-modal">
                    <div class="modal-header">
                        <h3>编辑任务</h3>
                        <button class="close-btn" onclick="closeQuestEditModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="quest-edit-form">
                            <input type="hidden" id="edit-quest-id" name="quest_id">

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-quest-title">任务名称</label>
                                    <input type="text" id="edit-quest-title" name="title" required>
                                </div>
                                <div class="form-group">
                                    <label for="edit-quest-type">任务类型</label>
                                    <select id="edit-quest-type" name="type" required>
                                        <option value="dialogue">对话</option>
                                        <option value="collection">收集</option>
                                        <option value="kill">击杀</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="edit-quest-group">所属分组</label>
                                <select id="edit-quest-group" name="group_id">
                                    <option value="">-- 选择分组 --</option>
                                </select>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-quest-giver">发布NPC</label>
                                    <select id="edit-quest-giver" name="giver_npc_id" required>
                                        <option value="">-- 选择发布NPC --</option>
                                    </select>
                                </div>
                                <div class="form-group">
                                    <label for="edit-quest-receiver">接收NPC</label>
                                    <select id="edit-quest-receiver" name="receiver_npc_id">
                                        <option value="">-- 选择接收NPC --</option>
                                    </select>
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="edit-quest-min-level">最低等级</label>
                                    <input type="number" id="edit-quest-min-level" name="min_level" min="1" required>
                                </div>
                                <div class="form-group">
                                    <label>
                                        <input type="checkbox" id="edit-quest-repeatable" name="is_repeatable" value="1">
                                        可重复任务
                                    </label>
                                </div>
                            </div>

                            <div class="form-group">
                                <label for="edit-quest-description">任务描述</label>
                                <textarea id="edit-quest-description" name="description" rows="4"></textarea>
                            </div>

                            <div class="form-group">
                                <label for="edit-quest-prerequisites">前置任务</label>
                                <select id="edit-quest-prerequisites" name="prerequisite_quests[]" multiple style="width: 100%; height: 120px;">
                                    <!-- 前置任务选项将通过JavaScript动态填充 -->
                                </select>
                                <small style="color: #666; font-size: 12px;">可以选择多个前置任务，玩家需要完成所有前置任务才能接取此任务</small>
                            </div>

                            <div class="rewards-section">
                                <h4>任务奖励</h4>
                                <div class="form-row">
                                    <div class="form-group">
                                        <label for="edit-quest-reward-gold">金币奖励</label>
                                        <input type="number" id="edit-quest-reward-gold" name="reward_gold" min="0">
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-quest-reward-exp">经验奖励</label>
                                        <input type="number" id="edit-quest-reward-exp" name="reward_exp" min="0">
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeQuestEditModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveQuestEdit()">保存</button>
                        <button type="button" class="btn btn-success" onclick="openQuestEditor()">高级编辑</button>
                    </div>
                </div>
            </div>

            <!-- 对话树编辑模态框 -->
            <div id="dialogue-edit-modal" class="modal-overlay" style="display: none;">
                <div class="modal-content dialogue-edit-modal">
                    <div class="modal-header">
                        <h3>编辑对话树</h3>
                        <button class="close-btn" onclick="closeDialogueEditModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        <form id="dialogue-edit-form">
                            <input type="hidden" id="edit-dialogue-id" name="dialogue_id">

                            <div class="form-group">
                                <label for="edit-dialogue-name">对话树名称</label>
                                <input type="text" id="edit-dialogue-name" name="name" required>
                            </div>

                            <div class="form-group">
                                <label for="edit-dialogue-description">对话树描述</label>
                                <textarea id="edit-dialogue-description" name="description" rows="3"></textarea>
                            </div>

                            <div class="dialogue-nodes-section">
                                <h4>对话节点 <span id="dialogue-node-count"></span></h4>
                                <div id="dialogue-nodes-list" class="nodes-list">
                                    <!-- 节点列表将在这里动态加载 -->
                                </div>
                                <button type="button" class="btn btn-secondary" onclick="addNewDialogueNode()" style="margin-top: 10px;">
                                    <i class="fas fa-plus"></i> 添加新节点
                                </button>
                            </div>

                            <!-- 节点编辑区域 -->
                            <div id="node-edit-section" class="node-edit-section" style="display: none;">
                                <h4>编辑节点 <span id="current-node-id"></span></h4>
                                <div class="form-group">
                                    <label for="edit-node-content">节点内容</label>
                                    <textarea id="edit-node-content" rows="4" placeholder="输入对话内容..."></textarea>
                                </div>

                                <div class="form-row">
                                    <div class="form-group">
                                        <label>
                                            <input type="checkbox" id="edit-node-is-player-choice">
                                            玩家选择节点
                                        </label>
                                    </div>
                                    <div class="form-group">
                                        <label for="edit-node-next-nodes">下一节点ID (逗号分隔)</label>
                                        <input type="text" id="edit-node-next-nodes" placeholder="例如: 92,93">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label for="condition-builder">条件脚本 (决定此节点是否出现)</label>
                                    <div id="condition-builder" class="script-builder">
                                        <div class="logical-operator-group">
                                            <select id="condition-logical-operator" class="form-control">
                                                <option value="AND">所有条件都必须满足 (AND)</option>
                                                <option value="OR">任意一个条件满足即可 (OR)</option>
                                            </select>
                                        </div>
                                        <div id="condition-list" class="script-list"></div>
                                        <button type="button" id="add-condition-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加条件</button>
                                    </div>
                                    <textarea name="condition_script" id="edit-node-condition" style="display:none;" placeholder='{"logical_operator": "AND", "conditions": []}'></textarea>
                                </div>

                                <div class="form-group">
                                    <label for="action-builder">动作脚本 (节点被选择时执行)</label>
                                    <div id="action-builder" class="script-builder">
                                        <div id="action-list" class="script-list"></div>
                                        <button type="button" id="add-action-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加动作</button>
                                    </div>
                                    <textarea name="action_script" id="edit-node-action" style="display:none;" placeholder='{"actions": []}'></textarea>
                                </div>

                                <div class="node-edit-buttons">
                                    <button type="button" class="btn btn-primary" onclick="saveNodeEdit()">保存节点</button>
                                    <button type="button" class="btn btn-secondary" onclick="cancelNodeEdit()">取消</button>
                                    <button type="button" class="btn btn-danger" onclick="deleteDialogueNode()" style="margin-left: auto;">删除节点</button>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" onclick="closeDialogueEditModal()">取消</button>
                        <button type="button" class="btn btn-primary" onclick="saveDialogueEdit()">保存</button>
                        <button type="button" class="btn btn-success" onclick="openDialogueEditor()">高级编辑</button>
                    </div>
                </div>
            </div>
        </div>
    </div>

<script>
class QuestWorkflowVisualization {
    constructor() {
        this.data = {
            scenes: [],
            npcs: [],
            quests: [],
            dialogues: [],
            questGroups: [],
            dialogueNodes: [],
            items: []
        };
        this.currentView = 'map';
        this.selectedNode = null;
        this.filters = {
            questGroup: '',
            questTypes: ['dialogue', 'collection', 'kill'],
            minLevel: null,
            maxLevel: null,
            npc: '',
            scene: '',
            search: ''
        };

        this.init();
    }

    async init() {
        await this.loadData();
        this.setupEventListeners();
        this.populateFilters();

        // 检查URL参数，设置当前对话树ID
        this.checkUrlParameters();

        this.renderCurrentView();
    }

    checkUrlParameters() {
        const urlParams = new URLSearchParams(window.location.search);
        const dialogueId = urlParams.get('dialogue_id');
        const viewParam = urlParams.get('view');

        if (dialogueId) {
            console.log('从URL参数获取对话树ID:', dialogueId);
            this.currentDialogueTreeId = dialogueId;

            // 强制切换到对话树视图
            console.log('强制切换到对话树视图');
            this.currentView = 'dialogue-tree';

            // 更新视图按钮状态
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            const dialogueBtn = document.querySelector('[data-view="dialogue-tree"]');
            if (dialogueBtn) {
                dialogueBtn.classList.add('active');
            }

            // 显示对话树视图面板
            document.querySelectorAll('.view-panel').forEach(panel => {
                panel.classList.remove('active');
            });
            const dialoguePanel = document.getElementById('dialogue-tree-view');
            if (dialoguePanel) {
                dialoguePanel.classList.add('active');
            }

            // 自动加载指定的对话树
            setTimeout(() => {
                const select = document.getElementById('dialogue-tree-select');
                if (select) {
                    select.value = dialogueId;
                    this.loadDialogueTreeDetail(dialogueId);
                }
            }, 200);
        } else if (viewParam) {
            // 处理view参数
            console.log('从URL参数获取视图:', viewParam);
            this.currentView = viewParam;
        }
    }

    // 动态更新URL参数，不刷新页面
    updateUrlParameter(key, value) {
        console.log(`更新URL参数: ${key} = ${value}`);

        const url = new URL(window.location);

        if (value) {
            url.searchParams.set(key, value);
        } else {
            url.searchParams.delete(key);
        }

        // 使用pushState更新URL，不刷新页面
        window.history.pushState({ path: url.href }, '', url.href);

        console.log('URL已更新为:', url.href);
    }

    async loadData() {
        try {
            // 检查是否使用测试数据
            const useTestData = window.location.search.includes('test=1');
            const testParam = useTestData ? '&test=1' : '';

            // 加载所有必要的数据
            const [scenesRes, npcsRes, questsRes, dialoguesRes, groupsRes, nodesRes, continentsRes, zonesRes, itemsRes] = await Promise.all([
                fetch(`api_quest_workflow.php?action=get_scenes${testParam}`),
                fetch(`api_quest_workflow.php?action=get_npcs${testParam}`),
                fetch(`api_quest_workflow.php?action=get_quests${testParam}`),
                fetch(`api_quest_workflow.php?action=get_dialogues${testParam}`),
                fetch(`api_quest_workflow.php?action=get_quest_groups${testParam}`),
                fetch(`api_quest_workflow.php?action=get_dialogue_nodes${testParam}`),
                fetch(`api_continents.php?action=get_continents`),
                fetch(`api_continents.php?action=get_all_zones`),
                fetch(`api_quest_workflow.php?action=get_items${testParam}`)
            ]);

            this.data.scenes = await scenesRes.json();
            this.data.npcs = await npcsRes.json();
            this.data.quests = await questsRes.json();
            this.data.dialogues = await dialoguesRes.json();
            this.data.questGroups = await groupsRes.json();
            this.data.dialogueNodes = await nodesRes.json();
            this.data.items = await itemsRes.json();

            // 处理大陆和区域数据
            const continentsData = await continentsRes.json();
            const zonesData = await zonesRes.json();
            this.data.continents = continentsData.success ? continentsData.continents : [];
            this.data.zones = zonesData.success ? zonesData.zones : [];

            console.log('Loaded continents:', this.data.continents);
            console.log('Loaded zones:', this.data.zones);

            // 调试信息：检查任务数据
            console.log('任务数据详情:', {
                总任务数: this.data.quests.length,
                任务ID列表: this.data.quests.map(q => q.id),
                任务7详情: this.data.quests.find(q => q.id == 7),
                是否包含任务7: this.data.quests.some(q => q.id == 7),
                使用测试数据: useTestData
            });

            console.log('数据加载完成:', this.data);
        } catch (error) {
            console.error('数据加载失败:', error);
            // 如果加载失败，尝试使用测试数据
            try {
                const [scenesRes, npcsRes, questsRes, dialoguesRes, groupsRes, nodesRes] = await Promise.all([
                    fetch('api_quest_workflow.php?action=get_scenes&test=1'),
                    fetch('api_quest_workflow.php?action=get_npcs&test=1'),
                    fetch('api_quest_workflow.php?action=get_quests&test=1'),
                    fetch('api_quest_workflow.php?action=get_dialogues&test=1'),
                    fetch('api_quest_workflow.php?action=get_quest_groups&test=1'),
                    fetch('api_quest_workflow.php?action=get_dialogue_nodes&test=1')
                ]);

                this.data.scenes = await scenesRes.json();
                this.data.npcs = await npcsRes.json();
                this.data.quests = await questsRes.json();
                this.data.dialogues = await dialoguesRes.json();
                this.data.questGroups = await groupsRes.json();
                this.data.dialogueNodes = await nodesRes.json();

                // 调试信息：检查测试数据中的任务
                console.warn('⚠️ 使用测试数据模式，实际数据加载失败');
                console.log('测试数据中的任务详情:', {
                    总任务数: this.data.quests.length,
                    任务ID列表: this.data.quests.map(q => q.id),
                    任务7详情: this.data.quests.find(q => q.id == 7),
                    是否包含任务7: this.data.quests.some(q => q.id == 7)
                });
                console.log('使用测试数据:', this.data);
                this.showError('使用测试数据模式，实际数据加载失败');
            } catch (testError) {
                console.error('测试数据加载也失败:', testError);
                this.showError('数据加载失败，请检查服务器连接');
            }
        }
    }

    setupEventListeners() {
        // 视图切换
        document.querySelectorAll('.view-tab').forEach(tab => {
            tab.addEventListener('click', (e) => {
                const view = e.target.dataset.view;
                this.switchView(view);
            });
        });

        // 筛选器事件 - 添加空值检查
        const questGroupFilter = document.getElementById('quest-group-filter');
        if (questGroupFilter) {
            questGroupFilter.addEventListener('change', (e) => {
                this.filters.questGroup = e.target.value;
                this.applyFilters();
            });
        }

        document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const type = e.target.value;
                if (e.target.checked) {
                    if (!this.filters.questTypes.includes(type)) {
                        this.filters.questTypes.push(type);
                    }
                } else {
                    this.filters.questTypes = this.filters.questTypes.filter(t => t !== type);
                }
                this.applyFilters();
            });
        });

        const minLevelInput = document.getElementById('min-level');
        if (minLevelInput) {
            minLevelInput.addEventListener('input', (e) => {
                this.filters.minLevel = e.target.value ? parseInt(e.target.value) : null;
                this.applyFilters();
            });
        }

        const maxLevelInput = document.getElementById('max-level');
        if (maxLevelInput) {
            maxLevelInput.addEventListener('input', (e) => {
                this.filters.maxLevel = e.target.value ? parseInt(e.target.value) : null;
                this.applyFilters();
            });
        }

        const npcFilter = document.getElementById('npc-filter');
        if (npcFilter) {
            npcFilter.addEventListener('change', (e) => {
                this.filters.npc = e.target.value;
                this.applyFilters();
            });
        }


        const searchInput = document.getElementById('search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value.toLowerCase();
                this.applyFilters();
            });
        }

        // 详情面板关闭
        const detailsClose = document.getElementById('details-close');
        if (detailsClose) {
            detailsClose.addEventListener('click', () => {
                this.hideDetails();
            });
        }

        // 点击空白区域关闭详情面板
        document.addEventListener('click', (e) => {
            const detailsPanel = document.getElementById('details-panel');
            if (detailsPanel && detailsPanel.classList.contains('show')) {
                // 检查点击的元素是否在详情面板内部
                if (!detailsPanel.contains(e.target)) {
                    // 检查点击的元素是否是触发显示详情面板的元素
                    const isDialogueNode = e.target.closest('.dialogue-node');
                    const isSceneNode = e.target.closest('.scene-node');
                    const isNpcMarker = e.target.closest('.npc-marker');
                    const isQuestItem = e.target.closest('.quest-item');
                    const isQuestNode = e.target.closest('.quest-node'); // 添加任务节点检测

                    // 如果不是触发元素，则关闭详情面板
                    if (!isDialogueNode && !isSceneNode && !isNpcMarker && !isQuestItem && !isQuestNode) {
                        this.hideDetails();
                    }
                }
            }
        });

        // 阻止详情面板内部点击事件冒泡
        const detailsPanel = document.getElementById('details-panel');
        if (detailsPanel) {
            detailsPanel.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }

        // ESC键关闭详情面板
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                const detailsPanel = document.getElementById('details-panel');
                if (detailsPanel && detailsPanel.classList.contains('show')) {
                    this.hideDetails();
                }
            }
        });

        // 刷新数据
        const refreshData = document.getElementById('refresh-data');
        if (refreshData) {
            refreshData.addEventListener('click', () => {
                this.loadData().then(() => {
                    this.populateFilters();
                    this.renderCurrentView();
                });
            });
        }

        // 导出视图
        const exportView = document.getElementById('export-view');
        if (exportView) {
            exportView.addEventListener('click', () => {
                this.exportCurrentView();
            });
        }

        // 测试对话树
        const testDialogue = document.getElementById('test-dialogue');
        if (testDialogue) {
            testDialogue.addEventListener('click', () => {
                this.testDialogueTree();
            });
        }

        // 工具栏收起/展开
        const sidebarToggle = document.getElementById('sidebar-toggle');
        if (sidebarToggle) {
            sidebarToggle.addEventListener('click', () => {
                this.toggleSidebar();
            });
        }

        // 初始化checkbox样式
        this.initializeCheckboxes();
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        if (!sidebar) {
            console.error('侧边栏元素未找到');
            return;
        }

        const toggleIcon = sidebar.querySelector('.sidebar-toggle-icon');
        if (!toggleIcon) {
            console.error('侧边栏切换图标未找到');
            return;
        }

        if (sidebar.classList.contains('collapsed')) {
            sidebar.classList.remove('collapsed');
            toggleIcon.style.transform = 'rotate(0deg)';
        } else {
            sidebar.classList.add('collapsed');
            toggleIcon.style.transform = 'rotate(180deg)';
        }
    }

    initializeCheckboxes() {
        // 为所有checkbox添加样式管理
        document.querySelectorAll('.checkbox-item').forEach(item => {
            const checkbox = item.querySelector('input[type="checkbox"]');

            if (!checkbox) {
                console.warn('Checkbox not found in item:', item);
                return;
            }

            // 初始状态
            if (checkbox.checked) {
                item.classList.add('checked');
            }

            // 点击事件
            item.addEventListener('click', (e) => {
                if (e.target.type !== 'checkbox') {
                    checkbox.checked = !checkbox.checked;
                }

                if (checkbox.checked) {
                    item.classList.add('checked');
                } else {
                    item.classList.remove('checked');
                }

                // 触发筛选更新
                this.applyFilters();
            });

            // checkbox直接点击事件
            checkbox.addEventListener('change', () => {
                if (checkbox.checked) {
                    item.classList.add('checked');
                } else {
                    item.classList.remove('checked');
                }
                this.applyFilters();
            });
        });
    }

    populateFilters() {
        // 填充大陆下拉框
        const continentSelect = document.getElementById('continent-filter');
        continentSelect.innerHTML = '<option value="">全部大陆</option>';
        if (this.data.continents) {
            this.data.continents.forEach(continent => {
                const option = document.createElement('option');
                option.value = continent.id;
                option.textContent = continent.display_name;
                continentSelect.appendChild(option);
            });
        }

        // 填充任务分组下拉框
        const questGroupSelect = document.getElementById('quest-group-filter');
        questGroupSelect.innerHTML = '<option value="">全部分组</option>';
        if (this.data.questGroups) {
            this.data.questGroups.forEach(group => {
                const option = document.createElement('option');
                option.value = group.id;
                option.textContent = group.name;
                questGroupSelect.appendChild(option);
            });
        }

        // 填充区域下拉框
        const zoneSelect = document.getElementById('zone-filter');
        zoneSelect.innerHTML = '<option value="">全部区域</option>';
        if (this.data.zones) {
            this.data.zones.forEach(zone => {
                const option = document.createElement('option');
                option.value = zone.id;
                option.textContent = zone.name;
                zoneSelect.appendChild(option);
            });
        }



        // 填充NPC下拉框
        const npcSelect = document.getElementById('npc-filter');
        npcSelect.innerHTML = '<option value="">全部NPC</option>';
        if (this.data.npcs) {
            this.data.npcs.forEach(npc => {
                const option = document.createElement('option');
                option.value = npc.id;
                option.textContent = npc.name;
                npcSelect.appendChild(option);
            });
        }



        // 添加大陆区域联动
        this.setupContinentZoneFilter();
    }

    setupContinentZoneFilter() {
        const continentSelect = document.getElementById('continent-filter');
        const zoneSelect = document.getElementById('zone-filter');

        if (!continentSelect || !zoneSelect) {
            console.error('大陆或区域选择器未找到');
            return;
        }

        continentSelect.addEventListener('change', () => {
            const selectedContinent = continentSelect.value;
            console.log('选择的大陆:', selectedContinent);
            console.log('可用区域:', this.data.zones);

            // 重置区域选择
            zoneSelect.innerHTML = '<option value="">全部区域</option>';

            if (selectedContinent && this.data.zones) {
                // 筛选属于选中大陆的区域
                const filteredZones = this.data.zones.filter(zone => zone.continent === selectedContinent);
                console.log('筛选后的区域:', filteredZones);

                filteredZones.forEach(zone => {
                    const option = document.createElement('option');
                    option.value = zone.id;
                    option.textContent = zone.name;
                    zoneSelect.appendChild(option);
                });

                if (filteredZones.length === 0) {
                    const option = document.createElement('option');
                    option.value = '';
                    option.textContent = '该大陆暂无区域';
                    option.disabled = true;
                    zoneSelect.appendChild(option);
                }
            } else if (this.data.zones) {
                // 显示所有区域
                this.data.zones.forEach(zone => {
                    const option = document.createElement('option');
                    option.value = zone.id;
                    option.textContent = `${zone.name} (${zone.continent_name || zone.continent})`;
                    zoneSelect.appendChild(option);
                });
            }

            // 触发筛选更新
            this.applyFilters();
        });

        // 区域变化时也触发筛选
        zoneSelect.addEventListener('change', () => {
            console.log('选择的区域:', zoneSelect.value);
            this.applyFilters();
        });
    }

    switchView(view) {
        // 更新标签状态
        document.querySelectorAll('.view-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-view="${view}"]`).classList.add('active');

        // 更新面板显示
        document.querySelectorAll('.view-panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.getElementById(`${view}-view`).classList.add('active');

        this.currentView = view;
        this.renderCurrentView();
    }

    renderCurrentView() {
        switch (this.currentView) {
            case 'map':
                this.renderMapView();
                break;
            case 'quest-flow':
                this.renderQuestFlowView();
                break;
            case 'dialogue-tree':
                this.renderDialogueTreeView();
                break;
            case 'statistics':
                this.renderStatisticsView();
                break;
        }
    }

    renderMapView() {
        console.log('开始渲染地图视图');

        const viewport = document.getElementById('map-viewport');
        if (!viewport) {
            console.error('地图视口未找到');
            return;
        }

        // 检查是否有模态框仍在显示
        const questModal = document.getElementById('quest-edit-modal');
        const dialogueModal = document.getElementById('dialogue-edit-modal');
        const hasModalOpen = (questModal && questModal.style.display === 'flex') ||
                            (dialogueModal && dialogueModal.style.display === 'flex');

        if (hasModalOpen) {
            console.log('检测到模态框仍在显示，延迟渲染地图');
            setTimeout(() => this.renderMapView(), 200);
            return;
        }

        // 检查容器尺寸
        const containerRect = viewport.getBoundingClientRect();
        console.log('地图容器尺寸:', {
            width: containerRect.width,
            height: containerRect.height,
            visible: containerRect.width > 0 && containerRect.height > 0
        });

        // 如果容器不可见，延迟渲染
        if (containerRect.width === 0 || containerRect.height === 0) {
            console.log('地图容器不可见，延迟渲染');
            setTimeout(() => this.renderMapView(), 300);
            return;
        }

        viewport.innerHTML = '';

        // 获取筛选后的数据
        const filteredScenes = this.getFilteredScenes();
        const filteredNpcs = this.getFilteredNpcs();
        const filteredQuests = this.getFilteredQuests();

        if (filteredScenes.length === 0) {
            viewport.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 400px; color: #666; font-size: 16px;">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 16px;">🗺️</div>
                        <div>没有找到符合条件的场景</div>
                        <div style="font-size: 14px; margin-top: 8px;">请调整筛选条件</div>
                    </div>
                </div>
            `;
            return;
        }

        // 使用场景的实际坐标进行布局
        this.renderSceneGrid(filteredScenes, filteredNpcs, filteredQuests, viewport);

        // 调试：检查容器尺寸
        const mapContainer = document.getElementById('map-container');
        if (mapContainer) {
            console.log('容器尺寸检查:', {
                containerWidth: mapContainer.clientWidth,
                containerHeight: mapContainer.clientHeight,
                viewportWidth: viewport.offsetWidth,
                viewportHeight: viewport.offsetHeight,
                scrollWidth: mapContainer.scrollWidth,
                scrollHeight: mapContainer.scrollHeight,
                canScrollX: mapContainer.scrollWidth > mapContainer.clientWidth,
                canScrollY: mapContainer.scrollHeight > mapContainer.clientHeight
            });
        }

        // 初始化地图控制
        this.initializeMapControls();
    }

    initializeMapControls() {
        const viewport = document.getElementById('map-viewport');
        const container = document.getElementById('map-container');
        const zoomInBtn = document.getElementById('zoom-in');
        const zoomOutBtn = document.getElementById('zoom-out');
        const zoomResetBtn = document.getElementById('zoom-reset');
        const zoomLevelDisplay = document.getElementById('zoom-level');

        if (!viewport || !container) return;

        // 缩放相关变量
        let currentZoom = 1;
        const minZoom = 0.25;
        const maxZoom = 3;
        const zoomStep = 0.25;

        // 拖拽相关变量
        let isDragging = false;
        let startX, startY, scrollLeft, scrollTop;

        // 更新缩放显示
        const updateZoomDisplay = () => {
            zoomLevelDisplay.textContent = Math.round(currentZoom * 100) + '%';
            viewport.style.transform = `scale(${currentZoom})`;

            // 更新按钮状态
            zoomInBtn.disabled = currentZoom >= maxZoom;
            zoomOutBtn.disabled = currentZoom <= minZoom;
        };

        // 缩放功能
        zoomInBtn.addEventListener('click', () => {
            if (currentZoom < maxZoom) {
                currentZoom = Math.min(maxZoom, currentZoom + zoomStep);
                updateZoomDisplay();
            }
        });

        zoomOutBtn.addEventListener('click', () => {
            if (currentZoom > minZoom) {
                currentZoom = Math.max(minZoom, currentZoom - zoomStep);
                updateZoomDisplay();
            }
        });

        zoomResetBtn.addEventListener('click', () => {
            currentZoom = 1;
            updateZoomDisplay();
            container.scrollLeft = 0;
            container.scrollTop = 0;
        });

        // 鼠标滚轮缩放
        container.addEventListener('wheel', (e) => {
            e.preventDefault();
            const delta = e.deltaY > 0 ? -zoomStep : zoomStep;
            const newZoom = Math.max(minZoom, Math.min(maxZoom, currentZoom + delta));

            if (newZoom !== currentZoom) {
                currentZoom = newZoom;
                updateZoomDisplay();
            }
        });

        // 拖拽功能
        container.addEventListener('mousedown', (e) => {
            // 检查是否点击在可拖拽区域
            const isDraggableArea = e.target === container ||
                                  e.target === viewport ||
                                  e.target.classList.contains('map-viewport') ||
                                  e.target.classList.contains('map-container') ||
                                  (!e.target.closest('.map-controls') &&
                                   !e.target.closest('.scene-node') &&
                                   !e.target.closest('.npc-marker'));

            if (isDraggableArea) {
                isDragging = true;
                startX = e.clientX;
                startY = e.clientY;
                scrollLeft = container.scrollLeft;
                scrollTop = container.scrollTop;

                container.classList.add('dragging');
                e.preventDefault(); // 防止文本选择

                console.log('开始拖拽:', { startX, startY, scrollLeft, scrollTop });
            }
        });

        // 全局监听鼠标事件，确保在容器外也能正常结束拖拽
        const stopDragging = () => {
            if (isDragging) {
                isDragging = false;
                container.classList.remove('dragging');
                console.log('结束拖拽');
            }
        };

        document.addEventListener('mouseup', stopDragging);
        document.addEventListener('mouseleave', stopDragging);

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;
            e.preventDefault();

            const deltaX = e.clientX - startX;
            const deltaY = e.clientY - startY;

            const newScrollLeft = scrollLeft - deltaX;
            const newScrollTop = scrollTop - deltaY;

            container.scrollLeft = newScrollLeft;
            container.scrollTop = newScrollTop;

            // 调试信息（可以在生产环境中移除）
            if (Math.abs(deltaX) > 5 || Math.abs(deltaY) > 5) {
                console.log('拖拽移动:', {
                    deltaX,
                    deltaY,
                    newScrollLeft,
                    newScrollTop,
                    maxScrollLeft: container.scrollWidth - container.clientWidth,
                    maxScrollTop: container.scrollHeight - container.clientHeight
                });
            }
        });

        // 初始化显示
        updateZoomDisplay();
    }

    renderSceneGrid(scenes, npcs, quests, container) {
        // 如果场景有坐标信息，使用坐标布局；否则使用网格布局
        const hasCoordinates = scenes.some(scene => scene.x !== undefined && scene.y !== undefined);

        if (hasCoordinates) {
            this.renderCoordinateBasedMap(scenes, npcs, quests, container);
        } else {
            this.renderGridBasedMap(scenes, npcs, quests, container);
        }
    }

    renderCoordinateBasedMap(scenes, npcs, quests, container) {
        // 计算地图范围
        const minX = Math.min(...scenes.map(s => s.x || 0));
        const maxX = Math.max(...scenes.map(s => s.x || 0));
        const minY = Math.min(...scenes.map(s => s.y || 0));
        const maxY = Math.max(...scenes.map(s => s.y || 0));

        const cellSize = 150;
        const padding = 100;

        // 计算地图总尺寸
        const mapWidth = (maxX - minX + 1) * cellSize + padding * 2;
        const mapHeight = (maxY - minY + 1) * cellSize + padding * 2;

        // 设置viewport尺寸，确保比容器大以便滚动
        // 确保最小尺寸足够大，能够超出map-container的600px高度
        const minWidth = 1500;
        const minHeight = 1200; // 确保超出600px的容器高度

        container.style.width = Math.max(mapWidth, minWidth) + 'px';
        container.style.height = Math.max(mapHeight, minHeight) + 'px';

        console.log('坐标地图尺寸设置:', {
            mapWidth,
            mapHeight,
            finalWidth: container.style.width,
            finalHeight: container.style.height,
            minWidth,
            minHeight
        });

        // 渲染场景节点
        scenes.forEach(scene => {
            const x = (scene.x - minX) * cellSize + padding;
            // 修复Y轴方向：使用屏幕坐标系（Y轴向下为正），与场景管理后台保持一致
            const y = (maxY - scene.y) * cellSize + padding;

            const sceneNode = this.createSceneNodeWithCoords(scene, x, y, npcs, quests);
            container.appendChild(sceneNode);
        });

        // 渲染NPC标记
        npcs.forEach(npc => {
            const scene = scenes.find(s => s.id === npc.scene_id);
            if (scene) {
                const npcMarker = this.createNpcMarkerWithCoords(npc, scene, minX, minY, maxY, cellSize, padding, npcs);
                container.appendChild(npcMarker);
            }
        });
    }

    renderGridBasedMap(scenes, npcs, quests, container) {
        // 使用简单的网格布局
        const cols = Math.ceil(Math.sqrt(scenes.length));
        const rows = Math.ceil(scenes.length / cols);
        const cellWidth = 180;
        const cellHeight = 140;
        const padding = 100;

        // 计算地图总尺寸
        const mapWidth = cols * cellWidth + padding * 2;
        const mapHeight = rows * cellHeight + padding * 2;

        // 设置viewport尺寸，确保比容器大以便滚动
        // 确保最小尺寸足够大，能够超出map-container的600px高度
        const minWidth = 1500;
        const minHeight = 1200; // 确保超出600px的容器高度

        container.style.width = Math.max(mapWidth, minWidth) + 'px';
        container.style.height = Math.max(mapHeight, minHeight) + 'px';

        console.log('网格地图尺寸设置:', {
            cols,
            rows,
            mapWidth,
            mapHeight,
            finalWidth: container.style.width,
            finalHeight: container.style.height,
            minWidth,
            minHeight
        });

        scenes.forEach((scene, index) => {
            const x = (index % cols) * cellWidth + padding;
            const y = Math.floor(index / cols) * cellHeight + padding;

            const sceneNode = this.createSceneNodeWithCoords(scene, x, y, npcs, quests);
            container.appendChild(sceneNode);
        });

        // 渲染NPC标记
        npcs.forEach(npc => {
            const npcMarker = this.createNpcMarker(npc);
            container.appendChild(npcMarker);
        });
    }

    createSceneNodeWithCoords(scene, x, y, npcs, quests) {
        const node = document.createElement('div');
        node.className = 'scene-node';
        node.dataset.sceneId = scene.id;

        node.style.left = x + 'px';
        node.style.top = y + 'px';

        // 检查场景中的NPC和任务
        const sceneNpcs = npcs.filter(npc => npc.scene_id == scene.id);
        const sceneQuests = quests.filter(quest =>
            sceneNpcs.some(npc => npc.id == quest.giver_npc_id || npc.id == quest.receiver_npc_id)
        );

        // 设置样式类
        if (sceneNpcs.length > 0 && sceneQuests.length > 0) {
            node.classList.add('has-both');
        } else if (sceneNpcs.length > 0) {
            node.classList.add('has-npcs');
        } else if (sceneQuests.length > 0) {
            node.classList.add('has-quests');
        }

        // 不显示坐标信息，保持界面简洁
        node.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 3px; font-size: 12px;">${scene.name}</div>
            <div style="font-size: 10px; color: #666;">
                NPC: ${sceneNpcs.length} | 任务: ${sceneQuests.length}
            </div>
        `;

        node.addEventListener('click', (e) => {
            this.showSceneDetails(scene, sceneNpcs, sceneQuests, e);
        });

        return node;
    }

    createNpcMarkerWithCoords(npc, scene, minX, minY, maxY, cellSize, padding, allNpcs) {
        const marker = document.createElement('div');
        marker.className = 'npc-marker';
        marker.dataset.npcId = npc.id;

        // 根据NPC类型设置样式
        if (npc.is_merchant && npc.is_quest_giver) {
            marker.classList.add('both');
        } else if (npc.is_merchant) {
            marker.classList.add('merchant');
        } else if (npc.is_quest_giver) {
            marker.classList.add('quest-giver');
        }

        // 计算场景位置
        const sceneX = (scene.x - minX) * cellSize + padding;
        // 修复Y轴方向：使用屏幕坐标系（Y轴向下为正），与场景管理后台保持一致
        const sceneY = (maxY - scene.y) * cellSize + padding;

        // 在场景内排列NPC
        const sceneNpcs = allNpcs.filter(n => n.scene_id === scene.id);
        const npcIndex = sceneNpcs.indexOf(npc);

        const npcSize = 16;
        const npcPadding = 4;
        const maxNpcsPerRow = Math.floor((100 - npcPadding * 2) / (npcSize + npcPadding));

        const npcRow = Math.floor(npcIndex / maxNpcsPerRow);
        const npcCol = npcIndex % maxNpcsPerRow;

        const x = sceneX + npcPadding + npcCol * (npcSize + npcPadding);
        const y = sceneY + 30 + npcRow * (npcSize + npcPadding);

        marker.style.left = x + 'px';
        marker.style.top = y + 'px';

        marker.textContent = npc.name.charAt(0);
        marker.title = npc.name;

        marker.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showNpcDetails(npc, e);
        });

        return marker;
    }

    createSceneNode(scene, index, npcs, quests) {
        const node = document.createElement('div');
        node.className = 'scene-node';
        node.dataset.sceneId = scene.id;

        // 计算位置（简单的网格布局）
        const cols = Math.ceil(Math.sqrt(this.data.scenes.length));
        const x = (index % cols) * 150 + 50;
        const y = Math.floor(index / cols) * 120 + 50;

        node.style.left = x + 'px';
        node.style.top = y + 'px';

        // 检查场景中的NPC和任务
        const sceneNpcs = npcs.filter(npc => npc.scene_id == scene.id);
        const sceneQuests = quests.filter(quest =>
            sceneNpcs.some(npc => npc.id == quest.giver_npc_id || npc.id == quest.receiver_npc_id)
        );

        // 设置样式类
        if (sceneNpcs.length > 0 && sceneQuests.length > 0) {
            node.classList.add('has-both');
        } else if (sceneNpcs.length > 0) {
            node.classList.add('has-npcs');
        } else if (sceneQuests.length > 0) {
            node.classList.add('has-quests');
        }

        node.innerHTML = `
            <div style="font-weight: bold; margin-bottom: 3px;">${scene.name}</div>
            <div style="font-size: 10px;">
                NPC: ${sceneNpcs.length} | 任务: ${sceneQuests.length}
            </div>
        `;

        node.addEventListener('click', (e) => {
            this.showSceneDetails(scene, sceneNpcs, sceneQuests, e);
        });

        return node;
    }

    createNpcMarker(npc) {
        const marker = document.createElement('div');
        marker.className = 'npc-marker';
        marker.dataset.npcId = npc.id;

        // 根据NPC类型设置样式
        if (npc.is_merchant && npc.is_quest_giver) {
            marker.classList.add('both');
        } else if (npc.is_merchant) {
            marker.classList.add('merchant');
        } else if (npc.is_quest_giver) {
            marker.classList.add('quest-giver');
        }

        // 计算位置（相对于场景节点）
        // 使用筛选后的场景列表来计算正确的索引
        const filteredScenes = this.getFilteredScenes();
        const sceneIndex = filteredScenes.findIndex(s => s.id == npc.scene_id);

        if (sceneIndex !== -1) {
            const cols = Math.ceil(Math.sqrt(filteredScenes.length));
            const sceneX = (sceneIndex % cols) * 150 + 50;
            const sceneY = Math.floor(sceneIndex / cols) * 120 + 50;

            // 在场景节点内部显示NPC，避免重叠
            const npcCount = this.getFilteredNpcs().filter(n => n.scene_id === npc.scene_id).length;
            const npcIndex = this.getFilteredNpcs().filter(n => n.scene_id === npc.scene_id).indexOf(npc);

            // 在场景节点内部排列NPC
            const npcSize = 16;
            const padding = 4;
            const maxNpcsPerRow = Math.floor((120 - padding * 2) / (npcSize + padding));

            const npcRow = Math.floor(npcIndex / maxNpcsPerRow);
            const npcCol = npcIndex % maxNpcsPerRow;

            const x = sceneX + padding + npcCol * (npcSize + padding);
            const y = sceneY + 25 + npcRow * (npcSize + padding); // 25px为场景标题预留空间

            marker.style.left = x + 'px';
            marker.style.top = y + 'px';
        }

        marker.textContent = npc.name.charAt(0);
        marker.title = npc.name;

        marker.addEventListener('click', (e) => {
            e.stopPropagation();
            this.showNpcDetails(npc, e);
        });

        return marker;
    }

    renderQuestFlowView() {
        const container = document.querySelector('.quest-flow-container');

        // 检查是否已经初始化
        if (!container.querySelector('.quest-flow-toolbar')) {
            this.initializeQuestFlowView(container);
        }

        const content = container.querySelector('.quest-flow-content');
        const viewport = content.querySelector('.quest-flow-viewport');
        const questNodes = viewport.querySelectorAll('.quest-node');
        questNodes.forEach(node => node.remove());

        const svg = viewport.querySelector('.quest-connections') || this.createQuestConnectionsSVG(viewport);
        svg.innerHTML = '';

        const filteredQuests = this.getFilteredQuests();

        // 使用改进的层次布局算法
        const layout = this.calculateQuestLayout(filteredQuests);

        // 渲染任务节点
        filteredQuests.forEach((quest, index) => {
            const questNode = this.createQuestNode(quest, layout[quest.id] || { x: 100 + (index % 5) * 300, y: 200 + Math.floor(index / 5) * 180 });
            viewport.appendChild(questNode);
        });

        // 渲染分组标题（在节点渲染后，这样可以根据实际位置计算）
        this.renderQuestGroupTitles(filteredQuests, content, layout);

        // 渲染连接线
        this.renderQuestConnections(filteredQuests, svg, layout);

        // 更新统计信息
        this.updateQuestFlowStats(filteredQuests);
    }

    initializeQuestFlowView(container) {
        // 清空容器但保留原有结构
        container.innerHTML = '';



        // 创建内容区域
        const content = document.createElement('div');
        content.className = 'quest-flow-content';

        // 创建可拖动的视口
        const viewport = document.createElement('div');
        viewport.className = 'quest-flow-viewport';
        content.appendChild(viewport);

        // 添加到容器
        container.appendChild(content);

        // 在DOM结构完成后添加拖动功能
        this.initializeDragFunctionality(content, viewport);
    }

    initializeDragFunctionality(content, viewport) {
        let isDragging = false;
        let startX = 0;
        let startY = 0;
        let currentTranslateX = 0;
        let currentTranslateY = 0;

        // 鼠标按下事件
        content.addEventListener('mousedown', (e) => {
            // 如果点击的是任务节点或其子元素，不启动拖动
            if (e.target.closest('.quest-node')) {
                return;
            }

            isDragging = true;
            startX = e.clientX - currentTranslateX;
            startY = e.clientY - currentTranslateY;
            content.style.cursor = 'grabbing';
            e.preventDefault();
        });

        // 鼠标移动事件
        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            currentTranslateX = e.clientX - startX;
            currentTranslateY = e.clientY - startY;

            viewport.style.transform = `translate(${currentTranslateX}px, ${currentTranslateY}px)`;
            e.preventDefault();
        });

        // 鼠标释放事件
        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                content.style.cursor = 'grab';
            }
        });

        // 防止拖动时选中文本
        content.addEventListener('selectstart', (e) => {
            if (isDragging) {
                e.preventDefault();
            }
        });

        // 添加重置视图按钮功能
        setTimeout(() => {
            const resetViewBtn = document.querySelector('#reset-view');
            if (resetViewBtn) {
                resetViewBtn.addEventListener('click', () => {
                    currentTranslateX = 0;
                    currentTranslateY = 0;
                    viewport.style.transform = 'translate(0px, 0px)';
                    viewport.style.transition = 'transform 0.3s ease';
                    setTimeout(() => {
                        viewport.style.transition = 'transform 0.1s ease-out';
                    }, 300);
                });
            }
        }, 100);
    }

    createQuestConnectionsSVG(container) {
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.setAttribute('class', 'quest-connections');
        svg.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 25;';

        // 添加箭头标记
        const defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
        const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
        marker.setAttribute('id', 'quest-arrow');
        marker.setAttribute('markerWidth', '10');
        marker.setAttribute('markerHeight', '10');
        marker.setAttribute('refX', '8');
        marker.setAttribute('refY', '3');
        marker.setAttribute('orient', 'auto');

        const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
        polygon.setAttribute('points', '0,0 0,6 9,3');
        polygon.setAttribute('fill', '#666');

        marker.appendChild(polygon);
        defs.appendChild(marker);
        svg.appendChild(defs);

        container.appendChild(svg);
        return svg;
    }

    updateQuestFlowStats(quests) {
        const visibleCount = document.getElementById('quest-visible-count');
        if (visibleCount) {
            visibleCount.textContent = quests.length;
        }
    }

    calculateQuestLayout(quests) {
        const layout = {};
        const nodeWidth = 220; // 节点宽度
        const nodeHeight = 120; // 节点高度
        const branchSpacing = 280; // 分支间距
        const levelSpacing = 180; // 层级间距
        const siblingSpacing = 60; // 同级节点间距

        console.log('开始计算分支树布局，任务数量:', quests.length);

        // 构建任务依赖关系图
        const questMap = {};
        const children = {}; // 子任务映射
        const parents = {}; // 父任务映射

        quests.forEach(quest => {
            questMap[quest.id] = quest;
            children[quest.id] = [];
            parents[quest.id] = [];
        });

        // 建立父子关系
        quests.forEach(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            prerequisites.forEach(prereqId => {
                if (questMap[prereqId]) {
                    children[prereqId].push(quest.id);
                    parents[quest.id].push(prereqId);
                }
            });
        });

        // 找到根节点（没有前置任务的）
        const rootQuests = quests.filter(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            return prerequisites.length === 0;
        });

        console.log('找到根任务:', rootQuests.map(q => `${q.id}:${q.title}`));

        // 按任务类型分组根节点
        const rootGroups = {
            main: rootQuests.filter(q => q.type === 'main'),
            side: rootQuests.filter(q => q.type === 'side'),
            daily: rootQuests.filter(q => q.type === 'daily'),
            other: rootQuests.filter(q => q.type !== 'main' && q.type !== 'side' && q.type !== 'daily')
        };

        let currentX = 150; // 增加左边距，确保任务节点不被侧边栏遮挡
        const startY = 100; // 减少起始Y坐标，因为没有工具栏了

        // 为每个分组计算分支树布局
        Object.keys(rootGroups).forEach(groupType => {
            if (rootGroups[groupType].length > 0) {
                console.log(`处理${groupType}分组，根任务数:`, rootGroups[groupType].length);

                rootGroups[groupType].forEach((rootQuest, index) => {
                    const branchLayout = this.calculateBranchLayout(
                        rootQuest,
                        questMap,
                        children,
                        currentX,
                        startY,
                        nodeWidth,
                        levelSpacing,
                        siblingSpacing
                    );

                    Object.assign(layout, branchLayout.positions);
                    currentX = branchLayout.maxX + branchSpacing;
                });
            }
        });

        // 处理孤立任务（没有被包含在任何分支中的）
        const processedIds = new Set(Object.keys(layout).map(id => parseInt(id)));
        const orphanQuests = quests.filter(quest => !processedIds.has(quest.id));

        if (orphanQuests.length > 0) {
            console.log('处理孤立任务:', orphanQuests.length);
            let orphanX = currentX;
            let orphanY = startY; // 使用相同的startY，已经调整为200

            orphanQuests.forEach((quest, index) => {
                layout[quest.id] = {
                    x: orphanX + (index % 3) * (nodeWidth + siblingSpacing),
                    y: orphanY + Math.floor(index / 3) * (nodeHeight + 40)
                };
            });
        }

        console.log('分支树布局计算完成:', layout);
        return layout;
    }

    // 分支树布局算法 - 递归计算每个分支的位置
    calculateBranchLayout(rootQuest, questMap, children, startX, startY, nodeWidth, levelSpacing, siblingSpacing) {
        const positions = {};
        const processed = new Set();

        // 使用深度优先搜索计算子树的宽度
        const calculateSubtreeWidth = (questId, level = 0) => {
            if (processed.has(questId)) return 0;

            const childIds = children[questId] || [];
            if (childIds.length === 0) {
                return nodeWidth; // 叶子节点宽度
            }

            let totalChildWidth = 0;
            childIds.forEach(childId => {
                totalChildWidth += calculateSubtreeWidth(childId, level + 1);
            });

            // 子节点间距
            const childSpacing = (childIds.length - 1) * siblingSpacing;
            return Math.max(nodeWidth, totalChildWidth + childSpacing);
        };

        // 递归布局函数
        const layoutBranch = (questId, x, y, level = 0) => {
            if (processed.has(questId) || !questMap[questId]) return x;

            processed.add(questId);

            const quest = questMap[questId];
            const childIds = children[questId] || [];

            // 计算当前节点位置
            positions[questId] = { x, y };

            console.log(`布局任务 ${questId}:${quest.title} 在 (${x}, ${y}), 子任务数: ${childIds.length}`);

            if (childIds.length === 0) {
                return x + nodeWidth;
            }

            // 计算所有子节点的总宽度
            let totalChildWidth = 0;
            const childWidths = [];

            childIds.forEach(childId => {
                const childWidth = calculateSubtreeWidth(childId, level + 1);
                childWidths.push(childWidth);
                totalChildWidth += childWidth;
            });

            // 添加子节点间距
            const totalSpacing = (childIds.length - 1) * siblingSpacing;
            const totalWidth = totalChildWidth + totalSpacing;

            // 计算子节点的起始位置（居中对齐）
            const childStartX = x + (nodeWidth - totalWidth) / 2;

            let currentChildX = childStartX;
            let maxChildX = currentChildX;

            // 递归布局子节点
            childIds.forEach((childId, index) => {
                const childX = layoutBranch(childId, currentChildX, y + levelSpacing, level + 1);
                maxChildX = Math.max(maxChildX, childX);
                currentChildX += childWidths[index] + siblingSpacing;
            });

            return Math.max(x + nodeWidth, maxChildX);
        };

        // 开始布局
        const maxX = layoutBranch(rootQuest.id, startX, startY);

        return {
            positions,
            maxX
        };
    }

    // 层次布局算法 - 用于主线任务
    calculateHierarchicalLayout(quests, startX, startY, nodeWidth, levelSpacing, nodeSpacing) {
        const positions = {};
        const levels = {};
        const processed = new Set();

        // 找到根任务（没有前置任务的）
        const rootQuests = quests.filter(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            return prerequisites.length === 0;
        });

        // 如果没有根任务，选择第一个作为根任务
        if (rootQuests.length === 0 && quests.length > 0) {
            rootQuests.push(quests[0]);
        }

        // 按层级分组
        const levelGroups = {};
        let currentLevel = 0;
        let currentQuests = [...rootQuests];

        while (currentQuests.length > 0) {
            const nextQuests = [];
            levelGroups[currentLevel] = [];

            currentQuests.forEach(quest => {
                if (!processed.has(quest.id)) {
                    levels[quest.id] = currentLevel;
                    levelGroups[currentLevel].push(quest);
                    processed.add(quest.id);

                    // 找到依赖此任务的任务
                    const dependents = quests.filter(q => {
                        const prerequisites = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
                        return prerequisites.includes(quest.id) && !processed.has(q.id);
                    });

                    nextQuests.push(...dependents);
                }
            });

            currentQuests = nextQuests;
            currentLevel++;
        }

        // 为每层的任务计算位置
        let maxY = startY;
        Object.keys(levelGroups).forEach(level => {
            const levelNum = parseInt(level);
            const questsInLevel = levelGroups[level];
            const y = startY + levelNum * levelSpacing;

            // 计算这一层的总宽度，居中对齐
            const totalWidth = questsInLevel.length * nodeWidth + (questsInLevel.length - 1) * nodeSpacing;
            const levelStartX = startX + Math.max(0, (800 - totalWidth) / 2);

            questsInLevel.forEach((quest, index) => {
                positions[quest.id] = {
                    x: levelStartX + index * (nodeWidth + nodeSpacing),
                    y: y
                };
            });

            maxY = Math.max(maxY, y + 100); // 100是节点高度
        });

        // 处理未处理的任务（孤立任务）
        let orphanIndex = 0;
        quests.forEach(quest => {
            if (!positions[quest.id]) {
                positions[quest.id] = {
                    x: startX + (orphanIndex % 3) * (nodeWidth + nodeSpacing),
                    y: maxY + Math.floor(orphanIndex / 3) * 120
                };
                orphanIndex++;
            }
        });

        if (orphanIndex > 0) {
            maxY += Math.ceil(orphanIndex / 3) * 120;
        }

        return { positions, maxY };
    }

    // 支线任务布局算法 - 按前置任务分组
    calculateSideQuestLayout(quests, startX, startY, nodeWidth, nodeHeight, nodeSpacing) {
        const positions = {};

        // 按前置任务数量分组
        const groups = {
            independent: [], // 独立支线（无前置）
            dependent: []    // 有前置的支线
        };

        quests.forEach(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            if (prerequisites.length === 0) {
                groups.independent.push(quest);
            } else {
                groups.dependent.push(quest);
            }
        });

        let currentY = startY;

        // 独立支线任务 - 网格布局
        if (groups.independent.length > 0) {
            const cols = Math.min(4, groups.independent.length);
            groups.independent.forEach((quest, index) => {
                const col = index % cols;
                const row = Math.floor(index / cols);
                positions[quest.id] = {
                    x: startX + col * (nodeWidth + nodeSpacing),
                    y: currentY + row * (nodeHeight + 20)
                };
            });
            currentY += Math.ceil(groups.independent.length / cols) * (nodeHeight + 20) + 50;
        }

        // 有前置的支线任务 - 按前置任务分组
        if (groups.dependent.length > 0) {
            const dependentGroups = {};

            groups.dependent.forEach(quest => {
                const prerequisites = JSON.parse(quest.prerequisite_quests);
                const key = prerequisites.sort().join(',');
                if (!dependentGroups[key]) {
                    dependentGroups[key] = [];
                }
                dependentGroups[key].push(quest);
            });

            Object.values(dependentGroups).forEach(group => {
                const cols = Math.min(3, group.length);
                group.forEach((quest, index) => {
                    const col = index % cols;
                    const row = Math.floor(index / cols);
                    positions[quest.id] = {
                        x: startX + col * (nodeWidth + nodeSpacing),
                        y: currentY + row * (nodeHeight + 20)
                    };
                });
                currentY += Math.ceil(group.length / cols) * (nodeHeight + 20) + 30;
            });
        }

        return { positions, maxY: currentY };
    }

    // 网格布局算法 - 用于日常任务和其他任务
    calculateGridLayout(quests, startX, startY, nodeWidth, nodeHeight, nodeSpacing, cols) {
        const positions = {};

        quests.forEach((quest, index) => {
            const col = index % cols;
            const row = Math.floor(index / cols);
            positions[quest.id] = {
                x: startX + col * (nodeWidth + nodeSpacing),
                y: startY + row * (nodeHeight + 20)
            };
        });

        const maxY = startY + Math.ceil(quests.length / cols) * (nodeHeight + 20);
        return { positions, maxY };
    }

    // 渲染任务分组标题 - 适应分支树布局
    renderQuestGroupTitles(quests, container, layout) {
        // 清理之前的标题和图例
        const existingTitles = container.querySelectorAll('.quest-branch-group-title, .quest-branch-title, .quest-branch-legend');
        existingTitles.forEach(el => el.remove());

        // 不再渲染任何标题和图例，直接返回
        return;

        // 按任务类型分组
        const questGroups = {
            main: quests.filter(q => q.type === 'main'),
            side: quests.filter(q => q.type === 'side'),
            daily: quests.filter(q => q.type === 'daily'),
            other: quests.filter(q => q.type !== 'main' && q.type !== 'side' && q.type !== 'daily')
        };

        const groupTitles = {
            main: '主线任务分支',
            side: '支线任务分支',
            daily: '日常任务分支',
            other: '其他任务分支'
        };

        const groupColors = {
            main: '#ff6b6b',
            side: '#4ecdc4',
            daily: '#45b7d1',
            other: '#96ceb4'
        };

        // 找到根任务来确定分支起始位置
        const rootQuests = quests.filter(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            return prerequisites.length === 0;
        });

        const rootGroups = {
            main: rootQuests.filter(q => q.type === 'main'),
            side: rootQuests.filter(q => q.type === 'side'),
            daily: rootQuests.filter(q => q.type === 'daily'),
            other: rootQuests.filter(q => q.type !== 'main' && q.type !== 'side' && q.type !== 'daily')
        };

        // 根据实际布局位置计算标题位置
        const titleY = 80; // 工具栏下方
        const branchTitleY = 120; // 分支标题位置

        Object.keys(rootGroups).forEach(groupType => {
            if (rootGroups[groupType].length > 0) {
                // 计算该分组的X坐标范围
                const groupRootPositions = rootGroups[groupType]
                    .map(quest => layout[quest.id])
                    .filter(pos => pos);

                if (groupRootPositions.length === 0) return;

                const minX = Math.min(...groupRootPositions.map(pos => pos.x));
                const maxX = Math.max(...groupRootPositions.map(pos => pos.x));
                const centerX = (minX + maxX) / 2;

                // 为每个分组添加一个总标题
                const groupTitleElement = document.createElement('div');
                groupTitleElement.className = 'quest-branch-group-title';
                groupTitleElement.style.cssText = `
                    position: absolute;
                    left: ${centerX - 60}px;
                    top: ${titleY}px;
                    font-size: 16px;
                    font-weight: bold;
                    color: ${groupColors[groupType]};
                    background: rgba(255, 255, 255, 0.95);
                    padding: 6px 12px;
                    border-radius: 15px;
                    border: 2px solid ${groupColors[groupType]};
                    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
                    z-index: 100;
                    text-align: center;
                    min-width: 120px;
                `;
                groupTitleElement.textContent = `${groupTitles[groupType]}`;
                container.appendChild(groupTitleElement);

                // 为每个根任务添加分支标题
                rootGroups[groupType].forEach((rootQuest, index) => {
                    const questPos = layout[rootQuest.id];
                    if (!questPos) return;

                    const branchTitleElement = document.createElement('div');
                    branchTitleElement.className = 'quest-branch-title';
                    branchTitleElement.style.cssText = `
                        position: absolute;
                        left: ${questPos.x}px;
                        top: ${branchTitleY}px;
                        font-size: 12px;
                        color: ${groupColors[groupType]};
                        background: rgba(255, 255, 255, 0.8);
                        padding: 4px 8px;
                        border-radius: 10px;
                        border: 1px solid ${groupColors[groupType]}80;
                        z-index: 99;
                        max-width: 200px;
                        text-align: center;
                        font-weight: 500;
                    `;
                    branchTitleElement.textContent = `${rootQuest.title}`;
                    branchTitleElement.title = `分支根任务: ${rootQuest.title}`;
                    container.appendChild(branchTitleElement);
                });
            }
        });

        // 只添加一个图例说明
        const existingLegend = container.querySelector('.quest-branch-legend');
        if (!existingLegend) {
            const legendElement = document.createElement('div');
            legendElement.className = 'quest-branch-legend';
            legendElement.style.cssText = `
                position: absolute;
                right: 20px;
                top: 20px;
                background: rgba(255, 255, 255, 0.95);
                padding: 12px;
                border-radius: 8px;
                border: 1px solid #ddd;
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
                z-index: 100;
                font-size: 12px;
                line-height: 1.5;
            `;

            legendElement.innerHTML = `
                <div style="font-weight: bold; margin-bottom: 8px; color: #333;">任务分支图例</div>
                <div style="color: #ff6b6b;"><span style="font-weight: bold;">■</span> 主线任务分支</div>
                <div style="color: #4ecdc4;"><span style="font-weight: bold;">▲</span> 支线任务分支</div>
                <div style="color: #45b7d1;"><span style="font-weight: bold;">●</span> 日常任务分支</div>
                <div style="color: #96ceb4;"><span style="font-weight: bold;">◆</span> 其他任务分支</div>
                <div style="margin-top: 8px; color: #666; font-size: 11px;">
                    <div><span style="color: #ffd700;">★</span> 根任务 <span style="color: #007bff;">◆</span> 分支节点 <span style="color: #28a745;">●</span> 叶子节点</div>
                    <div style="margin-top: 4px;">每个分支以根任务为起点，向下展开所有依赖任务</div>
                </div>
            `;
            container.appendChild(legendElement);
        }
    }

    // 计算分组高度
    calculateGroupHeight(groupQuests) {
        if (groupQuests.length === 0) return 0;

        const groupType = groupQuests[0].type;

        if (groupType === 'main') {
            // 主线任务使用层次布局，高度取决于层级数
            const maxLevel = this.calculateMaxLevel(groupQuests);
            return Math.max(200, (maxLevel + 1) * 150 + 100);
        } else if (groupType === 'side') {
            // 支线任务分为独立和有前置的
            const independent = groupQuests.filter(q => {
                const prerequisites = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
                return prerequisites.length === 0;
            });
            const dependent = groupQuests.filter(q => {
                const prerequisites = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
                return prerequisites.length > 0;
            });

            let height = 0;
            if (independent.length > 0) {
                height += Math.ceil(independent.length / 4) * 140 + 50;
            }
            if (dependent.length > 0) {
                // 简化计算，假设平均每组3个任务
                const groups = Math.ceil(dependent.length / 3);
                height += groups * 170;
            }
            return Math.max(200, height);
        } else {
            // 日常任务和其他任务使用网格布局
            const cols = 4;
            const rows = Math.ceil(groupQuests.length / cols);
            return Math.max(200, rows * 140 + 50);
        }
    }

    // 计算主线任务的最大层级
    calculateMaxLevel(quests) {
        const levels = {};
        const processed = new Set();

        // 找到根任务
        const rootQuests = quests.filter(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            return prerequisites.length === 0;
        });

        if (rootQuests.length === 0) return 0;

        let maxLevel = 0;
        let currentLevel = 0;
        let currentQuests = [...rootQuests];

        while (currentQuests.length > 0) {
            const nextQuests = [];

            currentQuests.forEach(quest => {
                if (!processed.has(quest.id)) {
                    levels[quest.id] = currentLevel;
                    processed.add(quest.id);
                    maxLevel = Math.max(maxLevel, currentLevel);

                    // 找到依赖此任务的任务
                    const dependents = quests.filter(q => {
                        const prerequisites = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
                        return prerequisites.includes(quest.id) && !processed.has(q.id);
                    });

                    nextQuests.push(...dependents);
                }
            });

            currentQuests = nextQuests;
            currentLevel++;
        }

        return maxLevel;
    }

    createQuestNode(quest, position) {
        const node = document.createElement('div');

        // 确定节点类型
        const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
        const isRoot = prerequisites.length === 0;

        // 检查是否是分支节点（有子任务）
        const hasChildren = this.data.quests.some(q => {
            const prereqs = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
            return prereqs.includes(quest.id);
        });

        const isLeaf = !hasChildren;
        const isBranch = hasChildren && !isRoot;

        // 设置节点类名
        let nodeClasses = `quest-node ${quest.type}`;
        if (isRoot) nodeClasses += ' root';
        if (isLeaf) nodeClasses += ' leaf';
        if (isBranch) nodeClasses += ' branch';

        node.className = nodeClasses;
        node.dataset.questId = quest.id;
        // 添加偏移量确保不被侧边栏遮挡
        node.style.left = (position.x + 0) + 'px'; // 暂时不加偏移，先看蓝色边框位置
        node.style.top = position.y + 'px';

        const giverNpc = this.data.npcs.find(npc => npc.id == quest.giver_npc_id);
        const receiverNpc = this.data.npcs.find(npc => npc.id == quest.receiver_npc_id);

        // 计算奖励信息
        const rewardItems = quest.reward_items ? JSON.parse(quest.reward_items) : [];
        const hasRewards = quest.reward_gold > 0 || quest.reward_exp > 0 || rewardItems.length > 0;

        // 生成奖励物品文本
        const rewardItemsText = rewardItems.length > 0 ?
            rewardItems.map(item => {
                const itemId = item.item_id || item.id || item.template_id;
                const itemName = this.getItemName(itemId);
                const quantity = item.quantity || 1;
                return quantity > 1 ? `${itemName}×${quantity}` : itemName;
            }).join(', ') : '';

        // 任务状态（这里可以根据实际需求调整）
        const questStatus = this.getQuestStatus(quest);

        node.innerHTML = `
            <div class="quest-status ${questStatus}"></div>
            <div class="quest-title">${quest.title}</div>
            <span class="quest-type ${quest.type}">${this.getQuestTypeText(quest.type)}</span>
            <div class="quest-level">等级: <strong>${quest.min_level}</strong>${quest.is_repeatable ? ' (可重复)' : ''}</div>
            <div class="quest-npcs">
                <div>发布: <strong>${giverNpc ? giverNpc.name : '未知'}</strong></div>
                ${receiverNpc && receiverNpc.id !== quest.giver_npc_id ? `<div>接收: <strong>${receiverNpc.name}</strong></div>` : ''}
            </div>
            ${prerequisites.length > 0 ? `
                <div class="quest-prerequisites">
                    前置: ${prerequisites.length} 个任务
                </div>
            ` : ''}
            ${hasRewards ? `
                <div class="quest-rewards">
                    奖励: ${quest.reward_exp > 0 ? `<strong>${quest.reward_exp}</strong>经验 ` : ''}${quest.reward_gold > 0 ? `<strong>${quest.reward_gold}</strong>金币 ` : ''}${rewardItemsText ? `<strong>${rewardItemsText}</strong>` : ''}
                </div>
            ` : ''}
        `;

        node.addEventListener('click', (e) => {
            this.showQuestDetails(quest, e);
        });

        // 添加双击编辑功能
        node.addEventListener('dblclick', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.openQuestEditModal(quest);
        });

        // 添加悬停效果
        node.addEventListener('mouseenter', () => {
            this.highlightQuestConnections(quest.id, true);
        });

        node.addEventListener('mouseleave', () => {
            this.highlightQuestConnections(quest.id, false);
        });

        return node;
    }

    getQuestStatus(quest) {
        // 这里可以根据实际的任务状态逻辑来判断
        // 目前使用简单的逻辑作为示例
        const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];

        if (prerequisites.length === 0) {
            return 'available'; // 可接受
        } else {
            // 检查前置任务是否完成（这里简化处理）
            return 'locked'; // 锁定
        }
    }

    highlightQuestConnections(questId, highlight) {
        const svg = document.querySelector('.quest-connections');
        if (!svg) return;

        const lines = svg.querySelectorAll(`[data-from-quest="${questId}"], [data-to-quest="${questId}"]`);
        lines.forEach(line => {
            if (highlight) {
                line.style.stroke = '#007bff';
                line.style.strokeWidth = '3';
                line.style.opacity = '1';
            } else {
                line.style.stroke = '#666';
                line.style.strokeWidth = '2';
                line.style.opacity = '0.6';
            }
        });

        // 高亮相关节点
        const relatedNodes = document.querySelectorAll(`[data-quest-id]`);
        relatedNodes.forEach(node => {
            const nodeQuestId = node.dataset.questId;
            const quest = this.data.quests.find(q => q.id == nodeQuestId);
            if (!quest) return;

            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
            const isRelated = nodeQuestId == questId || prerequisites.includes(parseInt(questId));

            if (highlight && isRelated) {
                node.style.transform = 'translateY(-3px) scale(1.05)';
                node.style.zIndex = '20';
            } else if (!highlight) {
                node.style.transform = '';
                node.style.zIndex = '10';
            }
        });
    }

    renderQuestConnections(quests, svg, layout) {
        // 确保SVG有defs和箭头标记
        let defs = svg.querySelector('defs');
        if (!defs) {
            defs = document.createElementNS('http://www.w3.org/2000/svg', 'defs');
            const marker = document.createElementNS('http://www.w3.org/2000/svg', 'marker');
            marker.setAttribute('id', 'quest-arrow');
            marker.setAttribute('markerWidth', '10');
            marker.setAttribute('markerHeight', '10');
            marker.setAttribute('refX', '8');
            marker.setAttribute('refY', '3');
            marker.setAttribute('orient', 'auto');

            const polygon = document.createElementNS('http://www.w3.org/2000/svg', 'polygon');
            polygon.setAttribute('points', '0,0 0,6 9,3');
            polygon.setAttribute('fill', '#666');

            marker.appendChild(polygon);
            defs.appendChild(marker);
            svg.appendChild(defs);
        }

        // 清除之前的连接线，但保留defs
        const paths = svg.querySelectorAll('path');
        paths.forEach(path => path.remove());

        quests.forEach(quest => {
            const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];

            prerequisites.forEach(prereqId => {
                const prereqQuest = quests.find(q => q.id == prereqId);
                if (prereqQuest && layout[prereqQuest.id] && layout[quest.id]) {
                    const line = this.createQuestConnectionLine(
                        layout[prereqQuest.id],
                        layout[quest.id],
                        prereqQuest.type,
                        quest.type
                    );
                    if (line) {
                        svg.appendChild(line);
                    }
                }
            });
        });
    }

    createQuestConnectionLine(fromPos, toPos, fromType, toType) {
        // 计算连接点
        const nodeWidth = 200;
        const nodeHeight = 100;

        const x1 = fromPos.x + nodeWidth / 2;
        const y1 = fromPos.y + nodeHeight;
        const x2 = toPos.x + nodeWidth / 2;
        const y2 = toPos.y;

        // 创建路径元素而不是直线，支持曲线连接
        const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

        // 计算控制点，创建贝塞尔曲线
        const controlY = y1 + (y2 - y1) / 2;
        const pathData = `M ${x1} ${y1} C ${x1} ${controlY} ${x2} ${controlY} ${x2} ${y2}`;

        path.setAttribute('d', pathData);
        path.setAttribute('fill', 'none');
        path.setAttribute('marker-end', 'url(#quest-arrow)');

        // 统一的连接线样式，不依赖任务类型
        path.setAttribute('class', 'quest-connection');
        path.setAttribute('stroke', '#007bff');
        path.setAttribute('stroke-width', '2');
        path.setAttribute('opacity', '0.8');

        return path;
    }

    // 保留原有方法以兼容其他功能
    createConnectionLine(fromQuest, toQuest, type) {
        const fromNode = document.querySelector(`[data-quest-id="${fromQuest.id}"]`);
        const toNode = document.querySelector(`[data-quest-id="${toQuest.id}"]`);

        if (!fromNode || !toNode) return null;

        const fromRect = fromNode.getBoundingClientRect();
        const toRect = toNode.getBoundingClientRect();
        const container = document.querySelector('.quest-flow-container');
        const containerRect = container.getBoundingClientRect();

        const x1 = fromRect.left - containerRect.left + fromRect.width / 2;
        const y1 = fromRect.top - containerRect.top + fromRect.height;
        const x2 = toRect.left - containerRect.left + toRect.width / 2;
        const y2 = toRect.top - containerRect.top;

        const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');
        line.setAttribute('x1', x1);
        line.setAttribute('y1', y1);
        line.setAttribute('x2', x2);
        line.setAttribute('y2', y2);
        line.setAttribute('stroke', '#666');
        line.setAttribute('stroke-width', '2');
        line.setAttribute('opacity', '0.6');
        line.setAttribute('marker-end', 'url(#quest-arrow)');
        line.dataset.fromQuest = fromQuest.id;
        line.dataset.toQuest = toQuest.id;

        return line;
    }

    renderDialogueTreeView() {
        const container = document.getElementById('dialogue-tree-container');
        container.innerHTML = '';

        // 创建分组筛选和对话树选择器
        const selector = document.createElement('div');
        selector.className = 'dialogue-tree-selector';
        selector.innerHTML = `
            <div style="padding: 15px; background: #f8f9fa; border-bottom: 1px solid #ddd;">
                <div style="margin-bottom: 10px;">
                    <label for="dialogue-group-filter" style="margin-right: 10px; font-weight: bold;">分组筛选:</label>
                    <select id="dialogue-group-filter" style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px; margin-right: 15px;">
                        <option value="">全部分组</option>
                        ${this.getDialogueGroups().map(g => `<option value="${g.id}">${g.name} (${g.count}个)</option>`).join('')}
                    </select>
                </div>
                <div>
                    <label for="dialogue-tree-select" style="margin-right: 10px; font-weight: bold;">选择对话树:</label>
                    <select id="dialogue-tree-select" style="padding: 5px 10px; border: 1px solid #ddd; border-radius: 4px;">
                        <option value="">请选择对话树</option>
                        ${this.data.dialogues.map(d => `<option value="${d.id}" data-group="${d.group_id || ''}">${d.name} (${d.node_count}个节点)</option>`).join('')}
                    </select>
                    <button id="load-dialogue-tree" style="margin-left: 10px; padding: 5px 15px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">加载</button>
                </div>
            </div>
        `;
        container.appendChild(selector);

        // 创建对话树显示区域
        const treeDisplay = document.createElement('div');
        treeDisplay.className = 'dialogue-tree-display';
        treeDisplay.style.cssText = 'position: relative; min-height: 600px; height: auto; overflow: hidden; padding: 20px;';
        container.appendChild(treeDisplay);

        // 绑定分组筛选事件
        document.getElementById('dialogue-group-filter').addEventListener('change', (e) => {
            this.filterDialoguesByGroup(e.target.value);
        });

        // 绑定事件
        document.getElementById('load-dialogue-tree').addEventListener('click', () => {
            const treeId = document.getElementById('dialogue-tree-select').value;
            if (treeId) {
                this.loadDialogueTreeDetail(treeId);
            }
        });

        // 绑定选择器改变事件，自动加载对话树
        document.getElementById('dialogue-tree-select').addEventListener('change', (e) => {
            const treeId = e.target.value;
            if (treeId) {
                console.log('对话树选择器改变，加载对话树:', treeId);
                this.loadDialogueTreeDetail(treeId);
            }
        });

        // 如果有对话树数据，默认加载第一个
        if (this.data.dialogues.length > 0) {
            document.getElementById('dialogue-tree-select').value = this.data.dialogues[0].id;
            this.loadDialogueTreeDetail(this.data.dialogues[0].id);
        }
    }

    getDialogueGroups() {
        // 从对话树数据中提取分组信息
        const groups = new Map();

        this.data.dialogues.forEach(dialogue => {
            // 处理分组信息
            let groupId, groupName;

            if (dialogue.group_id && dialogue.group_name) {
                // 有分组
                groupId = dialogue.group_id;
                groupName = dialogue.group_name;
            } else {
                // 未分组
                groupId = 'ungrouped';
                groupName = '未分组';
            }

            if (!groups.has(groupId)) {
                groups.set(groupId, {
                    id: groupId,
                    name: groupName,
                    type: dialogue.group_type || 'other',
                    count: 0
                });
            }

            groups.get(groupId).count++;
        });

        console.log('提取的对话树分组:', Array.from(groups.values()));

        // 转换为数组并排序
        return Array.from(groups.values()).sort((a, b) => {
            // 未分组排在最后
            if (a.id === 'ungrouped' && b.id !== 'ungrouped') return 1;
            if (a.id !== 'ungrouped' && b.id === 'ungrouped') return -1;
            // 其他按名称排序
            return a.name.localeCompare(b.name, 'zh-Hans-CN');
        });
    }

    filterDialoguesByGroup(groupId) {
        console.log('筛选对话树分组:', groupId, '当前对话树数据:', this.data.dialogues.length);

        const dialogueSelect = document.getElementById('dialogue-tree-select');

        // 清空选择器（保留第一个选项）
        dialogueSelect.innerHTML = '<option value="">请选择对话树</option>';

        // 筛选对话树
        const filteredDialogues = this.data.dialogues.filter(dialogue => {
            if (!groupId) {
                // 显示所有对话树
                return true;
            } else if (groupId === 'ungrouped') {
                // 显示未分组的对话树
                return !dialogue.group_id || dialogue.group_id === null;
            } else {
                // 显示指定分组的对话树
                return dialogue.group_id == groupId;
            }
        });

        console.log(`筛选条件: ${groupId}, 筛选结果: ${filteredDialogues.length}个对话树`);

        // 重新填充选择器
        filteredDialogues.forEach(dialogue => {
            const option = document.createElement('option');
            option.value = dialogue.id;

            // 显示分组信息
            let displayName = dialogue.name;
            if (dialogue.group_name) {
                displayName = `[${dialogue.group_name}] ${dialogue.name}`;
            }
            displayName += ` (${dialogue.node_count}个节点)`;

            option.textContent = displayName;
            option.dataset.group = dialogue.group_id || '';
            dialogueSelect.appendChild(option);
        });

        // 清空当前显示
        const display = document.querySelector('.dialogue-tree-display');
        if (display) {
            display.innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">请选择对话树</div>';
        }
    }

    async loadDialogueTreeDetail(treeId) {
        const display = document.querySelector('.dialogue-tree-display');
        display.innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">加载中...</div>';

        try {
            const useTestData = window.location.search.includes('test=1');
            const testParam = useTestData ? '&test=1' : '';

            const response = await fetch(`api_quest_workflow.php?action=get_dialogue_tree_detail&tree_id=${treeId}${testParam}`);
            const data = await response.json();

            if (data.error) {
                display.innerHTML = `<div style="text-align: center; padding: 50px; color: #dc3545;">错误: ${data.error}</div>`;
                return;
            }

            // 存储关联数据供后续使用
            this.currentTreeData = data;
            this.currentDialogueTreeId = treeId;

            // 动态更新URL参数，但不刷新页面
            this.updateUrlParameter('dialogue_id', treeId);

            this.renderDialogueTreeDetail(data);

        } catch (error) {
            console.error('加载对话树详情失败:', error);
            display.innerHTML = '<div style="text-align: center; padding: 50px; color: #dc3545;">加载失败，请重试</div>';
        }
    }

    renderDialogueTreeDetail(data) {
        const display = document.querySelector('.dialogue-tree-display');
        display.innerHTML = '';

        // 创建信息面板
        const infoPanel = document.createElement('div');
        infoPanel.className = 'dialogue-info-panel';
        infoPanel.innerHTML = `
            <div style="background: white; border: 1px solid #ddd; border-radius: 8px; padding: 15px; margin-bottom: 20px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                <h3 style="margin-top: 0; color: #333;">${data.tree.name}</h3>
                <p style="color: #666; margin-bottom: 10px;">${data.tree.description || '无描述'}</p>
                <div style="display: flex; gap: 20px; font-size: 14px;">
                    <span><strong>节点数量:</strong> ${data.tree.node_count}</span>
                    <span><strong>关联NPC:</strong> ${data.npcs.length}</span>
                </div>
                ${data.npcs.length > 0 ? `
                    <div style="margin-top: 15px;">
                        <strong>使用此对话树的NPC (${data.npcs.length}个):</strong>
                        <div style="margin-top: 8px;">
                            ${data.npcs.map(npc => {
                                const dialogueTypeColor = npc.dialogue_type === '默认对话' ? '#28a745' : '#007bff';
                                const sceneInfo = npc.scene_name ? `${npc.scene_name}` : '未知场景';
                                return `
                                    <div style="display: inline-block; background: white; border: 1px solid #ddd; padding: 6px 10px; border-radius: 8px; margin: 3px; font-size: 13px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
                                        <strong>${npc.name}</strong>
                                        <span style="color: ${dialogueTypeColor}; font-size: 11px; margin-left: 5px;">[${npc.dialogue_type}]</span>
                                        <br>
                                        <span style="color: #666; font-size: 11px;">📍 ${sceneInfo}</span>
                                    </div>
                                `;
                            }).join('')}
                        </div>
                    </div>
                ` : `
                    <div style="margin-top: 15px; padding: 10px; background: #fff3cd; border: 1px solid #ffeaa7; border-radius: 6px;">
                        <span style="color: #856404; font-size: 13px;">⚠️ 此对话树暂未被任何NPC使用</span>
                    </div>
                `}
            </div>
        `;
        display.appendChild(infoPanel);

        // 创建对话流程图
        const flowContainer = document.createElement('div');
        flowContainer.className = 'dialogue-flow-container';
        flowContainer.style.cssText = 'position: relative; min-height: 600px; height: auto; background: #f8f9fa; border: 1px solid #ddd; border-radius: 8px; padding: 20px; overflow: hidden;';
        display.appendChild(flowContainer);

        // 渲染对话节点
        this.renderDialogueNodes(data.nodes, flowContainer);
    }

    renderDialogueNodes(nodes, container) {
        if (!nodes || nodes.length === 0) {
            container.innerHTML = '<div style="text-align: center; padding: 50px; color: #666;">此对话树没有节点</div>';
            return;
        }

        // 分析节点类型：is_player_choice=1表示下一个节点是玩家选择
        const analyzedNodes = this.analyzeNodeTypes(nodes);

        // 创建SVG容器用于连接线
        const svg = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
        svg.style.cssText = 'position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 1;';
        svg.innerHTML = `
            <defs>
                <marker id="dialogue-arrow" markerWidth="8" markerHeight="6" refX="7" refY="3" orient="auto">
                    <polygon points="0 0, 8 3, 0 6" fill="#666" />
                </marker>
            </defs>
        `;
        container.appendChild(svg);

        // 计算节点布局
        const layout = this.calculateDialogueLayout(analyzedNodes);

        // 渲染节点
        analyzedNodes.forEach(node => {
            const nodeElement = this.createDialogueNode(node, layout[node.id]);
            container.appendChild(nodeElement);
        });

        // 计算容器所需的最小尺寸
        const positions = Object.values(layout);
        if (positions.length > 0) {
            const maxX = Math.max(...positions.map(p => p.x)) + 280; // 节点宽度
            const maxY = Math.max(...positions.map(p => p.y)) + 120; // 节点高度

            // 设置容器最小尺寸
            container.style.minWidth = Math.max(800, maxX + 40) + 'px';
            container.style.minHeight = Math.max(600, maxY + 40) + 'px';

            // 同时设置SVG尺寸
            svg.style.width = container.style.minWidth;
            svg.style.height = container.style.minHeight;
        }

        // 渲染连接线
        this.renderDialogueConnections(analyzedNodes, svg, layout);
    }

    analyzeNodeTypes(nodes) {
        console.log('开始分析节点类型，节点数量:', nodes.length);

        // 创建节点映射
        const nodeMap = {};
        nodes.forEach(node => {
            nodeMap[node.id] = { ...node, actualType: 'npc' }; // 默认都是NPC
        });

        // 根据is_player_choice字段标记玩家选择节点
        nodes.forEach(node => {
            console.log(`节点 ${node.id}: is_player_choice=${node.is_player_choice}, next_node_ids=${node.next_node_ids}`);

            if (node.is_player_choice == 1 && node.next_node_ids) {
                try {
                    const nextIds = JSON.parse(node.next_node_ids);
                    if (Array.isArray(nextIds)) {
                        console.log(`节点 ${node.id} 的下一个节点是玩家选择:`, nextIds);
                        // 当前节点的下一个节点是玩家选择
                        nextIds.forEach(nextId => {
                            if (nodeMap[nextId]) {
                                nodeMap[nextId].actualType = 'player';
                                console.log(`标记节点 ${nextId} 为玩家选择`);
                            }
                        });
                    }
                } catch (e) {
                    console.warn('解析next_node_ids失败:', node.next_node_ids);
                }
            }
        });

        const result = Object.values(nodeMap);
        console.log('节点类型分析完成:', result.map(n => `${n.id}:${n.actualType}`));
        return result;
    }


    calculateDialogueLayout(nodes) {
        const layout = {};
        const nodeMap = {};
        const connections = {};
        const reverseConnections = {}; // 反向连接，用于找父节点

        // 创建节点映射和连接关系
        nodes.forEach(node => {
            nodeMap[node.id] = node;
            connections[node.id] = [];
            reverseConnections[node.id] = [];

            if (node.next_node_ids) {
                try {
                    const nextIds = JSON.parse(node.next_node_ids);
                    if (Array.isArray(nextIds)) {
                        connections[node.id] = nextIds.map(id => parseInt(id));
                        // 建立反向连接
                        nextIds.forEach(nextId => {
                            const id = parseInt(nextId);
                            if (!reverseConnections[id]) {
                                reverseConnections[id] = [];
                            }
                            reverseConnections[id].push(node.id);
                        });
                    }
                } catch (e) {
                    console.warn('解析next_node_ids失败:', node.next_node_ids);
                }
            }
        });

        // 找到根节点（没有父节点的节点）
        const rootNodes = nodes.filter(node =>
            !reverseConnections[node.id] || reverseConnections[node.id].length === 0
        );

        if (rootNodes.length === 0) {
            console.warn('没有找到根节点，使用第一个节点');
            rootNodes.push(nodes[0]);
        }

        console.log('找到根节点:', rootNodes.map(n => n.id));

        // 使用层次布局算法
        const levels = {};
        const processed = new Set();

        // 计算每个节点的层级
        const calculateLevels = (nodeId, level = 0) => {
            if (processed.has(nodeId) || !nodeMap[nodeId]) {
                return;
            }

            processed.add(nodeId);
            levels[nodeId] = level;

            const nextIds = connections[nodeId] || [];
            nextIds.forEach(nextId => {
                calculateLevels(nextId, level + 1);
            });
        };

        // 从根节点开始计算层级
        rootNodes.forEach(rootNode => {
            calculateLevels(rootNode.id, 0);
        });

        // 按层级分组节点
        const nodesByLevel = {};
        Object.keys(levels).forEach(nodeId => {
            const level = levels[nodeId];
            if (!nodesByLevel[level]) {
                nodesByLevel[level] = [];
            }
            nodesByLevel[level].push(parseInt(nodeId));
        });

        console.log('节点层级分布:', nodesByLevel);

        // 计算每层的布局
        const nodeWidth = 280;
        const nodeHeight = 150;
        const levelSpacing = 200; // 层级间距
        const nodeSpacing = 50; // 同层节点间距

        Object.keys(nodesByLevel).forEach(level => {
            const levelNodes = nodesByLevel[level];
            const levelNum = parseInt(level);

            // 计算这一层的总宽度
            const totalWidth = levelNodes.length * nodeWidth + (levelNodes.length - 1) * nodeSpacing;
            const startX = Math.max(50, (1200 - totalWidth) / 2); // 居中对齐，最小边距50px

            levelNodes.forEach((nodeId, index) => {
                layout[nodeId] = {
                    x: startX + index * (nodeWidth + nodeSpacing),
                    y: 50 + levelNum * levelSpacing
                };
            });
        });

        // 优化分支布局：对于有多个子节点的节点，调整子节点位置使其更好地分布
        Object.keys(connections).forEach(nodeId => {
            const nextIds = connections[nodeId];
            if (nextIds.length > 1) {
                // 获取父节点位置
                const parentPos = layout[nodeId];
                if (!parentPos) return;

                // 获取所有子节点的当前位置
                const childPositions = nextIds.map(id => layout[id]).filter(pos => pos);
                if (childPositions.length !== nextIds.length) return;

                // 计算子节点应该的分布范围
                const totalChildWidth = nextIds.length * nodeWidth + (nextIds.length - 1) * nodeSpacing;
                const childStartX = parentPos.x - (totalChildWidth - nodeWidth) / 2;

                // 重新分布子节点位置
                nextIds.forEach((childId, index) => {
                    if (layout[childId]) {
                        layout[childId].x = childStartX + index * (nodeWidth + nodeSpacing);
                    }
                });
            }
        });

        // 处理未处理的节点（孤立节点）
        let orphanX = 50;
        let orphanY = 50;
        nodes.forEach(node => {
            if (!layout[node.id]) {
                layout[node.id] = {
                    x: orphanX,
                    y: orphanY
                };
                orphanX += nodeWidth + nodeSpacing;
                if (orphanX > 1000) {
                    orphanX = 50;
                    orphanY += levelSpacing;
                }
            }
        });

        console.log('最终布局:', layout);
        return layout;
    }

    createDialogueNode(node, position) {
        const nodeElement = document.createElement('div');
        nodeElement.className = 'dialogue-node';
        nodeElement.dataset.nodeId = node.id;
        // 使用分析后的实际类型
        const isPlayerNode = node.actualType === 'player';

        // 根据实际类型设置样式
        let borderColor = isPlayerNode ? '#007bff' : '#28a745'; // 玩家蓝色，NPC绿色
        let bgGradient = isPlayerNode ? 'linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%)' : 'white';
        let typeLabel = isPlayerNode ? '玩家选择' : 'NPC';

        nodeElement.style.cssText = `
            position: absolute;
            left: ${position.x}px;
            top: ${position.y}px;
            width: 250px;
            min-height: 80px;
            background: ${bgGradient};
            border: 2px solid ${borderColor};
            border-radius: 8px;
            padding: 12px;
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            z-index: 2;
        `;

        // 解析条件和动作脚本
        let conditionInfo = '';
        let actionInfo = '';

        // 获取关联数据
        const items = this.currentTreeData?.items || [];
        const quests = this.currentTreeData?.quests || [];

        if (node.condition_script) {
            try {
                const conditions = JSON.parse(node.condition_script);
                conditionInfo = this.formatConditions(conditions);
            } catch (e) {
                conditionInfo = '<span style="color: #dc3545;">条件脚本格式错误</span>';
            }
        }

        if (node.action_script) {
            try {
                const actions = JSON.parse(node.action_script);
                actionInfo = this.formatActions(actions);
            } catch (e) {
                actionInfo = '<span style="color: #dc3545;">动作脚本格式错误</span>';
            }
        }

        nodeElement.innerHTML = `
            <div style="display: flex; align-items: center; margin-bottom: 8px;">
                <span style="background: ${borderColor}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 10px; font-weight: bold; margin-right: 8px;">
                    ${typeLabel}
                </span>
                <span style="font-size: 11px; color: #666;">ID: ${node.id}</span>
            </div>
            <div style="font-size: 13px; line-height: 1.4; margin-bottom: 8px; color: #333;">
                ${node.content}
            </div>
            ${conditionInfo ? `<div style="font-size: 11px; color: #fd7e14; margin-bottom: 4px;"><strong>条件:</strong> ${conditionInfo}</div>` : ''}
            ${actionInfo ? `<div style="font-size: 11px; color: #6f42c1; margin-bottom: 4px;"><strong>动作:</strong> ${actionInfo}</div>` : ''}
        `;

        // 悬停效果
        nodeElement.addEventListener('mouseenter', () => {
            nodeElement.style.transform = 'translateY(-2px)';
            nodeElement.style.boxShadow = '0 4px 12px rgba(0,0,0,0.2)';
        });

        nodeElement.addEventListener('mouseleave', () => {
            nodeElement.style.transform = 'translateY(0)';
            nodeElement.style.boxShadow = '0 2px 8px rgba(0,0,0,0.1)';
        });

        // 点击显示详情
        nodeElement.addEventListener('click', (e) => {
            this.showDialogueNodeDetails(node, e);
        });

        // 添加双击编辑功能
        nodeElement.addEventListener('dblclick', (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('对话节点双击事件触发');

            // 获取对话树信息
            const dialogueTree = this.getCurrentDialogueTree();
            console.log('获取到的对话树:', dialogueTree);
            console.log('当前视图:', this.currentView);
            console.log('当前对话树ID:', this.currentDialogueTreeId);

            if (dialogueTree) {
                console.log('准备打开对话树编辑模态框');
                this.openDialogueEditModal(dialogueTree);
            } else {
                console.warn('未找到当前对话树，无法打开编辑模态框');
                showToast('未找到当前对话树信息', 'error');
            }
        });

        return nodeElement;
    }

    renderDialogueConnections(nodes, svg, layout) {
        const nodeMap = {};
        nodes.forEach(node => {
            nodeMap[node.id] = node;
        });

        // 清除现有连接线（保留defs）
        const defs = svg.querySelector('defs');
        svg.innerHTML = '';
        if (defs) {
            svg.appendChild(defs);
        }

        let connectionCount = 0;

        // 为每个节点绘制连接线
        nodes.forEach(node => {
            if (!node.next_node_ids || !layout[node.id]) {
                return;
            }

            try {
                const nextIds = JSON.parse(node.next_node_ids);
                if (!Array.isArray(nextIds) || nextIds.length === 0) {
                    return;
                }

                nextIds.forEach((nextId, index) => {
                    const nextNodeId = parseInt(nextId);
                    const nextNode = nodeMap[nextNodeId];

                    if (nextNode && layout[nextNodeId]) {
                        const line = this.createDialogueConnection(
                            layout[node.id],
                            layout[nextNodeId],
                            index,
                            nextIds.length,
                            node.actualType === 'player' ? '#007bff' : '#28a745'
                        );

                        if (line) {
                            svg.appendChild(line);
                            connectionCount++;
                        }

                        console.log(`连接: ${node.id} (${node.actualType}) -> ${nextNodeId}`);
                    } else {
                        console.warn(`找不到目标节点: ${nextId}`, nextNode, layout[nextNodeId]);
                    }
                });
            } catch (e) {
                console.warn('解析next_node_ids失败:', node.next_node_ids, e);
            }
        });

        console.log(`总共渲染了 ${connectionCount} 条连接线，节点数: ${nodes.length}`);
        console.log('布局信息:', layout);
    }

    createDialogueConnection(fromPos, toPos, index, total, color = '#666') {
        // 计算连接点 - 节点宽度250px，高度至少80px
        const nodeWidth = 250;
        const nodeHeight = 80;

        const fromX = fromPos.x + nodeWidth / 2; // 源节点中心X
        const fromY = fromPos.y + nodeHeight;    // 源节点底部Y
        const toX = toPos.x + nodeWidth / 2;     // 目标节点中心X
        const toY = toPos.y;                     // 目标节点顶部Y

        // 如果是分支连接（多个子节点），使用曲线路径
        if (total > 1) {
            const path = document.createElementNS('http://www.w3.org/2000/svg', 'path');

            // 计算控制点，创建平滑的分支曲线
            const midY = fromY + (toY - fromY) * 0.5;
            const offsetX = (toX - fromX) * 0.3; // 水平偏移量

            // 使用三次贝塞尔曲线创建更平滑的连接
            const pathData = `M ${fromX} ${fromY} C ${fromX + offsetX} ${midY}, ${toX - offsetX} ${midY}, ${toX} ${toY}`;

            path.setAttribute('d', pathData);
            path.setAttribute('stroke', color);
            path.setAttribute('stroke-width', '2');
            path.setAttribute('fill', 'none');
            path.setAttribute('marker-end', 'url(#dialogue-arrow)');
            path.setAttribute('opacity', '0.8');

            return path;
        } else {
            // 单连接使用直线或简单曲线
            const line = document.createElementNS('http://www.w3.org/2000/svg', 'line');

            line.setAttribute('x1', fromX);
            line.setAttribute('y1', fromY);
            line.setAttribute('x2', toX);
            line.setAttribute('y2', toY);
            line.setAttribute('stroke', color);
            line.setAttribute('stroke-width', '2');
            line.setAttribute('marker-end', 'url(#dialogue-arrow)');

            return line;
        }
    }

    formatConditions(conditions) {
        if (!conditions) return '无';

        console.log('原始条件数据:', conditions);

        // 处理不同的条件格式
        let conditionList = [];
        let logicalOperator = 'AND';

        if (conditions.conditions && Array.isArray(conditions.conditions)) {
            // 标准格式：{logical_operator: "AND", conditions: [...]}
            conditionList = conditions.conditions;
            logicalOperator = conditions.logical_operator || 'AND';
        } else if (Array.isArray(conditions)) {
            // 直接数组格式
            conditionList = conditions;
        } else if (typeof conditions === 'object') {
            // 检查是否是单个条件对象
            if (conditions.type) {
                // 单个条件对象
                conditionList = [conditions];
            } else {
                // 可能是多个条件的键值对格式，需要特殊处理
                // 检查是否有logical_operator字段
                if (conditions.logical_operator) {
                    logicalOperator = conditions.logical_operator;
                }

                // 提取所有非logical_operator的字段作为条件
                conditionList = Object.entries(conditions)
                    .filter(([key, value]) => key !== 'logical_operator')
                    .map(([key, value]) => {
                        if (typeof value === 'object' && value.type) {
                            return value;
                        } else {
                            // 如果值不是对象或没有type字段，尝试解析
                            return { type: key, ...value };
                        }
                    });
            }
        }

        if (conditionList.length === 0) return '无';

        console.log('解析后的条件列表:', conditionList);
        console.log('逻辑操作符:', logicalOperator);

        // 获取关联数据
        const items = this.currentTreeData?.items || [];
        const quests = this.currentTreeData?.quests || [];

        const conditionTexts = conditionList.map(cond => {
            console.log('处理条件:', cond);

            switch (cond.type) {
                case 'quest_status':
                    // 处理字符串/数字类型匹配问题
                    let quest = quests.find(q => q.id === cond.quest_id);
                    if (!quest) {
                        quest = quests.find(q => q.id == cond.quest_id);
                    }
                    if (!quest) {
                        quest = quests.find(q => String(q.id) === String(cond.quest_id));
                    }
                    const questName = quest ? quest.title : `任务${cond.quest_id}`;
                    console.log(`任务状态条件匹配: quest_id=${cond.quest_id} (${typeof cond.quest_id}), 找到任务:`, quest);
                    return `<strong>${questName}</strong>状态为${cond.status}`;
                case 'has_item':
                    const item1 = items.find(i => i.id == cond.item_id);
                    const itemName1 = item1 ? item1.name : `物品${cond.item_id}`;
                    return `拥有<strong>${itemName1}</strong>数量≥${cond.quantity || cond.count || 1}`;
                case 'does_not_have_item':
                    const item2 = items.find(i => i.id == cond.item_id);
                    const itemName2 = item2 ? item2.name : `物品${cond.item_id}`;
                    return `不拥有<strong>${itemName2}</strong>`;
                case 'item_count':
                    const item3 = items.find(i => i.id == cond.item_id);
                    const itemName3 = item3 ? item3.name : `物品${cond.item_id}`;
                    return `拥有<strong>${itemName3}</strong>数量${cond.operator || '≥'}${cond.count || cond.quantity}`;
                case 'level':
                case 'player_level':
                    return `玩家等级${cond.operator || '≥'}${cond.level || cond.value}`;
                case 'player_attribute':
                    const attributeNames = {
                        'strength': '力量', 'agility': '敏捷', 'constitution': '体质', 'intelligence': '智力',
                        'attack': '攻击力', 'defense': '防御力', 'max_hp': '最大生命值', 'max_mp': '最大法力值'
                    };
                    const attrName = attributeNames[cond.attribute] || cond.attribute;
                    return `<strong>${attrName}</strong>${cond.operator || '≥'}${cond.value}`;
                default:
                    // 如果是未知类型，尝试显示更友好的信息
                    if (cond.item_id) {
                        const item = items.find(i => i.id == cond.item_id);
                        const itemName = item ? item.name : `物品${cond.item_id}`;
                        return `${cond.type}(<strong>${itemName}</strong>)`;
                    }
                    return `${cond.type}: ${JSON.stringify(cond).substring(0, 50)}...`;
            }
        });

        // 根据逻辑操作符组合条件文本
        if (conditionTexts.length === 1) {
            return conditionTexts[0];
        } else {
            const connector = logicalOperator === 'OR' ? ' 或 ' : ' 且 ';
            return conditionTexts.join(connector);
        }
    }

    formatActions(actions) {
        if (!actions) return '无';

        // 处理不同的动作格式
        let actionList = [];

        if (actions.actions && Array.isArray(actions.actions)) {
            // 标准格式：{actions: [...]}
            actionList = actions.actions;
        } else if (Array.isArray(actions)) {
            // 直接数组格式
            actionList = actions;
        } else if (typeof actions === 'object') {
            // 直接对象格式，可能是单个动作或多个动作的键值对
            if (actions.type) {
                // 单个动作对象
                actionList = [actions];
            } else {
                // 多个动作的键值对格式
                actionList = Object.entries(actions).map(([key, value]) => {
                    if (typeof value === 'object' && value.type) {
                        return value;
                    } else {
                        return { type: key, ...value };
                    }
                });
            }
        }

        if (actionList.length === 0) return '无';

        // 获取关联数据
        const items = this.currentTreeData?.items || [];
        const quests = this.currentTreeData?.quests || [];

        return actionList.map(action => {
            switch (action.type) {
                case 'start_quest':
                    const quest1 = quests.find(q => q.id == action.quest_id);
                    const questName1 = quest1 ? quest1.title : `任务${action.quest_id}`;
                    return `开始<strong>${questName1}</strong>`;
                case 'complete_quest':
                    const quest2 = quests.find(q => q.id == action.quest_id);
                    const questName2 = quest2 ? quest2.title : `任务${action.quest_id}`;
                    return `完成<strong>${questName2}</strong>`;
                case 'give_item':
                    const item1 = items.find(i => i.id == action.item_id);
                    const itemName1 = item1 ? item1.name : `物品${action.item_id}`;
                    return `给予<strong>${itemName1}</strong>x${action.count || action.quantity || 1}`;
                case 'take_item':
                case 'remove_item':
                    const item2 = items.find(i => i.id == action.item_id);
                    const itemName2 = item2 ? item2.name : `物品${action.item_id}`;
                    return `获取<strong>${itemName2}</strong>x${action.count || action.quantity || 1}`;
                case 'give_exp':
                    return `给予<strong>经验${action.exp || action.experience}</strong>`;
                case 'give_gold':
                    return `给予<strong>金币${action.gold || action.amount}</strong>`;
                case 'remove_gold':
                    return `扣除<strong>金币${action.gold || action.amount}</strong>`;
                case 'teleport':
                    return `传送到<strong>场景${action.scene_id}</strong>`;
                case 'update_quest_progress':
                    const quest3 = quests.find(q => q.id == action.quest_id);
                    const questName3 = quest3 ? quest3.title : `任务${action.quest_id}`;
                    return `更新<strong>${questName3}</strong>进度`;
                default:
                    return `${action.type}: ${JSON.stringify(action).substring(0, 50)}...`;
            }
        }).join(', ');
    }

    showDialogueNodeDetails(node, event = null) {
        const panel = document.getElementById('details-panel');
        const title = document.getElementById('details-title');
        const content = document.getElementById('details-content');

        title.textContent = `对话节点 #${node.id}`;

        let conditionDetails = '无';
        let actionDetails = '无';
        let nextNodeDetails = '无';

        // 获取关联数据
        const items = this.currentTreeData?.items || [];
        const quests = this.currentTreeData?.quests || [];

        if (node.condition_script) {
            try {
                // 添加调试信息
                this.debugConditionScript(node.condition_script);

                const conditions = JSON.parse(node.condition_script);
                const detailedConditions = this.formatDetailedConditions(conditions, items, quests);
                conditionDetails = `
                    <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                        <strong>详细说明:</strong><br>
                        <div style="white-space: pre-line; font-size: 12px;">${detailedConditions}</div>
                    </div>
                    <details>
                        <summary style="cursor: pointer; color: #007bff;">查看原始JSON</summary>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 11px; overflow-x: auto; margin-top: 5px;">${JSON.stringify(conditions, null, 2)}</pre>
                    </details>
                `;
            } catch (e) {
                conditionDetails = `<span style="color: #dc3545;">脚本格式错误: ${e.message}</span>`;
                console.error('条件脚本解析错误:', e, node.condition_script);
            }
        }

        if (node.action_script) {
            try {
                const actions = JSON.parse(node.action_script);
                const detailedActions = this.formatDetailedActions(actions, items, quests);
                actionDetails = `
                    <div style="background: #e8f0ff; padding: 10px; border-radius: 4px; margin-bottom: 10px;">
                        <strong>详细说明:</strong><br>
                        <div style="white-space: pre-line; font-size: 12px;">${detailedActions}</div>
                    </div>
                    <details>
                        <summary style="cursor: pointer; color: #007bff;">查看原始JSON</summary>
                        <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; font-size: 11px; overflow-x: auto; margin-top: 5px;">${JSON.stringify(actions, null, 2)}</pre>
                    </details>
                `;
            } catch (e) {
                actionDetails = '<span style="color: #dc3545;">脚本格式错误</span>';
            }
        }

        if (node.next_node_ids) {
            try {
                const nextIds = JSON.parse(node.next_node_ids);
                nextNodeDetails = nextIds.length > 0 ? nextIds.join(', ') : '无';
            } catch (e) {
                nextNodeDetails = '<span style="color: #dc3545;">格式错误</span>';
            }
        }

        // 使用分析后的实际类型
        const isPlayerNode = node.actualType === 'player';

        content.innerHTML = `
            <div class="details-section">
                <h6>基本信息</h6>
                <div>节点ID: ${node.id}</div>
                <div>类型: ${isPlayerNode ? '玩家选择' : 'NPC对话'}</div>
                <div>内容: ${node.content}</div>
                <div>is_player_choice字段: ${node.is_player_choice == 1 ? '1 (下一节点为玩家选择)' : '0 (下一节点为NPC对话)'}</div>
            </div>

            <div class="details-section">
                <h6>执行条件</h6>
                <div>${conditionDetails}</div>
            </div>

            <div class="details-section">
                <h6>执行动作</h6>
                <div>${actionDetails}</div>
            </div>

            <div class="details-section">
                <h6>下一节点</h6>
                <div>节点ID: ${nextNodeDetails}</div>
            </div>
        `;

        // 智能定位详情面板
        this.positionDetailsPanel(panel, event);

        panel.classList.add('show');
    }
    // 智能定位详情面板
    positionDetailsPanel(panel, event) {
        if (!event) {
            // 如果没有事件信息，使用默认位置
            panel.style.position = 'fixed';
            panel.style.top = '20px';
            panel.style.right = '20px';
            panel.style.left = 'auto';
            panel.style.bottom = 'auto';
            return;
        }

        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const panelWidth = 300;
        const panelMaxHeight = Math.min(600, viewportHeight - 40);

        // 获取点击位置
        const clickX = event.clientX;
        const clickY = event.clientY;

        // 计算最佳位置
        let left, top, right, bottom;

        // 水平定位：优先放在右侧，如果空间不够则放在左侧
        if (clickX + panelWidth + 20 <= viewportWidth) {
            // 右侧有足够空间
            left = clickX + 10;
            right = 'auto';
        } else if (clickX - panelWidth - 20 >= 0) {
            // 左侧有足够空间
            left = clickX - panelWidth - 10;
            right = 'auto';
        } else {
            // 两侧都没有足够空间，使用默认右侧定位
            left = 'auto';
            right = '20px';
        }

        // 垂直定位：尽量靠近点击位置，但确保不超出视口
        if (clickY + panelMaxHeight <= viewportHeight) {
            // 下方有足够空间
            top = Math.max(20, clickY - 50); // 稍微向上偏移
            bottom = 'auto';
        } else {
            // 下方空间不足，尝试上方
            const availableTopSpace = clickY - 20;
            if (availableTopSpace >= panelMaxHeight) {
                // 上方有足够空间
                top = clickY - panelMaxHeight - 10;
                bottom = 'auto';
            } else {
                // 上下都没有足够空间，居中显示
                top = Math.max(20, (viewportHeight - panelMaxHeight) / 2);
                bottom = 'auto';
            }
        }

        // 应用定位
        panel.style.position = 'fixed';
        panel.style.left = left === 'auto' ? 'auto' : left + 'px';
        panel.style.right = right;
        panel.style.top = top === 'auto' ? 'auto' : top + 'px';
        panel.style.bottom = bottom;
        panel.style.maxHeight = panelMaxHeight + 'px';

        console.log('详情面板定位:', { clickX, clickY, left, top, right, bottom, panelMaxHeight });
    }





    renderStatisticsView() {
        const container = document.getElementById('statistics-container');

        const stats = this.calculateStatistics();

        container.innerHTML = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 20px;">
                <div class="stat-card">
                    <h4>任务统计</h4>
                    <div class="stat-item">总任务数: <strong>${stats.totalQuests}</strong></div>
                    <div class="stat-item">对话任务: <strong>${stats.dialogueQuests}</strong></div>
                    <div class="stat-item">收集任务: <strong>${stats.collectionQuests}</strong></div>
                    <div class="stat-item">击杀任务: <strong>${stats.killQuests}</strong></div>
                </div>

                <div class="stat-card">
                    <h4>NPC统计</h4>
                    <div class="stat-item">总NPC数: <strong>${stats.totalNpcs}</strong></div>
                    <div class="stat-item">商人NPC: <strong>${stats.merchantNpcs}</strong></div>
                    <div class="stat-item">任务NPC: <strong>${stats.questNpcs}</strong></div>
                    <div class="stat-item">普通NPC: <strong>${stats.normalNpcs}</strong></div>
                </div>

                <div class="stat-card">
                    <h4>场景统计</h4>
                    <div class="stat-item">总场景数: <strong>${stats.totalScenes}</strong></div>
                    <div class="stat-item">有NPC场景: <strong>${stats.scenesWithNpcs}</strong></div>
                    <div class="stat-item">有任务场景: <strong>${stats.scenesWithQuests}</strong></div>
                </div>

                <div class="stat-card">
                    <h4>对话树统计</h4>
                    <div class="stat-item">对话树数: <strong>${stats.totalDialogues}</strong></div>
                    <div class="stat-item">已关联NPC: <strong>${stats.linkedDialogues}</strong></div>
                </div>
            </div>
        `;

        // 添加统计卡片样式
        const style = document.createElement('style');
        style.textContent = `
            .stat-card {
                background: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 15px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }
            .stat-card h4 {
                margin-top: 0;
                margin-bottom: 15px;
                color: #333;
                border-bottom: 1px solid #eee;
                padding-bottom: 8px;
            }
            .stat-item {
                display: flex;
                justify-content: space-between;
                padding: 5px 0;
                border-bottom: 1px solid #f5f5f5;
            }
            .stat-item:last-child {
                border-bottom: none;
            }
        `;
        document.head.appendChild(style);
    }

    calculateStatistics() {
        const stats = {
            totalQuests: this.data.quests.length,
            dialogueQuests: this.data.quests.filter(q => q.type === 'dialogue').length,
            collectionQuests: this.data.quests.filter(q => q.type === 'collection').length,
            killQuests: this.data.quests.filter(q => q.type === 'kill').length,

            totalNpcs: this.data.npcs.length,
            merchantNpcs: this.data.npcs.filter(n => n.is_merchant && !n.is_quest_giver).length,
            questNpcs: this.data.npcs.filter(n => n.is_quest_giver && !n.is_merchant).length,
            normalNpcs: this.data.npcs.filter(n => !n.is_merchant && !n.is_quest_giver).length,

            totalScenes: this.data.scenes.length,
            scenesWithNpcs: new Set(this.data.npcs.map(n => n.scene_id)).size,
            scenesWithQuests: new Set(this.data.quests.flatMap(q => {
                const npcs = this.data.npcs.filter(n => n.id == q.giver_npc_id || n.id == q.receiver_npc_id);
                return npcs.map(n => n.scene_id);
            })).size,

            totalDialogues: this.data.dialogues.length,
            linkedDialogues: this.data.dialogues.filter(d =>
                this.data.npcs.some(n => n.default_dialogue_tree_id == d.id)
            ).length
        };

        return stats;
    }

    // 筛选方法
    getFilteredScenes() {
        if (!this.data.scenes) return [];

        return this.data.scenes.filter(scene => {
            // 大陆筛选
            const continentFilter = document.getElementById('continent-filter');
            if (continentFilter && continentFilter.value) {
                // 直接使用scene中的continent_id字段，或者通过zone_id查找
                if (scene.continent_id) {
                    if (scene.continent_id !== continentFilter.value) {
                        return false;
                    }
                } else {
                    // 备用方案：通过zone_id找到对应的区域，再找到对应的大陆
                    const zone = this.data.zones?.find(z => z.id === scene.zone_id);
                    if (!zone || zone.continent !== continentFilter.value) {
                        return false;
                    }
                }
            }

            // 区域筛选
            const zoneFilter = document.getElementById('zone-filter');
            if (zoneFilter && zoneFilter.value && scene.zone_id !== zoneFilter.value) {
                return false;
            }



            // 搜索筛选
            const searchInput = document.getElementById('search-input');
            if (searchInput && searchInput.value) {
                const searchText = searchInput.value.toLowerCase();
                if (!scene.name.toLowerCase().includes(searchText)) {
                    return false;
                }
            }

            return true;
        });
    }

    getFilteredNpcs() {
        return this.data.npcs.filter(npc => {
            if (this.filters.npc && npc.id !== this.filters.npc) return false;
            if (this.filters.scene && npc.scene_id !== this.filters.scene) return false;
            if (this.filters.search && !npc.name.toLowerCase().includes(this.filters.search)) return false;
            return true;
        });
    }

    getFilteredQuests() {
        if (!this.data.quests) return [];

        return this.data.quests.filter(quest => {
            // 使用左边工具栏的筛选条件

            // 任务分组筛选
            const groupFilter = document.getElementById('quest-group-filter');
            if (groupFilter && groupFilter.value && quest.group_id != groupFilter.value) {
                return false;
            }

            // 任务类型筛选（使用checkbox）
            const typeCheckboxes = document.querySelectorAll('#quest-type-filter input[type="checkbox"]:checked');
            if (typeCheckboxes.length > 0) {
                const selectedTypes = Array.from(typeCheckboxes).map(cb => cb.value);
                if (!selectedTypes.includes(quest.type)) {
                    return false;
                }
            }

            // 等级范围筛选
            const minLevelInput = document.getElementById('min-level');
            const maxLevelInput = document.getElementById('max-level');

            if (minLevelInput && minLevelInput.value && quest.min_level < parseInt(minLevelInput.value)) {
                return false;
            }

            if (maxLevelInput && maxLevelInput.value && quest.min_level > parseInt(maxLevelInput.value)) {
                return false;
            }

            // NPC筛选
            const npcFilter = document.getElementById('npc-filter');
            if (npcFilter && npcFilter.value) {
                const hasNpc = quest.giver_npc_id == npcFilter.value || quest.receiver_npc_id == npcFilter.value;
                if (!hasNpc) return false;
            }

            // 搜索筛选
            const searchInput = document.getElementById('search-input');
            if (searchInput && searchInput.value) {
                const searchText = searchInput.value.toLowerCase();
                const matchTitle = quest.title.toLowerCase().includes(searchText);
                const matchDesc = quest.description && quest.description.toLowerCase().includes(searchText);
                if (!matchTitle && !matchDesc) return false;
            }

            return true;
        });
    }

    applyFilters() {
        this.renderCurrentView();
    }

    // 详情显示方法
    showSceneDetails(scene, npcs, quests, event = null) {
        const panel = document.getElementById('details-panel');
        const title = document.getElementById('details-title');
        const content = document.getElementById('details-content');

        title.textContent = `场景: ${scene.name}`;

        content.innerHTML = `
            <div class="details-section">
                <h6>基本信息</h6>
                <div>场景ID: ${scene.id}</div>
                <div>描述: ${scene.description || '无'}</div>
                <div>最大玩家数: ${scene.max_players || '无限制'}</div>
            </div>

            <div class="details-section">
                <h6>NPC列表 (${npcs.length})</h6>
                <ul class="details-list">
                    ${npcs.map(npc => `
                        <li>
                            <strong>${npc.name}</strong>
                            ${npc.is_merchant ? '<span style="color: #28a745;">[商人]</span>' : ''}
                            ${npc.is_quest_giver ? '<span style="color: #fd7e14;">[任务]</span>' : ''}
                        </li>
                    `).join('')}
                </ul>
            </div>

            <div class="details-section">
                <h6>相关任务 (${quests.length})</h6>
                <ul class="details-list">
                    ${quests.map(quest => `
                        <li>
                            <strong>${quest.title}</strong>
                            <span class="quest-type ${quest.type}">${this.getQuestTypeText(quest.type)}</span>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;

        // 智能定位详情面板
        this.positionDetailsPanel(panel, event);

        panel.classList.add('show');
    }

    showNpcDetails(npc, event = null) {
        const panel = document.getElementById('details-panel');
        const title = document.getElementById('details-title');
        const content = document.getElementById('details-content');

        title.textContent = `NPC: ${npc.name}`;

        const relatedQuests = this.data.quests.filter(quest =>
            quest.giver_npc_id == npc.id || quest.receiver_npc_id == npc.id
        );

        const scene = this.data.scenes.find(s => s.id == npc.scene_id);
        const dialogue = this.data.dialogues.find(d => d.id == npc.default_dialogue_tree_id);

        content.innerHTML = `
            <div class="details-section">
                <h6>基本信息</h6>
                <div>NPC ID: ${npc.id}</div>
                <div>描述: ${npc.description || '无'}</div>
                <div>类型:
                    ${npc.is_merchant ? '<span style="color: #28a745;">[商人]</span>' : ''}
                    ${npc.is_quest_giver ? '<span style="color: #fd7e14;">[任务发放者]</span>' : ''}
                    ${!npc.is_merchant && !npc.is_quest_giver ? '[普通NPC]' : ''}
                </div>
            </div>

            <div class="details-section">
                <h6>位置信息</h6>
                <div>所在场景: ${scene ? scene.name : '未知'}</div>
                <div>位置描述: ${npc.position_desc || '无'}</div>
            </div>

            <div class="details-section">
                <h6>对话树</h6>
                <div>${dialogue ? dialogue.name : '无默认对话树'}</div>
            </div>

            <div class="details-section">
                <h6>相关任务 (${relatedQuests.length})</h6>
                <ul class="details-list">
                    ${relatedQuests.map(quest => `
                        <li>
                            <strong>${quest.title}</strong>
                            <span class="quest-type ${quest.type}">${this.getQuestTypeText(quest.type)}</span>
                            <div style="font-size: 11px; color: #666;">
                                ${quest.giver_npc_id === npc.id ? '发布者' : '接收者'}
                            </div>
                        </li>
                    `).join('')}
                </ul>
            </div>
        `;

        // 智能定位详情面板
        this.positionDetailsPanel(panel, event);

        panel.classList.add('show');
    }

    showQuestDetails(quest, event = null) {
        const panel = document.getElementById('details-panel');
        const title = document.getElementById('details-title');
        const content = document.getElementById('details-content');

        title.textContent = `任务: ${quest.title}`;

        const giverNpc = this.data.npcs.find(npc => npc.id == quest.giver_npc_id);
        const receiverNpc = this.data.npcs.find(npc => npc.id == quest.receiver_npc_id);
        const group = this.data.questGroups.find(g => g.id == quest.group_id);

        const prerequisites = quest.prerequisite_quests ? JSON.parse(quest.prerequisite_quests) : [];
        const prereqQuests = prerequisites.map(id => this.data.quests.find(q => q.id == id)).filter(Boolean);

        const rewardItems = quest.reward_items ? JSON.parse(quest.reward_items) : [];

        // 获取任务目标信息
        const objectives = quest.objectives || [];

        // 获取后续任务
        const followupQuests = this.data.quests.filter(q => {
            const prereqs = q.prerequisite_quests ? JSON.parse(q.prerequisite_quests) : [];
            return prereqs.includes(parseInt(quest.id));
        });

        // 获取关联的对话树
        const relatedDialogues = this.data.dialogues ? this.data.dialogues.filter(d => {
            // 这里可以根据实际的关联逻辑来判断
            return d.name && d.name.includes(quest.title.substring(0, 3));
        }) : [];

        content.innerHTML = `
            <div class="details-section">
                <h6>基本信息</h6>
                <div>任务ID: <strong>${quest.id}</strong></div>
                <div>类型: <span class="quest-type ${quest.type}">${this.getQuestTypeText(quest.type)}</span></div>
                <div>分组: <strong>${group ? group.name : '无'}</strong></div>
                <div>最低等级: <strong>${quest.min_level}</strong></div>
                <div>可重复: <strong>${quest.is_repeatable ? '是' : '否'}</strong></div>
                <div>状态: <span class="quest-status ${this.getQuestStatus(quest)}" style="display: inline-block; width: 12px; height: 12px; border-radius: 50%; margin-right: 5px;"></span>${this.getQuestStatusText(quest)}</div>
            </div>

            <div class="details-section">
                <h6>任务描述</h6>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 4px; border-left: 4px solid #007bff;">
                    ${quest.description || '无描述'}
                </div>
            </div>

            <div class="details-section">
                <h6>相关NPC</h6>
                <div>发布者: <strong>${giverNpc ? `${giverNpc.name} (ID:${giverNpc.id})` : '未知'}</strong></div>
                ${receiverNpc && receiverNpc.id !== quest.giver_npc_id ? `<div>接收者: <strong>${receiverNpc.name} (ID:${receiverNpc.id})</strong></div>` : ''}
                ${giverNpc && giverNpc.scene_id ? `<div>位置: 场景 ${giverNpc.scene_id}</div>` : ''}
            </div>

            ${objectives.length > 0 ? `
                <div class="details-section">
                    <h6>任务目标 (${objectives.length})</h6>
                    <ul class="details-list">
                        ${objectives.map((obj, index) => `
                            <li style="margin-bottom: 8px; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                                <div><strong>${obj.description || `目标 ${index + 1}`}</strong></div>
                                <div style="font-size: 12px; color: #666; margin-top: 4px;">
                                    目标ID: <strong>${obj.id}</strong> |
                                    类型: <strong>${obj.type || '未知'}</strong>
                                    ${obj.target_id ? ` | 目标ID: <strong>${obj.target_id}</strong>` : ''}
                                    ${obj.target_name ? ` | 目标: <strong>${obj.target_name}</strong>` : ''}
                                    ${obj.quantity ? ` | 数量: <strong>${obj.quantity}</strong>` : ''}
                                </div>
                                <div style="font-size: 11px; color: #888; margin-top: 2px;">
                                    任务ID: ${obj.quest_id}
                                </div>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            ` : ''}

            <div class="details-section">
                <h6>奖励信息</h6>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px;">
                    ${quest.reward_gold > 0 ? `<div style="background: #fff3cd; padding: 8px; border-radius: 4px; text-align: center;"><strong>${quest.reward_gold}</strong><br><small>金币</small></div>` : ''}
                    ${quest.reward_exp > 0 ? `<div style="background: #d4edda; padding: 8px; border-radius: 4px; text-align: center;"><strong>${quest.reward_exp}</strong><br><small>经验</small></div>` : ''}
                    ${rewardItems.length > 0 ? `<div style="background: #d1ecf1; padding: 8px; border-radius: 4px; text-align: center;"><strong>${rewardItems.length}</strong><br><small>物品</small></div>` : ''}
                </div>
                ${rewardItems.length > 0 ? `
                    <details style="margin-top: 10px;">
                        <summary style="cursor: pointer; color: #007bff;">查看奖励物品详情</summary>
                        <div style="margin-top: 5px; padding: 10px; background: #f8f9fa; border-radius: 4px;">
                            ${rewardItems.map(item => {
                                // 兼容不同的字段名
                                const itemId = item.item_id || item.id || item.template_id;
                                const itemName = this.getItemName(itemId);
                                return `<div>物品: ${itemName} (ID: ${itemId}), 数量: ${item.quantity || 1}</div>`;
                            }).join('')}
                        </div>
                    </details>
                ` : ''}
            </div>

            ${prereqQuests.length > 0 ? `
                <div class="details-section">
                    <h6>前置任务 (${prereqQuests.length})</h6>
                    <ul class="details-list">
                        ${prereqQuests.map(pq => `
                            <li style="display: flex; align-items: center; justify-content: space-between; padding: 5px 0;">
                                <div>
                                    <strong>${pq.title}</strong>
                                    <span class="quest-type ${pq.type}">${this.getQuestTypeText(pq.type)}</span>
                                </div>
                                <small style="color: #666;">等级 ${pq.min_level}</small>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            ` : ''}

            ${followupQuests.length > 0 ? `
                <div class="details-section">
                    <h6>后续任务 (${followupQuests.length})</h6>
                    <ul class="details-list">
                        ${followupQuests.map(fq => `
                            <li style="display: flex; align-items: center; justify-content: space-between; padding: 5px 0;">
                                <div>
                                    <strong>${fq.title}</strong>
                                    <span class="quest-type ${fq.type}">${this.getQuestTypeText(fq.type)}</span>
                                </div>
                                <small style="color: #666;">等级 ${fq.min_level}</small>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            ` : ''}

            ${relatedDialogues.length > 0 ? `
                <div class="details-section">
                    <h6>相关对话 (${relatedDialogues.length})</h6>
                    <ul class="details-list">
                        ${relatedDialogues.map(dialogue => `
                            <li>
                                <strong>${dialogue.name}</strong>
                                <small style="color: #666;"> (ID:${dialogue.id})</small>
                            </li>
                        `).join('')}
                    </ul>
                </div>
            ` : ''}
        `;

        // 智能定位详情面板
        this.positionDetailsPanel(panel, event);

        panel.classList.add('show');
    }

    getQuestStatusText(quest) {
        const status = this.getQuestStatus(quest);
        switch (status) {
            case 'available': return '可接受';
            case 'in-progress': return '进行中';
            case 'completed': return '已完成';
            case 'locked': return '未解锁';
            default: return '未知';
        }
    }

    hideDetails() {
        const panel = document.getElementById('details-panel');
        if (panel && panel.classList.contains('show')) {
            // 添加隐藏动画类
            panel.classList.add('hiding');

            // 动画完成后隐藏面板
            setTimeout(() => {
                panel.classList.remove('show', 'hiding');
            }, 300); // 与CSS transition时间一致
        }
    }

    // 工具方法
    getQuestTypeText(type) {
        const typeMap = {
            'dialogue': '对话',
            'collection': '收集',
            'kill': '击杀',
            'escort': '护送',
            'exploration': '探索'
        };
        return typeMap[type] || type;
    }

    showError(message) {
        // 简单的错误显示
        alert('错误: ' + message);
    }

    exportCurrentView() {
        // TODO: 实现视图导出功能
        alert('导出功能开发中...');
    }

    testDialogueTree() {
        // 切换到对话关系视图
        this.switchView('dialogue-tree');

        // 如果有对话树数据，自动加载第一个进行测试
        if (this.data.dialogues && this.data.dialogues.length > 0) {
            const firstDialogue = this.data.dialogues[0];
            document.getElementById('dialogue-tree-select').value = firstDialogue.id;
            this.loadDialogueTreeDetail(firstDialogue.id);

            console.log('测试对话树:', firstDialogue);
            alert(`正在测试对话树: ${firstDialogue.name}`);
        } else {
            alert('没有找到对话树数据，请先刷新数据');
        }
    }

    // 辅助函数：获取物品详细信息
    getItemDetails(itemId, items) {
        const item = items.find(i => i.id == itemId);
        if (item) {
            return `${item.name} (ID:${item.id}) - ${item.description || '无描述'}`;
        }
        return `物品${itemId} (未找到详细信息)`;
    }

    // 辅助函数：获取物品名称
    getItemName(itemId) {
        if (!itemId) return '未知物品';
        if (!this.data || !this.data.items) return `物品${itemId}`;
        const item = this.data.items.find(i => i.id == itemId);
        return item ? item.name : `物品${itemId}`;
    }

    // 辅助函数：获取任务详细信息
    getQuestDetails(questId, quests) {
        // 使用严格相等和宽松相等两种方式查找，处理字符串/数字类型问题
        let quest = quests.find(q => q.id === questId);
        if (!quest) {
            quest = quests.find(q => q.id == questId);
        }
        if (!quest) {
            // 尝试字符串和数字的转换匹配
            quest = quests.find(q => String(q.id) === String(questId));
        }

        if (quest) {
            return `${quest.title} (ID:${quest.id}) - ${quest.description || '无描述'}`;
        }

        // 提供更详细的错误信息，包括类型信息
        console.warn(`任务ID ${questId} (类型: ${typeof questId}) 未找到，当前加载的任务:`,
            quests.map(q => `ID:${q.id} (类型: ${typeof q.id}) - ${q.title}`));
        return `任务${questId} <span style="color: #dc3545;">(数据中未找到，请检查任务是否存在)</span>`;
    }

    // 辅助函数：格式化详细的条件信息
    formatDetailedConditions(conditions, items, quests) {
        if (!conditions) return '无条件';

        console.log('详细条件格式化 - 原始数据:', conditions);

        let conditionList = [];
        let logicalOp = 'AND';

        if (conditions.conditions && Array.isArray(conditions.conditions)) {
            // 标准格式
            conditionList = conditions.conditions;
            logicalOp = conditions.logical_operator || 'AND';
        } else if (Array.isArray(conditions)) {
            // 直接数组格式
            conditionList = conditions;
        } else if (typeof conditions === 'object') {
            if (conditions.type) {
                // 单个条件对象
                conditionList = [conditions];
            } else {
                // 可能是多个条件的键值对格式
                if (conditions.logical_operator) {
                    logicalOp = conditions.logical_operator;
                }

                // 提取所有非logical_operator的字段作为条件
                conditionList = Object.entries(conditions)
                    .filter(([key, value]) => key !== 'logical_operator')
                    .map(([key, value]) => {
                        if (typeof value === 'object' && value.type) {
                            return value;
                        } else {
                            return { type: key, ...value };
                        }
                    });
            }
        }

        if (conditionList.length === 0) return '无条件';

        console.log('详细条件格式化 - 解析后条件列表:', conditionList);

        const conditionTexts = conditionList.map(cond => {
            switch (cond.type) {
                case 'has_item':
                    const itemDetails1 = this.getItemDetails(cond.item_id, items);
                    return `拥有 <strong>${itemDetails1}</strong> 数量≥${cond.quantity || 1}`;
                case 'does_not_have_item':
                    const itemDetails2 = this.getItemDetails(cond.item_id, items);
                    return `不拥有 <strong>${itemDetails2}</strong>`;
                case 'quest_status':
                    console.log(`详细条件格式化 - 任务状态: quest_id=${cond.quest_id} (${typeof cond.quest_id})`);
                    const questDetails = this.getQuestDetails(cond.quest_id, quests);
                    return `<strong>${questDetails}</strong> 状态为${cond.status}`;
                case 'player_attribute':
                    const attributeNames = {
                        'strength': '力量', 'agility': '敏捷', 'constitution': '体质', 'intelligence': '智力',
                        'attack': '攻击力', 'defense': '防御力', 'max_hp': '最大生命值', 'max_mp': '最大法力值'
                    };
                    const attrName = attributeNames[cond.attribute] || cond.attribute;
                    return `<strong>${attrName}</strong>${cond.operator || '≥'}${cond.value}`;
                case 'player_level':
                    return `<strong>玩家等级</strong>${cond.operator || '≥'}${cond.value}`;
                default:
                    return `${cond.type}: ${JSON.stringify(cond)}`;
            }
        });

        return `${logicalOp === 'OR' ? '满足以下任一条件' : '满足以下所有条件'}:\n${conditionTexts.join('\n')}`;
    }

    // 辅助函数：格式化详细的动作信息
    formatDetailedActions(actions, items, quests) {
        if (!actions) return '无动作';

        let actionList = [];
        if (actions.actions && Array.isArray(actions.actions)) {
            actionList = actions.actions;
        } else if (Array.isArray(actions)) {
            actionList = actions;
        } else if (typeof actions === 'object' && actions.type) {
            actionList = [actions];
        }

        if (actionList.length === 0) return '无动作';

        const actionTexts = actionList.map(action => {
            switch (action.type) {
                case 'give_item':
                    const itemDetails = this.getItemDetails(action.item_id, items);
                    return `给予 <strong>${itemDetails}</strong> x${action.quantity || action.count || 1}`;
                case 'take_item':
                case 'remove_item':
                    const itemDetails2 = this.getItemDetails(action.item_id, items);
                    return `获取 <strong>${itemDetails2}</strong> x${action.quantity || action.count || 1}`;
                case 'start_quest':
                    const questDetails1 = this.getQuestDetails(action.quest_id, quests);
                    return `开始 <strong>${questDetails1}</strong>`;
                case 'complete_quest':
                    const questDetails2 = this.getQuestDetails(action.quest_id, quests);
                    return `完成 <strong>${questDetails2}</strong>`;
                case 'give_exp':
                    return `给予 <strong>经验${action.exp || action.experience}</strong>`;
                case 'give_gold':
                    return `给予 <strong>金币${action.gold || action.amount}</strong>`;
                case 'remove_gold':
                    return `扣除 <strong>金币${action.gold || action.amount}</strong>`;
                case 'teleport':
                    return `传送到 <strong>场景${action.scene_id}</strong>`;
                case 'update_quest_progress':
                    const questDetails3 = this.getQuestDetails(action.quest_id, quests);
                    return `更新 <strong>${questDetails3}</strong> 进度`;
                default:
                    return `${action.type}: ${JSON.stringify(action)}`;
            }
        });

        return `执行以下动作:\n${actionTexts.join('\n')}`;
    }

    // 调试函数：分析条件脚本格式
    debugConditionScript(conditionScript) {
        console.group('条件脚本调试');
        console.log('原始脚本:', conditionScript);

        try {
            const parsed = JSON.parse(conditionScript);
            console.log('解析后的JSON:', parsed);
            console.log('数据类型:', typeof parsed);
            console.log('是否为数组:', Array.isArray(parsed));

            if (parsed && typeof parsed === 'object') {
                console.log('对象键:', Object.keys(parsed));

                if (parsed.conditions) {
                    console.log('标准格式 - conditions字段:', parsed.conditions);
                    console.log('逻辑操作符:', parsed.logical_operator);
                } else {
                    console.log('非标准格式，尝试解析...');
                    Object.entries(parsed).forEach(([key, value]) => {
                        console.log(`键 "${key}":`, value);
                    });
                }
            }
        } catch (e) {
            console.error('JSON解析失败:', e);
        }

        console.groupEnd();
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    window.questWorkflow = new QuestWorkflowVisualization();
});

// 任务编辑模态框功能
function openQuestEditModal(quest) {
    const modal = document.getElementById('quest-edit-modal');
    const form = document.getElementById('quest-edit-form');

    // 填充表单数据
    document.getElementById('edit-quest-id').value = quest.id;
    document.getElementById('edit-quest-title').value = quest.title;
    document.getElementById('edit-quest-type').value = quest.type;
    document.getElementById('edit-quest-min-level').value = quest.min_level;
    document.getElementById('edit-quest-description').value = quest.description || '';
    document.getElementById('edit-quest-reward-gold').value = quest.reward_gold || 0;
    document.getElementById('edit-quest-reward-exp').value = quest.reward_exp || 0;
    document.getElementById('edit-quest-repeatable').checked = quest.is_repeatable == 1;

    // 填充分组选项
    populateQuestGroups(quest.group_id);

    // 填充NPC选项
    populateNpcOptions(quest.giver_npc_id, quest.receiver_npc_id);

    // 填充前置任务选项
    populatePrerequisiteQuests(quest.id, quest.prerequisite_quests);

    modal.style.display = 'flex';

    // 设置显示时间戳，用于避免立即关闭
    modal.dataset.showTime = Date.now().toString();
}

function closeQuestEditModal() {
    console.log('关闭任务编辑模态框');

    const modal = document.getElementById('quest-edit-modal');
    modal.style.display = 'none';

    // 清理时间戳
    if (modal.dataset.showTime) {
        delete modal.dataset.showTime;
    }

    // 如果当前视图是地图视图，重新渲染以修复可能的显示问题
    if (window.questWorkflow && window.questWorkflow.currentView === 'map') {
        console.log('模态框关闭后重新渲染地图视图');
        setTimeout(() => {
            window.questWorkflow.renderCurrentView();
        }, 100);
    }
}

function saveQuestEdit() {
    console.log('开始保存任务（简单编辑）');

    const form = document.getElementById('quest-edit-form');
    const formData = new FormData(form);

    // 转换为JSON，只包含非空值
    const data = {};
    for (let [key, value] of formData.entries()) {
        // 只包含非空值，避免覆盖未编辑的字段
        if (value !== '' && value !== null && value !== undefined) {
            data[key] = value;
        }
    }

    // 处理复选框（特殊处理，因为复选框总是需要明确的值）
    const repeatableCheckbox = document.getElementById('edit-quest-repeatable');
    if (repeatableCheckbox) {
        data.is_repeatable = repeatableCheckbox.checked ? 1 : 0;
    }

    // 处理前置任务（多选框）
    const prerequisiteSelect = document.getElementById('edit-quest-prerequisites');
    if (prerequisiteSelect) {
        const selectedValues = Array.from(prerequisiteSelect.selectedOptions).map(option => parseInt(option.value));
        if (selectedValues.length > 0) {
            data.prerequisite_quests = JSON.stringify(selectedValues);
            console.log('前置任务数据:', selectedValues);
        } else {
            // 如果没有选择前置任务，明确设置为空
            data.prerequisite_quests = '';
        }
    }

    console.log('简单编辑表单数据:', data);

    // 发送简单更新请求
    const requestData = {
        action: 'update_quest_simple',
        ...data
    };

    console.log('发送简单更新请求:', requestData);

    fetch('api_quests.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('响应状态:', response.status);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('API错误响应:', text);
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(result => {
        console.log('API响应结果:', result);
        if (result.success) {
            console.log('更新的字段:', result.data?.updated_fields);
            showToast('任务更新成功', 'success');
            closeQuestEditModal();
            // 重新加载数据
            if (window.questWorkflow) {
                window.questWorkflow.loadData().then(() => {
                    window.questWorkflow.renderCurrentView();
                });
            }
        } else {
            showToast('任务更新失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('保存任务失败:', error);
        showToast('保存失败: ' + error.message, 'error');
    });
}

function openQuestEditor() {
    const questId = document.getElementById('edit-quest-id').value;
    window.open(`quest_editor.php?id=${questId}`, '_blank');
}

// 对话树编辑模态框功能
function openDialogueEditModal(dialogueTree) {
    console.log('openDialogueEditModal 被调用，对话树:', dialogueTree);

    // 检查是否正在操作模态框
    if (window.modalState.isOpening || window.modalState.isClosing) {
        console.log('模态框正在操作中，忽略此次请求');
        return;
    }

    const modal = document.getElementById('dialogue-edit-modal');
    if (!modal) {
        console.error('找不到对话编辑模态框元素');
        showToast('界面元素加载失败', 'error');
        return;
    }

    // 设置模态框状态
    window.modalState.isOpening = true;
    window.modalState.currentModal = 'dialogue-edit-modal';

    // 填充表单数据
    document.getElementById('edit-dialogue-id').value = dialogueTree.id;
    document.getElementById('edit-dialogue-name').value = dialogueTree.name;
    document.getElementById('edit-dialogue-description').value = dialogueTree.description || '';

    // 加载对话节点
    loadDialogueNodes(dialogueTree.id);

    // 延迟显示模态框，避免事件冲突
    setTimeout(() => {
        console.log('显示对话编辑模态框');
        modal.style.display = 'flex';

        // 设置显示时间戳，用于避免立即关闭
        modal.dataset.showTime = Date.now().toString();

        // 清除开启状态
        window.modalState.isOpening = false;

        // 再次检查模态框是否正确显示
        setTimeout(() => {
            if (modal.style.display !== 'flex') {
                console.warn('模态框显示后被意外关闭');
                window.modalState.currentModal = null;
            } else {
                console.log('模态框成功显示');
                // 再次更新时间戳，确保有足够的保护时间
                modal.dataset.showTime = Date.now().toString();
            }
        }, 150);
    }, 150); // 增加延迟时间到150ms
}

function closeDialogueEditModal() {
    console.log('关闭对话编辑模态框');

    // 检查是否正在操作模态框
    if (window.modalState.isClosing) {
        console.log('模态框正在关闭中，忽略此次请求');
        return;
    }

    // 设置关闭状态
    window.modalState.isClosing = true;

    const modal = document.getElementById('dialogue-edit-modal');
    modal.style.display = 'none';

    // 清理节点编辑状态
    cancelNodeEdit();

    // 清理时间戳
    if (modal.dataset.showTime) {
        delete modal.dataset.showTime;
    }

    // 清理模态框状态
    setTimeout(() => {
        window.modalState.isClosing = false;
        window.modalState.currentModal = null;
    }, 100);

    // 如果当前视图是地图视图，重新渲染以修复可能的显示问题
    if (window.questWorkflow && window.questWorkflow.currentView === 'map') {
        console.log('模态框关闭后重新渲染地图视图');
        setTimeout(() => {
            window.questWorkflow.renderCurrentView();
        }, 200);
    }
}

function saveDialogueEdit() {
    console.log('开始保存对话树');

    const form = document.getElementById('dialogue-edit-form');
    const formData = new FormData(form);

    const data = {};
    for (let [key, value] of formData.entries()) {
        data[key] = value;
    }

    console.log('表单数据:', data);

    // 发送更新请求
    const requestData = {
        action: 'update_dialogue_tree',
        ...data
    };

    console.log('发送请求数据:', requestData);

    fetch('api_quest_workflow.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestData)
    })
    .then(response => {
        console.log('响应状态:', response.status);
        if (!response.ok) {
            return response.text().then(text => {
                console.error('API错误响应:', text);
                throw new Error(`HTTP ${response.status}: ${text}`);
            });
        }
        return response.json();
    })
    .then(result => {
        console.log('API响应结果:', result);
        if (result.success) {
            showToast('对话树更新成功', 'success');
            closeDialogueEditModal();

            // 重新加载数据并保持当前对话树
            if (window.questWorkflow) {
                const currentDialogueId = window.questWorkflow.currentDialogueTreeId;
                console.log('保存成功，重新加载当前对话树:', currentDialogueId);

                window.questWorkflow.loadData().then(() => {
                    // 如果当前视图是对话树视图，重新加载当前对话树
                    if (window.questWorkflow.currentView === 'dialogue-tree' && currentDialogueId) {
                        // 更新选择框
                        const select = document.getElementById('dialogue-tree-select');
                        if (select) {
                            select.value = currentDialogueId;
                        }
                        // 重新加载对话树详情
                        window.questWorkflow.loadDialogueTreeDetail(currentDialogueId);
                    } else {
                        window.questWorkflow.renderCurrentView();
                    }
                });
            }
        } else {
            showToast('对话树更新失败: ' + (result.message || result.error), 'error');
        }
    })
    .catch(error => {
        console.error('保存对话树失败:', error);
        showToast('保存失败: ' + error.message, 'error');
    });
}

function openDialogueEditor() {
    const dialogueId = document.getElementById('edit-dialogue-id').value;
    window.open(`dialogue_editor.php?id=${dialogueId}`, '_blank');
}

// 辅助函数
function populateQuestGroups(selectedGroupId) {
    const select = document.getElementById('edit-quest-group');
    select.innerHTML = '<option value="">-- 选择分组 --</option>';

    if (window.questWorkflow && window.questWorkflow.data.questGroups) {
        window.questWorkflow.data.questGroups.forEach(group => {
            const option = document.createElement('option');
            option.value = group.id;
            option.textContent = `${group.name} (${group.type || 'side'})`;
            if (group.id == selectedGroupId) {
                option.selected = true;
            }
            select.appendChild(option);
        });
    }
}

function populateNpcOptions(selectedGiverId, selectedReceiverId) {
    const giverSelect = document.getElementById('edit-quest-giver');
    const receiverSelect = document.getElementById('edit-quest-receiver');

    giverSelect.innerHTML = '<option value="">-- 选择发布NPC --</option>';
    receiverSelect.innerHTML = '<option value="">-- 选择接收NPC --</option>';

    if (window.questWorkflow && window.questWorkflow.data.npcs) {
        window.questWorkflow.data.npcs.forEach(npc => {
            const giverOption = document.createElement('option');
            giverOption.value = npc.id;
            giverOption.textContent = npc.name;
            if (npc.id == selectedGiverId) {
                giverOption.selected = true;
            }
            giverSelect.appendChild(giverOption);

            const receiverOption = document.createElement('option');
            receiverOption.value = npc.id;
            receiverOption.textContent = npc.name;
            if (npc.id == selectedReceiverId) {
                receiverOption.selected = true;
            }
            receiverSelect.appendChild(receiverOption);
        });
    }
}

function populatePrerequisiteQuests(currentQuestId, selectedPrerequisites) {
    console.log('填充前置任务选项:', { currentQuestId, selectedPrerequisites });

    const prerequisiteSelect = document.getElementById('edit-quest-prerequisites');
    prerequisiteSelect.innerHTML = '';

    // 解析已选择的前置任务
    let selectedIds = [];
    if (selectedPrerequisites) {
        if (typeof selectedPrerequisites === 'string') {
            try {
                selectedIds = JSON.parse(selectedPrerequisites) || [];
            } catch (e) {
                console.warn('解析前置任务JSON失败:', e);
                selectedIds = [];
            }
        } else if (Array.isArray(selectedPrerequisites)) {
            selectedIds = selectedPrerequisites;
        }
    }

    console.log('解析后的前置任务ID列表:', selectedIds);

    if (window.questWorkflow && window.questWorkflow.data.quests) {
        window.questWorkflow.data.quests.forEach(quest => {
            // 不能选择自己作为前置任务
            if (quest.id == currentQuestId) return;

            const option = document.createElement('option');
            option.value = quest.id;
            option.textContent = `[Lv.${quest.min_level}] ${quest.title}`;

            // 检查是否已选中
            if (selectedIds.includes(parseInt(quest.id))) {
                option.selected = true;
            }

            prerequisiteSelect.appendChild(option);
        });
    }

    console.log('前置任务选项填充完成，总计:', prerequisiteSelect.options.length, '个选项');
}

function loadDialogueNodes(dialogueTreeId) {
    fetch(`api_quest_workflow.php?action=get_dialogue_tree_detail&tree_id=${dialogueTreeId}`)
    .then(response => response.json())
    .then(data => {
        const nodesList = document.getElementById('dialogue-nodes-list');
        const nodeCount = document.getElementById('dialogue-node-count');

        // 存储节点数据供编辑使用
        window.currentDialogueNodes = data.nodes || [];

        if (data.nodes && data.nodes.length > 0) {
            nodeCount.textContent = `(${data.nodes.length}个节点)`;

            nodesList.innerHTML = data.nodes.map(node => `
                <div class="node-item" data-node-id="${node.id}">
                    <div class="node-content">
                        <span class="node-id">#${node.id}</span>
                        <div class="node-text">${node.content.substring(0, 80)}${node.content.length > 80 ? '...' : ''}</div>
                        <div style="font-size: 12px; color: #888; margin-top: 4px;">
                            ${node.is_player_choice == 1 ? '玩家选择' : 'NPC对话'} |
                            下一节点: ${node.next_node_ids ? JSON.parse(node.next_node_ids).join(', ') || '无' : '无'}
                        </div>
                    </div>
                    <div class="node-actions">
                        <button type="button" class="node-action-btn edit" onclick="event.preventDefault(); event.stopPropagation(); return editDialogueNode(${node.id});">编辑</button>
                        <button type="button" class="node-action-btn delete" onclick="event.preventDefault(); event.stopPropagation(); confirmDeleteNode(${node.id}); return false;">删除</button>
                    </div>
                </div>
            `).join('');
        } else {
            nodeCount.textContent = '(0个节点)';
            nodesList.innerHTML = '<div class="node-item">暂无节点</div>';
        }
    })
    .catch(error => {
        console.error('加载对话节点失败:', error);
        document.getElementById('dialogue-nodes-list').innerHTML = '<div class="node-item">加载失败</div>';
    });
}

function showToast(message, type = 'info') {
    // 简单的提示功能
    const toast = document.createElement('div');
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 12px 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        border-radius: 4px;
        z-index: 10001;
        animation: slideIn 0.3s ease;
    `;
    toast.textContent = message;
    document.body.appendChild(toast);

    setTimeout(() => {
        toast.remove();
    }, 3000);
}

// 为QuestWorkflowVisualization类添加方法
QuestWorkflowVisualization.prototype.openQuestEditModal = openQuestEditModal;
QuestWorkflowVisualization.prototype.openDialogueEditModal = openDialogueEditModal;
QuestWorkflowVisualization.prototype.getCurrentDialogueTree = function() {
    // 获取当前显示的对话树信息
    if (this.currentView === 'dialogue-tree' && this.currentDialogueTreeId) {
        return this.data.dialogues.find(d => d.id == this.currentDialogueTreeId);
    }
    return null;
};

// 点击模态框外部关闭 - 使用延迟绑定避免冲突
let modalClickHandlerBound = false;

function bindModalClickHandler() {
    if (modalClickHandlerBound) return;

    document.addEventListener('click', (e) => {
        // 检查是否正在操作模态框
        if (window.modalState.isOpening || window.modalState.isClosing) {
            console.log('模态框正在操作中，忽略点击事件');
            return;
        }

        // 检查是否是模态框刚刚显示，如果是则忽略这次点击
        if (e.target.classList.contains('modal-overlay')) {
            const modal = e.target;
            const modalShowTime = modal.dataset.showTime;
            const currentTime = Date.now();

            // 如果模态框显示时间少于400ms，忽略点击
            if (modalShowTime && (currentTime - parseInt(modalShowTime)) < 400) {
                console.log('忽略模态框刚显示时的点击事件，时间差:', currentTime - parseInt(modalShowTime));
                return;
            }

            // 检查点击的确实是模态框背景，而不是内容区域
            if (e.target === modal) {
                console.log('点击了模态框外部，目标元素ID:', e.target.id);
                if (e.target.id === 'quest-edit-modal') {
                    console.log('关闭任务编辑模态框');
                    closeQuestEditModal();
                } else if (e.target.id === 'dialogue-edit-modal') {
                    console.log('关闭对话编辑模态框');
                    closeDialogueEditModal();
                }
            } else {
                console.log('点击了模态框内容区域，不关闭模态框');
            }
        }
    });

    modalClickHandlerBound = true;
}

// 全局模态框状态管理
window.modalState = {
    isOpening: false,
    isClosing: false,
    currentModal: null
};

// 延迟绑定点击处理器
setTimeout(bindModalClickHandler, 1000);

// ESC键关闭模态框
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        const questModal = document.getElementById('quest-edit-modal');
        const dialogueModal = document.getElementById('dialogue-edit-modal');

        if (questModal.style.display === 'flex') {
            closeQuestEditModal();
        } else if (dialogueModal.style.display === 'flex') {
            closeDialogueEditModal();
        }
    }
});

// 节点编辑功能
function editDialogueNode(nodeId) {
    console.log('开始编辑对话节点:', nodeId);

    // 阻止任何可能的表单提交或页面跳转
    try {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
        }
    } catch (e) {
        console.log('无法获取事件对象:', e);
    }

    const node = window.currentDialogueNodes.find(n => n.id == nodeId);
    if (!node) {
        showToast('节点不存在', 'error');
        return false;
    }

    // 检查模态框是否仍然显示
    const modal = document.getElementById('dialogue-edit-modal');
    if (!modal || modal.style.display !== 'flex') {
        console.warn('对话编辑模态框未显示，无法编辑节点');
        showToast('请先打开对话树编辑界面', 'error');
        return false;
    }

    // 显示编辑区域
    const editSection = document.getElementById('node-edit-section');
    if (!editSection) {
        console.error('找不到节点编辑区域');
        return;
    }

    editSection.style.display = 'block';

    // 填充节点数据
    document.getElementById('current-node-id').textContent = `#${node.id}`;
    document.getElementById('edit-node-content').value = node.content || '';
    document.getElementById('edit-node-is-player-choice').checked = node.is_player_choice == 1;

    // 处理下一节点ID
    let nextNodes = '';
    if (node.next_node_ids) {
        try {
            const nextNodeIds = JSON.parse(node.next_node_ids);
            nextNodes = Array.isArray(nextNodeIds) ? nextNodeIds.join(', ') : '';
        } catch (e) {
            nextNodes = '';
        }
    }
    document.getElementById('edit-node-next-nodes').value = nextNodes;

    // 填充条件和动作脚本
    document.getElementById('edit-node-condition').value = node.condition_script || '';
    document.getElementById('edit-node-action').value = node.action_script || '';

    // 初始化脚本构建器
    if (!window.conditionBuilder) {
        window.conditionBuilder = new ScriptBuilder('condition');
        window.conditionBuilder.init();
    }

    if (!window.actionBuilder) {
        window.actionBuilder = new ScriptBuilder('action');
        window.actionBuilder.init();
    }

    // 反序列化脚本数据到可视化编辑器
    setTimeout(() => {
        if (window.conditionBuilder) {
            window.conditionBuilder.deserialize();
        }
        if (window.actionBuilder) {
            window.actionBuilder.deserialize();
        }
    }, 100);

    // 高亮当前编辑的节点
    document.querySelectorAll('.node-item').forEach(item => {
        item.classList.remove('editing');
    });
    const nodeElement = document.querySelector(`[data-node-id="${nodeId}"]`);
    if (nodeElement) {
        nodeElement.classList.add('editing');
    }

    // 存储当前编辑的节点ID
    window.currentEditingNodeId = nodeId;

    // 延迟滚动，避免触发其他事件
    setTimeout(() => {
        try {
            editSection.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            console.log('节点编辑区域已显示');
        } catch (e) {
            console.warn('滚动到编辑区域失败:', e);
        }
    }, 100);

    return false; // 阻止任何默认行为
}

function saveNodeEdit() {
    const nodeId = window.currentEditingNodeId;
    if (!nodeId) {
        showToast('没有正在编辑的节点', 'error');
        return;
    }

    const dialogueTreeId = document.getElementById('edit-dialogue-id').value;
    const content = document.getElementById('edit-node-content').value.trim();
    const isPlayerChoice = document.getElementById('edit-node-is-player-choice').checked ? 1 : 0;
    const nextNodesText = document.getElementById('edit-node-next-nodes').value.trim();
    const conditionScript = document.getElementById('edit-node-condition').value.trim();
    const actionScript = document.getElementById('edit-node-action').value.trim();

    if (!content) {
        showToast('节点内容不能为空', 'error');
        return;
    }

    // 处理下一节点ID
    let nextNodeIds = '[]';
    if (nextNodesText) {
        try {
            const ids = nextNodesText.split(',').map(id => id.trim()).filter(id => id);
            nextNodeIds = JSON.stringify(ids);
        } catch (e) {
            showToast('下一节点ID格式错误', 'error');
            return;
        }
    }

    // 验证JSON格式
    if (conditionScript) {
        try {
            JSON.parse(conditionScript);
        } catch (e) {
            showToast('条件脚本JSON格式错误', 'error');
            return;
        }
    }

    if (actionScript) {
        try {
            JSON.parse(actionScript);
        } catch (e) {
            showToast('动作脚本JSON格式错误', 'error');
            return;
        }
    }

    // 发送更新请求
    fetch('api_quest_workflow.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'update_dialogue_node',
            node_id: nodeId,
            content: content,
            is_player_choice: isPlayerChoice,
            next_node_ids: nextNodeIds,
            condition_script: conditionScript || null,
            action_script: actionScript || null
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast('节点更新成功', 'success');
            // 重新加载节点列表
            loadDialogueNodes(dialogueTreeId);
            cancelNodeEdit();
        } else {
            showToast('节点更新失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('更新节点失败:', error);
        showToast('节点更新失败', 'error');
    });
}

function cancelNodeEdit() {
    console.log('取消节点编辑');

    const editSection = document.getElementById('node-edit-section');
    if (editSection) {
        editSection.style.display = 'none';
    }

    document.querySelectorAll('.node-item').forEach(item => {
        item.classList.remove('editing');
    });

    // 清理编辑状态
    window.currentEditingNodeId = null;

    // 清理表单数据
    const form = document.getElementById('dialogue-edit-form');
    if (form) {
        const editFields = [
            'edit-node-content',
            'edit-node-next-nodes',
            'edit-node-condition',
            'edit-node-action'
        ];

        editFields.forEach(fieldId => {
            const field = document.getElementById(fieldId);
            if (field) {
                field.value = '';
            }
        });

        const checkbox = document.getElementById('edit-node-is-player-choice');
        if (checkbox) {
            checkbox.checked = false;
        }
    }
}

function addNewDialogueNode() {
    // 阻止任何可能的表单提交或页面跳转
    try {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
        }
    } catch (e) {
        console.log('无法获取事件对象:', e);
    }

    const dialogueTreeId = document.getElementById('edit-dialogue-id').value;

    // 发送创建请求
    fetch('api_quest_workflow.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'create_dialogue_node',
            tree_id: dialogueTreeId,
            content: '新建对话节点',
            is_player_choice: 0,
            next_node_ids: '[]'
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast('节点创建成功', 'success');
            // 重新加载节点列表
            loadDialogueNodes(dialogueTreeId);
            // 自动编辑新创建的节点
            setTimeout(() => {
                editDialogueNode(result.node_id);
            }, 500);
        } else {
            showToast('节点创建失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('创建节点失败:', error);
        showToast('节点创建失败', 'error');
    });

    return false; // 阻止任何默认行为
}

function confirmDeleteNode(nodeId) {
    // 阻止任何可能的表单提交或页面跳转
    try {
        if (event) {
            event.preventDefault();
            event.stopPropagation();
            event.stopImmediatePropagation();
        }
    } catch (e) {
        console.log('无法获取事件对象:', e);
    }

    if (confirm('确定要删除这个对话节点吗？此操作不可撤销。')) {
        deleteDialogueNode(nodeId);
    }
    return false;
}

function deleteDialogueNode(nodeId) {
    const dialogueTreeId = document.getElementById('edit-dialogue-id').value;

    // 如果正在编辑这个节点，先取消编辑
    if (window.currentEditingNodeId == nodeId) {
        cancelNodeEdit();
    }

    // 发送删除请求
    fetch('api_quest_workflow.php', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            action: 'delete_dialogue_node',
            node_id: nodeId
        })
    })
    .then(response => response.json())
    .then(result => {
        if (result.success) {
            showToast('节点删除成功', 'success');
            // 重新加载节点列表
            loadDialogueNodes(dialogueTreeId);
        } else {
            showToast('节点删除失败: ' + result.message, 'error');
        }
    })
    .catch(error => {
        console.error('删除节点失败:', error);
        showToast('节点删除失败', 'error');
    });
}

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
`;
document.head.appendChild(style);

// 脚本构建器功能 - 参考对话树编辑页面实现
document.addEventListener('DOMContentLoaded', function() {
    // PHP数据注入到JS - 完全参考对话树编辑页面
    window.ALL_ITEMS = <?php echo json_encode($all_items); ?>;
    window.ALL_QUESTS = <?php echo json_encode($all_quests); ?>;
    window.PLAYER_ATTRIBUTES = <?php echo json_encode($player_attributes ?? []); ?>;
    window.ATTRIBUTE_NAMES = <?php echo json_encode($attributeNames ?? []); ?>;

    console.log('加载物品数据:', window.ALL_ITEMS.length, '个物品');
    console.log('加载任务数据:', window.ALL_QUESTS.length, '个任务');
    console.log('加载玩家属性:', window.PLAYER_ATTRIBUTES.length, '个属性');

    // 初始化脚本构建器
    if (document.getElementById('condition-builder')) {
        window.conditionBuilder = new ScriptBuilder('condition');
        window.conditionBuilder.init();
    }

    if (document.getElementById('action-builder')) {
        window.actionBuilder = new ScriptBuilder('action');
        window.actionBuilder.init();
    }
});

// 脚本构建器类
class ScriptBuilder {
    constructor(type) {
        this.type = type; // 'condition' 或 'action'
        this.container = document.getElementById(`${type}-list`);
        this.hiddenInput = document.getElementById(`edit-node-${type === 'condition' ? 'condition' : 'action'}`);
        this.logicalOpSelect = type === 'condition' ? document.getElementById('condition-logical-operator') : null;
        this.addButton = document.getElementById(`add-${type}-btn`);
    }

    init() {
        if (!this.container) return;

        if (this.logicalOpSelect) {
            this.logicalOpSelect.addEventListener('change', () => this.serialize());
        }

        if (this.addButton) {
            this.addButton.addEventListener('click', () => this.add());
        }

        this.deserialize();
    }

    add(data = {}) {
        const item = document.createElement('div');
        item.className = 'script-item';

        if (this.type === 'condition') {
            item.innerHTML = this.getConditionItemHTML(data);
        } else {
            item.innerHTML = this.getActionItemHTML(data);
        }

        this.container.appendChild(item);

        // 绑定事件
        this.bindItemEvents(item, data);
        this.serialize();
    }

    getConditionItemHTML(data = {}) {
        return `
            <select class="condition-type">
                <option value="">-- 选择条件类型 --</option>
                <option value="player_attribute" ${data.type === 'player_attribute' ? 'selected' : ''}>玩家属性</option>
                <option value="has_item" ${data.type === 'has_item' ? 'selected' : ''}>拥有物品</option>
                <option value="does_not_have_item" ${data.type === 'does_not_have_item' ? 'selected' : ''}>不拥有物品</option>
                <option value="quest_status" ${data.type === 'quest_status' ? 'selected' : ''}>任务状态</option>
            </select>
            <div class="script-item-params"></div>
            <button type="button" class="btn-remove-script"><i class="fas fa-trash"></i></button>
        `;
    }

    getActionItemHTML(data = {}) {
        return `
            <select class="action-type">
                <option value="">-- 选择动作类型 --</option>
                <option value="give_item" ${data.type === 'give_item' ? 'selected' : ''}>给予物品</option>
                <option value="remove_item" ${data.type === 'remove_item' ? 'selected' : ''}>移除物品</option>
                <option value="give_exp" ${data.type === 'give_exp' ? 'selected' : ''}>给予经验</option>
                <option value="give_gold" ${data.type === 'give_gold' ? 'selected' : ''}>给予金币</option>
                <option value="set_quest_status" ${data.type === 'set_quest_status' ? 'selected' : ''}>设置任务状态</option>
                <option value="teleport_player" ${data.type === 'teleport_player' ? 'selected' : ''}>传送玩家</option>
            </select>
            <div class="script-item-params"></div>
            <button type="button" class="btn-remove-script"><i class="fas fa-trash"></i></button>
        `;
    }

    bindItemEvents(item, data = {}) {
        const typeSelect = item.querySelector(this.type === 'condition' ? '.condition-type' : '.action-type');
        const removeBtn = item.querySelector('.btn-remove-script');
        const paramsContainer = item.querySelector('.script-item-params');

        // 类型选择事件
        typeSelect.addEventListener('change', (e) => {
            this.updateItemParams(paramsContainer, e.target.value, data);
            this.serialize();
        });

        // 删除按钮事件
        removeBtn.addEventListener('click', () => {
            item.remove();
            this.serialize();
        });

        // 初始化参数
        if (typeSelect.value) {
            this.updateItemParams(paramsContainer, typeSelect.value, data);
        }
    }

    updateItemParams(container, type, data = {}) {
        container.innerHTML = '';

        if (this.type === 'condition') {
            this.updateConditionParams(container, type, data);
        } else {
            this.updateActionParams(container, type, data);
        }

        // 绑定参数变化事件
        container.querySelectorAll('input, select, textarea').forEach(input => {
            input.addEventListener('change', () => this.serialize());
            input.addEventListener('input', () => this.serialize());
        });
    }

    updateConditionParams(container, type, data = {}) {
        let html = '';

        switch (type) {
            case 'player_attribute':
                // 生成属性选择下拉框 - 完全参考对话树编辑页面
                const attrOptions = (window.PLAYER_ATTRIBUTES || []).map(attr => {
                    const displayName = window.ATTRIBUTE_NAMES[attr] ? `${window.ATTRIBUTE_NAMES[attr]} (${attr})` : attr;
                    return `<option value="${attr}" ${data.attribute === attr ? 'selected' : ''}>${displayName}</option>`;
                }).join('');

                html = `
                    <div>
                        <label>属性名称</label>
                        <select name="attribute">
                            <option value="">选择属性</option>
                            ${attrOptions}
                        </select>
                    </div>
                    <div>
                        <label>比较运算符</label>
                        <select name="operator">
                            <option value=">=" ${data.operator === '>=' ? 'selected' : ''}>大于等于</option>
                            <option value="<=" ${data.operator === '<=' ? 'selected' : ''}>小于等于</option>
                            <option value="==" ${data.operator === '==' ? 'selected' : ''}>等于</option>
                            <option value="!=" ${data.operator === '!=' ? 'selected' : ''}>不等于</option>
                        </select>
                    </div>
                    <div>
                        <label>数值</label>
                        <input type="number" name="value" value="${data.value || ''}" placeholder="输入数值">
                    </div>
                `;
                break;

            case 'has_item':
            case 'does_not_have_item':
                html = `
                    <div>
                        <label>物品</label>
                        <div class="item-selector">
                            <input type="hidden" name="item_id" value="${data.item_id || ''}">
                            <input type="text" class="item-selector-input" placeholder="点击选择物品" readonly>
                            <div class="item-selector-dropdown">
                                <input type="text" class="item-selector-search" placeholder="搜索物品...">
                                <div class="item-selector-categories"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label>数量</label>
                        <input type="number" name="quantity" value="${data.quantity || 1}" min="1" placeholder="数量">
                    </div>
                `;
                break;

            case 'quest_status':
                // 生成任务选择下拉框
                const questOptions = (window.ALL_QUESTS || []).map(quest =>
                    `<option value="${quest.id}" ${data.quest_id == quest.id ? 'selected' : ''}>${quest.title}</option>`
                ).join('');

                html = `
                    <div>
                        <label>任务</label>
                        <select name="quest_id">
                            <option value="">选择任务</option>
                            ${questOptions}
                        </select>
                    </div>
                    <div>
                        <label>任务状态</label>
                        <select name="status">
                            <option value="not_started" ${data.status === 'not_started' ? 'selected' : ''}>未开始</option>
                            <option value="in_progress" ${data.status === 'in_progress' ? 'selected' : ''}>进行中</option>
                            <option value="completed" ${data.status === 'completed' ? 'selected' : ''}>已完成</option>
                            <option value="failed" ${data.status === 'failed' ? 'selected' : ''}>已失败</option>
                        </select>
                    </div>
                `;
                break;
        }

        container.innerHTML = html;

        // 初始化物品选择器
        if (type === 'has_item' || type === 'does_not_have_item') {
            this.initItemSelector(container, data.item_id);
        }
    }

    updateActionParams(container, type, data = {}) {
        let html = '';

        switch (type) {
            case 'give_item':
            case 'remove_item':
                html = `
                    <div>
                        <label>物品</label>
                        <div class="item-selector">
                            <input type="hidden" name="item_id" value="${data.item_id || ''}">
                            <input type="text" class="item-selector-input" placeholder="点击选择物品" readonly>
                            <div class="item-selector-dropdown">
                                <input type="text" class="item-selector-search" placeholder="搜索物品...">
                                <div class="item-selector-categories"></div>
                            </div>
                        </div>
                    </div>
                    <div>
                        <label>数量</label>
                        <input type="number" name="quantity" value="${data.quantity || 1}" min="1" placeholder="数量">
                    </div>
                `;
                break;

            case 'give_exp':
                html = `
                    <div>
                        <label>经验值</label>
                        <input type="number" name="amount" value="${data.amount || ''}" min="0" placeholder="经验值">
                    </div>
                `;
                break;

            case 'give_gold':
                html = `
                    <div>
                        <label>金币数量</label>
                        <input type="number" name="amount" value="${data.amount || ''}" min="0" placeholder="金币数量">
                    </div>
                `;
                break;

            case 'set_quest_status':
                // 生成任务选择下拉框
                const questOptions = (window.ALL_QUESTS || []).map(quest =>
                    `<option value="${quest.id}" ${data.quest_id == quest.id ? 'selected' : ''}>${quest.title}</option>`
                ).join('');

                html = `
                    <div>
                        <label>任务</label>
                        <select name="quest_id">
                            <option value="">选择任务</option>
                            ${questOptions}
                        </select>
                    </div>
                    <div>
                        <label>任务状态</label>
                        <select name="status">
                            <option value="in_progress" ${data.status === 'in_progress' ? 'selected' : ''}>开始任务</option>
                            <option value="completed" ${data.status === 'completed' ? 'selected' : ''}>完成任务</option>
                            <option value="failed" ${data.status === 'failed' ? 'selected' : ''}>失败任务</option>
                        </select>
                    </div>
                `;
                break;

            case 'teleport_player':
                html = `
                    <div>
                        <label>场景ID</label>
                        <input type="number" name="scene_id" value="${data.scene_id || ''}" placeholder="场景ID">
                    </div>
                    <div>
                        <label>X坐标</label>
                        <input type="number" name="x" value="${data.x || ''}" placeholder="X坐标">
                    </div>
                    <div>
                        <label>Y坐标</label>
                        <input type="number" name="y" value="${data.y || ''}" placeholder="Y坐标">
                    </div>
                `;
                break;
        }

        container.innerHTML = html;

        // 初始化物品选择器
        if (type === 'give_item' || type === 'remove_item') {
            this.initItemSelector(container, data.item_id);
        }
    }

    // 初始化物品选择器 - 参考对话树编辑页面实现
    initItemSelector(container, selectedItemId) {
        const self = this;
        const hiddenInput = container.querySelector('input[name="item_id"]');
        const displayInput = container.querySelector('.item-selector-input');
        const dropdown = container.querySelector('.item-selector-dropdown');
        const searchInput = container.querySelector('.item-selector-search');
        const categoriesContainer = container.querySelector('.item-selector-categories');

        // 使用真实的物品数据 - 完全参考对话树编辑页面
        const ALL_ITEMS = window.ALL_ITEMS || [];

        // 按分类组织物品
        const itemsByCategory = {};
        ALL_ITEMS.forEach(item => {
            if (!itemsByCategory[item.category]) {
                itemsByCategory[item.category] = [];
            }
            itemsByCategory[item.category].push(item);
        });

        // 渲染分类和物品
        function renderItems(searchTerm = '') {
            categoriesContainer.innerHTML = '';

            Object.keys(itemsByCategory).forEach(category => {
                const items = itemsByCategory[category].filter(item =>
                    !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase())
                );

                if (items.length === 0) return;

                // 分类标题
                const categoryHeader = document.createElement('div');
                categoryHeader.className = 'category-header';
                categoryHeader.textContent = category;
                categoriesContainer.appendChild(categoryHeader);

                // 物品选项
                items.forEach(item => {
                    const itemDiv = document.createElement('div');
                    itemDiv.className = 'item-option';
                    itemDiv.textContent = item.name;
                    itemDiv.dataset.itemId = item.id;
                    itemDiv.dataset.itemName = item.name;

                    if (item.id == selectedItemId) {
                        itemDiv.classList.add('selected');
                        displayInput.value = item.name;
                    }

                    itemDiv.addEventListener('click', () => {
                        hiddenInput.value = item.id;
                        displayInput.value = item.name;
                        dropdown.style.display = 'none';

                        // 清除其他选中状态
                        categoriesContainer.querySelectorAll('.item-option').forEach(opt => {
                            opt.classList.remove('selected');
                        });
                        itemDiv.classList.add('selected');

                        // 触发序列化
                        self.serialize();
                    });

                    categoriesContainer.appendChild(itemDiv);
                });
            });
        }

        // 显示下拉框
        displayInput.addEventListener('click', () => {
            dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
            if (dropdown.style.display === 'block') {
                renderItems();
                searchInput.focus();
            }
        });

        // 搜索功能
        searchInput.addEventListener('input', (e) => {
            renderItems(e.target.value);
        });

        // 点击外部关闭下拉框
        document.addEventListener('click', (e) => {
            if (!container.contains(e.target)) {
                dropdown.style.display = 'none';
            }
        });

        // 初始化显示
        if (selectedItemId) {
            const selectedItem = ALL_ITEMS.find(item => item.id == selectedItemId);
            if (selectedItem) {
                displayInput.value = selectedItem.name;
            }
        }
    }

    // 序列化为JSON
    serialize() {
        if (!this.hiddenInput) return;

        const data = {};

        if (this.type === 'condition') {
            data.logical_operator = this.logicalOpSelect ? this.logicalOpSelect.value : 'AND';
            data.conditions = [];

            this.container.querySelectorAll('.script-item').forEach(item => {
                const typeSelect = item.querySelector('.condition-type');
                if (!typeSelect || !typeSelect.value) return;

                const condition = {
                    type: typeSelect.value
                };

                // 收集参数
                const params = item.querySelector('.script-item-params');
                if (params) {
                    params.querySelectorAll('input, select, textarea').forEach(input => {
                        if (input.name && input.value) {
                            condition[input.name] = input.value;
                        }
                    });
                }

                data.conditions.push(condition);
            });
        } else {
            data.actions = [];

            this.container.querySelectorAll('.script-item').forEach(item => {
                const typeSelect = item.querySelector('.action-type');
                if (!typeSelect || !typeSelect.value) return;

                const action = {
                    type: typeSelect.value
                };

                // 收集参数
                const params = item.querySelector('.script-item-params');
                if (params) {
                    params.querySelectorAll('input, select, textarea').forEach(input => {
                        if (input.name && input.value) {
                            action[input.name] = input.value;
                        }
                    });
                }

                data.actions.push(action);
            });
        }

        this.hiddenInput.value = JSON.stringify(data, null, 2);
        console.log(`${this.type} 脚本序列化:`, data);
    }

    // 从JSON反序列化
    deserialize() {
        if (!this.hiddenInput || !this.hiddenInput.value) return;

        try {
            const data = JSON.parse(this.hiddenInput.value);
            console.log(`${this.type} 脚本反序列化:`, data);

            // 清空现有项目
            this.container.innerHTML = '';

            if (this.type === 'condition') {
                // 设置逻辑运算符
                if (this.logicalOpSelect && data.logical_operator) {
                    this.logicalOpSelect.value = data.logical_operator;
                }

                // 添加条件项
                if (data.conditions && Array.isArray(data.conditions)) {
                    data.conditions.forEach(condition => {
                        this.add(condition);
                    });
                }
            } else {
                // 添加动作项
                if (data.actions && Array.isArray(data.actions)) {
                    data.actions.forEach(action => {
                        this.add(action);
                    });
                }
            }
        } catch (e) {
            console.error(`${this.type} 脚本反序列化失败:`, e);
        }
    }

    // 清空所有项目
    clear() {
        this.container.innerHTML = '';
        if (this.logicalOpSelect) {
            this.logicalOpSelect.value = 'AND';
        }
        this.serialize();
    }
}

function updateConditionFields(itemId, conditionType) {
    const fieldsContainer = document.getElementById(`condition-fields-${itemId}`);
    let fieldsHtml = '';

    switch (conditionType) {
        case 'item_count':
            fieldsHtml = `
                <div class="script-field">
                    <label>物品ID</label>
                    <input type="number" placeholder="物品ID">
                </div>
                <div class="script-field">
                    <label>比较运算符</label>
                    <select>
                        <option value=">=">大于等于</option>
                        <option value="<=">小于等于</option>
                        <option value="==">等于</option>
                        <option value="!=">不等于</option>
                    </select>
                </div>
                <div class="script-field">
                    <label>数量</label>
                    <input type="number" placeholder="数量" min="0">
                </div>
            `;
            break;
        case 'player_level':
            fieldsHtml = `
                <div class="script-field">
                    <label>比较运算符</label>
                    <select>
                        <option value=">=">大于等于</option>
                        <option value="<=">小于等于</option>
                        <option value="==">等于</option>
                    </select>
                </div>
                <div class="script-field">
                    <label>等级</label>
                    <input type="number" placeholder="等级" min="1">
                </div>
            `;
            break;
        case 'quest_status':
            fieldsHtml = `
                <div class="script-field">
                    <label>任务ID</label>
                    <input type="number" placeholder="任务ID">
                </div>
                <div class="script-field">
                    <label>任务状态</label>
                    <select>
                        <option value="not_started">未开始</option>
                        <option value="in_progress">进行中</option>
                        <option value="completed">已完成</option>
                        <option value="failed">已失败</option>
                    </select>
                </div>
            `;
            break;
        case 'custom_script':
            fieldsHtml = `
                <div class="script-field" style="grid-column: 1 / -1;">
                    <label>自定义脚本</label>
                    <textarea placeholder="输入自定义脚本代码"></textarea>
                </div>
            `;
            break;
    }

    fieldsContainer.innerHTML = fieldsHtml;
}

function updateActionFields(itemId, actionType) {
    const fieldsContainer = document.getElementById(`action-fields-${itemId}`);
    let fieldsHtml = '';

    switch (actionType) {
        case 'give_item':
        case 'remove_item':
            fieldsHtml = `
                <div class="script-field">
                    <label>物品ID</label>
                    <input type="number" placeholder="物品ID">
                </div>
                <div class="script-field">
                    <label>数量</label>
                    <input type="number" placeholder="数量" min="1">
                </div>
            `;
            break;
        case 'give_exp':
            fieldsHtml = `
                <div class="script-field">
                    <label>经验值</label>
                    <input type="number" placeholder="经验值" min="0">
                </div>
            `;
            break;
        case 'give_gold':
            fieldsHtml = `
                <div class="script-field">
                    <label>金币数量</label>
                    <input type="number" placeholder="金币数量" min="0">
                </div>
            `;
            break;
        case 'set_quest_status':
            fieldsHtml = `
                <div class="script-field">
                    <label>任务ID</label>
                    <input type="number" placeholder="任务ID">
                </div>
                <div class="script-field">
                    <label>任务状态</label>
                    <select>
                        <option value="in_progress">开始任务</option>
                        <option value="completed">完成任务</option>
                        <option value="failed">失败任务</option>
                    </select>
                </div>
            `;
            break;
        case 'teleport_player':
            fieldsHtml = `
                <div class="script-field">
                    <label>场景ID</label>
                    <input type="number" placeholder="场景ID">
                </div>
                <div class="script-field">
                    <label>X坐标</label>
                    <input type="number" placeholder="X坐标">
                </div>
                <div class="script-field">
                    <label>Y坐标</label>
                    <input type="number" placeholder="Y坐标">
                </div>
            `;
            break;
        case 'custom_script':
            fieldsHtml = `
                <div class="script-field" style="grid-column: 1 / -1;">
                    <label>自定义脚本</label>
                    <textarea placeholder="输入自定义脚本代码"></textarea>
                </div>
            `;
            break;
    }

    fieldsContainer.innerHTML = fieldsHtml;
}

function syncVisualToJson(scriptType) {
    // 将可视化编辑的内容同步到JSON编辑器
    console.log('同步可视化编辑到JSON:', scriptType);
    // 这里可以实现具体的同步逻辑
}

function syncJsonToVisual(scriptType) {
    // 将JSON编辑的内容同步到可视化编辑器
    console.log('同步JSON到可视化编辑:', scriptType);
    // 这里可以实现具体的同步逻辑
}
</script>

<?php require_once 'layout_footer.php'; ?>
