document.addEventListener('DOMContentLoaded', function() {
    const API_URL = 'api_recipes.php';

    // Modal elements
    const modal = document.getElementById('recipeModal');
    const modalTitle = document.getElementById('modalTitle');
    const recipeForm = document.getElementById('recipe-form');
    const materialsContainer = document.getElementById('materials-container');
    const materialRowTemplate = document.getElementById('material-row-template');
    const categoryFilter = document.getElementById('recipe-result-category-filter');
    const resultItemSelect = document.getElementById('recipe-result_item_id');

    // Main page elements
    const tbody = document.getElementById('recipes-tbody');
    const paginationContainer = document.querySelector('.pagination');
    const searchInput = document.getElementById('search-input');

    let currentPage = 1;
    let currentSearch = '';

    // Fetch and render recipes
    async function fetchRecipes(page = 1, search = '') {
        try {
            const response = await fetch(`${API_URL}?action=get_recipes&page=${page}&search=${search}`);
            const data = await response.json();
            if (data.success) {
                renderTable(data.recipes);
                renderPagination(data.pagination);
            } else {
                tbody.innerHTML = `<tr><td colspan="7">加载失败: ${data.message}</td></tr>`;
            }
        } catch (error) {
            tbody.innerHTML = `<tr><td colspan="7">加载出错: ${error.message}</td></tr>`;
        }
    }

    // Render table rows
    function renderTable(recipes) {
        tbody.innerHTML = '';
        if (recipes.length === 0) {
            tbody.innerHTML = '<tr><td colspan="7">没有找到配方。</td></tr>';
            return;
        }
        recipes.forEach(recipe => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td>${recipe.id}</td>
                <td>${escapeHTML(recipe.name)}</td>
                <td>${escapeHTML(recipe.item_name || 'N/A')}</td>
                <td>${recipe.result_quantity}</td>
                <td>${recipe.craft_level}</td>
                <td>${recipe.material_count}</td>
                <td>
                    <button class="btn btn-sm btn-primary edit-btn" data-id="${recipe.id}">编辑</button>
                    <button class="btn btn-sm btn-danger delete-btn" data-id="${recipe.id}">删除</button>
                </td>
            `;
            tbody.appendChild(tr);
        });
    }

    // Render pagination controls
    function renderPagination(pagination) {
        paginationContainer.innerHTML = '';
        if (pagination.total_pages <= 1) return;

        for (let i = 1; i <= pagination.total_pages; i++) {
            const pageButton = document.createElement('button');
            pageButton.textContent = i;
            pageButton.className = 'btn btn-sm ' + (i === pagination.current_page ? 'btn-primary' : 'btn-secondary');
            pageButton.addEventListener('click', () => {
                currentPage = i;
                fetchRecipes(currentPage, currentSearch);
            });
            paginationContainer.appendChild(pageButton);
        }
    }

    // Update the result items dropdown based on the selected category
    function updateResultItemsDropdown(selectedCategory = 'All', selectedItemId = null) {
        const filteredItems = selectedCategory === 'All' 
            ? allItemsData
            : allItemsData.filter(item => item.category === selectedCategory);
        
        // Clear previous options
        resultItemSelect.innerHTML = '<option value="">选择物品</option>';

        filteredItems.forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${escapeHTML(item.name)} [${item.category_cn}]`;
            resultItemSelect.appendChild(option);
        });

        // Restore selection if possible
        if (selectedItemId) {
            $(resultItemSelect).val(selectedItemId).trigger('change');
        }
    }

    // Show/Hide Modal
    window.showRecipeModal = function(id = null) {
        recipeForm.reset();

        // Destroy existing Select2 instances to prevent re-initialization errors
        if ($(resultItemSelect).data('select2')) {
            $(resultItemSelect).select2('destroy');
        }
        $(materialsContainer).find('.material-select').each(function() {
            if ($(this).data('select2')) {
                $(this).select2('destroy');
            }
        });
        materialsContainer.innerHTML = '';

        modal.style.display = 'flex'; // Make modal visible BEFORE initializing plugins

        // Reset and populate the result item dropdown
        categoryFilter.value = 'All';
        updateResultItemsDropdown();

        // Initialize the static Select2 dropdown
        $(resultItemSelect).select2({
            placeholder: '选择物品',
            dropdownParent: $(modal)
        });

        if (id) {
            modalTitle.textContent = '编辑配方';
            fetch(`${API_URL}?action=get_recipe&id=${id}`)
                .then(res => res.json())
                .then(data => {
                    if (data.success) {
                        const recipe = data.recipe;
                        const resultItem = allItemsData.find(item => item.id == recipe.result_item_id);
                        
                        document.getElementById('recipe-id').value = recipe.id;
                        document.getElementById('recipe-name').value = recipe.name;
                        
                        // Set filter and update dropdown
                        if (resultItem) {
                            categoryFilter.value = resultItem.category;
                            updateResultItemsDropdown(resultItem.category, recipe.result_item_id);
                        }
                        
                        document.getElementById('recipe-result_quantity').value = recipe.result_quantity;
                        document.getElementById('recipe-craft_level').value = recipe.craft_level;
                        document.getElementById('recipe-description').value = recipe.description;

                        if (recipe.materials && recipe.materials.length > 0) {
                            recipe.materials.forEach(mat => addMaterialRow(mat.material_item_id, mat.quantity));
                        } else {
                            addMaterialRow(); // Add one empty row if no materials
                        }
                    }
                });
        } else {
            modalTitle.textContent = '添加新配方';
            document.getElementById('recipe-id').value = '';
            addMaterialRow(); // Add one empty row for new recipes
        }
    }

    window.hideRecipeModal = function() {
        modal.style.display = 'none';
    }

    // Add a new material row to the form
    function addMaterialRow(materialId = '', quantity = 1) {
        const templateClone = materialRowTemplate.content.cloneNode(true);
        const newRow = templateClone.querySelector('.material-row');
        
        const select = newRow.querySelector('.material-select');
        select.value = materialId;

        const quantityInput = newRow.querySelector('input[type="number"]');
        quantityInput.value = quantity;
        
        materialsContainer.appendChild(newRow);

        // Initialize Select2 on the new row's select element
        $(select).select2({
            placeholder: '选择材料',
            dropdownParent: $(modal) // Important for modals
        });
    }
    
    // Handle form submission
    recipeForm.addEventListener('submit', async function(e) {
        e.preventDefault();
        const formData = new FormData(recipeForm);
        const id = formData.get('id');
        
        const materialIds = Array.from(document.querySelectorAll('.material-select')).map(s => s.value);
        const materialQtys = Array.from(document.querySelectorAll('input[name="material_quantities[]"]')).map(i => i.value);

        const materials = materialIds.map((matId, index) => ({
            id: matId,
            quantity: materialQtys[index]
        })).filter(m => m.id && m.quantity);

        const data = {
            id: id ? parseInt(id) : null,
            name: formData.get('name'),
            result_item_id: formData.get('result_item_id'),
            result_quantity: formData.get('result_quantity'),
            craft_level: formData.get('craft_level'),
            description: formData.get('description'),
            materials: materials
        };

        const response = await fetch(`${API_URL}?action=save_recipe`, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        if (result.success) {
            hideRecipeModal();
            fetchRecipes(currentPage, currentSearch);
        } else {
            alert(`保存失败: ${result.message}`);
        }
    });

    // Event Listeners
    document.getElementById('add-new-recipe-btn').addEventListener('click', () => showRecipeModal());

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            hideRecipeModal();
        }
    });

    document.getElementById('search-button').addEventListener('click', () => {
        currentPage = 1;
        currentSearch = searchInput.value;
        fetchRecipes(currentPage, currentSearch);
    });
    
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            document.getElementById('search-button').click();
        }
    });

    categoryFilter.addEventListener('change', function() {
        updateResultItemsDropdown(this.value);
    });

    document.getElementById('add-material-btn').addEventListener('click', () => addMaterialRow());

    materialsContainer.addEventListener('click', function(e) {
        if (e.target.classList.contains('remove-material-btn')) {
            $(e.target.closest('.material-row')).find('.material-select').select2('destroy');
            e.target.closest('.material-row').remove();
        }
    });

    tbody.addEventListener('click', function(e) {
        if (e.target.classList.contains('edit-btn')) {
            showRecipeModal(e.target.dataset.id);
        }
        if (e.target.classList.contains('delete-btn')) {
            const id = e.target.dataset.id;
            if (confirm(`确定要删除ID为 ${id} 的配方吗？`)) {
                fetch(`${API_URL}?action=delete_recipe`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id: id })
                })
                .then(res => res.json())
                .then(result => {
                    if (result.success) {
                        fetchRecipes(currentPage, currentSearch);
                    } else {
                        alert(`删除失败: ${result.message}`);
                    }
                });
            }
        }
    });
    
    fetchRecipes();
});

function escapeHTML(str) {
    if (str === null || str === undefined) {
        return '';
    }
    return str.toString()
      .replace(/&/g, '&amp;')
      .replace(/</g, '&lt;')
      .replace(/>/g, '&gt;')
      .replace(/"/g, '&quot;')
      .replace(/'/g, '&#039;');
} 