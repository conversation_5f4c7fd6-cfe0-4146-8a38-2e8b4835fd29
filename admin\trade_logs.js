$(document).ready(function() {
    let currentPage = 1;
    let pageSize = 20;
    let totalPages = 1;

    // 初始化页面
    init();

    function init() {
        loadScenes();
        loadTradeLogs();
        bindEvents();
        setDefaultDates();
    }

    // 设置默认日期（最近30天）
    function setDefaultDates() {
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        $('#date-range-start').val(formatDate(thirtyDaysAgo));
        $('#date-range-end').val(formatDate(today));
        
        $('#cleanup-date-start').val(formatDate(thirtyDaysAgo));
        $('#cleanup-date-end').val(formatDate(today));
    }

    function formatDate(date) {
        return date.toISOString().split('T')[0];
    }

    // 绑定事件
    function bindEvents() {
        $('#search-btn').click(function() {
            currentPage = 1;
            loadTradeLogs();
        });

        $('#reset-btn').click(function() {
            resetFilters();
        });

        $('#cleanup-btn').click(function() {
            confirmCleanup();
        });

        // 模态框关闭事件
        $('.modal-close-button, .modal-close').click(function() {
            $('#tradeDetailModal').hide();
        });

        // 点击模态框外部关闭
        $('#tradeDetailModal').click(function(e) {
            if (e.target === this) {
                $(this).hide();
            }
        });

        // 导出交易记录
        $('#export-trade-btn').click(function() {
            const tradeId = $('#modal-trade-id').text();
            exportTradeRecord(tradeId);
        });
    }

    // 加载场景列表
    function loadScenes() {
        $.get('api_trade_logs.php?action=get_scenes')
            .done(function(response) {
                if (response.success) {
                    const sceneSelect = $('#scene-filter');
                    sceneSelect.empty().append('<option value="">全部场景</option>');
                    
                    response.data.forEach(function(scene) {
                        sceneSelect.append(`<option value="${scene.id}">${scene.name}</option>`);
                    });
                }
            })
            .fail(function() {
                showToast('加载场景列表失败', 'error');
            });
    }

    // 加载交易记录
    function loadTradeLogs() {
        const params = {
            action: 'get_logs',
            page: currentPage,
            page_size: pageSize,
            trade_status: $('#trade-status-filter').val(),
            start_date: $('#date-range-start').val(),
            end_date: $('#date-range-end').val(),
            scene_id: $('#scene-filter').val(),
            player_name: $('#player-search').val()
        };

        $.get('api_trade_logs.php', params)
            .done(function(response) {
                if (response.success) {
                    displayTradeLogs(response.data.logs);
                    updatePagination(response.data.pagination);
                } else {
                    showToast(response.message || '加载交易记录失败', 'error');
                }
            })
            .fail(function() {
                showToast('加载交易记录失败', 'error');
            });
    }

    // 显示交易记录
    function displayTradeLogs(logs) {
        const tbody = $('#logs-table-body');
        tbody.empty();

        if (logs.length === 0) {
            tbody.append(`
                <tr>
                    <td colspan="9" style="text-align: center; padding: 40px; color: #666;">
                        <i class="fas fa-inbox" style="font-size: 48px; margin-bottom: 10px; display: block;"></i>
                        暂无交易记录
                    </td>
                </tr>
            `);
            return;
        }

        logs.forEach(function(log) {
            const statusClass = getStatusClass(log.status);
            const statusText = getStatusText(log.status);
            
            tbody.append(`
                <tr>
                    <td><code>${log.id}</code></td>
                    <td>${formatDateTime(log.created_at)}</td>
                    <td>${log.scene_name || '未知场景'}</td>
                    <td>${log.initiator_name}</td>
                    <td>${log.target_name}</td>
                    <td><span class="status-badge ${statusClass}">${statusText}</span></td>
                    <td>${log.total_items}</td>
                    <td>${log.total_currency}</td>
                    <td>
                        <button class="btn btn-primary btn-sm" onclick="viewTradeDetail('${log.id}')">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `);
        });
    }

    // 更新分页信息
    function updatePagination(pagination) {
        totalPages = pagination.total_pages;
        
        // 更新显示信息
        $('#showing-from').text(pagination.showing_from);
        $('#showing-to').text(pagination.showing_to);
        $('#total-logs').text(pagination.total);

        // 生成分页按钮
        generatePaginationButtons();
    }

    // 生成分页按钮
    function generatePaginationButtons() {
        const container = $('.pagination-controls');
        container.empty();

        // 上一页按钮
        const prevDisabled = currentPage <= 1 ? 'disabled' : '';
        container.append(`
            <button class="pagination-btn ${prevDisabled}" onclick="changePage(${currentPage - 1})" ${prevDisabled}>
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
        `);

        // 页码按钮
        const startPage = Math.max(1, currentPage - 2);
        const endPage = Math.min(totalPages, currentPage + 2);

        if (startPage > 1) {
            container.append(`<button class="pagination-btn" onclick="changePage(1)">1</button>`);
            if (startPage > 2) {
                container.append(`<span>...</span>`);
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            const activeClass = i === currentPage ? 'active' : '';
            container.append(`<button class="pagination-btn ${activeClass}" onclick="changePage(${i})">${i}</button>`);
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                container.append(`<span>...</span>`);
            }
            container.append(`<button class="pagination-btn" onclick="changePage(${totalPages})">${totalPages}</button>`);
        }

        // 下一页按钮
        const nextDisabled = currentPage >= totalPages ? 'disabled' : '';
        container.append(`
            <button class="pagination-btn ${nextDisabled}" onclick="changePage(${currentPage + 1})" ${nextDisabled}>
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        `);
    }

    // 切换页面
    window.changePage = function(page) {
        if (page >= 1 && page <= totalPages && page !== currentPage) {
            currentPage = page;
            loadTradeLogs();
        }
    };

    // 查看交易详情
    window.viewTradeDetail = function(tradeId) {
        $.get('api_trade_logs.php', { action: 'get_log_detail', trade_id: tradeId })
            .done(function(response) {
                if (response.success) {
                    displayTradeDetail(response.data);
                    $('#tradeDetailModal').show();
                } else {
                    showToast(response.message || '加载交易详情失败', 'error');
                }
            })
            .fail(function() {
                showToast('加载交易详情失败', 'error');
            });
    };

    // 显示交易详情
    function displayTradeDetail(trade) {
        $('#modal-trade-id').text(trade.id);
        $('#modal-scene-name').text(trade.scene_name || '未知场景');
        $('#modal-trade-status').html(`<span class="status-badge ${getStatusClass(trade.status)}">${getStatusText(trade.status)}</span>`);
        $('#modal-created-at').text(formatDateTime(trade.created_at));
        $('#modal-completed-at').text(trade.completed_at ? formatDateTime(trade.completed_at) : '未完成');
        $('#modal-initiator').text(trade.initiator_name);
        $('#modal-target').text(trade.target_name);

        // 显示交易物品
        displayTradeItems('#modal-initiator-items', trade.initiator_items);
        displayTradeItems('#modal-target-items', trade.target_items);

        // 显示交易货币
        displayTradeCurrency('#modal-initiator-currency', trade.initiator_currency);
        displayTradeCurrency('#modal-target-currency', trade.target_currency);
    }

    // 显示交易物品
    function displayTradeItems(container, items) {
        const $container = $(container);
        $container.empty();

        if (!items || items.length === 0) {
            $container.append('<div class="item-entry">无物品</div>');
            return;
        }

        items.forEach(function(item) {
            $container.append(`
                <div class="item-entry">
                    <div class="item-name">${item.item_name}</div>
                    <div class="item-quantity">数量: ${item.quantity}</div>
                    <div class="item-id">玩家物品ID: ${item.inventory_id}</div>
                    <div class="item-template-id">模板ID: ${item.item_template_id}</div>
                </div>
            `);
        });
    }

    // 显示交易货币
    function displayTradeCurrency(container, currencies) {
        const $container = $(container);
        $container.empty();

        if (!currencies || currencies.length === 0) {
            $container.append('<div class="currency-entry">无货币</div>');
            return;
        }

        currencies.forEach(function(currency) {
            if (currency.gold_amount > 0) {
                $container.append(`
                    <div class="currency-entry">
                        <div class="item-name">金币</div>
                        <div class="currency-amount">数量: ${currency.gold_amount}</div>
                    </div>
                `);
            }
            if (currency.diamond_amount > 0) {
                $container.append(`
                    <div class="currency-entry">
                        <div class="item-name">钻石</div>
                        <div class="currency-amount">数量: ${currency.diamond_amount}</div>
                    </div>
                `);
            }
        });
    }

    // 重置过滤器
    function resetFilters() {
        $('#trade-status-filter').val('');
        $('#scene-filter').val('');
        $('#player-search').val('');
        setDefaultDates();
        currentPage = 1;
        loadTradeLogs();
    }

    // 确认清理
    function confirmCleanup() {
        const startDate = $('#cleanup-date-start').val();
        const endDate = $('#cleanup-date-end').val();

        if (!startDate || !endDate) {
            showToast('请选择清理日期范围', 'warning');
            return;
        }

        if (new Date(startDate) > new Date(endDate)) {
            showToast('开始日期不能晚于结束日期', 'warning');
            return;
        }

        const message = `确定要清理 ${startDate} 到 ${endDate} 期间的所有交易记录吗？\n\n此操作不可撤销！`;
        
        if (confirm(message)) {
            cleanupLogs(startDate, endDate);
        }
    }

    // 清理日志
    function cleanupLogs(startDate, endDate) {
        $.post('api_trade_logs.php', {
            action: 'clear_logs',
            start_date: startDate,
            end_date: endDate
        })
        .done(function(response) {
            if (response.success) {
                showToast(`成功清理了 ${response.data.deleted_count} 条交易记录`, 'success');
                loadTradeLogs();
            } else {
                showToast(response.message || '清理失败', 'error');
            }
        })
        .fail(function() {
            showToast('清理操作失败', 'error');
        });
    }

    // 导出交易记录
    function exportTradeRecord(tradeId) {
        window.open(`api_trade_logs.php?action=export_log&trade_id=${tradeId}`, '_blank');
    }

    // 获取状态样式类
    function getStatusClass(status) {
        const statusMap = {
            'completed': 'status-completed',
            'cancelled': 'status-cancelled',
            'expired': 'status-expired'
        };
        return statusMap[status] || '';
    }

    // 获取状态文本
    function getStatusText(status) {
        const statusMap = {
            'completed': '已完成',
            'cancelled': '已取消',
            'expired': '已过期'
        };
        return statusMap[status] || status;
    }

    // 格式化日期时间
    function formatDateTime(dateTimeStr) {
        if (!dateTimeStr) return '';
        const date = new Date(dateTimeStr);
        return date.toLocaleString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });
    }

    // 显示Toast通知
    function showToast(message, type = 'success') {
        const toast = $('#toast');
        toast.removeClass('show success error warning').addClass(type);
        toast.text(message);
        toast.addClass('show');

        setTimeout(function() {
            toast.removeClass('show');
        }, 3000);
    }
});
