// admin/redemption_codes.js

// 显示创建兑换码模态框
function showCreateModal() {
    // 创建模态框HTML
    const modalHtml = `
        <div id="createCodeModal" class="modal-overlay">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>创建兑换码</h3>
                    <button class="modal-close" onclick="hideCreateModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="createCodeForm">
                        <div class="form-group">
                            <label for="codeName">兑换码名称 *</label>
                            <input type="text" id="codeName" name="name" class="form-control" required 
                                   placeholder="例如：新手礼包、周年庆典">
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group" style="flex: 1;">
                                <label for="totalUsageLimit">总使用次数 *</label>
                                <input type="number" id="totalUsageLimit" name="total_usage_limit" 
                                       class="form-control" min="1" value="1" required>
                            </div>
                            <div class="form-group" style="flex: 1; margin-left: 15px;">
                                <label for="playerUsageLimit">每人使用次数 *</label>
                                <input type="number" id="playerUsageLimit" name="player_usage_limit" 
                                       class="form-control" min="1" value="1" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="expiresAt">过期时间</label>
                            <input type="datetime-local" id="expiresAt" name="expires_at" class="form-control">
                            <small class="form-text">留空表示永不过期</small>
                        </div>
                        
                        <div class="form-group">
                            <label>兑换物品配置</label>
                            <div id="itemsContainer">
                                <!-- 物品配置将通过JavaScript动态添加 -->
                            </div>
                            <button type="button" class="btn btn-secondary" onclick="addItemConfig()">添加物品</button>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideCreateModal()">取消</button>
                    <button class="btn btn-primary" onclick="createRedemptionCode()">创建</button>
                </div>
            </div>
        </div>
    `;
    
    // 添加到页面
    document.body.insertAdjacentHTML('beforeend', modalHtml);
    
    // 加载物品列表
    loadItemTemplates();
    
    // 添加一个默认的物品配置
    addItemConfig();
}

// 隐藏创建模态框
function hideCreateModal() {
    const modal = document.getElementById('createCodeModal');
    if (modal) {
        modal.remove();
    }
}

// 添加物品配置行
function addItemConfig() {
    const container = document.getElementById('itemsContainer');
    const itemIndex = container.children.length;
    
    const itemHtml = `
        <div class="item-config-row" data-index="${itemIndex}">
            <div class="form-row">
                <div class="form-group" style="flex: 2;">
                    <select name="items[${itemIndex}][item_template_id]" class="form-control item-select" required>
                        <option value="">选择物品</option>
                    </select>
                </div>
                <div class="form-group" style="flex: 1; margin-left: 10px;">
                    <input type="number" name="items[${itemIndex}][quantity]" class="form-control" 
                           min="1" value="1" placeholder="数量" required>
                </div>
                <div class="form-group" style="margin-left: 10px;">
                    <label class="checkbox-label">
                        <input type="checkbox" name="items[${itemIndex}][is_bound]" value="1">
                        绑定
                    </label>
                </div>
                <div class="form-group" style="margin-left: 10px;">
                    <button type="button" class="btn btn-danger btn-sm" onclick="removeItemConfig(${itemIndex})">删除</button>
                </div>
            </div>
        </div>
    `;
    
    container.insertAdjacentHTML('beforeend', itemHtml);
    
    // 填充物品选项
    populateItemSelect(container.querySelector(`[data-index="${itemIndex}"] .item-select`));
}

// 移除物品配置行
function removeItemConfig(index) {
    const row = document.querySelector(`[data-index="${index}"]`);
    if (row) {
        row.remove();
    }
}

// 全局变量存储物品模板
let itemTemplates = [];

// 加载物品模板
async function loadItemTemplates() {
    try {
        const response = await fetch('api_redemption_codes.php?action=get_item_templates');
        const data = await response.json();
        if (data.success) {
            itemTemplates = data.items;
        }
    } catch (error) {
        console.error('加载物品模板失败:', error);
    }
}

// 填充物品选择框
function populateItemSelect(selectElement) {
    // 清空现有选项（保留第一个默认选项）
    selectElement.innerHTML = '<option value="">选择物品</option>';
    
    // 按类别分组
    const categories = {};
    itemTemplates.forEach(item => {
        if (!categories[item.category]) {
            categories[item.category] = [];
        }
        categories[item.category].push(item);
    });
    
    // 添加分组选项
    Object.keys(categories).forEach(category => {
        const optgroup = document.createElement('optgroup');
        optgroup.label = category;
        
        categories[category].forEach(item => {
            const option = document.createElement('option');
            option.value = item.id;
            option.textContent = `${item.name} [${item.category}]`;
            optgroup.appendChild(option);
        });
        
        selectElement.appendChild(optgroup);
    });
}

// 创建兑换码
async function createRedemptionCode() {
    const form = document.getElementById('createCodeForm');
    const formData = new FormData(form);
    
    // 验证至少有一个物品配置
    const itemRows = document.querySelectorAll('.item-config-row');
    if (itemRows.length === 0) {
        alert('请至少添加一个兑换物品');
        return;
    }
    
    // 验证所有物品都已选择
    let hasEmptyItem = false;
    itemRows.forEach(row => {
        const select = row.querySelector('.item-select');
        if (!select.value) {
            hasEmptyItem = true;
        }
    });
    
    if (hasEmptyItem) {
        alert('请为所有物品配置选择具体物品');
        return;
    }
    
    try {
        const response = await fetch('api_redemption_codes.php?action=create', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage('兑换码创建成功！兑换码：' + data.code, 'success');
            hideCreateModal();
            location.reload(); // 刷新页面显示新创建的兑换码
        } else {
            showMessage('创建失败：' + data.message, 'error');
        }
    } catch (error) {
        console.error('创建兑换码失败:', error);
        showMessage('创建失败：网络错误', 'error');
    }
}

// 显示批量生成模态框
function showBatchCreateModal() {
    const modalHtml = `
        <div id="batchCreateModal" class="modal-overlay">
            <div class="modal-content" style="max-width: 500px;">
                <div class="modal-header">
                    <h3>批量生成兑换码</h3>
                    <button class="modal-close" onclick="hideBatchCreateModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="batchCreateForm">
                        <div class="form-group">
                            <label for="batchCodeName">兑换码名称前缀 *</label>
                            <input type="text" id="batchCodeName" name="name_prefix" class="form-control" required 
                                   placeholder="例如：活动礼包">
                        </div>
                        
                        <div class="form-group">
                            <label for="batchCount">生成数量 *</label>
                            <input type="number" id="batchCount" name="count" class="form-control" 
                                   min="1" max="1000" value="10" required>
                            <small class="form-text">最多一次生成1000个</small>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group" style="flex: 1;">
                                <label for="batchTotalUsage">每码总使用次数 *</label>
                                <input type="number" id="batchTotalUsage" name="total_usage_limit" 
                                       class="form-control" min="1" value="1" required>
                            </div>
                            <div class="form-group" style="flex: 1; margin-left: 15px;">
                                <label for="batchPlayerUsage">每人使用次数 *</label>
                                <input type="number" id="batchPlayerUsage" name="player_usage_limit" 
                                       class="form-control" min="1" value="1" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="batchExpiresAt">过期时间</label>
                            <input type="datetime-local" id="batchExpiresAt" name="expires_at" class="form-control">
                            <small class="form-text">留空表示永不过期</small>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideBatchCreateModal()">取消</button>
                    <button class="btn btn-primary" onclick="batchCreateCodes()">生成</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// 隐藏批量生成模态框
function hideBatchCreateModal() {
    const modal = document.getElementById('batchCreateModal');
    if (modal) {
        modal.remove();
    }
}

// 批量生成兑换码
async function batchCreateCodes() {
    const form = document.getElementById('batchCreateForm');
    const formData = new FormData(form);
    
    try {
        const response = await fetch('api_redemption_codes.php?action=batch_create', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage(`成功生成 ${data.count} 个兑换码`, 'success');
            hideBatchCreateModal();
            location.reload();
        } else {
            showMessage('批量生成失败：' + data.message, 'error');
        }
    } catch (error) {
        console.error('批量生成失败:', error);
        showMessage('批量生成失败：网络错误', 'error');
    }
}

// 查看兑换码详情
async function viewCodeDetails(codeId) {
    try {
        const response = await fetch(`api_redemption_codes.php?action=get_details&id=${codeId}`);
        const data = await response.json();
        
        if (data.success) {
            showCodeDetailsModal(data.code);
        } else {
            showMessage('获取详情失败：' + data.message, 'error');
        }
    } catch (error) {
        console.error('获取详情失败:', error);
        showMessage('获取详情失败：网络错误', 'error');
    }
}

// 显示兑换码详情模态框
function showCodeDetailsModal(code) {
    const itemsHtml = code.items.map(item => `
        <tr>
            <td>${escapeHtml(item.item_name)}</td>
            <td>${item.quantity}</td>
            <td>${item.is_bound ? '是' : '否'}</td>
        </tr>
    `).join('');
    
    const modalHtml = `
        <div id="codeDetailsModal" class="modal-overlay">
            <div class="modal-content" style="max-width: 600px;">
                <div class="modal-header">
                    <h3>兑换码详情</h3>
                    <button class="modal-close" onclick="hideCodeDetailsModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="details-grid">
                        <div class="detail-item">
                            <label>兑换码：</label>
                            <span class="code-display">${escapeHtml(code.code)}</span>
                        </div>
                        <div class="detail-item">
                            <label>名称：</label>
                            <span>${escapeHtml(code.name)}</span>
                        </div>
                        <div class="detail-item">
                            <label>使用情况：</label>
                            <span>${code.current_usage_count} / ${code.total_usage_limit}</span>
                        </div>
                        <div class="detail-item">
                            <label>每人限制：</label>
                            <span>${code.player_usage_limit} 次</span>
                        </div>
                        <div class="detail-item">
                            <label>过期时间：</label>
                            <span>${code.expires_at ? code.expires_at : '永不过期'}</span>
                        </div>
                        <div class="detail-item">
                            <label>创建时间：</label>
                            <span>${code.created_at}</span>
                        </div>
                    </div>
                    
                    <h4 style="margin-top: 20px;">兑换物品</h4>
                    <table class="table">
                        <thead>
                            <tr>
                                <th>物品名称</th>
                                <th>数量</th>
                                <th>是否绑定</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${itemsHtml}
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideCodeDetailsModal()">关闭</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// 隐藏详情模态框
function hideCodeDetailsModal() {
    const modal = document.getElementById('codeDetailsModal');
    if (modal) {
        modal.remove();
    }
}

// 查看使用记录
async function viewUsageHistory(codeId) {
    try {
        const response = await fetch(`api_redemption_codes.php?action=get_usage_history&id=${codeId}`);
        const data = await response.json();
        
        if (data.success) {
            showUsageHistoryModal(data.usage_history, data.code_info);
        } else {
            showMessage('获取使用记录失败：' + data.message, 'error');
        }
    } catch (error) {
        console.error('获取使用记录失败:', error);
        showMessage('获取使用记录失败：网络错误', 'error');
    }
}

// 显示使用记录模态框
function showUsageHistoryModal(usageHistory, codeInfo) {
    const historyHtml = usageHistory.length > 0 ? usageHistory.map(record => `
        <tr>
            <td>${escapeHtml(record.username)}</td>
            <td>${record.used_at}</td>
            <td>${record.ip_address || '未记录'}</td>
        </tr>
    `).join('') : '<tr><td colspan="3" style="text-align: center; color: #6c757d;">暂无使用记录</td></tr>';
    
    const modalHtml = `
        <div id="usageHistoryModal" class="modal-overlay">
            <div class="modal-content" style="max-width: 700px;">
                <div class="modal-header">
                    <h3>使用记录 - ${escapeHtml(codeInfo.code)}</h3>
                    <button class="modal-close" onclick="hideUsageHistoryModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <div style="margin-bottom: 15px;">
                        <strong>${escapeHtml(codeInfo.name)}</strong> 
                        (已使用 ${codeInfo.current_usage_count} / ${codeInfo.total_usage_limit} 次)
                    </div>
                    
                    <table class="table">
                        <thead>
                            <tr>
                                <th>玩家</th>
                                <th>使用时间</th>
                                <th>IP地址</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${historyHtml}
                        </tbody>
                    </table>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary" onclick="hideUsageHistoryModal()">关闭</button>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', modalHtml);
}

// 隐藏使用记录模态框
function hideUsageHistoryModal() {
    const modal = document.getElementById('usageHistoryModal');
    if (modal) {
        modal.remove();
    }
}

// 切换兑换码状态
async function toggleCodeStatus(codeId, status) {
    const action = status ? '启用' : '禁用';
    
    if (!confirm(`确定要${action}这个兑换码吗？`)) {
        return;
    }
    
    try {
        const response = await fetch('api_redemption_codes.php?action=toggle_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                id: codeId,
                is_active: status
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            showMessage(`兑换码已${action}`, 'success');
            location.reload();
        } else {
            showMessage(`${action}失败：` + data.message, 'error');
        }
    } catch (error) {
        console.error(`${action}失败:`, error);
        showMessage(`${action}失败：网络错误`, 'error');
    }
}

// 显示消息
function showMessage(message, type) {
    // 创建或获取消息容器
    let container = document.getElementById('message-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'message-container';
        container.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            z-index: 1100;
        `;
        document.body.appendChild(container);
    }
    
    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `alert alert-${type}`;
    messageEl.style.cssText = `
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 4px;
        opacity: 1;
        transition: opacity 0.5s;
        max-width: 300px;
    `;
    messageEl.textContent = message;
    
    // 添加到容器
    container.appendChild(messageEl);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageEl.style.opacity = '0';
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 500);
    }, 3000);
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 加载物品模板
    loadItemTemplates();
});

// 添加CSS样式
const style = document.createElement('style');
style.textContent = `
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
    max-height: 80vh;
    overflow-y: auto;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
    border-radius: 8px 8px 0 0;
}

.modal-header h3 {
    margin: 0;
    color: #495057;
}

.modal-close {
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #6c757d;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    text-align: right;
    background-color: #f8f9fa;
    border-radius: 0 0 8px 8px;
}

.form-group {
    margin-bottom: 15px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #495057;
}

.form-control {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-row {
    display: flex;
    align-items: end;
}

.form-text {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
}

.item-config-row {
    margin-bottom: 10px;
    padding: 10px;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.checkbox-label {
    display: flex;
    align-items: center;
    font-weight: normal;
    margin-bottom: 0;
}

.checkbox-label input {
    margin-right: 5px;
}

.details-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-bottom: 20px;
}

.detail-item {
    display: flex;
    flex-direction: column;
}

.detail-item label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 5px;
}

.code-display {
    font-family: monospace;
    background: #f8f9fa;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 16px;
}

.table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
}

.table th,
.table td {
    padding: 8px 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 500;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.alert-error {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}
`;
document.head.appendChild(style);
