<?php

require_once __DIR__ . '/../config/formulas.php';
require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/RedisManager.php';

class ChatHandler {
    private $server;
    private $sensitiveWords = [];
    
    public function __construct($server) {
        $this->server = $server;
        $this->loadSensitiveWords();
    }
    
    /**
     * 加载敏感词列表
     */
    private function loadSensitiveWords() {
        // 从文件加载敏感词列表
        $filePath = __DIR__ . '/../config/sensitive_words.txt';
        if (file_exists($filePath)) {
            $words = file($filePath, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if ($words) {
                $this->sensitiveWords = array_map('trim', $words);
            }
        }
    }
    
    /**
     * 处理公聊消息
     */
    public function handlePublicChat($fd, $playerId, $payload) {
        try {
            $db = Database::getInstance();
            
            // 获取玩家信息
            $stmt = $db->query("SELECT pa.level, a.username FROM player_attributes pa JOIN accounts a ON pa.account_id = a.id WHERE pa.id = ?", [$playerId]);
            $player = $stmt->fetch();
            
            if (!$player) {
                return ['success' => false, 'message' => '找不到玩家信息'];
            }
            
            // 检查玩家等级是否满足要求
            if (!Formulas::canUsePublicChat($player['level'])) {
                return ['success' => false, 'message' => '需要达到'.Formulas::canUsePublicChatLevel().'级才能使用公聊功能'];
            }
            
            $message = trim($payload['message'] ?? '');
            if (empty($message)) {
                return ['success' => false, 'message' => '消息不能为空'];
            }
            
            // 检查消息长度
            if (mb_strlen($message) > 50) {
                return ['success' => false, 'message' => '消息长度不能超过50个字符'];
            }

            // 检查发送频率限制（服务端校验）
            $rateLimitResult = $this->checkPublicChatRateLimit($playerId);
            if (!$rateLimitResult['success']) {
                return $rateLimitResult;
            }

            // HTML转义
            $message = $this->escapeHtml($message);
            
            // 过滤敏感词
            $filtered = $this->filterMessage($message);
            $isFiltered = ($filtered !== $message);
            
            // 保存消息到数据库
            $stmt = $db->query(
                "INSERT INTO public_chat_messages (player_id, player_name, message, filtered) VALUES (?, ?, ?, ?)",
                [$playerId, $player['username'], $filtered, $isFiltered ? 1 : 0]
            );
            
            // 广播消息给所有玩家
            $this->broadcastPublicChatMessage($playerId, $player['username'], $filtered);
            
            return ['success' => true, 'message' => '消息已发送'];
        } catch (Exception $e) {
            error_log("公聊消息处理错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }
    
    /**
     * HTML转义函数
     */
    private function escapeHtml($text) {
        return htmlspecialchars($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    }
    
    /**
     * 过滤敏感词
     */
    private function filterMessage($message) {
        if (empty($this->sensitiveWords)) {
            return $message;
        }
        
        foreach ($this->sensitiveWords as $word) {
            $message = str_ireplace($word, str_repeat('*', mb_strlen($word)), $message);
        }
        
        return $message;
    }
    
    /**
     * 获取最近的公聊消息
     */
    public function getRecentPublicMessages($playerId, $limit = 50) {
        try {
            $db = Database::getInstance();
            
            // 获取玩家等级
            $stmt = $db->query("SELECT level FROM player_attributes WHERE id = ?", [$playerId]);
            $player = $stmt->fetch();
            
            if (!$player) {
                return ['success' => false, 'message' => '找不到玩家信息'];
            }
            
            // 检查玩家等级是否满足要求
            if (!Formulas::canUsePublicChat($player['level'])) {
                return ['success' => false, 'message' => '需要达到'.Formulas::canUsePublicChatLevel().'级才能使用公聊功能'];
            }
            
            // 获取最近的公聊消息
            $stmt = $db->query(
                "SELECT player_id, player_name, message, sent_at FROM public_chat_messages ORDER BY sent_at DESC LIMIT ?",
                [$limit]
            );
            
            $messages = [];
            while ($row = $stmt->fetch()) {
                // 消息已经在存储时进行了HTML转义，无需再次转义
                $messages[] = [
                    'playerId' => $row['player_id'],
                    'playerName' => $row['player_name'],
                    'message' => $row['message'],
                    'sentAt' => $row['sent_at']
                ];
            }
            
            return ['success' => true, 'messages' => array_reverse($messages)];
        } catch (Exception $e) {
            error_log("获取公聊消息错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }
    
    /**
     * 处理私聊消息
     */
    public function handlePrivateChat($fd, $senderId, $payload) {
        try {
            $db = Database::getInstance();

            // 获取发送者信息
            $stmt = $db->query("SELECT pa.level, a.username FROM player_attributes pa JOIN accounts a ON pa.account_id = a.id WHERE pa.id = ?", [$senderId]);
            $sender = $stmt->fetch();

            if (!$sender) {
                return ['success' => false, 'message' => '找不到发送者信息'];
            }

            // 检查发送者等级是否满足要求
            if (!Formulas::canUsePublicChat($sender['level'])) {
                return ['success' => false, 'message' => '需要达到'.Formulas::canUsePublicChatLevel().'级才能使用聊天功能'];
            }

            $receiverId = trim($payload['receiver_id'] ?? '');
            $message = trim($payload['message'] ?? '');

            if (empty($receiverId)) {
                return ['success' => false, 'message' => '接收者ID不能为空'];
            }

            if (empty($message)) {
                return ['success' => false, 'message' => '消息不能为空'];
            }

            // 检查消息长度
            if (mb_strlen($message) > 100) {
                return ['success' => false, 'message' => '私聊消息长度不能超过100个字符'];
            }

            // 获取接收者信息
            $stmt = $db->query("SELECT pa.level, a.username FROM player_attributes pa JOIN accounts a ON pa.account_id = a.id WHERE pa.id = ?", [$receiverId]);
            $receiver = $stmt->fetch();

            if (!$receiver) {
                return ['success' => false, 'message' => '找不到接收者信息'];
            }

            // 检查接收者等级是否满足要求
            if (!Formulas::canUsePublicChat($receiver['level'])) {
                return ['success' => false, 'message' => '接收者等级不足，无法接收私聊消息'];
            }

            // 不能给自己发私聊
            if ($senderId === $receiverId) {
                return ['success' => false, 'message' => '不能给自己发送私聊消息'];
            }

            // 检查发送频率限制（服务端校验）
            $rateLimitResult = $this->checkPrivateChatRateLimit($senderId);
            if (!$rateLimitResult['success']) {
                return $rateLimitResult;
            }

            // HTML转义
            $message = $this->escapeHtml($message);

            // 过滤敏感词
            $filtered = $this->filterMessage($message);
            $isFiltered = ($filtered !== $message);

            // 保存消息到数据库
            $stmt = $db->query(
                "INSERT INTO private_chat_messages (sender_id, sender_name, receiver_id, receiver_name, message, filtered) VALUES (?, ?, ?, ?, ?, ?)",
                [$senderId, $sender['username'], $receiverId, $receiver['username'], $filtered, $isFiltered ? 1 : 0]
            );

            // 更新或创建聊天联系人记录
            // 发送者的联系人记录（不增加未读计数，因为是自己发送的）
            $this->updateChatContact($senderId, $receiverId, $receiver['username'], false);
            // 接收者的联系人记录（增加未读计数，因为收到了新消息）
            $this->updateChatContact($receiverId, $senderId, $sender['username'], true);

            // 发送消息给接收者（如果在线）
            $this->sendPrivateChatMessage($senderId, $sender['username'], $receiverId, $filtered);

            return ['success' => true, 'message' => '私聊消息已发送'];
        } catch (Exception $e) {
            error_log("私聊消息处理错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }

    /**
     * 获取私聊历史消息
     */
    public function getPrivateChatHistory($playerId, $contactId, $limit = 50) {
        try {
            $db = Database::getInstance();

            // 获取玩家等级
            $stmt = $db->query("SELECT level FROM player_attributes WHERE id = ?", [$playerId]);
            $player = $stmt->fetch();

            if (!$player) {
                return ['success' => false, 'message' => '找不到玩家信息'];
            }

            // 检查玩家等级是否满足要求
            if (!Formulas::canUsePublicChat($player['level'])) {
                return ['success' => false, 'message' => '需要达到'.Formulas::canUsePublicChatLevel().'级才能使用聊天功能'];
            }

            // 获取私聊历史消息
            $stmt = $db->query(
                "SELECT sender_id, sender_name, receiver_id, receiver_name, message, sent_at
                 FROM private_chat_messages
                 WHERE (sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?)
                 ORDER BY sent_at DESC LIMIT ?",
                [$playerId, $contactId, $contactId, $playerId, $limit]
            );

            $messages = [];
            while ($row = $stmt->fetch()) {
                $messages[] = [
                    'senderId' => $row['sender_id'],
                    'senderName' => $row['sender_name'],
                    'receiverId' => $row['receiver_id'],
                    'receiverName' => $row['receiver_name'],
                    'message' => $row['message'],
                    'sentAt' => $row['sent_at'],
                    'isOwn' => $row['sender_id'] === $playerId
                ];
            }

            // 不在这里自动标记已读，只有用户主动选择联系人时才标记
            // $this->markMessagesAsRead($playerId, $contactId);

            return ['success' => true, 'messages' => array_reverse($messages)];
        } catch (Exception $e) {
            error_log("获取私聊历史错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }

    /**
     * 获取聊天联系人列表
     */
    public function getChatContacts($playerId) {
        try {
            $db = Database::getInstance();

            // 获取玩家等级
            $stmt = $db->query("SELECT level FROM player_attributes WHERE id = ?", [$playerId]);
            $player = $stmt->fetch();

            if (!$player) {
                return ['success' => false, 'message' => '找不到玩家信息'];
            }

            // 检查玩家等级是否满足要求
            if (!Formulas::canUsePublicChat($player['level'])) {
                return ['success' => false, 'message' => '需要达到'.Formulas::canUsePublicChatLevel().'级才能使用聊天功能'];
            }

            // 确保playerId为字符串类型
            $playerId = (string)$playerId;

            // 获取联系人列表，包含最后一条消息，最多10个
            $stmt = $db->query(
                "SELECT cc.contact_id, cc.contact_name, cc.last_message_time, cc.unread_count,
                        (SELECT message FROM private_chat_messages pcm
                         WHERE (pcm.sender_id = cc.player_id AND pcm.receiver_id = cc.contact_id)
                            OR (pcm.sender_id = cc.contact_id AND pcm.receiver_id = cc.player_id)
                         ORDER BY pcm.sent_at DESC LIMIT 1) as last_message
                 FROM chat_contacts cc
                 WHERE cc.player_id = ?
                 ORDER BY cc.last_message_time DESC LIMIT 10",
                [$playerId]
            );

            $contacts = [];
            while ($row = $stmt->fetch()) {
                $contacts[] = [
                    'contactId' => $row['contact_id'],
                    'contactName' => $row['contact_name'],
                    'lastMessage' => $row['last_message'] ?: '暂无消息',
                    'lastMessageTime' => $row['last_message_time'],
                    'unreadCount' => (int)$row['unread_count']
                ];
            }

            return ['success' => true, 'contacts' => $contacts];
        } catch (Exception $e) {
            error_log("获取聊天联系人错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }

    /**
     * 删除聊天联系人
     */
    public function deleteChatContact($playerId, $contactId) {
        try {
            $db = Database::getInstance();

            // 删除联系人记录
            $stmt = $db->query(
                "DELETE FROM chat_contacts WHERE player_id = ? AND contact_id = ?",
                [$playerId, $contactId]
            );

            return ['success' => true, 'message' => '联系人已删除'];
        } catch (Exception $e) {
            error_log("删除聊天联系人错误：" . $e->getMessage());
            return ['success' => false, 'message' => '服务器内部错误'];
        }
    }

    /**
     * 检查私聊发送频率限制
     * @param string $senderId 发送者ID
     * @return array
     */
    private function checkPrivateChatRateLimit($senderId) {
        try {
            // 使用Redis存储频率限制信息
            $redisManager = RedisManager::getInstance();

            return $redisManager->with(function($redis) use ($senderId) {
                $currentMinute = floor(time() / 60); // 当前分钟数
                $key = "private_chat_rate_limit:{$senderId}:{$currentMinute}";

                // 获取当前分钟内的发送次数
                $count = $redis->get($key);
                if ($count === false) {
                    $count = 0;
                }

                // 检查是否超过限制（每分钟9条）
                if ($count >= private_chat_rate_limit) {
                    return ['success' => false, 'message' => '发送太频繁，每分钟最多发送'.private_chat_rate_limit.'条私聊消息'];
                }

                // 增加计数并设置过期时间（60秒）
                $redis->incr($key);
                $redis->expire($key, 60);

                return ['success' => true];
            }, ['success' => false, 'message' => 'Redis连接失败']);

        } catch (Exception $e) {
            error_log("检查私聊频率限制错误：" . $e->getMessage());
            // 如果Redis出错，允许发送（避免因为Redis问题影响正常功能）
            return ['success' => true];
        }
    }

    /**
     * 检查公聊发送频率限制
     * @param string $playerId 玩家ID
     * @return array
     */
    private function checkPublicChatRateLimit($playerId) {
        try {
            // 使用Redis存储频率限制信息
            $redisManager = RedisManager::getInstance();

            return $redisManager->with(function($redis) use ($playerId) {
                $currentMinute = floor(time() / 60); // 当前分钟数
                $key = "public_chat_rate_limit:{$playerId}:{$currentMinute}";

                // 获取当前分钟内的发送次数
                $count = $redis->get($key);
                if ($count === false) {
                    $count = 0;
                }

                // 检查是否超过限制（每分钟5条）
                if ($count >= public_chat_rate_limit) {
                    return ['success' => false, 'message' => '发送太频繁，每分钟最多发送'.public_chat_rate_limit.'条公聊消息'];
                }

                // 增加计数并设置过期时间
                $redis->incr($key);
                $redis->expire($key, 60); // 60秒后过期

                return ['success' => true];
            }, ['success' => false, 'message' => 'Redis连接失败']);

        } catch (Exception $e) {
            error_log("检查公聊频率限制错误：" . $e->getMessage());
            // 如果Redis出错，允许发送（避免因为Redis问题影响正常功能）
            return ['success' => true];
        }
    }

    /**
     * 更新或创建聊天联系人记录
     * @param string $playerId 玩家ID
     * @param string $contactId 联系人ID
     * @param string $contactName 联系人名称
     * @param bool $shouldIncreaseUnread 是否应该增加未读计数（true=接收者，false=发送者）
     */
    private function updateChatContact($playerId, $contactId, $contactName, $shouldIncreaseUnread = false) {
        try {
            $db = Database::getInstance();

            // 确保ID为字符串类型（适配varchar字段）
            $playerId = (string)$playerId;
            $contactId = (string)$contactId;

            // 检查是否已存在联系人记录
            $stmt = $db->query(
                "SELECT id, unread_count FROM chat_contacts WHERE player_id = ? AND contact_id = ?",
                [$playerId, $contactId]
            );
            $existing = $stmt->fetch();

            if ($existing) {
                // 更新现有记录
                $newUnreadCount = $shouldIncreaseUnread ? $existing['unread_count'] + 1 : $existing['unread_count'];
                $stmt = $db->query(
                    "UPDATE chat_contacts SET contact_name = ?, last_message_time = NOW(), unread_count = ? WHERE id = ?",
                    [$contactName, $newUnreadCount, $existing['id']]
                );
            } else {
                // 创建新记录
                $unreadCount = $shouldIncreaseUnread ? 1 : 0;
                $stmt = $db->query(
                    "INSERT INTO chat_contacts (player_id, contact_id, contact_name, last_message_time, unread_count) VALUES (?, ?, ?, NOW(), ?)",
                    [$playerId, $contactId, $contactName, $unreadCount]
                );
            }
        } catch (Exception $e) {
            error_log("更新聊天联系人错误：" . $e->getMessage());
        }
    }

    /**
     * 发送私聊消息给接收者
     */
    private function sendPrivateChatMessage($senderId, $senderName, $receiverId, $message) {
        $connections = $this->server->getPlayerConnections();

        // 发送给接收者（如果在线）
        if (isset($connections[$receiverId])) {
            $receiverFd = $connections[$receiverId];
            try {
                $this->server->sendMessage($receiverFd, MessageProtocol::S2C_PRIVATE_CHAT_MESSAGE, [
                    'senderId' => $senderId,
                    'senderName' => $senderName,
                    'receiverId' => $receiverId,
                    'message' => $message,
                    'sentAt' => date('Y-m-d H:i:s')
                ]);
            } catch (Exception $e) {
                error_log("发送私聊消息错误：" . $e->getMessage());
            }
        }
    }


    /**
     * 广播公聊消息给所有在线玩家
     */
    private function broadcastPublicChatMessage($senderId, $senderName, $message) {
        $connections = $this->server->getPlayerConnections();
        
        foreach ($connections as $playerId => $fd) {
            try {
                // 检查玩家等级是否满足接收公聊的要求
                $db = Database::getInstance();
                $stmt = $db->query("SELECT level FROM player_attributes WHERE id = ?", [$playerId]);
                $player = $stmt->fetch();
                
                if ($player && Formulas::canUsePublicChat($player['level'])) {
                    $this->server->sendMessage($fd, MessageProtocol::S2C_PUBLIC_CHAT_MESSAGE, [
                        'playerId' => $senderId,
                        'playerName' => $senderName,
                        'message' => $message,
                        'sentAt' => date('Y-m-d H:i:s')
                    ]);
                }
            } catch (Exception $e) {
                error_log("广播公聊消息错误：" . $e->getMessage());
            }
        }
    }

    /**
     * 标记消息为已读
     */
    public function markMessagesAsRead($playerId, $contactId) {
        try {
            $db = Database::getInstance();

            // 确保ID为字符串类型（适配varchar字段）
            $playerId = (string)$playerId;
            $contactId = (string)$contactId;

            // 标记与指定联系人的消息为已读
            $stmt = $db->query(
                "UPDATE private_chat_messages SET is_read = 1
                 WHERE ((sender_id = ? AND receiver_id = ?) OR (sender_id = ? AND receiver_id = ?))
                 AND is_read = 0",
                [$contactId, $playerId, $playerId, $contactId]
            );

            // 更新联系人表的未读计数
            $stmt = $db->query(
                "UPDATE chat_contacts SET unread_count = 0
                 WHERE player_id = ? AND contact_id = ?",
                [$playerId, $contactId]
            );

            return ['success' => true, 'message' => '消息已标记为已读'];

        } catch (Exception $e) {
            error_log("标记消息已读错误：" . $e->getMessage());
            return ['success' => false, 'message' => '标记消息已读失败'];
        }
    }
}