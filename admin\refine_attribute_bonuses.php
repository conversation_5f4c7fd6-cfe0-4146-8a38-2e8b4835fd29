<?php
$pageTitle = "凝练属性加成管理";
$currentPage = "refine_attribute_bonuses";

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

// 处理表单提交（在输出任何HTML之前）
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $db->beginTransaction();
    try {
        if (isset($_POST['save_bonuses'])) {
            foreach ($_POST['bonuses'] as $id => $data) {
                if (empty($data['min_bonus']) || empty($data['max_bonus'])) {
                    continue; // 跳过空行
                }
                $update_query = "UPDATE refine_attribute_bonuses SET min_bonus = ?, max_bonus = ?, bonus_type = ? WHERE id = ?";
                $update_stmt = $db->prepare($update_query);
                $update_stmt->execute([$data['min_bonus'], $data['max_bonus'], $data['bonus_type'], $id]);
            }
        }

        if (isset($_POST['add_bonus'])) {
            $tier = $_POST['tier'];
            $attribute = $_POST['attribute'];
            $min_bonus = $_POST['min_bonus'];
            $max_bonus = $_POST['max_bonus'];
            $bonus_type = $_POST['bonus_type'];

            $check_query = "SELECT id FROM refine_attribute_bonuses WHERE tier = ? AND attribute = ?";
            $check_stmt = $db->prepare($check_query);
            $check_stmt->execute([$tier, $attribute]);

            if ($check_stmt->rowCount() > 0) {
                // 如果存在则更新
                 $update_query = "UPDATE refine_attribute_bonuses SET min_bonus = ?, max_bonus = ?, bonus_type = ? WHERE tier = ? AND attribute = ?";
                 $update_stmt = $db->prepare($update_query);
                 $update_stmt->execute([$min_bonus, $max_bonus, $bonus_type, $tier, $attribute]);
            } else {
                // 不存在则插入
                $insert_query = "INSERT INTO refine_attribute_bonuses (tier, attribute, min_bonus, max_bonus, bonus_type) VALUES (?, ?, ?, ?, ?)";
                $insert_stmt = $db->prepare($insert_query);
                $insert_stmt->execute([$tier, $attribute, $min_bonus, $max_bonus, $bonus_type]);
            }
        }

        if (isset($_POST['delete_bonus']) && isset($_POST['bonus_id'])) {
            $bonus_id = $_POST['bonus_id'];
            $delete_query = "DELETE FROM refine_attribute_bonuses WHERE id = ?";
            $delete_stmt = $db->prepare($delete_query);
            $delete_stmt->execute([$bonus_id]);
        }

        $db->commit();

        // 获取重定向的档位参数
        $redirect_tier = '';
        if (isset($_POST['current_tier'])) {
            $redirect_tier = '?tier=' . (int)$_POST['current_tier'];
        } elseif (isset($_POST['tier'])) {
            $redirect_tier = '?tier=' . (int)$_POST['tier'];
        } elseif (isset($_GET['tier'])) {
            $redirect_tier = '?tier=' . (int)$_GET['tier'];
        }

        header("Location: refine_attribute_bonuses.php" . $redirect_tier);
        exit;

    } catch (Exception $e) {
        $db->rollBack();
        $error_message = "操作失败: " . $e->getMessage();
    }
}

include 'layout_header.php';

// 获取所有档位及其名称
$tiers_query = "SELECT tier, prefix_equipment FROM refine_tiers ORDER BY tier";
$tiers_stmt = $db->prepare($tiers_query);
$tiers_stmt->execute();
$tiers_data = $tiers_stmt->fetchAll(PDO::FETCH_ASSOC);

// 构建档位数组和名称映射
$tiers = [];
$tier_names = [];
foreach ($tiers_data as $tier_info) {
    $tiers[] = $tier_info['tier'];
    $tier_names[$tier_info['tier']] = $tier_info['prefix_equipment'];
}

// 获取当前选中的档位（默认为第一个档位）
$current_tier = isset($_GET['tier']) ? (int)$_GET['tier'] : (empty($tiers) ? 1 : $tiers[0]);

// 获取当前档位的属性加成
$bonuses_query = "SELECT * FROM refine_attribute_bonuses WHERE tier = ? ORDER BY attribute";
$bonuses_stmt = $db->prepare($bonuses_query);
$bonuses_stmt->execute([$current_tier]);
$bonuses = $bonuses_stmt->fetchAll(PDO::FETCH_ASSOC);

// 获取当前档位已配置的属性列表，用于从下拉列表中排除
$configured_attributes = [];
foreach ($bonuses as $bonus) {
    $configured_attributes[] = $bonus['attribute'];
}

// 获取玩家属性表的所有字段
$columns_query = "SHOW COLUMNS FROM player_attributes";
$columns_stmt = $db->prepare($columns_query);
$columns_stmt->execute();
$table_columns_info = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);

$allowed_types = ['int', 'decimal', 'bigint', 'float', 'double'];
$excluded_columns = [
    'id', 'account_id', 'native_job_id', 'current_job_id', 'level',
    'experience', 'experience_to_next_level', 'potential_points', 
    'knowledge_points', 'gold', 'diamonds', 'combat_hp_potion_id',
    'combat_mp_potion_id', 'hp', 'mp', 'karma'
];

$player_attributes = [];
foreach ($table_columns_info as $column) {
    $is_allowed = false;
    foreach ($allowed_types as $type) {
        if (strpos(strtolower($column['Type']), $type) !== false) {
            $is_allowed = true;
            break;
        }
    }

    if ($is_allowed && !in_array($column['Field'], $excluded_columns)) {
        $player_attributes[] = $column['Field'];
    }
}

// 显示错误消息（如果有）
if (isset($error_message)) {
    echo "<div class='alert alert-danger'>{$error_message}</div>";
}

// 属性中文名对照
$attributeNames = [
    'attack' => '攻击力', 'defense' => '防御力', 'max_hp' => '最大生命值', 'max_mp' => '最大法力值',
    'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智力',
    'fire_damage' => '火焰伤害', 'fire_resistance' => '火焰抗性', 'ice_damage' => '冰冻伤害', 'ice_resistance' => '冰冻抗性',
    'wind_damage' => '风裂伤害', 'wind_resistance' => '风裂抗性', 'electric_damage' => '闪电伤害', 'electric_resistance' => '闪电抗性',
    'crit_rate' => '暴击率', 'crit_damage' => '暴击伤害', 'attack_speed' => '攻击速度', 'rage' => '怒气', 'dodge_bonus' => '闪避加成'
];
?>

<div class="container-fluid">
    <p>管理不同凝练档位可以获得的属性加成范围。</p>

    <!-- 档位切换标签 -->
    <div class="row mb-3">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-layer-group mr-1"></i>凝练档位选择
                </div>
                <div class="card-body">
                    <div class="tier-selector">
                        <?php foreach ($tiers as $tier): ?>
                        <a href="?tier=<?= $tier ?>"
                           class="btn <?= $tier == $current_tier ? 'btn-primary' : 'btn-outline-primary' ?>">
                            <i class="fas fa-gem mr-1"></i>
                            档位 <?= $tier ?> - <?= htmlspecialchars($tier_names[$tier]) ?>
                        </a>
                        <?php endforeach; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table mr-1"></i>
                    <?= htmlspecialchars($tier_names[$current_tier]) ?> (档位 <?= $current_tier ?>) - 属性加成列表
                </div>
                <div class="card-body">
                    <?php if (empty($bonuses)): ?>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle mr-1"></i>
                        当前档位暂无属性加成配置，请使用右侧表单添加。
                    </div>
                    <?php else: ?>
                    <form method="post" action="">
                        <input type="hidden" name="current_tier" value="<?= $current_tier ?>">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>属性</th>
                                        <th>最小加成</th>
                                        <th>最大加成</th>
                                        <th>加成类型</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($bonuses as $bonus): ?>
                                    <tr>
                                        <td>
                                            <?= isset($attributeNames[$bonus['attribute']]) ? $attributeNames[$bonus['attribute']] : htmlspecialchars($bonus['attribute']) ?>
                                            <small class="text-muted d-block"><?= htmlspecialchars($bonus['attribute']) ?></small>
                                        </td>
                                        <td><input type="number" step="0.01" class="form-control" name="bonuses[<?= $bonus['id'] ?>][min_bonus]" value="<?= $bonus['min_bonus'] ?>"></td>
                                        <td><input type="number" step="0.01" class="form-control" name="bonuses[<?= $bonus['id'] ?>][max_bonus]" value="<?= $bonus['max_bonus'] ?>"></td>
                                        <td>
                                            <select class="form-control" name="bonuses[<?= $bonus['id'] ?>][bonus_type]">
                                                <option value="flat" <?= $bonus['bonus_type'] == 'flat' ? 'selected' : '' ?>>固定值</option>
                                                <option value="percentage" <?= $bonus['bonus_type'] == 'percentage' ? 'selected' : '' ?>>百分比</option>
                                            </select>
                                        </td>
                                        <td>
                                            <form method="post" action="?tier=<?= $current_tier ?>" style="display:inline">
                                                <input type="hidden" name="bonus_id" value="<?= $bonus['id'] ?>">
                                                <button type="submit" name="delete_bonus" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个属性加成吗？')">删除</button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <button type="submit" name="save_bonuses" class="btn btn-primary">保存所有更改</button>
                    </form>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-plus mr-1"></i>
                    为 <?= htmlspecialchars($tier_names[$current_tier]) ?> (档位 <?= $current_tier ?>) 添加属性加成
                </div>
                <div class="card-body">
                    <form method="post" action="?tier=<?= $current_tier ?>">
                        <input type="hidden" name="tier" value="<?= $current_tier ?>">
                        <div class="form-group">
                            <label for="attribute">属性:</label>
                            <select class="form-control" id="attribute" name="attribute" required style="width: 100%;">
                                <option value="">-- 选择属性 --</option>
                                <?php
                                $available_attributes = array_diff($player_attributes, $configured_attributes);
                                if (empty($available_attributes)): ?>
                                <option value="" disabled>当前档位所有属性已配置完成</option>
                                <?php else: ?>
                                    <?php foreach ($available_attributes as $attr): ?>
                                    <option value="<?= htmlspecialchars($attr) ?>">
                                        <?= htmlspecialchars(isset($attributeNames[$attr]) ? $attributeNames[$attr] . " ($attr)" : $attr) ?>
                                    </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                            <?php if (!empty($configured_attributes)): ?>
                            <small class="form-text text-muted">
                                <i class="fas fa-info-circle mr-1"></i>
                                已配置属性:
                                <?php
                                $configured_names = [];
                                foreach ($configured_attributes as $attr) {
                                    $configured_names[] = isset($attributeNames[$attr]) ? $attributeNames[$attr] : $attr;
                                }
                                echo htmlspecialchars(implode('、', $configured_names));
                                ?>
                            </small>
                            <?php endif; ?>
                        </div>
                        <div class="form-group">
                            <label for="min_bonus">最小加成:</label>
                            <input type="number" step="0.01" class="form-control" id="min_bonus" name="min_bonus" required>
                        </div>
                        <div class="form-group">
                            <label for="max_bonus">最大加成:</label>
                            <input type="number" step="0.01" class="form-control" id="max_bonus" name="max_bonus" required>
                        </div>
                        <div class="form-group">
                            <label for="bonus_type">加成类型:</label>
                            <select class="form-control" id="bonus_type" name="bonus_type" required>
                                <option value="flat">固定值</option>
                                <option value="percentage">百分比</option>
                            </select>
                        </div>
                        <button type="submit" name="add_bonus" class="btn btn-success" <?= empty($available_attributes) ? 'disabled' : '' ?>>
                            <?= empty($available_attributes) ? '所有属性已配置' : '添加属性加成' ?>
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.tier-selector .btn {
    margin-right: 8px;
    margin-bottom: 8px;
    font-weight: 500;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.tier-selector .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tier-selector .btn-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.tier-selector .btn-outline-primary:hover {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-color: #007bff;
    transform: translateY(-1px);
}

@media (max-width: 768px) {
    .tier-selector .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
}
</style>

<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
<script>
$(document).ready(function() {
    $('#attribute').select2({
        placeholder: '-- 选择属性 --',
        allowClear: true
    });
});
</script>

<?php include 'layout_footer.php'; ?> 