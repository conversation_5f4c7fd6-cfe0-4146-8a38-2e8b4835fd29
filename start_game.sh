#!/bin/bash
# start_game.sh

echo "启动多人战斗游戏服务器..."

# 检查PHP是否安装
if ! command -v php &> /dev/null; then
    echo "错误: 未安装PHP"
    exit 1
fi

# 检查Redis是否运行
if ! pgrep -x "redis-server" > /dev/null; then
    echo "启动Redis服务器..."
    redis-server --daemonize yes
fi

# 检查MySQL是否运行
if ! pgrep -x "mysqld" > /dev/null; then
    echo "请先启动MySQL服务器"
    exit 1
fi

# 启动WebSocket服务器
echo "启动WebSocket服务器..."
cd server
php improved_websocket_server.php &
WS_PID=$!

echo "游戏服务器已启动："
echo "- WebSocket服务器: ws://localhost:8080"
echo "- 按 Ctrl+C 停止服务器"

# 等待用户中断
trap "echo '停止服务器...'; kill $WS_PID; exit" INT
wait
