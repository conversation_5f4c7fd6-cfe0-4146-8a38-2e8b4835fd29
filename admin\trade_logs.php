<?php 
$pageTitle = '玩家交易记录管理';
$currentPage = 'trade_logs';
$extra_css = '
    <link rel="stylesheet" href="trade_logs.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
';
require_once 'layout_header.php'; 
?>

<div class="page-content">
    <div class="filter-section">
        <div class="form-row">
            <div class="form-group">
                <label for="trade-status-filter">交易状态</label>
                <select id="trade-status-filter" class="form-control">
                    <option value="">全部</option>
                    <option value="completed">已完成</option>
                    <option value="cancelled">已取消</option>
                    <option value="expired">已过期</option>
                </select>
            </div>
            <div class="form-group">
                <label for="date-range-start">开始日期</label>
                <input type="date" id="date-range-start" class="form-control">
            </div>
            <div class="form-group">
                <label for="date-range-end">结束日期</label>
                <input type="date" id="date-range-end" class="form-control">
            </div>
            <div class="form-group">
                <label for="scene-filter">场景</label>
                <select id="scene-filter" class="form-control">
                    <option value="">全部场景</option>
                    <!-- 场景选项将通过JS动态加载 -->
                </select>
            </div>
            <div class="form-group">
                <label for="player-search">玩家搜索</label>
                <input type="text" id="player-search" class="form-control" placeholder="输入玩家名称...">
            </div>
        </div>
        <div class="form-row">
            <button id="search-btn" class="btn btn-primary">
                <i class="fas fa-search"></i> 搜索
            </button>
            <button id="reset-btn" class="btn btn-secondary">
                <i class="fas fa-redo"></i> 重置
            </button>
        </div>
    </div>

    <div class="cleanup-section">
        <div class="form-group">
            <label for="cleanup-date-start">清理日期范围:</label>
            <input type="date" id="cleanup-date-start" class="form-control" placeholder="开始日期">
        </div>
        <div class="form-group">
            <label for="cleanup-date-end">&nbsp;</label>
            <input type="date" id="cleanup-date-end" class="form-control" placeholder="结束日期">
        </div>
        <button id="cleanup-btn" class="btn btn-danger">
            <i class="fas fa-trash-alt"></i> 清理范围内记录
        </button>
    </div>

    <div class="trade-logs-container">
        <div class="logs-header">
            <h3>交易记录列表</h3>
            <div class="pagination-info">
                显示 <span id="showing-from">0</span> - <span id="showing-to">0</span> 条，共 <span id="total-logs">0</span> 条
            </div>
        </div>
        
        <div class="logs-table-container">
            <table class="logs-table">
                <thead>
                    <tr>
                        <th>交易ID</th>
                        <th>创建时间</th>
                        <th>场景</th>
                        <th>发起者</th>
                        <th>目标者</th>
                        <th>状态</th>
                        <th>物品数量</th>
                        <th>货币总额</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="logs-table-body">
                    <!-- 交易数据将通过JS动态加载 -->
                </tbody>
            </table>
        </div>
        
        <div class="pagination-controls">
            <!-- JS将动态生成分页按钮 -->
        </div>
    </div>
</div>

<!-- 交易详情模态框 -->
<div id="tradeDetailModal" class="modal">
    <div class="modal-content modal-large">
        <div class="modal-header">
            <h2>交易详情 #<span id="modal-trade-id"></span></h2>
            <span class="modal-close-button">&times;</span>
        </div>
        <div class="modal-body">
            <div class="trade-info">
                <div class="trade-info-section">
                    <h3>基本信息</h3>
                    <table class="info-table">
                        <tr>
                            <td><strong>场景:</strong></td>
                            <td id="modal-scene-name"></td>
                            <td><strong>交易状态:</strong></td>
                            <td id="modal-trade-status"></td>
                        </tr>
                        <tr>
                            <td><strong>创建时间:</strong></td>
                            <td id="modal-created-at"></td>
                            <td><strong>完成时间:</strong></td>
                            <td id="modal-completed-at"></td>
                        </tr>
                        <tr>
                            <td><strong>发起者:</strong></td>
                            <td id="modal-initiator"></td>
                            <td><strong>目标者:</strong></td>
                            <td id="modal-target"></td>
                        </tr>
                    </table>
                </div>
                
                <div class="trade-info-section">
                    <h3>交易物品</h3>
                    <div class="trade-items-container">
                        <div class="initiator-items">
                            <h4>发起者物品</h4>
                            <div id="modal-initiator-items"></div>
                        </div>
                        <div class="target-items">
                            <h4>目标者物品</h4>
                            <div id="modal-target-items"></div>
                        </div>
                    </div>
                </div>
                
                <div class="trade-info-section">
                    <h3>交易货币</h3>
                    <div class="trade-currency-container">
                        <div class="initiator-currency">
                            <h4>发起者货币</h4>
                            <div id="modal-initiator-currency"></div>
                        </div>
                        <div class="target-currency">
                            <h4>目标者货币</h4>
                            <div id="modal-target-currency"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal-footer">
            <button class="btn btn-secondary modal-close">关闭</button>
            <button id="export-trade-btn" class="btn btn-primary">导出记录</button>
        </div>
    </div>
</div>

<div id="toast" class="toast"></div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
<script src="trade_logs.js"></script>

<?php
require_once 'layout_footer.php';
?>
