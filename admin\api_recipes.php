<?php
header('Content-Type: application/json');
require_once '../config/Database.php';

$db = Database::getInstance()->getConnection();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_recipes':
        get_recipes($db);
        break;
    case 'get_recipe':
        get_recipe($db);
        break;
    case 'save_recipe':
        save_recipe($db);
        break;
    case 'delete_recipe':
        delete_recipe($db);
        break;
    default:
        echo json_encode(['success' => false, 'message' => '无效的操作']);
}

function get_recipes($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';

    $where_clauses = [];
    $params = [];

    if (!empty($search)) {
        $where_clauses[] = "(r.id LIKE ? OR r.name LIKE ? OR i.name LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_sql = count($where_clauses) > 0 ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    // Get total count
    $count_sql = "
        SELECT COUNT(r.id) 
        FROM recipe_templates r 
        LEFT JOIN item_templates i ON r.result_item_id = i.id
        $where_sql
    ";
    $total_stmt = $db->prepare($count_sql);
    $total_stmt->execute($params);
    $total_records = $total_stmt->fetchColumn();

    // Get records for the current page
    $sql = "
        SELECT r.*, i.name as item_name, 
               (SELECT COALESCE(SUM(quantity), 0) FROM recipe_materials WHERE recipe_id = r.id) as material_count
        FROM recipe_templates r
        LEFT JOIN item_templates i ON r.result_item_id = i.id
        $where_sql
        ORDER BY r.id DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $recipes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'recipes' => $recipes,
        'pagination' => [
            'total_records' => $total_records,
            'current_page' => $page,
            'total_pages' => ceil($total_records / $limit)
        ]
    ]);
}

function get_recipe($db) {
    $id = $_GET['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => '未提供ID']);
        return;
    }

    $stmt = $db->prepare("SELECT * FROM recipe_templates WHERE id = ?");
    $stmt->execute([$id]);
    $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($recipe) {
        $stmt = $db->prepare("SELECT material_item_id, quantity FROM recipe_materials WHERE recipe_id = ?");
        $stmt->execute([$id]);
        $recipe['materials'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['success' => true, 'recipe' => $recipe]);
    } else {
        echo json_encode(['success' => false, 'message' => '未找到配方']);
    }
}

function save_recipe($db) {
    $data = json_decode(file_get_contents('php://input'), true);

    $id = $data['id'] ?? null;
    $name = $data['name'];
    $result_item_id = $data['result_item_id'];
    $result_quantity = $data['result_quantity'];
    $craft_level = $data['craft_level'];
    $description = $data['description'] ?? '';
    $materials = $data['materials'] ?? [];

    $db->beginTransaction();
    try {
        if ($id) { // Update
            $stmt = $db->prepare("UPDATE recipe_templates SET name=?, result_item_id=?, result_quantity=?, craft_level=?, description=? WHERE id=?");
            $stmt->execute([$name, $result_item_id, $result_quantity, $craft_level, $description, $id]);
            
            // Delete old materials
            $stmt = $db->prepare("DELETE FROM recipe_materials WHERE recipe_id = ?");
            $stmt->execute([$id]);
        } else { // Insert
            $stmt = $db->prepare("INSERT INTO recipe_templates (name, result_item_id, result_quantity, craft_level, description) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([$name, $result_item_id, $result_quantity, $craft_level, $description]);
            $id = $db->lastInsertId();
        }

        // Insert new materials
        if (!empty($materials)) {
            $stmt = $db->prepare("INSERT INTO recipe_materials (recipe_id, material_item_id, quantity) VALUES (?, ?, ?)");
            foreach ($materials as $material) {
                if (!empty($material['id']) && !empty($material['quantity'])) {
                    $stmt->execute([$id, $material['id'], $material['quantity']]);
                }
            }
        }
        
        $db->commit();
        echo json_encode(['success' => true, 'message' => '配方已保存']);
    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => '保存失败: ' . $e->getMessage()]);
    }
}

function delete_recipe($db) {
    $data = json_decode(file_get_contents('php://input'), true);
    $id = $data['id'] ?? 0;

    if (!$id) {
        echo json_encode(['success' => false, 'message' => '未提供ID']);
        return;
    }

    try {
        $stmt = $db->prepare("DELETE FROM recipe_templates WHERE id = ?");
        $stmt->execute([$id]);
        // Materials are deleted automatically by FOREIGN KEY ON DELETE CASCADE
        echo json_encode(['success' => true, 'message' => '配方已删除']);
    } catch (Exception $e) {
        echo json_encode(['success' => false, 'message' => '删除失败: ' . $e->getMessage()]);
    }
}
?> 