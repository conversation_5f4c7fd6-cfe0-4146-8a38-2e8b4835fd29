           </div> <!-- .content -->
        </main>
    </div> <!-- .container -->

    <!-- 全局Toast通知容器 -->
    <div id="toast" class="toast"></div>

    <!-- Common scripts can be added here -->
    <script src="/admin/select2.min.js"></script>
    <script src="/admin/codemirror.min.js"></script>
    <script src="/admin/javascript.min.js"></script>

    <!-- 会话管理脚本 -->
    <script>
    $(document).ready(function() {
        // 会话超时设置（30分钟 = 1800秒）
        var sessionTimeout = 1800;
        var lastActivity = <?php echo isset($_SESSION['last_activity']) ? $_SESSION['last_activity'] : time(); ?>;
        var warningTime = 300; // 剩余5分钟时开始警告
        var criticalTime = 60; // 剩余1分钟时进入危险状态

        function updateSessionTimer() {
            var currentTime = Math.floor(Date.now() / 1000);
            var elapsedTime = currentTime - lastActivity;
            var remainingTime = sessionTimeout - elapsedTime;

            var timerElement = $('#session-timer');

            if (remainingTime <= 0) {
                // 会话已过期，重定向到登录页面
                alert('会话已过期，请重新登录');
                window.location.href = '/admin/index.php?message=' + encodeURIComponent('会话已过期，请重新登录');
                return;
            }

            // 格式化剩余时间
            var minutes = Math.floor(remainingTime / 60);
            var seconds = remainingTime % 60;
            var timeString = minutes + ':' + (seconds < 10 ? '0' : '') + seconds;

            // 更新显示
            timerElement.text('会话剩余: ' + timeString);

            // 根据剩余时间设置样式
            timerElement.removeClass('warning critical');
            if (remainingTime <= criticalTime) {
                timerElement.addClass('critical');
            } else if (remainingTime <= warningTime) {
                timerElement.addClass('warning');
            }

            // 在剩余5分钟时显示警告
            if (remainingTime === warningTime) {
                if (confirm('您的会话将在5分钟后过期，是否继续操作以保持登录状态？')) {
                    // 用户选择继续，发送一个请求来刷新会话
                    refreshSession();
                }
            }
        }

        function refreshSession() {
            $.ajax({
                url: '/admin/refresh_session.php',
                method: 'POST',
                success: function(response) {
                    if (response.success) {
                        lastActivity = Math.floor(Date.now() / 1000);
                    }
                },
                error: function() {
                    console.log('刷新会话失败');
                }
            });
        }

        // 监听用户活动
        var activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
        var activityTimer;

        function resetActivityTimer() {
            clearTimeout(activityTimer);
            activityTimer = setTimeout(function() {
                // 用户活动，更新最后活动时间
                lastActivity = Math.floor(Date.now() / 1000);
                refreshSession();
            }, 1000); // 1秒后发送刷新请求
        }

        // 绑定活动事件
        activityEvents.forEach(function(event) {
            document.addEventListener(event, resetActivityTimer, true);
        });

        // 每秒更新一次计时器
        setInterval(updateSessionTimer, 1000);

        // 初始化显示
        updateSessionTimer();
    });
    </script>

    <?php if (isset($extra_js)): ?>
        <?= $extra_js ?>
    <?php endif; ?>
</body>
</html>