{"_readme": ["This file locks the dependencies of your project to a known state", "Read more about it at https://getcomposer.org/doc/01-basic-usage.md#installing-dependencies", "This file is @generated automatically"], "content-hash": "f83c087b46e30125925a7337b17a4aca", "packages": [{"name": "workerman/workerman", "version": "v4.1.17", "source": {"type": "git", "url": "https://github.com/walkor/workerman.git", "reference": "bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9"}, "dist": {"type": "zip", "url": "https://api.github.com/repos/walkor/workerman/zipball/bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9", "reference": "bb9e4b0c3fc3931a2ae38a1fb7a3ac09246efae9", "shasum": ""}, "require": {"php": ">=7.0"}, "suggest": {"ext-event": "For better performance. "}, "type": "library", "autoload": {"psr-4": {"Workerman\\": "./"}}, "notification-url": "https://packagist.org/downloads/", "license": ["MIT"], "authors": [{"name": "walkor", "email": "<EMAIL>", "homepage": "http://www.workerman.net", "role": "Developer"}], "description": "An asynchronous event driven PHP framework for easily building fast, scalable network applications.", "homepage": "http://www.workerman.net", "keywords": ["asynchronous", "event-loop"], "support": {"email": "<EMAIL>", "forum": "http://wenda.workerman.net/", "issues": "https://github.com/walkor/workerman/issues", "source": "https://github.com/walkor/workerman", "wiki": "http://doc.workerman.net/"}, "funding": [{"url": "https://opencollective.com/workerman", "type": "open_collective"}, {"url": "https://www.patreon.com/walkor", "type": "patreon"}], "time": "2024-11-07T07:48:34+00:00"}], "packages-dev": [], "aliases": [], "minimum-stability": "stable", "stability-flags": [], "prefer-stable": false, "prefer-lowest": false, "platform": [], "platform-dev": [], "plugin-api-version": "2.6.0"}