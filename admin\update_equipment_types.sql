-- 为item_templates表添加equipment_type字段（如果不存在）
ALTER TABLE item_templates ADD COLUMN equipment_type VARCHAR(20) DEFAULT 'player';

-- 将现有的普级装备（quality=0）标记为怪物装备
UPDATE item_templates it
JOIN equipment_details ed ON it.id = ed.item_template_id
SET it.equipment_type = 'monster'
WHERE it.category = 'Equipment' 
  AND JSON_EXTRACT(ed.stats, '$.quality') = 0;

-- 或者如果stats字段不是JSON格式，可以通过装备名称来识别怪物装备
-- 这里列出一些明显的怪物装备名称模式
UPDATE item_templates 
SET equipment_type = 'monster'
WHERE category = 'Equipment' 
  AND (
    name LIKE '%破损%' OR
    name LIKE '%腐朽%' OR
    name LIKE '%断弦%' OR
    name LIKE '%碎裂%' OR
    name LIKE '%腐化%' OR
    name LIKE '%破烂%' OR
    name LIKE '%腐蚀%' OR
    name LIKE '%生锈%' OR
    name LIKE '%破旧%' OR
    name LIKE '%枯木%' OR
    name LIKE '%粗制%' OR
    name LIKE '%骨制%' OR
    name LIKE '%石制%' OR
    name LIKE '%木制%' OR
    name LIKE '%兽骨%' OR
    name LIKE '%皮制%' OR
    name LIKE '%骨制%' OR
    name LIKE '%布制%' OR
    name LIKE '%铁环%' OR
    name LIKE '%兽牙%' OR
    name LIKE '%粗麻%' OR
    name LIKE '%兽皮%' OR
    name LIKE '%铁制%' OR
    name LIKE '%草编%' OR
    name LIKE '%布条%' OR
    name LIKE '%虚空%' OR
    name LIKE '%熔岩%' OR
    name LIKE '%荒野%' OR
    name LIKE '%沙砾%' OR
    name LIKE '%风化%' OR
    name LIKE '%风暴%' OR
    name LIKE '%峡谷%' OR
    name LIKE '%星光%' OR
    name LIKE '%教团%' OR
    name LIKE '%遗迹%' OR
    name LIKE '%符文%' OR
    name LIKE '%冰晶%' OR
    name LIKE '%术士%' OR
    name LIKE '%烈焰%' OR
    name LIKE '%战将%' OR
    name LIKE '%王者%' OR
    name LIKE '%飓风%' OR
    name LIKE '%酋长%' OR
    name LIKE '%风王%' OR
    name LIKE '%净化%' OR
    name LIKE '%扭曲%' OR
    name LIKE '%祭坛%'
  );

-- 查看更新结果
SELECT 
  equipment_type,
  COUNT(*) as count
FROM item_templates 
WHERE category = 'Equipment'
GROUP BY equipment_type;
