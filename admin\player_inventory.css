/* player_inventory.css */

/* 沿用 monster.css 的双栏布局 */
.page-content-grid {
    display: grid;
    grid-template-columns: 280px 1fr;
    gap: 30px;
    height: calc(100vh - 100px); /* 视口高度减去顶部导航和padding */
}

.sidebar-secondary {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    height: 100%;
    display: flex;
    flex-direction: column;
}

.sidebar-secondary h3 {
    margin-top: 0;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
    font-size: 1.1rem;
    color: #333;
}

#player-search-input {
    width: 100%;
    margin-bottom: 15px;
    box-sizing: border-box;
}

#player-list {
    list-style: none;
    padding: 0;
    margin: 0;
    overflow-y: auto;
    flex-grow: 1;
}

#player-list li {
    padding: 10px 12px;
    cursor: pointer;
    border-radius: 5px;
    margin-bottom: 5px;
    border: 1px solid transparent;
    transition: background-color 0.2s, border-color 0.2s;
}

#player-list li:hover {
    background-color: #f8f9fa;
}

#player-list li.active {
    background-color: #e9ecef;
    border-color: #ced4da;
    font-weight: 600;
    color: #007bff;
}

/* 在线状态指示灯 */
.player-status {
    display: flex;
    align-items: center;
    margin-bottom: 3px;
}

.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-online {
    background-color: #28a745;
    box-shadow: 0 0 5px #28a745;
}

.status-offline {
    background-color: #6c757d;
}

.player-location {
    color: #6c757d;
    font-size: 0.85em;
    margin-top: 2px;
    display: block;
}

.main-content-area {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    overflow-y: auto;
    height: 100%;
}

.no-player-selected {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    color: #6c757d;
    font-size: 1.2rem;
}

/* 玩家详情页头部 */
.player-header {
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
    margin-bottom: 20px;
}

.player-header h2 {
    margin: 0 0 5px 0;
    font-weight: 600;
}

.player-meta {
    display: flex;
    color: #6c757d;
    font-size: 1rem;
}

.player-meta span {
    margin-right: 15px;
}

#player-id {
    color: #007bff;
    font-weight: 500;
}

/* 选项卡样式 */
.tabs {
    display: flex;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 20px;
}

.tab {
    padding: 10px 20px;
    cursor: pointer;
    border: 1px solid transparent;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-bottom: -1px;
    transition: all 0.2s ease;
}

.tab:hover {
    background-color: #f0f0f0;
}

.tab.active {
    border-color: #dee2e6 #dee2e6 #fff;
    background-color: #fff;
    color: #007bff;
    font-weight: 600;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* 属性编辑区域样式 */
.attributes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
}

.attribute-group {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    background-color: #f9f9f9;
}

.attribute-group h4 {
    margin-top: 0;
    margin-bottom: 15px;
    color: #343a40;
    font-size: 1rem;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 8px;
}

.attribute-item {
    margin-bottom: 15px;
}

.attribute-item label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    font-size: 0.9rem;
    color: #343a40;
}

.attribute-input {
    border: 1px solid #ccc;
    border-radius: 4px;
    padding: 5px 10px;
}

.form-actions {
    margin-top: 20px;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
    text-align: center;
}

/* 任务列表样式 */
.quests-list {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
    margin-top: 10px;
}

.quests-list table {
    margin-bottom: 0;
    width: 100%;
    border-collapse: collapse;
}

.quests-list table th {
    background-color: #f8f9fa;
    color: #495057;
    padding: 12px 15px;
    font-weight: 600;
    text-align: left;
    border-bottom: 2px solid #dee2e6;
}

.quests-list table td {
    padding: 12px 15px;
    border-top: 1px solid #e9ecef;
    vertical-align: middle;
}

.quests-list tbody tr:hover {
    background-color: #f8f9fa;
}

/* 任务状态标签样式 */
.quest-status {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.85rem;
    font-weight: 500;
    text-align: center;
    min-width: 80px;
}

.quest-status.active {
    background-color: #e3f2fd;
    color: #0d6efd;
    border: 1px solid #b6d4fe;
}

.quest-status.completed {
    background-color: #d1e7dd;
    color: #198754;
    border: 1px solid #badbcc;
}

.quest-status.failed {
    background-color: #f8d7da;
    color: #dc3545;
    border: 1px solid #f5c2c7;
}

.quest-status.abandoned {
    background-color: #e2e3e5;
    color: #6c757d;
    border: 1px solid #d3d4d5;
}

/* 时间格式样式 */
.quest-time {
    font-size: 0.9rem;
    color: #6c757d;
}

/* 空任务列表样式 */
.empty-quest-message {
    text-align: center;
    padding: 30px;
    color: #6c757d;
    font-style: italic;
    background-color: #f9f9f9;
    border-radius: 8px;
}

/* 技能网格样式 */
.skills-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(240px, 1fr));
    gap: 20px;
    padding: 10px;
}

.skill-card {
    border: 1px solid #e0e0e0;
    border-radius: 5px;
    padding: 15px;
    background-color: #fff;
    display: flex;
    align-items: center;
    gap: 15px;
}

.skill-icon {
    width: 40px;
    height: 40px;
    background-color: #f0f0f0;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.skill-info h5 {
    margin: 0;
    font-size: 1rem;
    color: #343a40;
}

.skill-level {
    font-size: 0.9rem;
    color: #6c757d;
}

.inventory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    border-bottom: 1px solid #e0e0e0;
    padding-bottom: 15px;
}

.inventory-header h3 {
    margin: 0;
    font-size: 1.5rem;
}

.modal-close-button {
    color: #aaa;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}
.modal-close-button:hover {
    color: #000;
}

/* 物品分类展示 */
.inventory-category-group {
    margin-bottom: 30px;
}

.inventory-category-group h4 {
    font-size: 1.2rem;
    color: #343a40;
    margin-bottom: 15px;
    border-bottom: 2px solid #007bff;
    padding-bottom: 8px;
    display: inline-block;
}

/* 复用 items.css 的卡片网格样式 */
.items-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 20px;
}

.item-card {
    border: 1px solid #e9ecef;
    border-radius: 6px;
    background-color: #fff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.item-card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
}

.item-name-wrapper {
    display: flex;
    align-items: center;
    gap: 10px;
}

.item-name-wrapper h5 {
    margin: 0;
    font-size: 1rem;
    font-weight: 600;
}

.item-card-header .item-quantity {
    font-size: 0.9rem;
    font-weight: 500;
    color: #6c757d;
}

.item-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.item-controls input {
    width: 60px;
}

/* 移除不再使用的样式 */
.item-card-body, .item-card-footer, .delete-form, .item-id-mono {
    display: none;
}

.add-item-container {
    padding: 20px;
    margin-top: 20px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.add-item-container h4 {
    margin-top: 0;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.add-item-form {
    display: flex;
    gap: 15px;
    align-items: center;
}

.add-item-form #item-template-select {
    flex-grow: 1;
}

.add-item-form #add-item-quantity {
    width: 80px;
}

/* 为 Select2 添加样式 */
.select2-container {
    width: 100% !important;
}

.no-items-message {
    padding: 40px 20px;
    text-align: center;
    color: #6c757d;
    font-size: 1.1rem;
    border: 2px dashed #e0e0e0;
    border-radius: 8px;
    margin-top: 20px;
}

/* 货币显示样式 */
#currency-display {
    display: flex;
    align-items: center;
    gap: 20px;
    padding: 0 15px;
}

.currency-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 1.1rem;
    font-weight: 500;
}

.currency-icon {
    width: 24px;
    height: 24px;
    vertical-align: middle;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: #fff;
    border-radius: 8px;
    width: 600px;
    max-width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.modal-header {
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 15px 20px;
    border-top: 1px solid #e0e0e0;
    display: flex;
    justify-content: flex-end;
}

.form-group {
    margin-bottom: 15px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.form-row .form-group {
    flex: 1;
} 