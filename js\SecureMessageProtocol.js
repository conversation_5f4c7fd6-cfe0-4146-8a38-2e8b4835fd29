class SecureMessageProtocol {
    // 将WordArray转换为Uint8Array
    wordArrayToUint8Array(wordArray) {
        const l = wordArray.sigBytes;
        const words = wordArray.words;
        const result = new Uint8Array(l);
        let i = 0, j = 0;
        while (i < l) {
            const w = words[j++];
            result[i++] = (w & 0xff000000) >>> 24;
            if(i<l) result[i++] = (w & 0x00ff0000) >>> 16;
            if(i<l) result[i++] = (w & 0x0000ff00) >>> 8;
            if(i<l) result[i++] = (w & 0x000000ff);
        }
        return result;
    }

    // 将Uint8Array转换为WordArray
    uint8ArrayToWordArray(u8Array) {
        const l = u8Array.length;
        const words = [];
        for (let i = 0; i < l; i++) {
            words[i >>> 2] |= u8Array[i] << (24 - (i % 4) * 8);
        }
        return CryptoJS.lib.WordArray.create(words, l);
    }

    // 实现与服务器端类似的编码方法
    encode(opcode, payload, key) {
        try {
            console.log('SecureMessageProtocol.encode - 参数:', { opcode, payload, keyLength: key ? key.length : 0 });
            
            const data = { opcode, payload };
            const key_wa = this.uint8ArrayToWordArray(key);
            const payloadJson = JSON.stringify(data);
            const payloadWordArray = CryptoJS.enc.Utf8.parse(payloadJson);

            // 生成IV
            const iv = CryptoJS.lib.WordArray.random(16);
            const ivBytes = this.wordArrayToUint8Array(iv);

            // 加密
            const encrypted = CryptoJS.AES.encrypt(
                payloadWordArray, 
                key_wa, 
                { 
                    iv: iv, 
                    mode: CryptoJS.mode.CBC, 
                    padding: CryptoJS.pad.Pkcs7 
                }
            );
            const encryptedBytes = this.wordArrayToUint8Array(encrypted.ciphertext);
            
            // 签名
            const hmac = CryptoJS.HmacSHA256(encrypted.ciphertext, key_wa);
            const hmacBytes = this.wordArrayToUint8Array(hmac);

            // 组装消息：IV + 签名 + 密文
            const message = new Uint8Array(ivBytes.length + hmacBytes.length + encryptedBytes.length);
            message.set(ivBytes, 0);
            message.set(hmacBytes, ivBytes.length);
            message.set(encryptedBytes, ivBytes.length + hmacBytes.length);

            console.log('SecureMessageProtocol.encode - 成功编码消息');
            return message.buffer;
        } catch (e) { 
            console.error("编码错误:", e); 
            return null; 
        }
    }

    // 实现与服务器端类似的解码方法
    decode(arrayBuffer, key) {
        try {
            const key_wa = this.uint8ArrayToWordArray(key);
            const fullMessage = new Uint8Array(arrayBuffer);

            const ivLength = 16;
            const hmacLength = 32;

            if (fullMessage.length < ivLength + hmacLength) {
                throw new Error("消息长度无效");
            }

            const iv = this.uint8ArrayToWordArray(fullMessage.slice(0, ivLength));
            const receivedHmac = fullMessage.slice(ivLength, ivLength + hmacLength);
            const ciphertext = this.uint8ArrayToWordArray(fullMessage.slice(ivLength + hmacLength));

            // 计算HMAC
            const calculatedHmac = this.wordArrayToUint8Array(
                CryptoJS.HmacSHA256(ciphertext, key_wa)
            );

            // 比较HMAC（模拟hash_equals）
            if (calculatedHmac.length !== receivedHmac.length) {
                throw new Error("HMAC验证失败：长度不匹配");
            }

            let hmacMatches = true;
            for (let i = 0; i < calculatedHmac.length; i++) {
                if (calculatedHmac[i] !== receivedHmac[i]) {
                    hmacMatches = false;
                    break;
                }
            }

            if (!hmacMatches) {
                throw new Error("HMAC验证失败");
            }

            // 解密
            const decrypted = CryptoJS.AES.decrypt(
                { ciphertext: ciphertext }, 
                key_wa, 
                { 
                    iv: iv, 
                    mode: CryptoJS.mode.CBC, 
                    padding: CryptoJS.pad.Pkcs7 
                }
            );

            const decryptedJson = decrypted.toString(CryptoJS.enc.Utf8);
            if (!decryptedJson) {
                throw new Error("解密返回空字符串");
            }

            return JSON.parse(decryptedJson);
        } catch (e) { 
            console.error("解码错误:", e); 
            return null; 
        }
    }
}