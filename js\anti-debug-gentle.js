/**
 * 温和版反调试保护系统
 * 只在确实检测到开发者工具时才触发保护
 */

(function() {
    'use strict';
    
    // 防止脚本被多次加载
    if (window.__ANTI_DEBUG_LOADED__) return;
    window.__ANTI_DEBUG_LOADED__ = true;

    // 初始化调试模式检查
    let debugMode = false;

    // 同步尝试加载debug.js
    try {
        // 给一点时间让脚本执行，然后检查调试模式并初始化反调试
        setTimeout(() => {
            debugMode = (typeof window.debugMode !== 'undefined') ? window.debugMode : false;
            // console.log('Debug mode:', debugMode);

            // 将反调试逻辑移到这里，确保在debugMode设置后执行
            if (debugMode == false) {
                initAntiDebugProtection();
            }
        }, 100);
    } catch (e) {
        // console.log('Failed to load debug.js');
        debugMode = false;
        // 如果加载失败，启动反调试保护
        setTimeout(() => {
            initAntiDebugProtection();
        }, 100);
    }
    //获取UA数据
    const userAgent = navigator.userAgent;
    // console.log(userAgent.indexOf('iPhone'));

    // 将反调试逻辑封装为函数
    function initAntiDebugProtection() {

        // 立即执行空白页面的函数
        function blankPageImmediately() {
            try {
                // 停止所有网络请求
                if (window.stop) window.stop();
                
                // 清空页面
                document.documentElement.innerHTML = '<html><head></head><body style="background:white;margin:0;padding:0;"><h1 style="text-align:center;margin-top:50px;color:#999;">非法操作，页面已被保护</h1></body></html>';
                
                // 可选：重定向到空白页
                setTimeout(() => window.location.replace('about:blank'), 2000);
            } catch (e) {
                window.location.replace('about:blank');
            }
        }
        
        // 方法1: 调试器检测 - 最可靠的方法
        setInterval(() => {
            const start = performance.now();
            debugger;
            const end = performance.now();
            // 如果debugger语句被暂停，时间差会很大
            if (end - start > 100) {
                // console.log('Debugger detected');
                setTimeout(() => reportViolation('Debugger detected'), 1000);
                blankPageImmediately();
            }
        }, 1000); // 降低检测频率
            
        // 方法2: 窗口尺寸检测 - 检测开发者工具占用空间
        let lastCheck = Date.now();
        setInterval(() => {
            const now = Date.now();
            if (now - lastCheck < 500) return; // 限制检测频率
            lastCheck = now;
            
            const heightDiff = window.outerHeight - window.innerHeight;
            const widthDiff = window.outerWidth - window.innerWidth;

            // 更宽松的阈值，只有明显的尺寸差异才触发 排除iphone
            if ((heightDiff > 300 || widthDiff > 300) && userAgent.indexOf('iPhone') === -1) {
                // console.log('Window size detected' + 'heightDiff:' + heightDiff + ',' + 'widthDiff:' + widthDiff);
                setTimeout(() => reportViolation('Window size detected' + 'heightDiff:' + heightDiff + ',' + 'widthDiff:' + widthDiff), 1000);
                blankPageImmediately();
            }
        }, 1000);
        
        // 方法3: 键盘快捷键检测 - 只检测明确的开发者工具快捷键
        document.addEventListener('keydown', function(e) {
            // F12 - 最常用的开发者工具快捷键
            if (e.code === 'F12') {
                e.preventDefault();
                // console.log('F12 key detected');
                setTimeout(() => reportViolation('F12 key detected'), 1000);
                blankPageImmediately();
                return false;
            }
            
            // Ctrl+Shift+I (Chrome DevTools)
            if (e.ctrlKey && e.shiftKey && e.code === 'KeyI') {
                e.preventDefault();
                // console.log('Chrome DevTools detected');
                setTimeout(() => reportViolation('Chrome DevTools detected'), 1000);
                blankPageImmediately();
                return false;
            }
                
            // Ctrl+Shift+J (Chrome Console)
            if (e.ctrlKey && e.shiftKey && e.code === 'KeyJ') {
                e.preventDefault();
                // console.log('Chrome Console detected');
                setTimeout(() => reportViolation('Chrome Console detected'), 1000);
                blankPageImmediately();
                return false;
            }
            
            // Ctrl+Shift+C (Element Inspector)
            if (e.ctrlKey && e.shiftKey && e.code === 'KeyC') {
                e.preventDefault();
                // console.log('Element Inspector detected');
                setTimeout(() => reportViolation('Element Inspector detected'), 1000);
                blankPageImmediately();
                return false;
            }
        }, true);
        
        // 方法4: 右键菜单检测 - 但给出警告而不是立即空白
        let rightClickCount = 0;
        document.addEventListener('contextmenu', function(e) {
            rightClickCount++;
            if (rightClickCount > 10) {
                e.preventDefault();
                // console.log('Right click detected');
                setTimeout(() => reportViolation('Right click detected'), 1000);
                blankPageImmediately();
                return false;
            } else {
                // 第一次右键给出警告
                e.preventDefault();
                // alert('检测到右键操作，请勿尝试使用开发者工具！');
                return false;
            }
        }, true);
        
        // 方法5: 开发者工具API检测
        setInterval(() => {
            // 检测Chrome DevTools
            if (window.chrome && window.chrome.devtools && window.chrome.devtools.inspectedWindow) {
                // console.log('Chrome DevTools detected');
                setTimeout(() => reportViolation('Chrome DevTools detected'), 1000);
                blankPageImmediately();
            }
            
            // 检测Firebug
            if (window.Firebug && window.Firebug.chrome && window.Firebug.chrome.isInitialized) {
                // console.log('Firebug detected');
                setTimeout(() => reportViolation('Firebug detected'), 1000);
                blankPageImmediately();
            }
        }, 2000);
        
        // 报告违规行为到服务器
        function reportViolation(reason) {
            try {
                const data = {
                    reason: reason,
                    timestamp: Date.now(),
                    userAgent: navigator.userAgent,
                    url: window.location.href
                };

                // console.log('Reporting violation:', data);

                fetch('/api/security_violation.php', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                })
                .then(response => {
                    // console.log('Violation report response status:', response.status);
                    return response.json();
                })
                .then(result => {
                    // console.log('Violation report result:', result);
                })
                .catch(error => {
                    // console.error('Violation report error:', error);
                });
            } catch (e) {
                // console.error('Exception in reportViolation:', e);
            }
        }
    } // 结束 initAntiDebugProtection 函数

// console.log('[SECURITY] Gentle anti-debug protection initialized');

    // 初始化token并生成HTML内容
    async function initializeWithToken() {
        try {
            const token = await generateTimestampSignature(secretKey);
            // console.log('Generated token:', token);

            // 设置全局变量
            window.token = token;

        } catch (error) {
            // console.error('Token generation failed:', error);
        }
    }

    // 动态加载游戏脚本
    function loadGameScripts() {
        const scripts = [
            'js/d.js',
            'js/e.js',
            'js/f.js',
            'js/i.js',
            'js/b.js',
            'js/j.js',
            'js/k.js',
            'js/l.js',
            'js/g.js',
        ];

        // const timestamp = Date.now();
        const token = String(window.token);

        // 更新加载进度到20%
        if (typeof window.updateLoadingProgress === 'function') {
            window.updateLoadingProgress(20, '正在加载游戏资源...');
        }

        // 按顺序加载脚本
        let loadedCount = 0;
        const totalScripts = scripts.length;

        function loadNextScript(index) {
            if (index >= scripts.length) {
                // 所有脚本加载完成，显示100%进度并解冻页面
                if (typeof window.updateLoadingProgress === 'function') {
                    window.updateLoadingProgress(100, '加载完成！');
                }

                setTimeout(() => {
                    // 调用全局解冻函数
                    if (typeof window.unfreezeUnifiedPage === 'function') {
                        window.unfreezeUnifiedPage();
                    }

                    // console.log('All scripts loaded, page unfrozen');
                }, 800); // 延迟800ms让用户看到100%
                return;
            }

            const script = document.createElement('script');
            script.src = `${scripts[index]}?v=${token}`;

            script.onload = () => {
                loadedCount++;

                // 计算进度：20% (初始) + 70% (脚本加载进度)
                const scriptProgress = (loadedCount / totalScripts) * 70;
                const totalProgress = 20 + scriptProgress;

                if (typeof window.updateLoadingProgress === 'function') {
                    window.updateLoadingProgress(totalProgress, `正在加载... (${loadedCount}/${totalScripts})`);
                }

                // console.log(`Script loaded: ${scripts[index]} (${loadedCount}/${totalScripts})`);

                // 短暂延迟让用户看到进度变化
                setTimeout(() => {
                    loadNextScript(index + 1);
                }, 100);
            };

            script.onerror = () => {
                // console.error(`Failed to load script: ${scripts[index]}`);
                loadedCount++;

                // 即使失败也更新进度
                const scriptProgress = (loadedCount / totalScripts) * 70;
                const totalProgress = 20 + scriptProgress;

                if (typeof window.updateLoadingProgress === 'function') {
                    window.updateLoadingProgress(totalProgress, `加载失败，继续下一个... (${loadedCount}/${totalScripts})`);
                }

                setTimeout(() => {
                    loadNextScript(index + 1);
                }, 100);
            };

            document.head.appendChild(script);
        }

        // 开始按顺序加载
        loadNextScript(0);
    }

        // 检查并加载游戏脚本
        setTimeout(() => {
            if (typeof GameClient === 'undefined') {
                loadGameScripts();
            }
        }, 1000);
})();