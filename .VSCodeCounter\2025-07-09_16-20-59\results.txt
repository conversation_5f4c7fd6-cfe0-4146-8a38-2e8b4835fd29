Date : 2025-07-09 16:20:59
Directory : c:\Users\<USER>\Desktop\game
Total : 189 files,  62218 codes, 12214 comments, 11079 blanks, all 85511 lines

Languages
+--------------+------------+------------+------------+------------+------------+
| language     | files      | code       | comment    | blank      | total      |
+--------------+------------+------------+------------+------------+------------+
| PHP          |        128 |     35,807 |      8,603 |      6,415 |     50,825 |
| JavaScript   |         24 |     14,779 |      2,759 |      2,768 |     20,306 |
| PostCSS      |         15 |      6,493 |        211 |      1,001 |      7,705 |
| Markdown     |          7 |      3,791 |          0 |        552 |      4,343 |
| MS SQL       |          4 |      1,030 |        625 |        317 |      1,972 |
| JSON         |          4 |        197 |          0 |          4 |        201 |
| HTML         |          3 |         85 |          0 |          4 |         89 |
| Shell Script |          1 |         22 |          7 |          8 |         37 |
| Ini          |          2 |         12 |          8 |          8 |         28 |
| YAML         |          1 |          2 |          1 |          2 |          5 |
+--------------+------------+------------+------------+------------+------------+

Directories
+----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| path                                                                                                           | files      | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+
| .                                                                                                              |        189 |     62,218 |     12,214 |     11,079 |     85,511 |
| . (Files)                                                                                                      |         14 |      3,742 |         15 |        486 |      4,243 |
| admin                                                                                                          |         71 |     23,265 |      1,537 |      3,513 |     28,315 |
| api                                                                                                            |          2 |        104 |         16 |         24 |        144 |
| classes                                                                                                        |         13 |      5,717 |      1,428 |      1,219 |      8,364 |
| config                                                                                                         |          5 |        693 |        300 |        190 |      1,183 |
| css                                                                                                            |          6 |      2,623 |         70 |        414 |      3,107 |
| database                                                                                                       |          4 |      1,030 |        625 |        317 |      1,972 |
| js                                                                                                             |         17 |     11,373 |      2,487 |      2,223 |     16,083 |
| server                                                                                                         |          7 |      6,733 |      1,511 |      1,520 |      9,764 |
| vendor                                                                                                         |         50 |      6,938 |      4,225 |      1,173 |     12,336 |
| vendor (Files)                                                                                                 |          1 |         20 |          1 |          5 |         26 |
| vendor\composer                                                                                                |         10 |        659 |        377 |        145 |      1,181 |
| vendor\workerman                                                                                               |         39 |      6,259 |      3,847 |      1,023 |     11,129 |
| vendor\workerman\workerman                                                                                     |         39 |      6,259 |      3,847 |      1,023 |     11,129 |
| vendor\workerman\workerman (Files)                                                                             |          5 |      2,080 |        988 |        362 |      3,430 |
| vendor\workerman\workerman\.github                                                                             |          1 |          2 |          1 |          2 |          5 |
| vendor\workerman\workerman\Connection                                                                          |          5 |        970 |        766 |        167 |      1,903 |
| vendor\workerman\workerman\Events                                                                              |         11 |      1,108 |        695 |        188 |      1,991 |
| vendor\workerman\workerman\Events (Files)                                                                      |          7 |        949 |        541 |        153 |      1,643 |
| vendor\workerman\workerman\Events\React                                                                        |          4 |        159 |        154 |         35 |        348 |
| vendor\workerman\workerman\Lib                                                                                 |          2 |         24 |         36 |          7 |         67 |
| vendor\workerman\workerman\Protocols                                                                           |         15 |      2,075 |      1,361 |        297 |      3,733 |
| vendor\workerman\workerman\Protocols (Files)                                                                   |          6 |        976 |        423 |        106 |      1,505 |
| vendor\workerman\workerman\Protocols\Http                                                                      |          9 |      1,099 |        938 |        191 |      2,228 |
| vendor\workerman\workerman\Protocols\Http (Files)                                                              |          5 |        889 |        699 |        140 |      1,728 |
| vendor\workerman\workerman\Protocols\Http\Session                                                              |          4 |        210 |        239 |         51 |        500 |
+----------------------------------------------------------------------------------------------------------------+------------+------------+------------+------------+------------+

Files
+----------------------------------------------------------------------------------------------------------------+--------------+------------+------------+------------+------------+
| filename                                                                                                       | language     | code       | comment    | blank      | total      |
+----------------------------------------------------------------------------------------------------------------+--------------+------------+------------+------------+------------+
| c:\Users\<USER>\Desktop\game\.user.ini                                                                        | Ini          |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\game\404.html                                                                         | HTML         |          7 |          0 |          0 |          7 |
| c:\Users\<USER>\Desktop\game\README.md                                                                        | Markdown     |         54 |          0 |         12 |         66 |
| c:\Users\<USER>\Desktop\game\admin\api_battle_logs.php                                                        | PHP          |        346 |         43 |         68 |        457 |
| c:\Users\<USER>\Desktop\game\admin\api_buildings.php                                                          | PHP          |        204 |         16 |         47 |        267 |
| c:\Users\<USER>\Desktop\game\admin\api_gem_recipes.php                                                        | PHP          |        180 |         10 |         36 |        226 |
| c:\Users\<USER>\Desktop\game\admin\api_item_templates.php                                                     | PHP          |         28 |          3 |          7 |         38 |
| c:\Users\<USER>\Desktop\game\admin\api_items.php                                                              | PHP          |        252 |         14 |         51 |        317 |
| c:\Users\<USER>\Desktop\game\admin\api_jobs.php                                                               | PHP          |         18 |          1 |          5 |         24 |
| c:\Users\<USER>\Desktop\game\admin\api_loot_tables.php                                                        | PHP          |        125 |         11 |         27 |        163 |
| c:\Users\<USER>\Desktop\game\admin\api_monsters.php                                                           | PHP          |        286 |         27 |         63 |        376 |
| c:\Users\<USER>\Desktop\game\admin\api_npcs.php                                                               | PHP          |        251 |         30 |         47 |        328 |
| c:\Users\<USER>\Desktop\game\admin\api_player_inventory.php                                                   | PHP          |        307 |         26 |         62 |        395 |
| c:\Users\<USER>\Desktop\game\admin\api_pvp_battle_logs.php                                                    | PHP          |        184 |         19 |         41 |        244 |
| c:\Users\<USER>\Desktop\game\admin\api_quests.php                                                             | PHP          |        269 |         30 |         47 |        346 |
| c:\Users\<USER>\Desktop\game\admin\api_recipes.php                                                            | PHP          |        137 |          5 |         22 |        164 |
| c:\Users\<USER>\Desktop\game\admin\api_scenes.php                                                             | PHP          |        560 |         42 |        126 |        728 |
| c:\Users\<USER>\Desktop\game\admin\api_shop.php                                                               | PHP          |         75 |          3 |         16 |         94 |
| c:\Users\<USER>\Desktop\game\admin\api_skills.php                                                             | PHP          |         96 |          8 |         15 |        119 |
| c:\Users\<USER>\Desktop\game\admin\api_teleporter.php                                                         | PHP          |        202 |         17 |         37 |        256 |
| c:\Users\<USER>\Desktop\game\admin\auth.php                                                                   | PHP          |         15 |          5 |          3 |         23 |
| c:\Users\<USER>\Desktop\game\admin\battle_logs.css                                                            | PostCSS      |        244 |          9 |         42 |        295 |
| c:\Users\<USER>\Desktop\game\admin\battle_logs.js                                                             | JavaScript   |        229 |         19 |         37 |        285 |
| c:\Users\<USER>\Desktop\game\admin\battle_logs.php                                                            | PHP          |        157 |          0 |         11 |        168 |
| c:\Users\<USER>\Desktop\game\admin\buildings.js                                                               | JavaScript   |        263 |         32 |         35 |        330 |
| c:\Users\<USER>\Desktop\game\admin\buildings.php                                                              | PHP          |        300 |          2 |         32 |        334 |
| c:\Users\<USER>\Desktop\game\admin\clear_cache.php                                                            | PHP          |         45 |         16 |         15 |         76 |
| c:\Users\<USER>\Desktop\game\admin\clear_server_cache.php                                                     | PHP          |         25 |          6 |          6 |         37 |
| c:\Users\<USER>\Desktop\game\admin\dashboard.php                                                              | PHP          |         84 |          9 |         11 |        104 |
| c:\Users\<USER>\Desktop\game\admin\dialogue_editor.php                                                        | PHP          |      1,011 |         73 |        131 |      1,215 |
| c:\Users\<USER>\Desktop\game\admin\dialogues.php                                                              | PHP          |        689 |         39 |         91 |        819 |
| c:\Users\<USER>\Desktop\game\admin\gem_recipes.js                                                             | JavaScript   |        342 |          9 |         12 |        363 |
| c:\Users\<USER>\Desktop\game\admin\gem_recipes.php                                                            | PHP          |        222 |         10 |         29 |        261 |
| c:\Users\<USER>\Desktop\game\admin\gem_recipes_enhanced.css                                                   | PostCSS      |        489 |         19 |         88 |        596 |
| c:\Users\<USER>\Desktop\game\admin\index.php                                                                  | PHP          |         71 |          1 |          6 |         78 |
| c:\Users\<USER>\Desktop\game\admin\install_groups.php                                                         | PHP          |        226 |         24 |         27 |        277 |
| c:\Users\<USER>\Desktop\game\admin\items.css                                                                  | PostCSS      |        383 |          8 |         60 |        451 |
| c:\Users\<USER>\Desktop\game\admin\items.js                                                                   | JavaScript   |        475 |        130 |         78 |        683 |
| c:\Users\<USER>\Desktop\game\admin\items.php                                                                  | PHP          |        178 |         15 |         24 |        217 |
| c:\Users\<USER>\Desktop\game\admin\layout_footer.php                                                          | PHP          |         12 |          0 |          0 |         12 |
| c:\Users\<USER>\Desktop\game\admin\layout_header.php                                                          | PHP          |         92 |          5 |          7 |        104 |
| c:\Users\<USER>\Desktop\game\admin\logout.php                                                                 | PHP          |         13 |          2 |          4 |         19 |
| c:\Users\<USER>\Desktop\game\admin\loot_tables.js                                                             | JavaScript   |         28 |          1 |          3 |         32 |
| c:\Users\<USER>\Desktop\game\admin\loot_tables.php                                                            | PHP          |        336 |          9 |         45 |        390 |
| c:\Users\<USER>\Desktop\game\admin\monsters.php                                                               | PHP          |      1,045 |         51 |        148 |      1,244 |
| c:\Users\<USER>\Desktop\game\admin\npc_dialogues.php                                                          | PHP          |        735 |         55 |         99 |        889 |
| c:\Users\<USER>\Desktop\game\admin\npcs.php                                                                   | PHP          |      1,376 |         90 |        190 |      1,656 |
| c:\Users\<USER>\Desktop\game\admin\pinyin.js                                                                  | JavaScript   |          2 |          1 |          1 |          4 |
| c:\Users\<USER>\Desktop\game\admin\player_inventory.css                                                       | PostCSS      |        460 |         17 |         82 |        559 |
| c:\Users\<USER>\Desktop\game\admin\player_inventory.js                                                        | JavaScript   |        556 |         60 |        125 |        741 |
| c:\Users\<USER>\Desktop\game\admin\player_inventory.php                                                       | PHP          |        379 |          4 |         24 |        407 |
| c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.css                                                        | PostCSS      |        303 |          8 |         54 |        365 |
| c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.js                                                         | JavaScript   |        236 |         20 |         36 |        292 |
| c:\Users\<USER>\Desktop\game\admin\pvp_battle_logs.php                                                        | PHP          |        158 |          0 |         11 |        169 |
| c:\Users\<USER>\Desktop\game\admin\quest_editor.php                                                           | PHP          |        682 |         42 |         94 |        818 |
| c:\Users\<USER>\Desktop\game\admin\quests.php                                                                 | PHP          |        753 |         31 |         94 |        878 |
| c:\Users\<USER>\Desktop\game\admin\recipes.css                                                                | PostCSS      |         93 |          4 |         17 |        114 |
| c:\Users\<USER>\Desktop\game\admin\recipes.js                                                                 | JavaScript   |        233 |         17 |         42 |        292 |
| c:\Users\<USER>\Desktop\game\admin\recipes.php                                                                | PHP          |        242 |         21 |         35 |        298 |
| c:\Users\<USER>\Desktop\game\admin\refine_attribute_bonuses.php                                               | PHP          |        211 |          7 |         21 |        239 |
| c:\Users\<USER>\Desktop\game\admin\refine_combos.php                                                          | PHP          |        205 |          8 |         24 |        237 |
| c:\Users\<USER>\Desktop\game\admin\refine_elements.php                                                        | PHP          |        158 |          7 |         18 |        183 |
| c:\Users\<USER>\Desktop\game\admin\refine_scores.php                                                          | PHP          |        169 |          7 |         19 |        195 |
| c:\Users\<USER>\Desktop\game\admin\refine_tiers.php                                                           | PHP          |        449 |         30 |         71 |        550 |
| c:\Users\<USER>\Desktop\game\admin\scene_npcs.php                                                             | PHP          |        256 |         23 |         37 |        316 |
| c:\Users\<USER>\Desktop\game\admin\scenes.css                                                                 | PostCSS      |        756 |         29 |         94 |        879 |
| c:\Users\<USER>\Desktop\game\admin\scenes.js                                                                  | JavaScript   |      1,120 |        110 |        220 |      1,450 |
| c:\Users\<USER>\Desktop\game\admin\scenes.php                                                                 | PHP          |        250 |          0 |         19 |        269 |
| c:\Users\<USER>\Desktop\game\admin\scenes_bak.js                                                              | JavaScript   |        740 |         56 |        148 |        944 |
| c:\Users\<USER>\Desktop\game\admin\security_violations.css                                                    | PostCSS      |        333 |         30 |         68 |        431 |
| c:\Users\<USER>\Desktop\game\admin\security_violations.php                                                    | PHP          |        290 |         14 |         27 |        331 |
| c:\Users\<USER>\Desktop\game\admin\shop_config_modal.php                                                      | PHP          |         38 |          0 |          5 |         43 |
| c:\Users\<USER>\Desktop\game\admin\skills.php                                                                 | PHP          |        779 |         30 |         94 |        903 |
| c:\Users\<USER>\Desktop\game\admin\style.css                                                                  | PostCSS      |        809 |         17 |         82 |        908 |
| c:\Users\<USER>\Desktop\game\api\get_players.php                                                              | PHP          |         14 |          1 |          4 |         19 |
| c:\Users\<USER>\Desktop\game\api\security_violation.php                                                       | PHP          |         90 |         15 |         20 |        125 |
| c:\Users\<USER>\Desktop\game\classes\BattleSystem.php                                                         | PHP          |      1,021 |        263 |        234 |      1,518 |
| c:\Users\<USER>\Desktop\game\classes\ChatHandler.php                                                          | PHP          |        344 |         98 |         87 |        529 |
| c:\Users\<USER>\Desktop\game\classes\CraftingManager.php                                                      | PHP          |        336 |        110 |         59 |        505 |
| c:\Users\<USER>\Desktop\game\classes\DialogueScriptProcessor.php                                              | PHP          |        425 |         86 |         91 |        602 |
| c:\Users\<USER>\Desktop\game\classes\GemCraftingManager.php                                                   | PHP          |        388 |        117 |         69 |        574 |
| c:\Users\<USER>\Desktop\game\classes\LootManager.php                                                          | PHP          |        233 |         78 |         59 |        370 |
| c:\Users\<USER>\Desktop\game\classes\MessageProtocol.php                                                      | PHP          |        166 |         24 |         14 |        204 |
| c:\Users\<USER>\Desktop\game\classes\NPCManager.php                                                           | PHP          |        329 |        124 |         84 |        537 |
| c:\Users\<USER>\Desktop\game\classes\PvpBattleSystem.php                                                      | PHP          |      1,093 |        243 |        223 |      1,559 |
| c:\Users\<USER>\Desktop\game\classes\QuestManager.php                                                         | PHP          |        976 |        227 |        203 |      1,406 |
| c:\Users\<USER>\Desktop\game\classes\RefineCalculator.php                                                     | PHP          |        167 |         34 |         46 |        247 |
| c:\Users\<USER>\Desktop\game\classes\SceneManager.php                                                         | PHP          |        205 |         11 |         37 |        253 |
| c:\Users\<USER>\Desktop\game\classes\SecureMessageProtocol.php                                                | PHP          |         34 |         13 |         13 |         60 |
| c:\Users\<USER>\Desktop\game\composer.json                                                                    | JSON         |          5 |          0 |          1 |          6 |
| c:\Users\<USER>\Desktop\game\composer.lock                                                                    | JSON         |         82 |          0 |          1 |         83 |
| c:\Users\<USER>\Desktop\game\config.ini                                                                       | Ini          |         11 |          8 |          8 |         27 |
| c:\Users\<USER>\Desktop\game\config\ConnectionPool.php                                                        | PHP          |         91 |          6 |         18 |        115 |
| c:\Users\<USER>\Desktop\game\config\Database.php                                                              | PHP          |         74 |         14 |         13 |        101 |
| c:\Users\<USER>\Desktop\game\config\RedisManager.php                                                          | PHP          |         56 |         19 |         10 |         85 |
| c:\Users\<USER>\Desktop\game\config\config.php                                                                | PHP          |          8 |         13 |          9 |         30 |
| c:\Users\<USER>\Desktop\game\config\formulas.php                                                              | PHP          |        464 |        248 |        140 |        852 |
| c:\Users\<USER>\Desktop\game\css\c.css                                                                        | PostCSS      |         24 |          3 |          1 |         28 |
| c:\Users\<USER>\Desktop\game\css\g.css                                                                        | PostCSS      |      1,096 |         26 |        158 |      1,280 |
| c:\Users\<USER>\Desktop\game\css\n.css                                                                        | PostCSS      |        302 |          8 |         52 |        362 |
| c:\Users\<USER>\Desktop\game\css\p.css                                                                        | PostCSS      |        802 |         20 |        132 |        954 |
| c:\Users\<USER>\Desktop\game\css\q.css                                                                        | PostCSS      |        340 |         13 |         59 |        412 |
| c:\Users\<USER>\Desktop\game\css\r.css                                                                        | PostCSS      |         59 |          0 |         12 |         71 |
| c:\Users\<USER>\Desktop\game\database\deploy_gem_crafting.sql                                                 | MS SQL       |         72 |         27 |         20 |        119 |
| c:\Users\<USER>\Desktop\game\database\game_battle.sql                                                         | MS SQL       |        839 |        580 |        279 |      1,698 |
| c:\Users\<USER>\Desktop\game\database\gem_crafting_tables.sql                                                 | MS SQL       |         64 |         13 |          9 |         86 |
| c:\Users\<USER>\Desktop\game\database\security_tables.sql                                                     | MS SQL       |         55 |          5 |          9 |         69 |
| c:\Users\<USER>\Desktop\game\game.html                                                                        | HTML         |         23 |          0 |          1 |         24 |
| c:\Users\<USER>\Desktop\game\index.html                                                                       | HTML         |         55 |          0 |          3 |         58 |
| c:\Users\<USER>\Desktop\game\js\MessageProtocol.js                                                            | JavaScript   |        130 |          9 |          8 |        147 |
| c:\Users\<USER>\Desktop\game\js\SecureMessageProtocol.js                                                      | JavaScript   |        103 |         11 |         19 |        133 |
| c:\Users\<USER>\Desktop\game\js\UnencryptedMessageProtocol.js                                                 | JavaScript   |         24 |          0 |          0 |         24 |
| c:\Users\<USER>\Desktop\game\js\anti-debug-gentle.js                                                          | JavaScript   |        231 |         76 |         57 |        364 |
| c:\Users\<USER>\Desktop\game\js\building-manager.js                                                           | JavaScript   |      2,434 |        574 |        443 |      3,451 |
| c:\Users\<USER>\Desktop\game\js\crypto-js.min.js                                                              | JavaScript   |          1 |          0 |          0 |          1 |
| c:\Users\<USER>\Desktop\game\js\debug-loader.php                                                              | PHP          |        155 |         21 |         39 |        215 |
| c:\Users\<USER>\Desktop\game\js\debug.js                                                                      | JavaScript   |          2 |          1 |          1 |          4 |
| c:\Users\<USER>\Desktop\game\js\gameclient.js                                                                 | JavaScript   |      4,515 |        651 |        834 |      6,000 |
| c:\Users\<USER>\Desktop\game\js\html.js                                                                       | JavaScript   |        469 |         29 |         25 |        523 |
| c:\Users\<USER>\Desktop\game\js\js.php                                                                        | PHP          |        169 |         35 |         37 |        241 |
| c:\Users\<USER>\Desktop\game\js\npc-manager.js                                                                | JavaScript   |        567 |        228 |        136 |        931 |
| c:\Users\<USER>\Desktop\game\js\pvp-manager.js                                                                | JavaScript   |      1,062 |        381 |        260 |      1,703 |
| c:\Users\<USER>\Desktop\game\js\quest-manager.js                                                              | JavaScript   |        716 |        242 |        165 |      1,123 |
| c:\Users\<USER>\Desktop\game\js\secure-config.php                                                             | PHP          |        140 |         73 |         55 |        268 |
| c:\Users\<USER>\Desktop\game\js\skill-manager.js                                                              | JavaScript   |        301 |        102 |         83 |        486 |
| c:\Users\<USER>\Desktop\game\js\updatejs.php                                                                  | PHP          |        354 |         54 |         61 |        469 |
| c:\Users\<USER>\Desktop\game\server\cache_control.php                                                         | PHP          |         68 |         31 |         15 |        114 |
| c:\Users\<USER>\Desktop\game\server\crafting_handlers.php                                                     | PHP          |         30 |         28 |         13 |         71 |
| c:\Users\<USER>\Desktop\game\server\gem_crafting_handlers.php                                                 | PHP          |         31 |         28 |         14 |         73 |
| c:\Users\<USER>\Desktop\game\server\improved_websocket_server.php                                             | PHP          |      4,843 |        881 |      1,018 |      6,742 |
| c:\Users\<USER>\Desktop\game\server\pvp_handler.php                                                           | PHP          |      1,170 |        423 |        312 |      1,905 |
| c:\Users\<USER>\Desktop\game\server\refine_handlers.php                                                       | PHP          |        357 |        101 |         87 |        545 |
| c:\Users\<USER>\Desktop\game\server\websocket_server.php                                                      | PHP          |        234 |         19 |         61 |        314 |
| c:\Users\<USER>\Desktop\game\start_game.sh                                                                    | Shell Script |         22 |          7 |          8 |         37 |
| c:\Users\<USER>\Desktop\game\vendor\autoload.php                                                              | PHP          |         20 |          1 |          5 |         26 |
| c:\Users\<USER>\Desktop\game\vendor\composer\ClassLoader.php                                                  | PHP          |        286 |        235 |         59 |        580 |
| c:\Users\<USER>\Desktop\game\vendor\composer\InstalledVersions.php                                            | PHP          |        178 |        133 |         49 |        360 |
| c:\Users\<USER>\Desktop\game\vendor\composer\autoload_classmap.php                                            | PHP          |          6 |          1 |          4 |         11 |
| c:\Users\<USER>\Desktop\game\vendor\composer\autoload_namespaces.php                                          | PHP          |          5 |          1 |          4 |         10 |
| c:\Users\<USER>\Desktop\game\vendor\composer\autoload_psr4.php                                                | PHP          |          6 |          1 |          4 |         11 |
| c:\Users\<USER>\Desktop\game\vendor\composer\autoload_real.php                                                | PHP          |         25 |          4 |         10 |         39 |
| c:\Users\<USER>\Desktop\game\vendor\composer\autoload_static.php                                              | PHP          |         28 |          1 |          8 |         37 |
| c:\Users\<USER>\Desktop\game\vendor\composer\installed.json                                                   | JSON         |         72 |          0 |          1 |         73 |
| c:\Users\<USER>\Desktop\game\vendor\composer\installed.php                                                    | PHP          |         32 |          0 |          1 |         33 |
| c:\Users\<USER>\Desktop\game\vendor\composer\platform_check.php                                               | PHP          |         21 |          1 |          5 |         27 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\.github\FUNDING.yml                                   | YAML         |          2 |          1 |          2 |          5 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Autoloader.php                                        | PHP          |         32 |         32 |          5 |         69 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\AsyncTcpConnection.php                     | PHP          |        222 |        127 |         30 |        379 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\AsyncUdpConnection.php                     | PHP          |        115 |         71 |         18 |        204 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\ConnectionInterface.php                    | PHP          |         25 |         87 |         15 |        127 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\TcpConnection.php                          | PHP          |        517 |        380 |         87 |        984 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Connection\UdpConnection.php                          | PHP          |         91 |        101 |         17 |        209 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Ev.php                                         | PHP          |        108 |         68 |         14 |        190 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Event.php                                      | PHP          |        121 |         69 |         26 |        216 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\EventInterface.php                             | PHP          |         17 |         78 |         13 |        108 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Libevent.php                                   | PHP          |        132 |         69 |         25 |        226 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\Base.php                                 | PHP          |        131 |        106 |         28 |        265 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\ExtEventLoop.php                         | PHP          |          9 |         16 |          3 |         28 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\ExtLibEventLoop.php                      | PHP          |         10 |         16 |          2 |         28 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\React\StreamSelectLoop.php                     | PHP          |          9 |         16 |          2 |         27 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Select.php                                     | PHP          |        218 |        107 |         33 |        358 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Swoole.php                                     | PHP          |        205 |         60 |         19 |        284 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Events\Uv.php                                         | PHP          |        148 |         90 |         23 |        261 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Lib\Constants.php                                     | PHP          |         21 |         18 |          6 |         45 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Lib\Timer.php                                         | PHP          |          3 |         18 |          1 |         22 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Frame.php                                   | PHP          |         23 |         34 |          5 |         62 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http.php                                    | PHP          |        212 |         89 |         23 |        324 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Chunk.php                              | PHP          |         14 |         30 |          4 |         48 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Request.php                            | PHP          |        412 |        237 |         46 |        695 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Response.php                           | PHP          |        244 |        176 |         39 |        459 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\ServerSentEvents.php                   | PHP          |         31 |         30 |          3 |         64 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session.php                            | PHP          |        188 |        226 |         48 |        462 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\FileSessionHandler.php         | PHP          |         92 |         76 |         15 |        183 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\RedisClusterSessionHandler.php | PHP          |         26 |         15 |          6 |         47 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\RedisSessionHandler.php        | PHP          |         80 |         55 |         20 |        155 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Http\Session\SessionHandlerInterface.php    | PHP          |         12 |         93 |         10 |        115 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\ProtocolInterface.php                       | PHP          |          9 |         39 |          5 |         53 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Text.php                                    | PHP          |         26 |         40 |          4 |         70 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Websocket.php                               | PHP          |        395 |        127 |         41 |        563 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Protocols\Ws.php                                      | PHP          |        311 |         94 |         28 |        433 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\README.md                                             | Markdown     |        255 |          0 |         88 |        343 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Timer.php                                             | PHP          |        112 |         85 |         24 |        221 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\Worker.php                                            | PHP          |      1,643 |        871 |        244 |      2,758 |
| c:\Users\<USER>\Desktop\game\vendor\workerman\workerman\composer.json                                         | JSON         |         38 |          0 |          1 |         39 |
| c:\Users\<USER>\Desktop\game\游戏世界NPC设计.md                                                                     | Markdown     |        434 |          0 |         48 |        482 |
| c:\Users\<USER>\Desktop\game\游戏世界任务设计 - 副本.md                                                                 | Markdown     |      1,815 |          0 |        186 |      2,001 |
| c:\Users\<USER>\Desktop\game\游戏世界任务设计.md                                                                      | Markdown     |        424 |          0 |         58 |        482 |
| c:\Users\<USER>\Desktop\game\游戏世界地图设计.md                                                                      | Markdown     |        438 |          0 |         97 |        535 |
| c:\Users\<USER>\Desktop\game\游戏任务系统实现设计.md                                                                    | Markdown     |        371 |          0 |         63 |        434 |
| Total                                                                                                          |              |     62,218 |     12,214 |     11,079 |     85,511 |
+----------------------------------------------------------------------------------------------------------------+--------------+------------+------------+------------+------------+