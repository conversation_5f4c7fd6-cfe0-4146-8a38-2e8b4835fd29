<?php
// classes/SecureMessageProtocol.php

class SecureMessageProtocol {
    /**
     * 加密并签名消息
     * @param array $payload 原始数据
     * @param string $key 用于加密和签名的32字节原始密钥
     * @return string|false 加密并签名后的二进制数据或失败时返回false
     */
    public static function encode($payload, $key) {
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');
        $jsonPayload = json_encode($payload);
        
        $iv = openssl_random_pseudo_bytes($ivLength);
        $encrypted = openssl_encrypt($jsonPayload, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);
        if ($encrypted === false) return false;
        
        $signature = hash_hmac('sha256', $encrypted, $key, true);

        return $iv . $signature . $encrypted;
    }

    /**
     * 验证签名并解密消息
     * @param string $binaryMsg 收到的二进制消息
     * @param string $key 用于验证和解密的32字节原始密钥
     * @return array|null 解密后的原始数据或在验证/解密失败时返回null
     */
    public static function decode($binaryMsg, $key) {
        $signatureLength = 32; // SHA-256
        $ivLength = openssl_cipher_iv_length('aes-256-cbc');

        if (strlen($binaryMsg) < $ivLength + $signatureLength) {
            return null;
        }

        $iv = substr($binaryMsg, 0, $ivLength);
        $receivedSignature = substr($binaryMsg, $ivLength, $signatureLength);
        $encrypted = substr($binaryMsg, $ivLength + $signatureLength);
        
        if (empty($encrypted)) {
            return null;
        }
        
        $calculatedSignature = hash_hmac('sha256', $encrypted, $key, true);
        
        if (!hash_equals($receivedSignature, $calculatedSignature)) {
            return null;
        }
        
        $decrypted = openssl_decrypt($encrypted, 'aes-256-cbc', $key, OPENSSL_RAW_DATA, $iv);

        if ($decrypted === false) {
            return null;
        }

        return json_decode($decrypted, true);
    }
} 