<?php
// classes/PvpBattleSystem.php
require_once __DIR__ . '/../config/formulas.php';

//不再继承BattleSystem，因为它依赖于不同的战斗状态结构（players/monster vs challenger/defender）
class PvpBattleSystem {
    // 属性名称映射
    public static $attributeNames = [
        'hp' => '生命', 'max_hp' => '最大生命', 'mp' => '法力', 'max_mp' => '最大法力',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智慧',
        'attack' => '攻击', 'defense' => '防御', 'attack_speed' => '攻速',
        'fire_resistance' => '火焰抗性', 'ice_resistance' => '冰冻抗性', 'wind_resistance' => '风裂抗性', 'electric_resistance' => '闪电抗性',
        'fire_damage' => '火焰伤害', 'ice_damage' => '冰冻伤害', 'wind_damage' => '风裂伤害', 'electric_damage' => '闪电伤害',
        'dodge_bonus' => '闪避加成'
    ];
    
    // ATB系统常量
    const ATB_BASE_MAX = 500; // 基础ATB最大值，会根据参战者的攻速动态调整
    const ROUND_TIME_LIMIT = 3000; // 3秒回合时间限制（毫秒）
    const ATB_MULTIPLIER = 2.0; // ATB增长速度的基础倍率
    const PVP_MAX_ROUNDS = 30; // PVP战斗最大回合数，防止无限战斗
    
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 初始化PVP战斗状态
     * 
     * @param string $challenger_id 挑战者ID
     * @param string $defender_id 被挑战者ID
     * @return array 初始化的战斗状态
     */
    public function initializePvpBattle($challenger_id, $defender_id) {
        // 获取玩家数据
        $challenger = $this->getPlayerWithAttributes($challenger_id);
        $defender = $this->getPlayerWithAttributes($defender_id);
        
        if (!$challenger || !$defender) {
            return ['error' => '无法获取玩家信息'];
        }
        
        // 初始化战斗状态
        $battleState = [
            'battle_id' => uniqid('pvp_'),
            'battle_type' => 'pvp',
            'challenger' => $challenger,
            'defender' => $defender,
            'is_over' => false,
            'winner' => null,
            'loser' => null,
            'round_count' => 0,
            'start_time' => microtime(true),
            'active_effects' => [],
            'skill_cooldowns' => [
                $challenger_id => [],
                $defender_id => []
            ],
            'damage_contribution' => [
                $challenger_id => 0,
                $defender_id => 0
            ]
        ];
        
        // 初始化ATB系统
        $this->initializeAtbState($battleState);
        
        return $battleState;
    }
    
    /**
     * 获取带有属性的玩家数据
     * 
     * @param string $player_id 玩家ID
     * @return array|null 玩家数据或null
     */
    private function getPlayerWithAttributes($player_id) {
        if (!$this->db) {
            return null;
        }
        
        try {
            $conn = $this->db->getConnection();
            
            // 获取玩家基本信息
            $stmt = $conn->prepare("
                SELECT a.id, a.username,
                       pa.level, pa.experience, pa.hp, pa.mp, pa.strength, 
                       pa.agility, pa.constitution, pa.intelligence, 
                       pa.attack, pa.defense, pa.attack_speed,
                       pa.max_hp, pa.max_mp, pa.dodge_bonus,
                       pa.fire_resistance, pa.ice_resistance, 
                       pa.wind_resistance, pa.electric_resistance,
                       pa.fire_damage, pa.ice_damage, 
                       pa.wind_damage, pa.electric_damage,
                       pa.current_job_id, pa.karma
                FROM accounts a
                JOIN player_attributes pa ON a.id = pa.account_id
                WHERE a.id = ?
            ");
            $stmt->execute([$player_id]);
            $player = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$player) {
                return null;
            }
            
            // 将属性组织为子数组
            $attributes = [
                'hp' => (int)$player['hp'],
                'mp' => (int)$player['mp'],
                'max_hp' => (int)$player['max_hp'],
                'max_mp' => (int)$player['max_mp'],
                'strength' => (int)$player['strength'],
                'agility' => (int)$player['agility'],
                'constitution' => (int)$player['constitution'],
                'intelligence' => (int)$player['intelligence'],
                'attack' => (int)$player['attack'],
                'defense' => (int)$player['defense'],
                'attack_speed' => (int)$player['attack_speed'],
                'dodge_bonus' => (int)$player['dodge_bonus'],
                'fire_resistance' => (int)$player['fire_resistance'],
                'ice_resistance' => (int)$player['ice_resistance'],
                'wind_resistance' => (int)$player['wind_resistance'],
                'electric_resistance' => (int)$player['electric_resistance'],
                'fire_damage' => (int)$player['fire_damage'],
                'ice_damage' => (int)$player['ice_damage'],
                'wind_damage' => (int)$player['wind_damage'],
                'electric_damage' => (int)$player['electric_damage'],
                'current_job_id' => (int)$player['current_job_id'],
                'karma' => (int)$player['karma']
            ];
            
            // 整理技能数据
            $stmt = $conn->prepare("
                SELECT ps.skill_template_id, ps.skill_level as level, st.name, st.skill_type, st.mp_cost
                FROM player_skills ps
                JOIN skill_templates st ON ps.skill_template_id = st.id
                WHERE ps.player_id = ?
            ");
            $stmt->execute([$player_id]);
            $skills = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 获取玩家武器名称
            $weaponName = null;
            $stmt = $conn->prepare("
                SELECT it.name, inv.instance_data
                FROM player_inventory inv
                JOIN item_templates it ON inv.item_template_id = it.id
                JOIN equipment_details ed ON it.id = ed.item_template_id
                WHERE inv.player_id = ? AND inv.is_equipped = 1 AND (ed.slot = 'RightHand' OR ed.slot = 'TwoHanded')
            ");
            $stmt->execute([$player_id]);
            $weapon = $stmt->fetch(PDO::FETCH_ASSOC);
            if ($weapon) {
                // 检查武器是否有自定义的显示名称（来自镶嵌宝石等）
                $instanceData = json_decode($weapon['instance_data'], true);
                if ($instanceData && !empty($instanceData['display_name'])) {
                    $weaponName = $instanceData['display_name'];
                } else {
                    $weaponName = $weapon['name'];
                }
            }
            
            return [
                'id' => $player_id,
                'username' => $player['username'],
                'level' => (int)$player['level'],
                'attributes' => $attributes,
                'skills' => $skills,
                'weapon_name' => $weaponName
            ];
            
        } catch (Exception $e) {
            error_log("获取玩家数据错误: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 根据ID在战斗状态中查找玩家。
     * @param array &$battleState 战斗状态的引用。
     * @param string $playerId 玩家ID。
     * @return array|null 玩家数据的引用或null。
     */
    private function &getPlayerInBattle(array &$battleState, string $playerId): ?array
    {
        if (isset($battleState['challenger']) && $battleState['challenger']['id'] == $playerId) {
            return $battleState['challenger'];
        }
        if (isset($battleState['defender']) && $battleState['defender']['id'] == $playerId) {
            return $battleState['defender'];
        }
        $null = null;
        return $null;
    }
    
    /**
     * 初始化战斗状态的ATB数据
     * @param array &$battleState 战斗状态引用
     */
    public function initializeAtbState(array &$battleState): void {
        // 计算ATB系统参数
        $atbParams = $this->calculateAtbParameters($battleState);
        
        $battleState['atb_state'] = [
            'challengerATB' => 0,
            'defenderATB' => 0,
            'roundStartTime' => microtime(true) * 1000, // 毫秒时间戳
            'lastUpdateTime' => microtime(true) * 1000,
            'lastActorId' => null, // 上一个行动者的ID
            'atbMax' => $atbParams['atbMax'], // 动态计算的ATB最大值
            'speedMultipliers' => $atbParams['speedMultipliers'] // 每个参与者的速度倍率
        ];
    }
    
    /**
     * 计算ATB系统参数，基于参战者的属性
     * @param array $battleState 战斗状态
     * @return array ATB系统参数
     */
    private function calculateAtbParameters(array $battleState): array {
        $challenger = $battleState['challenger'];
        $defender = $battleState['defender'];
        
        // 获取两位玩家的攻速
        $challengerSpeed = $challenger['attributes']['attack_speed'] ?? 10;
        $defenderSpeed = $defender['attributes']['attack_speed'] ?? 10;
        
        // 计算平均攻速
        $avgSpeed = ($challengerSpeed + $defenderSpeed) / 2;
        
        // 计算ATB最大值，基于平均攻速
        $atbMax = self::ATB_BASE_MAX * (1 + (10 / max(1, $avgSpeed)));
        
        // 调整每个参与者的速度倍率
        $speedMultipliers = [
            $challenger['id'] => ($challengerSpeed / max(1, $avgSpeed)) * self::ATB_MULTIPLIER,
            $defender['id'] => ($defenderSpeed / max(1, $avgSpeed)) * self::ATB_MULTIPLIER
        ];
        
        return [
            'atbMax' => round($atbMax),
            'speedMultipliers' => $speedMultipliers
        ];
    }
    
    /**
     * 更新ATB状态并检查是否有角色可以行动
     * @param array &$battleState 战斗状态引用
     * @return array|null 如果有角色可以行动，返回行动信息；否则返回null
     */
    public function updateAtbState(array &$battleState): ?array {
        if ($battleState['is_over']) {
            return null;
        }
        
        // 检查是否超过最大回合数
        if ($battleState['round_count'] >= self::PVP_MAX_ROUNDS) {
            // 平局处理
            $battleState['winner'] = 'draw';
            
            $battleState['is_over'] = true;
            return ['action' => 'battle_timeout', 'log' => ["战斗超过最大回合数，比赛结束！"]];
        }
        
        if (!isset($battleState['atb_state'])) {
            $this->initializeAtbState($battleState);
            return null;
        }
        
        $currentTime = microtime(true) * 1000;
        $deltaTime = $currentTime - $battleState['atb_state']['lastUpdateTime'];
        $battleState['atb_state']['lastUpdateTime'] = $currentTime;
        
        // 计算回合已经过去的时间
        $elapsedTime = $currentTime - $battleState['atb_state']['roundStartTime'];
        $isTimeUp = $elapsedTime >= self::ROUND_TIME_LIMIT;
        
        // 获取ATB最大值和速度倍率
        $atbMax = $battleState['atb_state']['atbMax'] ?? self::ATB_BASE_MAX;
        $speedMultipliers = $battleState['atb_state']['speedMultipliers'] ?? [];
        
        $challenger = $battleState['challenger'];
        $defender = $battleState['defender'];
        
        // 更新挑战者的ATB值
        if ($challenger['attributes']['hp'] > 0) {
            $challengerSpeedMultiplier = $speedMultipliers[$challenger['id']] ?? 1.0;
            $battleState['atb_state']['challengerATB'] += $challengerSpeedMultiplier * ($deltaTime / 1000) * 100;
            // 确保不超过最大值
            $battleState['atb_state']['challengerATB'] = min($battleState['atb_state']['challengerATB'], $atbMax);
        }
        
        // 更新被挑战者的ATB值
        if ($defender['attributes']['hp'] > 0) {
            $defenderSpeedMultiplier = $speedMultipliers[$defender['id']] ?? 1.0;
            $battleState['atb_state']['defenderATB'] += $defenderSpeedMultiplier * ($deltaTime / 1000) * 100;
            // 确保不超过最大值
            $battleState['atb_state']['defenderATB'] = min($battleState['atb_state']['defenderATB'], $atbMax);
        }
        
        // 检查是否有角色可以行动
        $challengerReady = $battleState['atb_state']['challengerATB'] >= $atbMax && $challenger['attributes']['hp'] > 0;
        $defenderReady = $battleState['atb_state']['defenderATB'] >= $atbMax && $defender['attributes']['hp'] > 0;
        
        if ($challengerReady || $defenderReady || $isTimeUp) {
            // 如果时间到了，但没有角色准备好行动
            if ($isTimeUp && !$challengerReady && !$defenderReady) {
                // 比较两者的ATB值，决定谁来行动
                if ($battleState['atb_state']['challengerATB'] >= $battleState['atb_state']['defenderATB']) {
                    return $this->prepareAction($battleState, 'challenger');
                } else {
                    return $this->prepareAction($battleState, 'defender');
                }
            }
            
            // 如果两者都准备好行动
            if ($challengerReady && $defenderReady) {
                // 优先让上次没行动的一方行动
                $lastActorId = $battleState['atb_state']['lastActorId'] ?? null;
                
                if ($lastActorId === $challenger['id']) {
                    return $this->prepareAction($battleState, 'defender');
                } else {
                    return $this->prepareAction($battleState, 'challenger');
                }
            }
            
            // 如果只有挑战者准备好行动
            if ($challengerReady) {
                return $this->prepareAction($battleState, 'challenger');
            }
            
            // 如果只有被挑战者准备好行动
            if ($defenderReady) {
                return $this->prepareAction($battleState, 'defender');
            }
        }
        
        return null;
    }
    
    /**
     * 准备角色行动
     * @param array &$battleState 战斗状态引用
     * @param string $actorType 行动者类型（'challenger'或'defender'）
     * @return array 行动信息
     */
    private function prepareAction(array &$battleState, string $actorType): array {
        // 确保actorType是有效的
        if ($actorType !== 'challenger' && $actorType !== 'defender') {
            error_log("错误: prepareAction收到无效的actorType: {$actorType}");
            // 默认使用challenger
            $actorType = 'challenger';
        }
        
        $actor = $battleState[$actorType];
        
        // 确保actor有效且包含id字段
        if (!isset($actor['id'])) {
            error_log("错误: prepareAction中的actor没有id字段，actorType={$actorType}");
            // 尝试从battleState中获取一个有效ID
            $playerId = $battleState['challenger']['id'] ?? $battleState['defender']['id'] ?? null;
            if (!$playerId) {
                error_log("错误: prepareAction无法获取有效的玩家ID");
            }
        } else {
            $playerId = $actor['id'];
        }
        
        // 重置行动者的ATB值
        if ($actorType === 'challenger') {
            $battleState['atb_state']['challengerATB'] = 0;
        } else {
            $battleState['atb_state']['defenderATB'] = 0;
        }
        
        $battleState['atb_state']['lastActorId'] = $playerId;
        
        // 重置回合开始时间
        $battleState['atb_state']['roundStartTime'] = microtime(true) * 1000;
        
        // 增加回合计数
        $battleState['round_count']++;
        
        return [
            'actor_type' => $actorType,
            'player_id' => $playerId,
            'action' => 'attack' // 默认行动是攻击
        ];
    }
    
    /**
     * PVP中处理玩家攻击
     * @param array &$battleState
     * @param string $attackerId
     * @return array
     */
    public function handlePlayerAttack(array &$battleState, string $attackerId): array {
        // Determine attacker and defender
        if ($battleState['challenger']['id'] == $attackerId) {
            $attacker = &$battleState['challenger'];
            $defender = &$battleState['defender'];
        } elseif ($battleState['defender']['id'] == $attackerId) {
            $attacker = &$battleState['defender'];
            $defender = &$battleState['challenger'];
        } else {
            return ['log' => ["错误: 在PVP战斗中未找到ID为 {$attackerId} 的攻击者。"], 'state' => $battleState, 'damage' => 0];
        }

        if ($attacker['attributes']['hp'] <= 0) {
            return ['log' => ["{$attacker['username']} 已经倒下，无法攻击。"], 'state' => $battleState];
        }

        // 检查攻击者是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, $attackerId)) {
            return ['log' => ["{$attacker['username']} 被眩晕了，无法行动！"], 'state' => $battleState];
        }

        $attackResult = Formulas::calculatePvpDamage($attacker, $defender);
        $log = $attackResult['log'];
        
        // 检查是否闪避成功
        if (isset($attackResult['is_dodged']) && $attackResult['is_dodged']) {
            // 生成更丰富的闪避描述
            $weaponName = $attacker['weapon_name'] ?? null;
            if (empty($log)) {
                $dodgeDescriptions = [
                    "轻巧地闪避",
                    "敏捷地躲开",
                    "以毫厘之差避过",
                    "灵活地旋身躲开",
                    "一个侧步躲过",
                    "身形一晃避开",
                    "后撤一步躲过",
                    "腾空翻跃躲开",
                    "矮身滑步闪避",
                    "身体微微倾斜避过"
                ];
                
                $attackDescriptions = [
                    "挥击", "攻击", "猛击", "突刺", "重击"
                ];
                
                $dodgeAction = $dodgeDescriptions[array_rand($dodgeDescriptions)];
                $attackAction = $attackDescriptions[array_rand($attackDescriptions)];
                
                if ($weaponName) {
                    $log = ["【闪避！】{$defender['username']} {$dodgeAction}了 {$attacker['username']} 的 <{$weaponName}> {$attackAction}！"];
                } else {
                    $log = ["【闪避！】{$defender['username']} {$dodgeAction}了 {$attacker['username']} 的{$attackAction}！"];
                }
            }
            
            return [
                'log' => $log,
                'state' => $battleState,
                'damage' => 0,
                'is_dodged' => true
            ];
        }
        
        $damage = $attackResult['damage'];

        $defender['attributes']['hp'] -= $damage;
        
        // 更新伤害贡献计数器
        if (!isset($battleState['damage_contribution'][$attackerId])) {
            $battleState['damage_contribution'][$attackerId] = 0;
        }
        $battleState['damage_contribution'][$attackerId] += $damage;
        
        // 如果log为空，生成一个默认的攻击描述
        if (empty($log)) {
            $critPrefix = $attackResult['is_critical'] ? "【暴击！】" : "";
            $weaponName = $attacker['weapon_name'] ?? null;
            
            if ($weaponName) {
                // 计算物理和元素伤害
                $physicalDamage = $attackResult['physical_damage'] ?? $damage;
                $elementalDamage = $attackResult['elemental_damage'] ?? 0;
                
                // 根据元素伤害类型生成额外描述
                $elementalDescription = "";
                if ($elementalDamage > 0) {
                    $elementalTypes = ['fire_damage', 'ice_damage', 'wind_damage', 'electric_damage'];
                    $elementalDetails = [];
                    
                    foreach ($elementalTypes as $type) {
                        if (isset($attacker['attributes'][$type]) && $attacker['attributes'][$type] > 0) {
                            $elementName = Formulas::$damageTypeNames[$type] ?? $type;
                            // 计算该元素类型的伤害贡献
                            $typeDamage = round($elementalDamage * ($attacker['attributes'][$type] / 100));
                            if ($typeDamage > 0) {
                                $elementalDetails[] = "{$elementName}({$typeDamage})";
                            }
                        }
                    }
                    
                    if (!empty($elementalDetails)) {
                        $elementalDescription = "，附带" . implode("、", $elementalDetails);
                    }
                }
                
                // 使用武器名称，生成更丰富的描述
                $actionVerbs = ["挥舞", "使用", "挥动", "操纵", "祭出", "紧握", "挥击", "重劈", "刺出", "舞动"];
                $actionVerb = $actionVerbs[array_rand($actionVerbs)];
                
                $log[] = "{$critPrefix}{$attacker['username']} {$actionVerb}着<{$weaponName}>对{$defender['username']}造成了 {$damage} 点伤害{$elementalDescription}！";
            } else {
                $log[] = "{$critPrefix}{$attacker['username']}对{$defender['username']}造成了 {$damage} 点伤害！";
            }
        }
            
        if ($defender['attributes']['hp'] <= 0) {
            $defender['attributes']['hp'] = 0;
            $log[] = "{$defender['username']} 被击败了！";
            $battleState['is_over'] = true;
            $battleState['winner'] = ($attackerId == $battleState['challenger']['id']) ? 'challenger' : 'defender';
            $battleState['loser'] = ($attackerId == $battleState['challenger']['id']) ? 'defender' : 'challenger';
        }
        
        return [
            'log' => $log, 
            'state' => $battleState,
            'damage' => $damage,
        ];
    }

    public function handleHeal(array &$battleState, string $playerId): array {
        $player = &$this->getPlayerInBattle($battleState, $playerId);
        if (!$player) {
            return ['log' => ["错误: 未在PVP战斗中找到ID为 {$playerId} 的玩家。"], 'state' => $battleState];
        }
        
        if ($player['attributes']['hp'] <= 0) {
            return ['log' => ["{$player['username']} 已经倒下，无法使用治疗。"], 'state' => $battleState];
        }
        
        $healAmount = 20; // 基础治疗量
        $healAmount += $player['attributes']['intelligence'] * 0.5; // 智力加成
        $healAmount = round($healAmount);
        
        $player['attributes']['hp'] = min($player['attributes']['hp'] + $healAmount, $player['attributes']['max_hp']);
        
        return [
            'log' => ["{$player['username']} 使用治疗，恢复了 {$healAmount} 点生命值。"],
            'state' => $battleState
        ];
    }
    
    public function handleUseItem(array &$battleState, string $playerId, string $itemType): array {
        $player = &$this->getPlayerInBattle($battleState, $playerId);
        if (!$player) {
            return ['log' => ["错误: 尝试使用物品时，未在PVP战斗中找到ID为 {$playerId} 的玩家。"], 'state' => $battleState];
        }

        // 检查玩家是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, $playerId)) {
            return ['log' => ["{$player['username']} 被眩晕了，无法行动！"], 'state' => $battleState];
        }

        // 检查玩家当前状态是否需要使用药水
        if ($itemType === 'hp' && $player['attributes']['hp'] >= $player['attributes']['max_hp']) {
            return ['log' => ["{$player['username']} 的生命值已满，无需使用回血药水。"], 'state' => $battleState];
        }
        
        if ($itemType === 'mp' && $player['attributes']['mp'] >= $player['attributes']['max_mp']) {
            return ['log' => ["{$player['username']} 的魔力值已满，无需使用回蓝药水。"], 'state' => $battleState];
        }

        $db = Database::getInstance();
        $conn = $db->getConnection();
        $log = [];
        $effectToFind = $itemType === 'hp' ? 'hp' : 'mp';
        
        // 1. 获取玩家配置的药品ID
        $stmt = $conn->prepare("SELECT combat_hp_potion_id, combat_mp_potion_id FROM player_attributes WHERE account_id = ?");
        $stmt->execute([$playerId]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $configuredItemId = $itemType === 'hp' ? $config['combat_hp_potion_id'] : $config['combat_mp_potion_id'];
        $itemToUse = null;
        $usedSubstitute = false;

        // 2. 检查玩家是否拥有配置的药品
        if ($configuredItemId) {
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.quantity, it.name, it.effects 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.player_id = ? AND it.id = ? AND inv.quantity > 0"
            );
            $stmt->execute([$playerId, $configuredItemId]);
            $itemToUse = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
        }

        // 3. 如果没有配置的药品或已用完，则寻找替代品
        if (!$itemToUse) {
            $usedSubstitute = true;
            // 使用兼容所有MySQL版本的查询方式
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.quantity, it.name, it.effects 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.player_id = ? AND it.category = 'Potion' AND it.effects LIKE ? AND inv.quantity > 0"
            );
            $stmt->execute([$playerId, '%"'.$effectToFind.'"%']);
            
            // 获取所有匹配的药水
            $potions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            // 使用PHP手动排序，找到效果最好的药水
            $bestPotion = null;
            $bestValue = 0;
            
            foreach ($potions as $potion) {
                $effects = json_decode($potion['effects'], true);
                $value = $effects[$effectToFind] ?? 0;
                
                if ($value > $bestValue) {
                    $bestValue = $value;
                    $bestPotion = $potion;
                }
            }
            
            $itemToUse = $bestPotion;
        }
        
        // 4. 如果找到药品并使用
        if ($itemToUse) {
            $effects = json_decode($itemToUse['effects'], true);
            $value = $effects[$effectToFind] ?? 0;

            if ($itemType === 'hp') {
                $player['attributes']['hp'] = min($player['attributes']['max_hp'], $player['attributes']['hp'] + $value);
            } else {
                $player['attributes']['mp'] = min($player['attributes']['max_mp'], $player['attributes']['mp'] + $value);
            }
            
            $log[] = "{$player['username']} 使用了 {$itemToUse['name']}，恢复了 {$value} 点" . strtoupper($itemType) . "。";
            if ($usedSubstitute && $configuredItemId) {
                $log[] = "(配置的药品已用完，自动使用了替代品)";
            }
            
            // 更新库存
            if ($itemToUse['quantity'] > 1) {
                $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
                $updateStmt->execute([$itemToUse['inventory_id']]);
            } else {
                $deleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                $deleteStmt->execute([$itemToUse['inventory_id']]);
            }
            
            return [
                'state' => $battleState, 
                'log' => $log, 
                'heal_amount' => $value,
                'target_id' => $playerId
            ];
        }

        // 5. 如果没有找到任何可用药品
        $log[] = "{$player['username']} 试图使用药品，但没有找到任何可用的" . strtoupper($itemType) . "药水，只好发动了攻击！";
        $attackResult = $this->handlePlayerAttack($battleState, $playerId);
        
        return [
            'state' => $attackResult['state'],
            'log' => array_merge($log, $attackResult['log']),
            'damage' => $attackResult['damage'],
            'target_id' => ($attackResult['target_id'] ?? null)
        ];
    }
    
    public function handleUseSkill(array &$battleState, string $playerId, int $skillId): array {
        $player = &$this->getPlayerInBattle($battleState, $playerId);
        if (!$player) {
            return ['log' => ["错误: 未在PVP战斗中找到ID为 {$playerId} 的玩家。"], 'state' => $battleState];
        }
        
        // 1. 检查玩家是否正在吟唱，如果是，则忽略本次行动，直接处理吟唱进度
        if (isset($player['casting_info'])) {
            return $this->handleCastingTurn($battleState, $playerId);
        }

        // 1.5. 检查玩家是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, $playerId)) {
            return ['log' => ["{$player['username']} 被眩晕了，无法行动！"], 'state' => $battleState];
        }

        // 2. 检查玩家是否被沉默，无法使用技能
        if ($this->isPlayerSilenced($battleState, $playerId)) {
            return ['log' => ["{$player['username']} 被沉默了，无法使用技能！"], 'state' => $battleState];
        }

        $db = Database::getInstance();
        $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
        $skill = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        if (!$skill) {
            return ['log' => ["{$player['username']} 试图施放一个不存在的技能。"], 'state' => $battleState];
        }
        
        // 2. 检查玩家身上是否已有来自该技能的增益效果
        if ($skill['skill_type'] === 'BUFF' || $skill['skill_type'] === 'DEBUFF') {
            if (!empty($battleState['active_effects'])) {
                foreach ($battleState['active_effects'] as $effect) {
                    // 检查当前玩家是否已经有来自该技能的有效效果
                    if (($effect['source_skill_id'] ?? null) == $skillId && $effect['target_id'] == $playerId) {
                        return ['log' => ["你已经处于【{$skill['name']}】的效果中，无需重复施放。"], 'state' => $battleState];
                    }
                }
            }
        }

        // 3. 检查职业要求
        $requiredJobId = $skill['required_job_id'] ?? null;
        $currentJobId = $player['attributes']['current_job_id'] ?? null;
        if ($requiredJobId !== null && $requiredJobId != $currentJobId) {
            $jobStmt = $db->query("SELECT name FROM jobs WHERE id = ?", [$requiredJobId]);
            $requiredJobName = $jobStmt->fetchColumn() ?: '未知职业';
            $jobStmt->closeCursor();
            return ['log' => ["【职业不符】{$player['username']}当前的职业无法施放【{$skill['name']}】！"], 'state' => $battleState];
        }

        // 4. 检查技能冷却
        $cooldownTurnsLeft = $battleState['skill_cooldowns'][$playerId][$skillId] ?? 0;
        if ($cooldownTurnsLeft > 0) {
            // 如果技能在冷却中，自动转为普通攻击
            $skillName = $skill['name'];
            
            // 生成沉浸式的冷却提示信息
            $cooldownMessages = [
                "{$player['username']}试图施放【{$skillName}】，但技能冷却尚未准备就绪！只好挥动武器发起攻击！",
                "【{$skillName}】的技能冷却尚未恢复，{$player['username']}果断改变策略，发起普通攻击！",
                "技能冷却不足以再次引导【{$skillName}】，{$player['username']}转而发起猛烈的物理攻击！",
                "{$player['username']}的技能冷却尚未恢复，无法再次施放【{$skillName}】！立刻变招，挥舞武器直取敌方要害！",
                "【{$skillName}】的技能冷却尚未恢复(还需{$cooldownTurnsLeft}回合)，{$player['username']}迅速改变战术，发起近身攻击！"
            ];
            
            // 随机选择一条消息
            $cooldownMessage = $cooldownMessages[array_rand($cooldownMessages)];
            
            // 执行普通攻击并返回结果
            $attackResult = $this->handlePlayerAttack($battleState, $playerId);
            
            // 在攻击日志前添加冷却提示
            if (is_array($attackResult['log'])) {
                array_unshift($attackResult['log'], $cooldownMessage);
            }
            
            return $attackResult;
        }
        
        // 5. 检查法力值
        $skillManaCost = Formulas::calculateSkillManaCost($skill['mp_cost'], $skill['skill_level'], $skill['rarity'], $player['attributes']['intelligence']);
        if ($player['attributes']['mp'] < $skillManaCost) {
            return ['log' => ["{$player['username']} 试图施放【{$skill['name']}】，但法力不足！"], 'state' => $battleState];
        }
        
        // 扣除法力值
        $player['attributes']['mp'] -= $skillManaCost;

        // 6. 设置冷却时间
        $cooldown_turns = $skill['cooldown_turns'] ?? 0;
        if ($cooldown_turns > 0) {
            if (!isset($battleState['skill_cooldowns'][$playerId])) {
                $battleState['skill_cooldowns'][$playerId] = [];
            }
            // 直接使用配置的冷却回合数，不再+1
            $battleState['skill_cooldowns'][$playerId][$skillId] = $cooldown_turns;
        }
        
        // 7. 根据技能类型处理
        if ($skill['skill_type'] === 'COMBAT_DELAYED' && !empty($skill['delay_turns'])) {
            // 这是一个吟唱技能
            $player['casting_info'] = [
                'skill_id' => $skillId,
                'skill_name' => $skill['name'],
                'total_rounds' => (int)$skill['delay_turns'],
                'rounds_passed' => 1 // 开始吟唱，算作第1回合
            ];
            
            return ['log' => ["{$player['username']} 开始吟唱【{$skill['name']}】... (1/{$skill['delay_turns']})"], 'state' => $battleState, 'player_id' => $playerId];

        } elseif ($skill['skill_type'] === 'COMBAT_INSTANT') {
            // 这是一个即时技能，直接执行
            $skillResult = $this->executeSkill($battleState, $playerId, $skillId);
            // 为瞬发技能添加完成标记，以便服务器重置意图
            $skillResult['action_completed'] = 'instant_skill_finished';
            return $skillResult;
        } elseif ($skill['skill_type'] === 'BUFF' || $skill['skill_type'] === 'DEBUFF') {
            $skillEffects = json_decode($skill['effects'], true) ?: [];
            $duration = (int)($skill['duration_turns'] ?? 0);
            if (empty($skillEffects) || $duration <= 0) {
                return ['log' => ["【{$skill['name']}】发动了，但似乎什么也没发生。"], 'state' => $battleState];
            }
        
            $targetData = []; // 存储目标信息以便后续处理
            switch ($skill['target_type']) {
                case 'SELF':
                    if ($player['attributes']['hp'] > 0) {
                        $targetData[] = ['id' => $playerId, 'name' => $player['username'], 'type' => 'player'];
                    }
                    break;
                case 'ENEMY':
                case 'HOSTILE':
                    // 确定对手
                    $opponent = null;
                    $opponentId = null;
                    if ($battleState['challenger']['id'] == $playerId) {
                        $opponent = &$battleState['defender'];
                        $opponentId = $opponent['id'];
                    } else {
                        $opponent = &$battleState['challenger'];
                        $opponentId = $opponent['id'];
                    }
                    
                    if ($opponent['attributes']['hp'] > 0) {
                        $targetData[] = ['id' => $opponentId, 'name' => $opponent['username'], 'type' => 'player'];
                    }
                    break;
                case 'ALLY':
                    // 在PVP中，ALLY只能是自己
                    if ($player['attributes']['hp'] > 0) {
                        $targetData[] = ['id' => $playerId, 'name' => $player['username'], 'type' => 'player'];
                    }
                    break;
            }
            
            if (empty($targetData)) {
                return ['log' => ["没有找到【{$skill['name']}】的有效目标。"], 'state' => $battleState];
            }

            if (!isset($battleState['active_effects'])) {
                $battleState['active_effects'] = [];
            }
        
            $targetNames = [];
            $allAppliedEffectsForLog = []; // 用于统一生成日志

            foreach ($targetData as $t) {
                $target = null;
                if ($t['type'] === 'player') {
                    $target = &$this->getPlayerInBattle($battleState, $t['id']);
                }
        
                if ($target) {
                    // 获取技能等级
                    $stmt = $db->query("SELECT skill_level FROM player_skills WHERE player_id = ? AND skill_template_id = ?", [$playerId, $skillId]);
                    $skillLevel = $stmt->fetchColumn() ?: 1;
                    $stmt->closeCursor();
                    
                    $options = ['skill_level' => $skillLevel];
                    $evaluatedEffects = [];
                    
                    foreach($skillEffects as $stat => $valueOrFormula) {
                        $evaluatedValue = 0;
                        if (is_numeric($valueOrFormula)) {
                            $evaluatedValue = $valueOrFormula;
                        } elseif (is_string($valueOrFormula) && trim($valueOrFormula) !== '') {
                            try {
                                // 对于BUFF, 'attacker' 是施法者, 'defender' 是目标
                                $evaluatedValue = Formulas::evaluateDamageFormula($valueOrFormula, $player, $target, $options);
                            } catch (Exception $e) {
                                return ['log' => ["评估增益公式时出错 ({$stat} for {$t['name']}): " . $e->getMessage()], 'state' => $battleState];
                            }
                        }
                        // 只添加有实际效果的属性
                        if ($evaluatedValue != 0) {
                            $evaluatedEffects[$stat] = round($evaluatedValue);
                        }
                    }

                    if (!empty($evaluatedEffects)) {
                        $this->applyEffect($target['attributes'], $evaluatedEffects);

                        $battleState['active_effects'][] = [
                            'id' => uniqid('effect_'),
                            'source_skill_id' => $skillId,
                            'target_id' => $t['id'],
                            'target_type' => $t['type'],
                            'effects' => $evaluatedEffects, // 存储计算后的值
                            'remaining_turns' => $duration,
                            'source_skill_name' => $skill['name']
                        ];
                        $targetNames[] = $t['name'];
                        $allAppliedEffectsForLog[$t['name']] = $evaluatedEffects;

                        // 如果影响了敏捷属性，需要重新计算ATB参数
                        if (isset($evaluatedEffects['agility'])) {
                            $this->recalculateAtbParameters($battleState);
                        }
                    }
                }
                unset($target);
            }

            if (empty($targetNames)) {
                return ['log' => ["【{$skill['name']}】没有产生任何效果。"], 'state' => $battleState];
            }

            // 如果只有一个目标且是自己，则使用特殊的描述
            if (count($targetNames) == 1 && $targetNames[0] === $player['username']) {
                $log = ["{$player['username']} 施放了【{$skill['name']}】，效果持续{$duration}回合。"];
            } else {
                $log = ["{$player['username']}对 " . implode('、', $targetNames) . " 施放了【{$skill['name']}】，效果持续{$duration}回合。"];
            }
            
            // 生成效果描述日志 (以第一个目标为例)
            if (!empty($allAppliedEffectsForLog)) {
                $firstTargetEffects = reset($allAppliedEffectsForLog);
                $effectDescriptions = [];
                foreach($firstTargetEffects as $stat => $value) {
                    if ($value == 0) continue;

                    // 特殊效果的专门描述
                    if ($stat === 'silence' && $value > 0) {
                        $effectDescriptions[] = "沉默";
                    } elseif ($stat === 'stun' && $value > 0) {
                        $effectDescriptions[] = "眩晕";
                    } else {
                        // 普通属性效果
                        $statName = Formulas::$damageTypeNames[$stat] ?? Formulas::$resistanceTypeNames[$stat] ?? self::$attributeNames[$stat] ?? $stat;
                        $change = $value > 0 ? "提升" : "降低";
                        $effectDescriptions[] = "{$statName}{$change}" . abs($value);
                    }
                }
                if(!empty($effectDescriptions)){
                    $log[] = "效果: " . implode('，', $effectDescriptions) . "。";
                }
            }
        
            $result = ['state' => $battleState, 'log' => $log, 'player_id' => $playerId];
            // 为BUFF/DEBUFF技能添加完成标记
            $result['action_completed'] = 'instant_skill_finished';
            return $result;
        } else {
            // 其他未知或非战斗技能类型
            return ['log' => ["{$player['username']} 使用了技能【{$skill['name']}】！"], 'state' => $battleState];
        }
    }
    
    /**
     * 处理吟唱过程中的回合
     * @param array &$battleState 战斗状态引用
     * @param string $playerId 玩家ID
     * @return array 结果
     */
    public function handleCastingTurn(array &$battleState, string $playerId): array {
        $player = &$this->getPlayerInBattle($battleState, $playerId);
        if (!$player || !isset($player['casting_info'])) {
            return ['log' => ["错误: 玩家不在吟唱状态。"], 'state' => $battleState];
        }
        
        $castingInfo = &$player['casting_info'];
        $log = [];
        
        // 推进吟唱进度
        $castingInfo['rounds_passed']++;
        
        if ($castingInfo['rounds_passed'] >= $castingInfo['total_rounds']) {
            // 吟唱完成
            $log = ["{$player['username']}完成了【{$castingInfo['skill_name']}】的吟唱！"];
            
            $skillId = $castingInfo['skill_id'];
            
            // 清除吟唱状态，必须在执行技能前完成，以防bug
            unset($player['casting_info']);
            
            // 执行技能效果
            $skillResult = $this->executeSkill($battleState, $playerId, $skillId);
            
            // 设置技能冷却时间
            $db = Database::getInstance();
            $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
            $skill = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if ($skill && isset($skill['cooldown_turns']) && $skill['cooldown_turns'] > 0) {
                if (!isset($battleState['skill_cooldowns'])) $battleState['skill_cooldowns'] = [];
                if (!isset($battleState['skill_cooldowns'][$playerId])) $battleState['skill_cooldowns'][$playerId] = [];
                // 直接使用配置的冷却回合数，不再+1
                $battleState['skill_cooldowns'][$playerId][$skillId] = $skill['cooldown_turns'];
            }
            
            $finalLog = array_merge($log, $skillResult['log']);
            
            return [
                'log' => $finalLog,
                'state' => $battleState,
                'damage' => $skillResult['damage'] ?? 0,
                'player_id' => $playerId,
                'action_completed' => 'cast_finished' // 明确告知施法已完成
            ];
        } else {
            // 吟唱未完成，继续吟唱
            $log[] = "{$player['username']} 继续吟唱【{$castingInfo['skill_name']}】... ({$castingInfo['rounds_passed']}/{$castingInfo['total_rounds']})";
            return ['log' => $log, 'state' => $battleState, 'player_id' => $playerId];
        }
    }
    
    /**
     * 执行技能效果
     * @param array &$battleState 战斗状态引用
     * @param string $casterId 施法者ID
     * @param int $skillId 技能ID
     * @return array 技能执行结果
     */
    private function executeSkill(array &$battleState, string $casterId, int $skillId): array {
        $log = [];
        $caster = &$this->getPlayerInBattle($battleState, $casterId);
        if (!$caster) {
            return ['log' => ["错误: 施法者不存在。"], 'state' => $battleState, 'damage' => 0];
        }

        // 1. 获取技能模板
        $db = Database::getInstance();
        $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
        $skillTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        if (!$skillTemplate) {
            return ['log' => ["尝试使用了一个不存在的技能。"], 'state' => $battleState, 'damage' => 0];
        }
        
        $log[] = "{$caster['username']} 施放了【{$skillTemplate['name']}】！";
    
        // 2. 确定目标
        $target = null;
        $targetId = null; 
        
        switch ($skillTemplate['target_type']) {
            case 'ENEMY':
            case 'HOSTILE':
                // 确定对手
                if ($battleState['challenger']['id'] == $casterId) {
                    $target = &$battleState['defender'];
                } else {
                    $target = &$battleState['challenger'];
                }
                $targetId = $target['id'];
                break;
            case 'SELF':
                $target = &$caster;
                $targetId = $casterId;
                break;
            case 'ALLY':
                // 在PVP中，ALLY只能是自己
                $target = &$caster;
                $targetId = $casterId;
                break;
            default:
                return ['log' => ["【{$skillTemplate['name']}】的目标类型未知。"], 'state' => $battleState, 'damage' => 0];
        }

        if (!$target || $target['attributes']['hp'] <= 0) {
            return ['log' => ["但目标已经死亡或不存在。"], 'state' => $battleState, 'damage' => 0];
        }
    
        // 3. 计算伤害 (如果技能有伤害公式)
        $damageResult = ['damage' => 0, 'physical_damage' => 0, 'elemental_damage' => 0, 'is_critical' => false, 'is_dodged' => false];
        $skillEffects = json_decode($skillTemplate['effects'], true);

        if (isset($skillEffects['damage_formula'])) {
            try {
                // 获取技能等级
                $stmt = $db->query("SELECT skill_level FROM player_skills WHERE player_id = ? AND skill_template_id = ?", [$casterId, $skillId]);
                $skillLevel = $stmt->fetchColumn() ?: 1;
                $stmt->closeCursor();
                
                $options = ['skill_level' => $skillLevel];

                $baseDamage = Formulas::evaluateDamageFormula($skillEffects['damage_formula'], $caster, $target, $options);

                // 为技能添加暴击判定
                $critChance = 5 + (($caster['attributes']['agility'] ?? 0) / 10);
                $isCritical = (mt_rand(1, 1000) / 10) <= $critChance;
                if ($isCritical) {
                    $baseDamage *= 1.5;
                }
                
                $finalDamage = max(0, floor($baseDamage));

                // 填充伤害结果
                $damageResult['damage'] = $finalDamage;
                $damageResult['is_critical'] = $isCritical;

                $element = $skillEffects['element'] ?? 'physical_damage';
                if ($element === 'physical_damage') {
                    $damageResult['physical_damage'] = $finalDamage;
                } else {
                    $damageResult['elemental_damage'] = $finalDamage;
                }

            } catch (Exception $e) {
                return ['log' => ["伤害公式计算错误: " . $e->getMessage()], 'state' => $battleState, 'damage' => 0];
            }
        
            // 4. 应用伤害和生成日志
            $target['attributes']['hp'] -= $damageResult['damage'];

            if (!empty($skillTemplate['battle_description'])) {
                // 使用自定义战斗描述
                $parseResult = Formulas::parseBattleDescription($skillTemplate['battle_description'], $caster, $target, $skillTemplate, $damageResult);
                $parsedDescription = $parseResult['description'];
                
                // 如果自定义描述中不包含任何伤害占位符，并且确实造成了伤害，则自动在末尾附加标准的伤害描述
                if (!$parseResult['has_damage_placeholder'] && $damageResult['damage'] > 0) {
                    $parsedDescription .= " 造成了 {$damageResult['damage']} 点伤害！";
                }

                $log[] = trim($parsedDescription);
            } else {
                // 使用默认的丰富日志
                $critPrefix = $damageResult['is_critical'] ? "【暴击！】" : "";
                $weaponName = $caster['weapon_name'] ?? null;
                $skillName = $skillTemplate['name'];
                
                // 元素伤害描述
                $elementalDescription = "";
                if (isset($damageResult['elemental_damage']) && $damageResult['elemental_damage'] > 0) {
                    $element = $skillEffects['element'] ?? '';
                    if ($element && isset(Formulas::$damageTypeNames[$element])) {
                        $elementDamage = $damageResult['elemental_damage'];
                        $elementalDescription = "，附带" . Formulas::$damageTypeNames[$element] . "({$elementDamage})";
                    }
                }
                
                // 根据技能类型生成不同的描述
                $skillType = $skillTemplate['skill_type'];
                if ($weaponName) {
                    // 如果有武器，使用更丰富的描述
                    $skillActionVerbs = [
                        "COMBAT_INSTANT" => ["挥舞", "舞动", "操纵", "挥击", "聚力于"],
                        "BUFF" => ["举起", "高举", "凝聚力量于", "借助", "运用"],
                        "DEBUFF" => ["指向", "瞄准", "聚焦", "锁定", "朝向"]
                    ];
                    
                    $verbList = $skillActionVerbs[$skillType] ?? $skillActionVerbs["COMBAT_INSTANT"];
                    $actionVerb = $verbList[array_rand($verbList)];
                    
                    $log[] = "{$critPrefix}{$caster['username']} {$actionVerb} <{$weaponName}>施放【{$skillName}】，对{$target['username']}造成了 {$damageResult['damage']} 点伤害{$elementalDescription}！";
                } else {
                    $log[] = "{$critPrefix}{$caster['username']}施放【{$skillName}】，对{$target['username']}造成了 {$damageResult['damage']} 点伤害{$elementalDescription}！";
                }
            }

            // 更新伤害贡献
            if (!isset($battleState['damage_contribution'][$casterId])) {
                $battleState['damage_contribution'][$casterId] = 0;
            }
            $battleState['damage_contribution'][$casterId] += $damageResult['damage'];

            if ($target['attributes']['hp'] <= 0) {
                $target['attributes']['hp'] = 0;
                $log[] = "{$target['username']} 被击败了！";
                $battleState['is_over'] = true;
                
                // 确定胜利者和失败者
                if ($battleState['challenger']['id'] == $casterId) {
                    $battleState['winner'] = 'challenger';
                    $battleState['loser'] = 'defender';
                } else {
                    $battleState['winner'] = 'defender';
                    $battleState['loser'] = 'challenger';
                }
            }
        }
    
        return ['state' => $battleState, 'log' => $log, 'damage' => $damageResult['damage'], 'player_id' => $casterId];
    }
    
    /**
     * 在每个角色回合开始时处理其身上的所有效果（BUFF/DEBUFF）
     * @param array &$battleState 战斗状态引用
     * @param string $playerId 玩家ID
     * @return array 包含日志和更新后状态的结果
     */
    public function processEffectsTurn(array &$battleState, string $playerId): array {
        if (!isset($battleState['active_effects'])) {
            return ['log' => [], 'state' => $battleState];
        }
    
        $log = [];
        $player = &$this->getPlayerInBattle($battleState, $playerId);
        if (!$player) {
            return ['log' => [], 'state' => $battleState];
        }
    
        $remainingEffects = [];
        foreach ($battleState['active_effects'] as $effect) {
            // 只处理属于当前玩家的效果
            if ($effect['target_id'] == $playerId) {
                // 如果回合数已耗尽，则效果到期
                if ($effect['remaining_turns'] <= 0) {
                    $this->removeEffect($player['attributes'], $effect['effects']);
                    $log[] = "{$player['username']}身上的【{$effect['source_skill_name']}】效果消失了。";

                    // 如果移除的效果影响了敏捷属性，需要重新计算ATB参数
                    if (isset($effect['effects']['agility'])) {
                        $this->recalculateAtbParameters($battleState);
                    }

                    // 不将到期的效果添加到新数组中，直接继续下一个
                    continue;
                }
                
                // 如果效果仍然有效，则减少其持续时间
                $effect['remaining_turns']--;
            }
            
            // 保留效果（无论是其他人的，还是自己的未到期效果）
            $remainingEffects[] = $effect;
        }
        
        $battleState['active_effects'] = $remainingEffects;
        
        return ['log' => $log, 'state' => $battleState];
    }

    /**
     * 将技能效果应用到目标属性上
     * @param array &$targetAttributes 目标属性数组的引用
     * @param array $effects 效果数组
     */
    private function applyEffect(array &$targetAttributes, array $effects): void {
        $needsRecalculation = false;

        foreach ($effects as $stat => $value) {
            if (isset($targetAttributes[$stat])) {
                $targetAttributes[$stat] += $value;

                // 检查是否需要重新计算衍生属性
                if (in_array($stat, ['strength', 'agility', 'constitution', 'intelligence'])) {
                    $needsRecalculation = true;
                }
            } else {
                // 如果属性不存在，则创建它 (例如 'dodge_bonus')
                $targetAttributes[$stat] = $value;
            }
        }

        // 如果基础属性被修改，重新计算衍生属性
        if ($needsRecalculation) {
            $this->recalculateDerivedAttributes($targetAttributes);
        }
    }

    /**
     * 从目标属性上移除技能效果
     * @param array &$targetAttributes 目标属性数组的引用
     * @param array $effects 效果数组
     */
    private function removeEffect(array &$targetAttributes, array $effects): void {
        $needsRecalculation = false;

        foreach ($effects as $stat => $value) {
            if (isset($targetAttributes[$stat])) {
                $targetAttributes[$stat] -= $value;

                // 检查是否需要重新计算衍生属性
                if (in_array($stat, ['strength', 'agility', 'constitution', 'intelligence'])) {
                    $needsRecalculation = true;
                }
            }
        }

        // 如果基础属性被修改，重新计算衍生属性
        if ($needsRecalculation) {
            $this->recalculateDerivedAttributes($targetAttributes);
        }
    }

    /**
     * 获取当前ATB状态，用于发送给客户端
     * @param array $battleState 战斗状态
     * @return array ATB状态数据
     */
    public function getAtbStatus(array $battleState): array {
        if (!isset($battleState['atb_state'])) {
            return [
                'challengerATB' => 0,
                'defenderATB' => 0,
                'roundTimeRemaining' => self::ROUND_TIME_LIMIT,
                'atbMax' => self::ATB_BASE_MAX
            ];
        }
        
        $currentTime = microtime(true) * 1000;
        $elapsedTime = $currentTime - $battleState['atb_state']['roundStartTime'];
        $remainingTime = max(0, self::ROUND_TIME_LIMIT - $elapsedTime);
        $atbMax = $battleState['atb_state']['atbMax'] ?? self::ATB_BASE_MAX;
        
        return [
            'challengerATB' => $battleState['atb_state']['challengerATB'],
            'defenderATB' => $battleState['atb_state']['defenderATB'],
            'roundTimeRemaining' => $remainingTime,
            'atbMax' => $atbMax,
            'round_count' => $battleState['round_count'],
            'max_rounds' => self::PVP_MAX_ROUNDS
        ];
    }
    
    /**
     * 保存PVP战斗结果
     * @param array $battleState 战斗状态
     * @return array 战斗结果
     */
    public function savePvpBattleResults(array $battleState): array {
        if (!$this->db) {
            return ['success' => false, 'message' => '数据库连接不可用'];
        }
        
        if (!$battleState['is_over']) {
            return ['success' => false, 'message' => '战斗尚未结束'];
        }
        
        try {
            $conn = $this->db->getConnection();
            
            // 记录PVP战斗结果
            $stmt = $conn->prepare("
                INSERT INTO pvp_battle_records (
                    battle_id, challenger_id, defender_id, 
                    winner_id, battle_duration, rounds, 
                    challenger_damage, defender_damage, 
                    created_at
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, NOW())
            ");
            
            $battleDuration = microtime(true) - $battleState['start_time'];
            $winnerId = null;
            
            if ($battleState['winner'] === 'challenger') {
                $winnerId = $battleState['challenger']['id'];
            } else if ($battleState['winner'] === 'defender') {
                $winnerId = $battleState['defender']['id'];
            }
            
            $stmt->execute([
                $battleState['battle_id'],
                $battleState['challenger']['id'],
                $battleState['defender']['id'],
                $winnerId,
                $battleDuration,
                $battleState['round_count'],
                $battleState['damage_contribution'][$battleState['challenger']['id']],
                $battleState['damage_contribution'][$battleState['defender']['id']]
            ]);
            
            // 更新玩家PVP统计数据
            $this->updatePlayerPvpStats($battleState);
            
            // 保存PVP战斗日志
            $this->savePvpBattleLog($battleState);
            
            // 更新玩家战斗后的HP和MP
            $this->updatePlayerHpMpAfterBattle($battleState);
            
            return [
                'success' => true,
                'message' => '战斗结果已保存',
                'battle_id' => $battleState['battle_id'],
                'winner' => $battleState['winner']
            ];
            
        } catch (Exception $e) {
            error_log("保存PVP战斗结果时出错: " . $e->getMessage());
            return ['success' => false, 'message' => '保存战斗结果时发生错误'];
        }
    }
    
    /**
     * 更新PVP战斗后玩家的HP和MP
     * @param array $battleState 战斗状态
     */
    private function updatePlayerHpMpAfterBattle(array $battleState): void {
        if (!$this->db) {
            error_log("无法更新玩家HP和MP：数据库连接不可用");
            return;
        }
        
        try {
            $conn = $this->db->getConnection();
            
            // 更新挑战者的HP和MP
            $challengerId = $battleState['challenger']['id'];
            $challengerHp = $battleState['challenger']['attributes']['hp'];
            $challengerMaxHp = $battleState['challenger']['attributes']['max_hp'];
            $challengerMp = $battleState['challenger']['attributes']['mp'];
            $challengerMaxMp = $battleState['challenger']['attributes']['max_mp'];
            
            
            $stmt = $conn->prepare("
                UPDATE player_attributes 
                SET hp = ?, mp = ? 
                WHERE account_id = ?
            ");
            $stmt->execute([$challengerHp, $challengerMp, $challengerId]);
            
            // 更新防守者的HP和MP
            $defenderId = $battleState['defender']['id'];
            $defenderHp = $battleState['defender']['attributes']['hp'];
            $defenderMaxHp = $battleState['defender']['attributes']['max_hp'];
            $defenderMp = $battleState['defender']['attributes']['mp'];
            $defenderMaxMp = $battleState['defender']['attributes']['max_mp'];
            
            $stmt->execute([$defenderHp, $defenderMp, $defenderId]);
            
            error_log("已更新PVP战斗后的玩家HP和MP - 挑战者ID: $challengerId, HP: $challengerHp, MP: $challengerMp | 防守者ID: $defenderId, HP: $defenderHp, MP: $defenderMp");
        } catch (Exception $e) {
            error_log("更新玩家HP和MP时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 更新玩家PVP统计数据
     * @param array $battleState 战斗状态
     */
    private function updatePlayerPvpStats(array $battleState): void {
        if (!$this->db) {
            return;
        }
        
        try {
            $conn = $this->db->getConnection();
            
            // 更新挑战者统计
            $this->updateSinglePlayerPvpStats(
                $conn,
                $battleState['challenger']['id'],
                $battleState['winner'] === 'challenger',
                $battleState['winner'] === 'defender',
                $battleState['winner'] === 'draw'
            );
            
            // 更新被挑战者统计
            $this->updateSinglePlayerPvpStats(
                $conn,
                $battleState['defender']['id'],
                $battleState['winner'] === 'defender',
                $battleState['winner'] === 'challenger',
                $battleState['winner'] === 'draw'
            );
            
        } catch (Exception $e) {
            error_log("更新玩家PVP统计数据时出错: " . $e->getMessage());
        }
    }
    
    /**
     * 更新单个玩家的PVP统计数据
     * @param PDO $conn 数据库连接
     * @param string $playerId 玩家ID
     * @param bool $isWin 是否胜利
     * @param bool $isLoss 是否失败
     * @param bool $isDraw 是否平局
     */
    private function updateSinglePlayerPvpStats($conn, $playerId, $isWin, $isLoss, $isDraw): void {
        // 检查玩家是否有PVP统计记录
        $stmt = $conn->prepare("
            SELECT COUNT(*) FROM pvp_player_stats WHERE player_id = ?
        ");
        $stmt->execute([$playerId]);
        $hasStats = $stmt->fetchColumn() > 0;
        
        if ($hasStats) {
            // 更新现有记录
            $stmt = $conn->prepare("
                UPDATE pvp_player_stats 
                SET total_battles = total_battles + 1,
                    wins = wins + ?,
                    losses = losses + ?,
                    draws = draws + ?,
                    last_battle_time = NOW()
                WHERE player_id = ?
            ");
            $stmt->execute([
                $isWin ? 1 : 0,
                $isLoss ? 1 : 0,
                $isDraw ? 1 : 0,
                $playerId
            ]);
        } else {
            // 创建新记录
            $stmt = $conn->prepare("
                INSERT INTO pvp_player_stats (
                    player_id, total_battles, wins, losses, draws, last_battle_time
                ) VALUES (?, 1, ?, ?, ?, NOW())
            ");
            $stmt->execute([
                $playerId,
                $isWin ? 1 : 0,
                $isLoss ? 1 : 0,
                $isDraw ? 1 : 0
            ]);
        }
    }
    
    /**
     * 保存PVP战斗日志
     * @param array $battleState 战斗状态
     * @return bool 保存是否成功
     */
    private function savePvpBattleLog(array $battleState): bool {
        if (!$this->db) {
            error_log("无法保存PVP战斗日志：数据库连接不可用");
            return false;
        }
        
        try {
            $conn = $this->db->getConnection();
            
            // 获取战斗日志数据
            $challenger = $battleState['challenger'];
            $defender = $battleState['defender'];
            
            // 确定战斗结果
            $battleResult = '';
            if ($battleState['winner'] === 'challenger') {
                $battleResult = 'challenger_win';
            } elseif ($battleState['winner'] === 'defender') {
                $battleResult = 'defender_win';
            } elseif ($battleState['winner'] === 'draw') {
                $battleResult = 'draw';
            } elseif ($battleState['round_count'] >= self::PVP_MAX_ROUNDS) {
                $battleResult = 'timeout';
            } else {
                $battleResult = 'surrender'; // 默认为投降
            }
            
            // 计算总伤害
            $totalDamage = 0;
            if (isset($battleState['damage_contribution'])) {
                foreach ($battleState['damage_contribution'] as $damage) {
                    $totalDamage += $damage;
                }
            }
            
            // 获取场景信息
            // 优先使用战斗状态中保存的场景ID
            $sceneId = $battleState['scene_id'] ?? null;
            $sceneName = $battleState['scene_name'] ?? null;
            
            // 如果战斗状态中没有场景ID，尝试从session获取
            if (!$sceneId && isset($_SESSION['current_scene_id'])) {
                $sceneId = $_SESSION['current_scene_id'];
                
                // 查询场景名称(如果战斗状态中没有场景名)
                if (!$sceneName) {
                    $sceneStmt = $conn->prepare("SELECT name FROM scenes WHERE id = ?");
                    $sceneStmt->execute([$sceneId]);
                    $sceneName = $sceneStmt->fetchColumn() ?: null;
                }
            }
            
            // 准备参与者数据
            $participants = [
                [
                    'id' => $challenger['id'],
                    'name' => $challenger['username'],
                    'level' => $challenger['level'],
                    'role' => 'challenger'
                ],
                [
                    'id' => $defender['id'],
                    'name' => $defender['username'],
                    'level' => $defender['level'],
                    'role' => 'defender'
                ]
            ];
            
            // 收集战斗日志
            $logData = [];
            if (isset($_SESSION['pvp_battle_logs'][$battleState['battle_id']])) {
                $logData = $_SESSION['pvp_battle_logs'][$battleState['battle_id']];
            }
            
            // 准备结束信息
            $endTime = date('Y-m-d H:i:s');
            $startTime = date('Y-m-d H:i:s', $battleState['start_time']);
            
            // 插入战斗日志记录
            $stmt = $conn->prepare("
                INSERT INTO pvp_battle_logs (
                    challenger_id, challenger_name, defender_id, defender_name,
                    scene_id, scene_name, start_time, end_time,
                    battle_result, total_damage, participants, log_data
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");
            
            $stmt->execute([
                $challenger['id'],
                $challenger['username'],
                $defender['id'],
                $defender['username'],
                $sceneId,
                $sceneName,
                $startTime,
                $endTime,
                $battleResult,
                $totalDamage,
                json_encode($participants),
                json_encode($logData)
            ]);
            
            return true;
            
        } catch (Exception $e) {
            error_log("保存PVP战斗日志时出错: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 检查玩家是否被沉默
     * @param array $battleState 战斗状态
     * @param string $playerId 玩家ID
     * @return bool 是否被沉默
     */
    private function isPlayerSilenced(array $battleState, string $playerId): bool {
        if (!isset($battleState['active_effects'])) {
            return false;
        }

        foreach ($battleState['active_effects'] as $effect) {
            if ($effect['target_id'] == $playerId && isset($effect['effects']['silence']) && $effect['effects']['silence'] > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查玩家是否被眩晕
     * @param array $battleState 战斗状态
     * @param string $playerId 玩家ID
     * @return bool 是否被眩晕
     */
    private function isPlayerStunned(array $battleState, string $playerId): bool {
        if (!isset($battleState['active_effects'])) {
            return false;
        }

        foreach ($battleState['active_effects'] as $effect) {
            if ($effect['target_id'] == $playerId && isset($effect['effects']['stun']) && $effect['effects']['stun'] > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 重新计算衍生属性（战斗中临时计算）
     * @param array &$attributes 属性数组的引用
     */
    private function recalculateDerivedAttributes(array &$attributes): void {
        // 确保有等级信息，如果没有则使用默认值
        $level = $attributes['level'] ?? 1;

        // 重新计算衍生属性
        $attributes['attack'] = Formulas::calculateAttack($attributes['strength'], $level);
        $attributes['defense'] = Formulas::calculateDefense($attributes['constitution'], $attributes['agility'], $level);
        $attributes['max_hp'] = Formulas::calculateMaxHp($attributes['constitution'], $level);
        $attributes['max_mp'] = Formulas::calculateMaxMp($attributes['intelligence'], $level);
        $attributes['attack_speed'] = Formulas::calculateAttackSpeed($attributes['agility']);

        // 确保当前血量和魔法值不超过新的最大值
        if (isset($attributes['hp']) && $attributes['hp'] > $attributes['max_hp']) {
            $attributes['hp'] = $attributes['max_hp'];
        }
        if (isset($attributes['mp']) && $attributes['mp'] > $attributes['max_mp']) {
            $attributes['mp'] = $attributes['max_mp'];
        }
    }

    /**
     * 重新计算ATB参数（当攻速相关属性变化时）
     * @param array &$battleState 战斗状态引用
     */
    private function recalculateAtbParameters(array &$battleState): void {
        if (!isset($battleState['atb_state'])) {
            return;
        }

        // 重新计算ATB参数
        $atbParams = $this->calculateAtbParameters($battleState);

        // 更新ATB状态中的参数
        $battleState['atb_state']['atbMax'] = $atbParams['atbMax'];
        $battleState['atb_state']['speedMultipliers'] = $atbParams['speedMultipliers'];
    }
}