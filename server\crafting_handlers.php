<?php
// server/crafting_handlers.php

/**
 * 合成系统服务器处理函数
 * 用于处理与合成相关的WebSocket消息
 */

/**
 * 处理获取配方请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleGetRecipes($fd, $data, $server) {
    if (!isset($data['scene_building_id']) || !isset($data['player_id'])) {
        // 错误消息需要通过服务器实例发送，但不能直接调用私有方法
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整',
            'context' => 'get_recipes_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $sceneBuildingId = $data['scene_building_id'];
    
    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();
    
    // 创建合成管理器实例
    $craftingManager = new CraftingManager($conn);
    
    // 获取可用配方数据
    $recipesData = $craftingManager->getAvailableRecipes($playerId, $sceneBuildingId);
    
    // 返回配方数据，让服务器实例处理发送
    return [MessageProtocol::S2C_RECIPES_DATA, $recipesData];
}

/**
 * 处理合成物品请求
 * @param int $fd WebSocket连接标识符
 * @param array $data 请求数据
 * @param SwooleWebSocketServer $server 服务器实例
 */
function handleCraftItem($fd, $data, $server) {
    if (!isset($data['recipe_id']) || !isset($data['player_id']) || !isset($data['scene_building_id'])) {
        // 返回错误消息，让服务器实例处理
        return [MessageProtocol::S2C_ERROR, [
            'message' => '请求参数不完整',
            'context' => 'craft_item_fail'
        ]];
    }

    $playerId = $data['player_id'];
    $recipeId = $data['recipe_id'];
    $sceneBuildingId = $data['scene_building_id'];
    
    // 获取数据库连接
    $conn = Database::getInstance()->getConnection();
    
    // 创建合成管理器实例
    $craftingManager = new CraftingManager($conn);
    
    // 执行合成操作
    $craftResult = $craftingManager->craftItem($playerId, $recipeId);
    
    // 返回合成结果，让服务器实例处理发送
    return [MessageProtocol::S2C_CRAFT_RESULT, $craftResult];
} 