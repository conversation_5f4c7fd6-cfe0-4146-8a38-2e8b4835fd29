/* General Layout */
body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f4f7f6;
    color: #333;
    margin: 0;
    padding: 20px;
}

.map-container {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

#map-controls {
    background-color: #fff;
    padding: 10px 15px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

#map-controls label {
    font-weight: 600;
}

.control-divider {
    color: #ccc;
    margin: 0 5px;
}

.keyboard-hint {
    color: #28a745;
    font-size: 12px;
    font-weight: 500;
    cursor: help;
    padding: 2px 6px;
    border-radius: 3px;
    background-color: rgba(40, 167, 69, 0.1);
    border: 1px solid rgba(40, 167, 69, 0.2);
}

.keyboard-hint:hover {
    background-color: rgba(40, 167, 69, 0.2);
}

/* 传送点配置筛选样式 */
.continent-zone-filter-container {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
}

.continent-zone-filter-container label {
    font-weight: 600;
    color: #495057;
}

.continent-zone-filter-container select {
    padding: 5px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
}

.continent-zone-filter-container select:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

#layer-selector {
    display: flex;
    gap: 5px;
}

#map-grid-container {
    overflow: auto;
    background-color: #e9ecef;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 10px;
    max-height: 70vh;
}

#map-grid {
    display: grid;
    grid-template-columns: repeat(var(--grid-cols, 21), 1fr);
    gap: 4px;
    min-width: max-content;
}

.map-cell {
    position: relative;
    width: 150px;
    height: 70px;
    border: 1px solid #ccc;
    background-color: #fff;
    border-radius: 4px;
    padding: 4px;
    font-size: 12px;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    overflow: hidden;
}
.map-cell:hover {
    background-color: #f0f0f0;
}

.map-cell.existing {
    background-color: #e8f5e9;
    border-color: #a5d6a7;
}

/* 安全区样式 - 浅绿色 */
.map-cell.existing.safe {
    background-color: #e8f5e9;
    border-color: #4CAF50;
    border-width: 2px;
}

/* 非安全区样式 - 浅红色 */
.map-cell.existing.unsafe {
    background-color: #ffebee;
    border-color: #ef9a9a;
    border-width: 2px;
}

.map-cell.empty {
    background-color: #f5f5f5;
    border-style: dashed;
}

.map-cell.selected {
    border-width: 2px;
    border-color: #42a5f5;
    background-color: #e3f2fd;
    box-shadow: 0 0 10px rgba(66, 165, 245, 0.5);
}

.cell-name {
    font-weight: bold;
    color: #1b5e20;
    font-size: 13px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.cell-id {
    font-size: 11px;
    color: #6c757d;
    margin-top: 2px;
}

.cell-content {
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.cell-coords {
    position: absolute;
    bottom: 4px;
    right: 5px;
    color: #757575;
    font-size: 11px;
    background-color: rgba(255, 255, 255, 0.7);
    padding: 0 3px;
    border-radius: 3px;
    z-index: 4;
}

.cell-monsters {
    color: #d32f2f;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

#map-legend {
    display: flex;
    gap: 20px;
    align-items: center;
    padding: 5px 0;
    font-size: 13px;
}
.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
}
.legend-item .map-cell {
    width: 20px;
    height: 20px;
    cursor: default;
    box-shadow: none;
    transform: none;
}
.legend-item .map-cell:hover {
    transform: none;
    box-shadow: none;
}

/* Modal styles */
.modal {
    display: none; 
    position: fixed; 
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(0,0,0,0.5);
    backdrop-filter: blur(4px);
}

.modal-content {
    background-color: #fefefe;
    margin: 10vh auto;
    padding: 25px;
    border: 1px solid #888;
    width: 80%;
    max-width: 500px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}
.modal-content.large {
    max-width: 700px;
}
.close-button {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

/* Form styles */
#scene-form .form-row {
    display: flex;
    gap: 20px;
}
#scene-form .form-group {
    flex: 1;
    margin-bottom: 15px;
}
.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}
.form-group input[type="text"],
.form-group input[type="number"],
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box;
}

.form-actions {
    margin-top: 20px;
    text-align: right;
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.monster-selection {
    display: flex;
    gap: 10px;
    align-items: center;
}
.monster-selection select { flex: 2; }
.monster-selection input { flex: 1; }
.monster-selection button { flex: 1; }

.monster-list-container {
    margin-top: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    min-height: 50px;
    max-height: 150px;
    overflow-y: auto;
    background-color: #fafafa;
}

.monster-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    background-color: #fff;
    border: 1px solid #eee;
    border-radius: 3px;
    margin-bottom: 5px;
}
.remove-monster-btn {
    background: none;
    border: none;
    color: #f44336;
    font-size: 18px;
    cursor: pointer;
}

/* Toast Notification */
#toast {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    padding: 12px 25px;
    border-radius: 25px;
    background-color: #333;
    color: white;
    font-size: 14px;
    z-index: 2000;
    visibility: hidden;
    opacity: 0;
    transition: opacity 0.3s, visibility 0.3s;
}

#toast.show {
    visibility: visible;
    opacity: 1;
}
#toast.success { background-color: #4CAF50; }
#toast.error { background-color: #f44336; }

/* Batch Actions Panel */
#batch-actions-panel {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px 20px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    gap: 15px;
    z-index: 500;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
}

#batch-actions-panel .close-button {
    font-size: 20px;
    font-weight: normal;
    background: none;
    border: none;
    color: white;
    opacity: 0.7;
}
#batch-actions-panel .close-button:hover {
    opacity: 1;
}

/* Buttons */
.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 600;
    text-decoration: none;
    display: inline-block;
}
.btn-primary {
    background-color: #007bff;
    color: white;
}
.btn-secondary {
    background-color: #6c757d;
    color: white;
}
.btn-danger {
    background-color: #dc3545;
    color: white;
}
.btn-info {
    background-color: #17a2b8;
    color: white;
}
.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}
small { color: #666; }
h1, h2 {
    color: #333;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
    margin-top: 0;
}

/* --- Form Grid --- */
.form-grid-scenes {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
    margin-bottom: 20px;
}

/* --- Monsters Section --- */
#monsters-section {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}
#monster-list {
    margin-bottom: 15px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.monster-item {
    background-color: #e9ecef;
    padding: 5px 10px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}
.monster-item .remove-monster-btn {
    background: none;
    border: none;
    color: #dc3545;
    cursor: pointer;
    font-weight: bold;
}
.monster-adder {
    display: flex;
    gap: 10px;
}
.monster-adder select {
    flex-grow: 1;
}
.monster-adder input {
    width: 80px;
}

/* --- Batch Edit Modal --- */
#batch-edit-modal .form-description {
    font-size: 14px;
    color: #6c757d;
    margin-bottom: 15px;
}

#batch-monsters-section label small {
    font-weight: normal;
    color: #888;
}

#batch-monster-list {
    margin-bottom: 10px;
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

#main-title {
    text-align: center;
    color: #333;
    margin-bottom: 15px;
    font-size: 24px;
    font-weight: 600;
}

.controls-container {
    padding: 10px 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 15px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.map-controls {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 20px;
}

.expand-controls, #layer-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.expand-controls input[type="number"] {
    width: 60px;
    padding: 5px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
}

.expand-controls .btn {
    padding: 5px 10px;
    font-size: 16px;
    line-height: 1;
}

.map-grid {
    background-color: #f8f9fa;
}

.form-row {
    display: flex;
    gap: 20px;
}

.form-row .form-group {
    flex: 1;
}

.monster-selection {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.monster-selection select {
    flex: 1;
}

.monster-selection input[type="number"] {
    width: 80px;
}

.monster-list-container {
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

/* 建筑样式 */
.building-selection {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.building-selection select {
    flex: 1;
}

.building-list-container {
    min-height: 80px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ccc;
    padding: 10px;
    border-radius: 4px;
    background-color: #f8f9fa;
}

.building-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 5px;
    background-color: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 3px;
    margin-bottom: 5px;
}

.remove-building-btn {
    background: none;
    border: none;
    color: #f44336;
    font-size: 18px;
    cursor: pointer;
}

/* 地图单元格中的建筑指示器 */
.cell-buildings {
    color: #1565c0;
    font-size: 11px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* 商店商品配置模态框样式 */
.shop-item-selection {
    display: flex;
    align-items: flex-end;
    gap: 10px;
    margin-top: 15px;
}

.category-filters {
    display: block;
    width: 100%;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.category-filter-btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.shop-item-selection .form-group {
    margin-bottom: 0;
}

.shop-item-selection .select2-container {
    flex-grow: 1;
}

.shop-item-list-container {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

.shop-item-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
    background-color: #f9f9f9;
    border-radius: 4px;
    margin-bottom: 5px;
}

.shop-item-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.shop-item-row .item-name {
    font-weight: bold;
    flex: 2;
}

.shop-item-row .item-details {
    display: flex;
    gap: 15px;
    flex: 1;
    justify-content: flex-end;
    margin-right: 10px;
}

/* 传送点配置样式 */
.destination-list-container {
    margin-top: 15px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 10px;
    max-height: 400px;
    overflow-y: auto;
}

.destination-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    background-color: #fff;
    margin-bottom: 5px;
    border-radius: 4px;
}

.destination-item:last-child {
    margin-bottom: 0;
}

.destination-item:hover {
    background-color: #f8f9fa;
}

.destination-item > span {
    flex-grow: 1;
    font-weight: 500;
}

.cost-info {
    margin-left: 10px;
    color: #666;
    font-size: 12px;
}

.config-cost-btn {
    margin-left: 10px;
    padding: 4px 8px;
    font-size: 12px;
}

.remove-destination-btn {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 24px;
    height: 24px;
    cursor: pointer;
    font-size: 16px;
    line-height: 1;
    margin-left: 10px;
}

.remove-destination-btn:hover {
    background: #c82333;
}

.destination-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid var(--border-color);
}

.destination-row:last-child {
    border-bottom: none;
}

.destination-row:hover {
    background-color: rgba(0, 0, 0, 0.05);
}

.destination-actions {
    display: flex;
    gap: 10px;
}

.modal-header-info {
    margin-bottom: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px;
}

.modal-header-info p {
    margin: 5px 0;
}

.destination-selection {
    display: flex;
    gap: 10px;
    align-items: center;
    margin-bottom: 10px;
}

.destination-selection select {
    flex-grow: 1;
}

/* Select2样式调整 */
.select2-container {
    min-width: 300px;
}

.select2-dropdown {
    border-color: var(--border-color);
}

.select2-container--default .select2-selection--single {
    border-color: var(--border-color);
    height: 36px;
    display: flex;
    align-items: center;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
    height: 34px;
}

/* 传送目标场景标记 */
.teleport-destination-marker {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 16px;
    height: 16px;
    background-color: #4CAF50;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 5px rgba(0,0,0,0.5);
    cursor: help;
    z-index: 100;
    pointer-events: auto;
    display: block !important;
    visibility: visible !important;
    animation: pulse 1.5s infinite; /* 添加脉冲动画效果 */
}

/* 脉冲动画 */
@keyframes pulse {
    0% { transform: scale(1); box-shadow: 0 0 5px rgba(0,0,0,0.5); }
    50% { transform: scale(1.15); box-shadow: 0 0 8px rgba(76,175,80,0.7); }
    100% { transform: scale(1); box-shadow: 0 0 5px rgba(0,0,0,0.5); }
}

.teleport-destination-marker:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* 传送点建筑标记 */
.teleport-source-marker {
    position: absolute;
    top: 5px;
    right: 5px;
    width: 12px;
    height: 12px;
    background-color: #f44336;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 3px rgba(0,0,0,0.3);
    cursor: help;
    z-index: 10;
    pointer-events: auto;
}

.teleport-source-marker:hover::after {
    content: attr(data-tooltip);
    position: absolute;
    right: 15px;
    top: 50%;
    transform: translateY(-50%);
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 1000;
}

/* NPC头像容器 */
.npc-avatars-container {
    position: absolute;
    bottom: 4px;
    left: 4px;
    display: flex;
    gap: 3px;
    z-index: 5;
}

/* NPC头像样式 */
.npc-avatar {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    position: relative;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23666"><path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/></svg>');
    background-size: 16px;
    background-position: center;
    background-repeat: no-repeat;
}

/* 商人NPC样式 */
.npc-avatar.merchant {
    background-color: #ffecb3;
    border-color: #ffc107;
}

/* 任务NPC样式 */
.npc-avatar.quest-giver {
    background-color: #c8e6c9;
    border-color: #4caf50;
}

/* 既是商人又是任务NPC的样式 */
.npc-avatar.merchant-quest {
    background-color: #e1f5fe;
    border-color: #03a9f4;
}

/* 悬停提示样式 - 使用伪元素实现 */
.npc-avatar[data-tooltip],
.teleport-source-marker[data-tooltip] {
    position: relative;
}

.npc-avatar[data-tooltip]::before,
.teleport-source-marker[data-tooltip]::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 125%;
    left: 50%;
    transform: translateX(-50%);
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 12px;
    white-space: nowrap;
    z-index: 9999;
    pointer-events: none;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.2s, visibility 0.2s;
    max-width: 250px;
    text-align: center;
}

/* 处理多行提示文本 */
.teleport-source-marker[data-tooltip]::before {
    white-space: pre-line;
    text-align: left;
}

/* 显示提示框 */
.npc-avatar:hover[data-tooltip]::before,
.teleport-source-marker:hover[data-tooltip]::before {
    opacity: 1;
    visibility: visible;
}

/* 确保NPC头像不会与坐标信息重叠 */
.cell-coords {
    z-index: 4;
}

/* 全局提示框样式 */
.global-tooltip {
    position: fixed;
    display: none;
    background-color: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 14px;
    z-index: 10000;
    pointer-events: none;
    max-width: 300px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    white-space: pre-line;
}

/* Layer Name Modal Styles */
#layer-name-modal .modal-content {
    max-width: 500px;
}

#layer-name-modal .form-group {
    margin-bottom: 15px;
}

#layer-name-modal .form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
    color: #333;
}

#layer-name-modal .form-group input,
#layer-name-modal .form-group textarea {
    width: 100%;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    box-sizing: border-box;
}

#layer-name-modal .form-group input:focus,
#layer-name-modal .form-group textarea:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

#layer-name-modal .form-actions {
    display: flex;
    gap: 10px;
    justify-content: flex-end;
    margin-top: 20px;
}

/* Layer button hover effects */
#layer-selector .btn {
    position: relative;
    transition: all 0.2s ease;
}

#layer-selector .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

#layer-selector .btn:hover::after {
    content: "右键设置名称";
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 11px;
    white-space: nowrap;
    z-index: 1000;
}

/* View Center Marker */
.view-center-marker {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 16px;
    color: #28a745;
    text-shadow: 0 0 3px rgba(255, 255, 255, 0.8);
    z-index: 10;
    pointer-events: none;
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
}

/* 传送点配置模态框样式 */
#teleporter-config-modal {
    z-index: 1001; /* 比普通模态框稍高 */
}

#teleporter-config-modal select {
    position: relative;
    z-index: 1002; /* 确保下拉列表在模态框之上 */
}

/* 物品成本配置模态框样式 */
#item-cost-modal {
    z-index: 1003 !important; /* 比传送点配置模态框更高 */
    position: fixed !important;
    display: none;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6); /* 稍微深一点的背景 */
    backdrop-filter: blur(4px);
}

#item-cost-modal .modal-content {
    background-color: #fefefe;
    margin: 15vh auto;
    padding: 25px;
    border: 1px solid #888;
    width: 80%;
    max-width: 400px;
    border-radius: 8px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.3);
    position: relative;
    z-index: 1004;
}

#item-cost-modal select {
    position: relative;
    z-index: 1005; /* 确保下拉列表在模态框之上 */
}

/* 确保物品配置模态框的关闭按钮正常工作 */
#item-cost-modal .close-button {
    position: absolute;
    top: 15px;
    right: 20px;
    font-size: 24px;
    font-weight: bold;
    color: #aaa;
    cursor: pointer;
    z-index: 1006;
}

#item-cost-modal .close-button:hover {
    color: #000;
}

/* 物品配置表单样式 */
#item-cost-form .form-group {
    margin-bottom: 15px;
}

#item-cost-form label {
    display: block;
    margin-bottom: 5px;
    font-weight: 600;
}

#item-cost-form select,
#item-cost-form input {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

#item-cost-form .form-actions {
    margin-top: 20px;
    text-align: right;
}

#item-cost-form .form-actions button {
    margin-left: 10px;
}

/* 修复select下拉选项的z-index问题 */
#destination-scene-select {
    position: relative;
    z-index: 1002 !important;
}

/* 自定义下拉列表样式 */
.custom-select-container {
    position: relative;
}

.custom-select {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    cursor: pointer;
    user-select: none;
    min-height: 20px;
}

.custom-select:hover {
    border-color: #007bff;
}

.custom-select.active {
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.select-text {
    flex: 1;
    color: #333;
}

.select-text.placeholder {
    color: #999;
}

.select-arrow {
    margin-left: 8px;
    transition: transform 0.2s;
    font-size: 12px;
}

.custom-select.active .select-arrow {
    transform: rotate(180deg);
}

.custom-select-options {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1003;
    display: none;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.custom-select-options.show {
    display: block;
}

.custom-select-option {
    padding: 8px 12px;
    cursor: pointer;
    border-bottom: 1px solid #f0f0f0;
}

.custom-select-option:hover {
    background-color: #f8f9fa;
}

.custom-select-option:last-child {
    border-bottom: none;
}

.custom-select-option.selected {
    background-color: #007bff;
    color: white;
}