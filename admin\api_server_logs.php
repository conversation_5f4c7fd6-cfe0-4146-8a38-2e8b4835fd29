<?php
// admin/api_server_logs.php

require_once 'auth.php'; // 添加认证检查
header('Content-Type: application/json; charset=utf-8');

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

try {
    // 获取请求参数
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'get_logs':
            $lines = (int)($_POST['lines'] ?? 100);
            $filter = $_POST['filter'] ?? '';
            $result = getServerLogs($lines, $filter);
            break;
        case 'get_stats':
            $result = getLogStats();
            break;
        case 'clear_logs':
            $result = clearLogs();
            break;
        case 'download_logs':
            downloadLogs();
            exit;
        default:
            throw new Exception('无效的操作');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 获取服务器日志
 */
function getServerLogs($lines = 100, $filter = '') {
    $logFile = '../game-server.log';
    
    if (!file_exists($logFile)) {
        return [
            'logs' => [],
            'message' => '日志文件不存在',
            'file_size' => 0,
            'last_modified' => null
        ];
    }
    
    // 获取文件信息
    $fileSize = filesize($logFile);
    $lastModified = filemtime($logFile);
    
    // 读取最后N行
    $logs = [];
    if ($fileSize > 0) {
        // 使用tail命令获取最后N行（如果系统支持）
        if (function_exists('exec') && !isWindows()) {
            $command = "tail -n {$lines} " . escapeshellarg($logFile);
            exec($command, $output);
            $logs = $output;
        } else {
            // 备用方法：读取整个文件然后取最后N行
            $allLines = file($logFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
            if ($allLines !== false) {
                $logs = array_slice($allLines, -$lines);
            }
        }
    }
    
    // 应用过滤器
    if (!empty($filter) && !empty($logs)) {
        $logs = array_filter($logs, function($line) use ($filter) {
            return stripos($line, $filter) !== false;
        });
        $logs = array_values($logs); // 重新索引
    }
    
    // 解析日志行
    $parsedLogs = [];
    foreach ($logs as $line) {
        $parsedLogs[] = parseLogLine($line);
    }
    
    return [
        'logs' => $parsedLogs,
        'total_lines' => count($parsedLogs),
        'file_size' => $fileSize,
        'file_size_human' => formatBytes($fileSize),
        'last_modified' => date('Y-m-d H:i:s', $lastModified),
        'filter_applied' => !empty($filter)
    ];
}

/**
 * 解析日志行
 */
function parseLogLine($line) {
    // 日志格式: Jul 15 22:00:07 iZe1jacokmymj4Z start_game.sh[2559736]: 消息内容
    $pattern = '/^(\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+(\S+)\s+(\S+)\[(\d+)\]:\s*(.*)$/';
    
    if (preg_match($pattern, $line, $matches)) {
        $timestamp = $matches[1];
        $hostname = $matches[2];
        $process = $matches[3];
        $pid = $matches[4];
        $message = $matches[5];
        
        // 确定日志级别
        $level = determineLogLevel($message);
        
        return [
            'timestamp' => $timestamp,
            'hostname' => $hostname,
            'process' => $process,
            'pid' => $pid,
            'message' => $message,
            'level' => $level,
            'raw' => $line
        ];
    } else {
        // 无法解析的行
        return [
            'timestamp' => '',
            'hostname' => '',
            'process' => '',
            'pid' => '',
            'message' => $line,
            'level' => 'unknown',
            'raw' => $line
        ];
    }
}

/**
 * 确定日志级别
 */
function determineLogLevel($message) {
    $message = strtolower($message);
    
    if (strpos($message, 'error') !== false || strpos($message, 'fatal') !== false) {
        return 'error';
    } elseif (strpos($message, 'warning') !== false || strpos($message, 'warn') !== false) {
        return 'warning';
    } elseif (strpos($message, 'info') !== false || strpos($message, '启动') !== false || strpos($message, '成功') !== false) {
        return 'info';
    } elseif (strpos($message, 'debug') !== false) {
        return 'debug';
    } else {
        return 'normal';
    }
}

/**
 * 获取日志统计信息
 */
function getLogStats() {
    $logFile = '../game-server.log';
    
    if (!file_exists($logFile)) {
        return [
            'file_exists' => false,
            'file_size' => 0,
            'total_lines' => 0,
            'last_modified' => null
        ];
    }
    
    $fileSize = filesize($logFile);
    $lastModified = filemtime($logFile);
    
    // 统计行数
    $lineCount = 0;
    if ($fileSize > 0) {
        $handle = fopen($logFile, 'r');
        if ($handle) {
            while (!feof($handle)) {
                fgets($handle);
                $lineCount++;
            }
            fclose($handle);
        }
    }
    
    return [
        'file_exists' => true,
        'file_size' => $fileSize,
        'file_size_human' => formatBytes($fileSize),
        'total_lines' => $lineCount,
        'last_modified' => date('Y-m-d H:i:s', $lastModified),
        'file_path' => realpath($logFile)
    ];
}

/**
 * 清空日志文件
 */
function clearLogs() {
    $logFile = '../game-server.log';
    
    if (!file_exists($logFile)) {
        throw new Exception('日志文件不存在');
    }
    
    // 备份当前日志
    $backupFile = '../game-server-backup-' . date('Y-m-d-H-i-s') . '.log';
    if (!copy($logFile, $backupFile)) {
        throw new Exception('无法创建日志备份');
    }
    
    // 清空日志文件
    if (file_put_contents($logFile, '') === false) {
        throw new Exception('无法清空日志文件');
    }
    
    return [
        'message' => '日志文件已清空',
        'backup_file' => basename($backupFile),
        'cleared_at' => date('Y-m-d H:i:s')
    ];
}

/**
 * 下载日志文件
 */
function downloadLogs() {
    $logFile = '../game-server.log';
    
    if (!file_exists($logFile)) {
        http_response_code(404);
        echo json_encode(['success' => false, 'message' => '日志文件不存在']);
        return;
    }
    
    $filename = 'game-server-' . date('Y-m-d-H-i-s') . '.log';
    
    header('Content-Type: application/octet-stream');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . filesize($logFile));
    header('Cache-Control: no-cache, must-revalidate');
    header('Expires: 0');
    
    readfile($logFile);
}

/**
 * 格式化字节大小
 */
function formatBytes($size, $precision = 2) {
    $units = ['B', 'KB', 'MB', 'GB', 'TB'];
    
    for ($i = 0; $size > 1024 && $i < count($units) - 1; $i++) {
        $size /= 1024;
    }
    
    return round($size, $precision) . ' ' . $units[$i];
}

/**
 * 检查是否为Windows系统
 */
function isWindows() {
    return strtoupper(substr(PHP_OS, 0, 3)) === 'WIN';
}
?>
