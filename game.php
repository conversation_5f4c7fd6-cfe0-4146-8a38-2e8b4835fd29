<?php
//获取访问的URL
$requestedUrl = $_SERVER['REQUEST_URI'];
//如果是game.php则返回404
if ($requestedUrl === '/game.php') {
    header('HTTP/1.0 404 Not Found');
    exit;
}

require_once 'config/aes.php';
require_once 'config/config.php';
//开启session
session_start();
//生成随机数
$randomNumber = random_int(100000, 999999);
$secretKey = 'Start_New_Game_' . $randomNumber . '_' . date('YmdHis');
//设置session
$_SESSION['secretKey'] = $secretKey;

// 验证时间戳签名（允许±10秒时间差）
function validateTimestampToken($secretKey) {
    $key = aes_key; // 密钥，必须是 16、24 或 32 字符长度的字符串
    $iv = aes_iv; // 初始化向量，必须是 16 字符长度的字符串

    $timestamp = time();
    $salt = bin2hex(random_bytes(8)); // 8位十六进制随机数，可自定义

    //加密时间戳
    $encryptedTimestamp = encryptAES($timestamp, $key, $iv);

    // 使用加密的时间戳生成签名
    $signature = hash_hmac('sha256', $encryptedTimestamp . ':' . $salt, $secretKey);

    return base64_encode($encryptedTimestamp . ':' . $salt . ':' . $signature);
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>星尘战纪</title>
    <link rel="preconnect" href="https://fonts.font.im">
    <link rel="preconnect" href="https://gstatic.font.im" crossorigin>
    <link href="css/c.css" rel="stylesheet">
    <link rel="stylesheet" href="css/g.css" class="cache-bust">
    <link rel="stylesheet" href="css/r.css" class="cache-bust">
    <link rel="stylesheet" href="css/n.css" class="cache-bust">
    <link rel="stylesheet" href="css/q.css" class="cache-bust">
    <link rel="stylesheet" href="css/p.css" class="cache-bust">
</head>
<body>
    <script>var N = "<?php echo $secretKey . '_' . aes_key . '_' . aes_iv; ?>";</script>
    <script src="js/c.js?v=<?php echo validateTimestampToken($secretKey); ?>" ></script>
    <script src="js/h.js?v=<?php echo validateTimestampToken($secretKey); ?>" ></script>
</body>
</html>
