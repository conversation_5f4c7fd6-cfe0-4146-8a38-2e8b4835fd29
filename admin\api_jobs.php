<?php
// admin/api_jobs.php
require_once '../config/Database.php';
require_once 'auth.php'; // Assuming you have an auth check

header('Content-Type: application/json');

$response = ['success' => false, 'message' => '无效的操作'];

$action = $_REQUEST['action'] ?? '';

if ($action === 'list') {
    try {
        $db = Database::getInstance();
        $stmt = $db->query("SELECT id, name FROM jobs ORDER BY id");
        $jobs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $response = ['success' => true, 'jobs' => $jobs];
    } catch (Exception $e) {
        http_response_code(500);
        $response = ['success' => false, 'message' => '无法获取职业列表: ' . $e->getMessage()];
    }
}

echo json_encode($response, JSON_UNESCAPED_UNICODE); 