<?php
session_start();
require_once '../config/Database.php';

// 如果已经登录，直接跳转到主面板
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in'] === true) {
    error_log('已登录，重定向到主面板。Session ID: ' . session_id());
    header('Location: dashboard.php');
    exit;
}

$error = '';
$message = $_GET['message'] ?? '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $username = $_POST['username'] ?? '';
    $password = $_POST['password'] ?? '';

    if (empty($username) || empty($password)) {
        $error = '请输入账号和密码';
        error_log('登录失败：用户名或密码为空');
    } else {
        try {
            $db = Database::getInstance()->getConnection();
            $stmt = $db->prepare("SELECT * FROM admins WHERE username = :username");
            $stmt->execute(['username' => $username]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($admin && password_verify($password, $admin['password_hash'])) {
                session_regenerate_id(true);
                $_SESSION['admin_logged_in'] = true;
                $_SESSION['admin_id'] = $admin['id'];
                $_SESSION['admin_username'] = $admin['username'];
                $_SESSION['last_activity'] = time(); // 设置初始活动时间

                error_log('登录成功：' . $username . ' Session ID: ' . session_id());

                session_write_close();
                header('Location: dashboard.php');
                exit;
            } else {
                $error = '账号或密码错误';
                error_log('登录失败：用户名或密码不正确 - ' . $username);
            }
        } catch (PDOException $e) {
            error_log('数据库错误：' . $e->getMessage());
            $error = '数据库连接失败，请联系管理员';
        }
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>游戏后台管理 - 登录</title>
    <link rel="stylesheet" href="style.css">
</head>
<body class="login-page">
    <div class="login-container">
        <h2>游戏后台管理系统</h2>
        <form action="index.php" method="post">
            <div class="form-group">
                <label for="username">管理员账号:</label>
                <input type="text" id="username" name="username" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" name="password" required>
            </div>
            <button type="submit" class="btn">登录</button>
            <?php
            if (!empty($message)) {
                echo '<p class="success-message">' . htmlspecialchars($message) . '</p>';
            }
            if (!empty($error)) {
                echo '<p class="error-message">' . htmlspecialchars($error) . '</p>';
            }
            ?>
        </form>
    </div>
</body>
</html> 