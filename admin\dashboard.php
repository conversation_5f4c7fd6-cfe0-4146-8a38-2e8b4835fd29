<?php
require_once 'auth.php';

$adminUsername = $_SESSION['admin_username'] ?? '管理员';

// Database connection
require_once '../config/Database.php';

// Fetch stats
try {
    $pdo = Database::getInstance()->getConnection();

    // 基础统计
    $player_count = $pdo->query('SELECT count(*) FROM accounts')->fetchColumn();
    $scene_count = $pdo->query('SELECT count(*) FROM scenes')->fetchColumn();
    $monster_count = $pdo->query('SELECT count(*) FROM monster_templates')->fetchColumn();
    $item_template_count = $pdo->query('SELECT count(*) FROM item_templates')->fetchColumn();

    // 游戏活动统计
    $battle_count_today = 0;
    $pvp_count_today = 0;
    $trade_count_today = 0;

    // 安全地查询各个表
    try {
        $battle_count_today = $pdo->query("SELECT count(*) FROM battle_logs WHERE DATE(start_time) = CURDATE()")->fetchColumn();
    } catch (Exception $e) {
        $battle_count_today = 0;
    }

    try {
        $pvp_count_today = $pdo->query("SELECT count(*) FROM pvp_battle_logs WHERE DATE(start_time) = CURDATE()")->fetchColumn();
    } catch (Exception $e) {
        $pvp_count_today = 0;
    }

    try {
        $trade_count_today = $pdo->query("SELECT count(*) FROM trade_history WHERE DATE(completed_at) = CURDATE()")->fetchColumn();
    } catch (Exception $e) {
        $trade_count_today = 0;
    }

    // 24小时活跃玩家统计（基于PVP战斗活动）
    $pvp_players_today = $pdo->query("
        SELECT COUNT(DISTINCT challenger_id) + COUNT(DISTINCT defender_id) as active_count
        FROM pvp_battle_logs
        WHERE DATE(start_time) = CURDATE()
    ")->fetchColumn();

    // 如果没有PVP数据，使用简单的估算
    $active_players_24h = $pvp_players_today > 0 ? $pvp_players_today : floor($player_count * 0.1);

    // 等级分布统计
    $level_stats = [
        'level_1_10' => 0,
        'level_11_30' => 0,
        'level_31_50' => 0,
        'level_50_plus' => 0,
        'avg_level' => 1,
        'max_level' => 1
    ];

    try {
        $level_stats = $pdo->query("
            SELECT
                COUNT(CASE WHEN level BETWEEN 1 AND 10 THEN 1 END) as level_1_10,
                COUNT(CASE WHEN level BETWEEN 11 AND 30 THEN 1 END) as level_11_30,
                COUNT(CASE WHEN level BETWEEN 31 AND 50 THEN 1 END) as level_31_50,
                COUNT(CASE WHEN level > 50 THEN 1 END) as level_50_plus,
                COALESCE(AVG(level), 1) as avg_level,
                COALESCE(MAX(level), 1) as max_level
            FROM player_attributes
        ")->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 使用默认值
    }

    // 职业分布统计
    $job_stats = [];
    try {
        // 首先检查jobs表是否有数据，如果没有则初始化基础职业
        $job_count = $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn();
        if ($job_count == 0) {
            // 插入基础职业数据
            $pdo->exec("
                INSERT INTO jobs (id, name, description) VALUES
                (1, '新手', '初始职业，没有特殊技能'),
                (2, '战士', '近战物理职业，高血量高防御'),
                (3, '法师', '远程魔法职业，高魔法攻击'),
                (4, '射手', '远程物理职业，高敏捷高暴击')
            ");
        }

        // 查询职业分布（优先使用current_job_id）
        $job_stats_new = $pdo->query("
            SELECT j.name as job, COUNT(pa.account_id) as count
            FROM jobs j
            LEFT JOIN player_attributes pa ON j.id = pa.current_job_id
            GROUP BY j.id, j.name
            ORDER BY count DESC
        ")->fetchAll(PDO::FETCH_ASSOC);

        // 过滤掉count为0的职业
        $job_stats = array_filter($job_stats_new, function($job) {
            return $job['count'] > 0;
        });

        // 如果没有使用current_job_id的数据，尝试使用旧的job字段
        if (empty($job_stats)) {
            $job_stats = $pdo->query("
                SELECT job, COUNT(*) as count
                FROM player_attributes
                WHERE job IS NOT NULL AND job != ''
                GROUP BY job
                ORDER BY count DESC
            ")->fetchAll(PDO::FETCH_ASSOC);
        }
    } catch (Exception $e) {
        // 如果所有查询都失败，使用默认空数组
        error_log("职业统计查询失败: " . $e->getMessage());
    }

    // 最近7天的活动统计
    $weekly_activity = [];
    try {
        $weekly_activity = $pdo->query("
            SELECT
                DATE(start_time) as date,
                COUNT(*) as battles
            FROM pvp_battle_logs
            WHERE start_time >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
            GROUP BY DATE(start_time)
            ORDER BY date DESC
        ")->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 使用默认空数组
    }

    // 仓库使用统计
    $warehouse_stats = ['users_count' => 0, 'total_items' => 0, 'warehouse_count' => 0];
    try {
        $warehouse_stats = $pdo->query("
            SELECT
                COUNT(DISTINCT player_id) as users_count,
                COUNT(*) as total_items,
                COUNT(DISTINCT scene_building_id) as warehouse_count
            FROM warehouse_storage
        ")->fetch(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        // 使用默认值
    }

    // 安全违规统计
    $security_violations_today = 0;
    try {
        $security_violations_today = $pdo->query("SELECT count(*) FROM security_violations WHERE DATE(violation_time) = CURDATE()")->fetchColumn();
    } catch (Exception $e) {
        $security_violations_today = 0;
    }

} catch (PDOException $e) {
    // Handle DB error gracefully
    error_log($e->getMessage());
    $error_message = "数据库查询失败: " . $e->getMessage();
    $player_count = '错误';
    $scene_count = '错误';
    $monster_count = '错误';
    $item_template_count = '错误';
    $battle_count_today = '错误';
    $pvp_count_today = '错误';
    $trade_count_today = '错误';
    $active_players_24h = '错误';
    $level_stats = [
        'level_1_10' => 0,
        'level_11_30' => 0,
        'level_31_50' => 0,
        'level_50_plus' => 0,
        'avg_level' => '错误',
        'max_level' => '错误'
    ];
    $job_stats = [];
    $weekly_activity = [];
    $warehouse_stats = ['users_count' => '错误', 'total_items' => '错误', 'warehouse_count' => '错误'];
    $security_violations_today = '错误';
}

$pageTitle = '主面板';
$currentPage = 'dashboard';
require_once 'layout_header.php'; 
?>

<div class="page-content">
    <h1>欢迎, <?php echo htmlspecialchars($adminUsername); ?>!</h1>

    <h2>系统状态概览</h2>
    <div class="stats-container">
        <div class="stat-card">
            <h4>玩家总数</h4>
            <p><?php echo $player_count; ?></p>
        </div>
        <div class="stat-card">
            <h4>24小时活跃</h4>
            <p><?php echo $active_players_24h; ?></p>
        </div>
        <div class="stat-card">
            <h4>今日战斗</h4>
            <p><?php echo $battle_count_today; ?></p>
        </div>
        <div class="stat-card">
            <h4>今日PVP</h4>
            <p><?php echo $pvp_count_today; ?></p>
        </div>
        <div class="stat-card">
            <h4>今日交易</h4>
            <p><?php echo $trade_count_today; ?></p>
        </div>
        <div class="stat-card">
            <h4>安全违规</h4>
            <p style="color: <?php echo $security_violations_today > 0 ? '#dc3545' : '#28a745'; ?>">
                <?php echo $security_violations_today; ?>
            </p>
        </div>
    </div>

    <div class="dashboard-grid">
        <!-- 游戏内容统计 -->
        <div class="dashboard-section">
            <h3>游戏内容</h3>
            <div class="mini-stats">
                <div class="mini-stat">
                    <span class="mini-stat-label">场景</span>
                    <span class="mini-stat-value"><?php echo $scene_count; ?></span>
                </div>
                <div class="mini-stat">
                    <span class="mini-stat-label">怪物</span>
                    <span class="mini-stat-value"><?php echo $monster_count; ?></span>
                </div>
                <div class="mini-stat">
                    <span class="mini-stat-label">物品</span>
                    <span class="mini-stat-value"><?php echo $item_template_count; ?></span>
                </div>
                <div class="mini-stat">
                    <span class="mini-stat-label">仓库用户</span>
                    <span class="mini-stat-value"><?php echo $warehouse_stats['users_count']; ?></span>
                </div>
            </div>
        </div>

        <!-- 玩家等级分布 -->
        <div class="dashboard-section">
            <h3>玩家等级分布</h3>
            <div class="level-distribution">
                <div class="level-bar">
                    <span class="level-label">1-10级</span>
                    <div class="level-progress">
                        <div class="level-fill" style="width: <?php echo $level_stats['level_1_10'] > 0 ? min(100, ($level_stats['level_1_10'] / $player_count) * 100) : 0; ?>%"></div>
                    </div>
                    <span class="level-count"><?php echo $level_stats['level_1_10']; ?></span>
                </div>
                <div class="level-bar">
                    <span class="level-label">11-30级</span>
                    <div class="level-progress">
                        <div class="level-fill" style="width: <?php echo $level_stats['level_11_30'] > 0 ? min(100, ($level_stats['level_11_30'] / $player_count) * 100) : 0; ?>%"></div>
                    </div>
                    <span class="level-count"><?php echo $level_stats['level_11_30']; ?></span>
                </div>
                <div class="level-bar">
                    <span class="level-label">31-50级</span>
                    <div class="level-progress">
                        <div class="level-fill" style="width: <?php echo $level_stats['level_31_50'] > 0 ? min(100, ($level_stats['level_31_50'] / $player_count) * 100) : 0; ?>%"></div>
                    </div>
                    <span class="level-count"><?php echo $level_stats['level_31_50']; ?></span>
                </div>
                <div class="level-bar">
                    <span class="level-label">50级以上</span>
                    <div class="level-progress">
                        <div class="level-fill" style="width: <?php echo $level_stats['level_50_plus'] > 0 ? min(100, ($level_stats['level_50_plus'] / $player_count) * 100) : 0; ?>%"></div>
                    </div>
                    <span class="level-count"><?php echo $level_stats['level_50_plus']; ?></span>
                </div>
                <div class="level-summary">
                    <span>平均等级: <?php echo number_format($level_stats['avg_level'], 1); ?></span>
                    <span>最高等级: <?php echo $level_stats['max_level']; ?></span>
                </div>
            </div>
        </div>
    </div>
    
    <?php if (isset($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>

    <div class="dashboard-grid">
        <!-- 职业分布 -->
        <div class="dashboard-section">
            <h3>职业分布</h3>
            <div class="job-distribution">
                <?php foreach ($job_stats as $job): ?>
                <div class="job-item">
                    <span class="job-name"><?php echo htmlspecialchars($job['job']); ?></span>
                    <div class="job-progress">
                        <div class="job-fill" style="width: <?php echo $player_count > 0 ? min(100, ($job['count'] / $player_count) * 100) : 0; ?>%"></div>
                    </div>
                    <span class="job-count"><?php echo $job['count']; ?></span>
                </div>
                <?php endforeach; ?>
            </div>
        </div>

        <!-- 最近7天活动 -->
        <div class="dashboard-section">
            <h3>最近7天战斗活动</h3>
            <div class="activity-chart">
                <?php if (!empty($weekly_activity)): ?>
                    <?php
                    $max_battles = max(array_column($weekly_activity, 'battles'));
                    foreach ($weekly_activity as $day):
                    ?>
                    <div class="activity-day">
                        <div class="activity-bar">
                            <div class="activity-fill" style="height: <?php echo $max_battles > 0 ? ($day['battles'] / $max_battles) * 100 : 0; ?>%"></div>
                        </div>
                        <span class="activity-date"><?php echo date('m/d', strtotime($day['date'])); ?></span>
                        <span class="activity-count"><?php echo $day['battles']; ?></span>
                    </div>
                    <?php endforeach; ?>
                <?php else: ?>
                    <p class="no-data">暂无数据</p>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 快速操作区域 -->
    <div class="quick-actions">
        <h3>快速操作</h3>
        <div class="quick-actions-grid">
            <a href="player_inventory.php" class="quick-action-btn">
                <span class="quick-action-icon">🎒</span>
                <span class="quick-action-text">查看玩家背包</span>
            </a>
            <a href="battle_logs.php" class="quick-action-btn">
                <span class="quick-action-icon">⚔️</span>
                <span class="quick-action-text">最新战斗记录</span>
            </a>
            <a href="trade_logs.php" class="quick-action-btn">
                <span class="quick-action-icon">💰</span>
                <span class="quick-action-text">交易记录</span>
            </a>
            <a href="security_violations.php" class="quick-action-btn">
                <span class="quick-action-icon">🛡️</span>
                <span class="quick-action-text">安全监控</span>
            </a>
            <a href="javascript:void(0)" onclick="clearCache(event)" class="quick-action-btn">
                <span class="quick-action-icon">🧹</span>
                <span class="quick-action-text">清理缓存</span>
            </a>
            <a href="warehouse_expansion.php" class="quick-action-btn">
                <span class="quick-action-icon">📦</span>
                <span class="quick-action-text">仓库管理</span>
            </a>
        </div>
    </div>

    <h2>管理功能</h2>
    <div class="card-container">
        <!-- 世界管理 -->
        <div class="card">
            <h3>🌍 世界管理</h3>
            <p>管理大陆、区域和场景的层级结构。</p>
            <a href="continents.php" class="btn">管理世界</a>
        </div>
        <div class="card">
            <h3>🏠 场景管理</h3>
            <p>创建、编辑和管理游戏世界中的所有地点。</p>
            <a href="scenes.php" class="btn">管理场景</a>
        </div>

        <!-- 角色管理 -->
        <div class="card">
            <h3>👥 NPC管理</h3>
            <p>创建和管理游戏中的NPC角色，设置对话和任务。</p>
            <a href="npcs.php" class="btn">管理NPC</a>
        </div>
        <div class="card">
            <h3>👹 怪物管理</h3>
            <p>管理游戏中的怪物模板，定义其属性和行为。</p>
            <a href="monsters.php" class="btn">管理怪物</a>
        </div>

        <!-- 游戏内容 -->
        <div class="card">
            <h3>📋 任务系统</h3>
            <p>创建和管理游戏任务，设置奖励和目标。</p>
            <a href="quests.php" class="btn">管理任务</a>
        </div>
        <div class="card">
            <h3>💬 对话编辑</h3>
            <p>创建和编辑NPC对话树，设计对话分支和选项。</p>
            <a href="dialogues.php" class="btn">管理对话</a>
        </div>

        <!-- 物品系统 -->
        <div class="card">
            <h3>🎒 物品管理</h3>
            <p>管理所有物品模板，包括装备、药品和材料等。</p>
            <a href="items.php" class="btn">管理物品</a>
        </div>
        <div class="card">
            <h3>⚔️ 技能管理</h3>
            <p>管理游戏技能模板和效果配置。</p>
            <a href="skills.php" class="btn">管理技能</a>
        </div>

        <!-- 玩家管理 -->
        <div class="card">
            <h3>🎒 玩家背包</h3>
            <p>查看和管理特定玩家的装备和背包物品。</p>
            <a href="player_inventory.php" class="btn">管理背包</a>
        </div>
        <div class="card">
            <h3>🏪 仓库扩容</h3>
            <p>管理仓库扩容物品配置和玩家扩容记录。</p>
            <a href="warehouse_expansion.php" class="btn">仓库管理</a>
        </div>

        <!-- 日志监控 -->
        <div class="card">
            <h3>⚔️ 战斗日志</h3>
            <p>查看和分析PVE战斗记录和数据。</p>
            <a href="battle_logs.php" class="btn">战斗日志</a>
        </div>
        <div class="card">
            <h3>🥊 PVP日志</h3>
            <p>查看和分析PVP战斗记录和统计。</p>
            <a href="pvp_battle_logs.php" class="btn">PVP日志</a>
        </div>
        <div class="card">
            <h3>💰 交易日志</h3>
            <p>查看玩家间的交易记录和统计。</p>
            <a href="trade_logs.php" class="btn">交易日志</a>
        </div>
        <div class="card">
            <h3>🛡️ 安全监控</h3>
            <p>查看安全违规记录和系统监控。</p>
            <a href="security_violations.php" class="btn">安全监控</a>
        </div>

        <!-- 系统工具 -->
        <div class="card">
            <h3>🧹 缓存清理</h3>
            <p>清理系统缓存和重置服务器状态。</p>
            <a href="javascript:void(0)" onclick="clearCache(event)" class="btn">清理缓存</a>
        </div>
        <div class="card">
            <h3>🗑️ 掉落物品清理</h3>
            <p>管理和清理场景中超时的掉落物品。</p>
            <a href="scene_items_cleanup.php" class="btn">物品清理</a>
        </div>
        <div class="card">
            <h3>📄 服务器日志</h3>
            <p>实时查看和管理服务器运行日志。</p>
            <a href="server_logs.php" class="btn">查看日志</a>
        </div>
        <div class="card">
            <h3>🔧 系统工具</h3>
            <p>其他系统维护和调试工具。</p>
            <a href="debug_layers.php" class="btn">调试工具</a>
        </div>
    </div>
</div>

<script>
// 清理缓存功能
function clearCache(event) {
    if (!confirm('确定要清理系统缓存吗？这可能会影响系统性能，直到缓存重新生成。')) {
        return;
    }

    // 显示加载状态
    const btn = event.target.closest('.quick-action-btn') || event.target.closest('.btn');
    let originalText = '清理缓存';
    let textElement = null;

    if (btn.classList.contains('quick-action-btn')) {
        textElement = btn.querySelector('.quick-action-text');
        originalText = textElement.textContent;
        textElement.textContent = '清理中...';
    } else {
        originalText = btn.textContent;
        btn.textContent = '清理中...';
    }

    btn.style.pointerEvents = 'none';
    btn.style.opacity = '0.6';

    fetch('clear_cache.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'clear_all'
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showToast('缓存清理成功！', 'success');
        } else {
            showToast('缓存清理失败: ' + (data.message || '未知错误'), 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showToast('网络错误，请重试', 'error');
    })
    .finally(() => {
        // 恢复按钮状态
        if (textElement) {
            textElement.textContent = originalText;
        } else {
            btn.textContent = originalText;
        }
        btn.style.pointerEvents = '';
        btn.style.opacity = '';
    });
}

// Toast通知函数
function showToast(message, type = 'success', duration = 3000) {
    // 获取或创建Toast元素
    let toast = document.getElementById('dashboard-toast');
    if (!toast) {
        toast = document.createElement('div');
        toast.id = 'dashboard-toast';
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #333;
            color: #fff;
            padding: 12px 20px;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
            max-width: 300px;
            min-width: 200px;
            word-wrap: break-word;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 1060;
            transform: translateX(400px);
            opacity: 0;
            transition: all 0.3s ease;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        `;
        document.body.appendChild(toast);
    }

    // 设置Toast样式
    if (type === 'success') {
        toast.style.backgroundColor = '#28a745';
    } else if (type === 'error') {
        toast.style.backgroundColor = '#dc3545';
    } else if (type === 'warning') {
        toast.style.backgroundColor = '#ffc107';
        toast.style.color = '#212529';
    }

    // 设置消息内容
    toast.textContent = message;

    // 清除之前的定时器
    if (toast.hideTimer) {
        clearTimeout(toast.hideTimer);
    }

    // 显示Toast
    toast.style.transform = 'translateX(0)';
    toast.style.opacity = '1';

    // 设置自动隐藏
    toast.hideTimer = setTimeout(() => {
        toast.style.transform = 'translateX(400px)';
        toast.style.opacity = '0';
        toast.hideTimer = null;
    }, duration);
}
</script>

<?php require_once 'layout_footer.php'; ?>