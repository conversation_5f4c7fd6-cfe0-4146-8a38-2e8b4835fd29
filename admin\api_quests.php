<?php
// admin/api_quests.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 初始化响应数组
$response = [
    'success' => false,
    'message' => '未知错误',
    'data' => null
];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 检查请求方法
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        // 处理GET请求
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'get_all_quests':
                // 获取所有任务
                $stmt = $pdo->query("
                    SELECT q.*, 
                           giver.name AS giver_name,
                           receiver.name AS receiver_name
                    FROM quests q
                    LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
                    LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
                    ORDER BY q.min_level, q.title
                ");
                $quests = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                $response = [
                    'success' => true,
                    'message' => '获取任务列表成功',
                    'data' => $quests
                ];
                break;
                
            case 'get_quest':
                // 获取单个任务详情
                if (!isset($_GET['id'])) {
                    $response['message'] = '缺少任务ID参数';
                    break;
                }
                
                $quest_id = (int)$_GET['id'];
                
                // 获取任务基本信息
                $stmt = $pdo->prepare("
                    SELECT q.*, 
                           giver.name AS giver_name,
                           receiver.name AS receiver_name
                    FROM quests q
                    LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
                    LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
                    WHERE q.id = :id
                ");
                $stmt->execute([':id' => $quest_id]);
                $quest = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$quest) {
                    $response['message'] = '找不到指定的任务';
                    break;
                }
                
                // 获取任务目标
                $stmt = $pdo->prepare("
                    SELECT qo.*,
                           CASE 
                               WHEN qo.type = 'dialogue' OR qo.type = 'visit' THEN npc.name
                               WHEN qo.type = 'kill' THEN monster.name
                               WHEN qo.type = 'collect' THEN item.name
                               ELSE NULL
                           END AS target_name
                    FROM quest_objectives qo
                    LEFT JOIN npc_templates npc ON qo.type IN ('dialogue', 'visit') AND qo.target_id = npc.id
                    LEFT JOIN monster_templates monster ON qo.type = 'kill' AND qo.target_id = monster.id
                    LEFT JOIN item_templates item ON qo.type = 'collect' AND qo.target_id = item.id
                    WHERE qo.quest_id = :quest_id
                    ORDER BY qo.id
                ");
                $stmt->execute([':quest_id' => $quest_id]);
                $objectives = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 将目标添加到任务数据中
                $quest['objectives'] = $objectives;
                
                $response = [
                    'success' => true,
                    'message' => '获取任务详情成功',
                    'data' => $quest
                ];
                break;
                
            default:
                $response['message'] = '未知的GET操作';
                break;
        }
    } elseif ($_SERVER['REQUEST_METHOD'] === 'POST') {
        // 处理POST请求
        $input = null;
        $action = '';

        // 检查是否是JSON数据
        $contentType = $_SERVER['CONTENT_TYPE'] ?? '';
        if (strpos($contentType, 'application/json') !== false) {
            $input = json_decode(file_get_contents('php://input'), true);
            $action = $input['action'] ?? '';
        } else {
            $action = $_POST['action'] ?? '';
        }

        switch ($action) {
            case 'create_quest':
                // 创建新任务
                $title = $_POST['title'] ?? '';
                $description = $_POST['description'] ?? '';
                $type = $_POST['type'] ?? 'dialogue';
                $giver_npc_id = isset($_POST['giver_npc_id']) ? (int)$_POST['giver_npc_id'] : null;
                $receiver_npc_id = !empty($_POST['receiver_npc_id']) ? (int)$_POST['receiver_npc_id'] : null;
                $min_level = isset($_POST['min_level']) ? (int)$_POST['min_level'] : 1;
                $is_repeatable = isset($_POST['is_repeatable']) ? 1 : 0;
                
                // 验证必填字段
                if (empty($title) || empty($giver_npc_id)) {
                    $response['message'] = '任务名称和发布NPC是必填项';
                    break;
                }
                
                // 插入任务
                $stmt = $pdo->prepare("
                    INSERT INTO quests 
                    (title, description, type, giver_npc_id, receiver_npc_id, min_level, is_repeatable)
                    VALUES 
                    (:title, :description, :type, :giver_npc_id, :receiver_npc_id, :min_level, :is_repeatable)
                ");
                
                $stmt->execute([
                    ':title' => $title,
                    ':description' => $description,
                    ':type' => $type,
                    ':giver_npc_id' => $giver_npc_id,
                    ':receiver_npc_id' => $receiver_npc_id,
                    ':min_level' => $min_level,
                    ':is_repeatable' => $is_repeatable
                ]);
                
                $quest_id = $pdo->lastInsertId();
                
                $response = [
                    'success' => true,
                    'message' => '任务创建成功',
                    'data' => ['id' => $quest_id]
                ];
                break;
                
            case 'update_quest':
                // 更新任务
                $data = $input ?: $_POST;

                if (!isset($data['quest_id']) && !isset($data['id'])) {
                    $response['message'] = '缺少任务ID参数';
                    break;
                }

                $quest_id = (int)($data['quest_id'] ?? $data['id']);
                $title = $data['title'] ?? '';
                $description = $data['description'] ?? '';
                $type = $data['type'] ?? 'dialogue';
                $group_id = !empty($data['group_id']) ? (int)$data['group_id'] : null;
                $giver_npc_id = isset($data['giver_npc_id']) ? (int)$data['giver_npc_id'] : null;
                $receiver_npc_id = !empty($data['receiver_npc_id']) ? (int)$data['receiver_npc_id'] : null;
                $min_level = isset($data['min_level']) ? (int)$data['min_level'] : 1;
                $is_repeatable = isset($data['is_repeatable']) ? (int)$data['is_repeatable'] : 0;
                $reward_gold = isset($data['reward_gold']) ? (int)$data['reward_gold'] : 0;
                $reward_exp = isset($data['reward_exp']) ? (int)$data['reward_exp'] : 0;
                $reward_items = isset($data['reward_items']) ? $data['reward_items'] : null;
                $prerequisite_quests = isset($data['prerequisite_quests']) ? $data['prerequisite_quests'] : null;
                
                // 验证必填字段
                if (empty($title) || empty($giver_npc_id)) {
                    $response['message'] = '任务名称和发布NPC是必填项';
                    break;
                }
                
                // 更新任务
                $stmt = $pdo->prepare("
                    UPDATE quests
                    SET title = :title,
                        description = :description,
                        type = :type,
                        group_id = :group_id,
                        giver_npc_id = :giver_npc_id,
                        receiver_npc_id = :receiver_npc_id,
                        min_level = :min_level,
                        is_repeatable = :is_repeatable,
                        reward_gold = :reward_gold,
                        reward_exp = :reward_exp,
                        reward_items = :reward_items,
                        prerequisite_quests = :prerequisite_quests,
                        updated_at = NOW()
                    WHERE id = :id
                ");
                
                $stmt->execute([
                    ':title' => $title,
                    ':description' => $description,
                    ':type' => $type,
                    ':group_id' => $group_id,
                    ':giver_npc_id' => $giver_npc_id,
                    ':receiver_npc_id' => $receiver_npc_id,
                    ':min_level' => $min_level,
                    ':is_repeatable' => $is_repeatable,
                    ':reward_gold' => $reward_gold,
                    ':reward_exp' => $reward_exp,
                    ':reward_items' => $reward_items,
                    ':prerequisite_quests' => $prerequisite_quests,
                    ':id' => $quest_id
                ]);
                
                $response = [
                    'success' => true,
                    'message' => '任务更新成功',
                    'data' => ['id' => $quest_id]
                ];
                break;

            case 'update_quest_simple':
                // 简单更新任务 - 只更新表单中提供的字段
                $data = $input ?: $_POST;

                if (!isset($data['quest_id']) && !isset($data['id'])) {
                    $response['message'] = '缺少任务ID参数';
                    break;
                }

                $quest_id = (int)($data['quest_id'] ?? $data['id']);

                // 构建动态更新语句，只更新提供的字段
                $updateFields = [];
                $updateParams = [':id' => $quest_id];

                // 检查并添加需要更新的字段
                if (isset($data['title']) && !empty($data['title'])) {
                    $updateFields[] = 'title = :title';
                    $updateParams[':title'] = $data['title'];
                }

                if (isset($data['description'])) {
                    $updateFields[] = 'description = :description';
                    $updateParams[':description'] = $data['description'];
                }

                if (isset($data['type'])) {
                    $updateFields[] = 'type = :type';
                    $updateParams[':type'] = $data['type'];
                }

                if (isset($data['group_id'])) {
                    $updateFields[] = 'group_id = :group_id';
                    $updateParams[':group_id'] = !empty($data['group_id']) ? (int)$data['group_id'] : null;
                }

                if (isset($data['giver_npc_id'])) {
                    $updateFields[] = 'giver_npc_id = :giver_npc_id';
                    $updateParams[':giver_npc_id'] = !empty($data['giver_npc_id']) ? (int)$data['giver_npc_id'] : null;
                }

                if (isset($data['receiver_npc_id'])) {
                    $updateFields[] = 'receiver_npc_id = :receiver_npc_id';
                    $updateParams[':receiver_npc_id'] = !empty($data['receiver_npc_id']) ? (int)$data['receiver_npc_id'] : null;
                }

                if (isset($data['min_level'])) {
                    $updateFields[] = 'min_level = :min_level';
                    $updateParams[':min_level'] = (int)$data['min_level'];
                }

                if (isset($data['is_repeatable'])) {
                    $updateFields[] = 'is_repeatable = :is_repeatable';
                    $updateParams[':is_repeatable'] = (int)$data['is_repeatable'];
                }

                if (isset($data['reward_gold'])) {
                    $updateFields[] = 'reward_gold = :reward_gold';
                    $updateParams[':reward_gold'] = (int)$data['reward_gold'];
                }

                if (isset($data['reward_exp'])) {
                    $updateFields[] = 'reward_exp = :reward_exp';
                    $updateParams[':reward_exp'] = (int)$data['reward_exp'];
                }

                if (isset($data['prerequisite_quests'])) {
                    $updateFields[] = 'prerequisite_quests = :prerequisite_quests';
                    $updateParams[':prerequisite_quests'] = $data['prerequisite_quests'];
                }

                // 如果没有要更新的字段，返回错误
                if (empty($updateFields)) {
                    $response['message'] = '没有提供要更新的字段';
                    break;
                }

                // 添加更新时间
                $updateFields[] = 'updated_at = NOW()';

                // 构建并执行更新语句
                $sql = "UPDATE quests SET " . implode(', ', $updateFields) . " WHERE id = :id";
                $stmt = $pdo->prepare($sql);
                $stmt->execute($updateParams);

                $response = [
                    'success' => true,
                    'message' => '任务更新成功',
                    'data' => ['id' => $quest_id, 'updated_fields' => array_keys($updateParams)]
                ];
                break;

            case 'delete_quest':
                // 删除任务
                if (!isset($_POST['quest_id'])) {
                    $response['message'] = '缺少任务ID参数';
                    break;
                }
                
                $quest_id = (int)$_POST['quest_id'];
                
                // 开始事务
                $pdo->beginTransaction();
                
                try {
                    // 先删除任务目标
                    $stmt = $pdo->prepare("DELETE FROM quest_objectives WHERE quest_id = :quest_id");
                    $stmt->execute([':quest_id' => $quest_id]);
                    
                    // 删除玩家任务进度
                    $stmt = $pdo->prepare("DELETE FROM player_quests WHERE quest_id = :quest_id");
                    $stmt->execute([':quest_id' => $quest_id]);
                    
                    // 最后删除任务
                    $stmt = $pdo->prepare("DELETE FROM quests WHERE id = :id");
                    $stmt->execute([':id' => $quest_id]);
                    
                    // 提交事务
                    $pdo->commit();
                    
                    $response = [
                        'success' => true,
                        'message' => '任务删除成功',
                        'data' => ['id' => $quest_id]
                    ];
                } catch (Exception $e) {
                    // 回滚事务
                    $pdo->rollBack();
                    throw $e;
                }
                break;
                
            case 'add_objective':
                // 添加任务目标
                if (!isset($_POST['quest_id'])) {
                    $response['message'] = '缺少任务ID参数';
                    break;
                }
                
                $quest_id = (int)$_POST['quest_id'];
                $objective_type = $_POST['objective_type'] ?? '';
                $target_id = isset($_POST['target_id']) ? (int)$_POST['target_id'] : null;
                $quantity = isset($_POST['quantity']) ? (int)$_POST['quantity'] : 1;
                $description = $_POST['objective_description'] ?? '';
                
                // 验证必填字段
                if (empty($objective_type) || empty($target_id)) {
                    $response['message'] = '目标类型和目标ID是必填项';
                    break;
                }
                
                // 插入任务目标
                $stmt = $pdo->prepare("
                    INSERT INTO quest_objectives 
                    (quest_id, type, target_id, quantity, description)
                    VALUES 
                    (:quest_id, :type, :target_id, :quantity, :description)
                ");
                
                $stmt->execute([
                    ':quest_id' => $quest_id,
                    ':type' => $objective_type,
                    ':target_id' => $target_id,
                    ':quantity' => $quantity,
                    ':description' => $description
                ]);
                
                $objective_id = $pdo->lastInsertId();
                
                $response = [
                    'success' => true,
                    'message' => '任务目标添加成功',
                    'data' => ['id' => $objective_id]
                ];
                break;
                
            case 'delete_objective':
                // 删除任务目标
                if (!isset($_POST['objective_id'])) {
                    $response['message'] = '缺少目标ID参数';
                    break;
                }
                
                $objective_id = (int)$_POST['objective_id'];
                
                // 删除任务目标
                $stmt = $pdo->prepare("DELETE FROM quest_objectives WHERE id = :id");
                $stmt->execute([':id' => $objective_id]);
                
                $response = [
                    'success' => true,
                    'message' => '任务目标删除成功',
                    'data' => ['id' => $objective_id]
                ];
                break;
                
            default:
                $response['message'] = '未知的POST操作';
                break;
        }
    } else {
        $response['message'] = '不支持的请求方法';
    }
} catch (PDOException $e) {
    $response = [
        'success' => false,
        'message' => '数据库错误: ' . $e->getMessage(),
        'data' => null
    ];
} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => '系统错误: ' . $e->getMessage(),
        'data' => null
    ];
}

// 返回JSON响应
header('Content-Type: application/json');
echo json_encode($response);
exit; 