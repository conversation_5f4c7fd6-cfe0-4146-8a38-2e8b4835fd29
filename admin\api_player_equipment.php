<?php
// admin/api_player_equipment.php
header('Content-Type: application/json; charset=utf-8');
require_once '../config/Database.php';
require_once 'auth.php';

// 检查管理员权限
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['error' => '未授权访问'], JSON_UNESCAPED_UNICODE);
    exit;
}

$db = Database::getInstance();
$conn = $db->getConnection();

$action = $_GET['action'] ?? '';

try {
    switch ($action) {
        case 'get_equipment_detail':
            getEquipmentDetail();
            break;
        case 'update_equipment':
            updateEquipment();
            break;
        case 'delete_equipment':
            deleteEquipment();
            break;
        case 'export_equipment_data':
            exportEquipmentData();
            break;
        default:
            throw new Exception('无效的操作');
    }
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()], JSON_UNESCAPED_UNICODE);
}

/**
 * 获取装备详细信息
 */
function getEquipmentDetail() {
    global $conn;
    
    $inventoryId = $_GET['inventory_id'] ?? '';
    if (empty($inventoryId)) {
        throw new Exception('缺少装备ID');
    }
    
    $query = "
        SELECT 
            pi.id as inventory_id,
            pi.player_id,
            pi.item_template_id,
            pi.quantity,
            pi.is_equipped,
            pi.instance_data,
            pi.is_bound,
            it.name as item_name,
            it.description as item_description,
            it.item_id,
            ed.slot,
            ed.stats as base_stats,
            ed.sockets,
            ed.job_restriction,
            ed.grants_job_id,
            a.username,
            pa.level,
            pa.current_scene_id,
            pa.strength,
            pa.agility,
            pa.constitution,
            pa.intelligence
        FROM player_inventory pi
        JOIN item_templates it ON pi.item_template_id = it.id
        JOIN equipment_details ed ON it.id = ed.item_template_id
        JOIN accounts a ON pi.player_id = a.id
        LEFT JOIN player_attributes pa ON a.id = pa.account_id
        WHERE pi.id = ? AND it.category = 'Equipment'
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute([$inventoryId]);
    $equipment = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$equipment) {
        throw new Exception('装备不存在');
    }
    
    // 解析实例数据
    $instanceData = json_decode($equipment['instance_data'], true) ?: [];
    $equipment['parsed_instance_data'] = $instanceData;
    
    // 解析基础属性
    $baseStats = json_decode($equipment['base_stats'], true) ?: [];
    $equipment['parsed_base_stats'] = $baseStats;
    
    echo json_encode($equipment, JSON_UNESCAPED_UNICODE);
}

/**
 * 更新装备信息
 */
function updateEquipment() {
    global $conn;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法错误');
    }
    
    $inventoryId = $_POST['inventory_id'] ?? '';
    $isBound = isset($_POST['is_bound']) ? intval($_POST['is_bound']) : null;
    $isEquipped = isset($_POST['is_equipped']) ? intval($_POST['is_equipped']) : null;
    $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : null;
    
    if (empty($inventoryId)) {
        throw new Exception('缺少装备ID');
    }
    
    $conn->beginTransaction();
    
    try {
        // 构建更新语句
        $updateFields = [];
        $params = [];
        
        if ($isBound !== null) {
            $updateFields[] = 'is_bound = ?';
            $params[] = $isBound;
        }
        
        if ($isEquipped !== null) {
            $updateFields[] = 'is_equipped = ?';
            $params[] = $isEquipped;
        }
        
        if ($quantity !== null && $quantity > 0) {
            $updateFields[] = 'quantity = ?';
            $params[] = $quantity;
        }
        
        if (empty($updateFields)) {
            throw new Exception('没有要更新的字段');
        }
        
        $params[] = $inventoryId;
        
        $updateQuery = "UPDATE player_inventory SET " . implode(', ', $updateFields) . " WHERE id = ?";
        $stmt = $conn->prepare($updateQuery);
        $stmt->execute($params);
        
        if ($stmt->rowCount() === 0) {
            throw new Exception('装备不存在或更新失败');
        }
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => '装备更新成功'], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * 删除装备
 */
function deleteEquipment() {
    global $conn;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        throw new Exception('请求方法错误');
    }
    
    $inventoryId = $_POST['inventory_id'] ?? '';
    if (empty($inventoryId)) {
        throw new Exception('缺少装备ID');
    }
    
    $conn->beginTransaction();
    
    try {
        // 检查装备是否存在
        $checkStmt = $conn->prepare("SELECT id, is_equipped FROM player_inventory WHERE id = ?");
        $checkStmt->execute([$inventoryId]);
        $equipment = $checkStmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$equipment) {
            throw new Exception('装备不存在');
        }
        
        if ($equipment['is_equipped']) {
            throw new Exception('无法删除已装备的物品，请先卸下装备');
        }
        
        // 删除装备
        $deleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
        $deleteStmt->execute([$inventoryId]);
        
        $conn->commit();
        echo json_encode(['success' => true, 'message' => '装备删除成功'], JSON_UNESCAPED_UNICODE);
        
    } catch (Exception $e) {
        $conn->rollBack();
        throw $e;
    }
}

/**
 * 导出装备数据
 */
function exportEquipmentData() {
    global $conn;
    
    $format = $_GET['format'] ?? 'json';
    $playerId = $_GET['player_id'] ?? '';
    
    $whereClause = '';
    $params = [];
    
    if (!empty($playerId)) {
        $whereClause = 'AND pi.player_id = ?';
        $params[] = $playerId;
    }
    
    $query = "
        SELECT 
            pi.id as inventory_id,
            pi.player_id,
            pi.item_template_id,
            pi.quantity,
            pi.is_equipped,
            pi.instance_data,
            pi.is_bound,
            it.name as item_name,
            it.item_id,
            ed.slot,
            ed.stats as base_stats,
            ed.sockets,
            a.username,
            pa.level
        FROM player_inventory pi
        JOIN item_templates it ON pi.item_template_id = it.id
        JOIN equipment_details ed ON it.id = ed.item_template_id
        JOIN accounts a ON pi.player_id = a.id
        LEFT JOIN player_attributes pa ON a.id = pa.account_id
        WHERE it.category = 'Equipment'
        {$whereClause}
        ORDER BY pi.player_id, ed.slot
    ";
    
    $stmt = $conn->prepare($query);
    $stmt->execute($params);
    $equipmentData = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理实例数据
    foreach ($equipmentData as &$equipment) {
        $instanceData = json_decode($equipment['instance_data'], true) ?: [];
        $equipment['parsed_instance_data'] = $instanceData;
        $equipment['parsed_base_stats'] = json_decode($equipment['base_stats'], true) ?: [];
    }
    
    if ($format === 'csv') {
        exportAsCSV($equipmentData);
    } else {
        header('Content-Type: application/json; charset=utf-8');
        header('Content-Disposition: attachment; filename="equipment_data_' . date('Y-m-d_H-i-s') . '.json"');
        echo json_encode($equipmentData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT);
    }
}

/**
 * 导出为CSV格式
 */
function exportAsCSV($data) {
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="equipment_data_' . date('Y-m-d_H-i-s') . '.csv"');
    
    $output = fopen('php://output', 'w');
    
    // 输出BOM以支持中文
    fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));
    
    // CSV头部
    $headers = [
        '装备ID', '玩家ID', '用户名', '装备名称', '部位', '数量',
        '是否装备', '是否绑定', '插槽数', '是否凝练', '凝练值', '凝练档位'
    ];
    fputcsv($output, $headers);
    
    // 数据行
    foreach ($data as $equipment) {
        $instanceData = $equipment['parsed_instance_data'];
        $row = [
            $equipment['inventory_id'],
            $equipment['player_id'],
            $equipment['username'],
            $equipment['item_name'],
            $equipment['slot'],
            $equipment['quantity'],
            $equipment['is_equipped'] ? '是' : '否',
            $equipment['is_bound'] ? '是' : '否',
            $equipment['sockets'],
            isset($instanceData['refined']) && $instanceData['refined'] ? '是' : '否',
            $instanceData['refine_value'] ?? '',
            $instanceData['refine_tier'] ?? ''
        ];
        fputcsv($output, $row);
    }
    
    fclose($output);
}
?>
