<?php
/**
 * 数值平衡分析器
 * 分析游戏中的实际数据，提供平衡性建议
 */

class BalanceAnalyzer {
    private $db;
    
    public function __construct($db) {
        $this->db = $db;
    }
    
    /**
     * 分析玩家等级分布
     */
    public function analyzePlayerLevels() {
        $stmt = $this->db->query("
            SELECT 
                CASE 
                    WHEN level BETWEEN 1 AND 10 THEN '1-10'
                    WHEN level BETWEEN 11 AND 20 THEN '11-20'
                    WHEN level BETWEEN 21 AND 30 THEN '21-30'
                    WHEN level BETWEEN 31 AND 40 THEN '31-40'
                    WHEN level BETWEEN 41 AND 50 THEN '41-50'
                    ELSE '51+'
                END as level_range,
                COUNT(*) as player_count,
                AVG(level) as avg_level
            FROM player_attributes 
            GROUP BY level_range
            ORDER BY MIN(level)
        ");
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 分析装备掉落统计
     */
    public function analyzeEquipmentDrops($days = 7) {
        $stmt = $this->db->query("
            SELECT 
                it.category,
                it.equipment_type,
                COUNT(*) as drop_count,
                COUNT(DISTINCT si.scene_id) as scenes_count,
                AVG(si.quantity) as avg_quantity
            FROM scene_items si
            JOIN item_templates it ON si.item_template_id = it.id
            WHERE si.dropped_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
                AND it.category = 'Equipment'
            GROUP BY it.category, it.equipment_type
            ORDER BY drop_count DESC
        ", [$days]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 分析战斗平衡性
     */
    public function analyzeCombatBalance($days = 7) {
        // 这里需要战斗日志表，暂时返回模拟数据
        return [
            'player_win_rate' => 0.75,
            'average_battle_duration' => 45.2,
            'most_used_skills' => [
                '普通攻击' => 45.2,
                '重击' => 23.1,
                '治疗' => 15.7
            ],
            'damage_distribution' => [
                'physical' => 60.5,
                'fire' => 15.2,
                'ice' => 12.3,
                'wind' => 8.1,
                'electric' => 3.9
            ]
        ];
    }
    
    /**
     * 分析经济平衡
     */
    public function analyzeEconomy() {
        // 分析玩家金币分布
        $stmt = $this->db->query("
            SELECT 
                CASE 
                    WHEN gold < 1000 THEN '0-1K'
                    WHEN gold < 10000 THEN '1K-10K'
                    WHEN gold < 100000 THEN '10K-100K'
                    WHEN gold < 1000000 THEN '100K-1M'
                    ELSE '1M+'
                END as gold_range,
                COUNT(*) as player_count,
                AVG(gold) as avg_gold
            FROM player_attributes 
            GROUP BY gold_range
            ORDER BY MIN(gold)
        ");
        
        $goldDistribution = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 分析物品价格
        $stmt = $this->db->query("
            SELECT 
                category,
                AVG(buy_price) as avg_buy_price,
                AVG(sell_price) as avg_sell_price,
                COUNT(*) as item_count
            FROM item_templates 
            WHERE buy_price > 0
            GROUP BY category
            ORDER BY avg_buy_price DESC
        ");
        
        $priceAnalysis = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        return [
            'gold_distribution' => $goldDistribution,
            'price_analysis' => $priceAnalysis
        ];
    }
    
    /**
     * 检测数值异常
     */
    public function detectAnomalies() {
        $anomalies = [];
        
        // 检测等级异常玩家
        $stmt = $this->db->query("
            SELECT id, username, level, experience 
            FROM accounts a
            JOIN player_attributes pa ON a.id = pa.player_id
            WHERE level > 50 OR experience > 1000000
            ORDER BY level DESC, experience DESC
            LIMIT 10
        ");
        
        $highLevelPlayers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($highLevelPlayers)) {
            $anomalies['high_level_players'] = $highLevelPlayers;
        }
        
        // 检测金币异常
        $stmt = $this->db->query("
            SELECT id, username, gold 
            FROM accounts a
            JOIN player_attributes pa ON a.id = pa.player_id
            WHERE gold > 1000000
            ORDER BY gold DESC
            LIMIT 10
        ");
        
        $richPlayers = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($richPlayers)) {
            $anomalies['rich_players'] = $richPlayers;
        }
        
        // 检测装备数量异常
        $stmt = $this->db->query("
            SELECT player_id, COUNT(*) as equipment_count
            FROM player_inventory pi
            JOIN item_templates it ON pi.item_template_id = it.id
            WHERE it.category = 'Equipment'
            GROUP BY player_id
            HAVING equipment_count > 100
            ORDER BY equipment_count DESC
            LIMIT 10
        ");
        
        $equipmentHoarders = $stmt->fetchAll(PDO::FETCH_ASSOC);
        if (!empty($equipmentHoarders)) {
            $anomalies['equipment_hoarders'] = $equipmentHoarders;
        }
        
        return $anomalies;
    }
    
    /**
     * 生成平衡性报告
     */
    public function generateBalanceReport() {
        return [
            'player_levels' => $this->analyzePlayerLevels(),
            'equipment_drops' => $this->analyzeEquipmentDrops(),
            'combat_balance' => $this->analyzeCombatBalance(),
            'economy' => $this->analyzeEconomy(),
            'anomalies' => $this->detectAnomalies(),
            'generated_at' => date('Y-m-d H:i:s')
        ];
    }
    
    /**
     * 提供平衡性建议
     */
    public function getBalanceSuggestions($report) {
        $suggestions = [];
        
        // 基于玩家等级分布的建议
        $levelData = $report['player_levels'];
        $lowLevelCount = 0;
        $highLevelCount = 0;
        
        foreach ($levelData as $range) {
            if (in_array($range['level_range'], ['1-10', '11-20'])) {
                $lowLevelCount += $range['player_count'];
            } elseif (in_array($range['level_range'], ['41-50', '51+'])) {
                $highLevelCount += $range['player_count'];
            }
        }
        
        if ($lowLevelCount > $highLevelCount * 5) {
            $suggestions[] = [
                'type' => 'level_progression',
                'priority' => 'high',
                'message' => '大量玩家停留在低等级，建议优化升级体验或增加中级内容'
            ];
        }
        
        // 基于经济数据的建议
        $economy = $report['economy'];
        $goldData = $economy['gold_distribution'];
        
        $poorPlayers = 0;
        $richPlayers = 0;
        
        foreach ($goldData as $range) {
            if ($range['gold_range'] === '0-1K') {
                $poorPlayers = $range['player_count'];
            } elseif (in_array($range['gold_range'], ['100K-1M', '1M+'])) {
                $richPlayers += $range['player_count'];
            }
        }
        
        if ($poorPlayers > $richPlayers * 10) {
            $suggestions[] = [
                'type' => 'economy',
                'priority' => 'medium',
                'message' => '经济分化严重，建议增加金币获取途径或调整物品价格'
            ];
        }
        
        // 基于异常数据的建议
        $anomalies = $report['anomalies'];
        
        if (!empty($anomalies['high_level_players'])) {
            $suggestions[] = [
                'type' => 'progression',
                'priority' => 'low',
                'message' => '发现高等级玩家，建议增加高级内容或检查经验获取是否合理'
            ];
        }
        
        if (!empty($anomalies['rich_players'])) {
            $suggestions[] = [
                'type' => 'economy',
                'priority' => 'medium',
                'message' => '发现金币异常玩家，建议检查金币获取机制或增加金币消耗途径'
            ];
        }
        
        return $suggestions;
    }
}
