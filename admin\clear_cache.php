<?php
// admin/clear_cache.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';
require_once '../config/RedisManager.php'; // 引入Redis管理器


// 初始化响应数组
$response = [
    'success' => false,
    'message' => ''
];

try {

    
    // 尝试使用Redis直接设置缓存标志
    $success = false;
    $error = '';
    
    // 后台管理脚本在标准的Web服务器环境（如Nginx+PHP-FPM）下运行，
    // 而非Swoole的协程环境，因此不能使用为协程设计的Redis连接池。
    // 这里我们使用标准的phpredis扩展直接发送通知。
    if (!class_exists('Redis')) {
        error_log("phpredis extension is not installed. Cannot send NPC update notification from admin panel.");
        return;
    }

    try {
        $redis = new Redis();
        // 使用与您现有Swoole Redis池相同的连接信息
        $redis->connect('127.0.0.1', 6379, 1.0); // 1秒超时
        // 如果您的Redis有密码，请在此处填写
        // $redis->auth('');
        
        $redis->publish('game-system-notifications', 'npcs_updated');
        $redis->publish('game-system-notifications', 'quests_updated');

        $redis->close();

    } catch (Exception $e) {
        // 在生产环境中，最好只记录日志，不应中断用户操作
        error_log("Redis publish failed in scene_npcs.php (using phpredis): " . $e->getMessage());
    }
    
    if ($success) {
        $response['success'] = true;
        $response['message'] = '缓存已成功清除！服务器缓存也已更新。';
    } else {
        // 如果Redis方法失败，尝试使用命令行工具
        $output = [];
        $return_var = 0;
        
        if ($return_var === 0) {
            // 命令执行成功
            $response['success'] = true;
            $response['message'] = '缓存已成功清除！服务器缓存也已更新。';
        } else {
            // 命令执行失败，但本地缓存已清除
            $response['success'] = true;
            $response['message'] = '本地缓存已清除，但服务器缓存清理失败: ' . $error;
            
            // 记录详细错误到日志
            error_log('服务器缓存清理失败: ' . $error . ', 命令输出: ' . implode("\n", $output));
        }
    }
} catch (Exception $e) {
    $response['message'] = '清除缓存失败: ' . $e->getMessage();
}

// 返回JSON响应
header('Content-Type: application/json');
echo json_encode($response);
exit; 