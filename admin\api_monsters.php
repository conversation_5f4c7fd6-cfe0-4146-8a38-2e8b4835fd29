<?php
session_start();
header('Content-Type: application/json');

// 详细的调试日志函数
function debugLog($message) {
    error_log('[Monster API Debug] ' . $message);
}

// 记录所有传入的 POST 数据
debugLog('全部 POST 数据: ' . print_r($_POST, true));
debugLog('全部 GET 数据: ' . print_r($_GET, true));
debugLog('Action: ' . ($_POST['action'] ?? $_GET['action'] ?? 'N/A'));

// 安全检查
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    debugLog('未授权访问');
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';

// 修改为使用 getConnection()
$db = Database::getInstance()->getConnection();

// 尝试从 POST 和 GET 中获取 action
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$response = ['success' => false, 'message' => '无效的操作'];

// 记录动作
debugLog('处理动作: ' . $action);

try {
    switch ($action) {
        case 'get':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            debugLog('获取怪物 ID: ' . $id);
            if (!$id) {
                throw new Exception("无效的ID");
            }
            $stmt = $db->prepare("SELECT * FROM monster_templates WHERE id = ?");
            $stmt->execute([$id]);
            $monster = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if (!$monster) {
                throw new Exception("找不到ID为 {$id} 的怪物");
            }

            // Map db columns to easier-to-use form names for frontend consistency
            $monster['hp'] = $monster['base_hp'] ?? null;
            $monster['max_hp'] = $monster['base_max_hp'] ?? null;
            $monster['attack'] = $monster['base_attack'] ?? null;

            // 获取装备
            $equipStmt = $db->prepare("SELECT item_template_id FROM monster_equipment WHERE monster_template_id = ?");
            $equipStmt->execute([$id]);
            $monster['equipment'] = $equipStmt->fetchAll(PDO::FETCH_COLUMN);
            $equipStmt->closeCursor();

            // 获取技能
            $skillStmt = $db->prepare("SELECT skill_template_id, cast_chance FROM monster_skills WHERE monster_template_id = ?");
            $skillStmt->execute([$id]);
            $monster['skills'] = $skillStmt->fetchAll(PDO::FETCH_ASSOC);
            $skillStmt->closeCursor();

            $response = ['success' => true, 'data' => $monster];
            break;

        case 'create':
        case 'update':
            $data = $_POST;
            $isCreate = ($action === 'create');
            
            if (!$isCreate) {
                $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
                if (!$id) throw new Exception("更新操作需要一个有效的怪物ID");
            }

            $params = [
                ':name' => $data['name'] ?? '新怪物',
                ':description' => $data['description'] ?? '',
                ':level' => $data['level'] ?? 1,
                ':strength' => $data['strength'] ?? 10,
                ':agility' => $data['agility'] ?? 10,
                ':constitution' => $data['constitution'] ?? 10,
                ':intelligence' => $data['intelligence'] ?? 10,
                ':hp' => $data['hp'] ?? 100,
                ':max_hp' => $data['max_hp'] ?? 100,
                ':attack' => $data['attack'] ?? 10,
                ':defense' => $data['defense'] ?? 0,
                ':attack_speed' => $data['attack_speed'] ?? 1.0,
                ':experience_reward' => $data['experience_reward'] ?? 0,
                ':loot_table_id' => empty($data['loot_table_id']) ? null : $data['loot_table_id'],
                ':fire_resistance' => $data['fire_resistance'] ?? 0,
                ':ice_resistance' => $data['ice_resistance'] ?? 0,
                ':wind_resistance' => $data['wind_resistance'] ?? 0,
                ':electric_resistance' => $data['electric_resistance'] ?? 0,
                ':group_id' => empty($data['group_id']) ? null : $data['group_id']
            ];

            if ($isCreate) {
                $sql = "INSERT INTO monster_templates (name, group_id, description, level, strength, agility, constitution, intelligence, base_hp, base_max_hp, base_attack, defense, attack_speed, experience_reward, loot_table_id, fire_resistance, ice_resistance, wind_resistance, electric_resistance) 
                        VALUES (:name, :group_id, :description, :level, :strength, :agility, :constitution, :intelligence, :hp, :max_hp, :attack, :defense, :attack_speed, :experience_reward, :loot_table_id, :fire_resistance, :ice_resistance, :wind_resistance, :electric_resistance)";
            } else {
                $params[':id'] = $id;
                $sql = "UPDATE monster_templates SET 
                            name = :name, group_id = :group_id, description = :description, level = :level, 
                            strength = :strength, agility = :agility, constitution = :constitution, intelligence = :intelligence,
                            base_hp = :hp, base_max_hp = :max_hp, 
                            base_attack = :attack, defense = :defense, attack_speed = :attack_speed, experience_reward = :experience_reward, 
                            loot_table_id = :loot_table_id, fire_resistance = :fire_resistance, ice_resistance = :ice_resistance, 
                            wind_resistance = :wind_resistance, electric_resistance = :electric_resistance
                        WHERE id = :id";
            }
            
            // --- 开始统一事务处理 ---
            $db->beginTransaction();
            try {
                // 1. 创建或更新怪物本身
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $monsterId = $isCreate ? $db->lastInsertId() : $id;

                // 2. 处理装备
                // 删除旧装备
                $deleteStmt = $db->prepare("DELETE FROM monster_equipment WHERE monster_template_id = ?");
                $deleteStmt->execute([$monsterId]);
                
                // 插入新装备
                $equipment = $data['equipment'] ?? [];
                if (!empty($equipment) && is_array($equipment)) {
                    $insertStmt = $db->prepare("INSERT INTO monster_equipment (monster_template_id, item_template_id) VALUES (?, ?)");
                    foreach ($equipment as $itemId) {
                        if (filter_var($itemId, FILTER_VALIDATE_INT)) {
                            $insertStmt->execute([$monsterId, $itemId]);
                        }
                    }
                }

                // 3. 处理技能
                // 删除旧技能
                $deleteSkillsStmt = $db->prepare("DELETE FROM monster_skills WHERE monster_template_id = ?");
                $deleteSkillsStmt->execute([$monsterId]);

                // 插入新技能
                $skillsData = $data['skills'] ?? [];
                if (!empty($skillsData) && is_array($skillsData)) {
                    $insertSkillStmt = $db->prepare("INSERT INTO monster_skills (monster_template_id, skill_template_id, cast_chance) VALUES (?, ?, ?)");
                    foreach ($skillsData as $skill) {
                        $skillId = filter_var($skill['id'], FILTER_VALIDATE_INT);
                        $chance = filter_var($skill['chance'], FILTER_VALIDATE_FLOAT);
                        if ($skillId && $chance !== false) {
                            $insertSkillStmt->execute([$monsterId, $skillId, $chance]);
                        }
                    }
                }

                $db->commit();
                
                if ($isCreate) {
                    $response = ['success' => true, 'message' => '怪物创建成功', 'id' => $monsterId];
                } else {
                    $response = ['success' => true, 'message' => '怪物更新成功'];
                }
            } catch (Exception $e) {
                $db->rollBack();
                throw new Exception(($isCreate ? '创建' : '更新') . '怪物失败: ' . $e->getMessage());
            }
            break;

        case 'delete':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            if (!$id) {
                throw new Exception("无效的ID");
            }
            
            $db->beginTransaction();
            try {
                // 0. 从怪物技能表中删除关联
                $stmt = $db->prepare("DELETE FROM monster_skills WHERE monster_template_id = ?");
                $stmt->execute([$id]);

                // 1. 从场景配置中删除该怪物
                $stmt = $db->prepare("DELETE FROM scene_monsters WHERE monster_template_id = ?");
                $stmt->execute([$id]);

                // 2. 从怪物装备表中删除关联
                $stmt = $db->prepare("DELETE FROM monster_equipment WHERE monster_template_id = ?");
                $stmt->execute([$id]);

                // 3. 最后删除怪物模板本身
                $stmt = $db->prepare("DELETE FROM monster_templates WHERE id = ?");
                $stmt->execute([$id]);

                $db->commit();
                $response = ['success' => true, 'message' => '怪物及其所有关联配置已成功删除'];

            } catch (Exception $e) {
                $db->rollBack();
                throw new Exception('删除怪物失败，数据库操作被回滚: ' . $e->getMessage());
            }
            break;

        case 'get_for_list':
            // 构建查询
            $filter_group = isset($_GET['group']) ? (int)$_GET['group'] : null;
            
            $sql = "
                SELECT mt.id, mt.name, mt.level, mt.base_hp, mt.base_attack, mt.defense, 
                       mt.attack_speed, mt.strength, mt.agility, mt.constitution, 
                       mt.intelligence, mt.experience_reward,
                       mg.name AS group_name, mg.type AS group_type,
                       lt.id AS loot_table_id, lt.name AS loot_table_name
                FROM monster_templates mt
                LEFT JOIN monster_groups mg ON mt.group_id = mg.id
                LEFT JOIN loot_tables lt ON mt.loot_table_id = lt.id
            ";
            
            $params = [];
            if ($filter_group) {
                $sql .= " WHERE mt.group_id = :group_id";
                $params[':group_id'] = $filter_group;
            }
            
            $sql .= " ORDER BY mt.id ASC";
            
            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $monsters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $monsters];
            break;

        case 'get_all_simple':
            $stmt = $db->prepare("SELECT id, name FROM monster_templates ORDER BY id ASC");
            $stmt->execute();
            $monsters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'monsters' => $monsters];
            break;

        case 'get_monster':
            $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
            if (!$id) throw new Exception("无效的怪物ID");

            $stmt = $db->prepare("SELECT * FROM monster_templates WHERE id = ?");
            $stmt->execute([$id]);
            $monster = $stmt->fetch(PDO::FETCH_ASSOC);

            if (!$monster) throw new Exception("找不到怪物");

            // 获取怪物装备
            $stmt = $db->prepare("SELECT item_template_id FROM monster_equipment WHERE monster_template_id = ?");
            $stmt->execute([$id]);
            $monster['equipment'] = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

            $response = ['success' => true, 'data' => $monster];
            break;

        case 'get_all':
            $stmt = $db->prepare("SELECT id, name FROM monster_templates ORDER BY id ASC");
            $stmt->execute();
            $monsters = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'monsters' => $monsters];
            break;
            
        // 添加分组相关API
        case 'add_group':
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'normal';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                throw new Exception("分组名称不能为空");
            }
            
            $stmt = $db->prepare("
                INSERT INTO monster_groups 
                (name, type, description, sort_order)
                VALUES 
                (:name, :type, :description, :sort_order)
            ");
            $stmt->execute([
                ':name' => $name,
                ':type' => $type,
                ':description' => $description,
                ':sort_order' => $sort_order
            ]);
            
            $response = ['success' => true, 'message' => '分组添加成功', 'id' => $db->lastInsertId()];
            break;
            
        case 'update_group':
            $id = (int)($_POST['id'] ?? 0);
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'normal';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (!$id) {
                throw new Exception("无效的分组ID");
            }
            
            if (empty($name)) {
                throw new Exception("分组名称不能为空");
            }
            
            $stmt = $db->prepare("
                UPDATE monster_groups 
                SET name = :name,
                    type = :type,
                    description = :description,
                    sort_order = :sort_order,
                    updated_at = NOW()
                WHERE id = :id
            ");
            $stmt->execute([
                ':name' => $name,
                ':type' => $type,
                ':description' => $description,
                ':sort_order' => $sort_order,
                ':id' => $id
            ]);
            
            $response = ['success' => true, 'message' => '分组更新成功'];
            break;
            
        case 'delete_group':
            $id = (int)($_POST['id'] ?? 0);
            
            if (!$id) {
                throw new Exception("无效的分组ID");
            }
            
            // 检查是否有怪物使用此分组
            $stmt = $db->prepare("SELECT COUNT(*) FROM monster_templates WHERE group_id = :id");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                throw new Exception("无法删除此分组，有 {$count} 个怪物属于该分组");
            }
            
            $stmt = $db->prepare("DELETE FROM monster_groups WHERE id = :id");
            $stmt->execute([':id' => $id]);
            
            $response = ['success' => true, 'message' => '分组删除成功'];
            break;
            
        case 'get_groups':
            $stmt = $db->query("
                SELECT mg.*, 
                       (SELECT COUNT(*) FROM monster_templates WHERE group_id = mg.id) AS monster_count
                FROM monster_groups mg
                ORDER BY mg.sort_order, mg.name
            ");
            $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'data' => $groups];
            break;

        default:
            debugLog('未知操作: ' . $action);
            throw new Exception("未知操作: " . $action);
    }
} catch (Exception $e) {
    // 记录详细错误日志
    debugLog('Monster API Error: ' . $e->getMessage());
    $response['message'] = $e->getMessage();
}

// 记录最终响应
debugLog('最终响应: ' . print_r($response, true));

echo json_encode($response); 