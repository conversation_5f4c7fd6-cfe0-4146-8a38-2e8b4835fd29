<?php
require_once 'auth.php';
require_once '../config/Database.php';

$page_title = '图层数据调试';
$extra_css = '<style>
.debug-container {
    max-width: 1200px;
    margin: 20px auto;
    padding: 20px;
}
.debug-section {
    margin: 20px 0;
    padding: 15px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}
.debug-table {
    width: 100%;
    border-collapse: collapse;
    margin: 10px 0;
}
.debug-table th, .debug-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}
.debug-table th {
    background: #f5f5f5;
    font-weight: bold;
}
.sync-button {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 4px;
    cursor: pointer;
    margin: 10px 5px;
}
.sync-button:hover {
    background: #218838;
}
.status-ok { color: #28a745; font-weight: bold; }
.status-error { color: #dc3545; font-weight: bold; }
.status-warning { color: #ffc107; font-weight: bold; }
</style>';
require_once 'layout_header.php';

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    
    // 获取大陆数据
    $continentsStmt = $pdo->prepare("SELECT id, display_name, z_level FROM continents ORDER BY z_level");
    $continentsStmt->execute();
    $continents = $continentsStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取图层数据
    $layersStmt = $pdo->prepare("SELECT z, name, description FROM scene_layers ORDER BY z");
    $layersStmt->execute();
    $layers = $layersStmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取场景统计
    $scenesStmt = $pdo->prepare("SELECT z, COUNT(*) as scene_count FROM scenes GROUP BY z ORDER BY z");
    $scenesStmt->execute();
    $sceneStats = $scenesStmt->fetchAll(PDO::FETCH_ASSOC);
    $sceneStatsByZ = [];
    foreach ($sceneStats as $stat) {
        $sceneStatsByZ[$stat['z']] = $stat['scene_count'];
    }
    
} catch (Exception $e) {
    echo "<div class='alert alert-danger'>数据库错误: " . htmlspecialchars($e->getMessage()) . "</div>";
    require_once 'layout_footer.php';
    exit;
}
?>

<div class="debug-container">
    <h1>图层数据调试页面</h1>
    <p>这个页面用于调试大陆和场景图层的同步问题</p>
    
    <div class="debug-section">
        <h2>大陆数据 (continents表)</h2>
        <table class="debug-table">
            <thead>
                <tr>
                    <th>大陆ID</th>
                    <th>显示名称</th>
                    <th>Z坐标层级</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($continents as $continent): ?>
                <tr>
                    <td><?= htmlspecialchars($continent['id']) ?></td>
                    <td><?= htmlspecialchars($continent['display_name']) ?></td>
                    <td><?= $continent['z_level'] ?></td>
                    <td>
                        <?php
                        $hasLayer = false;
                        foreach ($layers as $layer) {
                            if ($layer['z'] == $continent['z_level']) {
                                $hasLayer = true;
                                break;
                            }
                        }
                        if ($hasLayer) {
                            echo '<span class="status-ok">✓ 已同步</span>';
                        } else {
                            echo '<span class="status-error">✗ 未同步</span>';
                        }
                        ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <p><strong>总计:</strong> <?= count($continents) ?> 个大陆</p>
    </div>
    
    <div class="debug-section">
        <h2>场景图层数据 (scene_layers表)</h2>
        <table class="debug-table">
            <thead>
                <tr>
                    <th>Z坐标</th>
                    <th>图层名称</th>
                    <th>描述</th>
                    <th>场景数量</th>
                    <th>状态</th>
                </tr>
            </thead>
            <tbody>
                <?php foreach ($layers as $layer): ?>
                <tr>
                    <td><?= $layer['z'] ?></td>
                    <td><?= htmlspecialchars($layer['name']) ?></td>
                    <td><?= htmlspecialchars($layer['description'] ?: '无') ?></td>
                    <td><?= $sceneStatsByZ[$layer['z']] ?? 0 ?></td>
                    <td>
                        <?php
                        $hasContinent = false;
                        foreach ($continents as $continent) {
                            if ($continent['z_level'] == $layer['z']) {
                                $hasContinent = true;
                                break;
                            }
                        }
                        if ($hasContinent) {
                            echo '<span class="status-ok">✓ 有对应大陆</span>';
                        } else {
                            echo '<span class="status-warning">⚠ 无对应大陆</span>';
                        }
                        ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            </tbody>
        </table>
        <p><strong>总计:</strong> <?= count($layers) ?> 个图层</p>
    </div>
    
    <div class="debug-section">
        <h2>同步状态分析</h2>
        <?php
        $missingLayers = [];
        $orphanLayers = [];
        
        // 检查缺失的图层
        foreach ($continents as $continent) {
            $hasLayer = false;
            foreach ($layers as $layer) {
                if ($layer['z'] == $continent['z_level']) {
                    $hasLayer = true;
                    break;
                }
            }
            if (!$hasLayer) {
                $missingLayers[] = $continent;
            }
        }
        
        // 检查孤立的图层
        foreach ($layers as $layer) {
            $hasContinent = false;
            foreach ($continents as $continent) {
                if ($continent['z_level'] == $layer['z']) {
                    $hasContinent = true;
                    break;
                }
            }
            if (!$hasContinent) {
                $orphanLayers[] = $layer;
            }
        }
        ?>
        
        <?php if (empty($missingLayers) && empty($orphanLayers)): ?>
            <div class="status-ok">✓ 所有大陆和图层都已正确同步</div>
        <?php else: ?>
            <?php if (!empty($missingLayers)): ?>
                <div class="status-error">
                    <strong>缺失图层的大陆 (<?= count($missingLayers) ?>个):</strong>
                    <ul>
                        <?php foreach ($missingLayers as $continent): ?>
                            <li><?= htmlspecialchars($continent['display_name']) ?> (Z=<?= $continent['z_level'] ?>)</li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
            
            <?php if (!empty($orphanLayers)): ?>
                <div class="status-warning">
                    <strong>孤立的图层 (<?= count($orphanLayers) ?>个):</strong>
                    <ul>
                        <?php foreach ($orphanLayers as $layer): ?>
                            <li><?= htmlspecialchars($layer['name']) ?> (Z=<?= $layer['z'] ?>)</li>
                        <?php endforeach; ?>
                    </ul>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
    
    <div class="debug-section">
        <h2>修复操作</h2>
        <button class="sync-button" onclick="syncLayers()">🔄 同步大陆到图层表</button>
        <button class="sync-button" onclick="refreshData()" style="background: #17a2b8;">🔄 刷新数据</button>
        <div id="sync-result" style="margin-top: 10px;"></div>
    </div>
</div>

<script>
// 同步图层
async function syncLayers() {
    try {
        const formData = new FormData();
        formData.append('action', 'sync_layers');
        
        const response = await fetch('api_continents.php', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        const resultDiv = document.getElementById('sync-result');
        
        if (result.success) {
            resultDiv.innerHTML = `<div class="status-ok">✓ ${result.message}</div>`;
            showToast(result.message, 'success');
            // 3秒后刷新页面
            setTimeout(() => {
                location.reload();
            }, 3000);
        } else {
            resultDiv.innerHTML = `<div class="status-error">✗ ${result.message}</div>`;
            showToast(result.message, 'error');
        }
    } catch (error) {
        console.error('同步失败:', error);
        document.getElementById('sync-result').innerHTML = `<div class="status-error">✗ 网络错误: ${error.message}</div>`;
        showToast('网络错误', 'error');
    }
}

// 刷新数据
function refreshData() {
    location.reload();
}

// 页面加载完成后显示状态
document.addEventListener('DOMContentLoaded', function() {
    const missingCount = <?= count($missingLayers) ?>;
    const orphanCount = <?= count($orphanLayers) ?>;
    
    if (missingCount > 0 || orphanCount > 0) {
        showToast(`发现同步问题：${missingCount}个大陆缺失图层，${orphanCount}个孤立图层`, 'warning');
    } else {
        showToast('所有数据同步正常', 'success');
    }
});
</script>

<?php require_once 'layout_footer.php'; ?>
