<?php
$pageTitle = "凝练材料分值管理";
$currentPage = "refine_scores";

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

include 'layout_header.php';

// 获取所有材料分值
$scores_query = "SELECT id, element, tier, score FROM material_scores ORDER BY element, tier";
$scores_stmt = $db->prepare($scores_query);
$scores_stmt->execute();
$scores = $scores_stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_scores'])) {
        foreach ($_POST['scores'] as $id => $score) {
            $update_query = "UPDATE material_scores SET score = ? WHERE id = ?";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([$score, $id]);
        }
        echo "<div class='alert alert-success'>材料分值已更新</div>";
    }
    
    if (isset($_POST['add_score'])) {
        $element = $_POST['element'];
        $tier = $_POST['tier'];
        $score = $_POST['score'];
        
        // 检查是否已存在
        $check_query = "SELECT id FROM material_scores WHERE element = ? AND tier = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$element, $tier]);
        
        if ($check_stmt->rowCount() > 0) {
            // 更新
            $update_query = "UPDATE material_scores SET score = ? WHERE element = ? AND tier = ?";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([$score, $element, $tier]);
            echo "<div class='alert alert-success'>材料分值已更新</div>";
        } else {
            // 插入
            $insert_query = "INSERT INTO material_scores (element, tier, score) VALUES (?, ?, ?)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([$element, $tier, $score]);
            echo "<div class='alert alert-success'>材料分值已添加</div>";
        }
        
        // 刷新页面
        header("Location: refine_scores.php");
        exit;
    }
    
    if (isset($_POST['delete_score']) && isset($_POST['score_id'])) {
        $score_id = $_POST['score_id'];
        $delete_query = "DELETE FROM material_scores WHERE id = ?";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->execute([$score_id]);
        
        echo "<div class='alert alert-success'>材料分值已删除</div>";
        header("Location: refine_scores.php");
        exit;
    }
}

// 按元素分组显示材料分值
$scoresByElement = [];
foreach ($scores as $score) {
    $scoresByElement[$score['element']][$score['tier']] = $score;
}
?>

<div class="container-fluid">
    <p>管理不同元素和等级的材料分值，用于装备凝练系统。</p>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table mr-1"></i>
                    材料分值表
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>元素</th>
                                        <th>1级</th>
                                        <th>2级</th>
                                        <th>3级</th>
                                        <th>4级</th>
                                        <th>5级</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php 
                                    $elements = ['gold' => '金', 'wood' => '木', 'water' => '水', 'fire' => '火', 'earth' => '土'];
                                    foreach ($elements as $elementKey => $elementName): 
                                    ?>
                                    <tr>
                                        <td><strong><?= $elementName ?></strong></td>
                                        <?php for ($tier = 1; $tier <= 5; $tier++): ?>
                                            <td>
                                                <?php if (isset($scoresByElement[$elementKey][$tier])): ?>
                                                    <div class="input-group">
                                                        <input type="number" step="0.1" min="0.1" max="10" 
                                                            class="form-control" 
                                                            name="scores[<?= $scoresByElement[$elementKey][$tier]['id'] ?>]" 
                                                            value="<?= $scoresByElement[$elementKey][$tier]['score'] ?>">
                                                        <div class="input-group-append">
                                                            <form method="post" action="" style="display:inline">
                                                                <input type="hidden" name="score_id" value="<?= $scoresByElement[$elementKey][$tier]['id'] ?>">
                                                                <button type="submit" name="delete_score" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个分值设置吗？')">
                                                                    <i class="fas fa-trash"></i>
                                                                </button>
                                                            </form>
                                                        </div>
                                                    </div>
                                                <?php else: ?>
                                                    <span class="text-muted">未设置</span>
                                                <?php endif; ?>
                                            </td>
                                        <?php endfor; ?>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <button type="submit" name="save_scores" class="btn btn-primary">保存所有分值</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-plus mr-1"></i>
                    添加/修改材料分值
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="element">元素属性:</label>
                            <select class="form-control" id="element" name="element" required>
                                <option value="gold">金</option>
                                <option value="wood">木</option>
                                <option value="water">水</option>
                                <option value="fire">火</option>
                                <option value="earth">土</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="tier">材料等级:</label>
                            <select class="form-control" id="tier" name="tier" required>
                                <option value="1">1级</option>
                                <option value="2">2级</option>
                                <option value="3">3级</option>
                                <option value="4">4级</option>
                                <option value="5">5级</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="score">分值:</label>
                            <input type="number" step="0.1" min="0.1" max="10" class="form-control" id="score" name="score" required>
                        </div>
                        
                        <button type="submit" name="add_score" class="btn btn-success">添加/修改</button>
                    </form>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle mr-1"></i>
                    帮助信息
                </div>
                <div class="card-body">
                    <p>材料分值会影响凝练最终结果的RV值。</p>
                    <p>凝练值（RV）计算公式:</p>
                    <p><code>RV = (材料总分值 × 组合系数) × (1 ± 5%)</code></p>
                    <p>材料总分值是5个材料的分值总和，根据材料的元素和等级决定基础分值。</p>
                </div>
            </div>
        </div>
    </div>
</div>

<?php include 'layout_footer.php'; ?> 