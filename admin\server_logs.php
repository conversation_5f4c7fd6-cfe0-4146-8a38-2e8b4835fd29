<?php
$pageTitle = '服务器日志管理';
$currentPage = 'server_logs';

ob_start();
?>
<style>
    .log-controls {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 20px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .log-controls h3 {
        margin-top: 0;
        margin-bottom: 15px;
        color: #495057;
        border-bottom: 2px solid #007bff;
        padding-bottom: 8px;
    }
    .control-row {
        display: flex;
        align-items: center;
        gap: 15px;
        margin-bottom: 15px;
        flex-wrap: wrap;
    }
    .control-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .control-group label {
        font-weight: 600;
        color: #495057;
        white-space: nowrap;
    }
    .control-group input, .control-group select {
        padding: 6px 10px;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 14px;
    }
    .control-group input:focus, .control-group select:focus {
        outline: none;
        border-color: #007bff;
        box-shadow: 0 0 0 2px rgba(0,123,255,0.25);
    }
    .btn {
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        border: 1px solid;
        transition: all 0.2s;
        text-decoration: none;
        display: inline-block;
    }
    .btn-primary {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    .btn-primary:hover {
        background-color: #0056b3;
        border-color: #004085;
    }
    .btn-success {
        background-color: #28a745;
        color: white;
        border-color: #28a745;
    }
    .btn-success:hover {
        background-color: #1e7e34;
        border-color: #1c7430;
    }
    .btn-danger {
        background-color: #dc3545;
        color: white;
        border-color: #dc3545;
    }
    .btn-danger:hover {
        background-color: #c82333;
        border-color: #bd2130;
    }
    .btn-secondary {
        background-color: #6c757d;
        color: white;
        border-color: #6c757d;
    }
    .btn-secondary:hover {
        background-color: #545b62;
        border-color: #4e555b;
    }
    .log-stats {
        background: #f8f9fa;
        border: 1px solid #dee2e6;
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    .stats-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
    }
    .stat-item {
        text-align: center;
        padding: 10px;
        background: white;
        border-radius: 4px;
        border: 1px solid #e9ecef;
    }
    .stat-value {
        font-size: 18px;
        font-weight: bold;
        color: #007bff;
        display: block;
    }
    .stat-label {
        font-size: 12px;
        color: #6c757d;
        margin-top: 4px;
    }
    .log-container {
        background: #1e1e1e;
        border: 1px solid #333;
        border-radius: 8px;
        padding: 0;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        max-height: 600px;
        overflow: hidden;
        position: relative;
    }
    .log-header {
        background: #2d2d2d;
        color: #fff;
        padding: 10px 15px;
        border-bottom: 1px solid #333;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    .log-content {
        height: 550px;
        overflow-y: auto;
        padding: 10px;
        font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        font-size: 13px;
        line-height: 1.4;
    }
    .log-line {
        margin-bottom: 2px;
        padding: 2px 5px;
        border-radius: 2px;
        word-wrap: break-word;
    }
    .log-line.error {
        background-color: rgba(220, 53, 69, 0.1);
        color: #ff6b6b;
        border-left: 3px solid #dc3545;
    }
    .log-line.warning {
        background-color: rgba(255, 193, 7, 0.1);
        color: #ffc107;
        border-left: 3px solid #ffc107;
    }
    .log-line.info {
        background-color: rgba(23, 162, 184, 0.1);
        color: #17a2b8;
        border-left: 3px solid #17a2b8;
    }
    .log-line.debug {
        background-color: rgba(108, 117, 125, 0.1);
        color: #6c757d;
        border-left: 3px solid #6c757d;
    }
    .log-line.normal {
        color: #e9ecef;
    }
    .log-line.unknown {
        color: #adb5bd;
    }
    .log-timestamp {
        color: #6c757d;
        font-weight: bold;
        margin-right: 8px;
    }
    .log-process {
        color: #28a745;
        margin-right: 8px;
    }
    .log-message {
        color: inherit;
    }
    .auto-refresh {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #495057;
    }
    .auto-refresh input[type="checkbox"] {
        margin: 0;
    }
    .loading {
        text-align: center;
        padding: 20px;
        color: #6c757d;
        font-style: italic;
    }
    .no-logs {
        text-align: center;
        padding: 40px;
        color: #6c757d;
    }
    .alert {
        padding: 12px 16px;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    .alert-success {
        background-color: #d4edda;
        border: 1px solid #c3e6cb;
        color: #155724;
    }
    .alert-danger {
        background-color: #f8d7da;
        border: 1px solid #f5c6cb;
        color: #721c24;
    }
    .alert-info {
        background-color: #d1ecf1;
        border: 1px solid #bee5eb;
        color: #0c5460;
    }
</style>
<?php
$extra_css = ob_get_clean();

require_once 'layout_header.php';
?>

<!-- 日志统计 -->
<div class="log-stats" id="logStats">
    <div class="stats-grid">
        <div class="stat-item">
            <span class="stat-value" id="statFileSize">-</span>
            <div class="stat-label">文件大小</div>
        </div>
        <div class="stat-item">
            <span class="stat-value" id="statTotalLines">-</span>
            <div class="stat-label">总行数</div>
        </div>
        <div class="stat-item">
            <span class="stat-value" id="statLastModified">-</span>
            <div class="stat-label">最后修改</div>
        </div>
        <div class="stat-item">
            <span class="stat-value" id="statDisplayLines">-</span>
            <div class="stat-label">显示行数</div>
        </div>
    </div>
</div>

<!-- 控制面板 -->
<div class="log-controls">
    <h3>📋 日志控制</h3>
    <div class="control-row">
        <div class="control-group">
            <label for="linesInput">显示行数:</label>
            <select id="linesInput">
                <option value="50">50行</option>
                <option value="100" selected>100行</option>
                <option value="200">200行</option>
                <option value="500">500行</option>
                <option value="1000">1000行</option>
            </select>
        </div>
        <div class="control-group">
            <label for="filterInput">过滤关键词:</label>
            <input type="text" id="filterInput" placeholder="输入关键词过滤日志..." />
        </div>
        <button class="btn btn-primary" onclick="loadLogs()">刷新日志</button>
        <button class="btn btn-success" onclick="downloadLogs()">下载日志</button>
    </div>
    <div class="control-row">
        <div class="auto-refresh">
            <input type="checkbox" id="autoRefresh" onchange="toggleAutoRefresh()" />
            <label for="autoRefresh">自动刷新 (每5秒)</label>
        </div>
        <button class="btn btn-secondary" onclick="clearFilter()">清除过滤</button>
        <button class="btn btn-danger" onclick="clearLogs()">清空日志</button>
    </div>
</div>

<!-- 消息提示 -->
<div id="alertContainer"></div>

<!-- 日志显示 -->
<div class="log-container">
    <div class="log-header">
        <span>📄 服务器日志 (game-server.log)</span>
        <span id="logStatus">准备就绪</span>
    </div>
    <div class="log-content" id="logContent">
        <div class="loading">正在加载日志...</div>
    </div>
</div>

<?php
ob_start();
?>
<script>
    let autoRefreshInterval = null;
    let isLoading = false;

    // 页面加载时初始化
    document.addEventListener('DOMContentLoaded', function() {
        loadStats();
        loadLogs();
        
        // 绑定回车键事件
        document.getElementById('filterInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                loadLogs();
            }
        });
    });

    // 加载日志统计
    function loadStats() {
        fetch('api_server_logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=get_stats'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStats(data.data);
            } else {
                console.error('获取统计失败:', data.message);
            }
        })
        .catch(error => {
            console.error('请求失败:', error);
        });
    }

    // 更新统计信息
    function updateStats(stats) {
        document.getElementById('statFileSize').textContent = stats.file_size_human || '-';
        document.getElementById('statTotalLines').textContent = stats.total_lines || '-';
        document.getElementById('statLastModified').textContent = stats.last_modified || '-';
    }

    // 加载日志
    function loadLogs() {
        if (isLoading) return;

        isLoading = true;
        document.getElementById('logStatus').textContent = '加载中...';

        const lines = document.getElementById('linesInput').value;
        const filter = document.getElementById('filterInput').value;

        fetch('api_server_logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: `action=get_logs&lines=${lines}&filter=${encodeURIComponent(filter)}`
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayLogs(data.data);
                updateStats(data.data);
                document.getElementById('logStatus').textContent = `已加载 ${data.data.total_lines} 行`;
                document.getElementById('statDisplayLines').textContent = data.data.total_lines;
            } else {
                showAlert('加载日志失败: ' + data.message, 'danger');
                document.getElementById('logStatus').textContent = '加载失败';
            }
        })
        .catch(error => {
            showAlert('请求失败: ' + error.message, 'danger');
            document.getElementById('logStatus').textContent = '请求失败';
        })
        .finally(() => {
            isLoading = false;
        });
    }

    // 显示日志
    function displayLogs(data) {
        const logContent = document.getElementById('logContent');

        if (!data.logs || data.logs.length === 0) {
            logContent.innerHTML = '<div class="no-logs">📝 暂无日志数据</div>';
            return;
        }

        let html = '';
        data.logs.forEach(log => {
            const levelClass = log.level || 'normal';
            html += `<div class="log-line ${levelClass}">`;

            if (log.timestamp) {
                html += `<span class="log-timestamp">${log.timestamp}</span>`;
            }

            if (log.process && log.pid) {
                html += `<span class="log-process">[${log.process}:${log.pid}]</span>`;
            }

            html += `<span class="log-message">${escapeHtml(log.message)}</span>`;
            html += '</div>';
        });

        logContent.innerHTML = html;

        // 滚动到底部
        logContent.scrollTop = logContent.scrollHeight;
    }

    // 切换自动刷新
    function toggleAutoRefresh() {
        const checkbox = document.getElementById('autoRefresh');

        if (checkbox.checked) {
            autoRefreshInterval = setInterval(() => {
                loadLogs();
            }, 5000);
            showAlert('自动刷新已启用 (每5秒)', 'info');
        } else {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
            }
            showAlert('自动刷新已关闭', 'info');
        }
    }

    // 清除过滤
    function clearFilter() {
        document.getElementById('filterInput').value = '';
        loadLogs();
    }

    // 下载日志
    function downloadLogs() {
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = 'api_server_logs.php';
        form.style.display = 'none';

        const actionInput = document.createElement('input');
        actionInput.name = 'action';
        actionInput.value = 'download_logs';
        form.appendChild(actionInput);

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);

        showAlert('日志下载已开始', 'success');
    }

    // 清空日志
    function clearLogs() {
        if (!confirm('确定要清空服务器日志吗？此操作会创建备份文件，但不可撤销！')) {
            return;
        }

        fetch('api_server_logs.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: 'action=clear_logs'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showAlert(`日志已清空，备份文件: ${data.data.backup_file}`, 'success');
                loadStats();
                loadLogs();
            } else {
                showAlert('清空日志失败: ' + data.message, 'danger');
            }
        })
        .catch(error => {
            showAlert('请求失败: ' + error.message, 'danger');
        });
    }

    // 显示提示消息
    function showAlert(message, type) {
        const container = document.getElementById('alertContainer');
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.textContent = message;

        container.appendChild(alert);

        // 3秒后自动移除
        setTimeout(() => {
            if (alert.parentNode) {
                alert.parentNode.removeChild(alert);
            }
        }, 3000);
    }

    // HTML转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 页面卸载时清理定时器
    window.addEventListener('beforeunload', function() {
        if (autoRefreshInterval) {
            clearInterval(autoRefreshInterval);
        }
    });
</script>
</script>
<?php
$extra_js = ob_get_clean();

require_once 'layout_footer.php';
?>
