<?php
header('Content-Type: application/json; charset=utf-8');

// 错误处理函数
function debugLog($message) {
    error_log("[属性重修配置API] " . $message);
}

// 设置错误处理
set_error_handler(function($severity, $message, $file, $line) {
    debugLog("PHP错误: $message 在 $file:$line");
});

set_exception_handler(function($exception) {
    debugLog("未捕获异常: " . $exception->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => '服务器内部错误: ' . $exception->getMessage()
    ], JSON_UNESCAPED_UNICODE);
});

require_once __DIR__ . '/../config/Database.php';

// 获取数据库连接
$db = Database::getInstance()->getConnection();

// 从 POST 和 GET 中获取 action
$action = $_POST['action'] ?? $_GET['action'] ?? '';
$response = ['success' => false, 'message' => '无效的操作'];

// 记录动作
debugLog('处理动作: ' . $action);

try {
    switch ($action) {
        case 'get_buildings':
            // 获取所有属性重修建筑及其配置
            $stmt = $db->prepare("
                SELECT 
                    sb.id as scene_building_id,
                    sb.scene_id,
                    s.name as scene_name,
                    b.name as building_name,
                    arc.required_item_id,
                    arc.required_quantity,
                    it.name as item_name,
                    it.description as item_description
                FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                JOIN scenes s ON sb.scene_id = s.id
                LEFT JOIN attribute_reset_config arc ON sb.id = arc.scene_building_id
                LEFT JOIN item_templates it ON arc.required_item_id = it.id
                WHERE b.type = 'ATTRIBUTE_RESET'
                ORDER BY s.name, b.name
            ");
            $stmt->execute();
            $buildings = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            // 组织数据结构
            $result = [];
            foreach ($buildings as $building) {
                $config = null;
                if ($building['required_item_id']) {
                    $config = [
                        'required_item_id' => $building['required_item_id'],
                        'required_quantity' => $building['required_quantity'],
                        'item_name' => $building['item_name'],
                        'item_description' => $building['item_description']
                    ];
                }
                
                $result[] = [
                    'scene_building_id' => (int)$building['scene_building_id'],
                    'scene_id' => $building['scene_id'],
                    'scene_name' => $building['scene_name'],
                    'building_name' => $building['building_name'],
                    'config' => $config
                ];
            }
            
            $response = [
                'success' => true,
                'data' => $result,
                'message' => '获取建筑列表成功'
            ];
            break;
            
        case 'get_items':
            // 获取所有物品模板（用于搜索选择）
            $stmt = $db->prepare("
                SELECT id, name, description, category
                FROM item_templates
                WHERE category IN ('Potion', 'Material', 'Scroll', 'Misc')
                ORDER BY category, name
            ");
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            $response = [
                'success' => true,
                'data' => $items,
                'message' => '获取物品列表成功'
            ];
            break;
            
        case 'save_config':
            $sceneBuildingId = filter_input(INPUT_POST, 'scene_building_id', FILTER_VALIDATE_INT);
            $requiredItemId = filter_input(INPUT_POST, 'required_item_id', FILTER_VALIDATE_INT);
            $requiredQuantity = filter_input(INPUT_POST, 'required_quantity', FILTER_VALIDATE_INT);
            
            if (!$sceneBuildingId || !$requiredItemId || !$requiredQuantity || $requiredQuantity < 1) {
                throw new Exception('参数无效');
            }
            
            $db->beginTransaction();
            try {
                // 验证场景建筑是否存在且为属性重修类型
                $stmt = $db->prepare("
                    SELECT sb.id, b.type 
                    FROM scene_buildings sb
                    JOIN buildings b ON sb.building_id = b.id
                    WHERE sb.id = ? AND b.type = 'ATTRIBUTE_RESET'
                ");
                $stmt->execute([$sceneBuildingId]);
                $building = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();
                
                if (!$building) {
                    throw new Exception('找不到指定的属性重修建筑');
                }
                
                // 验证物品是否存在
                $stmt = $db->prepare("SELECT id, name FROM item_templates WHERE id = ?");
                $stmt->execute([$requiredItemId]);
                $item = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();
                
                if (!$item) {
                    throw new Exception('指定的物品不存在');
                }
                
                // 检查是否已有配置
                $stmt = $db->prepare("SELECT id FROM attribute_reset_config WHERE scene_building_id = ?");
                $stmt->execute([$sceneBuildingId]);
                $existingConfig = $stmt->fetch(PDO::FETCH_ASSOC);
                $stmt->closeCursor();
                
                if ($existingConfig) {
                    // 更新现有配置
                    $stmt = $db->prepare("
                        UPDATE attribute_reset_config 
                        SET required_item_id = ?, required_quantity = ?, updated_at = NOW()
                        WHERE scene_building_id = ?
                    ");
                    $stmt->execute([$requiredItemId, $requiredQuantity, $sceneBuildingId]);
                    $message = '配置更新成功';
                } else {
                    // 创建新配置
                    $stmt = $db->prepare("
                        INSERT INTO attribute_reset_config (scene_building_id, required_item_id, required_quantity)
                        VALUES (?, ?, ?)
                    ");
                    $stmt->execute([$sceneBuildingId, $requiredItemId, $requiredQuantity]);
                    $message = '配置创建成功';
                }
                
                $db->commit();
                
                $response = [
                    'success' => true,
                    'message' => $message . "，重修物品：{$item['name']}，数量：{$requiredQuantity}"
                ];
                
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;
            
        case 'delete_config':
            $sceneBuildingId = filter_input(INPUT_POST, 'scene_building_id', FILTER_VALIDATE_INT);
            
            if (!$sceneBuildingId) {
                throw new Exception('参数无效');
            }
            
            $stmt = $db->prepare("DELETE FROM attribute_reset_config WHERE scene_building_id = ?");
            $stmt->execute([$sceneBuildingId]);
            
            if ($stmt->rowCount() > 0) {
                $response = [
                    'success' => true,
                    'message' => '配置删除成功'
                ];
            } else {
                throw new Exception('找不到要删除的配置');
            }
            break;
            
        default:
            throw new Exception('未知的操作: ' . $action);
    }
    
} catch (Exception $e) {
    debugLog('操作失败: ' . $e->getMessage());
    $response = [
        'success' => false,
        'message' => $e->getMessage()
    ];
}

// 输出响应
echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
