<?php
// admin/api_battle_logs.php
session_start();
require_once '../config/Database.php';
require_once 'auth.php';

// 检查是否登录
if (empty($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Content-Type: application/json');
    echo json_encode(['success' => false, 'message' => '未登录或会话已过期']);
    exit;
}

// 获取请求操作
$action = isset($_GET['action']) ? $_GET['action'] : '';

// 根据操作类型处理请求
switch ($action) {
    case 'get_logs':
        getBattleLogs();
        break;
    case 'get_log_detail':
        getBattleLogDetail();
        break;
    case 'get_scenes':
        getScenes();
        break;
    case 'export_log':
        exportBattleLog();
        break;
    case 'clear_old_logs':
        clearOldLogs();
        break;
    default:
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '未知操作']);
        break;
}

/**
 * 获取战斗日志列表
 */
function getBattleLogs() {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        // 分页参数
        $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
        $pageSize = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 20;
        $offset = ($page - 1) * $pageSize;
        
        // 筛选条件
        $conditions = [];
        $params = [];
        
        // 战斗结果筛选
        if (!empty($_GET['battle_result'])) {
            $conditions[] = 'bl.battle_result = :battle_result';
            $params[':battle_result'] = $_GET['battle_result'];
        }
        
        // 日期范围筛选
        if (!empty($_GET['start_date'])) {
            $conditions[] = 'bl.start_time >= :start_date';
            $params[':start_date'] = $_GET['start_date'] . ' 00:00:00';
        }
        
        if (!empty($_GET['end_date'])) {
            $conditions[] = 'bl.start_time <= :end_date';
            $params[':end_date'] = $_GET['end_date'] . ' 23:59:59';
        }
        
        // 场景筛选
        if (!empty($_GET['scene_id'])) {
            $conditions[] = 'bl.scene_id = :scene_id';
            $params[':scene_id'] = $_GET['scene_id'];
        }
        
        // 玩家名称筛选
        if (!empty($_GET['player_name'])) {
            $conditions[] = 'bl.participants LIKE :player_name';
            $params[':player_name'] = '%' . $_GET['player_name'] . '%';
        }
        
        // 怪物名称筛选
        if (!empty($_GET['monster_name'])) {
            $conditions[] = 'COALESCE(mt.name, \'\') LIKE :monster_name';
            $params[':monster_name'] = '%' . $_GET['monster_name'] . '%';
        }
        
        // 构建WHERE子句
        $whereClause = !empty($conditions) ? 'WHERE ' . implode(' AND ', $conditions) : '';
        
        // 查询总记录数
        $countSql = "SELECT COUNT(*) FROM battle_logs bl 
                    LEFT JOIN scenes s ON bl.scene_id = s.id
                    LEFT JOIN monster_templates mt ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bl.monster_id, '_', 5), '_', -1) AS UNSIGNED) = mt.id
                    $whereClause";
        $countStmt = $conn->prepare($countSql);
        foreach ($params as $key => $value) {
            $countStmt->bindValue($key, $value);
        }
        $countStmt->execute();
        $totalLogs = $countStmt->fetchColumn();
        
        // 计算总页数
        $totalPages = ceil($totalLogs / $pageSize);
        
        // 查询日志数据
        $sql = "
            SELECT 
                bl.id,
                bl.battle_id,
                bl.scene_id,
                s.name as scene_name,
                bl.monster_id,
                COALESCE(mt.name, '未知怪物') as monster_name,
                bl.start_time,
                bl.end_time,
                bl.battle_result,
                bl.participants
            FROM 
                battle_logs bl
            LEFT JOIN 
                scenes s ON bl.scene_id = s.id
            LEFT JOIN
                monster_templates mt ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bl.monster_id, '_', 5), '_', -1) AS UNSIGNED) = mt.id
            $whereClause
            ORDER BY 
                bl.id DESC
            LIMIT :offset, :limit
        ";
        
        $stmt = $conn->prepare($sql);
        foreach ($params as $key => $value) {
            $stmt->bindValue($key, $value);
        }
        $stmt->bindValue(':offset', $offset, PDO::PARAM_INT);
        $stmt->bindValue(':limit', $pageSize, PDO::PARAM_INT);
        $stmt->execute();
        
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 返回结果
        $response = [
            'success' => true,
            'data' => [
                'logs' => $logs,
                'pagination' => [
                    'current_page' => $page,
                    'total_pages' => $totalPages,
                    'page_size' => $pageSize,
                    'total_logs' => $totalLogs
                ]
            ]
        ];
        
        header('Content-Type: application/json');
        echo json_encode($response);
        
    } catch (PDOException $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

/**
 * 获取战斗日志详情
 */
function getBattleLogDetail() {
    try {
        if (empty($_GET['id'])) {
            throw new Exception('缺少日志ID参数');
        }
        
        $logId = (int)$_GET['id'];
        
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        $sql = "
            SELECT 
                bl.*,
                s.name as scene_name,
                mt.name as monster_name,
                mt.level as monster_level,
                mt.base_hp as monster_base_hp,
                mt.base_max_hp as monster_base_max_hp,
                mt.base_attack as monster_base_attack,
                mt.defense as monster_defense,
                mt.attack_speed as monster_attack_speed,
                mt.experience_reward as monster_experience_reward,
                mt.description as monster_description,
                mt.fire_resistance,
                mt.ice_resistance,
                mt.wind_resistance,
                mt.electric_resistance,
                mt.strength,
                mt.agility,
                mt.constitution,
                mt.intelligence
            FROM 
                battle_logs bl
            LEFT JOIN 
                scenes s ON bl.scene_id = s.id
            LEFT JOIN
                monster_templates mt ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bl.monster_id, '_', 5), '_', -1) AS UNSIGNED) = mt.id
            WHERE 
                bl.id = :id
        ";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':id', $logId, PDO::PARAM_INT);
        $stmt->execute();
        
        $log = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$log) {
            throw new Exception('找不到指定的战斗日志');
        }

        // 添加原始怪物ID，方便调试
        $log['monster_instance_id'] = $log['monster_id'];

        // 添加调试信息
        if ($log['monster_id'] === null) {
            $log['debug_info'] = '怪物ID为空';
            $log['monster_name'] = '未知怪物';
        } else if ($log['monster_name'] === null) {
            // 尝试解析怪物ID格式（假设格式为 "monster_X_Y" 或 "X"，其中X是monster_templates中的ID）
            $monsterIdParts = explode('_', $log['monster_id']);
            $templateId = $monsterIdParts[1] ?? null;
            
            if (is_numeric($templateId)) {
                // 尝试直接查询怪物信息
                $monsterSql = "SELECT * FROM monster_templates WHERE id = :monster_id";
                $monsterStmt = $conn->prepare($monsterSql);
                $monsterStmt->bindValue(':monster_id', $templateId, PDO::PARAM_INT);
                $monsterStmt->execute();
                $monster = $monsterStmt->fetch(PDO::FETCH_ASSOC);
                
                if ($monster && $monster['name']) {
                    $log['monster_name'] = $monster['name'];
                    $log['monster_level'] = $monster['level'];
                    $log['monster_base_hp'] = $monster['base_hp'];
                    $log['monster_base_max_hp'] = $monster['base_max_hp'];
                    $log['monster_base_attack'] = $monster['base_attack'];
                    $log['monster_defense'] = $monster['defense'];
                    $log['monster_attack_speed'] = $monster['attack_speed'];
                    $log['monster_experience_reward'] = $monster['experience_reward'];
                    $log['monster_description'] = $monster['description'];
                    $log['fire_resistance'] = $monster['fire_resistance'];
                    $log['ice_resistance'] = $monster['ice_resistance'];
                    $log['wind_resistance'] = $monster['wind_resistance'];
                    $log['electric_resistance'] = $monster['electric_resistance'];
                    $log['strength'] = $monster['strength'];
                    $log['agility'] = $monster['agility'];
                    $log['constitution'] = $monster['constitution'];
                    $log['intelligence'] = $monster['intelligence'];
                    $log['debug_info'] = '通过解析怪物ID并单独查询找到怪物名称，模板ID: ' . $templateId;
                } else {
                    $log['monster_name'] = '未知怪物';
                    $log['debug_info'] = '解析出模板ID但在怪物表中未找到对应记录，模板ID: ' . $templateId;
                }
            } else {
                $log['monster_name'] = '未知怪物';
                $log['debug_info'] = '无法从怪物ID中解析出有效的模板ID，原始ID: ' . $log['monster_id'];
            }
        } else {
            $log['debug_info'] = '成功找到怪物名称';
        }
        
        // 返回结果
        $response = [
            'success' => true,
            'data' => $log
        ];
        
        header('Content-Type: application/json');
        echo json_encode($response);
        
    } catch (Exception $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => $e->getMessage()]);
    }
}

/**
 * 获取场景列表
 */
function getScenes() {
    try {
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        $sql = "
            SELECT DISTINCT 
                s.id, 
                s.name
            FROM 
                scenes s
            INNER JOIN 
                battle_logs bl ON s.id = bl.scene_id
            ORDER BY 
                s.name
        ";
        
        $stmt = $conn->prepare($sql);
        $stmt->execute();
        
        $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 返回结果
        $response = [
            'success' => true,
            'data' => $scenes
        ];
        
        header('Content-Type: application/json');
        echo json_encode($response);
        
    } catch (PDOException $e) {
        header('Content-Type: application/json');
        echo json_encode(['success' => false, 'message' => '数据库错误: ' . $e->getMessage()]);
    }
}

/**
 * 导出战斗日志
 */
function exportBattleLog() {
    try {
        if (empty($_GET['id'])) {
            throw new Exception('缺少日志ID参数');
        }
        
        $logId = (int)$_GET['id'];
        
        $db = Database::getInstance();
        $conn = $db->getConnection();
        
        $sql = "
            SELECT 
                bl.*,
                s.name as scene_name,
                COALESCE(mt.name, '未知怪物') as monster_name
            FROM 
                battle_logs bl
            LEFT JOIN 
                scenes s ON bl.scene_id = s.id
            LEFT JOIN
                monster_templates mt ON CAST(SUBSTRING_INDEX(SUBSTRING_INDEX(bl.monster_id, '_', 5), '_', -1) AS UNSIGNED) = mt.id
            WHERE 
                bl.id = :id
        ";
        
        $stmt = $conn->prepare($sql);
        $stmt->bindValue(':id', $logId, PDO::PARAM_INT);
        $stmt->execute();
        
        $log = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$log) {
            throw new Exception('找不到指定的战斗日志');
        }
        
        // 解析战斗日志
        $logEntries = json_decode($log['log_data'], true);
        if (!is_array($logEntries)) {
            $logEntries = ['无法解析战斗日志'];
        }
        
        // 解析参与者
        $participants = json_decode($log['participants'], true);
        if (!is_array($participants)) {
            $participants = [];
        }
        
        $participantNames = array_map(function($p) {
            return $p['name'] ?? '未知';
        }, $participants);
        
        // 生成文件名
        $filename = 'battle_log_' . $log['id'] . '_' . date('Ymd_His') . '.txt';
        
        // 设置响应头
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        
        // 输出日志内容
        echo "战斗日志 #" . $log['id'] . "\n";
        echo "===========================================\n\n";
        
        echo "基本信息:\n";
        echo "场景: " . ($log['scene_name'] ?? '未知') . "\n";
        echo "战斗结果: " . getBattleResultText($log['battle_result']) . "\n";
        echo "开始时间: " . $log['start_time'] . "\n";
        echo "结束时间: " . $log['end_time'] . "\n";
        echo "怪物名称: " . ($log['monster_name'] ?? '未知') . "\n\n";
        
        echo "参与玩家:\n";
        echo implode(", ", $participantNames) . "\n\n";
        
        echo "战斗日志:\n";
        echo "===========================================\n";
        foreach ($logEntries as $index => $entry) {
            echo ($index + 1) . ". " . $entry . "\n";
        }
        
    } catch (Exception $e) {
        header('Content-Type: text/plain');
        echo "错误: " . $e->getMessage();
    }
}

/**
 * 获取战斗结果文本
 */
function getBattleResultText($result) {
    $resultMap = [
        'victory' => '胜利',
        'defeated' => '失败',
        'flee' => '逃跑',
        'all_fled' => '全部逃跑',
        'last_player_left' => '最后玩家离开',
        'disconnect' => '连接断开'
    ];
    
    return isset($resultMap[$result]) ? $resultMap[$result] : '未知';
}

function clearOldLogs() {
    if (!isset($_POST['cleanup_date']) || empty($_POST['cleanup_date'])) {
        throw new Exception('必须提供清理日期');
    }

    $cleanupDate = $_POST['cleanup_date'];
    // 验证日期格式
    $d = DateTime::createFromFormat('Y-m-d', $cleanupDate);
    if (!$d || $d->format('Y-m-d') !== $cleanupDate) {
        throw new Exception('无效的日期格式');
    }

    $db = Database::getInstance();
    $conn = $db->getConnection();

    $sql = "DELETE FROM battle_logs WHERE DATE(start_time) < :cleanup_date";
    $stmt = $conn->prepare($sql);
    $stmt->bindValue(':cleanup_date', $cleanupDate, PDO::PARAM_STR);
    
    $stmt->execute();
    
    $deletedCount = $stmt->rowCount();

    sendJsonResponse(['success' => true, 'message' => "成功清理了 {$deletedCount} 条过期日志。"]);
} 