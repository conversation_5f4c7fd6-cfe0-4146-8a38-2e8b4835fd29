<?php
// admin/upload_image.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '方法不允许']);
    exit;
}

require_once '../config/Database.php';

// 配置
$uploadDir = '../uploads/announcements/';
$allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
$maxFileSize = 2 * 1024 * 1024; // 2MB
$maxWidth = 1200;
$maxHeight = 800;

// 创建上传目录
if (!file_exists($uploadDir)) {
    if (!mkdir($uploadDir, 0755, true)) {
        echo json_encode(['success' => false, 'message' => '无法创建上传目录']);
        exit;
    }
}

// 检查文件上传
if (!isset($_FILES['image']) || $_FILES['image']['error'] !== UPLOAD_ERR_OK) {
    $errorMessages = [
        UPLOAD_ERR_INI_SIZE => '文件大小超过系统限制',
        UPLOAD_ERR_FORM_SIZE => '文件大小超过表单限制',
        UPLOAD_ERR_PARTIAL => '文件只有部分被上传',
        UPLOAD_ERR_NO_FILE => '没有文件被上传',
        UPLOAD_ERR_NO_TMP_DIR => '找不到临时文件夹',
        UPLOAD_ERR_CANT_WRITE => '文件写入失败',
        UPLOAD_ERR_EXTENSION => '文件上传被扩展程序阻止'
    ];
    
    $error = $_FILES['image']['error'];
    $message = $errorMessages[$error] ?? '未知上传错误';
    
    echo json_encode(['success' => false, 'message' => $message]);
    exit;
}

$file = $_FILES['image'];
$originalName = $file['name'];
$tmpName = $file['tmp_name'];
$fileSize = $file['size'];
$mimeType = $file['type'];

// 验证文件类型
if (!in_array($mimeType, $allowedTypes)) {
    echo json_encode(['success' => false, 'message' => '不支持的文件类型，只允许JPG、PNG、GIF格式']);
    exit;
}

// 验证文件大小
if ($fileSize > $maxFileSize) {
    echo json_encode(['success' => false, 'message' => '文件大小不能超过2MB']);
    exit;
}

// 验证文件内容（防止伪造MIME类型）
$imageInfo = getimagesize($tmpName);
if ($imageInfo === false) {
    echo json_encode(['success' => false, 'message' => '无效的图片文件']);
    exit;
}

$imageWidth = $imageInfo[0];
$imageHeight = $imageInfo[1];
$imageType = $imageInfo[2];

// 验证图片类型
$validImageTypes = [IMAGETYPE_JPEG, IMAGETYPE_PNG, IMAGETYPE_GIF];
if (!in_array($imageType, $validImageTypes)) {
    echo json_encode(['success' => false, 'message' => '不支持的图片格式']);
    exit;
}

// 生成安全的文件名
$extension = strtolower(pathinfo($originalName, PATHINFO_EXTENSION));
$fileName = uniqid('announcement_', true) . '.' . $extension;
$filePath = $uploadDir . $fileName;

// 如果图片太大，进行压缩
if ($imageWidth > $maxWidth || $imageHeight > $maxHeight) {
    $resizedImage = resizeImage($tmpName, $imageType, $maxWidth, $maxHeight);
    if ($resizedImage === false) {
        echo json_encode(['success' => false, 'message' => '图片处理失败']);
        exit;
    }
    
    // 保存压缩后的图片
    $saveResult = false;
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $saveResult = imagejpeg($resizedImage, $filePath, 85);
            break;
        case IMAGETYPE_PNG:
            $saveResult = imagepng($resizedImage, $filePath, 8);
            break;
        case IMAGETYPE_GIF:
            $saveResult = imagegif($resizedImage, $filePath);
            break;
    }
    
    imagedestroy($resizedImage);
    
    if (!$saveResult) {
        echo json_encode(['success' => false, 'message' => '图片保存失败']);
        exit;
    }
} else {
    // 直接移动文件
    if (!move_uploaded_file($tmpName, $filePath)) {
        echo json_encode(['success' => false, 'message' => '文件保存失败']);
        exit;
    }
}

// 记录到数据库
try {
    $pdo = Database::getInstance()->getConnection();
    
    $stmt = $pdo->prepare("
        INSERT INTO announcement_images 
        (original_filename, stored_filename, file_path, file_size, mime_type, width, height, upload_ip, created_by) 
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $uploadIp = $_SERVER['REMOTE_ADDR'] ?? '';
    $adminUsername = $_SESSION['admin_username'] ?? 'admin';
    
    // 重新获取图片信息（可能已经被压缩）
    $finalImageInfo = getimagesize($filePath);
    $finalWidth = $finalImageInfo[0];
    $finalHeight = $finalImageInfo[1];
    $finalSize = filesize($filePath);
    
    $result = $stmt->execute([
        $originalName,
        $fileName,
        $filePath,
        $finalSize,
        $mimeType,
        $finalWidth,
        $finalHeight,
        $uploadIp,
        $adminUsername
    ]);
    
    if ($result) {
        // 返回相对URL路径
        $imageUrl = 'uploads/announcements/' . $fileName;
        echo json_encode([
            'success' => true,
            'message' => '图片上传成功',
            'url' => $imageUrl,
            'filename' => $fileName,
            'width' => $finalWidth,
            'height' => $finalHeight
        ]);
    } else {
        // 删除已上传的文件
        unlink($filePath);
        echo json_encode(['success' => false, 'message' => '数据库记录失败']);
    }
    
} catch (Exception $e) {
    // 删除已上传的文件
    if (file_exists($filePath)) {
        unlink($filePath);
    }
    
    error_log('图片上传数据库错误: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '数据库操作失败']);
}

/**
 * 调整图片大小
 */
function resizeImage($sourcePath, $imageType, $maxWidth, $maxHeight) {
    // 创建源图片资源
    switch ($imageType) {
        case IMAGETYPE_JPEG:
            $sourceImage = imagecreatefromjpeg($sourcePath);
            break;
        case IMAGETYPE_PNG:
            $sourceImage = imagecreatefrompng($sourcePath);
            break;
        case IMAGETYPE_GIF:
            $sourceImage = imagecreatefromgif($sourcePath);
            break;
        default:
            return false;
    }
    
    if ($sourceImage === false) {
        return false;
    }
    
    $sourceWidth = imagesx($sourceImage);
    $sourceHeight = imagesy($sourceImage);
    
    // 计算新尺寸
    $ratio = min($maxWidth / $sourceWidth, $maxHeight / $sourceHeight);
    $newWidth = intval($sourceWidth * $ratio);
    $newHeight = intval($sourceHeight * $ratio);
    
    // 创建新图片
    $newImage = imagecreatetruecolor($newWidth, $newHeight);
    
    // 保持透明度（PNG和GIF）
    if ($imageType === IMAGETYPE_PNG || $imageType === IMAGETYPE_GIF) {
        imagealphablending($newImage, false);
        imagesavealpha($newImage, true);
        $transparent = imagecolorallocatealpha($newImage, 255, 255, 255, 127);
        imagefill($newImage, 0, 0, $transparent);
    }
    
    // 调整大小
    $result = imagecopyresampled(
        $newImage, $sourceImage,
        0, 0, 0, 0,
        $newWidth, $newHeight,
        $sourceWidth, $sourceHeight
    );
    
    imagedestroy($sourceImage);
    
    if ($result === false) {
        imagedestroy($newImage);
        return false;
    }
    
    return $newImage;
}
?>
