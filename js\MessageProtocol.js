class MessageProtocol {
    // A. Bi-directional
    static PING                             = 0x00;
    static PONG                             = 0x01;

    // B. Client to Server (C2S)
    static C2S_REGISTER                     = 0x08;
    static C2S_LOGIN                        = 0x10;
    static C2S_ENTER_SCENE                  = 0x11;
    static C2S_RESUME_SESSION               = 0x12;
    static C2S_LOGOUT                       = 0x13;
    static C2S_HEAL                         = 0x14;
    static C2S_GET_SCENES                   = 0x15;
    static C2S_RESET_SCENE                  = 0x16;
    static C2S_REVIVE                       = 0x17;
    static C2S_START_BATTLE                 = 0x18;
    static C2S_BATTLE_ACTION                = 0x19; // Player attacks
    static C2S_FLEE_BATTLE                  = 0x1A;
    static C2S_MOVE                         = 0x1B;
    static C2S_MONSTER_ATTACK               = 0x1C; // 保留，但在服务端ATB系统中不再使用
    static C2S_GET_INVENTORY                = 0x1D;
    static C2S_USE_ITEM                     = 0x1E;
    static C2S_EQUIP_ITEM                   = 0x1F;
    static C2S_UNEQUIP_ITEM                 = 0x20;
    static C2S_SOCKET_GEM                   = 0x21;
    static C2S_GET_MONSTER_DETAILS          = 0x22;
    static C2S_GET_PLAYER_DETAILS           = 0x23;
    static C2S_PICKUP_ITEM                  = 0x24;
    static C2S_ALLOCATE_POTENTIAL_POINTS    = 0x25;
    static C2S_GET_COMBAT_POTION_CONFIG     = 0x26;
    static C2S_SET_COMBAT_POTION_CONFIG     = 0x27;
    static C2S_GET_PLAYER_SKILLS            = 0x28;
    static C2S_GET_BATTLE_SKILLS            = 0x29;
    static C2S_GET_BUILDING_DATA            = 0x2A;
    static C2S_BUY_ITEM                     = 0x2B;
    static C2S_SELL_ITEM                    = 0x2C;
    static C2S_REVIVE_IN_BUILDING           = 0x2D;
    static C2S_TELEPORT                     = 0x2E;
    static C2S_TALK_TO_NPC                  = 0x2F;
    static C2S_CONTINUE_DIALOGUE            = 0x30;
    static C2S_SELECT_DIALOGUE_OPTION       = 0x31;
    static C2S_GET_QUEST_LIST               = 0x32;
    static C2S_ACCEPT_QUEST                 = 0x33;
    static C2S_COMPLETE_QUEST               = 0x34;
    static C2S_ABANDON_QUEST                = 0x35;
    static C2S_VIEW_NPC_DETAIL              = 0x36;
    static C2S_GET_RECIPES                  = 0x37;
    static C2S_CRAFT_ITEM                   = 0x38;
    static C2S_GET_REFINE_MATERIALS         = 0x39;
    static C2S_REFINE_ITEM                  = 0x3A;
    static C2S_DROP_ITEM                    = 0x3B;
    static C2S_BIND_ITEM                    = 0x3C;
    static C2S_PRELOAD_DIALOGUE             = 0x3D;
    static C2S_BUY_DIAMOND_ITEM             = 0x3E;
    static C2S_GET_REFINE_COST_ESTIMATE     = 0x3F;

    // PVP相关协议
    static C2S_PVP_CHALLENGE                = 0x50;
    static C2S_PVP_ACCEPT_CHALLENGE         = 0x51;
    static C2S_PVP_DECLINE_CHALLENGE        = 0x52;
    static C2S_PVP_SET_INTENTION            = 0x53;
    static C2S_PVP_ACTION                   = 0x54;
    static C2S_PVP_SURRENDER                = 0x55;
    static C2S_GET_PVP_LEADERBOARD          = 0x56;
    static C2S_GET_PVP_STATS                = 0x57;
    static C2S_PVP_DIRECT_ATTACK            = 0x58;

    // 聊天相关协议
    static C2S_SEND_PUBLIC_CHAT             = 0x60;
    static C2S_GET_PUBLIC_CHAT              = 0x61;
    static C2S_SEND_PRIVATE_CHAT            = 0x62;
    static C2S_GET_PRIVATE_CHAT             = 0x63;
    static C2S_GET_CHAT_CONTACTS            = 0x64;
    static C2S_DELETE_CHAT_CONTACT          = 0x65;
    static C2S_MARK_MESSAGES_READ           = 0x66;
    static C2S_CRAFT_GEM                    = 0x67;

    // 仓库相关协议
    static C2S_WAREHOUSE_DEPOSIT            = 0x68; // 存入物品到仓库
    static C2S_WAREHOUSE_WITHDRAW           = 0x69; // 从仓库取出物品
    static C2S_WAREHOUSE_EXPAND             = 0x6A; // 扩容仓库
    static C2S_GET_GEM_RECIPES              = 0x6B;

    // 属性重修相关协议
    static C2S_ATTRIBUTE_RESET              = 0x6C; // 属性重修

    // 公告相关协议
    static C2S_GET_LATEST_ANNOUNCEMENT      = 0x6D; // 获取最新公告
    static C2S_GET_ANNOUNCEMENT_DETAIL      = 0x6E; // 获取公告详情
    static C2S_GET_ANNOUNCEMENT_LIST        = 0x6F; // 获取公告列表

    // 兑换码相关协议
    static C2S_REDEEM_CODE                  = 0x70; // 兑换码兑换

    // 排行榜相关协议
    static C2S_GET_LEVEL_RANKING            = 0x71;
    static C2S_GET_HERO_RANKING             = 0x72;
    static C2S_GET_VILLAIN_RANKING          = 0x73;

    // 交易相关协议
    static C2S_TRADE_REQUEST                = 0x74; // 发起交易请求
    static C2S_TRADE_RESPOND                = 0x75; // 响应交易请求
    static C2S_TRADE_ADD_ITEM               = 0x76; // 添加交易物品
    static C2S_TRADE_REMOVE_ITEM            = 0x77; // 移除交易物品
    static C2S_TRADE_ADD_CURRENCY           = 0x78; // 添加交易货币
    static C2S_TRADE_CONFIRM                = 0x79; // 确认交易
    static C2S_TRADE_FINAL_CONFIRM          = 0x7B; // 最终确认交易
    static C2S_TRADE_CANCEL                 = 0x7C; // 取消交易

    // 装备凝练榜相关协议
    static C2S_GET_REFINE_LEADERBOARD       = 0x7A; // 获取装备凝练榜

    // C. Server to Client (S2C)
    static S2C_REGISTER_SUCCESS             = 0x7D;
    static S2C_LOGIN_SUCCESS                = 0x80;
    static S2C_ERROR                        = 0x81;
    static S2C_SCENES_LIST                  = 0x82;
    static S2C_SCENE_ENTERED                = 0x83;
    static S2C_INFO_MESSAGE                 = 0x84;
    static S2C_SCENE_PLAYER_CHANGE          = 0x85;
    static S2C_PLAYER_HEALED                = 0x87;
    static S2C_SCENE_RESET                  = 0x88;
    static S2C_PLAYER_REVIVED               = 0x89;
    static S2C_BATTLE_STARTED               = 0x90;
    static S2C_BATTLE_UPDATE                = 0x91; // 包含ATB状态更新
    static S2C_BATTLE_TURN_UPDATE           = 0x91; // 与 BATTLE_UPDATE 相同值
    static S2C_BATTLE_ENDED                 = 0x92;
    static S2C_BATTLE_LOG                   = 0x93;
    static S2C_INVENTORY_DATA               = 0x94;
    static S2C_PLAYER_ATTRIBUTE_UPDATE      = 0x95;
    static S2C_MONSTER_DETAILS              = 0x96;
    static S2C_PLAYER_DETAILS               = 0x97;
    static S2C_ATB_STATUS_UPDATE            = 0x98;
    static S2C_COMBAT_POTION_CONFIG_DATA    = 0x99;
    static S2C_PLAYER_SKILLS_LIST           = 0x9A;
    static S2C_BATTLE_SKILLS_LIST           = 0x9B;
    static S2C_BUILDING_DATA                = 0x9C;
    static S2C_SHOP_NOTIFICATION            = 0x9D;
    static S2C_NPC_DIALOGUE                 = 0x9E;
    static S2C_QUEST_LIST                   = 0x9F;
    static S2C_QUEST_UPDATE                 = 0xA0;
    static S2C_SCENE_NPCS                   = 0xA1;
    static S2C_NPC_DETAIL                   = 0xA2;
    static S2C_RECIPES_DATA                 = 0xA3;
    static S2C_CRAFT_RESULT                 = 0xA4;
    static S2C_REFINE_MATERIALS_DATA        = 0xA5;
    static S2C_REFINE_RESULT                = 0xA6;
    static S2C_ITEM_DROPPED                 = 0xA7;
    static S2C_ITEM_BOUND                   = 0xA8;
    static S2C_REFINE_COST_ESTIMATE         = 0xA9;
    static S2C_GEM_RECIPES_DATA             = 0xB0;
    static S2C_GEM_CRAFT_RESULT             = 0xB1;
    static S2C_SUCCESS                      = 0xB2; // 通用成功消息

    // 公告相关响应
    static S2C_LATEST_ANNOUNCEMENT          = 0xB3; // 最新公告数据
    static S2C_ANNOUNCEMENT_DETAIL          = 0xB4; // 公告详情数据
    static S2C_ANNOUNCEMENT_LIST            = 0xB5; // 公告列表数据

    // 兑换码相关响应
    static S2C_REDEEM_CODE_RESULT           = 0xB6; // 兑换码兑换结果

    // PVP相关响应
    static S2C_PVP_CHALLENGE                = 0xD0;
    static S2C_PVP_CHALLENGE_RESPONSE       = 0xD1;
    static S2C_PVP_BATTLE_STARTED           = 0xD2;
    static S2C_PVP_BATTLE_UPDATE            = 0xD3;
    static S2C_PVP_ATB_STATUS_UPDATE        = 0xD4;
    static S2C_PVP_BATTLE_ENDED             = 0xD5;
    static S2C_PVP_LEADERBOARD              = 0xD6;
    static S2C_PVP_STATS                    = 0xD7;

    // 排行榜相关响应
    static S2C_LEVEL_RANKING                = 0xD8;
    static S2C_HERO_RANKING                 = 0xD9;
    static S2C_VILLAIN_RANKING              = 0xDA;

    // 聊天相关响应
    static S2C_PUBLIC_CHAT_MESSAGE          = 0xE0;
    static S2C_PUBLIC_CHAT_HISTORY          = 0xE1;
    static S2C_PRIVATE_CHAT_MESSAGE         = 0xE2;
    static S2C_PRIVATE_CHAT_HISTORY         = 0xE3;
    static S2C_CHAT_CONTACTS_LIST           = 0xE4;
    static S2C_CHAT_CONTACT_UPDATED         = 0xE5;

    // 交易相关响应
    static S2C_TRADE_REQUEST_RECEIVED       = 0xF0; // 收到交易请求
    static S2C_TRADE_STARTED                = 0xF1; // 交易开始
    static S2C_TRADE_UPDATE                 = 0xF2; // 交易内容更新
    static S2C_TRADE_CONFIRMED              = 0xF3; // 交易确认状态更新
    static S2C_TRADE_FINAL_CONFIRMED        = 0xF7; // 最终确认状态更新
    static S2C_TRADE_COMPLETED              = 0xF4; // 交易完成
    static S2C_TRADE_CANCELLED              = 0xF5; // 交易取消
    static S2C_TRADE_ERROR                  = 0xF6; // 交易错误

    // 装备凝练榜相关响应
    static S2C_REFINE_LEADERBOARD           = 0xF8; // 装备凝练榜数据

    //回城
    static C2S_REVIVE_CITY                  = 0xF9; // 回城
}