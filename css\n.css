/* NPC相关样式 */

/* 场景中的NPC列表样式 */
#sceneNpcs {
    margin: 5px 0;
    padding: 5px 0;
    border-top: 1px solid var(--border-color);
}

.scene-npc-group {
    margin: 5px 0;
    padding: 2px 5px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.scene-npc-group:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.npc-link {
    text-decoration: none;
    color: var(--main-color);
    font-weight: bold;
}

/* .npc-level 样式已移除，因为不再显示NPC等级 */

.npc-position {
    font-size: 12px;
    color: #666;
    margin-left: 5px;
    font-style: italic;
}

.npc-type-icon {
    margin-right: 3px;
    font-size: 14px;
}

/* NPC类型样式 */
.merchant-npc .npc-link {
    color: #cc9900;
}

.quest-npc .npc-link {
    color: #3366cc;
}

.merchant-quest-npc .npc-link {
    color: #993399;
}

/* NPC详情视图样式 */
#npcDetailView {
    border: 1px solid var(--border-color);
    padding: 10px;
    background-color: var(--bg-color);
}

.npc-detail-title {
    margin-top: 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.npc-detail-content {
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 20px;
}

.npc-info-row {
    margin-bottom: 8px;
}

.npc-info-label {
    font-weight: bold;
    margin-right: 5px;
    color: #555;
}

.npc-info-value {
    color: #333;
}

.npc-info-description {
    font-style: italic;
    color: #555;
    display: block;
    margin-top: 10px;
    padding: 5px;
    background-color: #f9f9f9;
    border-radius: 3px;
    border-left: 3px solid #ddd;
}

.npc-type-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: white;
    margin-right: 5px;
}

.merchant-tag {
    background-color: #cc9900;
}

.quest-tag {
    background-color: #3366cc;
}

.npc-detail-actions {
    text-align: center;
    margin-top: 15px;
    min-height: 30px;
}

/* 对话界面样式 */
#dialogueView {
    border: 1px solid var(--border-color);
    padding: 10px;
    background-color: var(--bg-color);
    display: flex;
    flex-direction: column;
    max-height: 90vh; /* 改为视口高度的90%，更灵活 */
    overflow: hidden;
}

#dialogueContainer {
    border: 1px solid var(--border-color);
    border-radius: 5px;
    padding: 0 !important;
    background-color: #f9f9f9;
    margin-bottom: 15px;
    overflow-y: auto !important;
    overflow-x: hidden;
    flex: 1 1 auto !important;
    min-height: 200px !important; /* 减小最小高度 */
    max-height: 50vh !important; /* 设置为视口高度的50%，确保有滚动功能 */
    display: block !important;
    position: relative;
}

.dialogue-wrapper {
    padding: 10px;
    min-height: 100%;
    display: flex;
    flex-direction: column;
}

.npc-dialogue {
    background-color: #e6f3ff;
    border-radius: 10px;
    padding: 8px 12px;
    margin-bottom: 10px;
    position: relative;
    max-width: 80%;
    margin-left: 5px;
    border-top-left-radius: 0;
    word-break: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.player-dialogue {
    background-color: #dcf8c6;
    border-radius: 10px;
    padding: 8px 12px;
    margin-bottom: 10px;
    position: relative;
    max-width: 80%;
    margin-left: auto;
    margin-right: 5px;
    border-top-right-radius: 0;
    word-break: break-word;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.current-dialogue {
    border: 1px solid #3399ff;
    animation: highlight-dialogue 1s ease;
}

@keyframes highlight-dialogue {
    0% { background-color: #b3d9ff; }
    100% { background-color: #e6f3ff; }
}

.dialogue-title {
    margin-top: 0;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 5px;
    margin-bottom: 10px;
}

.dialogue-options {
    margin-top: 15px;
    flex-shrink: 0;
    max-height: 40vh; /* 限制选项区域最大高度为视口高度的40% */
    overflow-y: auto; /* 如果选项太多，允许滚动 */
}

.dialogue-options-title {
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
}

.dialogue-options-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
    max-height: 35vh; /* 选项列表的最大高度 */
    overflow-y: auto; /* 允许选项列表滚动 */
}

.dialogue-option-btn {
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 5px;
    padding: 8px 12px;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dialogue-option-btn:hover {
    background-color: #e0e0e0;
}

.dialogue-continue-btn {
    background-color: #3399ff;
    color: white;
    border: none;
    border-radius: 5px;
    padding: 8px 15px;
    cursor: pointer;
    display: block;
    margin: 0 auto;
    transition: background-color 0.2s;
}

.dialogue-continue-btn:hover {
    background-color: #2277dd;
}

/* 确保对话框中的滚动条正确显示 */
#dialogueContainer::-webkit-scrollbar,
.dialogue-options::-webkit-scrollbar,
.dialogue-options-list::-webkit-scrollbar {
    width: 6px;
}

#dialogueContainer::-webkit-scrollbar-track,
.dialogue-options::-webkit-scrollbar-track,
.dialogue-options-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

#dialogueContainer::-webkit-scrollbar-thumb,
.dialogue-options::-webkit-scrollbar-thumb,
.dialogue-options-list::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 3px;
}

#dialogueContainer::-webkit-scrollbar-thumb:hover,
.dialogue-options::-webkit-scrollbar-thumb:hover,
.dialogue-options-list::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 新增：NPC装备相关样式 */
.npc-equipment-section {
    margin-top: 15px;
}

.npc-equipment-section h4 {
    margin-bottom: 10px;
    color: #333;
    border-bottom: 1px solid #eee;
    padding-bottom: 5px;
}

.npc-equipment-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.npc-equipment-item {
    display: flex;
    align-items: center;
    background-color: #f9f9f9;
    border-radius: 4px;
    padding: 5px 8px;
    transition: background-color 0.2s;
}

.npc-equipment-item:hover {
    background-color: #f0f0f0;
}

.equipment-slot-name {
    font-weight: bold;
    color: #555;
    width: 60px;
    flex-shrink: 0;
}

.equipment-item-name {
    color: #3366cc;
    cursor: pointer;
}

.equipment-item-name:hover {
    text-decoration: underline;
}

/* 物品信息样式 */
.item-info-row {
    margin-bottom: 8px;
}

.item-info-label {
    font-weight: bold;
    color: #555;
    margin-right: 5px;
}

.item-info-description {
    font-style: italic;
    color: #555;
    display: block;
    margin-top: 5px;
    padding: 5px;
    background-color: #f9f9f9;
    border-radius: 3px;
    border-left: 3px solid #ddd;
}

.item-equipped-tag {
    display: inline-block;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 12px;
    color: white;
    background-color: #3399ff;
} 