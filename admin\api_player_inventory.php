<?php
// admin/api_player_inventory.php
require_once '../config/Database.php';
// 移除RedisManager依赖
// require_once '../config/RedisManager.php';
require_once 'auth.php';

header('Content-Type: application/json');

$db = Database::getInstance();
$conn = $db->getConnection();

// Redis连接辅助函数
function getRedisConnection() {
    static $redis = null;
    if ($redis === null) {
        try {
            $redis = new Redis();
            $redis->connect('127.0.0.1', 6379);
        } catch (Exception $e) {
            error_log("Redis连接失败: " . $e->getMessage());
            return null;
        }
    }
    return $redis;
}

// 从Redis检查玩家是否在线
function isPlayerOnline($playerId) {
    $redis = getRedisConnection();
    if (!$redis) return false;
    
    // 检查玩家是否有活跃连接
    $fd = $redis->get("player_fd:{$playerId}");
    return !empty($fd);
}

// 获取所有在线玩家ID
function getOnlinePlayers() {
    $redis = getRedisConnection();
    if (!$redis) return [];
    
    $onlinePlayers = [];
    // 获取所有在线玩家键
    $keys = $redis->keys("player_fd:*");
    
    foreach ($keys as $key) {
        $playerIdParts = explode(':', $key);
        if (count($playerIdParts) > 1) {
            $onlinePlayers[] = $playerIdParts[1];
        }
    }
    
    return $onlinePlayers;
}

$action = $_REQUEST['action'] ?? ''; // 使用 $_REQUEST 同时处理 GET 和 POST

try {
    switch ($action) {
        case 'get_player_details':
            $playerId = $_GET['player_id'] ?? 0;
            if (!$playerId) throw new Exception('未提供玩家ID。');
            
            // 获取玩家基本信息和属性
            $stmt = $conn->prepare("
                SELECT a.*, pa.*,
                    s.name as scene_name,
                    DATE_FORMAT(pa.logindate, '%Y-%m-%d %H:%i:%s') as formatted_logindate
                FROM accounts a
                LEFT JOIN player_attributes pa ON a.id = pa.account_id
                LEFT JOIN scenes s ON pa.current_scene_id = s.id
                WHERE a.id = ?
            ");
            $stmt->execute([$playerId]);
            $player = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$player) {
                throw new Exception('找不到指定ID的玩家。');
            }
            
            // 检查Redis中的在线状态
            $player['is_online'] = isPlayerOnline($playerId);
            
            // 获取玩家任务信息
            $questStmt = $conn->prepare("
                SELECT pq.id, pq.quest_id, pq.status, pq.objectives_progress, pq.started_at, pq.completed_at,
                       q.title as quest_title
                FROM player_quests pq
                JOIN quests q ON pq.quest_id = q.id
                WHERE pq.player_id = ?
            ");
            $questStmt->execute([$playerId]);
            $quests = $questStmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 获取玩家技能信息
            $skillStmt = $conn->prepare("
                SELECT ps.id, ps.skill_template_id, ps.skill_level, ps.last_used_at,
                       st.name as skill_name, st.icon as skill_icon
                FROM player_skills ps
                JOIN skill_templates st ON ps.skill_template_id = st.id
                WHERE ps.player_id = ?
            ");
            $skillStmt->execute([$playerId]);
            $skills = $skillStmt->fetchAll(PDO::FETCH_ASSOC);
            
            echo json_encode([
                'success' => true, 
                'data' => [
                    'player' => $player,
                    'quests' => $quests,
                    'skills' => $skills
                ]
            ]);
            break;

        case 'update_player_attribute':
            $playerId = $_POST['player_id'] ?? 0;
            $attribute = $_POST['attribute'] ?? '';
            $value = $_POST['value'] ?? '';
            
            if (!$playerId || !$attribute) {
                throw new Exception('缺少必要的参数');
            }
            
            // 安全检查：确保只能更新允许的属性
            $allowedAttributes = [
                'level', 'experience', 'hp', 'max_hp', 'mp', 'max_mp', 'attack', 'defense',
                'attack_speed', 'strength', 'agility', 'constitution', 'intelligence',
                'potential_points', 'knowledge_points', 'gold', 'diamonds',
                'fire_resistance', 'ice_resistance', 'wind_resistance', 'electric_resistance',
                'current_scene_id', 'karma', 'fire_damage', 'ice_damage', 'wind_damage',
                'electric_damage', 'rage', 'dodge_bonus', 'current_job_id'
            ];
            
            if (!in_array($attribute, $allowedAttributes)) {
                throw new Exception('不允许修改该属性');
            }
            
            $stmt = $conn->prepare("UPDATE player_attributes SET $attribute = ? WHERE account_id = ?");
            $stmt->execute([$value, $playerId]);
            
            echo json_encode(['success' => true, 'message' => '属性已成功更新']);
            break;
            
        case 'list_players':
            // 获取玩家列表，包括在线状态和位置信息
            $stmt = $conn->prepare("
                SELECT a.id, a.username, a.created_at,
                       pa.level, pa.current_scene_id, pa.hp, pa.max_hp,
                       s.name as scene_name,
                       DATE_FORMAT(pa.logindate, '%Y-%m-%d %H:%i:%s') as formatted_logindate
                FROM accounts a
                LEFT JOIN player_attributes pa ON a.id = pa.account_id
                LEFT JOIN scenes s ON pa.current_scene_id = s.id
                ORDER BY a.username ASC
            ");
            $stmt->execute();
            $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            // 获取在线状态信息
            $onlinePlayers = getOnlinePlayers();
            
            // 添加在线状态信息
            foreach ($players as &$player) {
                $player['is_online'] = in_array($player['id'], $onlinePlayers);
            }
            
            // 按在线状态排序
            usort($players, function($a, $b) {
                if ($a['is_online'] == $b['is_online']) {
                    return strcmp($a['username'], $b['username']);
                }
                return $b['is_online'] - $a['is_online'];
            });
            
            echo json_encode(['success' => true, 'data' => $players]);
            break;

        case 'get_all_players':
            try {
                $stmt = $conn->prepare("
                    SELECT a.id, a.username, pa.level, pa.current_scene_id,
                           s.name as scene_name,
                           DATE_FORMAT(pa.logindate, '%Y-%m-%d %H:%i:%s') as formatted_logindate
                    FROM accounts a
                    LEFT JOIN player_attributes pa ON a.id = pa.account_id
                    LEFT JOIN scenes s ON pa.current_scene_id = s.id
                    ORDER BY a.username ASC
                ");
                $stmt->execute();
                $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                // 获取在线状态信息
                $onlinePlayers = getOnlinePlayers();
                
                // 添加在线状态信息
                foreach ($players as &$player) {
                    $player['is_online'] = in_array($player['id'], $onlinePlayers);
                }
                
                // 按在线状态排序
                usort($players, function($a, $b) {
                    if ($a['is_online'] == $b['is_online']) {
                        return strcmp($a['username'], $b['username']);
                    }
                    return $b['is_online'] - $a['is_online'];
                });
                
                echo json_encode(['success' => true, 'data' => $players]);
            } catch (PDOException $e) {
                echo json_encode(['success' => false, 'message' => '数据库错误: ' . $e->getMessage()]);
            }
            break;

        case 'get_inventory':
            $playerId = $_GET['player_id'] ?? 0;
            if (!$playerId) throw new Exception('未提供玩家ID。');

            // 获取玩家货币信息
            $currencyStmt = $conn->prepare("SELECT gold, diamonds FROM player_attributes WHERE account_id = ?");
            $currencyStmt->execute([$playerId]);
            $currencies = $currencyStmt->fetch(PDO::FETCH_ASSOC);
            if (!$currencies) {
                $currencies = ['gold' => 0, 'diamonds' => 0]; // 如果没有属性记录，则默认为0
            }

            $stmt = $conn->prepare("
                SELECT 
                    inv.id as inventory_id, inv.item_template_id, inv.quantity, inv.is_equipped, inv.instance_data,
                    it.name, it.category, it.stackable, it.effects,
                    ed.stats
                FROM player_inventory inv
                JOIN item_templates it ON inv.item_template_id = it.id
                LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
                WHERE inv.player_id = ?
                ORDER BY inv.is_equipped DESC, it.name
            ");
            $stmt->execute([$playerId]);
            $inventory = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $equipped = [];
            $backpack = [];
            foreach ($inventory as $item) {
                if ($item['is_equipped']) {
                    $equipped[] = $item;
                } else {
                    $backpack[] = $item;
                }
            }

            echo json_encode(['success' => true, 'data' => ['equipped' => $equipped, 'backpack' => $backpack, 'currencies' => $currencies]]);
            break;

        case 'add_item':
            $playerId = $_POST['player_id'] ?? 0;
            $itemTemplateId = $_POST['item_template_id'] ?? 0;
            $quantity = max(1, (int)($_POST['quantity'] ?? 1));
            
            if (!$playerId || !$itemTemplateId) throw new Exception('缺少玩家ID或物品模板ID。');

            $conn->beginTransaction();
            try {
                // 这是从 LootManager 中借鉴并适配的 addItemToPlayer 逻辑
                $itemStmt = $conn->prepare("SELECT stackable, max_stack FROM item_templates WHERE id = ?");
                $itemStmt->execute([$itemTemplateId]);
                $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
                $itemStmt->closeCursor();

                if (!$item) {
                    throw new Exception("无效的物品模板ID: {$itemTemplateId}");
                }

                $isStackable = (bool)$item['stackable'];
                $maxStack = (int)$item['max_stack'];
                $quantityToAdd = $quantity;

                if ($isStackable && $quantityToAdd > 0) {
                    $stacksStmt = $conn->prepare(
                        "SELECT id, quantity FROM player_inventory 
                         WHERE player_id = ? AND item_template_id = ? AND quantity < ? AND is_equipped = 0 
                         ORDER BY quantity DESC FOR UPDATE"
                    );
                    $stacksStmt->execute([$playerId, $itemTemplateId, $maxStack]);
                    $existingStacks = $stacksStmt->fetchAll(PDO::FETCH_ASSOC);
                    $stacksStmt->closeCursor();

                    foreach ($existingStacks as $stack) {
                        if ($quantityToAdd <= 0) break;
                        $spaceAvailable = $maxStack - $stack['quantity'];
                        $amountToFill = min($quantityToAdd, $spaceAvailable);
                        $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?");
                        $updateStmt->execute([$amountToFill, $stack['id']]);
                        $quantityToAdd -= $amountToFill;
                    }
                }

                while ($quantityToAdd > 0) {
                    if ($isStackable) {
                        $newStackQuantity = min($quantityToAdd, $maxStack);
                        $insertStmt = $conn->prepare("INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data) VALUES (?, ?, ?, ?)");
                        $insertStmt->execute([$playerId, $itemTemplateId, $newStackQuantity, null]);
                        $quantityToAdd -= $newStackQuantity;
                    } else {
                        // 非堆叠物品，逐个添加
                        $insertStmt = $conn->prepare("INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data) VALUES (?, ?, 1, ?)");
                        for ($i = 0; $i < $quantityToAdd; $i++) {
                            $insertStmt->execute([$playerId, $itemTemplateId, null]);
                        }
                        $quantityToAdd = 0; // 循环结束
                    }
                }
                
                $conn->commit();
                echo json_encode(['success' => true, 'message' => '物品已成功添加到玩家背包。']);
            } catch (Exception $e) {
                $conn->rollBack();
                throw $e;
            }
            break;

        case 'update_item_quantity':
            $inventoryId = $_POST['inventory_id'] ?? 0;
            $quantity = max(1, (int)($_POST['quantity'] ?? 1));

            if (!$inventoryId) throw new Exception('未提供库存ID。');

            $stmt = $conn->prepare("UPDATE player_inventory SET quantity = ? WHERE id = ?");
            $stmt->execute([$quantity, $inventoryId]);
            
            echo json_encode(['success' => true, 'message' => '物品数量已更新。']);
            break;

        case 'delete_item':
            $playerId = $_POST['player_id'] ?? 0;
            $inventoryId = $_POST['inventory_id'] ?? 0;
            $quantityToDelete = $_POST['quantity'] ?? 0;

            if (!$playerId || !$inventoryId || $quantityToDelete <= 0) {
                throw new Exception("无效的删除请求参数。");
            }

            $conn = $db->getConnection();
            $conn->beginTransaction();

            try {
                // Lock the row for update
                $stmt = $conn->prepare("SELECT quantity, item_template_id FROM player_inventory WHERE id = ? AND player_id = ? FOR UPDATE");
                $stmt->execute([$inventoryId, $playerId]);
                $item = $stmt->fetch(PDO::FETCH_ASSOC);

                if (!$item) {
                    throw new Exception("在玩家背包中找不到该物品。");
                }

                if ($quantityToDelete >= $item['quantity']) {
                    // Delete the item row completely
                    $deleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                    $deleteStmt->execute([$inventoryId]);
                } else {
                    // Just update the quantity
                    $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?");
                    $updateStmt->execute([$quantityToDelete, $inventoryId]);
                }

                $conn->commit();
                $response = ['success' => true, 'message' => '物品已成功删除。'];
                echo json_encode($response);

            } catch (Exception $e) {
                $conn->rollBack();
                throw $e; // Re-throw to be caught by the main catch block
            }
            break;
            
        case 'get_all_item_templates':
             $stmt = $conn->prepare("SELECT id, name, category FROM item_templates ORDER BY category, name");
             $stmt->execute();
             $templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
             echo json_encode(['success' => true, 'data' => $templates]);
            break;
            
        case 'get_all_scenes':
            $stmt = $conn->query("SELECT id, name, description FROM scenes ORDER BY name");
            $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
            echo json_encode(['success' => true, 'data' => $scenes]);
            break;

        default:
            throw new Exception("未知操作: " . $action);
    }
} catch (Exception $e) {
    if ($conn->inTransaction()) {
        $conn->rollBack();
    }
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
} 