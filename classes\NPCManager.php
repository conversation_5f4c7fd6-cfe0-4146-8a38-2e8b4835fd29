<?php
// classes/NPCManager.php

// 引入新的对话脚本处理器
require_once 'DialogueScriptProcessor.php';

class NPCManager {
    private $db;
    private $scriptProcessor; // 新增脚本处理器实例
    private $npcsCache = null;
    private $dialoguesCache = null;
    
    public function __construct(Database $db) {
        $this->db = $db;
        // 在构造函数中实例化处理器，传递Database实例而不是PDO连接
        $this->scriptProcessor = new DialogueScriptProcessor($db);
    }
    
    /**
     * 获取所有NPC实例
     * @param bool $forceRefresh 是否强制刷新缓存
     * @return array NPC实例数组
     */
    public function getAllNpcInstances($forceRefresh = false) {
        if ($this->npcsCache === null || $forceRefresh) {
            $stmt = $this->db->query("
                SELECT ni.*, nt.name, nt.description, nt.level, nt.avatar, nt.is_merchant, nt.is_quest_giver, nt.default_dialogue_tree_id, nt.id as template_id
                FROM npc_instances ni
                JOIN npc_templates nt ON ni.template_id = nt.id
                ORDER BY ni.scene_id, nt.name
            ");
            $this->npcsCache = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
        }
        return $this->npcsCache;
    }
    
    /**
     * 获取场景中的所有NPC
     * @param string $sceneId 场景ID
     * @return array 场景中的NPC数组
     */
    public function getSceneNpcs($sceneId) {
        $npcs = [];
        $allNpcs = $this->getAllNpcInstances();

        foreach ($allNpcs as $npc) {
            if ($npc['scene_id'] === $sceneId) {
                $npcs[] = $npc;
            }
        }

        return $npcs;
    }

    /**
     * 获取场景中的所有NPC（包含任务信息）
     * @param string $sceneId 场景ID
     * @param int $playerId 玩家ID
     * @param QuestManager $questManager 任务管理器
     * @return array 场景中的NPC数组（包含has_quests_for_player字段）
     */
    public function getSceneNpcsWithQuestInfo($sceneId, $playerId, $questManager) {
        $npcs = $this->getSceneNpcs($sceneId);

        // 为每个NPC检查是否有任务给该玩家
        foreach ($npcs as &$npc) {
            $hasQuestsForPlayer = false;

            try {
                // 获取NPC可提供的任务
                $availableQuests = $questManager->getAvailableQuestsFromNPC($npc['id'], $playerId);

                // 获取可以在该NPC处完成的任务
                $completableQuests = $questManager->getCompletableQuestsForNPC($npc['id'], $playerId);

                // 如果有可接或可完成的任务，设置标志为true
                if (!empty($availableQuests) || !empty($completableQuests)) {
                    $hasQuestsForPlayer = true;
                }
            } catch (Exception $e) {
                error_log("[场景NPC任务检查] NPC {$npc['id']} 检查失败: " . $e->getMessage());
            }

            $npc['has_quests_for_player'] = $hasQuestsForPlayer;
        }

        return $npcs;
    }
    
    /**
     * 获取NPC详细信息
     * @param int $npcId NPC实例ID
     * @return array|null NPC详细信息或null
     */
    public function getNpcDetails($npcId) {
        // Find in cache first
        $allNpcs = $this->getAllNpcInstances();
        foreach ($allNpcs as $npc) {
            if ($npc['id'] == $npcId) {
                // 获取NPC装备信息（新增）
                $npc['inventory'] = $this->getNpcInventory($npcId);
                return $npc;
            }
        }
        return null; // Not found
    }
    
    /**
     * 新增：获取NPC装备
     * @param int $npcId NPC实例ID
     * @return array NPC装备数组
     */
    public function getNpcInventory($npcId) {
        $stmt = $this->db->query("
            SELECT ni.id, ni.item_template_id, ni.slot, ni.quantity, ni.is_equipped,
                   it.name, it.category, it.description
            FROM npc_inventories ni
            JOIN item_templates it ON ni.item_template_id = it.id
            WHERE ni.npc_instance_id = ?
            ORDER BY ni.is_equipped DESC, it.category, it.name
        ", [$npcId]);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }
    
    /**
     * 获取NPC的默认对话树
     * @param int $npcId NPC实例ID
     * @return array|null 对话树数据或null
     */
    public function getNpcDefaultDialogue($npcId) {
        $npc = $this->getNpcDetails($npcId);
        if (!$npc || !$npc['default_dialogue_tree_id']) {
            return null;
        }
        
        return $this->getDialogueTree($npc['default_dialogue_tree_id']);
    }
    
    /**
     * 获取对话树
     * @param int $dialogueTreeId 对话树ID
     * @return array|null 对话树数据或null
     */
    public function getDialogueTree($dialogueTreeId) {
        if ($this->dialoguesCache === null) {
            $this->dialoguesCache = [];
        } else if (isset($this->dialoguesCache[$dialogueTreeId])) {
            return $this->dialoguesCache[$dialogueTreeId];
        }
        
        // 获取对话树基本信息
        $stmt = $this->db->query("
            SELECT * FROM dialogue_trees WHERE id = ?
        ", [$dialogueTreeId]);
        
        $dialogueTree = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!$dialogueTree) {
            return null;
        }
        
        // 获取对话树的所有节点
        $stmt = $this->db->query("
            SELECT * FROM dialogue_nodes WHERE dialogue_tree_id = ? ORDER BY id
        ", [$dialogueTreeId]);
        
        $nodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        // 处理节点的next_node_ids，将JSON字符串转换为数组
        foreach ($nodes as &$node) {
            if (!empty($node['next_node_ids'])) {
                $node['next_node_ids'] = json_decode($node['next_node_ids'], true);
            } else {
                $node['next_node_ids'] = [];
            }
        }
        
        $dialogueTree['nodes'] = $nodes;
        $this->dialoguesCache[$dialogueTreeId] = $dialogueTree;
        
        return $dialogueTree;
    }
    
    /**
     * 获取对话节点
     * @param int $nodeId 节点ID
     * @return array|null 节点数据或null
     */
    public function getDialogueNode($nodeId) {
        // This function is frequently called, could benefit from caching if performance is an issue
        $stmt = $this->db->query("
            SELECT * FROM dialogue_nodes WHERE id = ?
        ", [$nodeId]);
        
        $node = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        
        if (!$node) {
            return null;
        }
        
        // 处理next_node_ids，将JSON字符串转换为数组
        if (!empty($node['next_node_ids'])) {
            $node['next_node_ids'] = json_decode($node['next_node_ids'], true);
        } else {
            $node['next_node_ids'] = [];
        }
        
        return $node;
    }
    
    /**
     * 【新】获取适用于玩家的对话树
     * @param int $npcTemplateId NPC模板ID
     * @param int $playerId 玩家ID
     * @return int|null 对话树ID或null
     */
    private function getApplicableDialogueTreeId($npcTemplateId, $playerId) {
        error_log("[对话树选择] 开始为NPC模板ID {$npcTemplateId} 查找适用于玩家 {$playerId} 的对话树");
        
        // 1. 获取所有关联的对话
        $stmt = $this->db->query(
            "SELECT id, dialogue_tree_id, condition_script, is_default FROM npc_dialogues WHERE npc_template_id = ? ORDER BY id DESC", // 降序排列，优先考虑较新的对话
            [$npcTemplateId]
        );
        $dialogues = $stmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("[对话树选择] 找到 " . count($dialogues) . " 个关联对话树记录");

        $defaultTreeId = null;
        $applicableTreeIds = [];
        
        // 2. 检查所有对话条件
        foreach ($dialogues as $dialogue) {
            error_log("[对话树选择] 检查对话记录ID: {$dialogue['id']}, 对话树ID: {$dialogue['dialogue_tree_id']}, 是否默认: " . ($dialogue['is_default'] ? '是' : '否'));
            
            // 记录默认对话树ID
            if (!empty($dialogue['is_default']) && $dialogue['is_default'] == 1) {
                $defaultTreeId = (int)$dialogue['dialogue_tree_id'];
                error_log("[对话树选择] 记录默认对话树ID: {$defaultTreeId}");
                continue;
            }
            
            // 检查带条件的对话树
            if (!empty($dialogue['condition_script'])) {
                error_log("[对话树选择] 评估条件脚本: " . $dialogue['condition_script']);
                // 记录条件脚本中的quest_id类型
                $conditionData = json_decode($dialogue['condition_script'], true);
                if ($conditionData && isset($conditionData['conditions']) && is_array($conditionData['conditions'])) {
                    foreach ($conditionData['conditions'] as $condition) {
                        if (isset($condition['quest_id'])) {
                            error_log("[对话树选择] 条件中的quest_id类型: " . gettype($condition['quest_id']) . ", 值: {$condition['quest_id']}");
                        }
                    }
                }
                
                $result = $this->scriptProcessor->evaluateConditions($dialogue['condition_script'], $playerId);
                error_log("[对话树选择] 条件评估结果: " . ($result ? '通过' : '不通过'));
                
                if ($result) {
                    $treeId = (int)$dialogue['dialogue_tree_id'];
                    error_log("[对话树选择] 添加适用对话树: {$treeId}");
                    $applicableTreeIds[] = $treeId;
                }
            } else {
                error_log("[对话树选择] 对话记录没有条件脚本");
            }
        }
        
        // 3. 如果有多个适用的对话树，选择ID最大的（假设ID越大越新）
        if (!empty($applicableTreeIds)) {
            rsort($applicableTreeIds); // 降序排序
            $selectedTreeId = $applicableTreeIds[0];
            error_log("[对话树选择] 从 " . count($applicableTreeIds) . " 个适用对话树中选择ID最大的: {$selectedTreeId}");
            return $selectedTreeId;
        }

        // 4. 如果没有符合条件的，使用默认对话树
        if ($defaultTreeId !== null) {
            error_log("[对话树选择] 没有找到符合条件的对话树，使用默认对话树: {$defaultTreeId}");
            return $defaultTreeId;
        }

        error_log("[对话树选择] 没有找到任何适用的对话树或默认对话树");
        return null; // 没有任何适用的对话树
    }
    
    /**
     * 【新】获取有效的下一个节点 (单个)
     * @param array $currentNode
     * @param int $playerId
     * @return array|null
     */
    private function getValidNextNode(array $currentNode, int $playerId): ?array {
        if (empty($currentNode['next_node_ids'])) {
            return null;
        }

        foreach ($currentNode['next_node_ids'] as $nextNodeId) {
            $node = $this->getDialogueNode($nextNodeId);
            if ($node && $this->scriptProcessor->evaluateConditions($node['condition_script'] ?? '', $playerId)) {
                return $node; // 返回第一个有效的节点
            }
        }

        return null;
    }

    /**
     * 【重构】开始与NPC对话
     * @param int $npcId NPC实例ID
     * @param int $playerId 玩家ID
     * @return array 对话数据
     */
    public function startDialogue($npcId, $playerId) {
        error_log("[对话系统] 开始对话: NPC实例ID {$npcId}, 玩家ID {$playerId}");
        
        $npc = $this->getNpcDetails($npcId);
        if (!$npc) {
            error_log("[对话系统] 错误: 找不到指定的NPC (ID: {$npcId})");
            return ['error' => '找不到指定的NPC'];
        }
        error_log("[对话系统] NPC信息: 名称 '{$npc['name']}', 模板ID {$npc['template_id']}");

        $dialogueTreeId = $this->getApplicableDialogueTreeId($npc['template_id'], $playerId);
        if (!$dialogueTreeId) {
            error_log("[对话系统] 错误: 没有找到适用的对话树");
            return ['error' => '该NPC现在不想和你说话'];
        }
        error_log("[对话系统] 选择的对话树ID: {$dialogueTreeId}");

        $dialogueTree = $this->getDialogueTree($dialogueTreeId);
        if (!$dialogueTree || empty($dialogueTree['nodes'])) {
            return ['error' => '该NPC没有对话内容'];
        }

        // 找到第一个对玩家有效的节点作为起始节点
        $firstNode = null;
        foreach ($dialogueTree['nodes'] as $node) {
            if ($this->scriptProcessor->evaluateConditions($node['condition_script'] ?? '', $playerId)) {
                $firstNode = $node;
                break;
            }
        }

        if (!$firstNode) {
            return ['error' => '该NPC现在不想和你说话'];
        }
        
        $actionResults = $this->scriptProcessor->executeActions($firstNode['action_script'] ?? '', $playerId);

        $history = [[
            'id' => $firstNode['id'],
            'content' => $firstNode['content'],
            'is_player' => false
        ]];
        
        $result = [
            'npc' => $npc,
            'dialogue' => $firstNode,
            'history' => $history,
            'action_results' => $actionResults
        ];
        
        if ($firstNode['is_player_choice']) {
            $result['dialogue']['options'] = $this->getDialogueOptions($firstNode, $playerId);
        }
        
        return $result;
    }
    
    /**
     * 【重构】继续对话
     * @param int $npcId NPC实例ID
     * @param int $nodeId 当前节点ID
     * @param int $playerId 玩家ID
     * @param array $history 对话历史
     * @return array 对话数据
     */
    public function continueDialogue($npcId, $nodeId, $playerId, $history = []) {
        $npc = $this->getNpcDetails($npcId);
        if (!$npc) return ['error' => '找不到指定的NPC'];
        
        $node = $this->getDialogueNode($nodeId);
        if (!$node) return ['error' => '找不到指定的对话节点'];

        $nextNode = $this->getValidNextNode($node, $playerId);
        
        if (!$nextNode) {
            return ['npc' => $npc, 'dialogue' => null, 'history' => $history, 'is_ended' => true];
        }
        
        $actionResults = $this->scriptProcessor->executeActions($nextNode['action_script'] ?? '', $playerId);

        $history[] = ['id' => $nextNode['id'], 'content' => $nextNode['content'], 'is_player' => false];
        
        $result = ['npc' => $npc, 'dialogue' => $nextNode, 'history' => $history, 'action_results' => $actionResults];
        
        if ($nextNode['is_player_choice']) {
            $result['dialogue']['options'] = $this->getDialogueOptions($nextNode, $playerId);
        }
        
        return $result;
    }
    
    /**
     * 【重构】选择对话选项
     * @param int $npcId NPC实例ID
     * @param int $nodeId 当前节点ID
     * @param int $optionId 选项ID
     * @param int $playerId 玩家ID
     * @param array $history 对话历史
     * @return array 对话数据
     */
    public function selectDialogueOption($npcId, $nodeId, $optionId, $playerId, $history = []) {
        $npc = $this->getNpcDetails($npcId);
        if (!$npc) return ['error' => '找不到指定的NPC'];
        
        $optionNode = $this->getDialogueNode($optionId);
        if (!$optionNode) return ['error' => '找不到指定的对话选项'];
        
        // 1. 将玩家选择添加到历史
        $history[] = ['id' => $optionNode['id'], 'content' => $optionNode['content'], 'is_player' => true];
        
        // 2. 立刻执行玩家选项的动作脚本
        $playerActionResults = $this->scriptProcessor->executeActions($optionNode['action_script'] ?? '', $playerId);

        // 3. 查找并处理NPC的回复
        $nextNode = $this->getValidNextNode($optionNode, $playerId);

        if (!$nextNode) {
            return ['npc' => $npc, 'dialogue' => null, 'history' => $history, 'is_ended' => true, 'action_results' => $playerActionResults];
        }
        
        // 4. 执行NPC回复节点的动作脚本
        $npcActionResults = $this->scriptProcessor->executeActions($nextNode['action_script'] ?? '', $playerId);
        $allActionResults = array_merge($playerActionResults, $npcActionResults);

        // 5. 将NPC的回复添加到历史
        $history[] = ['id' => $nextNode['id'], 'content' => $nextNode['content'], 'is_player' => false];
        
        $result = ['npc' => $npc, 'dialogue' => $nextNode, 'history' => $history, 'action_results' => $allActionResults];
        
        if ($nextNode['is_player_choice']) {
            $result['dialogue']['options'] = $this->getDialogueOptions($nextNode, $playerId);
        }
        
        return $result;
    }
    
    /**
     * 【重构】获取对话节点的选项
     * @param array $node 对话节点
     * @param int $playerId 玩家ID
     * @return array 选项数组
     */
    private function getDialogueOptions($node, $playerId) {
        if (empty($node['next_node_ids'])) {
            return [];
        }
        
        $options = [];
        foreach ($node['next_node_ids'] as $nextNodeId) {
            $optionNode = $this->getDialogueNode($nextNodeId);
            // 只返回对玩家有效的选项
            if ($optionNode && $this->scriptProcessor->evaluateConditions($optionNode['condition_script'] ?? '', $playerId)) {
                $options[] = ['id' => $optionNode['id'], 'content' => $optionNode['content']];
            }
        }
        
        return $options;
    }
    
    /**
     * 更新NPC缓存 - 添加NPC
     * @param array $npc 新添加的NPC数据
     */
    public function updateCacheForAddedNpc($npc) {
        if ($this->npcsCache !== null) {
            $this->npcsCache[] = $npc;
        }
    }
    
    /**
     * 更新NPC缓存 - 更新NPC
     * @param int $npcId 要更新的NPC ID
     * @param array $updatedData 更新的数据
     */
    public function updateCacheForUpdatedNpc($npcId, $updatedData) {
        if ($this->npcsCache !== null) {
            foreach ($this->npcsCache as &$npc) {
                if ($npc['id'] == $npcId) {
                    foreach ($updatedData as $key => $value) {
                        $npc[$key] = $value;
                    }
                    break;
                }
            }
        }
    }
    
    /**
     * 更新NPC缓存 - 删除NPC
     * @param int $npcId 要删除的NPC ID
     */
    public function updateCacheForDeletedNpc($npcId) {
        if ($this->npcsCache !== null) {
            foreach ($this->npcsCache as $index => $npc) {
                if ($npc['id'] == $npcId) {
                    array_splice($this->npcsCache, $index, 1);
                    break;
                }
            }
        }
    }
    
    /**
     * 清除NPC缓存
     * @return array 清除结果状态
     */
    public function clearCache() {
        $this->npcsCache = null;
        echo "NPC缓存已清除\n";
        $this->dialoguesCache = null;
        echo "对话缓存已清除\n";
        
        return [
            'success' => true,
            'message' => 'NPC和对话缓存已成功清除'
        ];
    }

    /**
     * 【新增】预加载对话内容，检查对话条件
     * @param int $npcId NPC实例ID
     * @param int $playerId 玩家ID
     * @return array 预加载结果
     */
    public function preloadDialogue($npcId, $playerId) {
        error_log("[对话系统] 预加载对话: NPC实例ID {$npcId}, 玩家ID {$playerId}");
        
        $npc = $this->getNpcDetails($npcId);
        if (!$npc) {
            error_log("[对话系统] 错误: 找不到指定的NPC (ID: {$npcId})");
            return ['error' => '找不到指定的NPC'];
        }
        error_log("[对话系统] NPC信息: 名称 '{$npc['name']}', 模板ID {$npc['template_id']}");

        $dialogueTreeId = $this->getApplicableDialogueTreeId($npc['template_id'], $playerId);
        if (!$dialogueTreeId) {
            error_log("[对话系统] 错误: 没有找到适用的对话树");
            return ['error' => '该NPC现在不想和你说话'];
        }
        error_log("[对话系统] 预加载成功，将使用对话树ID: {$dialogueTreeId}");

        // 获取对话树信息，但不执行动作脚本
        $dialogueTree = $this->getDialogueTree($dialogueTreeId);
        if (!$dialogueTree || empty($dialogueTree['nodes'])) {
            error_log("[对话系统] 错误: 对话树没有节点");
            return ['error' => '该NPC没有对话内容'];
        }
        
        return [
            'success' => true,
            'dialogue_tree_id' => $dialogueTreeId,
            'npc' => $npc
        ];
    }
} 