<?php
// admin/refresh_session.php
header('Content-Type: application/json; charset=utf-8');
session_start();

// 检查是否是POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

// 检查管理员是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(401);
    echo json_encode(['success' => false, 'message' => '未登录', 'redirect' => 'index.php']);
    exit;
}

try {
    // 更新最后活动时间
    $_SESSION['last_activity'] = time();
    
    // 记录会话刷新日志
    error_log('管理员会话刷新 - 用户: ' . $_SESSION['admin_username'] . ' ID: ' . $_SESSION['admin_id']);
    
    echo json_encode([
        'success' => true, 
        'message' => '会话已刷新',
        'last_activity' => $_SESSION['last_activity']
    ]);
    
} catch (Exception $e) {
    error_log('会话刷新失败: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['success' => false, 'message' => '会话刷新失败']);
}
?>
