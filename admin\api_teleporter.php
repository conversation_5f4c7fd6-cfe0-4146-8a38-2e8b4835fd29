<?php
require_once '../config/Database.php';
require_once 'auth.php';

header('Content-Type: application/json');

$db = Database::getInstance();
$response = ['success' => false, 'message' => ''];

try {
    $action = $_GET['action'] ?? $_POST['action'] ?? '';

    switch ($action) {
        case 'get_all_destinations':
            // 获取所有传送点的目标场景
            error_log("get_all_destinations: 开始查询传送点数据");

            // 先检查是否有传送点建筑
            $checkStmt = $db->query("SELECT COUNT(*) as count FROM buildings WHERE type = 'TELEPORTER'");
            $teleporterCount = $checkStmt->fetch()['count'];
            error_log("get_all_destinations: 找到 {$teleporterCount} 个传送点建筑");

            // 检查是否有场景建筑关联
            $checkStmt2 = $db->query(
                "SELECT COUNT(*) as count FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                WHERE b.type = 'TELEPORTER'"
            );
            $sceneBuildingCount = $checkStmt2->fetch()['count'];
            error_log("get_all_destinations: 找到 {$sceneBuildingCount} 个场景中的传送点");

            // 检查是否有传送目标配置
            $checkStmt3 = $db->query("SELECT COUNT(*) as count FROM teleporter_destinations");
            $destinationCount = $checkStmt3->fetch()['count'];
            error_log("get_all_destinations: 找到 {$destinationCount} 个传送目标配置");

            $stmt = $db->query(
                "SELECT td.*,
                        target_scene.name as scene_name,
                        target_scene.x, target_scene.y, target_scene.z,
                        sb.scene_id as source_scene_id,
                        source_scene.name as source_scene_name,
                        sb.building_id,
                        b.name as building_name,
                        it.name as required_item_name
                FROM teleporter_destinations td
                JOIN scenes target_scene ON td.target_scene_id = target_scene.id
                JOIN scene_buildings sb ON td.scene_building_id = sb.id
                JOIN scenes source_scene ON sb.scene_id = source_scene.id
                JOIN buildings b ON sb.building_id = b.id
                LEFT JOIN item_templates it ON td.required_item_id = it.id
                WHERE b.type = 'TELEPORTER'
                ORDER BY td.sort_order"
            );
            $destinations = $stmt->fetchAll();
            $stmt->closeCursor();

            error_log("get_all_destinations: 查询结果数量: " . count($destinations));
            if (count($destinations) > 0) {
                error_log("get_all_destinations: 第一条记录: " . json_encode($destinations[0]));
            }

            $response['success'] = true;
            $response['data'] = $destinations;
            break;

        case 'get_destinations':
            // 可以接收scene_building_id或者scene_id+building_id
            $sceneBuildingId = $_GET['scene_building_id'] ?? 0;
            $sceneId = $_GET['scene_id'] ?? '';
            $buildingId = $_GET['building_id'] ?? 0;
            
            if (!$sceneBuildingId && (!$sceneId || !$buildingId)) {
                throw new Exception('缺少必要参数');
            }
            
            // 如果提供了scene_id和building_id，先查找对应的scene_building_id
            if (!$sceneBuildingId && $sceneId && $buildingId) {
                $stmt = $db->query(
                    "SELECT id FROM scene_buildings WHERE scene_id = ? AND building_id = ?",
                    [$sceneId, $buildingId]
                );
                $result = $stmt->fetch();
                $stmt->closeCursor();
                
                if ($result) {
                    $sceneBuildingId = $result['id'];
                } else {
                    // 如果找不到对应的scene_building_id，创建一个新记录
                    $db->query(
                        "INSERT INTO scene_buildings (scene_id, building_id) VALUES (?, ?)",
                        [$sceneId, $buildingId]
                    );
                    $sceneBuildingId = $db->lastInsertId();
                }
            }

            // 获取传送点的所有目标场景
            $stmt = $db->query(
                "SELECT td.*, s.name as scene_name, s.x, s.y, s.z, 
                        it.name as required_item_name, it.id as required_item_id,
                        sb.scene_id, sb.building_id
                FROM teleporter_destinations td 
                JOIN scenes s ON td.target_scene_id = s.id 
                JOIN scene_buildings sb ON td.scene_building_id = sb.id
                LEFT JOIN item_templates it ON td.required_item_id = it.id
                WHERE td.scene_building_id = ? 
                ORDER BY td.sort_order",
                [$sceneBuildingId]
            );
            $destinations = $stmt->fetchAll();
            $stmt->closeCursor();

            $response['success'] = true;
            $response['data'] = $destinations;
            break;

        case 'add_destination':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($data === null) {
                throw new Exception('无效的JSON数据');
            }

            $sceneBuildingId = $data['scene_building_id'] ?? 0;
            $targetSceneId = $data['destination_scene_id'] ?? '';
            $requiredItemId = $data['required_item_id'] ?? null;
            $requiredQuantity = $data['required_quantity'] ?? 1;

            error_log("添加传送目的地 - 输入参数: scene_building_id={$sceneBuildingId}, target_scene_id={$targetSceneId}");

            if (!$sceneBuildingId || !$targetSceneId) {
                throw new Exception('缺少必要参数: scene_building_id 或 destination_scene_id');
            }
            
            // 如果提供了物品ID，确保数量是有效的正整数
            if ($requiredItemId) {
                $requiredQuantity = max(1, intval($requiredQuantity));
            } else {
                // 如果没有物品ID，将数量设置为null
                $requiredItemId = null;
                $requiredQuantity = null;
            }

            // 检查传送点是否存在
            $stmt = $db->query(
                "SELECT sb.*, b.name as building_name, b.type as building_type
                FROM scene_buildings sb 
                JOIN buildings b ON sb.building_id = b.id 
                WHERE sb.id = ?",
                [$sceneBuildingId]
            );
            $teleporter = $stmt->fetch();
            $stmt->closeCursor();

            if (!$teleporter) {
                error_log("错误: 找不到scene_building_id={$sceneBuildingId}的记录");
                throw new Exception("找不到ID为{$sceneBuildingId}的场景建筑记录");
            }
            
            error_log("找到场景建筑: " . json_encode($teleporter));
            
            // 检查建筑类型
            if ($teleporter['building_type'] !== 'TELEPORTER') {
                error_log("错误: 建筑类型不是传送点，实际类型: " . $teleporter['building_type']);
                throw new Exception("建筑[{$teleporter['building_name']}]不是传送点，无法添加传送目的地");
            }

            // 检查目标场景是否存在
            $stmt = $db->query("SELECT id FROM scenes WHERE id = ?", [$targetSceneId]);
            if (!$stmt->fetch()) {
                throw new Exception('目标场景不存在');
            }
            $stmt->closeCursor();

            // 检查是否已经添加过这个目标场景
            $stmt = $db->query(
                "SELECT id FROM teleporter_destinations 
                WHERE scene_building_id = ? AND target_scene_id = ?",
                [$sceneBuildingId, $targetSceneId]
            );
            if ($stmt->fetch()) {
                throw new Exception('该目标场景已经添加过了');
            }
            $stmt->closeCursor();

            // 获取当前最大排序值
            $stmt = $db->query(
                "SELECT COALESCE(MAX(sort_order), 0) as max_order 
                FROM teleporter_destinations 
                WHERE scene_building_id = ?",
                [$sceneBuildingId]
            );
            $maxOrder = $stmt->fetch()['max_order'];
            $stmt->closeCursor();

            // 添加新目标场景
            $db->query(
                "INSERT INTO teleporter_destinations (scene_building_id, target_scene_id, sort_order, required_item_id, required_quantity) 
                VALUES (?, ?, ?, ?, ?)",
                [$sceneBuildingId, $targetSceneId, $maxOrder + 1, $requiredItemId, $requiredQuantity]
            );

            $response['success'] = true;
            $response['message'] = '目标场景添加成功';
            break;

        case 'delete_destination':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($data === null) {
                throw new Exception('无效的JSON数据');
            }
            $destinationId = $data['id'] ?? 0;
            if (!$destinationId) {
                throw new Exception('缺少目标场景ID');
            }

            $db->query(
                "DELETE FROM teleporter_destinations WHERE id = ?",
                [$destinationId]
            );

            $response['success'] = true;
            $response['message'] = '目标场景移除成功';
            break;

        case 'update_destination':
            $data = json_decode(file_get_contents('php://input'), true);
            if ($data === null) {
                throw new Exception('无效的JSON数据');
            }
            $destinationId = $data['destination_id'] ?? 0;
            $requiredItemId = $data['required_item_id'] ?? null;
            $requiredQuantity = $data['required_quantity'] ?? 1;
            
            if (!$destinationId) {
                throw new Exception('缺少目标场景ID');
            }
            
            // 如果提供了物品ID，确保数量是有效的正整数
            if ($requiredItemId) {
                $requiredQuantity = max(1, intval($requiredQuantity));
            } else {
                // 如果没有物品ID，将数量设置为null
                $requiredItemId = null;
                $requiredQuantity = null;
            }
            
            // 更新传送点目标场景的物品要求
            $db->query(
                "UPDATE teleporter_destinations 
                SET required_item_id = ?, required_quantity = ? 
                WHERE id = ?",
                [$requiredItemId, $requiredQuantity, $destinationId]
            );
            
            $response['success'] = true;
            $response['message'] = '传送点物品需求已更新';
            break;
            
        case 'get_all_items':
            // 获取符石类物品用于传送点消耗
            $stmt = $db->query(
                "SELECT id, name, description, category
                FROM item_templates
                WHERE category = 'Rune'
                ORDER BY name"
            );
            $items = $stmt->fetchAll();
            $stmt->closeCursor();

            $response['success'] = true;
            $response['data'] = $items;
            break;
            
        default:
            throw new Exception('未知的操作类型: ' . $action);
    }
} catch (Exception $e) {
    $response['message'] = $e->getMessage();
}

echo json_encode($response); 