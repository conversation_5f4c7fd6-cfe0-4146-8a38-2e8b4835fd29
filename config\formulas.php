<?php
// config/formulas.php

class Formulas {
    public static $sceneResetCooldown = scene_reset_cooldown;

    public static $requiredLevel = chat_level;

    public static $damageTypeNames = [
        'physical_damage' => '物理伤害',
        'fire_damage'     => '火焰伤害',
        'ice_damage'      => '冰冻伤害',
        'wind_damage'     => '风裂伤害',
        'electric_damage' => '闪电伤害',
    ];

    public static $resistanceTypeNames = [
        'fire_resistance'     => '火焰抗性',
        'ice_resistance'      => '冰冻抗性',
        'wind_resistance'     => '风裂抗性',
        'electric_resistance' => '闪电抗性',
    ];

    public static $itemCategoryNames = [
        'Equipment' => '装备',
        'Gem'       => '宝石',
        'Material'  => '材料',
        'Potion'    => '药品',
        'Misc'      => '杂物',
        'Scroll'    => '书卷',
        'Unknown'   => '未知'
    ];

    /**
     * Calculates the success rate for gem socketing.
     * The rate decreases with the number of previous attempts on the same socket.
     * @param int $attempts The number of previous attempts on this socket (0 for the first try).
     * @return float The success rate (e.g., 1.0 for 100%, 0.1 for 10%).
     */
    public static function calculateSocketingSuccessRate($attempts) {
        // 每次失败后成功率略微提高，但收益递减
        return max(0.1, 1.0 - $attempts * 0.15);
    }

    /**
     * Calculates the result of a PVE attack turn with elemental damage and rich logs.
     * @param array $attacker The attacker's data, including 'attributes' and 'weapon_name'.
     * @param array $defender The defender's data, including 'attributes'.
     * @return array An array containing 'damage' and a 'log' array.
     */
    public static function calculatePveDamage($attacker, $defender) {
        $logs = [];
        $totalDamage = 0;
        
        // --- 闪避检查 ---
        $dodgeResult = self::calculateDodgeChance($defender, $attacker);
        $dodgeChance = $dodgeResult['chance'];
        $dodgeDetails = $dodgeResult['details'];
        
        // 生成1-100的随机数（百分比）
        $randomRoll = mt_rand(1, 1000) / 10; // 1-100之间的数，代表百分比
        
        // 将闪避率转换为百分比进行比较（确保单位一致）
        $dodgePercentage = round($dodgeChance * 100, 2);
        
        // 闪避判定：当随机值小于或等于闪避率时，闪避成功
        // 例如闪避率90%，随机值9.7%，9.7 < 90，闪避成功
        $isDodged = $randomRoll <= $dodgePercentage;

        if ($isDodged) {
            $logs[] = self::generateDodgeDescription($defender, $attacker);
            return [
                'damage' => 0,
                'physical_damage' => 0,
                'elemental_damage' => 0,
                'is_critical' => false,
                'is_dodged' => true,
                'log' => $logs
            ];
        }
        
        // --- 物理伤害计算 ---
        $rawDamage = $attacker['attributes']['attack'];
        $mitigation = 1 - (($defender['attributes']['defense'] ?? 0) / (($defender['attributes']['defense'] ?? 0) + 200));
        $baseDamage = $rawDamage * max(0.05, $mitigation);
        $critChance = 5 + (($attacker['attributes']['agility'] ?? 0) / 10);
        $isCritical = (mt_rand(1, 1000) / 10) <= $critChance;
        if ($isCritical) $baseDamage *= 1.5;
        $variance = mt_rand(-10, 10) / 100;
        $physicalDamageThisTurn = max(1, round($baseDamage * (1 + $variance)));
        
        // --- 元素伤害计算 ---
        $elementalDamageDetails = [];
        $totalElementalDamage = 0;
        $elementalTypes = ['fire', 'ice', 'wind', 'electric'];
        
        foreach ($elementalTypes as $index => $type) {
            $damageKey = "{$type}_damage";
            $resistanceKey = "{$type}_resistance";
            $attackerDamage = $attacker['attributes'][$damageKey] ?? 0;
            $defenderResistance = $defender['attributes'][$resistanceKey] ?? 0;

            // PVE中抗性公式：抗性越高收益越高（非线性递减）
            // 使用公式: 1 / (1 + resistance/100)，让高抗性有更好的收益
            // 抗性0时减伤0%，抗性100时减伤50%，抗性200时减伤66.7%，抗性300时减伤75%
            $resistanceMultiplier = 1 / (1 + ($defenderResistance / 100));
            $elementalDamage = max(0, $attackerDamage * $resistanceMultiplier);

            if ($elementalDamage > 0) {
                $totalElementalDamage += $elementalDamage;
                $elementalDamageDetails[] = self::$damageTypeNames[$damageKey] . $elementalDamage . "点";
            }
        }
        
        $totalDamage = $physicalDamageThisTurn + $totalElementalDamage;

        // --- 生成战斗描述 ---
        $attackerName = $attacker['username'];
        $defenderName = $defender['username'];
        $weaponName = $attacker['weapon_name'] ?? null;

        $actionVerb = $weaponName ? "紧紧握着<{$weaponName}>，重重砸向" : "挥拳攻击";

        $damageDescription = "{$attackerName} {$actionVerb} {$defenderName}，造成了 {$physicalDamageThisTurn} 点物理伤害";
        
        if (!empty($elementalDamageDetails)) {
            $damageDescription .= "，追加 " . implode(" + ", $elementalDamageDetails) . "。";
        } else {
            $damageDescription .= "。";
        }
        
        if ($isCritical) {
            $damageDescription = "【暴击！】" . $damageDescription;
        }
        
        $logs[] = $damageDescription;
        
        return [
            'damage' => $totalDamage,  // 确保返回总伤害（物理+元素）
            'physical_damage' => $physicalDamageThisTurn,
            'elemental_damage' => $totalElementalDamage,
            'is_critical' => $isCritical,
            'is_dodged' => false,
            'log' => $logs
        ];
    }

    /**
     * 生成闪避动作的随机描述
     * @param array $defender 防御者数据
     * @param array $attacker 攻击者数据
     * @return string 闪避描述
     */
    private static function generateDodgeDescription($defender, $attacker) {
        $defenderName = $defender['username'];
        $attackerName = $attacker['username'];
        $weaponName = $attacker['weapon_name'] ?? null;
        
        $attackDescriptions = [
            "挥击", "攻击", "猛击", "突刺", "重击"
        ];
        
        $dodgeDescriptions = [
            "轻巧地闪避",
            "敏捷地躲开",
            "以毫厘之差避过",
            "灵活地旋身躲开",
            "一个侧步躲过",
            "身形一晃避开",
            "后撤一步躲过",
            "腾空翻跃躲开",
            "矮身滑步闪避",
            "身体微微倾斜避过"
        ];
        
        $attackAction = $attackDescriptions[array_rand($attackDescriptions)];
        $dodgeAction = $dodgeDescriptions[array_rand($dodgeDescriptions)];
        
        if ($weaponName) {
            return "【闪避！】{$defenderName} {$dodgeAction}了 {$attackerName} 的 <{$weaponName}> {$attackAction}！";
        } else {
            return "【闪避！】{$defenderName} {$dodgeAction}了 {$attackerName} 的{$attackAction}！";
        }
    }

    /**
     * Calculates the experience required to reach the next level.
     * @param int $currentLevel The player's current level.
     * @return int The total experience needed for the next level.
     */
    public static function calculateExperienceForNextLevel($currentLevel) {
        // 一个简单的例子：每级所需经验以10%的速率指数增长，让升级过程更平滑
        return floor(100 * pow(1.1, $currentLevel - 1));
    }

    /**
     * 计算闪避概率
     * 基于防御者的敏捷和攻速，以及攻击者的敏捷和攻速
     * @param array $defender 防御者数据，包含attributes数组
     * @param array $attacker 攻击者数据，包含attributes数组
     * @return float 闪避概率（0.0-1.0之间的小数）
     */
    public static function calculateDodgeChance($defender, $attacker) {
        // 基础闪避概率 5%
        $baseDodgeChance = 0.05;
        $dodgeDetails = [];
        $dodgeDetails[] = "基础闪避: " . round($baseDodgeChance * 100, 2) . "%";
        
        // 1. 敏捷差异因素
        // 防御者敏捷高于攻击者时，闪避概率提高
        $defenderAgility = $defender['attributes']['agility'] ?? 10;
        $attackerAgility = $attacker['attributes']['agility'] ?? 10;
        $agilityDiff = $defenderAgility - $attackerAgility;
        
        // 敏捷差异对闪避概率的影响（每10点敏捷差异影响4%，最多±20%）
        $agilityFactor = min(0.20, max(-0.10, $agilityDiff / 10 * 0.04));
        $dodgeDetails[] = "敏捷差异: " . $defenderAgility . " vs " . $attackerAgility . 
                          ", 加成: " . round($agilityFactor * 100, 2) . "%";
        
        // 2. 攻速差异因素
        // 防御者攻速高于攻击者时，闪避概率提高
        $defenderSpeed = $defender['attributes']['attack_speed'] ?? 10;
        $attackerSpeed = $attacker['attributes']['attack_speed'] ?? 10;
        $speedDiff = $defenderSpeed - $attackerSpeed;
        
        // 攻速差异对闪避概率的影响（每10点攻速差异影响3%，最多±15%）
        $speedFactor = min(0.15, max(-0.08, $speedDiff / 10 * 0.03));
        $dodgeDetails[] = "攻速差异: " . $defenderSpeed . " vs " . $attackerSpeed . 
                         ", 加成: " . round($speedFactor * 100, 2) . "%";
        
        // 3. 等级差异修正因素
        $defenderLevel = $defender['attributes']['level'] ?? 1;
        $attackerLevel = $attacker['attributes']['level'] ?? 1;
        $levelDiff = $defenderLevel - $attackerLevel;
        
        // 等级差异对闪避概率的影响（每级差异影响1%，最多±5%）
        $levelFactor = min(0.05, max(-0.05, $levelDiff * 0.01));
        $dodgeDetails[] = "等级差异: " . $defenderLevel . " vs " . $attackerLevel . 
                         ", 加成: " . round($levelFactor * 100, 2) . "%";
        
        // 4. 固有闪避加成（如果有）
        $inherentDodge = $defender['attributes']['dodge_bonus'] ?? 0;
        $inherentDodgeFactor = min(0.15, max(0, $inherentDodge / 100)); // 最多15%固有闪避加成
        $dodgeDetails[] = "固有闪避: " . $inherentDodge . ", 加成: " . 
                         round($inherentDodgeFactor * 100, 2) . "%";
        
        // 计算最终闪避概率
        $dodgeChance = $baseDodgeChance + $agilityFactor + $speedFactor + $levelFactor + $inherentDodgeFactor;
        
        // 确保闪避概率在5%-40%之间
        $dodgeChance = min(0.40, max(0.05, $dodgeChance));
        $dodgeDetails[] = "最终闪避率: " . round($dodgeChance * 100, 2) . "%";
        
        // 直接在计算结果中返回闪避详情，而不是修改传入的defender变量
        return ['chance' => $dodgeChance, 'details' => $dodgeDetails];
    }

    /**
     * 根据基础力量和等级计算最终攻击力
     * @param int $strength 总力量 (基础 + 装备)
     * @param int $level 玩家等级
     * @return int
     */
    public static function calculateAttack($strength, $level) {
        // 每1点力量增加2点攻击力，每级提供1点基础攻击力
        return ($strength * 2) + $level;
    }

    /**
     * 根据体质、敏捷和等级计算最终防御力
     * @param int $constitution 总体质
     * @param int $agility 总敏捷
     * @param int $level 玩家等级
     * @return int
     */
    public static function calculateDefense($constitution, $agility, $level) {
        // 每1点体质增加1点防御力，每3点敏捷增加1点防御力
        return floor($constitution * 1) + floor($agility / 3) + $level;
    }

    /**
     * 根据体质和等级计算最大生命值
     * @param int $constitution 总体质
     * @param int $level 玩家等级
     * @return int
     */
    public static function calculateMaxHp($constitution, $level) {
        // 100点基础生命值，每点体质增加10点，每级增加5点
        return 100 + ($constitution * 10) + ($level * 5);
    }

    /**
     * 根据智慧和等级计算最大魔力值
     * @param int $intelligence 总智慧
     * @param int $level 玩家等级
     * @return int
     */
    public static function calculateMaxMp($intelligence, $level) {
        // 50点基础魔力值，每点智慧增加8点，每级增加3点
        return 50 + ($intelligence * 8) + ($level * 3);
    }

    /**
     * 根据敏捷计算攻击速度
     * @param int $agility 总敏捷
     * @return int 整数攻速值
     */
    public static function calculateAttackSpeed($agility) {
        // 基础攻速为10，每点敏捷增加1点攻速，返回整数值
        return floor(10 + $agility);
    }

    /**
     * 定义玩家升级时获得的基础属性。
     * @return array 每个属性增加的值
     */
    public static function getStatGainsOnLevelUp() {
        return [
            // 移除升级时的基础属性增加，玩家只获得潜力点来自由分配
            // 'strength' => 5,
            // 'agility' => 5,
            // 'constitution' => 5,
            // 'intelligence' => 5,
        ];
    }
    
    /**
     * 计算玩家在战斗中逃跑的成功率
     * 考虑因素：玩家和怪物的生命值比例、攻速差异、等级差异
     * 
     * @param array $player 玩家数据，包含attributes数组
     * @param array $monster 怪物数据，包含attributes数组
     * @return float 逃跑成功率（0.0-1.0之间的小数）
     */
    public static function calculateFleeChance($player, $monster) {
        // 基础逃跑成功率 30%
        $baseFleeChance = 0.30;
        
        // 1. 生命值比例因素
        // 玩家生命值比例越低，逃跑成功率越高（绝境求生）
        $playerHpRatio = $player['attributes']['hp'] / $player['attributes']['max_hp'];
        $monsterHpRatio = $monster['attributes']['hp'] / $monster['attributes']['max_hp'];
        
        // 生命值比例对逃跑成功率的影响（最多±15%）
        $hpFactor = (1 - $playerHpRatio) * 0.15; // 玩家生命值越低，加成越高
        $hpFactor -= (1 - $monsterHpRatio) * 0.10; // 怪物生命值越低，逃跑难度越高
        
        // 2. 攻速差异因素
        // 玩家攻速高于怪物时，逃跑成功率提高
        $playerSpeed = $player['attributes']['attack_speed'] ?? 10;
        $monsterSpeed = $monster['attributes']['attack_speed'] ?? 10;
        $speedDiff = $playerSpeed - $monsterSpeed;
        
        // 攻速差异对逃跑成功率的影响（每10点攻速差异影响5%，最多±20%）
        $speedFactor = min(0.20, max(-0.20, $speedDiff / 10 * 0.05));
        
        // 3. 等级差异因素
        // 怪物等级高于玩家时，逃跑成功率降低
        $playerLevel = $player['attributes']['level'] ?? 1;
        $monsterLevel = $monster['attributes']['level'] ?? 1;
        $levelDiff = $playerLevel - $monsterLevel;
        
        // 等级差异对逃跑成功率的影响（每级差异影响3%，最多±15%）
        $levelFactor = min(0.15, max(-0.15, $levelDiff * 0.03));
        
        // 4. 计算最终逃跑成功率
        $fleeChance = $baseFleeChance + $hpFactor + $speedFactor + $levelFactor;
        
        // 确保逃跑成功率在10%-90%之间
        $fleeChance = min(0.90, max(0.10, $fleeChance));
        
        return $fleeChance;
    }

    /**
     * 计算技能伤害的浮动倍率。
     * 伤害浮动与攻击者的等级相关，等级越高，伤害越稳定（浮动范围越小）。
     * @param array $attacker 攻击方的数据，需要包含 'level'
     * @return float 伤害倍率 (例如: 0.9 to 1.1)
     */
    public static function calculateSkillDamageFluctuation($attacker) {
        $level = $attacker['level'] ?? 1;
        
        // 等级越高，浮动范围越小。范围从 +/-30% 逐渐收敛到 +/-5%
        // 当等级为1时，maxFluctuationRange = 0.3 (30%)
        // 当等级为100时，maxFluctuationRange = 0.05 (5%)
        $maxFluctuationRange = 0.05 + (0.25 * (1 - ($level - 1) / 99));
        $maxFluctuationRange = max(0.05, $maxFluctuationRange); // 保证最小浮动范围

        $minMultiplier = 1 - $maxFluctuationRange;
        $maxMultiplier = 1 + $maxFluctuationRange;

        // 生成一个在最小和最大倍率之间的随机浮点数
        return mt_rand($minMultiplier * 1000, $maxMultiplier * 1000) / 1000;
    }

    /**
     * 解析并计算一个技能伤害公式字符串.
     * @param string $formula 包含占位符的公式, e.g., "attacker.intelligence * 2"
     * @param array $attacker 攻击方数据
     * @param array $defender 防御方数据
     * @return float|int 计算后的伤害值
     * @throws Exception 如果公式包含非法字符
     */
    public static function evaluateDamageFormula($formula, $attacker, $defender, $options = []) {
        // 0. 健壮性增强：如果传入的是JSON，尝试解析它
        $decodedJson = json_decode($formula, true);
        if (json_last_error() === JSON_ERROR_NONE && isset($decodedJson['damage_formula'])) {
            $formula = $decodedJson['damage_formula'];
        }

        // 0b. 健壮性增强：自动转换全角括号为半角括号
        $formula = str_replace(['（', '）'], ['(', ')'], $formula);

        // 1. 替换浮动伤害标记
        if (strpos($formula, '%%SKILL_FLUCTUATION%%') !== false) {
            $fluctuation = self::calculateSkillDamageFluctuation($attacker['attributes']);
            $formula = str_replace('%%SKILL_FLUCTUATION%%', $fluctuation, $formula);
        }

        // 2. 替换技能等级标记
        if (strpos($formula, '%%SKILL_LEVEL%%') !== false) {
            $skillLevel = $options['skill_level'] ?? 1; // 如果未提供技能等级，默认为1
            $formula = str_replace('%%SKILL_LEVEL%%', $skillLevel, $formula);
        }

        // 3. 替换攻击方和防御方的属性值
        $formula = preg_replace_callback('/(attacker|defender)\.([a-zA-Z_]+)/', function($matches) use ($attacker, $defender) {
            $source = $matches[1]; // 'attacker' or 'defender'
            $attribute = $matches[2]; // e.g., 'intelligence'

            if ($source === 'attacker') {
                $data = $attacker;
            } else {
                // 如果是群体攻击，defender为null，返回0
                if ($defender === null) {
                    return 0;
                }
                $data = $defender;
            }

            // 优先从 'attributes' 数组中取值
            return $data['attributes'][$attribute] ?? 0;

        }, $formula);

        // 4. 安全性检查：移除非法字符，只允许数字、操作符、小数点、空格、逗号和字母
        $safeFormula = preg_replace('/[^a-z0-9\.\+\-\*\/\(\)\s,]/i', '', $formula);

        if ($safeFormula !== $formula) {
            throw new Exception("伤害公式包含不允许的字符或格式错误: " . $formula);
        }

        // 5. 安全性增强：只允许白名单中的函数被执行
        if (preg_match_all('/([a-zA-Z_][a-zA-Z0-9_]*)\s*\(/', $safeFormula, $matches)) {
            $allowed_functions = ['max']; // 只允许 max 函数
            $found_functions = $matches[1];
            foreach ($found_functions as $func) {
                if (!in_array(strtolower($func), $allowed_functions)) {
                    throw new Exception("伤害公式包含不允许的函数: {$func}");
                }
            }
        }
        
        // 6. 防止空的eval, e.g. eval(';'), and other exploits
        if (empty(trim($safeFormula))) {
            return 0;
        }

        // 7. 使用eval计算最终结果
        // 增加错误抑制，防止eval出现 warning/notice
        $result = @eval("return {$safeFormula};");
        
        if ($result === false) {
            // 记录错误日志，便于调试
            error_log("公式计算失败: {$safeFormula}");
            throw new Exception("伤害公式语法错误。");
        }

        return $result;
    }

    /**
     * 计算学习或升级技能所需的知识点.
     * @param int $baseCost 技能模板中定义的基础消耗
     * @param int $currentSkillLevel 玩家当前拥有的技能等级 (如果是第一次学习，则为0)
     * @param string $rarity 技能的稀有度
     * @return int 计算出的最终消耗
     */
    public static function calculateKnowledgePointCost($baseCost, $currentSkillLevel, $rarity) {
        $rarityMultipliers = [
            'common' => 1.0,
            'uncommon' => 1.5,
            'rare' => 2.5,
            'epic' => 4.0,
            'legendary' => 7.0,
        ];

        $multiplier = $rarityMultipliers[$rarity] ?? 1.0;
        
        // 升级消耗公式：基础消耗 * 稀有度系数 * (当前等级 + 1)^1.2
        // 使用 pow 函数进行指数运算，让高等级的技能升级成本显著增加
        $levelFactor = pow($currentSkillLevel + 1, 1.2);

        $finalCost = $baseCost * $multiplier * $levelFactor;

        // 最终消耗向上取整，确保不会出现小数
        return (int)ceil($finalCost);
    }

    /**
     * 解析技能战斗描述，替换变量为实际值
     * @param string $description 战斗描述模板
     * @param array $attacker 攻击者数据
     * @param array $defender 防御者数据
     * @param array $skill 技能数据
     * @param array $damageResult 伤害计算结果
     * @return string 解析后的描述
     */
    public static function parseBattleDescription($description, $attacker, $defender, $skill, $damageResult) {
        // 1. 解析随机组合语法 {A|B|C}
        $processedDescription = preg_replace_callback('/\{([^{}]+)\}/', function($matches) {
            $options = explode('|', $matches[1]);
            return trim($options[array_rand($options)]);
        }, $description);

        // 2. 检查随机选择后的描述文本是否包含伤害占位符
        $hasDamagePlaceholder = (
            strpos($processedDescription, '[伤害]') !== false ||
            strpos($processedDescription, '[物理伤害]') !== false ||
            strpos($processedDescription, '[元素伤害]') !== false
        );

        $weaponName = $attacker['weapon_name'] ?? '幽灵般的武器';
        
        // 如果武器名称包含 `+`，只取 `+` 前面的部分作为基础名称
        $plusPos = strpos($weaponName, '§');
        if ($plusPos !== false) {
            $baseWeaponName = substr($weaponName, 0, $plusPos);
        } else {
            $baseWeaponName = $weaponName;
        }

        $patterns = [
            '/\[攻击者\]/' => $attacker['username'] ?? '未知攻击者',
            '/\[敌人\]/'   => $defender['username'] ?? '未知敌人',
            '/\[武器\]/'   => $weaponName,
            '/\[武器名\]/' => $baseWeaponName,
            '/\[技能\]/'   => $skill['name'] ?? '未知技能',
            '/\[伤害\]/'   => $damageResult['damage'] ?? 0,
            '/\[物理伤害\]/' => $damageResult['physical_damage'] ?? 0,
            '/\[元素伤害\]/' => $damageResult['elemental_damage'] ?? 0,
            '/\[暴击\]/'   => !empty($damageResult['is_critical']) ? '【暴击！】' : '',
        ];
        
        $parsed = preg_replace(array_keys($patterns), array_values($patterns), $processedDescription);
        
        // 3. 返回包含描述和标志位的数组
        return [
            'description' => trim(str_replace('  ', ' ', $parsed)),
            'has_damage_placeholder' => $hasDamagePlaceholder
        ];
    }

    /**
     * 计算技能施放所需的魔力消耗
     * 随着技能等级提高，魔力消耗降低（熟练度提升）
     * 
     * @param int $baseManaCost 技能模板中定义的基础魔力消耗
     * @param int $skillLevel 当前技能等级
     * @param string $rarity 技能稀有度
     * @param int $intelligence 玩家智力属性（可选）
     * @return int 计算出的魔力消耗值
     */
    public static function calculateSkillManaCost($baseManaCost, $skillLevel, $rarity, $intelligence = null) {
        // 稀有度系数 - 越稀有的技能基础消耗越高
        $rarityMultipliers = [
            'common' => 1.0,
            'uncommon' => 1.2,
            'rare' => 1.5,
            'epic' => 2.0,
            'legendary' => 3.0,
        ];
        
        $rarityMultiplier = $rarityMultipliers[$rarity] ?? 1.0;
        
        // 等级减免因子 - 随着等级提高，消耗降低（最多降低30%）
        // 等级越高，降低的越多，但收益递减
        $levelDiscountFactor = min(0.30, $skillLevel * 0.03);
        
        // 智力影响因子 - 智力越高，魔力消耗越少（可选）
        $intelligenceFactor = 0;
        if ($intelligence !== null) {
            // 每10点智力降低1%魔力消耗，最多降低20%
            $intelligenceFactor = min(0.20, $intelligence / 10 * 0.01);
        }
        
        // 计算最终魔力消耗
        // 基础消耗 * 稀有度系数 * (1 - 等级减免 - 智力减免)
        $finalManaCost = $baseManaCost * $rarityMultiplier * (1 - $levelDiscountFactor - $intelligenceFactor);
        
        // 向上取整，确保消耗至少为1
        return max(1, (int)ceil($finalManaCost));
    }

    /**
     * 计算合成物品所需的金币费用
     * 费用基于合成等级和产出物品的价值计算
     * 
     * @param int $craftLevel 配方合成等级
     * @param int $resultItemPrice 产出物品的商店价格
     * @param int $resultQuantity 产出物品的数量
     * @return int 计算出的合成费用
     */
    public static function calculateCraftingFee($craftLevel, $resultItemPrice, $resultQuantity = 1) {
        // 基础费用为物品价值的10%
        $baseFee = ($resultItemPrice * $resultQuantity) * 2;
        
        // 合成等级越高，额外费用越高（每级增加5%）
        $levelMultiplier = 1.0 + (($craftLevel - 1) * 0.05);
        
        // 计算最终费用
        $finalFee = ceil($baseFee * $levelMultiplier);
        
        // 确保最低费用为1金币
        return max(1, $finalFee);
    }

    /**
     * 计算PVP战斗中的伤害
     * 与PVE不同，PVP中需要考虑更多的平衡性因素
     * @param array $attacker 攻击者数据
     * @param array $defender 防御者数据
     * @return array 伤害计算结果
     */
    public static function calculatePvpDamage($attacker, $defender) {
        $log = [];
        $totalDamage = 0;
        
        // --- 闪避检查 ---
        $dodgeChance = self::calculatePvpDodgeChance($attacker, $defender);
        $randomValue = mt_rand(0, 10000) / 100;
        
        if ($randomValue <= $dodgeChance) {
            return [
                'damage' => 0,
                'physical_damage' => 0,
                'elemental_damage' => 0,
                'is_critical' => false,
                'is_dodged' => true,
                'log' => $log
            ];
        }
        
        // --- 物理伤害计算 ---
        $rawDamage = $attacker['attributes']['attack'];
        $mitigation = 1 - (($defender['attributes']['defense'] ?? 0) / (($defender['attributes']['defense'] ?? 0) + 200));
        $baseDamage = $rawDamage * max(0.05, $mitigation);
        $critChance = 5 + (($attacker['attributes']['agility'] ?? 0) / 10);
        $isCritical = (mt_rand(1, 1000) / 10) <= $critChance;
        if ($isCritical) $baseDamage *= 1.5;
        $variance = mt_rand(-10, 10) / 100;
        $physicalDamageThisTurn = max(1, round($baseDamage * (1 + $variance)));
        
        // --- 元素伤害计算 ---
        $elementalDamageDetails = [];
        $elementalLog = [];
        $totalElementalDamage = 0;
        $elementalTypes = ['fire', 'ice', 'wind', 'electric'];
        
        foreach ($elementalTypes as $type) {
            $damageKey = "{$type}_damage";
            $resistanceKey = "{$type}_resistance";
            $attackerDamage = $attacker['attributes'][$damageKey] ?? 0;
            $defenderResistance = $defender['attributes'][$resistanceKey] ?? 0;
            
            // PVP中抗性公式：抗性越高收益越高（非线性递减）
            // 使用公式: 1 / (1 + resistance/100)，让高抗性有更好的收益
            // 抗性0时减伤0%，抗性100时减伤50%，抗性200时减伤66.7%，抗性300时减伤75%
            $resistanceMultiplier = 1 / (1 + ($defenderResistance / 100));
            $elementalDamage = max(0, $attackerDamage * $resistanceMultiplier);

            if ($elementalDamage > 0) {
                $totalElementalDamage += $elementalDamage;
                $elementalDamageDetails[] = self::$damageTypeNames[$damageKey] . "({$elementalDamage})";
                $elementalLog[] = self::$damageTypeNames[$damageKey] . ":{$elementalDamage}";
            }
        }
        
        // 应用PVP伤害系数，使战斗更耐久
        $pvpDamageModifier = 0.8;
        $physicalDamageThisTurn = floor($physicalDamageThisTurn * $pvpDamageModifier);
        $totalElementalDamage = floor($totalElementalDamage * $pvpDamageModifier);
        
        $totalDamage = $physicalDamageThisTurn + $totalElementalDamage;
        
        // --- 生成战斗描述 ---
        $weaponName = $attacker['weapon_name'] ?? null;
        
        // 使用更丰富的描述
        if ($weaponName) {
            $actionVerbs = ["挥舞", "使用", "挥动", "操纵", "祭出", "紧握", "挥击", "重劈", "刺出", "舞动"];
            $actionVerb = $actionVerbs[array_rand($actionVerbs)];
            
            $elementalDesc = "";
            if (!empty($elementalDamageDetails)) {
                $elementalDesc = "，附带" . implode("、", $elementalDamageDetails);
            }
            
            $critPrefix = $isCritical ? "【暴击！】" : "";
            $log[] = "{$critPrefix}{$attacker['username']} {$actionVerb} <{$weaponName}>攻击了{$defender['username']}，造成了 {$totalDamage} 点伤害{$elementalDesc}！";
        } else {
            $elementalDesc = "";
            if (!empty($elementalDamageDetails)) {
                $elementalDesc = "，附带" . implode("、", $elementalDamageDetails);
            }
            
            $critPrefix = $isCritical ? "【暴击！】" : "";
            $log[] = "{$critPrefix}{$attacker['username']} 攻击了 {$defender['username']}，造成了 {$totalDamage} 点伤害{$elementalDesc}！";
        }
        
        return [
            'damage' => $totalDamage,
            'physical_damage' => $physicalDamageThisTurn,
            'elemental_damage' => $totalElementalDamage,
            'is_critical' => $isCritical,
            'is_dodged' => false,
            'log' => $log
        ];
    }
    
    /**
     * 计算PVP战斗中的闪避概率
     * @param array $attacker 攻击者数据
     * @param array $defender 防御者数据
     * @return float 闪避百分比 (0-100)
     */
    public static function calculatePvpDodgeChance($attacker, $defender) {
        $attackerAttrs = $attacker['attributes'];
        $defenderAttrs = $defender['attributes'];
        
        // 基础闪避率
        $baseDodge = 5;
        
        // 防御者敏捷提供的闪避加成
        $agilityBonus = $defenderAttrs['agility'] * 0.2;
        
        // 攻击者敏捷降低目标闪避几率
        $attackerPrecision = $attackerAttrs['agility'] * 0.1;
        
        // 防御者闪避加成
        $dodgeBonus = $defenderAttrs['dodge_bonus'] ?? 0;
        
        // 计算最终闪避率，PVP中降低闪避上限，避免战斗难以进行
        $finalDodgeChance = min(30, $baseDodge + $agilityBonus + $dodgeBonus - $attackerPrecision);
        
        // 确保闪避率不低于5%
        return max(5, $finalDodgeChance);
    }

    /**
     * 计算PVP战斗中失败者的掉落物品和金币
     * @param array $loser 失败者数据，包含inventory和attributes
     * @return array 返回掉落的物品和金币信息
     */
    public static function calculatePvpDrops($loser) {
        $drops = [
            'gold' => 0,
            'items' => []
        ];
        
        // 计算掉落的金币数量 (10% - 20% 的玩家金币)
        $goldPercentage = mt_rand(10, 20) / 100;
        $totalGold = $loser['attributes']['gold'] ?? 0;
        $droppedGold = floor($totalGold * $goldPercentage);
        
        // 设置最小和最大掉落金额
        $droppedGold = max(10, min($droppedGold, 10000));
        $drops['gold'] = $droppedGold;
        
        // 如果玩家没有物品，直接返回
        if (empty($loser['inventory']) || count($loser['inventory']) == 0) {
            return $drops;
        }
        
        // 过滤掉武器和已装备的物品
        $availableItems = [];
        foreach ($loser['inventory'] as $item) {
            if ($item['category'] != 'Equipment' && $item['is_equipped'] == 0) {
                $availableItems[] = $item;
            }
        }
        
        // 如果没有可掉落的物品，直接返回
        if (empty($availableItems)) {
            return $drops;
        }
        
        // 随机掉落1-2种物品
        $dropItemTypes = mt_rand(1, 2);
        $dropItemTypes = min($dropItemTypes, count($availableItems));
        
        // 随机选择物品类型
        shuffle($availableItems);
        $selectedItems = array_slice($availableItems, 0, $dropItemTypes);
        
        foreach ($selectedItems as $item) {
            // 每种物品掉落1-5个，但不超过玩家拥有的数量
            $dropQuantity = min(mt_rand(1, 5), $item['quantity']);
            
            // 转换物品类别为中文
            $categoryEn = $item['category'] ?? 'Unknown';
            $categoryZh = self::$itemCategoryNames[$categoryEn] ?? '未知';
            
            $drops['items'][] = [
                'inventory_id' => $item['id'],
                'item_template_id' => $item['item_template_id'],
                'name' => $item['name'],
                'description' => $item['description'] ?? '',
                'category' => $categoryZh, // 使用中文类别
                'category_en' => $categoryEn, // 保留英文类别
                'effects' => $item['effects'] ?? '',
                'quantity' => $dropQuantity,
                'stackable' => $item['stackable'] ?? 0, // 添加物品是否可堆叠属性
                'max_stack' => $item['max_stack'] ?? 1 // 添加物品最大堆叠数量
            ];
        }
        
        return $drops;
    }

    /**
     * 检查玩家是否可以使用公聊功能
     * 
     * @param int $playerLevel 玩家等级
     * @return bool 是否可以使用公聊
     */
    public static function canUsePublicChat($playerLevel) {
        return $playerLevel >= self::$requiredLevel;
    }

    public static function canUsePublicChatLevel() {
        return self::$requiredLevel;
    }
} 