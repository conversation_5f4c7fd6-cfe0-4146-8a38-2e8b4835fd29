/* 战斗日志管理页面样式 */
.page-content {
    padding: 20px;
}

/* Filter and Cleanup Sections */
.filter-section, .cleanup-section {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
}

.filter-section .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

.filter-section .form-row .form-group {
    margin-bottom: 0;
}

.cleanup-section {
    display: flex;
    align-items: flex-end;
    gap: 15px;
}

.cleanup-section .form-group {
    margin-bottom: 0;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    font-size: 14px;
}

.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.btn-primary {
    background-color: #4a6da7;
    color: white;
}

.btn-primary:hover {
    background-color: #3a5a8f;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

.btn-sm {
    padding: 5px 10px;
    font-size: 12px;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* Logs Table Styling */
.battle-logs-container {
    background-color: #fff;
    padding: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.logs-header h3 {
    margin: 0;
    font-size: 1.2em;
}

.pagination-info {
    font-size: 0.9em;
    color: #666;
}

.logs-table-container {
    overflow-x: auto;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
}

.logs-table th, .logs-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #f0f0f0;
}

.logs-table thead th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.logs-table tbody tr:hover {
    background-color: #f5f5f5;
}

.badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 0.8em;
    color: #fff;
    text-transform: capitalize;
}

.result-victory { background-color: #28a745; }
.result-defeated { background-color: #dc3545; }
.result-flee { background-color: #ffc107; color: #212529;}
.result-all_fled { background-color: #fd7e14; }
.result-last_player_left { background-color: #6c757d; }
.result-disconnect { background-color: #17a2b8; }

/* Pagination Controls */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 20px;
    gap: 5px;
}

.pagination-controls .page-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination-controls .page-ellipsis {
    padding: 0 5px;
}

/* Modal Styling */
.modal-large {
    max-width: 80%;
    width: 1000px;
}

.battle-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.battle-info-section h3 {
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
    margin-bottom: 10px;
    font-size: 1.1em;
    color: #333;
}

.info-table {
    width: 100%;
}
.info-table td {
    padding: 5px 0;
}
.info-table td:nth-child(odd) {
    font-weight: bold;
    width: 15%;
}
.info-table td:nth-child(even) {
    width: 35%;
}

#modal-participants {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}
.participant-tag {
    background-color: #e9ecef;
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 0.9em;
}

.battle-log-container {
    background-color: #f8f9fa;
    border: 1px solid #eee;
    padding: 15px;
    border-radius: 5px;
    max-height: 400px;
    overflow-y: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 0.9em;
}

.log-entry {
    padding: 2px 0;
    border-bottom: 1px dashed #ddd;
}
.log-critical { color: #dc3545; font-weight: bold; }
.log-dodge { color: #ffc107; font-style: italic; }
.log-reward { color: #28a745; }
.log-defeat { color: #6c757d; }

/* 提示框样式 */
.toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background-color: #333;
    color: white;
    padding: 10px 20px;
    border-radius: 4px;
    display: none;
    z-index: 1100;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

/* 战斗结果标签样式 */
.result-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
}

/* 操作按钮样式 */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    color: #4a6da7;
    padding: 5px;
    margin: 0 2px;
    font-size: 16px;
}

.action-btn:hover {
    color: #3a5a8f;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .form-group {
        min-width: 100%;
    }
    
    .modal-content {
        width: 95%;
        margin: 10% auto;
    }
} 