document.addEventListener('DOMContentLoaded', function() {
    const playerList = document.getElementById('player-list');
    const searchInput = document.getElementById('player-search-input');
    const inventoryDisplay = document.getElementById('inventory-display');
    const addItemModal = document.getElementById('addItemModal');
    const closeButton = addItemModal.querySelector('.modal-close-button');
    const modalAddItemBtn = document.getElementById('modal-add-item-btn');
    const categoryFilter = document.getElementById('modal-item-category-filter');
    const searchItemInput = document.getElementById('modal-item-search-input');
    const itemSelect = $('#modal-item-template-select');
    const playerDetails = document.getElementById('player-details');
    const noPlayerSelected = document.querySelector('.no-player-selected');

    let allPlayers = [];
    let allItems = [];
    let allScenes = [];
    let selectedPlayerId = null;
    let selectedPlayerName = '';
    let playerAttributes = {};

    const categoryMap = {
        'Equipment': '装备', 'Gem': '宝石', 'Material': '材料',
        'Potion': '药品', 'Rune': '符石', 'Misc': '杂物', 'Scroll': '书卷'
    };
    
    const questStatusMap = {
        'active': '进行中',
        'completed': '已完成',
        'failed': '失败',
        'abandoned': '已放弃'
    };

    // --- Toast Notification Function ---
    function showToast(message, type = 'success', duration = 3000) {
        const container = document.getElementById('toast-container');
        if (!container) return;

        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        toast.textContent = message;

        container.appendChild(toast);

        // Animate in
        setTimeout(() => {
            toast.classList.add('show');
        }, 10);

        // Animate out and remove
        setTimeout(() => {
            toast.classList.remove('show');
            toast.addEventListener('transitionend', () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            });
        }, duration);
    }

    // --- 初始化 ---
    function initialize() {
        Promise.all([
            fetch('api_player_inventory.php?action=get_all_players').then(res => res.json()),
            fetch('api_items.php?action=get_all').then(res => res.json()),
            fetch('api_player_inventory.php?action=get_all_scenes').then(res => res.json())
        ]).then(([playersResponse, itemsResponse, scenesResponse]) => {
            if (playersResponse.success) {
                allPlayers = playersResponse.data;
                renderPlayerList();
            }
            if (itemsResponse.success) {
                allItems = itemsResponse.data;
                populateItemFilters();
            }
            if (scenesResponse.success) {
                allScenes = scenesResponse.data;
                populateSceneDropdown();
            }
        }).catch(error => showToast(`初始化失败: ${error}`, 'error'));

        searchInput.addEventListener('input', renderPlayerList);
        categoryFilter.addEventListener('change', updateItemSelection);
        searchItemInput.addEventListener('input', updateItemSelection);
        
        // 选项卡切换事件
        document.querySelectorAll('.tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabTarget = this.getAttribute('data-target');
                
                // 更新激活标签
                document.querySelectorAll('.tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 更新激活内容
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabTarget).classList.add('active');
            });
        });
        
        // 属性保存按钮
        document.getElementById('save-attributes-btn').addEventListener('click', saveAttributes);
        
        // 属性重置按钮
        document.getElementById('reset-attributes-btn').addEventListener('click', () => {
            if (confirm('确定要重置所有修改吗？未保存的更改将丢失。')) {
                populateAttributeForm(playerAttributes);
            }
        });
    }

    // --- 填充场景下拉列表 ---
    function populateSceneDropdown() {
        const sceneSelect = document.getElementById('attr-current_scene_id');
        sceneSelect.innerHTML = '';
        
        allScenes.forEach(scene => {
            const option = document.createElement('option');
            option.value = scene.id;
            option.textContent = scene.name;
            sceneSelect.appendChild(option);
        });
    }

    // --- 玩家列表渲染 ---
    function renderPlayerList() {
        const searchTerm = searchInput.value.toLowerCase();
        const filteredPlayers = allPlayers.filter(player => {
            return player.username.toLowerCase().includes(searchTerm);
        });

        playerList.innerHTML = '';
        filteredPlayers.forEach(player => {
            const li = document.createElement('li');
            
            // 添加在线状态指示器
            const playerStatus = document.createElement('div');
            playerStatus.className = 'player-status';
            
            const statusIndicator = document.createElement('span');
            statusIndicator.className = `status-indicator ${player.is_online ? 'status-online' : 'status-offline'}`;
            
            const playerName = document.createElement('span');
            playerName.textContent = `${player.username} (等级 ${player.level || 1}) [ID: ${player.id}]`;
            
            playerStatus.appendChild(statusIndicator);
            playerStatus.appendChild(playerName);
            
            li.appendChild(playerStatus);
            
            // 添加地点信息
            if (player.scene_name) {
                const location = document.createElement('span');
                location.className = 'player-location';
                location.textContent = `位置: ${player.scene_name}`;
                li.appendChild(location);
            }

            // 添加最后登录时间信息
            if (player.formatted_logindate) {
                const loginDate = document.createElement('span');
                loginDate.className = 'player-logindate';
                loginDate.textContent = `最后登录: ${player.formatted_logindate}`;
                loginDate.style.fontSize = '0.85em';
                loginDate.style.color = '#666';
                li.appendChild(loginDate);
            }
            
            li.dataset.playerId = player.id;
            li.dataset.playerName = player.username;
            if (player.id === selectedPlayerId) {
                li.classList.add('active');
            }
            playerList.appendChild(li);
        });
    }

    // --- 新增：填充物品筛选器 ---
    function populateItemFilters() {
        const categories = [...new Set(allItems.map(item => item.category))];
        const translatedCategories = categories.map(cat => ({ key: cat, value: categoryMap[cat] || cat })).sort((a,b) => a.value.localeCompare(b.value, 'zh-Hans-CN'));
        
        translatedCategories.forEach(cat => {
            const option = document.createElement('option');
            option.value = cat.key;
            option.textContent = cat.value;
            categoryFilter.appendChild(option);
        });
    }

    // --- 新增：更新物品选择列表 ---
    function updateItemSelection() {
        const selectedCategory = categoryFilter.value;
        const searchTerm = searchItemInput.value.toLowerCase();

        const filteredItems = allItems.filter(item => {
            const matchesCategory = !selectedCategory || item.category === selectedCategory;
            const matchesSearch = !searchTerm || item.name.toLowerCase().includes(searchTerm);
            return matchesCategory && matchesSearch;
        });

        const select2data = filteredItems.map(item => ({
            id: item.id,
            text: `${item.name} (ID: ${item.id})`
        }));
        
        itemSelect.empty().select2({
            placeholder: '选择一个物品模板',
            dropdownParent: $('#addItemModal .modal-content'),
            data: select2data
        }).trigger('change');
    }

    // --- 物品展示渲染 ---
    function renderInventory(inventoryData) {
        const { equipped, backpack } = inventoryData;
        
        // 注意：这里的inventoryDisplay是全局变量，但现在只渲染物品部分
        const container = document.getElementById('inventory-display'); // 重新获取容器
        
        // 清理旧的物品内容 (保留头部)
        container.querySelectorAll('.inventory-category-group, .no-items-message').forEach(el => el.remove());

        if (equipped.length === 0 && backpack.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'no-items-message';
            emptyMessage.innerHTML = '<p>该玩家背包无物品</p>';
            container.appendChild(emptyMessage);
            return;
        }

        // 对背包物品按分类进行分组
        const groupedBackpack = backpack.reduce((acc, item) => {
            const category = categoryMap[item.category] || '杂项';
            if (!acc[category]) {
                acc[category] = [];
            }
            acc[category].push(item);
            return acc;
        }, {});
        
        // 渲染装备
        renderCategory(container, '当前装备', equipped);
        
        // 渲染背包物品
        const sortedCategories = Object.keys(groupedBackpack).sort();
        sortedCategories.forEach(category => {
            renderCategory(container, category, groupedBackpack[category]);
        });
    }
    
    function renderCategory(container, title, items) {
        if (items.length === 0) return;

        const categoryGroup = document.createElement('div');
        categoryGroup.className = 'inventory-category-group';
        
        const categoryTitle = document.createElement('h4');
        categoryTitle.textContent = title;
        categoryGroup.appendChild(categoryTitle);

        const grid = document.createElement('div');
        grid.className = 'items-grid';

        items.forEach(item => {
            grid.appendChild(createItemCard(item));
        });

        categoryGroup.appendChild(grid);
        container.appendChild(categoryGroup);
    }
    
    function createItemCard(item) {
        const card = document.createElement('div');
        card.className = 'item-card';

        const header = document.createElement('div');
        header.className = 'item-card-header';

        // Item name and quantity
        const nameWrapper = document.createElement('div');
        nameWrapper.className = 'item-name-wrapper';
        const displayName = item.instance_data && JSON.parse(item.instance_data).display_name 
            ? JSON.parse(item.instance_data).display_name.replace(/§/g, '<span class="gem-suffix">').replace(/§/g, '</span>')
            : item.name;
        nameWrapper.innerHTML = `<h5>${displayName}</h5> ${item.stackable == 1 ? `<span class="item-quantity">x${item.quantity}</span>` : ''}`;

        // Deletion controls
        const controlsWrapper = document.createElement('div');
        controlsWrapper.className = 'item-controls';

        if (item.stackable == 1) {
            const qtyInput = document.createElement('input');
            qtyInput.type = 'number';
            qtyInput.className = 'form-control form-control-sm';
            qtyInput.value = 1;
            qtyInput.min = 1;
            qtyInput.max = item.quantity;

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-danger btn-sm';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = () => {
                handleItemDeletion(item.inventory_id, qtyInput.value);
            };

            controlsWrapper.appendChild(qtyInput);
            controlsWrapper.appendChild(deleteBtn);
        } else {
            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-danger btn-sm';
            deleteBtn.textContent = '删除';
            deleteBtn.onclick = () => {
                handleItemDeletion(item.inventory_id, 1);
            };
            controlsWrapper.appendChild(deleteBtn);
        }

        header.appendChild(nameWrapper);
        header.appendChild(controlsWrapper);
        card.appendChild(header);

        return card;
    }
    
    // --- Modal Logic ---
    function showAddItemModal() {
        if (!selectedPlayerId) {
            showToast('请先选择一个玩家。', 'error');
            return;
        }
        
        const modalTitle = addItemModal.querySelector('#modalTitle');
        modalTitle.textContent = `向 ${selectedPlayerName} 添加物品`;

        // 只有在第一次打开时才初始化select2
        if (!itemSelect.data('select2')) {
             itemSelect.select2({
                placeholder: '选择一个物品模板',
                dropdownParent: $('#addItemModal .modal-content'),
             });
        }
        
        // 每次打开时都更新物品列表
        updateItemSelection();
       
        addItemModal.style.display = 'flex';
    }

    function hideAddItemModal() {
        addItemModal.style.display = 'none';
    }

    // --- 事件处理 ---
    playerList.addEventListener('click', function(e) {
        const liElement = e.target.closest('li');
        if (liElement) {
            const newPlayerId = liElement.dataset.playerId;
            if (newPlayerId !== selectedPlayerId) {
                selectedPlayerId = newPlayerId;
                selectedPlayerName = liElement.dataset.playerName;
                
                const lastActive = playerList.querySelector('.active');
                if(lastActive) lastActive.classList.remove('active');
                liElement.classList.add('active');

                loadPlayerDetails(selectedPlayerId);
            }
        }
    });

    // Add item button in header
    document.getElementById('add-item-to-player-btn').addEventListener('click', () => {
        if (selectedPlayerId) {
            showAddItemModal();
        } else {
            showToast('请先选择一个玩家', 'error');
        }
    });

    function handleItemDeletion(inventoryId, quantity) {
        if (!inventoryId || !quantity || quantity <= 0) {
            showToast('无效的删除请求。', 'error');
            return;
        }
        
        if (!confirm(`确定要删除 ${quantity} 个此物品吗？`)) {
            return;
        }

        const formData = new FormData();
        formData.append('action', 'delete_item');
        formData.append('inventory_id', inventoryId);
        formData.append('quantity', quantity);
        formData.append('player_id', selectedPlayerId);

        fetch('api_player_inventory.php', {
            method: 'POST',
            body: formData
        })
        .then(res => res.json())
        .then(response => {
            if (response.success) {
                // Refresh the inventory view
                fetchInventory(selectedPlayerId);
                showToast(response.message || '物品已删除。');
            } else {
                showToast('删除失败: ' + response.message, 'error');
            }
        })
        .catch(error => {
            console.error("删除物品时出错:", error);
            showToast('删除物品时发生网络错误。', 'error');
        });
    }

    function handleAddItem() {
        const itemTemplateId = document.getElementById('modal-item-template-select').value;
        const quantity = document.getElementById('modal-add-item-quantity').value;

        if (!itemTemplateId || !selectedPlayerId) {
            showToast('请选择一个物品和一个玩家。', 'error');
            return;
        }

        const formData = new FormData();
        formData.append('player_id', selectedPlayerId);
        formData.append('item_template_id', itemTemplateId);
        formData.append('quantity', quantity);

        fetch('api_player_inventory.php?action=add_item', {
            method: 'POST',
            body: formData
        })
        .then(res => res.json())
        .then(response => {
            hideAddItemModal();
            if (response.success) {
                fetchInventory(selectedPlayerId);
                showToast(response.message || '物品添加成功！');
            } else {
                showToast(`添加失败: ${response.message}`, 'error');
            }
        })
        .catch(error => {
            hideAddItemModal();
            console.error("添加物品失败:", error);
            showToast(`添加物品失败: ${error}`, 'error');
        });
    }

    // --- 加载玩家详情，包括背包、属性、任务等 ---
    function loadPlayerDetails(playerId) {
        // 显示玩家详情，隐藏提示
        playerDetails.style.display = 'block';
        noPlayerSelected.style.display = 'none';
        
        // 获取详细信息
        fetch(`api_player_inventory.php?action=get_player_details&player_id=${playerId}`)
            .then(res => res.json())
            .then(response => {
                if (response.success) {
                    const { player, quests, skills } = response.data;
                    
                    // 设置基本信息
                    document.getElementById('player-name').textContent = player.username;
                    document.getElementById('player-level').textContent = `等级: ${player.level || 1}`;
                    document.getElementById('player-location').textContent = player.scene_name ? `位置: ${player.scene_name}` : '未知位置';
                    document.getElementById('player-id').textContent = `ID: ${player.id}`;
                    document.getElementById('player-logindate').textContent = player.formatted_logindate ? `最后登录: ${player.formatted_logindate}` : '从未登录';
                    
                    // 保存属性信息并填充表单
                    playerAttributes = {...player};
                    populateAttributeForm(player);
                    
                    // 渲染任务列表
                    renderQuests(quests);
                    
                    // 渲染技能列表
                    renderSkills(skills);
                    
                    // 加载背包
                    fetchInventory(playerId);
                } else {
                    showToast(response.message, 'error');
                }
            })
            .catch(error => {
                showToast(`获取玩家信息失败: ${error}`, 'error');
            });
    }
    
    // --- 填充属性编辑表单 ---
    function populateAttributeForm(player) {
        // 基础属性
        document.getElementById('attr-level').value = player.level || 1;
        document.getElementById('attr-experience').value = player.experience || 0;
        document.getElementById('attr-experience_to_next_level').value = player.experience_to_next_level || 100;
        
        // 生命/魔法
        document.getElementById('attr-hp').value = player.hp || 0;
        document.getElementById('attr-max_hp').value = player.max_hp || 100;
        document.getElementById('attr-mp').value = player.mp || 0;
        document.getElementById('attr-max_mp').value = player.max_mp || 50;
        
        // 战斗属性
        document.getElementById('attr-attack').value = player.attack || 0;
        document.getElementById('attr-defense').value = player.defense || 0;
        document.getElementById('attr-attack_speed').value = player.attack_speed || 1.00;
        document.getElementById('attr-dodge_bonus').value = player.dodge_bonus || 0;
        
        // 基础属性点
        document.getElementById('attr-strength').value = player.strength || 5;
        document.getElementById('attr-agility').value = player.agility || 5;
        document.getElementById('attr-constitution').value = player.constitution || 5;
        document.getElementById('attr-intelligence').value = player.intelligence || 5;
        
        // 元素抗性
        document.getElementById('attr-fire_resistance').value = player.fire_resistance || 0;
        document.getElementById('attr-ice_resistance').value = player.ice_resistance || 0;
        document.getElementById('attr-wind_resistance').value = player.wind_resistance || 0;
        document.getElementById('attr-electric_resistance').value = player.electric_resistance || 0;
        
        // 元素伤害
        document.getElementById('attr-fire_damage').value = player.fire_damage || 0;
        document.getElementById('attr-ice_damage').value = player.ice_damage || 0;
        document.getElementById('attr-wind_damage').value = player.wind_damage || 0;
        document.getElementById('attr-electric_damage').value = player.electric_damage || 0;
        
        // 职业和位置
        if (document.getElementById('attr-current_job_id').querySelector(`option[value="${player.current_job_id}"]`)) {
            document.getElementById('attr-current_job_id').value = player.current_job_id;
        }
        
        if (document.getElementById('attr-current_scene_id').querySelector(`option[value="${player.current_scene_id}"]`)) {
            document.getElementById('attr-current_scene_id').value = player.current_scene_id;
        }
        
        // 货币
        document.getElementById('attr-gold').value = player.gold || 0;
        document.getElementById('attr-diamonds').value = player.diamonds || 0;
        
        // 其他
        document.getElementById('attr-karma').value = player.karma || 0;
        document.getElementById('attr-rage').value = player.rage || 0;
    }
    
    // --- 保存属性更改 ---
    function saveAttributes() {
        if (!selectedPlayerId) {
            showToast('未选择玩家', 'error');
            return;
        }
        
        const attributesToUpdate = {};
        const attributeInputs = document.querySelectorAll('.attribute-input');
        
        attributeInputs.forEach(input => {
            const attributeName = input.name;
            const attributeValue = input.value;
            
            if (attributeValue != playerAttributes[attributeName]) {
                attributesToUpdate[attributeName] = attributeValue;
            }
        });
        
        if (Object.keys(attributesToUpdate).length === 0) {
            showToast('没有属性需要更新');
            return;
        }
        
        const updatePromises = [];
        
        for (const [attribute, value] of Object.entries(attributesToUpdate)) {
            const formData = new FormData();
            formData.append('player_id', selectedPlayerId);
            formData.append('attribute', attribute);
            formData.append('value', value);
            
            const promise = fetch('api_player_inventory.php?action=update_player_attribute', {
                method: 'POST',
                body: formData
            }).then(res => res.json());
            
            updatePromises.push(promise);
        }
        
        Promise.all(updatePromises)
            .then(responses => {
                const hasError = responses.some(res => !res.success);
                
                if (hasError) {
                    const errorMessages = responses.filter(res => !res.success).map(res => res.message).join(', ');
                    showToast(`部分属性更新失败: ${errorMessages}`, 'error');
                } else {
                    showToast('属性已成功更新');
                    
                    // 更新缓存的属性
                    attributeInputs.forEach(input => {
                        const attributeName = input.name;
                        playerAttributes[attributeName] = input.value;
                    });
                    
                    // 刷新玩家详情
                    loadPlayerDetails(selectedPlayerId);
                }
            })
            .catch(error => {
                showToast(`更新属性失败: ${error}`, 'error');
            });
    }
    
    // --- 渲染任务列表 ---
    function renderQuests(quests) {
        const questsTableBody = document.getElementById('quests-table-body');
        questsTableBody.innerHTML = '';
        
        if (!quests || quests.length === 0) {
            const emptyRow = document.createElement('tr');
            const emptyCell = document.createElement('td');
            emptyCell.colSpan = 4;
            emptyCell.className = 'empty-quest-message';
            emptyCell.innerHTML = '<i class="fas fa-scroll"></i> 该玩家没有任何任务记录';
            emptyRow.appendChild(emptyCell);
            questsTableBody.appendChild(emptyRow);
            return;
        }
        
        quests.forEach(quest => {
            const row = document.createElement('tr');
            
            // 任务名称
            const nameCell = document.createElement('td');
            nameCell.textContent = quest.quest_title || `任务 #${quest.quest_id}`;
            
            // 状态
            const statusCell = document.createElement('td');
            const statusSpan = document.createElement('span');
            statusSpan.className = `quest-status ${quest.status}`;
            statusSpan.textContent = questStatusMap[quest.status] || quest.status;
            statusCell.appendChild(statusSpan);
            
            // 开始时间
            const startedCell = document.createElement('td');
            const startedSpan = document.createElement('span');
            startedSpan.className = 'quest-time';
            startedSpan.textContent = formatDate(quest.started_at);
            startedCell.appendChild(startedSpan);
            
            // 完成时间
            const completedCell = document.createElement('td');
            const completedSpan = document.createElement('span');
            completedSpan.className = 'quest-time';
            completedSpan.textContent = quest.completed_at ? formatDate(quest.completed_at) : '-';
            completedCell.appendChild(completedSpan);
            
            row.appendChild(nameCell);
            row.appendChild(statusCell);
            row.appendChild(startedCell);
            row.appendChild(completedCell);
            
            questsTableBody.appendChild(row);
        });
    }
    
    // --- 渲染技能列表 ---
    function renderSkills(skills) {
        const skillsGrid = document.querySelector('.skills-grid');
        skillsGrid.innerHTML = '';
        
        if (!skills || skills.length === 0) {
            const emptyMessage = document.createElement('div');
            emptyMessage.className = 'no-items-message';
            emptyMessage.style.gridColumn = '1 / -1';
            emptyMessage.innerHTML = '<p>该玩家未学习任何技能</p>';
            skillsGrid.appendChild(emptyMessage);
            return;
        }
        
        skills.forEach(skill => {
            const card = document.createElement('div');
            card.className = 'skill-card';
            
            const icon = document.createElement('div');
            icon.className = 'skill-icon';
            if (skill.skill_icon) {
                icon.innerHTML = `<img src="../assets/skills/${skill.skill_icon}" alt="${skill.skill_name}" width="32" height="32">`;
            } else {
                icon.textContent = skill.skill_name.charAt(0);
            }
            
            const info = document.createElement('div');
            info.className = 'skill-info';
            
            const name = document.createElement('h5');
            name.textContent = skill.skill_name || `技能 #${skill.skill_template_id}`;
            
            const level = document.createElement('div');
            level.className = 'skill-level';
            level.textContent = `等级: ${skill.skill_level}`;
            
            info.appendChild(name);
            info.appendChild(level);
            
            card.appendChild(icon);
            card.appendChild(info);
            
            skillsGrid.appendChild(card);
        });
    }
    
    // --- 格式化日期 ---
    function formatDate(dateString) {
        if (!dateString) return '-';
        const date = new Date(dateString);
        return date.toLocaleString('zh-CN');
    }

    // --- 获取背包 ---
    function fetchInventory(playerId) {
        fetch(`api_player_inventory.php?action=get_inventory&player_id=${playerId}`)
            .then(res => res.json())
            .then(response => {
                if (response.success) {
                    const { currencies } = response.data;
                    document.getElementById('gold-amount').textContent = (currencies.gold || 0).toLocaleString();
                    document.getElementById('diamonds-amount').textContent = (currencies.diamonds || 0).toLocaleString();
                    renderInventory(response.data);
                } else {
                    showToast(response.message, 'error');
                    inventoryDisplay.innerHTML += `<p>加载失败: ${response.message}</p>`;
                }
            })
            .catch(error => {
                showToast(`获取背包失败: ${error}`, 'error');
                inventoryDisplay.innerHTML += `<p>加载背包时发生错误。</p>`;
            });
    }

    // --- 启动 ---
    initialize();

    // Modal event listeners
    closeButton.addEventListener('click', hideAddItemModal);
    window.addEventListener('click', (event) => {
        if (event.target === addItemModal) {
            hideAddItemModal();
        }
    });
    modalAddItemBtn.addEventListener('click', handleAddItem);
}); 