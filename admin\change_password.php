<?php
$pageTitle = "修改管理员密码";
$currentPage = "change_password";

require_once '../config/Database.php';
require_once 'auth.php';

$db = Database::getInstance()->getConnection();
$success_message = '';
$error_message = '';

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $current_password = $_POST['current_password'] ?? '';
    $new_password = $_POST['new_password'] ?? '';
    $confirm_password = $_POST['confirm_password'] ?? '';
    
    // 验证输入
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error_message = '请填写所有字段';
    } elseif ($new_password !== $confirm_password) {
        $error_message = '新密码和确认密码不匹配';
    } elseif (strlen($new_password) < 6) {
        $error_message = '新密码长度至少6位';
    } else {
        try {
            // 获取当前管理员信息
            $admin_id = $_SESSION['admin_id'];
            $stmt = $db->prepare("SELECT password_hash FROM admins WHERE id = ?");
            $stmt->execute([$admin_id]);
            $admin = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$admin) {
                $error_message = '管理员账户不存在';
            } elseif (!password_verify($current_password, $admin['password_hash'])) {
                $error_message = '当前密码错误';
            } else {
                // 更新密码
                $new_password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                $update_stmt = $db->prepare("UPDATE admins SET password_hash = ? WHERE id = ?");
                $update_stmt->execute([$new_password_hash, $admin_id]);

                // 记录日志
                error_log("管理员密码修改成功 - 用户: " . $_SESSION['admin_username'] . " ID: " . $admin_id);

                // 密码修改成功后立即清除会话，强制重新登录
                session_destroy();

                // 设置成功消息并重定向到登录页面
                header("Location: index.php?message=" . urlencode("密码修改成功，请重新登录"));
                exit;
            }
        } catch (Exception $e) {
            $error_message = '密码修改失败: ' . $e->getMessage();
            error_log("管理员密码修改失败: " . $e->getMessage());
        }
    }
}

include 'layout_header.php';
?>

<div class="container-fluid">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <i class="fas fa-key mr-2"></i>修改管理员密码
                </div>
                <div class="card-body">
                    <?php if ($success_message): ?>
                    <div class="alert alert-success">
                        <i class="fas fa-check-circle mr-1"></i>
                        <?= htmlspecialchars($success_message) ?>
                    </div>
                    <?php endif; ?>
                    
                    <?php if ($error_message): ?>
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-1"></i>
                        <?= htmlspecialchars($error_message) ?>
                    </div>
                    <?php endif; ?>
                    
                    <form method="post" action="" id="password-form">
                        <div class="form-group">
                            <label for="current_password">
                                <i class="fas fa-lock mr-1"></i>当前密码:
                            </label>
                            <input type="password" class="form-control" id="current_password" 
                                   name="current_password" required autocomplete="current-password">
                        </div>
                        
                        <div class="form-group">
                            <label for="new_password">
                                <i class="fas fa-key mr-1"></i>新密码:
                            </label>
                            <input type="password" class="form-control" id="new_password" 
                                   name="new_password" required minlength="6" autocomplete="new-password">
                            <small class="form-text text-muted">密码长度至少6位</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="confirm_password">
                                <i class="fas fa-check mr-1"></i>确认新密码:
                            </label>
                            <input type="password" class="form-control" id="confirm_password" 
                                   name="confirm_password" required minlength="6" autocomplete="new-password">
                        </div>
                        
                        <div class="form-group text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="fas fa-save mr-1"></i>修改密码
                            </button>
                            <a href="dashboard.php" class="btn btn-secondary btn-lg ml-2">
                                <i class="fas fa-times mr-1"></i>取消
                            </a>
                        </div>
                    </form>
                </div>
            </div>
            
            <!-- 安全提示 -->
            <div class="card mt-4">
                <div class="card-header">
                    <i class="fas fa-shield-alt mr-2"></i>安全提示
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success mr-2"></i>密码长度至少6位</li>
                        <li><i class="fas fa-check text-success mr-2"></i>建议使用字母、数字和特殊字符组合</li>
                        <li><i class="fas fa-check text-success mr-2"></i>定期更换密码以确保账户安全</li>
                        <li><i class="fas fa-check text-success mr-2"></i>不要与他人分享管理员密码</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 500;
}

.form-group label {
    font-weight: 500;
    color: #495057;
}

.btn-lg {
    padding: 0.75rem 2rem;
    font-size: 1.1rem;
    border-radius: 6px;
}

.alert {
    border-radius: 6px;
    border: none;
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

.list-unstyled li {
    padding: 0.25rem 0;
}
</style>

<script>
$(document).ready(function() {
    // 密码确认验证
    $('#confirm_password').on('input', function() {
        var newPassword = $('#new_password').val();
        var confirmPassword = $(this).val();
        
        if (confirmPassword && newPassword !== confirmPassword) {
            $(this).addClass('is-invalid');
            if (!$(this).next('.invalid-feedback').length) {
                $(this).after('<div class="invalid-feedback">密码不匹配</div>');
            }
        } else {
            $(this).removeClass('is-invalid');
            $(this).next('.invalid-feedback').remove();
        }
    });
    
    // 表单提交验证
    $('#password-form').on('submit', function(e) {
        var newPassword = $('#new_password').val();
        var confirmPassword = $('#confirm_password').val();
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('新密码和确认密码不匹配');
            return false;
        }
        
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('新密码长度至少6位');
            return false;
        }
        
        return confirm('确定要修改密码吗？');
    });
});
</script>

<?php include 'layout_footer.php'; ?>
