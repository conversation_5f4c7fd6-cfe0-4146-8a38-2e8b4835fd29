<?php
// classes/BattleSystem.php

require_once __DIR__ . '/../config/formulas.php';

class BattleSystem {
    
    // 中文属性名称映射，用于生成更友好的战斗日志
    public static $attributeNames = [
        'hp' => '生命', 'max_hp' => '最大生命', 'mp' => '法力', 'max_mp' => '最大法力',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智慧',
        'attack' => '攻击', 'defense' => '防御', 'attack_speed' => '攻速',
        'fire_resistance' => '火焰抗性', 'ice_resistance' => '冰冻抗性', 'wind_resistance' => '风裂抗性', 'electric_resistance' => '闪电抗性',
        'fire_damage' => '火焰伤害', 'ice_damage' => '冰冻伤害', 'wind_damage' => '风裂伤害', 'electric_damage' => '闪电伤害',
        'dodge_bonus' => '闪避加成'
    ];
    
    // ATB系统常量
    const ATB_BASE_MAX = 500; // 基础ATB最大值，会根据参战者的攻速动态调整
    const ROUND_TIME_LIMIT = 3000; // 3秒回合时间限制（毫秒）
    const ATB_MULTIPLIER = 2.0; // ATB增长速度的基础倍率
    
    private $db;

    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * 检查并初始化战斗状态中的闪避属性
     * 
     * @param array &$battleState 战斗状态的引用
     * @return void
     */
    public function ensureDodgeAttributes(array &$battleState): void {
        // 确保怪物有闪避属性
        if (!isset($battleState['monster']['attributes']['dodge_bonus'])) {
            $battleState['monster']['attributes']['dodge_bonus'] = 0; // 默认值
            
            // 尝试从数据库获取怪物的闪避值
            $monsterId = $battleState['monster']['id'] ?? null;
            if ($monsterId && $this->db) {
                try {
                    $conn = $this->db->getConnection();
                    $stmt = $conn->prepare("SELECT dodge_bonus FROM monster_templates WHERE id = ?");
                    $stmt->execute([$monsterId]);
                    $result = $stmt->fetch(PDO::FETCH_ASSOC);
                    if ($result && isset($result['dodge_bonus'])) {
                        $battleState['monster']['attributes']['dodge_bonus'] = (int)$result['dodge_bonus'];
                    }
                } catch (Exception $e) {
                    // 记录错误但继续使用默认值
                    // error_log("无法获取怪物 {$monsterId} 的闪避值: " . $e->getMessage());
                }
            }
        }
        
        // 确保所有玩家有闪避属性
        foreach ($battleState['players'] as $playerId => &$player) {
            if (!isset($player['attributes']['dodge_bonus'])) {
                $player['attributes']['dodge_bonus'] = 0; // 默认值
                
                // 尝试从数据库获取玩家的闪避值
                if ($this->db) {
                    try {
                        $conn = $this->db->getConnection();
                        $stmt = $conn->prepare("SELECT dodge_bonus FROM player_attributes WHERE account_id = ?");
                        $stmt->execute([$playerId]);
                        $result = $stmt->fetch(PDO::FETCH_ASSOC);
                        if ($result && isset($result['dodge_bonus'])) {
                            $player['attributes']['dodge_bonus'] = (int)$result['dodge_bonus'];
                        }
                    } catch (Exception $e) {
                        // 记录错误但继续使用默认值
                        // error_log("无法获取玩家 {$playerId} 的闪避值: " . $e->getMessage());
                    }
                }
            }
        }
    }
    
    /**
     * 初始化战斗状态的ATB数据
     * @param array &$battleState 战斗状态引用
     */
    public function initializeAtbState(array &$battleState): void {
        // 确保闪避属性已初始化
        $this->ensureDodgeAttributes($battleState);
        
        // 计算ATB系统参数
        $atbParams = $this->calculateAtbParameters($battleState);
        
        // 为每个玩家初始化单独的ATB值
        $playerAtbValues = [];
        foreach ($battleState['players'] as $playerId => $player) {
            $playerAtbValues[$playerId] = 0;
        }
        
        $battleState['atb_state'] = [
            'playerATBs' => $playerAtbValues, // 每个玩家的ATB值
            'monsterATB' => 0,
            'roundStartTime' => microtime(true) * 1000, // 毫秒时间戳
            'lastUpdateTime' => microtime(true) * 1000,
            'lastActorId' => null, // 上一个行动者的ID，用于避免同一玩家连续行动
            'atbMax' => $atbParams['atbMax'], // 动态计算的ATB最大值
            'speedMultipliers' => $atbParams['speedMultipliers'] // 每个参与者的速度倍率
        ];
    }
    
    /**
     * 计算ATB系统参数，基于参战者的属性
     * @param array $battleState 战斗状态
     * @return array ATB系统参数
     */
    private function calculateAtbParameters(array $battleState): array {
        // 获取所有参战者的攻速
        $allSpeeds = [];
        $speedMultipliers = [];
        
        // 获取怪物的攻速
        $monsterSpeed = $battleState['monster']['attributes']['attack_speed'] ?? 10;
        $allSpeeds[] = $monsterSpeed;
        $speedMultipliers['monster'] = 1.0; // 初始值
        
        // 获取所有玩家的攻速
        foreach ($battleState['players'] as $playerId => $player) {
            $playerSpeed = $player['attributes']['attack_speed'] ?? 10;
            $allSpeeds[] = $playerSpeed;
            $speedMultipliers[$playerId] = 1.0; // 初始值
        }
        
        // 计算平均攻速和最大攻速
        $avgSpeed = count($allSpeeds) > 0 ? array_sum($allSpeeds) / count($allSpeeds) : 10;
        $maxSpeed = count($allSpeeds) > 0 ? max($allSpeeds) : 10;
        
        // 计算ATB最大值，基于平均攻速
        // 攻速越高，ATB最大值越小，使得行动更快
        $atbMax = self::ATB_BASE_MAX * (1 + (10 / max(1, $avgSpeed)));
        
        // 调整每个参与者的速度倍率，使其相对于平均值更平衡
        $speedMultipliers['monster'] = ($monsterSpeed / max(1, $avgSpeed)) * self::ATB_MULTIPLIER;
        
        foreach ($battleState['players'] as $playerId => $player) {
            $playerSpeed = $player['attributes']['attack_speed'] ?? 10;
            $speedMultipliers[$playerId] = ($playerSpeed / max(1, $avgSpeed)) * self::ATB_MULTIPLIER;
        }
        
        return [
            'atbMax' => round($atbMax),
            'speedMultipliers' => $speedMultipliers
        ];
    }
    
    /**
     * 更新ATB状态并检查是否有角色可以行动
     * @param array &$battleState 战斗状态引用
     * @return array|null 如果有角色可以行动，返回行动信息；否则返回null
     */
    public function updateAtbState(array &$battleState): ?array {
        if ($battleState['is_over']) {
            return null;
        }
        
        if (!isset($battleState['atb_state'])) {
            $this->initializeAtbState($battleState);
            return null;
        }
        
        $currentTime = microtime(true) * 1000;
        $deltaTime = $currentTime - $battleState['atb_state']['lastUpdateTime'];
        $battleState['atb_state']['lastUpdateTime'] = $currentTime;
        
        // 计算回合已经过去的时间
        $elapsedTime = $currentTime - $battleState['atb_state']['roundStartTime'];
        $isTimeUp = $elapsedTime >= self::ROUND_TIME_LIMIT;
        
        // 获取ATB最大值和速度倍率
        $atbMax = $battleState['atb_state']['atbMax'] ?? self::ATB_BASE_MAX;
        $speedMultipliers = $battleState['atb_state']['speedMultipliers'] ?? [];
        
        // 更新怪物的ATB值
        $monsterSpeedMultiplier = $speedMultipliers['monster'] ?? 1.0;
        if ($battleState['atb_state']['monsterATB'] < $atbMax && $battleState['monster']['attributes']['hp'] > 0) {
            $battleState['atb_state']['monsterATB'] += $monsterSpeedMultiplier * ($deltaTime / 1000) * 100;
        }
        
        // 确保怪物ATB值不超过最大值
        $battleState['atb_state']['monsterATB'] = min($battleState['atb_state']['monsterATB'], $atbMax);
        
        // 更新每个活着的玩家的ATB值
        $maxPlayerATB = 0;
        $maxPlayerATBId = null;
        
        foreach ($battleState['players'] as $playerId => $player) {
            if ($player['attributes']['hp'] <= 0) {
                // 已死亡的玩家不更新ATB
                continue;
            }
            
            $playerSpeedMultiplier = $speedMultipliers[$playerId] ?? 1.0;
            
            // 如果这个玩家的ATB值尚未初始化，初始化为0
            if (!isset($battleState['atb_state']['playerATBs'][$playerId])) {
                $battleState['atb_state']['playerATBs'][$playerId] = 0;
            }
            
            // 更新玩家ATB值
            if ($battleState['atb_state']['playerATBs'][$playerId] < $atbMax) {
                $battleState['atb_state']['playerATBs'][$playerId] += $playerSpeedMultiplier * ($deltaTime / 1000) * 100;
            }
            
            // 确保玩家ATB值不超过最大值
            $battleState['atb_state']['playerATBs'][$playerId] = min($battleState['atb_state']['playerATBs'][$playerId], $atbMax);
            
            // 跟踪ATB值最高的玩家
            if ($battleState['atb_state']['playerATBs'][$playerId] > $maxPlayerATB) {
                $maxPlayerATB = $battleState['atb_state']['playerATBs'][$playerId];
                $maxPlayerATBId = $playerId;
            }
        }
        
        // 检查是否有角色可以行动
        $monsterReady = $battleState['atb_state']['monsterATB'] >= $atbMax && $battleState['monster']['attributes']['hp'] > 0;
        $playerReady = $maxPlayerATB >= $atbMax && $maxPlayerATBId !== null;
        
        if ($monsterReady || $playerReady || $isTimeUp) {
            // 如果时间到了，但没有角色准备好行动
            if ($isTimeUp && !$monsterReady && !$playerReady) {
                // 比较怪物和ATB最高的玩家，决定谁来行动
                if ($maxPlayerATB >= $battleState['atb_state']['monsterATB']) {
                    return $this->prepareAction($battleState, 'player', $maxPlayerATBId);
                } else {
                    return $this->prepareAction($battleState, 'monster');
                }
            }
            
            // 如果怪物和玩家都准备好行动
            if ($monsterReady && $playerReady) {
                // 优先让上次没行动的一方行动
                $lastActorId = $battleState['atb_state']['lastActorId'] ?? null;
                
                if ($lastActorId === 'monster') {
                    return $this->prepareAction($battleState, 'player', $maxPlayerATBId);
                } else {
                    return $this->prepareAction($battleState, 'monster');
                }
            }
            
            // 如果只有玩家准备好行动
            if ($playerReady) {
                return $this->prepareAction($battleState, 'player', $maxPlayerATBId);
            }
            
            // 如果只有怪物准备好行动
            if ($monsterReady) {
                return $this->prepareAction($battleState, 'monster');
            }
        }
        
        return null;
    }
    
    /**
     * 准备角色行动
     * @param array &$battleState 战斗状态引用
     * @param string $actorType 行动者类型（'player'或'monster'）
     * @param string|null $playerId 如果是玩家行动，需要提供玩家ID
     * @return array 行动信息
     */
    private function prepareAction(array &$battleState, string $actorType, ?string $playerId = null): array {
        // 重置行动者的ATB值
        if ($actorType === 'player' && $playerId !== null) {
            $battleState['atb_state']['playerATBs'][$playerId] = 0;
            $battleState['atb_state']['lastActorId'] = $playerId;
        } else {
            $battleState['atb_state']['monsterATB'] = 0;
            $battleState['atb_state']['lastActorId'] = 'monster';
        }
        
        // 重置回合开始时间
        $battleState['atb_state']['roundStartTime'] = microtime(true) * 1000;

        // 在玩家回合开始时减少该玩家的技能冷却时间
        // 这样确保冷却检查发生在减少之后，但不会在同一个请求中立即减少刚设置的冷却
        if ($actorType === 'player' && $playerId !== null) {
            $this->decreaseSkillCooldowns($battleState, $actorType, $playerId);
        }

        return [
            'actor_type' => $actorType,
            'player_id' => $playerId,
            'action' => 'attack' // 默认行动是攻击
        ];
    }

    /**
     * 减少技能冷却时间（移植自PVP系统）
     * @param array &$battleState 战斗状态引用
     * @param string $actorType 行动者类型（'player'或'monster'）
     * @param string|null $playerId 如果是玩家行动，需要提供玩家ID
     */
    private function decreaseSkillCooldowns(array &$battleState, string $actorType, ?string $playerId = null): void {
        // 只有玩家回合时才减少技能冷却，怪物回合不减少
        if ($actorType !== 'player' || $playerId === null) {
            // error_log("BattleSystem::decreaseSkillCooldowns - 跳过冷却减少，行动者: {$actorType}" . ($playerId ? " (玩家ID: {$playerId})" : " (无玩家ID)"));
            return;
        }

        if (!isset($battleState['skill_cooldowns'])) {
            // error_log("BattleSystem::decreaseSkillCooldowns - 没有技能冷却数据");
            return;
        }

        // error_log("BattleSystem::decreaseSkillCooldowns - 开始减少技能冷却，玩家回合: {$playerId}");
        // error_log("BattleSystem::decreaseSkillCooldowns - 减少前的冷却状态: " . json_encode($battleState['skill_cooldowns']));

        // 只减少当前行动玩家的技能冷却时间
        if (isset($battleState['skill_cooldowns'][$playerId])) {
            $playerCooldowns = &$battleState['skill_cooldowns'][$playerId];
            foreach ($playerCooldowns as $skillId => &$cooldownTurns) {
                if ($cooldownTurns > 0) {
                    $oldCooldown = $cooldownTurns;
                    $cooldownTurns--;
                    // error_log("BattleSystem::decreaseSkillCooldowns - 玩家 {$playerId} 技能 {$skillId} 冷却: {$oldCooldown} -> {$cooldownTurns}");

                    // 如果冷却时间归零，从数组中移除
                    if ($cooldownTurns <= 0) {
                        // error_log("BattleSystem::decreaseSkillCooldowns - 玩家 {$playerId} 技能 {$skillId} 冷却结束");
                        unset($playerCooldowns[$skillId]);
                    }
                }
            }
            unset($cooldownTurns);

            // 如果玩家没有任何冷却中的技能，移除该玩家的记录
            if (empty($playerCooldowns)) {
                // error_log("BattleSystem::decreaseSkillCooldowns - 玩家 {$playerId} 没有冷却中的技能，移除记录");
                unset($battleState['skill_cooldowns'][$playerId]);
            }
        }

        // 如果没有任何玩家有冷却中的技能，移除整个冷却数组
        if (empty($battleState['skill_cooldowns'])) {
            // error_log("BattleSystem::decreaseSkillCooldowns - 没有任何冷却中的技能，移除整个冷却数组");
            unset($battleState['skill_cooldowns']);
        } else {
            // error_log("BattleSystem::decreaseSkillCooldowns - 减少后的冷却状态: " . json_encode($battleState['skill_cooldowns']));
        }
    }

    /**
     * 在行动完成后减少技能冷却时间（新的时机）
     * @param array &$battleState 战斗状态引用
     * @param string $actorType 行动者类型（'player'或'monster'）
     * @param string|null $playerId 如果是玩家行动，需要提供玩家ID
     */
    public function decreaseSkillCooldownsAfterAction(array &$battleState, string $actorType, ?string $playerId = null): void {
        // 只有玩家行动完成后才减少技能冷却，怪物行动不减少
        if ($actorType !== 'player' || $playerId === null) {
            // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 跳过冷却减少，行动者: {$actorType}" . ($playerId ? " (玩家ID: {$playerId})" : " (无玩家ID)"));
            return;
        }

        if (!isset($battleState['skill_cooldowns'])) {
            // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 没有技能冷却数据");
            return;
        }

        // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 行动完成后减少技能冷却，玩家: {$playerId}");
        // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 减少前的冷却状态: " . json_encode($battleState['skill_cooldowns']));

        // 只减少当前行动玩家的技能冷却时间
        if (isset($battleState['skill_cooldowns'][$playerId])) {
            $playerCooldowns = &$battleState['skill_cooldowns'][$playerId];
            foreach ($playerCooldowns as $skillId => &$cooldownTurns) {
                if ($cooldownTurns > 0) {
                    $oldCooldown = $cooldownTurns;
                    $cooldownTurns--;
                    // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 玩家 {$playerId} 技能 {$skillId} 冷却: {$oldCooldown} -> {$cooldownTurns}");

                    // 如果冷却时间归零，从数组中移除
                    if ($cooldownTurns <= 0) {
                        // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 玩家 {$playerId} 技能 {$skillId} 冷却结束");
                        unset($playerCooldowns[$skillId]);
                    }
                }
            }
            unset($cooldownTurns);

            // 如果玩家没有任何冷却中的技能，移除该玩家的记录
            if (empty($playerCooldowns)) {
                // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 玩家 {$playerId} 没有冷却中的技能，移除记录");
                unset($battleState['skill_cooldowns'][$playerId]);
            }
        }

        // 如果没有任何玩家有冷却中的技能，移除整个冷却数组
        if (empty($battleState['skill_cooldowns'])) {
            // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 没有任何冷却中的技能，移除整个冷却数组");
            unset($battleState['skill_cooldowns']);
        } else {
            // error_log("BattleSystem::decreaseSkillCooldownsAfterAction - 减少后的冷却状态: " . json_encode($battleState['skill_cooldowns']));
        }
    }

    /**
     * 获取当前ATB状态，用于发送给客户端
     * @param array $battleState 战斗状态
     * @return array ATB状态数据
     */
    public function getAtbStatus(array $battleState): array {
        if (!isset($battleState['atb_state'])) {
            return [
                'playerATBs' => [], // 每个玩家的ATB值
                'monsterATB' => 0,
                'roundTimeRemaining' => self::ROUND_TIME_LIMIT,
                'atbMax' => self::ATB_BASE_MAX // 默认ATB最大值
            ];
        }
        
        $currentTime = microtime(true) * 1000;
        $elapsedTime = $currentTime - $battleState['atb_state']['roundStartTime'];
        $remainingTime = max(0, self::ROUND_TIME_LIMIT - $elapsedTime);
        $atbMax = $battleState['atb_state']['atbMax'] ?? self::ATB_BASE_MAX;
        
        return [
            'playerATBs' => $battleState['atb_state']['playerATBs'],
            'monsterATB' => $battleState['atb_state']['monsterATB'],
            'roundTimeRemaining' => $remainingTime,
            'atbMax' => $atbMax
        ];
    }
    
    /**
     * Processes a single attack action. This is the new entry point for combat actions.
     * @param array &$battleState The reference to the current battle state.
     * @param string $attackerType Must be 'player' or 'monster'.
     * @param string|null $playerId The player's ID, required if attacker is a player.
     * @return array An array containing the updated state and the log of actions.
     */
    public function handleAttack(array &$battleState, string $attackerType, ?string $playerId = null): array {
        // 确保闪避属性已初始化
        $this->ensureDodgeAttributes($battleState);
        
        // 调试模式下，临时增加闪避值，方便测试闪避功能
        if (defined('DEBUG_DODGE') && DEBUG_DODGE) {
            // 为怪物增加闪避值
            $battleState['monster']['attributes']['dodge_bonus'] = 80; // 给怪物80%的闪避加成
            
            // 为所有玩家增加闪避值
            foreach ($battleState['players'] as &$player) {
                $player['attributes']['dodge_bonus'] = 80; // 给玩家80%的闪避加成
            }
        }
        
        if ($attackerType === 'player') {
            if (!$playerId) {
                return ['log' => ['错误: 玩家攻击时未指定玩家ID。'], 'state' => $battleState];
            }
            
            // 检查玩家是否正在吟唱，如果是，则处理吟唱回合
            if (isset($battleState['players'][$playerId]['casting_info'])) {
                return $this->handleCastingTurn($battleState, $playerId);
            }

            // 检查玩家是否被眩晕，无法进行任何操作
            if ($this->isPlayerStunned($battleState, $playerId)) {
                return [
                    'log' => ["{$battleState['players'][$playerId]['username']} 被眩晕了，无法行动！"],
                    'state' => $battleState,
                    'damage' => 0,
                    'player_id' => $playerId
                ];
            }
            
            $result = $this->playerAttack($battleState, $playerId);
            
            // 如果是闪避，记录在战斗统计中
            if (isset($result['is_dodged']) && $result['is_dodged']) {
                // 可以在这里添加闪避统计的代码
                // 例如：$battleState['stats']['dodges']++;
            }
            
            return $result;
        } elseif ($attackerType === 'monster') {
            $result = $this->monsterAttack($battleState);
            
            // 如果是闪避，记录在战斗统计中
            if (isset($result['is_dodged']) && $result['is_dodged']) {
                // 可以在这里添加闪避统计的代码
                // 例如：$battleState['stats']['player_dodges']++;
            }
            
            return $result;
        } else {
            return ['log' => ["错误: 未知的攻击者类型 '{$attackerType}'。"], 'state' => $battleState];
        }
    }

    private function playerAttack(array &$battleState, string $playerId): array {
        if (!isset($battleState['players'][$playerId])) {
             return ['log' => ["错误: 找不到玩家 {$playerId}。"], 'state' => $battleState, 'damage' => 0, 'player_id' => $playerId];
        }
        $player = &$battleState['players'][$playerId];
        $monster = &$battleState['monster'];

        $attackResult = Formulas::calculatePveDamage($player, $monster);
        $log = $attackResult['log'];
        
        // 检查是否闪避成功
        if (isset($attackResult['is_dodged']) && $attackResult['is_dodged']) {
            return [
                'log' => $log,
                'state' => $battleState,
                'damage' => 0,
                'player_id' => $playerId,
                'is_dodged' => true
            ];
        }
        
        $damage = $attackResult['damage']; // 获取伤害值（包括物理和元素伤害）

        $monster['attributes']['hp'] -= $damage;
            
        if ($monster['attributes']['hp'] <= 0) {
            $monster['attributes']['hp'] = 0;
            $log[] = "{$monster['username']} 被击败了！";
            $battleState['is_over'] = true;
            $battleState['winner'] = 'players';
        }
        
        // 返回伤害值，以便在processAttackResult中记录伤害贡献
        return [
            'log' => $log, 
            'state' => $battleState,
            'damage' => $damage, // 添加伤害值到返回结果中
            'player_id' => $playerId // 添加玩家ID到返回结果中
        ];
    }

    private function monsterAttack(array &$battleState): array {
        $monster = &$battleState['monster'];
        $log = [];

        // 检查怪物是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, 'monster')) {
            $log[] = "{$monster['username']} 被眩晕了，无法行动！";
            return ['state' => $battleState, 'log' => $log];
        }

        // --- NEW: Monster Skill Casting Logic ---
        if (!empty($monster['skills'])) {
            // 检查怪物是否被沉默，无法使用技能
            if ($this->isPlayerSilenced($battleState, 'monster')) {
                $log[] = "{$monster['username']} 被沉默了，无法使用技能！";
                // 沉默状态下只能普通攻击，跳到普通攻击逻辑
            } else {
                shuffle($monster['skills']);

                foreach ($monster['skills'] as $skillInfo) {
                    $skillId = $skillInfo['skill_template_id'];
                    $chance = (float)$skillInfo['cast_chance'];
                    $roll = (mt_rand(0, 10000) / 100);

                    if ($roll <= $chance) {
                        $db = Database::getInstance();
                        $skillTemplate = $db->query("SELECT mp_cost, skill_type, target_type, name FROM skill_templates WHERE id = ?", [$skillId])->fetch();

                        if (!$skillTemplate) {
                            continue;
                        }

                        // 检查技能是否是瞬发类型且有目标
                        $isInstantSkill = in_array($skillTemplate['skill_type'], ['COMBAT_INSTANT', 'DEBUFF']);
                        $hasValidTargetType = in_array($skillTemplate['target_type'], ['ENEMY', 'HOSTILE']);

                        if ($isInstantSkill && $hasValidTargetType) {
                            // 怪物施法不再检查MP
                            $log[] = $this->generateMonsterSkillCastDescription($monster, $skillTemplate);

                            // 使用通用的技能执行逻辑
                            $skillResult = $this->executeSkill($battleState, $monster['id'], 'monster', $skillId);

                            // 合并结果
                            $skillResult['log'] = array_merge($log, $skillResult['log']);
                            return $skillResult;
                        }
                    }
                }
            }
        }
        // --- END: New Logic ---
    
        // 如果没有技能被施放，则执行普通攻击
        $targetPlayerId = $this->selectRandomPlayerTarget($battleState);
        if (!$targetPlayerId) {
            return ['log' => ["{$monster['username']} 找不到攻击目标。"], 'state' => $battleState];
        }

        $targetPlayer = &$battleState['players'][$targetPlayerId];

        $attackResult = Formulas::calculatePveDamage($monster, $targetPlayer);
        $log = array_merge($log, $attackResult['log']);

        // 检查是否闪避成功
        if (isset($attackResult['is_dodged']) && $attackResult['is_dodged']) {
            return [
                'log' => $log, 
                'state' => $battleState,
                'target_player_id' => $targetPlayerId,
                'is_dodged' => true
            ];
        }
        
        $targetPlayer['attributes']['hp'] -= $attackResult['damage'];
        
        if ($targetPlayer['attributes']['hp'] <= 0) {
            $targetPlayer['attributes']['hp'] = 0;
            $log[] = "{$targetPlayer['username']} 已被击败！";
        }

        $allPlayersDefeated = count(array_filter($battleState['players'], fn($p) => $p['attributes']['hp'] > 0)) === 0;
        
        if($allPlayersDefeated) {
            $battleState['is_over'] = true;
            $battleState['winner'] = 'monster';
        }

        return [
            'log' => $log, 
            'state' => $battleState,
            'target_player_id' => $targetPlayerId
        ];
    }
    
    /**
     * 生成怪物施法前摇的随机描述
     * @param array $monster 怪物数据
     * @param array $skill 技能数据
     * @return string 施法前摇描述
     */
    private function generateMonsterSkillCastDescription(array $monster, array $skill): string {
        $monsterName = $monster['username'];
        $skillName = $skill['name'];

        $descriptions = [
            "{$monsterName}的眼中闪过一丝狡黠，口中念念有词，似乎在准备着什么...",
            "空气中的元素开始骚动，{$monsterName}的周围凝聚起危险的能量！",
            "{$monsterName}发出一声低吼，强大的魔力从它体内迸发，即将施放【{$skillName}】！",
            "一道不祥的光环在{$monsterName}脚下浮现，它要施展【{$skillName}】了！",
            "{$monsterName}举起了它的武器，对准了它的下一个目标，吟唱起了【{$skillName}】。",
            "战场的气氛突然变得凝重，{$monsterName}正在引导一股强大的力量。",
            "你感觉到一股寒意，只见{$monsterName}露出了残忍的笑容，开始准备法术。",
            "大地微微颤抖，{$monsterName}似乎在与深渊沟通，准备释放毁灭性的【{$skillName}】。",
            "符文在{$monsterName}的身体上若隐若现，它正在激活一项古老的技能。",
            "{$monsterName}的身影变得模糊，它在积蓄力量，准备发动致命一击！",
        ];

        return $descriptions[array_rand($descriptions)];
    }

    public function handleHeal(array &$battleState, string $playerId): array
    {
        $player = &$battleState['players'][$playerId];
        $log = [];

        // 检查玩家是否正在吟唱，如果是，则忽略本次行动，直接处理吟唱进度
        if (isset($player['casting_info'])) {
            return $this->handleCastingTurn($battleState, $playerId);
        }
        
        if ($player['attributes']['hp'] <= 0) {
            $log[] = "{$player['username']} 已经倒下，无法使用治疗。";
            return ['log' => $log, 'state' => $battleState];
        }
        
        $healAmount = 20; // 基础治疗量
        $healAmount += $player['attributes']['intelligence'] * 0.5; // 智力加成
        $healAmount = round($healAmount);
        
        $player['attributes']['hp'] = min($player['attributes']['hp'] + $healAmount, $player['attributes']['max_hp']);
        $log[] = "{$player['username']} 使用治疗，恢复了 {$healAmount} 点生命值。";
        
        return ['log' => $log, 'state' => $battleState];
    }
    
    public function handleTurn(array $battleState, string $playerId, string $action): array {
        // 此方法已不再使用，保留以兼容旧代码
        return ['log' => ["错误: handleTurn方法已弃用。"], 'state' => $battleState];
    }

    public function handleFlee(array &$battleState, string $playerId): array {
        $player = $battleState['players'][$playerId];
        $monster = $battleState['monster'];
        $log = [];

        // 检查玩家是否正在吟唱，如果是，则忽略本次行动，直接处理吟唱进度
        if (isset($battleState['players'][$playerId]['casting_info'])) {
            return $this->handleCastingTurn($battleState, $playerId);
        }

        // 检查玩家是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, $playerId)) {
            $log[] = "{$player['username']} 被眩晕了，无法行动！";
            return ['log' => $log, 'state' => $battleState];
        }
        
        // 使用新的公式计算逃跑成功率
        $fleeChance = Formulas::calculateFleeChance($player, $monster);
        $roll = mt_rand(0, 100) / 100;
        $success = $roll <= $fleeChance;

        if ($success) {
            $log[] = "{$player['username']} 成功逃离了战斗！";
            
            // 保存逃跑玩家的状态
            $this->savePlayerState($playerId, $player);
            
            return [
                'state' => $battleState,
                'log' => $log,
                'fled_player_id' => $playerId
            ];
        } else {
            // 生成更丰富的失败描述
            $failReason = $this->generateFleeFailReason($player, $monster);
            $log[] = "{$player['username']} 试图逃跑，但{$failReason}！";
            return ['state' => $battleState, 'log' => $log];
        }
    }
    
    /**
     * 生成逃跑失败的随机原因描述
     * @param array $player 玩家数据
     * @param array $monster 怪物数据
     * @return string 失败原因描述
     */
    private function generateFleeFailReason($player, $monster): string {
        $reasons = [
            "被{$monster['username']}拦住了去路",
            "被{$monster['username']}抓住了衣角",
            "因恐惧而脚步踉跄",
            "被{$monster['username']}的气势震慑",
            "失败了",
            "因慌乱而摔倒",
            "被{$monster['username']}预判到了行动",
            "被{$monster['username']}的攻击逼回",
            "因地形阻碍无法脱身"
        ];
        
        return $reasons[array_rand($reasons)];
    }

    public function handleUseItem(array &$battleState, string $playerId, string $itemType): array {
        $player = &$battleState['players'][$playerId];
        $log = [];
        $conn = $this->db->getConnection();

        // 检查玩家是否正在吟唱，如果是，则忽略本次行动，直接处理吟唱进度
        if (isset($player['casting_info'])) {
            return $this->handleCastingTurn($battleState, $playerId);
        }

        // 检查玩家是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($battleState, $playerId)) {
            $log[] = "{$player['username']} 被眩晕了，无法行动！";
            return ['state' => $battleState, 'log' => $log];
        }
        
        // 检查玩家当前状态是否需要使用药水
        if ($itemType === 'hp' && $player['attributes']['hp'] >= $player['attributes']['max_hp']) {
            $log[] = "{$player['username']} 的生命值已满，无需使用回血药水。";
            return ['state' => $battleState, 'log' => $log];
        }
        
        if ($itemType === 'mp' && $player['attributes']['mp'] >= $player['attributes']['max_mp']) {
            $log[] = "{$player['username']} 的魔力值已满，无需使用回蓝药水。";
            return ['state' => $battleState, 'log' => $log];
        }

        $effectToFind = $itemType === 'hp' ? 'hp' : 'mp';

        // 1. 获取玩家配置的药品ID
        $stmt = $conn->prepare("SELECT combat_hp_potion_id, combat_mp_potion_id FROM player_attributes WHERE account_id = ?");
        $stmt->execute([$playerId]);
        $config = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $configuredItemId = $itemType === 'hp' ? $config['combat_hp_potion_id'] : $config['combat_mp_potion_id'];
        $itemToUse = null;
        $usedSubstitute = false;

        // 2. 检查玩家是否拥有配置的药品
        if ($configuredItemId) {
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.quantity, it.name, it.effects 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.player_id = ? AND it.id = ? AND inv.quantity > 0"
            );
            $stmt->execute([$playerId, $configuredItemId]);
            $itemToUse = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
        }

        // 3. 如果没有配置的药品或已用完，则寻找替代品
        if (!$itemToUse) {
            $usedSubstitute = true;
            // 使用兼容所有MySQL版本的查询方式
            $stmt = $conn->prepare(
                "SELECT inv.id as inventory_id, inv.quantity, it.name, it.effects 
                 FROM player_inventory inv
                 JOIN item_templates it ON inv.item_template_id = it.id
                 WHERE inv.player_id = ? AND it.category = 'Potion' AND it.effects LIKE ? AND inv.quantity > 0"
            );
            $stmt->execute([$playerId, '%"'.$effectToFind.'"%']);
            
            // 获取所有匹配的药水
            $potions = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            // 使用PHP手动排序，找到效果最好的药水
            $bestPotion = null;
            $bestValue = 0;
            
            foreach ($potions as $potion) {
                $effects = json_decode($potion['effects'], true);
                $value = $effects[$effectToFind] ?? 0;
                
                if ($value > $bestValue) {
                    $bestValue = $value;
                    $bestPotion = $potion;
                }
            }
            
            $itemToUse = $bestPotion;
        }
        
        // 4. 如果找到药品并使用
        if ($itemToUse) {
            $effects = json_decode($itemToUse['effects'], true);
            $value = $effects[$effectToFind] ?? 0;

            if ($itemType === 'hp') {
                $player['attributes']['hp'] = min($player['attributes']['max_hp'], $player['attributes']['hp'] + $value);
            } else {
                $player['attributes']['mp'] = min($player['attributes']['max_mp'], $player['attributes']['mp'] + $value);
            }
            
            $log[] = "{$player['username']} 使用了 {$itemToUse['name']}，恢复了 {$value} 点" . strtoupper($itemType) . "。";
            if ($usedSubstitute && $configuredItemId) {
                $log[] = "(配置的药品已用完，自动使用了替代品)";
            }
            
            // 更新库存
            if ($itemToUse['quantity'] > 1) {
                $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - 1 WHERE id = ?");
                $updateStmt->execute([$itemToUse['inventory_id']]);
            } else {
                $deleteStmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                $deleteStmt->execute([$itemToUse['inventory_id']]);
            }

            // 添加提示信息，告知玩家下一回合将自动切换为攻击
            $log[] = "下一回合将自动切换为攻击。";
            
            return ['state' => $battleState, 'log' => $log];
        }

        // 5. 如果没有找到任何可用药品，则转为普通攻击
        $log[] = "{$player['username']} 试图使用药品，但没有找到任何可用的" . strtoupper($itemType) . "药水，只好发动了攻击！";
        $attackResult = $this->playerAttack($battleState, $playerId);
        
        return [
            'state' => $attackResult['state'],
            'log' => array_merge($log, $attackResult['log']),
            'damage' => $attackResult['damage'],
            'player_id' => $playerId
        ];
    }

    /**
     * 处理吟唱过程中的回合
     * @param array &$battleState 战斗状态引用
     * @param string $playerId 玩家ID
     * @return array 结果
     */
    public function handleCastingTurn(array &$battleState, string $playerId): array {
        $castingInfo = &$battleState['players'][$playerId]['casting_info'];
        $player = &$battleState['players'][$playerId];
        $log = [];
        
        // 推进吟唱进度
        $castingInfo['rounds_passed']++;
        
        if ($castingInfo['rounds_passed'] >= $castingInfo['total_rounds']) {
            // 吟唱完成
            $log = ["{$player['username']}完成了【{$castingInfo['skill_name']}】的吟唱！"];
            
            $skillId = $castingInfo['skill_id'];
            
            // 清除吟唱状态，必须在执行技能前完成，以防bug
            unset($battleState['players'][$playerId]['casting_info']);
            
            // 执行技能效果
            $skillResult = $this->executeSkill($battleState, $playerId, 'player', $skillId);
            
            // 设置技能冷却时间
            $db = Database::getInstance();
            $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
            $skill = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if ($skill && isset($skill['cooldown_turns']) && $skill['cooldown_turns'] > 0) {
                if (!isset($battleState['skill_cooldowns'])) $battleState['skill_cooldowns'] = [];
                if (!isset($battleState['skill_cooldowns'][$playerId])) $battleState['skill_cooldowns'][$playerId] = [];
                // 直接使用配置的冷却回合数，不再+1
                $battleState['skill_cooldowns'][$playerId][$skillId] = $skill['cooldown_turns'];
            }
            
            $finalLog = array_merge($log, $skillResult['log']);
            
            return [
                'log' => $finalLog,
                'state' => $skillResult['state'],
                'damage' => $skillResult['damage'] ?? 0,
                'player_id' => $playerId,
                'action_completed' => 'cast_finished' // 新增：明确告知施法已完成
            ];
            
        } else {
            // 吟唱未完成，继续吟唱
            $log[] = "{$player['username']} 继续吟唱【{$castingInfo['skill_name']}】... ({$castingInfo['rounds_passed']}/{$castingInfo['total_rounds']})";
            return ['log' => $log, 'state' => $battleState, 'player_id' => $playerId];
        }
    }

    /**
     * 手动设置战斗中角色的闪避值（用于测试）
     * 
     * @param array &$battleState 战斗状态的引用
     * @param string $targetType 目标类型，'player'或'monster'
     * @param string|null $playerId 如果是玩家，需要提供玩家ID
     * @param int $dodgeValue 要设置的闪避值
     * @return array 操作结果信息
     */
    public function setDodgeBonus(array &$battleState, string $targetType, ?string $playerId = null, int $dodgeValue = 0): array {
        if ($targetType === 'player' && $playerId !== null && isset($battleState['players'][$playerId])) {
            $battleState['players'][$playerId]['attributes']['dodge_bonus'] = $dodgeValue;
        } elseif ($targetType === 'monster') {
            $battleState['monster']['attributes']['dodge_bonus'] = $dodgeValue;
        }
        return $battleState;
    }

    private function selectRandomPlayerTarget(array &$state): ?string {
        $alivePlayers = array_filter($state['players'], fn($p) => $p['attributes']['hp'] > 0);
        if (empty($alivePlayers)) {
            return null;
        }
        return array_rand($alivePlayers);
    }

    private function executeSkill(&$state, $casterId, $casterType, $skillId) {
        $log = [];

        // 1. 获取技能模板
        $db = Database::getInstance();
        $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
        $skillTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        if (!$skillTemplate) {
            return ['log' => ["尝试使用了一个不存在的技能。"], 'state' => $state, 'damage' => 0];
        }

        // 2. 确定施法者
        $caster = null;
        if ($casterType === 'player') {
            if (!isset($state['players'][$casterId])) {
                return ['log' => ["施法者玩家不存在。"], 'state' => $state];
            }
            $caster = &$state['players'][$casterId];
        } elseif ($casterType === 'monster') {
            $caster = &$state['monster'];
        } else {
            return ['log' => ["未知的施法者类型。"], 'state' => $state];
        }
        
        // $log[] = "{$caster['username']} 施放了【{$skillTemplate['name']}】！";
    
        // 3. 确定目标
        $target = null;
        $targetId = null; 
        $targetType = null; 

        if ($casterType === 'player') {
            switch ($skillTemplate['target_type']) {
                case 'ENEMY':
                    $target = &$state['monster'];
                    $targetId = 'monster';
                    $targetType = 'monster';
                    break;
                case 'HOSTILE':
                    // 玩家的群体攻击：只能攻击怪物（PVE中玩家不能群攻其他玩家）
                    $target = &$state['monster'];
                    $targetId = 'monster';
                    $targetType = 'monster';
                    break;
                case 'SELF':
                    $target = &$caster;
                    $targetId = $casterId;
                    $targetType = 'player';
                    break;
                case 'ALLY':
                    $target = &$caster;
                    $targetId = $casterId;
                    $targetType = 'player';
                    break;
                default:
                    $log[] = "【{$skillTemplate['name']}】的目标类型未知。";
                    return ['state' => $state, 'log' => $log, 'damage' => 0];
            }
        } elseif ($casterType === 'monster') {
            switch ($skillTemplate['target_type']) {
                case 'ENEMY':
                    $targetPlayerId = $this->selectRandomPlayerTarget($state);
                    if ($targetPlayerId) {
                        $target = &$state['players'][$targetPlayerId];
                        $targetId = $targetPlayerId;
                        $targetType = 'player';
                    }
                    break;
                case 'HOSTILE':
                    // 怪物的群体攻击：攻击所有存活的玩家
                    return $this->executeGroupAttack($state, $caster, $skillTemplate, $skillId);
                case 'SELF':
                    $target = &$caster;
                    $targetId = $caster['id'];
                    $targetType = 'monster';
                    break;
                default:
                    $log[] = "怪物技能【{$skillTemplate['name']}】的目标类型未知或不支持。";
                    return ['state' => $state, 'log' => $log, 'damage' => 0];
            }
        }

        if (!$target || $target['attributes']['hp'] <= 0) {
            $log[] = "但目标已经死亡或不存在。";
            return ['state' => $state, 'log' => $log, 'damage' => 0];
        }
    
        // 4. 计算伤害 (如果技能有伤害公式)
        $damageResult = ['damage' => 0, 'physical_damage' => 0, 'elemental_damage' => 0, 'is_critical' => false, 'is_dodged' => false];
        $skillEffects = json_decode($skillTemplate['effects'], true);

        if (isset($skillEffects['damage_formula'])) {
            try {
                $skillLevel = 1; 
                if($casterType === 'player' && isset($state['player_skills'][$casterId][$skillId]['level'])) {
                    $skillLevel = $state['player_skills'][$casterId][$skillId]['level'];
                }
                $options = ['skill_level' => $skillLevel];

                $baseDamage = Formulas::evaluateDamageFormula($skillEffects['damage_formula'], $caster, $target, $options);

                // 为技能添加暴击判定
                $critChance = 5 + (($caster['attributes']['agility'] ?? 0) / 10);
                $isCritical = (mt_rand(1, 1000) / 10) <= $critChance;
                if ($isCritical) {
                    $baseDamage *= 1.5;
                }
                
                $finalDamage = max(0, floor($baseDamage));

                // 填充伤害结果
                $damageResult['damage'] = $finalDamage;
                $damageResult['is_critical'] = $isCritical;

                $element = $skillEffects['element'] ?? 'physical_damage';
                if ($element === 'physical_damage') {
                    $damageResult['physical_damage'] = $finalDamage;
                } else {
                    $damageResult['elemental_damage'] = $finalDamage;
                }

            } catch (Exception $e) {
                $log[] = "伤害公式计算错误: " . $e->getMessage();
                return ['state' => $state, 'log' => $log, 'damage' => 0];
            }
        
            // 5. 应用伤害和生成日志
            $target['attributes']['hp'] -= $damageResult['damage'];

            if (!empty($skillTemplate['battle_description'])) {
                // 使用自定义战斗描述
                $parseResult = Formulas::parseBattleDescription($skillTemplate['battle_description'], $caster, $target, $skillTemplate, $damageResult);
                $parsedDescription = $parseResult['description'];
                
                // 如果自定义描述中不包含任何伤害占位符，并且确实造成了伤害，则自动在末尾附加标准的伤害描述
                if (!$parseResult['has_damage_placeholder'] && $damageResult['damage'] > 0) {
                    // 注意：这里的伤害描述是固定的，并且不包含暴击前缀，因为暴击信息由 [暴击] 占位符处理
                    $parsedDescription .= " 造成了 {$damageResult['damage']} 点伤害！";
                }

                $log[] = trim($parsedDescription);
            } else {
                // 使用默认的丰富日志
                $critPrefix = $damageResult['is_critical'] ? "【暴击！】" : "";
                $log[] = "{$critPrefix}{$caster['username']}对{$target['username']}造成了 {$damageResult['damage']} 点伤害！";
            }

            if ($target['attributes']['hp'] <= 0) {
                $target['attributes']['hp'] = 0;
                $log[] = "{$target['username']} 被击败了！";
                $state['is_over'] = $this->checkBattleEnd($state);
            }
        }
    
        // 更新引用
        if ($targetId) {
            if ($targetType === 'player') {
                $state['players'][$targetId] = $target;
            } else {
                $state['monster'] = $target;
            }
        }
        unset($target);

        return ['state' => $state, 'log' => $log, 'damage' => $damageResult['damage'], 'player_id' => ($casterType === 'player' ? $casterId : null)];
    }

    /**
     * 检查战斗是否结束
     * @param array &$state 战斗状态引用
     * @return bool 如果战斗结束则返回true，否则返回false
     */
    private function checkBattleEnd(array &$state): bool {
        // 检查怪物是否被击败
        if ($state['monster']['attributes']['hp'] <= 0) {
            $state['winner'] = 'players';
            // 保存战斗结果，更新玩家状态
            $this->saveBattleResults($state);
            return true;
        }
    
        // 检查是否所有玩家都被击败
        $alivePlayers = array_filter($state['players'], fn($p) => $p['attributes']['hp'] > 0);
        if (count($alivePlayers) === 0) {
            $state['winner'] = 'monster';
            // 保存战斗结果，更新玩家状态
            $this->saveBattleResults($state);
            return true;
        }
    
        return false;
    }
    
    /**
     * 保存战斗结果到数据库
     * 
     * @param array &$state 战斗状态引用
     */
    private function saveBattleResults(array &$state): void {
        if (!$this->db) {
            // error_log("无法保存战斗结果：数据库连接不可用");
            return;
        }
        
        $conn = $this->db->getConnection();
        
        // 更新所有玩家的状态
        foreach ($state['players'] as $playerId => $player) {
            $this->savePlayerState($playerId, $player);
        }
    }
    
    /**
     * 保存单个玩家的状态到数据库
     * 
     * @param string $playerId 玩家ID
     * @param array $player 玩家数据
     */
    private function savePlayerState(string $playerId, array $player): void {
        if (!$this->db) {
            // error_log("无法保存玩家状态：数据库连接不可用");
            return;
        }
        
        try {
            $conn = $this->db->getConnection();
            // 准备更新语句，确保更新HP和MP
            $stmt = $conn->prepare("
                UPDATE player_attributes 
                SET hp = ?, mp = ?
                WHERE account_id = ?
            ");
            
            // 限制HP和MP不能为负数
            $hp = max(0, $player['attributes']['hp']);
            $mp = max(0, $player['attributes']['mp']);
            
            $stmt->execute([$hp, $mp, $playerId]);
            
            // 记录日志
            if ($stmt->rowCount() <= 0) {
                // error_log("无法更新玩家 {$playerId} 的状态");
            }
        } catch (Exception $e) {
            // error_log("保存玩家 {$playerId} 状态时出错: " . $e->getMessage());
        }
    }

    public function handleUseSkill(&$state, $playerId, $skillId) {
        $log = [];
        $player = &$state['players'][$playerId];

        // 1. 检查玩家是否正在吟唱，如果是，则忽略本次行动，直接处理吟唱进度
        if (isset($player['casting_info'])) {
            return $this->handleCastingTurn($state, $playerId);
        }

        // 2. 检查玩家是否被眩晕，无法进行任何操作
        if ($this->isPlayerStunned($state, $playerId)) {
            $log[] = "{$player['username']} 被眩晕了，无法行动！";
            return ['state' => $state, 'log' => $log];
        }

        // 3. 检查玩家是否被沉默，无法使用技能
        if ($this->isPlayerSilenced($state, $playerId)) {
            $log[] = "{$player['username']} 被沉默了，无法使用技能！";
            return ['state' => $state, 'log' => $log];
        }

        $db = Database::getInstance();
        $stmt = $db->query("SELECT * FROM skill_templates WHERE id = ?", [$skillId]);
        $skill = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
    
        if (!$skill) {
            $log[] = "{$player['username']} 试图施放一个不存在的技能。";
            return ['state' => $state, 'log' => $log];
        }
        
        // 新增：检查玩家身上是否已有来自该技能的增益效果
        if ($skill['skill_type'] === 'BUFF' || $skill['skill_type'] === 'DEBUFF') {
            if (!empty($state['active_effects'])) {
                foreach ($state['active_effects'] as $effect) {
                    // 检查当前玩家是否已经有来自该技能的有效效果
                    if (($effect['source_skill_id'] ?? null) == $skillId && $effect['target_id'] == $playerId) {
                        $log[] = "你已经处于【{$skill['name']}】的效果中，无需重复施放。";
                        return ['state' => $state, 'log' => $log];
                    }
                }
            }
        }

        // 2. 检查职业要求
        $requiredJobId = $skill['required_job_id'] ?? null;
        $currentJobId = $player['attributes']['current_job_id'] ?? null;
        if ($requiredJobId !== null && $requiredJobId != $currentJobId) {
            $jobStmt = $db->query("SELECT name FROM jobs WHERE id = ?", [$requiredJobId]);
            $requiredJobName = $jobStmt->fetchColumn() ?: '未知职业';
            $jobStmt->closeCursor();
            $log[] = "【职业不符】{$player['username']}当前的职业无法施放【{$skill['name']}】！";
            return ['state' => $state, 'log' => $log];
        }

        // 3. 检查技能冷却 (基于服务器传入的 $state['skill_cooldowns'])
        $cooldownTurnsLeft = $state['skill_cooldowns'][$playerId][$skillId] ?? 0;
        // error_log("BattleSystem::handleUseSkill - 检查技能冷却: 玩家 {$playerId}, 技能 {$skillId} ({$skill['name']}), 剩余冷却: {$cooldownTurnsLeft}");
        // error_log("BattleSystem::handleUseSkill - 当前冷却状态: " . json_encode($state['skill_cooldowns'] ?? []));

        if ($cooldownTurnsLeft > 0) {
            // 如果技能在冷却中，自动转为普通攻击
            $skillName = $skill['name'];
            // error_log("BattleSystem::handleUseSkill - 技能 {$skillId} ({$skillName}) 在冷却中，转为普通攻击");

            // 生成沉浸式的冷却提示信息
            $cooldownMessages = [
                "{$player['username']}试图施放【{$skillName}】，但法术尚未准备就绪！只好挥动武器发起攻击！",
                "【{$skillName}】的魔力尚未恢复，{$player['username']}果断改变策略，发起普通攻击！",
                "法术能量不足以再次引导【{$skillName}】，{$player['username']}转而发起猛烈的物理攻击！",
                "{$player['username']}的法术印记尚未成型，无法再次施放【{$skillName}】！立刻变招，挥舞武器直取敌方要害！",
                "【{$skillName}】的符文尚未充能完毕(还需{$cooldownTurnsLeft}回合)，{$player['username']}迅速改变战术，发起近身攻击！"
            ];

            // 随机选择一条消息
            $cooldownMessage = $cooldownMessages[array_rand($cooldownMessages)];

            // 执行普通攻击并返回结果
            $attackResult = $this->playerAttack($state, $playerId);
            
            // 在攻击日志前添加冷却提示
            if (is_array($attackResult['log'])) {
                array_unshift($attackResult['log'], $cooldownMessage);
            }
            
            return $attackResult;
        }
        
        // 4. 检查法力值
        $skillManaCost = Formulas::calculateSkillManaCost($skill['mp_cost'], $skill['skill_level'], $skill['rarity'], $player['attributes']['intelligence']);
        if ($player['attributes']['mp'] < $skillManaCost) {
            $log[] = "{$player['username']} 试图施放【{$skill['name']}】，但法力不足！";
            return ['state' => $state, 'log' => $log];
        }
        
        // 扣除法力值
        $player['attributes']['mp'] -= $skillManaCost;

        // 5. 设置冷却时间 (在$state中)
        $cooldown_turns = $skill['cooldown_turns'] ?? 0;
        if ($cooldown_turns > 0) {
            if (!isset($state['skill_cooldowns'])) $state['skill_cooldowns'] = [];
            if (!isset($state['skill_cooldowns'][$playerId])) $state['skill_cooldowns'][$playerId] = [];
            // 直接使用配置的冷却回合数，不再+1
            $state['skill_cooldowns'][$playerId][$skillId] = $cooldown_turns;

            // 调试信息：技能冷却设置
            // error_log("BattleSystem::handleUseSkill - 设置技能冷却: 玩家 {$playerId}, 技能 {$skillId} ({$skill['name']}), 冷却 {$cooldown_turns} 回合");
            // error_log("BattleSystem::handleUseSkill - 当前技能冷却状态: " . json_encode($state['skill_cooldowns']));
        } else {
            // error_log("BattleSystem::handleUseSkill - 技能 {$skillId} ({$skill['name']}) 无冷却时间");
        }
        
        // 更新数据库中的最后使用时间 (可以保留，用于非战斗场景或持久化)
        $db->query("UPDATE player_skills SET last_used_at = NOW() WHERE player_id = ? AND skill_template_id = ?", [$playerId, $skillId]);
        
        // 根据技能类型处理
        if ($skill['skill_type'] === 'COMBAT_DELAYED' && !empty($skill['delay_turns'])) {
            // 这是一个吟唱技能
            $state['players'][$playerId]['casting_info'] = [
                'skill_id' => $skillId,
                'skill_name' => $skill['name'],
                'total_rounds' => (int)$skill['delay_turns'],
                'rounds_passed' => 1 // 开始吟唱，算作第1回合
            ];
            
            $log[] = "{$player['username']} 开始吟唱【{$skill['name']}】... (1/{$skill['delay_turns']})";
            return ['state' => $state, 'log' => $log, 'player_id' => $playerId];

        } elseif ($skill['skill_type'] === 'COMBAT_INSTANT') {
            // 这是一个即时技能，直接执行
            $skillResult = $this->executeSkill($state, $playerId, 'player', $skillId);
            // 新增：为瞬发技能（包括BUFF/DEBUFF）也添加完成标记，以便服务器重置意图
            $skillResult['action_completed'] = 'instant_skill_finished';
            return $skillResult;
        } elseif ($skill['skill_type'] === 'BUFF' || $skill['skill_type'] === 'DEBUFF') {
            $skillEffects = json_decode($skill['effects'], true) ?: [];
            $duration = (int)($skill['duration_turns'] ?? 0);
            if (empty($skillEffects) || $duration <= 0) {
                $log[] = "【{$skill['name']}】发动了，但似乎什么也没发生。";
                return ['state' => $state, 'log' => $log];
            }
        
            $targetData = []; // 存储目标信息以便后续处理
            switch ($skill['target_type']) {
                case 'SELF':
                    if ($player['attributes']['hp'] > 0) {
                        $targetData[] = ['id' => $playerId, 'name' => $player['username'], 'type' => 'player'];
                    }
                    break;
                case 'ENEMY':
                case 'HOSTILE':
                    if ($state['monster']['attributes']['hp'] > 0) {
                        $targetData[] = ['id' => 'monster', 'name' => $state['monster']['username'], 'type' => 'monster'];
                    }
                    break;
                case 'ALLY':
                    // 对所有存活的友方（包括自己）生效
                    foreach ($state['players'] as $p_id => &$p) {
                        if ($p['attributes']['hp'] > 0) {
                            $targetData[] = ['id' => $p_id, 'name' => $p['username'], 'type' => 'player'];
                        }
                    }
                    unset($p);
                    break;
            }
            
            if (empty($targetData)) {
                $log[] = "没有找到【{$skill['name']}】的有效目标。";
                return ['state' => $state, 'log' => $log];
            }

            if (!isset($state['active_effects'])) {
                $state['active_effects'] = [];
            }
        
            $targetNames = [];
            $allAppliedEffectsForLog = []; // 用于统一生成日志

            foreach ($targetData as $t) {
                $target = null;
                if ($t['type'] === 'player') {
                    if (!isset($state['players'][$t['id']])) continue;
                    $target = &$state['players'][$t['id']];
                } else {
                    $target = &$state['monster'];
                }
        
                if ($target) {
                    $options = ['skill_level' => $state['player_skills'][$playerId][$skill['id']]['level'] ?? 1];
                    $evaluatedEffects = [];
                    $caster = $player;
        
                    foreach($skillEffects as $stat => $valueOrFormula) {
                        $evaluatedValue = 0;
                        if (is_numeric($valueOrFormula)) {
                            $evaluatedValue = $valueOrFormula;
                        } elseif (is_string($valueOrFormula) && trim($valueOrFormula) !== '') {
                            try {
                                // 对于BUFF, 'attacker' 是施法者, 'defender' 是目标
                                $evaluatedValue = Formulas::evaluateDamageFormula($valueOrFormula, $caster, $target, $options);
                            } catch (Exception $e) {
                                $log[] = "评估增益公式时出错 ({$stat} for {$t['name']}): " . $e->getMessage();
                                continue 2; // 继续外层循环
                            }
                        }
                        // 只添加有实际效果的属性
                        if ($evaluatedValue != 0) {
                            $evaluatedEffects[$stat] = round($evaluatedValue);
                        }
                    }

                    if(!empty($evaluatedEffects)) {
                        $this->applyEffect($target['attributes'], $evaluatedEffects);

                        $state['active_effects'][] = [
                            'id' => uniqid('effect_'),
                            'source_skill_id' => $skillId,
                            'target_id' => $t['id'],
                            'target_type' => $t['type'],
                            'effects' => $evaluatedEffects, // 存储计算后的值
                            'remaining_turns' => $duration,
                            'source_skill_name' => $skill['name']
                        ];
                        $targetNames[] = $t['name'];
                        $allAppliedEffectsForLog[$t['name']] = $evaluatedEffects;

                        // 如果影响了敏捷属性，需要重新计算ATB参数
                        if (isset($evaluatedEffects['agility'])) {
                            $this->recalculateAtbParameters($state);
                        }
                    }
                }
                unset($target);
            }

            if (empty($targetNames)) {
                $log[] = "【{$skill['name']}】没有产生任何效果。";
                return ['state' => $state, 'log' => $log];
            }

            $log[] = "{$player['username']}对 " . implode('、', $targetNames) . " 施放了【{$skill['name']}】，效果持续{$duration}回合。";
            
            // 生成效果描述日志 (以第一个目标为例)
            if (!empty($allAppliedEffectsForLog)) {
                $firstTargetEffects = reset($allAppliedEffectsForLog);
                $effectDescriptions = [];
                foreach($firstTargetEffects as $stat => $value) {
                    if ($value == 0) continue;

                    // 特殊效果的专门描述
                    if ($stat === 'silence' && $value > 0) {
                        $effectDescriptions[] = "沉默";
                    } elseif ($stat === 'stun' && $value > 0) {
                        $effectDescriptions[] = "眩晕";
                    } else {
                        // 普通属性效果
                        $statName = Formulas::$damageTypeNames[$stat] ?? Formulas::$resistanceTypeNames[$stat] ?? self::$attributeNames[$stat] ?? $stat;
                        $change = $value > 0 ? "提升" : "降低";
                        $effectDescriptions[] = "{$statName}{$change}" . abs($value);
                    }
                }
                if(!empty($effectDescriptions)){
                    $log[] = "效果: " . implode('，', $effectDescriptions) . "。";
                }
            }
        
            $result = ['state' => $state, 'log' => $log, 'player_id' => $playerId];
            // 新增：为BUFF/DEBUFF技能添加完成标记
            $result['action_completed'] = 'instant_skill_finished';
            return $result;
        } else {
            // 其他未知或非战斗技能类型
            $log[] = "{$player['username']} 使用了技能【{$skill['name']}】！";
            return ['state' => $state, 'log' => $log];
        }
    }

    /**
     * 在每个角色回合开始时处理其身上的所有效果（BUFF/DEBUFF）
     * @param array &$state 战斗状态引用
     * @param string $actorType 行动者类型 ('player' 或 'monster')
     * @param string|null $actorId 行动者ID
     * @return array 包含日志和更新后状态的结果
     */
    public function processEffectsTurn(&$state, string $actorType, ?string $actorId = null): array {
        if (!isset($state['active_effects'])) {
            return ['log' => [], 'state' => $state];
        }
    
        $log = [];
        $targetId = ($actorType === 'monster') ? 'monster' : $actorId;
    
        $remainingEffects = [];
        foreach ($state['active_effects'] as $effect) {
            // 只处理属于当前行动者的效果，使用宽松比较以避免类型问题
            if ($effect['target_id'] == $targetId) {
                // 如果回合数已耗尽，则效果到期
                if ($effect['remaining_turns'] <= 0) {
                    $target = null;
                    if ($effect['target_type'] === 'monster') {
                        $target = &$state['monster'];
                    } elseif (isset($state['players'][$effect['target_id']])) {
                        $target = &$state['players'][$effect['target_id']];
                    }
    
                    if ($target) {
                        $this->removeEffect($target['attributes'], $effect['effects']);
                        $log[] = "{$target['username']}身上的【{$effect['source_skill_name']}】效果消失了。";

                        // 如果移除的效果影响了敏捷属性，需要重新计算ATB参数
                        if (isset($effect['effects']['agility'])) {
                            $this->recalculateAtbParameters($state);
                        }
                    }
                    // 不将到期的效果添加到新数组中，直接继续下一个
                    continue;
                }
                
                // 如果效果仍然有效，则减少其持续时间
                $effect['remaining_turns']--;
            }
            
            // 保留效果（无论是其他人的，还是自己的未到期效果）
            $remainingEffects[] = $effect;
        }
        
        $state['active_effects'] = $remainingEffects;
        unset($target); // 断开在循环中可能创建的对玩家或怪物对象的引用



        return ['log' => $log, 'state' => $state];
    }

    /**
     * 将技能效果应用到目标属性上
     * @param array &$targetAttributes 目标属性数组的引用
     * @param array $effects 效果数组
     */
    private function applyEffect(array &$targetAttributes, array $effects): void {
        $needsRecalculation = false;

        foreach ($effects as $stat => $value) {
            if (isset($targetAttributes[$stat])) {
                $targetAttributes[$stat] += $value;

                // 检查是否需要重新计算衍生属性
                if (in_array($stat, ['strength', 'agility', 'constitution', 'intelligence'])) {
                    $needsRecalculation = true;
                }
            } else {
                // 如果属性不存在，则创建它 (例如 'dodge_bonus')
                $targetAttributes[$stat] = $value;
            }
        }

        // 如果基础属性被修改，重新计算衍生属性
        if ($needsRecalculation) {
            $this->recalculateDerivedAttributes($targetAttributes);
        }
    }

    /**
     * 重新计算ATB参数（当攻速相关属性变化时）
     * @param array &$battleState 战斗状态引用
     */
    private function recalculateAtbParameters(array &$battleState): void {
        if (!isset($battleState['atb_state'])) {
            return;
        }

        // 重新计算ATB参数
        $atbParams = $this->calculateAtbParameters($battleState);

        // 更新ATB状态中的参数
        $battleState['atb_state']['atbMax'] = $atbParams['atbMax'];
        $battleState['atb_state']['speedMultipliers'] = $atbParams['speedMultipliers'];
    }

    /**
     * 从目标属性上移除技能效果
     * @param array &$targetAttributes 目标属性数组的引用
     * @param array $effects 效果数组
     */
    private function removeEffect(array &$targetAttributes, array $effects): void {
        $needsRecalculation = false;

        foreach ($effects as $stat => $value) {
            if (isset($targetAttributes[$stat])) {
                $targetAttributes[$stat] -= $value;

                // 检查是否需要重新计算衍生属性
                if (in_array($stat, ['strength', 'agility', 'constitution', 'intelligence'])) {
                    $needsRecalculation = true;
                }
            }
        }

        // 如果基础属性被修改，重新计算衍生属性
        if ($needsRecalculation) {
            $this->recalculateDerivedAttributes($targetAttributes);
        }
    }

    /**
     * 检查玩家是否被沉默
     * @param array $state 战斗状态
     * @param string $playerId 玩家ID
     * @return bool 是否被沉默
     */
    private function isPlayerSilenced(array $state, string $playerId): bool {
        if (!isset($state['active_effects'])) {
            return false;
        }

        foreach ($state['active_effects'] as $effect) {
            if ($effect['target_id'] == $playerId && isset($effect['effects']['silence']) && $effect['effects']['silence'] > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查玩家是否被眩晕
     * @param array $state 战斗状态
     * @param string $playerId 玩家ID
     * @return bool 是否被眩晕
     */
    private function isPlayerStunned(array $state, string $playerId): bool {
        if (!isset($state['active_effects'])) {
            return false;
        }

        foreach ($state['active_effects'] as $effect) {
            if ($effect['target_id'] == $playerId && isset($effect['effects']['stun']) && $effect['effects']['stun'] > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 执行群体攻击技能
     * @param array &$state 战斗状态引用
     * @param array $caster 施法者数据
     * @param array $skillTemplate 技能模板
     * @param int $skillId 技能ID
     * @return array 执行结果
     */
    private function executeGroupAttack(array &$state, array $caster, array $skillTemplate, int $skillId): array {
        $log = [];
        $totalDamage = 0;

        // 获取所有存活的玩家作为目标
        $targets = [];
        foreach ($state['players'] as $playerId => $player) {
            if ($player['attributes']['hp'] > 0) {
                $targets[] = ['id' => $playerId, 'player' => &$state['players'][$playerId]];
            }
        }

        if (empty($targets)) {
            $log[] = "【{$skillTemplate['name']}】没有找到有效目标。";
            return ['state' => $state, 'log' => $log, 'damage' => 0];
        }

        // 检查技能是否有伤害公式
        if (isset($skillTemplate['effects'])) {
            $skillEffects = json_decode($skillTemplate['effects'], true);

            if (isset($skillEffects['damage_formula'])) {
                try {
                    // 计算基础伤害（仅使用攻击者属性）
                    $options = ['skill_level' => 1]; // 怪物技能默认等级1
                    $baseDamage = Formulas::evaluateDamageFormula($skillEffects['damage_formula'], $caster, null, $options);

                    // 平均分配伤害
                    $damagePerTarget = $baseDamage / count($targets);

                    // 生成技能描述
                    if (!empty($skillTemplate['battle_description'])) {
                        // 为群体攻击创建特殊的伤害结果，显示分配后的伤害
                        $firstTarget = $targets[0]['player'];
                        $damageResult = [
                            'damage' => round($damagePerTarget),
                            'physical_damage' => round($damagePerTarget),
                            'elemental_damage' => 0,
                            'is_critical' => false
                        ];

                        $descriptionResult = Formulas::parseBattleDescription(
                            $skillTemplate['battle_description'],
                            $caster,
                            $firstTarget,
                            $skillTemplate,
                            $damageResult
                        );

                        // 修改描述中的[敌人]为"所有敌人"，并调整伤害描述
                        $groupDescription = str_replace($firstTarget['username'], '所有敌人', $descriptionResult['description']);

                        // 如果描述中包含伤害信息，添加说明这是每个目标的伤害
                        $damageText = (string)round($damagePerTarget);
                        if (strpos($groupDescription, $damageText) !== false) {
                            $groupDescription = str_replace(
                                $damageText . '伤害',
                                '每个目标' . $damageText . '点伤害',
                                $groupDescription
                            );
                        }

                        $log[] = $groupDescription;
                    } else {
                        // 如果没有配置描述，使用默认描述
                        $log[] = "{$caster['username']} 对所有玩家施放了【{$skillTemplate['name']}】！";
                    }

                    // 对每个目标应用伤害
                    foreach ($targets as $targetInfo) {
                        $target = &$targetInfo['player'];
                        $playerId = $targetInfo['id'];

                        // 应用防御减免
                        $defense = $target['attributes']['defense'] ?? 0;
                        $mitigation = 1 - ($defense / ($defense + 200));
                        $finalDamage = max(1, round($damagePerTarget * $mitigation));

                        // 应用伤害
                        $target['attributes']['hp'] -= $finalDamage;
                        $totalDamage += $finalDamage;

                        if ($target['attributes']['hp'] <= 0) {
                            $target['attributes']['hp'] = 0;
                            $log[] = "→ {$target['username']} 承受了 {$finalDamage} 点伤害并被击倒！";
                        } else {
                            $log[] = "→ {$target['username']} 承受了 {$finalDamage} 点伤害 (HP: {$target['attributes']['hp']}/{$target['attributes']['max_hp']})";
                        }
                    }

                    // 检查战斗是否结束
                    $allPlayersDefeated = count(array_filter($state['players'], fn($p) => $p['attributes']['hp'] > 0)) === 0;
                    if ($allPlayersDefeated) {
                        $state['is_over'] = true;
                        $state['winner'] = 'monster';
                        // 保存战斗结果
                        $this->saveBattleResults($state);
                    }

                } catch (Exception $e) {
                    $log[] = "群体技能伤害计算出错: " . $e->getMessage();
                }
            }
        }

        return [
            'state' => $state,
            'log' => $log,
            'damage' => $totalDamage,
            'is_group_attack' => true,
            'target_count' => count($targets)
        ];
    }

    /**
     * 重新计算衍生属性（战斗中临时计算）
     * @param array &$attributes 属性数组的引用
     */
    private function recalculateDerivedAttributes(array &$attributes): void {
        // 确保有等级信息，如果没有则使用默认值
        $level = $attributes['level'] ?? 1;

        // 重新计算衍生属性
        $attributes['attack'] = Formulas::calculateAttack($attributes['strength'], $level);
        $attributes['defense'] = Formulas::calculateDefense($attributes['constitution'], $attributes['agility'], $level);
        $attributes['max_hp'] = Formulas::calculateMaxHp($attributes['constitution'], $level);
        $attributes['max_mp'] = Formulas::calculateMaxMp($attributes['intelligence'], $level);
        $attributes['attack_speed'] = Formulas::calculateAttackSpeed($attributes['agility']);

        // 确保当前血量和魔法值不超过新的最大值
        if (isset($attributes['hp']) && $attributes['hp'] > $attributes['max_hp']) {
            $attributes['hp'] = $attributes['max_hp'];
        }
        if (isset($attributes['mp']) && $attributes['mp'] > $attributes['max_mp']) {
            $attributes['mp'] = $attributes['max_mp'];
        }
    }
}
