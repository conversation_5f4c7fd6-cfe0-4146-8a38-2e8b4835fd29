nohup: ignoring input
Jul 15 22:00:07 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x1b
Jul 15 22:00:08 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x1b
Jul 15 22:00:08 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x1b
Jul 15 22:00:08 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x16
Jul 15 22:00:08 iZe1jacokmymj4Z start_game.sh[2559736]: 场景 scene_-6_8_0 没有清空，无需重置。
Jul 15 22:00:12 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x1b
Jul 15 22:00:13 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x1b
Jul 15 22:00:45 iZe1jacokmymj4Z start_game.sh[2559736]: 收到来自玩家 1 (FD 6) 的加密消息 Opcode: 0x16
Jul 15 22:00:45 iZe1jacokmymj4Z start_game.sh[2559736]: 场景 scene_-8_8_0 没有清空，无需重置。
Jul 15 22:00:55 iZe1jacokmymj4Z start_game.sh[2559736]: 健康检查通过 - 2025-07-15 22:00:55 - 连接数: 1
Jul 15 22:01:02 iZe1jacokmymj4Z systemd[1]: Stopping My WAP MUD Game Server...
Jul 15 22:01:03 iZe1jacokmymj4Z start_game.sh[2559732]: [2025-07-15 22:01:03 #2559732.1]        INFO        Server is shutdown now
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: [2025-07-15 22:01:06 *2559736.0]        WARNING        Worker_reactor_try_to_exit() (ERRNO 9101): worker exit timeout, forced termination
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: ===================================================================
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]:  [FATAL ERROR]: all coroutines (count: 1) are asleep - deadlock!
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: ===================================================================
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]:  [Coroutine-2]
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: --------------------------------------------------------------------
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: #0  Redis->subscribe() called at [/www/wwwroot/wx.xstudio.net.cn/server/improved_websocket_server.php:375]
Jul 15 22:01:06 iZe1jacokmymj4Z start_game.sh[2559736]: Worker #0 正在停止。
Jul 15 22:01:07 iZe1jacokmymj4Z systemd[1]: game-server.service: Deactivated successfully.
Jul 15 22:01:07 iZe1jacokmymj4Z systemd[1]: Stopped My WAP MUD Game Server.
Jul 15 22:01:07 iZe1jacokmymj4Z systemd[1]: Started My WAP MUD Game Server.
Jul 15 22:01:07 iZe1jacokmymj4Z start_game.sh[2560010]: 启动多人战斗游戏服务器...
Jul 15 22:01:07 iZe1jacokmymj4Z start_game.sh[2560010]: 启动WebSocket服务器...
Jul 15 22:01:07 iZe1jacokmymj4Z start_game.sh[2560010]: 游戏服务器已启动：
Jul 15 22:01:07 iZe1jacokmymj4Z start_game.sh[2560010]: - WebSocket服务器: ws://localhost:8080
Jul 15 22:01:07 iZe1jacokmymj4Z start_game.sh[2560010]: - 按 Ctrl+C 停止服务器
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560017]:  检查Swoole扩展是否加载...
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560017]: Swoole扩展已加载。
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560017]: 正在启动 Swoole WebSocket 服务器...
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560017]: Swoole WebSocket 服务器启动在 0.0.0.0:8080
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560021]: Worker 0 已启动，进程ID: 2560021
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560021]: Worker 0: 数据库预连接成功
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560021]: Worker 0: Redis 预连接成功
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560021]: [Redis] 订阅器已准备就绪
Jul 15 22:01:09 iZe1jacokmymj4Z start_game.sh[2560021]: Redis订阅器已准备就绪。
Jul 15 22:01:10 iZe1jacokmymj4Z start_game.sh[2560021]: WebSocket 握手成功 - 客户端 FD: 1
Jul 15 22:01:10 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 1
Jul 15 22:01:41 iZe1jacokmymj4Z start_game.sh[2560021]: 客户端 FD: 1 关闭连接
Jul 15 22:01:41 iZe1jacokmymj4Z start_game.sh[2560021]: 未登录的连接 FD: 1 断开。
Jul 15 22:01:41 iZe1jacokmymj4Z start_game.sh[2560021]: 连接清理操作: 未登录连接断开
Jul 15 22:01:41 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 0
Jul 15 22:01:45 iZe1jacokmymj4Z start_game.sh[2560021]: WebSocket 握手成功 - 客户端 FD: 2
Jul 15 22:01:45 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 1
Jul 15 22:01:46 iZe1jacokmymj4Z start_game.sh[2560021]: 客户端 FD: 2 关闭连接
Jul 15 22:01:46 iZe1jacokmymj4Z start_game.sh[2560021]: 未登录的连接 FD: 2 断开。
Jul 15 22:01:46 iZe1jacokmymj4Z start_game.sh[2560021]: 连接清理操作: 未登录连接断开
Jul 15 22:01:46 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 0
Jul 15 22:01:50 iZe1jacokmymj4Z start_game.sh[2560021]: WebSocket 握手成功 - 客户端 FD: 3
Jul 15 22:01:50 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 1
Jul 15 22:01:57 iZe1jacokmymj4Z start_game.sh[2560021]: 客户端 FD: 3 关闭连接
Jul 15 22:01:57 iZe1jacokmymj4Z start_game.sh[2560021]: 未登录的连接 FD: 3 断开。
Jul 15 22:01:57 iZe1jacokmymj4Z start_game.sh[2560021]: 连接清理操作: 未登录连接断开
Jul 15 22:01:57 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 0
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: WebSocket 握手成功 - 客户端 FD: 4
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 1
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 (FD 4) 的会话恢复成功。
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 的在线状态标记已恢复
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 4) 的加密消息 Opcode: 0x6d
Jul 15 22:02:02 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 4) 的加密消息 Opcode: 0x11
Jul 15 22:02:04 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 4) 的加密消息 Opcode: 0x1b
Jul 15 22:02:06 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 4) 的加密消息 Opcode: 0x2a
Jul 15 22:02:06 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 4) 的加密消息 Opcode: 0x39
Jul 15 22:02:09 iZe1jacokmymj4Z start_game.sh[2560021]: 健康检查通过 - 2025-07-15 22:02:09 - 连接数: 1
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 客户端 FD: 4 关闭连接
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 断开连接
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 已清除玩家 1 的在线状态标记
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 开始为玩家 1 处理场景移除。
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家在场景 scene_-9_8_0 中，正在从Redis移除并广播。
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 已从场景 scene_-9_8_0 的在线列表移除并广播。
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 连接清理操作: 玩家ID: 1 已确认从fdInfo清理, 玩家Redis在线状态标记已清除, 玩家已从场景中移除, 玩家连接映射已清理
Jul 15 22:03:03 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 0
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: WebSocket 握手成功 - 客户端 FD: 5
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: 当前连接数: 1
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 (FD 5) 的会话恢复成功。
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: 玩家 1 的在线状态标记已恢复
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 5) 的加密消息 Opcode: 0x6d
Jul 15 22:03:06 iZe1jacokmymj4Z start_game.sh[2560021]: 收到来自玩家 1 (FD 5) 的加密消息 Opcode: 0x11
Jul 15 22:03:09 iZe1jacokmymj4Z start_game.sh[2560021]: 健康检查通过 - 2025-07-15 22:03:09 - 连接数: 1
Jul 15 22:03:29 iZe1jacokmymj4Z start_game.sh[2560021]: [Server] Received notification on channel 'game-system-notifications': scenes_updated
Jul 15 22:03:29 iZe1jacokmymj4Z start_game.sh[2560021]: [Server] scenes_updated notification received. Clearing SceneManager cache.
Jul 15 22:03:29 iZe1jacokmymj4Z start_game.sh[2560021]: 场景缓存已清除。
Jul 15 22:03:34 iZe1jacokmymj4Z start_game.sh[2560021]: [Server] Received notification on channel 'game-system-notifications': scenes_updated
Jul 15 22:03:34 iZe1jacokmymj4Z start_game.sh[2560021]: [Server] scenes_updated notification received. Clearing SceneManager cache.
Jul 15 22:03:34 iZe1jacokmymj4Z start_game.sh[2560021]: 场景缓存已清除。
