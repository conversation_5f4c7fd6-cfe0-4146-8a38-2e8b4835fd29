/* 交易记录管理页面样式 */
.page-content {
    padding: 20px;
}

/* 过滤和清理部分 */
.filter-section, .cleanup-section {
    background-color: #f9f9f9;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border: 1px solid #ddd;
}

.filter-section .form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    align-items: flex-end;
}

.filter-section .form-row .form-group {
    margin-bottom: 0;
}

.cleanup-section {
    display: flex;
    align-items: flex-end;
    gap: 15px;
}

.cleanup-section .form-group {
    margin-bottom: 0;
}

.form-group {
    flex: 1;
    min-width: 200px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: 500;
    color: #333;
}

.form-control {
    width: 100%;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
}

.form-control:focus {
    outline: none;
    border-color: #007bff;
    box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 5px;
    transition: background-color 0.2s;
}

.btn-primary {
    background-color: #007bff;
    color: white;
}

.btn-primary:hover {
    background-color: #0056b3;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background-color: #545b62;
}

.btn-danger {
    background-color: #dc3545;
    color: white;
}

.btn-danger:hover {
    background-color: #c82333;
}

/* 交易记录容器 */
.trade-logs-container {
    background-color: white;
    border-radius: 8px;
    border: 1px solid #ddd;
    overflow: hidden;
}

.logs-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #ddd;
}

.logs-header h3 {
    margin: 0;
    color: #333;
}

.pagination-info {
    color: #666;
    font-size: 14px;
}

/* 表格样式 */
.logs-table-container {
    overflow-x: auto;
}

.logs-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 14px;
}

.logs-table th,
.logs-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.logs-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
    position: sticky;
    top: 0;
    z-index: 10;
}

.logs-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签 */
.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
    display: inline-block;
}

.status-completed {
    background-color: #d4edda;
    color: #155724;
}

.status-cancelled {
    background-color: #f8d7da;
    color: #721c24;
}

.status-expired {
    background-color: #fff3cd;
    color: #856404;
}

/* 分页控件 */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 15px;
    gap: 10px;
    background-color: #f8f9fa;
    border-top: 1px solid #ddd;
}

.pagination-btn {
    padding: 6px 12px;
    border: 1px solid #ddd;
    background-color: white;
    color: #333;
    text-decoration: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s;
}

.pagination-btn:hover {
    background-color: #e9ecef;
}

.pagination-btn.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    background-color: white;
    margin: 2% auto;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.modal-large {
    max-width: 1000px;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #ddd;
    background-color: #f8f9fa;
}

.modal-header h2 {
    margin: 0;
    color: #333;
}

.modal-close-button {
    font-size: 24px;
    cursor: pointer;
    color: #666;
    line-height: 1;
}

.modal-close-button:hover {
    color: #333;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
    padding: 20px;
    border-top: 1px solid #ddd;
    background-color: #f8f9fa;
}

/* 交易信息样式 */
.trade-info-section {
    margin-bottom: 25px;
}

.trade-info-section h3 {
    margin: 0 0 15px 0;
    color: #333;
    border-bottom: 2px solid #007bff;
    padding-bottom: 5px;
}

.info-table {
    width: 100%;
    border-collapse: collapse;
}

.info-table td {
    padding: 8px 12px;
    border-bottom: 1px solid #eee;
}

.info-table td:first-child,
.info-table td:nth-child(3) {
    width: 120px;
    font-weight: 500;
}

/* 交易物品和货币容器 */
.trade-items-container,
.trade-currency-container {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 20px;
}

.initiator-items,
.target-items,
.initiator-currency,
.target-currency {
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 15px;
    background-color: #f9f9f9;
}

.initiator-items h4,
.target-items h4,
.initiator-currency h4,
.target-currency h4 {
    margin: 0 0 10px 0;
    color: #333;
    font-size: 16px;
}

.item-entry,
.currency-entry {
    padding: 8px;
    margin-bottom: 5px;
    background-color: white;
    border-radius: 4px;
    border: 1px solid #eee;
}

.item-name {
    font-weight: 500;
    color: #333;
    margin-bottom: 4px;
}

.item-quantity,
.currency-amount {
    color: #666;
    font-size: 14px;
    margin-bottom: 2px;
}

.item-id,
.item-template-id {
    color: #888;
    font-size: 12px;
    font-family: 'Courier New', monospace;
    margin-bottom: 2px;
}

.item-id {
    color: #007bff;
    font-weight: 500;
}

/* Toast通知 */
.toast {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 12px 20px;
    border-radius: 6px;
    color: white;
    font-weight: 500;
    z-index: 1100;
    transform: translateX(100%);
    transition: transform 0.3s ease;
}

.toast.show {
    transform: translateX(0);
}

.toast.success {
    background-color: #28a745;
}

.toast.error {
    background-color: #dc3545;
}

.toast.warning {
    background-color: #ffc107;
    color: #333;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .filter-section .form-row {
        flex-direction: column;
    }
    
    .cleanup-section {
        flex-direction: column;
        align-items: stretch;
    }
    
    .trade-items-container,
    .trade-currency-container {
        grid-template-columns: 1fr;
    }
    
    .logs-table {
        font-size: 12px;
    }
    
    .logs-table th,
    .logs-table td {
        padding: 8px;
    }
}
