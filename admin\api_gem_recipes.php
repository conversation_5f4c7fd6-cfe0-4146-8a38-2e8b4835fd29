<?php
header('Content-Type: application/json');
require_once '../config/Database.php';

$db = Database::getInstance()->getConnection();
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_recipes':
        get_gem_recipes($db);
        break;
    case 'get_recipe':
        get_gem_recipe($db);
        break;
    case 'save_recipe':
        save_gem_recipe($db);
        break;
    case 'delete_recipe':
        delete_gem_recipe($db);
        break;
    default:
        echo json_encode(['success' => false, 'message' => '无效的操作']);
}

function get_gem_recipes($db) {
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $limit = 20;
    $offset = ($page - 1) * $limit;
    $search = $_GET['search'] ?? '';

    $where_clauses = [];
    $params = [];

    if (!empty($search)) {
        $where_clauses[] = "(r.id LIKE ? OR r.name LIKE ? OR i.name LIKE ?)";
        $search_param = "%$search%";
        $params[] = $search_param;
        $params[] = $search_param;
        $params[] = $search_param;
    }

    $where_sql = count($where_clauses) > 0 ? 'WHERE ' . implode(' AND ', $where_clauses) : '';

    // Get total count
    $count_sql = "
        SELECT COUNT(r.id) 
        FROM gem_recipe_templates r 
        LEFT JOIN item_templates i ON r.result_item_id = i.id
        $where_sql
    ";
    $total_stmt = $db->prepare($count_sql);
    $total_stmt->execute($params);
    $total_records = $total_stmt->fetchColumn();

    // Get records for the current page
    $sql = "
        SELECT r.*, i.name as item_name, 
               (SELECT COUNT(*) FROM gem_recipe_materials WHERE recipe_id = r.id) as material_count
        FROM gem_recipe_templates r
        LEFT JOIN item_templates i ON r.result_item_id = i.id
        $where_sql
        ORDER BY r.id DESC
        LIMIT $limit OFFSET $offset
    ";
    
    $stmt = $db->prepare($sql);
    $stmt->execute($params);
    $recipes = $stmt->fetchAll(PDO::FETCH_ASSOC);

    echo json_encode([
        'success' => true,
        'recipes' => $recipes,
        'pagination' => [
            'total_records' => $total_records,
            'current_page' => $page,
            'total_pages' => ceil($total_records / $limit)
        ]
    ]);
}

function get_gem_recipe($db) {
    $id = $_GET['id'] ?? 0;
    if (!$id) {
        echo json_encode(['success' => false, 'message' => '未提供ID']);
        return;
    }

    $stmt = $db->prepare("SELECT * FROM gem_recipe_templates WHERE id = ?");
    $stmt->execute([$id]);
    $recipe = $stmt->fetch(PDO::FETCH_ASSOC);

    if ($recipe) {
        $stmt = $db->prepare("SELECT material_item_id, quantity FROM gem_recipe_materials WHERE recipe_id = ?");
        $stmt->execute([$id]);
        $recipe['materials'] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        echo json_encode(['success' => true, 'recipe' => $recipe]);
    } else {
        echo json_encode(['success' => false, 'message' => '未找到配方']);
    }
}

function save_gem_recipe($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        
        if (!$input) {
            echo json_encode(['success' => false, 'message' => '无效的请求数据']);
            return;
        }

        $id = $input['id'] ?? 0;
        $name = trim($input['name'] ?? '');
        $result_item_id = (int)($input['result_item_id'] ?? 0);
        $result_quantity = (int)($input['result_quantity'] ?? 1);
        $craft_level = (int)($input['craft_level'] ?? 1);
        $description = trim($input['description'] ?? '');
        $materials = $input['materials'] ?? [];

        // 验证必填字段
        if (empty($name)) {
            echo json_encode(['success' => false, 'message' => '配方名称不能为空']);
            return;
        }

        if ($result_item_id <= 0) {
            echo json_encode(['success' => false, 'message' => '请选择产出物品']);
            return;
        }

        if ($result_quantity <= 0) {
            echo json_encode(['success' => false, 'message' => '产出数量必须大于0']);
            return;
        }

        if ($craft_level <= 0) {
            echo json_encode(['success' => false, 'message' => '合成等级必须大于0']);
            return;
        }

        // 验证材料
        if (empty($materials)) {
            echo json_encode(['success' => false, 'message' => '至少需要一种材料']);
            return;
        }

        foreach ($materials as $material) {
            if (empty($material['material_item_id']) || $material['quantity'] <= 0) {
                echo json_encode(['success' => false, 'message' => '材料信息不完整']);
                return;
            }
        }

        $db->beginTransaction();

        if ($id > 0) {
            // 更新配方
            $stmt = $db->prepare("
                UPDATE gem_recipe_templates 
                SET name = ?, result_item_id = ?, result_quantity = ?, craft_level = ?, description = ?, updated_at = NOW()
                WHERE id = ?
            ");
            $stmt->execute([$name, $result_item_id, $result_quantity, $craft_level, $description, $id]);

            // 删除旧材料
            $stmt = $db->prepare("DELETE FROM gem_recipe_materials WHERE recipe_id = ?");
            $stmt->execute([$id]);
        } else {
            // 创建新配方
            $stmt = $db->prepare("
                INSERT INTO gem_recipe_templates (name, result_item_id, result_quantity, craft_level, description) 
                VALUES (?, ?, ?, ?, ?)
            ");
            $stmt->execute([$name, $result_item_id, $result_quantity, $craft_level, $description]);
            $id = $db->lastInsertId();
        }

        // 添加材料
        $stmt = $db->prepare("INSERT INTO gem_recipe_materials (recipe_id, material_item_id, quantity, created_at) VALUES (?, ?, ?, NOW())");
        foreach ($materials as $material) {
            $stmt->execute([$id, $material['material_item_id'], $material['quantity']]);
        }

        $db->commit();
        echo json_encode(['success' => true, 'message' => '宝石配方保存成功']);

    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => '保存失败: ' . $e->getMessage()]);
    }
}

function delete_gem_recipe($db) {
    try {
        $input = json_decode(file_get_contents('php://input'), true);
        $id = $input['id'] ?? 0;

        if (!$id) {
            echo json_encode(['success' => false, 'message' => '未提供配方ID']);
            return;
        }

        $db->beginTransaction();

        // 删除材料
        $stmt = $db->prepare("DELETE FROM gem_recipe_materials WHERE recipe_id = ?");
        $stmt->execute([$id]);

        // 删除配方
        $stmt = $db->prepare("DELETE FROM gem_recipe_templates WHERE id = ?");
        $stmt->execute([$id]);

        if ($stmt->rowCount() > 0) {
            $db->commit();
            echo json_encode(['success' => true, 'message' => '宝石配方删除成功']);
        } else {
            $db->rollBack();
            echo json_encode(['success' => false, 'message' => '未找到要删除的配方']);
        }

    } catch (Exception $e) {
        $db->rollBack();
        echo json_encode(['success' => false, 'message' => '删除失败: ' . $e->getMessage()]);
    }
}
?>
