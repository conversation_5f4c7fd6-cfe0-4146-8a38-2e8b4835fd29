<?php
// admin/api_scene_items_cleanup.php

// 引入必要的文件
require_once '../config/Database.php';
require_once '../config/RedisManager.php';
require_once 'auth.php'; // 添加认证检查

header('Content-Type: application/json; charset=utf-8');

// 检查是否为POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只支持POST请求']);
    exit;
}

try {
    // 获取请求参数
    $action = $_POST['action'] ?? '';
    
    switch ($action) {
        case 'cleanup_expired':
            $result = cleanupExpiredSceneItems();
            break;
        case 'get_stats':
            $result = getSceneItemsStats();
            break;
        case 'cleanup_by_hours':
            $hours = (int)($_POST['hours'] ?? 1);
            if ($hours < 1 || $hours > 24) {
                throw new Exception('小时数必须在1-24之间');
            }
            $result = cleanupSceneItemsByHours($hours);
            break;
        default:
            throw new Exception('无效的操作');
    }
    
    echo json_encode(['success' => true, 'data' => $result]);
    
} catch (Exception $e) {
    http_response_code(400);
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

/**
 * 清理超过1小时的场景掉落物品
 */
function cleanupExpiredSceneItems() {
    return cleanupSceneItemsByHours(1);
}

/**
 * 清理超过指定小时数的场景掉落物品
 */
function cleanupSceneItemsByHours($hours) {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // 查询超过指定小时数的掉落物品
    $stmt = $conn->prepare("
        SELECT id, scene_id, item_template_id, quantity, dropped_at,
               it.name as item_name
        FROM scene_items si
        JOIN item_templates it ON si.item_template_id = it.id
        WHERE dropped_at < DATE_SUB(NOW(), INTERVAL ? HOUR)
        ORDER BY dropped_at ASC
    ");
    $stmt->execute([$hours]);
    $expiredItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();
    
    if (empty($expiredItems)) {
        return [
            'cleaned_count' => 0,
            'message' => "没有需要清理的超过{$hours}小时的掉落物品",
            'items' => []
        ];
    }
    
    $cleanedCount = 0;
    $cleanedItems = [];
    $affectedScenes = [];
    
    // 清理Redis中的物品保护信息和数据库中的物品记录
    RedisManager::getInstance()->with(function($redis) use ($conn, $expiredItems, &$cleanedCount, &$cleanedItems, &$affectedScenes) {
        $redisPipe = $redis->pipeline();
        
        foreach ($expiredItems as $item) {
            $sceneItemId = $item['id'];
            $sceneId = $item['scene_id'];
            
            // 清理Redis中的保护信息
            $protectionKey = "scene_item_protection:{$sceneItemId}";
            $redisPipe->del($protectionKey);
            
            // 从数据库中删除物品
            $deleteStmt = $conn->prepare("DELETE FROM scene_items WHERE id = ?");
            $deleteStmt->execute([$sceneItemId]);
            
            if ($deleteStmt->rowCount() > 0) {
                $cleanedCount++;
                $affectedScenes[$sceneId] = true;
                $cleanedItems[] = [
                    'id' => $sceneItemId,
                    'scene_id' => $sceneId,
                    'item_name' => $item['item_name'],
                    'quantity' => $item['quantity'],
                    'dropped_at' => $item['dropped_at']
                ];
            }
        }
        
        $redisPipe->exec();
    });
    
    return [
        'cleaned_count' => $cleanedCount,
        'message' => "成功清理了{$cleanedCount}个超过{$hours}小时的掉落物品",
        'affected_scenes' => array_keys($affectedScenes),
        'items' => $cleanedItems
    ];
}

/**
 * 获取场景掉落物品统计信息
 */
function getSceneItemsStats() {
    $db = Database::getInstance();
    $conn = $db->getConnection();
    
    // 获取总体统计
    $stmt = $conn->prepare("
        SELECT 
            COUNT(*) as total_items,
            COUNT(DISTINCT scene_id) as scenes_with_items,
            MIN(dropped_at) as oldest_drop,
            MAX(dropped_at) as newest_drop
        FROM scene_items
    ");
    $stmt->execute();
    $totalStats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stmt->closeCursor();
    
    // 获取按时间段分组的统计
    $stmt = $conn->prepare("
        SELECT 
            CASE 
                WHEN dropped_at >= DATE_SUB(NOW(), INTERVAL 1 HOUR) THEN '1小时内'
                WHEN dropped_at >= DATE_SUB(NOW(), INTERVAL 6 HOUR) THEN '1-6小时'
                WHEN dropped_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR) THEN '6-24小时'
                ELSE '超过24小时'
            END as time_range,
            COUNT(*) as item_count
        FROM scene_items
        GROUP BY time_range
        ORDER BY 
            CASE 
                WHEN time_range = '1小时内' THEN 1
                WHEN time_range = '1-6小时' THEN 2
                WHEN time_range = '6-24小时' THEN 3
                ELSE 4
            END
    ");
    $stmt->execute();
    $timeStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();
    
    // 获取按场景分组的统计
    $stmt = $conn->prepare("
        SELECT 
            si.scene_id,
            s.name as scene_name,
            COUNT(*) as item_count,
            MIN(si.dropped_at) as oldest_drop
        FROM scene_items si
        LEFT JOIN scenes s ON si.scene_id = s.id
        GROUP BY si.scene_id, s.name
        ORDER BY item_count DESC
        LIMIT 10
    ");
    $stmt->execute();
    $sceneStats = $stmt->fetchAll(PDO::FETCH_ASSOC);
    $stmt->closeCursor();
    
    // 获取可清理的物品数量（超过1小时）
    $stmt = $conn->prepare("
        SELECT COUNT(*) as cleanable_count
        FROM scene_items 
        WHERE dropped_at < DATE_SUB(NOW(), INTERVAL 1 HOUR)
    ");
    $stmt->execute();
    $cleanableStats = $stmt->fetch(PDO::FETCH_ASSOC);
    $stmt->closeCursor();
    
    return [
        'total_stats' => $totalStats,
        'time_stats' => $timeStats,
        'scene_stats' => $sceneStats,
        'cleanable_count' => $cleanableStats['cleanable_count']
    ];
}
?>
