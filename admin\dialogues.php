<?php
// admin/dialogues.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 页面标题和当前页面标识
$pageTitle = '对话管理';
$currentPage = 'dialogues';

// 添加额外的CSS
$extra_css = '
<style>
    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    .header-actions {
        display: flex;
        gap: 10px;
    }
    /* 分组样式 */
    .groups-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 15px;
    }
    .group-item {
        position: relative;
        display: flex;
        align-items: center;
        padding: 10px 15px;
        background: #f8f8f8;
        border: 1px solid #ddd;
        border-radius: 4px;
        text-decoration: none;
        color: #333;
        min-width: 150px;
    }
    .group-item:hover {
        background: #f0f0f0;
    }
    .group-item.active {
        background: #e8f4f8;
        border-color: #4a89dc;
        font-weight: bold;
    }
    .group-type-main {
        border-left: 5px solid #4CAF50;
    }
    .group-type-side {
        border-left: 5px solid #2196F3;
    }
    .group-type-other {
        border-left: 5px solid #9E9E9E;
    }
    .group-count {
        margin-left: 5px;
        font-size: 0.8em;
        color: #666;
    }
    .group-actions {
    display: none;
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
}
.group-item:hover .group-actions {
    display: flex;
    gap: 8px;
}
.group-actions button {
    padding: 3px 8px;
    border-radius: 4px;
    transition: all 0.2s ease;
}
.group-actions button:hover {
    transform: scale(1.05);
}
.group-item.active .group-actions button {
    color: white;
    border-color: rgba(255,255,255,0.7);
}
    /* 徽章样式 */
    .badge {
        display: inline-block;
        padding: 2px 8px;
        border-radius: 12px;
        font-size: 0.8em;
        font-weight: normal;
    }
    .badge-main {
        background-color: #e8f5e9;
        color: #2e7d32;
    }
    .badge-side {
        background-color: #e3f2fd;
        color: #1565c0;
    }
    .badge-other {
        background-color: #f5f5f5;
        color: #616161;
    }
    .badge-secondary {
        background-color: #e0e0e0;
        color: #616161;
    }
    /* 卡片样式 */
    .card {
        border: 1px solid #ddd;
        border-radius: 4px;
        margin-bottom: 20px;
    }
    .card-header {
        background: #f8f8f8;
        padding: 15px;
        border-bottom: 1px solid #ddd;
    }
    .mb-4 {
        margin-bottom: 1.5rem;
    }
</style>
';

// 初始化变量
$success_message = '';
$error_message = '';
$dialogues = [];
$groups = [];  // 添加组变量

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有分组
    $stmt = $pdo->query("
        SELECT * FROM dialogue_groups 
        ORDER BY sort_order, name
    ");
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理添加/编辑/删除对话树
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'add_dialogue') {
            // 添加新对话树
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null;
            
            if (empty($name)) {
                $error_message = "对话树名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO dialogue_trees 
                    (name, group_id, description)
                    VALUES 
                    (:name, :group_id, :description)
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':group_id' => $group_id,
                    ':description' => $description
                ]);
                $success_message = '对话树添加成功！';
            }
            
        } elseif ($_POST['action'] === 'update_dialogue' && isset($_POST['id'])) {
            // 更新对话树
            $id = (int)$_POST['id'];
            $name = $_POST['name'] ?? '';
            $description = $_POST['description'] ?? '';
            $group_id = !empty($_POST['group_id']) ? (int)$_POST['group_id'] : null;
            
            if (empty($name)) {
                $error_message = "对话树名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE dialogue_trees 
                    SET name = :name,
                        group_id = :group_id,
                        description = :description,
                        updated_at = NOW()
                    WHERE id = :id
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':group_id' => $group_id,
                    ':description' => $description,
                    ':id' => $id
                ]);
                $success_message = '对话树更新成功！';
            }
            
        } elseif ($_POST['action'] === 'delete_dialogue' && isset($_POST['id'])) {
            // 删除对话树
            $id = (int)$_POST['id'];
            
            // 检查是否有NPC使用此对话树
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM npc_dialogues WHERE dialogue_tree_id = :id
            ");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $error_message = "无法删除此对话树，有 {$count} 个NPC正在使用它";
            } else {
                // 先删除对话节点
                $stmt = $pdo->prepare("DELETE FROM dialogue_nodes WHERE dialogue_tree_id = :id");
                $stmt->execute([':id' => $id]);
                
                // 再删除对话树
                $stmt = $pdo->prepare("DELETE FROM dialogue_trees WHERE id = :id");
                $stmt->execute([':id' => $id]);
                
                $success_message = '对话树删除成功！';
            }
        } elseif ($_POST['action'] === 'add_group') {
            // 添加新分组
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'other';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO dialogue_groups 
                    (name, type, description, sort_order)
                    VALUES 
                    (:name, :type, :description, :sort_order)
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order
                ]);
                $success_message = '分组添加成功！';
            }
        } elseif ($_POST['action'] === 'update_group' && isset($_POST['id'])) {
            // 更新分组
            $id = (int)$_POST['id'];
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'other';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE dialogue_groups 
                    SET name = :name,
                        type = :type,
                        description = :description,
                        sort_order = :sort_order
                    WHERE id = :id
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order,
                    ':id' => $id
                ]);
                $success_message = '分组更新成功！';
            }
        } elseif ($_POST['action'] === 'delete_group' && isset($_POST['id'])) {
            // 删除分组
            $id = (int)$_POST['id'];
            
            // 先检查是否有对话树使用此分组
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM dialogue_trees WHERE group_id = :id
            ");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $error_message = "无法删除此分组，有 {$count} 个对话树属于该分组";
            } else {
                $stmt = $pdo->prepare("DELETE FROM dialogue_groups WHERE id = :id");
                $stmt->execute([':id' => $id]);
                
                $success_message = '分组删除成功！';
            }
        }
    }
    
    // 获取所有对话树
    $filter_group = isset($_GET['group']) ? (int)$_GET['group'] : null;
    
    $query = "
        SELECT dt.*, 
               COUNT(dn.id) AS node_count,
               COUNT(DISTINCT nd.npc_template_id) AS npc_count,
               dg.name as group_name,
               dg.type as group_type
        FROM dialogue_trees dt
        LEFT JOIN dialogue_nodes dn ON dt.id = dn.dialogue_tree_id
        LEFT JOIN npc_dialogues nd ON dt.id = nd.dialogue_tree_id
        LEFT JOIN dialogue_groups dg ON dt.group_id = dg.id
    ";
    
    $params = [];
    if ($filter_group) {
        $query .= " WHERE dt.group_id = :group_id";
        $params[':group_id'] = $filter_group;
    }
    
    $query .= " GROUP BY dt.id ORDER BY dg.sort_order, dg.name, dt.name";
    
    $stmt = $pdo->prepare($query);
    $stmt->execute($params);
    $dialogues = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 引入页面头部
require_once 'layout_header.php';
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <div class="content-header">
        <div class="header-actions">
            <button id="clear-cache-btn" class="btn btn-warning">清除缓存</button>
            <button id="add-group-btn" class="btn btn-success">添加分组</button>
            <button id="add-dialogue-btn" class="btn btn-primary">添加对话树</button>
        </div>
    </div>
    
    <!-- 缓存清除通知 -->
    <div id="cache-notification" class="alert alert-success" style="display: none;"></div>
    
    <!-- 分组管理 -->
    <div class="card mb-4">
        <div class="card-header">
            <h3>分组管理</h3>
        </div>
        <div class="groups-container">
            <a href="dialogues.php" class="group-item <?php echo !isset($_GET['group']) ? 'active' : ''; ?>">
                全部对话树
            </a>
            <?php foreach($groups as $group): ?>
                <a href="dialogues.php?group=<?php echo $group['id']; ?>" 
                   class="group-item <?php echo (isset($_GET['group']) && $_GET['group'] == $group['id']) ? 'active' : ''; ?> group-type-<?php echo $group['type']; ?>">
                    <?php echo htmlspecialchars($group['name']); ?> 
                    <span class="group-count">(<?php 
                        // 计算该分组下的对话树数量
                        $stmt = $pdo->prepare("SELECT COUNT(*) FROM dialogue_trees WHERE group_id = :group_id");
                        $stmt->execute([':group_id' => $group['id']]);
                        echo $stmt->fetchColumn(); 
                    ?>)</span>
                            <div class="group-actions">
            <button class="btn btn-sm edit-group" data-id="<?php echo $group['id']; ?>" 
                    data-name="<?php echo htmlspecialchars($group['name']); ?>"
                    data-type="<?php echo $group['type']; ?>"
                    data-sort="<?php echo $group['sort_order']; ?>"
                    data-description="<?php echo htmlspecialchars($group['description'] ?? ''); ?>">
                <i class="fa fa-edit"></i> 编辑
            </button>
            <button class="btn btn-sm btn-danger delete-group" data-id="<?php echo $group['id']; ?>" 
                    data-name="<?php echo htmlspecialchars($group['name']); ?>">
                <i class="fa fa-trash"></i> 删除
            </button>
        </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="card">
        <div class="card-header d-flex justify-content-between align-items-center">
            <h3>对话树列表</h3>
            <?php if (isset($_GET['group']) && $_GET['group']): ?>
                <div>
                    当前分组: <?php 
                        foreach($groups as $g) {
                            if ($g['id'] == $_GET['group']) {
                                echo htmlspecialchars($g['name']);
                                break;
                            }
                        }
                    ?>
                </div>
            <?php endif; ?>
        </div>
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>名称</th>
                    <th>分组</th>
                    <th>描述</th>
                    <th>节点数</th>
                    <th>使用NPC数</th>
                    <th>创建时间</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody>
                <?php if (empty($dialogues)): ?>
                    <tr>
                        <td colspan="8" class="text-center">暂无对话树数据</td>
                    </tr>
                <?php else: ?>
                    <?php foreach ($dialogues as $dialogue): ?>
                        <tr>
                            <td><?php echo $dialogue['id']; ?></td>
                            <td><?php echo htmlspecialchars($dialogue['name']); ?></td>
                            <td>
                                <?php if (!empty($dialogue['group_name'])): ?>
                                    <span class="badge badge-<?php echo $dialogue['group_type']; ?>">
                                        <?php echo htmlspecialchars($dialogue['group_name']); ?>
                                    </span>
                                <?php else: ?>
                                    <span class="badge badge-secondary">未分组</span>
                                <?php endif; ?>
                            </td>
                            <td><?php echo htmlspecialchars(mb_substr($dialogue['description'] ?? '', 0, 50) . (mb_strlen($dialogue['description'] ?? '') > 50 ? '...' : '')); ?></td>
                            <td><?php echo $dialogue['node_count']; ?></td>
                            <td><?php echo $dialogue['npc_count']; ?></td>
                            <td><?php echo date('Y-m-d H:i', strtotime($dialogue['created_at'])); ?></td>
                            <td>
                                <a href="dialogue_editor.php?id=<?php echo $dialogue['id']; ?>" class="btn btn-sm">编辑对话</a>
                                <button class="btn btn-sm edit-dialogue" 
                                        data-id="<?php echo $dialogue['id']; ?>" 
                                        data-name="<?php echo htmlspecialchars($dialogue['name']); ?>" 
                                        data-group="<?php echo $dialogue['group_id'] ?? ''; ?>"
                                        data-description="<?php echo htmlspecialchars($dialogue['description'] ?? ''); ?>">编辑信息</button>
                                <button class="btn btn-sm btn-danger delete-dialogue" data-id="<?php echo $dialogue['id']; ?>" data-name="<?php echo htmlspecialchars($dialogue['name']); ?>">删除</button>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                <?php endif; ?>
            </tbody>
        </table>
    </div>
</div>

<!-- 添加对话树模态框 -->
<div id="add-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加对话树</h3>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="add_dialogue">
            
            <div class="form-group">
                <label for="add-name">名称</label>
                <input type="text" id="add-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="add-group">分组</label>
                <select id="add-group" name="group_id" class="form-control">
                    <option value="">-- 选择分组 --</option>
                    <?php foreach ($groups as $group): ?>
                        <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-description">描述</label>
                <textarea id="add-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑对话树模态框 -->
<div id="edit-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑对话树</h3>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="update_dialogue">
            <input type="hidden" name="id" id="edit-id" value="">
            
            <div class="form-group">
                <label for="edit-name">名称</label>
                <input type="text" id="edit-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="edit-group">分组</label>
                <select id="edit-group" name="group_id" class="form-control">
                    <option value="">-- 选择分组 --</option>
                    <?php foreach ($groups as $group): ?>
                        <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-description">描述</label>
                <textarea id="edit-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 添加分组模态框 -->
<div id="add-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加分组</h3>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="add_group">
            
            <div class="form-group">
                <label for="add-group-name">名称</label>
                <input type="text" id="add-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="add-group-type">类型</label>
                <select id="add-group-type" name="type" class="form-control">
                    <option value="main">主线</option>
                    <option value="side">支线</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-group-sort">排序顺序</label>
                <input type="number" id="add-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="add-group-description">描述</label>
                <textarea id="add-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑分组模态框 -->
<div id="edit-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑分组</h3>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="update_group">
            <input type="hidden" name="id" id="edit-group-id" value="">
            
            <div class="form-group">
                <label for="edit-group-name">名称</label>
                <input type="text" id="edit-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="edit-group-type">类型</label>
                <select id="edit-group-type" name="type" class="form-control">
                    <option value="main">主线</option>
                    <option value="side">支线</option>
                    <option value="other">其他</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-group-sort">排序顺序</label>
                <input type="number" id="edit-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="edit-group-description">描述</label>
                <textarea id="edit-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除分组确认模态框 -->
<div id="delete-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-group-message"></p>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="delete_group">
            <input type="hidden" name="id" id="delete-group-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-dialogue-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-dialogue-message"></p>
        
        <form method="post" action="dialogues.php">
            <input type="hidden" name="action" value="delete_dialogue">
            <input type="hidden" name="id" id="delete-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    var addModal = document.getElementById('add-dialogue-modal');
    var editModal = document.getElementById('edit-dialogue-modal');
    var deleteModal = document.getElementById('delete-dialogue-modal');
    var addGroupModal = document.getElementById('add-group-modal');
    var editGroupModal = document.getElementById('edit-group-modal');
    var deleteGroupModal = document.getElementById('delete-group-modal');
    var cacheNotification = document.getElementById('cache-notification');
    
    // 清除缓存按钮
    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        // 禁用按钮，防止重复点击
        this.disabled = true;
        this.textContent = '正在清除...';
        
        // 发送AJAX请求清除缓存
        fetch('clear_cache.php')
            .then(response => response.json())
            .then(data => {
                // 显示通知
                cacheNotification.textContent = data.message;
                cacheNotification.style.display = 'block';
                cacheNotification.className = data.success ? 'alert alert-success' : 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
                
                // 3秒后隐藏通知
                setTimeout(function() {
                    cacheNotification.style.display = 'none';
                }, 3000);
            })
            .catch(error => {
                console.error('清除缓存出错:', error);
                cacheNotification.textContent = '清除缓存请求失败，请检查网络连接';
                cacheNotification.style.display = 'block';
                cacheNotification.className = 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
            });
    });
    
    // 添加对话树按钮
    document.getElementById('add-dialogue-btn').addEventListener('click', function() {
        addModal.style.display = 'block';
    });
    
    // 编辑对话树按钮
    document.querySelectorAll('.edit-dialogue').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var name = this.dataset.name;
            var groupId = this.dataset.group;
            var description = this.dataset.description;
            
            document.getElementById('edit-id').value = id;
            document.getElementById('edit-name').value = name;
            document.getElementById('edit-description').value = description;
            
            // 设置分组下拉框的值
            if (groupId) {
                document.getElementById('edit-group').value = groupId;
            } else {
                document.getElementById('edit-group').value = '';
            }
            
            editModal.style.display = 'block';
        });
    });
    
    // 删除对话树按钮
    document.querySelectorAll('.delete-dialogue').forEach(function(btn) {
        btn.addEventListener('click', function() {
            var id = this.dataset.id;
            var name = this.dataset.name;
            
            document.getElementById('delete-id').value = id;
            document.getElementById('delete-dialogue-message').textContent = '确定要删除对话树 "' + name + '" 吗？此操作将同时删除所有相关的对话节点，且无法撤销。';
            
            deleteModal.style.display = 'block';
        });
    });
    
    // 添加分组按钮
    document.getElementById('add-group-btn').addEventListener('click', function() {
        addGroupModal.style.display = 'block';
    });
    
    // 编辑分组按钮
    document.querySelectorAll('.edit-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var id = this.dataset.id;
            var name = this.dataset.name;
            var type = this.dataset.type;
            var sort = this.dataset.sort;
            var description = this.dataset.description;
            
            document.getElementById('edit-group-id').value = id;
            document.getElementById('edit-group-name').value = name;
            document.getElementById('edit-group-type').value = type;
            document.getElementById('edit-group-sort').value = sort;
            document.getElementById('edit-group-description').value = description;
            
            editGroupModal.style.display = 'block';
        });
    });
    
    // 删除分组按钮
    document.querySelectorAll('.delete-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            var id = this.dataset.id;
            var name = this.dataset.name;
            
            document.getElementById('delete-group-id').value = id;
            document.getElementById('delete-group-message').textContent = '确定要删除分组 "' + name + '" 吗？此操作无法撤销。';
            
            deleteGroupModal.style.display = 'block';
        });
    });
    
    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            addModal.style.display = 'none';
            editModal.style.display = 'none';
            deleteModal.style.display = 'none';
            addGroupModal.style.display = 'none';
            editGroupModal.style.display = 'none';
            deleteGroupModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == addModal) {
            addModal.style.display = 'none';
        }
        if (event.target == editModal) {
            editModal.style.display = 'none';
        }
        if (event.target == deleteModal) {
            deleteModal.style.display = 'none';
        }
        if (event.target == addGroupModal) {
            addGroupModal.style.display = 'none';
        }
        if (event.target == editGroupModal) {
            editGroupModal.style.display = 'none';
        }
        if (event.target == deleteGroupModal) {
            deleteGroupModal.style.display = 'none';
        }
    });
});
</script>

<?php require_once 'layout_footer.php'; ?> 