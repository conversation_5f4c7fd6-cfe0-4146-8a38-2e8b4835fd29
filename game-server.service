[Unit]
Description=My WAP MUD Game Server
After=network.target mysql.service redis.service

[Service]
# !!重要!!: 请将 'yescion' 替换为您在服务器上运行此脚本的实际用户名
User=www

# !!重要!!: 这是您项目的绝对路径，请确认是否正确
WorkingDirectory=/www/wwwroot/wx.xstudio.net.cn

# 这里我们直接启动PHP脚本，而不是通过.sh脚本
# systemd会自动处理后台运行和重启
ExecStart=/www/wwwroot/wx.xstudio.net.cn/start_game.sh

Restart=on-failure
RestartSec=5s

[Install]
WantedBy=multi-user.target