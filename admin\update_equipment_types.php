<?php
require_once '../config/Database.php';

try {
    $db = Database::getInstance()->getConnection();
    
    echo "<h2>更新装备类型标识</h2>\n";
    
    // 1. 添加equipment_type字段（如果不存在）
    echo "<p>1. 检查并添加equipment_type字段...</p>\n";
    try {
        $db->exec("ALTER TABLE item_templates ADD COLUMN equipment_type VARCHAR(20) DEFAULT 'player'");
        echo "<p style='color: green;'>✓ equipment_type字段添加成功</p>\n";
    } catch (PDOException $e) {
        if (strpos($e->getMessage(), 'Duplicate column name') !== false) {
            echo "<p style='color: blue;'>ℹ equipment_type字段已存在</p>\n";
        } else {
            throw $e;
        }
    }
    
    // 2. 更新现有装备的类型
    echo "<p>2. 根据装备名称模式标记怪物装备...</p>\n";
    
    $monsterPatterns = [
        '%破损%', '%腐朽%', '%断弦%', '%碎裂%', '%腐化%', '%破烂%', '%腐蚀%', 
        '%生锈%', '%破旧%', '%枯木%', '%粗制%', '%骨制%', '%石制%', '%木制%', 
        '%兽骨%', '%皮制%', '%布制%', '%铁环%', '%兽牙%', '%粗麻%', '%兽皮%', 
        '%铁制%', '%草编%', '%布条%', '%虚空%', '%熔岩%', '%荒野%', '%沙砾%', 
        '%风化%', '%风暴%', '%峡谷%', '%星光%', '%教团%', '%遗迹%', '%符文%', 
        '%冰晶%', '%术士%', '%烈焰%', '%战将%', '%王者%', '%飓风%', '%酋长%', 
        '%风王%', '%净化%', '%扭曲%', '%祭坛%', '%尘埃%', '%时空%', '%矿工%'
    ];
    
    $whereClause = implode(' OR ', array_fill(0, count($monsterPatterns), 'name LIKE ?'));
    
    $stmt = $db->prepare("
        UPDATE item_templates 
        SET equipment_type = 'monster'
        WHERE category = 'Equipment' 
          AND ({$whereClause})
    ");
    
    $stmt->execute($monsterPatterns);
    $updatedCount = $stmt->rowCount();
    
    echo "<p style='color: green;'>✓ 已更新 {$updatedCount} 件装备为怪物装备</p>\n";
    
    // 3. 显示统计结果
    echo "<p>3. 装备类型统计:</p>\n";
    $stmt = $db->query("
        SELECT 
            COALESCE(equipment_type, 'NULL') as equipment_type,
            COUNT(*) as count
        FROM item_templates 
        WHERE category = 'Equipment'
        GROUP BY equipment_type
        ORDER BY equipment_type
    ");
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>装备类型</th><th>数量</th></tr>\n";
    
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        $type = $row['equipment_type'];
        $count = $row['count'];
        $typeLabel = $type === 'player' ? '玩家装备' : ($type === 'monster' ? '怪物装备' : '未分类');
        echo "<tr><td>{$typeLabel} ({$type})</td><td>{$count}</td></tr>\n";
    }
    echo "</table>\n";
    
    // 4. 显示一些示例装备
    echo "<p>4. 怪物装备示例:</p>\n";
    $stmt = $db->query("
        SELECT name 
        FROM item_templates 
        WHERE category = 'Equipment' AND equipment_type = 'monster'
        ORDER BY name
        LIMIT 10
    ");
    
    echo "<ul>\n";
    while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
        echo "<li>{$row['name']}</li>\n";
    }
    echo "</ul>\n";
    
    echo "<p style='color: green; font-weight: bold;'>✓ 装备类型更新完成！</p>\n";
    echo "<p><a href='monsters.php'>返回怪物管理页面</a></p>\n";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>\n";
    echo "<p>详细信息: " . $e->getTraceAsString() . "</p>\n";
}
?>
