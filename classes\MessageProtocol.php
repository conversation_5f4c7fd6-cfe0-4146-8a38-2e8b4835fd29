<?php
// classes/MessageProtocol.php

class MessageProtocol {
    // Opcodes Definition
    // A. Bi-directional
    const PING                              = 0x00;
    const PONG                              = 0x01;
    
    // B. Client to Server (C2S)
    const C2S_REGISTER                      = 0x08;
    const C2S_LOGIN                         = 0x10;
    const C2S_ENTER_SCENE                   = 0x11;
    const C2S_RESUME_SESSION                = 0x12;
    const C2S_LOGOUT                        = 0x13;
    const C2S_HEAL                          = 0x14; 
    const C2S_GET_SCENES                    = 0x15;
    const C2S_RESET_SCENE                   = 0x16;
    const C2S_REVIVE                        = 0x17;
    const C2S_START_BATTLE                  = 0x18;
    const C2S_BATTLE_ACTION                 = 0x19; // Player attacks
    const C2S_FLEE_BATTLE                   = 0x1A;
    const C2S_MOVE                          = 0x1B;
    const C2S_MONSTER_ATTACK                = 0x1C; // 保留，但在服务端ATB系统中不再使用
    const C2S_GET_INVENTORY                 = 0x1D;
    const C2S_USE_ITEM                      = 0x1E;
    const C2S_EQUIP_ITEM                    = 0x1F;
    const C2S_UNEQUIP_ITEM                  = 0x20;
    const C2S_SOCKET_GEM                    = 0x21;
    const C2S_GET_MONSTER_DETAILS           = 0x22;
    const C2S_GET_PLAYER_DETAILS            = 0x23;
    const C2S_PICKUP_ITEM                   = 0x24;
    const C2S_ALLOCATE_POTENTIAL_POINTS     = 0x25;
    const C2S_GET_COMBAT_POTION_CONFIG      = 0x26;
    const C2S_SET_COMBAT_POTION_CONFIG      = 0x27;
    const C2S_GET_PLAYER_SKILLS             = 0x28;
    const C2S_GET_BATTLE_SKILLS             = 0x29; // NEW: 获取战斗中的可用技能
    const C2S_GET_BUILDING_DATA             = 0x2A; // NEW: 获取建筑数据, 如商店
    const C2S_BUY_ITEM                      = 0x2B; // NEW: 购买商店物品
    const C2S_SELL_ITEM                     = 0x2C; // NEW: 出售物品到商店
    const C2S_REVIVE_IN_BUILDING            = 0x2D; // NEW: 在复活建筑中复活
    const C2S_TELEPORT                      = 0x2E; // NEW: 传送到其他场景
    const C2S_TALK_TO_NPC                   = 0x2F; // NEW: 与NPC对话
    const C2S_CONTINUE_DIALOGUE             = 0x30; // NEW: 继续对话
    const C2S_SELECT_DIALOGUE_OPTION        = 0x31; // NEW: 选择对话选项
    const C2S_GET_QUEST_LIST                = 0x32; // NEW: 获取任务列表
    const C2S_ACCEPT_QUEST                  = 0x33; // NEW: 接受任务
    const C2S_COMPLETE_QUEST                = 0x34; // NEW: 完成任务
    const C2S_ABANDON_QUEST                 = 0x35; // NEW: 放弃任务
    const C2S_VIEW_NPC_DETAIL               = 0x36; // NEW: 查看NPC详情
    const C2S_GET_RECIPES                   = 0x37;
    const C2S_CRAFT_ITEM                    = 0x38;
    const C2S_GET_REFINE_MATERIALS          = 0x39;
    const C2S_REFINE_ITEM                   = 0x3A;
    const C2S_DROP_ITEM                     = 0x3B;
    const C2S_BIND_ITEM                     = 0x3C;
    const C2S_PRELOAD_DIALOGUE              = 0x3D;
    const C2S_BUY_DIAMOND_ITEM              = 0x3E;
    const C2S_GET_REFINE_COST_ESTIMATE      = 0x3F;

    // PVP相关协议
    const C2S_PVP_CHALLENGE                 = 0x50;
    const C2S_PVP_ACCEPT_CHALLENGE          = 0x51;
    const C2S_PVP_DECLINE_CHALLENGE         = 0x52;
    const C2S_PVP_SET_INTENTION             = 0x53;
    const C2S_PVP_ACTION                    = 0x54;
    const C2S_PVP_SURRENDER                 = 0x55;
    const C2S_GET_PVP_LEADERBOARD           = 0x56;
    const C2S_GET_PVP_STATS                 = 0x57;
    const C2S_PVP_DIRECT_ATTACK             = 0x58;

    // 聊天相关协议
    const C2S_SEND_PUBLIC_CHAT              = 0x60;
    const C2S_GET_PUBLIC_CHAT               = 0x61;
    const C2S_SEND_PRIVATE_CHAT             = 0x62;
    const C2S_GET_PRIVATE_CHAT              = 0x63;
    const C2S_GET_CHAT_CONTACTS             = 0x64;
    const C2S_DELETE_CHAT_CONTACT           = 0x65;
    const C2S_MARK_MESSAGES_READ            = 0x66;
    const C2S_CRAFT_GEM                     = 0x67;

    // 仓库相关协议
    const C2S_WAREHOUSE_DEPOSIT             = 0x68; // 存入物品到仓库
    const C2S_WAREHOUSE_WITHDRAW            = 0x69; // 从仓库取出物品
    const C2S_WAREHOUSE_EXPAND              = 0x6A; // 扩容仓库
    const C2S_GET_GEM_RECIPES               = 0x6B;

    // 属性重修相关协议
    const C2S_ATTRIBUTE_RESET               = 0x6C; // 属性重修

    // 公告相关协议
    const C2S_GET_LATEST_ANNOUNCEMENT       = 0x6D; // 获取最新公告
    const C2S_GET_ANNOUNCEMENT_DETAIL       = 0x6E; // 获取公告详情
    const C2S_GET_ANNOUNCEMENT_LIST         = 0x6F; // 获取公告列表

    // 兑换码相关协议
    const C2S_REDEEM_CODE                   = 0x70; // 兑换码兑换

    // 排行榜相关协议
    const C2S_GET_LEVEL_RANKING             = 0x71;
    const C2S_GET_HERO_RANKING              = 0x72;
    const C2S_GET_VILLAIN_RANKING           = 0x73;

    // 交易相关协议
    const C2S_TRADE_REQUEST                 = 0x74; // 发起交易请求
    const C2S_TRADE_RESPOND                 = 0x75; // 响应交易请求
    const C2S_TRADE_ADD_ITEM                = 0x76; // 添加交易物品
    const C2S_TRADE_REMOVE_ITEM             = 0x77; // 移除交易物品
    const C2S_TRADE_ADD_CURRENCY            = 0x78; // 添加交易货币
    const C2S_TRADE_CONFIRM                 = 0x79; // 确认交易
    const C2S_TRADE_FINAL_CONFIRM           = 0x7B; // 最终确认交易
    const C2S_TRADE_CANCEL                  = 0x7C; // 取消交易

    // 装备凝练榜相关协议
    const C2S_GET_REFINE_LEADERBOARD        = 0x7A; // 获取装备凝练榜

    // C. Server to Client (S2C)
    const S2C_REGISTER_SUCCESS              = 0x7D;
    const S2C_LOGIN_SUCCESS                 = 0x80;
    const S2C_ERROR                         = 0x81;
    const S2C_SCENES_LIST                   = 0x82;
    const S2C_SCENE_ENTERED                 = 0x83;
    const S2C_INFO_MESSAGE                  = 0x84;
    const S2C_SCENE_PLAYER_CHANGE           = 0x85;
    const S2C_PLAYER_HEALED                 = 0x87;
    const S2C_SCENE_RESET                   = 0x88;
    const S2C_PLAYER_REVIVED                = 0x89;
    const S2C_BATTLE_STARTED                = 0x90;
    const S2C_BATTLE_UPDATE                 = 0x91; // 包含ATB状态更新
    const S2C_BATTLE_TURN_UPDATE            = 0x91; // 与 BATTLE_UPDATE 相同值
    const S2C_BATTLE_ENDED                  = 0x92;
    const S2C_BATTLE_LOG                    = 0x93;
    const S2C_INVENTORY_DATA                = 0x94;
    const S2C_PLAYER_ATTRIBUTE_UPDATE       = 0x95;
    const S2C_MONSTER_DETAILS               = 0x96;
    const S2C_PLAYER_DETAILS                = 0x97;
    const S2C_ATB_STATUS_UPDATE             = 0x98;
    const S2C_COMBAT_POTION_CONFIG_DATA     = 0x99;
    const S2C_PLAYER_SKILLS_LIST            = 0x9A;
    const S2C_BATTLE_SKILLS_LIST            = 0x9B;
    const S2C_BUILDING_DATA                 = 0x9C;
    const S2C_SHOP_NOTIFICATION             = 0x9D;
    const S2C_NPC_DIALOGUE                  = 0x9E;
    const S2C_QUEST_LIST                    = 0x9F;
    const S2C_QUEST_UPDATE                  = 0xA0;
    const S2C_SCENE_NPCS                    = 0xA1;
    const S2C_NPC_DETAIL                    = 0xA2;
    const S2C_RECIPES_DATA                  = 0xA3;
    const S2C_CRAFT_RESULT                  = 0xA4;
    const S2C_REFINE_MATERIALS_DATA         = 0xA5;
    const S2C_REFINE_RESULT                 = 0xA6;
    const S2C_ITEM_DROPPED                  = 0xA7;
    const S2C_ITEM_BOUND                    = 0xA8;
    const S2C_REFINE_COST_ESTIMATE          = 0xA9;
    const S2C_GEM_RECIPES_DATA              = 0xB0;
    const S2C_GEM_CRAFT_RESULT              = 0xB1;
    const S2C_SUCCESS                       = 0xB2; // 通用成功消息

    // 公告相关响应
    const S2C_LATEST_ANNOUNCEMENT           = 0xB3; // 最新公告数据
    const S2C_ANNOUNCEMENT_DETAIL           = 0xB4; // 公告详情数据
    const S2C_ANNOUNCEMENT_LIST             = 0xB5; // 公告列表数据

    // 兑换码相关响应
    const S2C_REDEEM_CODE_RESULT            = 0xB6; // 兑换码兑换结果

    // PVP相关响应
    const S2C_PVP_CHALLENGE                 = 0xD0;
    const S2C_PVP_CHALLENGE_RESPONSE        = 0xD1;
    const S2C_PVP_BATTLE_STARTED            = 0xD2;
    const S2C_PVP_BATTLE_UPDATE             = 0xD3;
    const S2C_PVP_ATB_STATUS_UPDATE         = 0xD4;
    const S2C_PVP_BATTLE_ENDED              = 0xD5;
    const S2C_PVP_LEADERBOARD               = 0xD6;
    const S2C_PVP_STATS                     = 0xD7;

    // 排行榜相关响应
    const S2C_LEVEL_RANKING                 = 0xD8;
    const S2C_HERO_RANKING                  = 0xD9;
    const S2C_VILLAIN_RANKING               = 0xDA;

    // 聊天相关响应
    const S2C_PUBLIC_CHAT_MESSAGE           = 0xE0;
    const S2C_PUBLIC_CHAT_HISTORY           = 0xE1;
    const S2C_PRIVATE_CHAT_MESSAGE          = 0xE2;
    const S2C_PRIVATE_CHAT_HISTORY          = 0xE3;
    const S2C_CHAT_CONTACTS_LIST            = 0xE4;
    const S2C_CHAT_CONTACT_UPDATED          = 0xE5;

    // 交易相关响应
    const S2C_TRADE_REQUEST_RECEIVED        = 0xF0; // 收到交易请求
    const S2C_TRADE_STARTED                 = 0xF1; // 交易开始
    const S2C_TRADE_UPDATE                  = 0xF2; // 交易内容更新
    const S2C_TRADE_CONFIRMED               = 0xF3; // 交易确认状态更新
    const S2C_TRADE_FINAL_CONFIRMED         = 0xF7; // 最终确认状态更新
    const S2C_TRADE_COMPLETED               = 0xF4; // 交易完成
    const S2C_TRADE_CANCELLED               = 0xF5; // 交易取消
    const S2C_TRADE_ERROR                   = 0xF6; // 交易错误

    // 装备凝练榜相关响应
    const S2C_REFINE_LEADERBOARD            = 0xF8; // 装备凝练榜数据

    //回城
    const C2S_REVIVE_CITY                   = 0xF9; // 回城
    /**
     * Decodes an incoming binary message (opcode + json) into an associative array.
     * This is for UNENCRYPTED messages only.
     * @param string $binaryMsg The binary message from the client.
     * @return array|null The decoded data or null on failure.
     */
    public static function decode($binaryMsg) {
        if (empty($binaryMsg) || strlen($binaryMsg) < 1) {
            return null;
        }
        try {
            $opcode = unpack('C', $binaryMsg)[1];
            $payloadJson = substr($binaryMsg, 1);
            $payload = json_decode($payloadJson, true);

            if ($payload === null && $payloadJson !== 'null' && $payloadJson !== '') {
                 error_log("MessageProtocol::decode JSON decode error for opcode " . dechex($opcode));
                 return null;
            }

            $decodedData = ['type' => $opcode];
            if (is_array($payload)) {
                $decodedData = array_merge($decodedData, $payload);
            }
            return $decodedData;

        } catch (Exception $e) {
            error_log("MessageProtocol::decode Exception: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Encodes an associative array into a binary message (opcode + json).
     * This is for UNENCRYPTED messages only.
     * @param int $type The opcode for the message.
     * @param array $payload The data to encode.
     * @return string|null The packed binary message or null on failure.
     */
    public static function encode($type, $payload = []) {
        try {
            $payloadJson = json_encode($payload, JSON_UNESCAPED_UNICODE);
            if ($payloadJson === false) {
                error_log("MessageProtocol::encode JSON encode error for opcode " . dechex($type));
                return null;
            }
            return pack('C', $type) . $payloadJson;
        } catch (Exception $e) {
            error_log("MessageProtocol::encode Exception: " . $e->getMessage());
            return null;
        }
    }
} 