<?php
// classes/SceneManager.php

class SceneManager {
    private $db;
    private $scenesCache = null;
    
    public function __construct(Database $db) {
        $this->db = $db;
    }
    
    public function clearCache() {
        $this->scenesCache = null;
        echo "场景缓存已清除。\n";
    }

    public function getAllScenes() {
        if ($this->scenesCache === null) {
            $stmt = $this->db->query("SELECT * FROM scenes");
            $this->scenesCache = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
    }
        return $this->scenesCache;
    }
    
    public function getInitialSceneId() {
        // 您可以根据自己的逻辑定义初始场景ID
        // 如果没有初始场景ID，返回任意一个安全区场景
        $db = Database::getInstance();
        $stmt = $db->query("SELECT id FROM scenes WHERE is_safe_zone = 1 ORDER by id LIMIT 1");
        $safeScene = $stmt->fetch();
        $stmt->closeCursor();

        return $safeScene ? $safeScene['id'] : 'town_square';
        // return 'castle_001';
    }
    
    public function getSceneData($sceneId) {
        $scenes = $this->getAllScenes();
        foreach ($scenes as $scene) {
            if ($scene['id'] === $sceneId) {
                $scene['monsters'] = $this->getSceneMonsters($sceneId);
                $scene['items'] = $this->getSceneItems($sceneId);
                $scene['buildings'] = $this->getSceneBuildings($sceneId);
                
                // 添加安全区标记，如果是安全区，在场景名称后添加[安]标记
                if (isset($scene['is_safe_zone']) && $scene['is_safe_zone'] == 1) {
                    $scene['display_name'] = $scene['name'] . " [安]";
                } else {
                    $scene['display_name'] = $scene['name'];
                }
                
                return $scene;
            }
        }
        return null;
    }
    
    public function getSceneBuildings($sceneId) {
        $stmt = $this->db->query(
            "SELECT 
                sb.id as scene_building_id,
                b.id as building_id,
                b.name,
                b.type,
                b.description
             FROM scene_buildings sb
             JOIN buildings b ON sb.building_id = b.id
             WHERE sb.scene_id = ?",
            [$sceneId]
        );
        $buildings = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $buildings;
    }
    
    public function getSceneItems($sceneId) {
        $stmt = $this->db->query(
            "SELECT si.id, si.item_template_id, si.quantity, si.instance_data, it.name, it.category, it.stackable, it.effects, it.description, si.dropped_at
             FROM scene_items si
             JOIN item_templates it ON si.item_template_id = it.id
             WHERE si.scene_id = ?
             ORDER BY si.dropped_at ASC",
            [$sceneId]
        );
        $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        // 处理每个物品，解析instance_data中的display_name
        foreach ($items as &$item) {
            if (!empty($item['instance_data'])) {
                try {
                    $instanceData = json_decode($item['instance_data'], true);
                    if (isset($instanceData['display_name'])) {
                        $item['display_name'] = $instanceData['display_name'];
                    }
                } catch (Exception $e) {
                    // 如果JSON解析失败，忽略错误，使用默认名称
                    error_log("Failed to parse instance_data for scene item {$item['id']}: " . $e->getMessage());
                }
            }
        }

        return $items;
    }
    
    public function getSceneMonsters($sceneId) {
        $monstersData = RedisManager::getInstance()->with(function($redis) use ($sceneId) {
            return $redis->hgetall("scene_monsters:{$sceneId}");
        });
        $monsters = [];

        // If Redis has data, decode it.
        if (!empty($monstersData)) {
            // Check for the empty marker first.
            if (isset($monstersData['_EMPTY_'])) {
                return []; // Return empty array if the marker is present.
            }
            foreach ($monstersData as $monsterId => $monsterJson) {
                $monsters[$monsterId] = json_decode($monsterJson, true);
            }
            return $monsters;
            }
            
        // If Redis is empty, load from database and cache.
        // This query is now compatible with the user's provided database schema.
        $stmt = $this->db->query(
            "SELECT 
                sm.monster_template_id, sm.quantity,
                mt.name, mt.description, mt.base_hp, mt.base_max_hp, mt.base_attack
             FROM scene_monsters sm
             JOIN monster_templates mt ON sm.monster_template_id = mt.id
             WHERE sm.scene_id = ?",
            [$sceneId]
        );
        $monsterSpawns = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if (empty($monsterSpawns)) {
            // Cache an empty marker to avoid repeated DB queries for scenes with no monsters.
            RedisManager::getInstance()->with(function($redis) use ($sceneId) {
                $redis->hset("scene_monsters:{$sceneId}", "_EMPTY_", "1");
                $redis->expire("scene_monsters:{$sceneId}", 300); // Expire after 5 mins
            });
            return [];
        }

        $monstersToCache = [];
        // Instantiate monsters based on the templates and quantity.
        foreach ($monsterSpawns as $spawn) {
            for ($i = 1; $i <= $spawn['quantity']; $i++) {
                $uniqueId = "{$sceneId}_{$spawn['monster_template_id']}_{$i}";
                
                // Create a monster instance.
                $monsterInstance = [
                    'id' => $uniqueId,
                    'scene_id' => $sceneId,
                    'monster_template_id' => $spawn['monster_template_id'],
                    'instance_id' => $i,
                    'name' => $spawn['name'],
                    'description' => $spawn['description'],
                    'hp' => $spawn['base_hp'], // Use base_hp from DB
                    'max_hp' => $spawn['base_max_hp'], // Use base_max_hp from DB
                    'attack' => $spawn['base_attack'], // Use base_attack from DB
                    // Provide default values for attributes not present in the monster_templates table.
                    'level' => 1,
                    'defense' => 5,
                    'attack_speed' => 1.0,
                    'experience_reward' => 10,
                    'loot_table_id' => null
                ];

                $monsters[$uniqueId] = $monsterInstance;
                $monstersToCache[$uniqueId] = json_encode($monsterInstance);
            }
        }
        
        if (!empty($monstersToCache)) {
            RedisManager::getInstance()->with(function($redis) use ($sceneId, $monstersToCache) {
                $redis->hMSet("scene_monsters:{$sceneId}", $monstersToCache);
            });
        }

        return $monsters;
    }
    
    public function resetSceneMonsters($sceneId) {
        RedisManager::getInstance()->with(function($redis) use ($sceneId) {
            $redis->del("scene_monsters:{$sceneId}");
            $redis->del("scene_last_monster_death_time:{$sceneId}");
        });
        return $this->getSceneData($sceneId);
    }
    
    public function getMonsterFromScene($sceneId, $monsterId) {
        $monsters = $this->getSceneMonsters($sceneId);
        return $monsters[$monsterId] ?? null;
    }

    public function getScenePlayerIds($sceneId) {
        return RedisManager::getInstance()->with(function($redis) use ($sceneId) {
            return $redis->smembers("scene_players:{$sceneId}");
        }, []);
    }

    public function getScenePlayers($sceneId) {
        $playerIds = $this->getScenePlayerIds($sceneId);
            if (empty($playerIds)) {
            return [];
        }

        $placeholders = implode(',', array_fill(0, count($playerIds), '?'));
        
        $stmt = $this->db->query(
            "SELECT acc.id, acc.username, pa.*, j.name as job_name, j.description as job_description
             FROM accounts acc
             JOIN player_attributes pa ON acc.id = pa.account_id
             LEFT JOIN jobs j ON pa.current_job_id = j.id
             WHERE acc.id IN ($placeholders)",
            $playerIds
        );
        $allPlayersData = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $formattedPlayers = [];
        foreach ($allPlayersData as $playerData) {
            $attributes = $playerData;
            unset($attributes['id'], $attributes['username'], $attributes['account_id']);
            
            $formattedPlayers[] = [
                'id' => $playerData['id'],
                'username' => $playerData['username'],
                'attributes' => $attributes
            ];
        }

        return $formattedPlayers;
    }

    public function playerEnterScene($playerId, $newSceneId) {
        $oldSceneId = RedisManager::getInstance()->with(function($redis) use ($playerId) {
            return $redis->get("player_scene:{$playerId}");
        });

        RedisManager::getInstance()->with(function($redis) use ($oldSceneId, $newSceneId, $playerId) {
            if ($oldSceneId) {
                $redis->srem("scene_players:{$oldSceneId}", $playerId);
            }
            
            $redis->set("player_scene:{$playerId}", $newSceneId);
            $redis->sadd("scene_players:{$newSceneId}", $playerId);
        });
        
        $this->db->query("UPDATE player_attributes SET current_scene_id = ? WHERE account_id = ?", [$newSceneId, $playerId]);
        
        return [
            'scene_data' => $this->getSceneData($newSceneId),
            'old_scene_id' => $oldSceneId
        ];
    }

    public function getSceneExits($x, $y, $z) {
        $scenes = $this->getAllScenes();
        $exits = ['north' => null, 'south' => null, 'east' => null, 'west' => null];
        
        foreach ($scenes as $scene) {
            if ($scene['x'] == $x && $scene['y'] == $y + 1 && $scene['z'] == $z) $exits['north'] = $scene;
            if ($scene['x'] == $x && $scene['y'] == $y - 1 && $scene['z'] == $z) $exits['south'] = $scene;
            if ($scene['x'] == $x + 1 && $scene['y'] == $y && $scene['z'] == $z) $exits['east'] = $scene;
            if ($scene['x'] == $x - 1 && $scene['y'] == $y && $scene['z'] == $z) $exits['west'] = $scene;
        }

        return $exits;
    }
}
