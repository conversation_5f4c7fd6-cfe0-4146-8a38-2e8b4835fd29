<?php
// admin/layout_header.php
session_start();

// 更加健壮的登录检查
if (empty($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    // 记录重定向日志
    error_log('未登录，重定向至登录页面。当前页面：' . $_SERVER['PHP_SELF']);
    
    // 防止无限重定向
    if (basename($_SERVER['PHP_SELF']) !== 'index.php') {
        header('Location: index.php');
        exit;
    }
}

// $currentPage is expected to be set before including this file
if (!isset($currentPage)) {
    $currentPage = '';
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo $pageTitle; ?> - 游戏管理后台</title>
    <link rel="stylesheet" href="/admin/style.css">
    
    <!-- jQuery and Select2 -->
    <script src="/admin/jquery-3.6.0.min.js"></script>
    <link href="/admin/select2.min.css" rel="stylesheet" />
    <script src="/admin/select2.min.js"></script>

    <!-- DataTables -->
    <link rel="stylesheet" href="/admin/jquery.dataTables.min.css">
    <script src="/admin/jquery.dataTables.min.js"></script>

    <link rel="stylesheet" href="/admin/codemirror.min.css">
    <link rel="stylesheet" href="/admin/material-darker.min.css">

    <!-- 全局Toast脚本 -->
    <script src="/admin/global-toast.js"></script>

    <?php if (isset($extra_css)) { echo $extra_css; } ?>

    <style>
    .session-timer {
        color: #666;
        font-size: 12px;
        margin-right: 15px;
    }

    .session-timer.warning {
        color: #e74c3c;
        font-weight: bold;
    }

    .session-timer.critical {
        color: #c0392b;
        font-weight: bold;
        animation: blink 1s infinite;
    }

    @keyframes blink {
        0%, 50% { opacity: 1; }
        51%, 100% { opacity: 0.3; }
    }
    </style>
</head>
<body>
    <div class="top-nav">
        <!-- <div class="top-nav-logo">
            <a href="dashboard.php">游戏管理后台</a>
        </div> -->
        <nav class="top-nav-links">
            <ul>
                <li class="<?php echo ($currentPage == 'dashboard') ? 'active' : ''; ?>"><a href="/admin/dashboard.php">首页</a></li>

                <!-- 物品系统 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['items', 'recipes', 'gem_recipes', 'skills', 'loot_tables'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">物品</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage == 'items') ? 'active' : ''; ?>">
                            <a href="/admin/items.php">物品模板</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'batch_equipment') ? 'active' : ''; ?>">
                            <a href="/admin/batch_equipment.php">批量装备创建</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'recipes') ? 'active' : ''; ?>">
                            <a href="/admin/recipes.php">材料合成</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'gem_recipes') ? 'active' : ''; ?>">
                            <a href="/admin/gem_recipes.php">宝石合成</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'skills') ? 'active' : ''; ?>">
                            <a href="/admin/skills.php">技能管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'loot_tables') ? 'active' : ''; ?>">
                            <a href="/admin/loot_tables.php">掉落配置</a>
                        </li>
                    </ul>
                </li>

                <!-- 装备凝练系统 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['refine_elements', 'refine_scores', 'refine_combos', 'refine_tiers', 'refine_attribute_bonuses'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">凝练</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage == 'refine_elements') ? 'active' : ''; ?>">
                            <a href="/admin/refine_elements.php">材料元素</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'refine_scores') ? 'active' : ''; ?>">
                            <a href="/admin/refine_scores.php">材料分值</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'refine_combos') ? 'active' : ''; ?>">
                            <a href="/admin/refine_combos.php">组合系数</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'refine_tiers') ? 'active' : ''; ?>">
                            <a href="/admin/refine_tiers.php">档位区间</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'refine_attribute_bonuses') ? 'active' : ''; ?>">
                            <a href="/admin/refine_attribute_bonuses.php">属性加成</a>
                        </li>
                    </ul>
                </li>

                <!-- 游戏世界 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['scenes', 'continents', 'buildings', 'monsters', 'npcs', 'dialogues', 'quests', 'quest_workflow'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">世界</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage == 'continents') ? 'active' : ''; ?>">
                            <a href="/admin/continents.php">大陆管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'scenes') ? 'active' : ''; ?>">
                            <a href="/admin/scenes.php">场景管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'buildings') ? 'active' : ''; ?>">
                            <a href="/admin/buildings.php">建筑管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'monsters') ? 'active' : ''; ?>">
                            <a href="/admin/monsters.php">怪物管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'npcs') ? 'active' : ''; ?>">
                            <a href="/admin/npcs.php">NPC管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'dialogues') ? 'active' : ''; ?>">
                            <a href="/admin/dialogues.php">对话管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'quests') ? 'active' : ''; ?>">
                            <a href="/admin/quests.php">任务管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'quest_workflow') ? 'active' : ''; ?>">
                            <a href="/admin/quest_workflow.php">任务流程图</a>
                        </li>
                    </ul>
                </li>

                <!-- 玩家管理 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['player_inventory', 'player_equipment_refine', 'attribute_reset_config', 'warehouse_expansion', 'redemption_codes'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">玩家</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage == 'player_inventory') ? 'active' : ''; ?>">
                            <a href="/admin/player_inventory.php">玩家管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'player_equipment_refine') ? 'active' : ''; ?>">
                            <a href="/admin/player_equipment_refine.php">装备凝练</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'attribute_reset_config') ? 'active' : ''; ?>">
                            <a href="/admin/attribute_reset_config.php">属性重置</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'warehouse_expansion') ? 'active' : ''; ?>">
                            <a href="/admin/warehouse_expansion.php">仓库扩容</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'redemption_codes') ? 'active' : ''; ?>">
                            <a href="/admin/redemption_codes.php">兑换码</a>
                        </li>
                    </ul>
                </li>

                <!-- 日志监控 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['battle_logs', 'pvp_battle_logs', 'trade_logs', 'chat_logs', 'security_violations'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">日志</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage === 'battle_logs') ? 'active' : ''; ?>">
                            <a href="/admin/battle_logs.php">PVE战斗</a>
                        </li>
                        <li class="<?php echo ($currentPage === 'pvp_battle_logs') ? 'active' : ''; ?>">
                            <a href="/admin/pvp_battle_logs.php">PVP战斗</a>
                        </li>
                        <li class="<?php echo ($currentPage === 'trade_logs') ? 'active' : ''; ?>">
                            <a href="/admin/trade_logs.php">交易记录</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'chat_logs') ? 'active' : ''; ?>">
                            <a href="/admin/chat_logs.php">聊天记录</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'security_violations') ? 'active' : ''; ?>">
                            <a href="/admin/security_violations.php">安全监控</a>
                        </li>
                    </ul>
                </li>

                <!-- 系统管理 -->
                <li class="dropdown <?php echo (in_array($currentPage, ['announcements', 'balance_manager', 'scene_items_cleanup', 'server_logs', 'updatejs', 'change_password'])) ? 'active' : ''; ?>">
                    <a href="#" class="dropdown-toggle">系统</a>
                    <ul class="dropdown-menu">
                        <li class="<?php echo ($currentPage == 'announcements') ? 'active' : ''; ?>">
                            <a href="/admin/announcements.php">公告管理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'balance_manager') ? 'active' : ''; ?>">
                            <a href="/admin/balance_manager.php">数值平衡</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'scene_items_cleanup') ? 'active' : ''; ?>">
                            <a href="/admin/scene_items_cleanup.php">物品清理</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'server_logs') ? 'active' : ''; ?>">
                            <a href="/admin/server_logs.php">服务器日志</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'updatejs') ? 'active' : ''; ?>">
                            <a href="/js/updatejs.php">JS更新</a>
                        </li>
                        <li class="<?php echo ($currentPage == 'change_password') ? 'active' : ''; ?>">
                            <a href="/admin/change_password.php">修改密码</a>
                        </li>
                    </ul>
                </li>
            </ul>
        </nav>
        <div class="top-nav-user">
            <span id="session-timer" class="session-timer"></span>
            <a href="/admin/logout.php">退出登录</a>
        </div>
    </div>
    <div class="main-content">
        <main>
            <div class="content">
                <header class="content-header">
                    <h2><?= isset($pageTitle) ? htmlspecialchars($pageTitle) : '' ?></h2>
                </header> 