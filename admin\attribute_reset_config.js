// 全局变量
let buildings = [];
let items = [];
let currentBuildingId = null;
let deleteTargetId = null;
let searchTimeout = null;

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    loadBuildings();
    loadItems();
});

// 加载属性重修建筑列表
function loadBuildings() {
    fetch('api_attribute_reset_config.php?action=get_buildings')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                buildings = data.data;
                renderBuildings();
            } else {
                showStatusMessage('加载建筑列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 加载物品列表
function loadItems() {
    fetch('api_attribute_reset_config.php?action=get_items')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                items = data.data;
            } else {
                console.error('加载物品列表失败:', data.message);
            }
        })
        .catch(error => {
            console.error('加载物品列表出错:', error);
        });
}

// 渲染建筑列表
function renderBuildings() {
    const container = document.getElementById('buildings-container');

    if (buildings.length === 0) {
        container.innerHTML = `
            <div class="building-card">
                <h3>暂无属性重修建筑</h3>
                <p>请先在建筑管理中创建属性重修类型的建筑，并将其放置在场景中。</p>
            </div>
        `;
        return;
    }

    let html = '';

    buildings.forEach(building => {
        const isConfigured = building.config !== null;

        html += `
            <div class="building-card">
                <h3>${escapeHtml(building.building_name)}</h3>
                <div class="building-type">属性重修</div>
                <div class="building-description">
                    场景: ${escapeHtml(building.scene_name)} (ID: ${building.scene_id})
                </div>

                <div class="config-status ${isConfigured ? 'configured' : 'not-configured'}">
                    <span class="status-icon">${isConfigured ? '✅' : '❌'}</span>
                    <span>${isConfigured ? '已配置' : '未配置'}</span>
                </div>

                ${isConfigured ? `
                    <div class="building-scenes">
                        <div class="config-item">
                            <span class="config-label">重修物品:</span>
                            <span class="config-value">${escapeHtml(building.config.item_name)}</span>
                        </div>
                        <div class="config-item">
                            <span class="config-label">需要数量:</span>
                            <span class="config-value">${building.config.required_quantity}</span>
                        </div>
                    </div>
                ` : `
                    <div class="building-scenes">
                        <p style="color: #6c757d; font-style: italic;">该建筑尚未配置重修物品，玩家无法使用。</p>
                    </div>
                `}

                <div class="building-actions">
                    <button class="btn ${isConfigured ? 'btn-warning' : 'btn-primary'}"
                            onclick="showConfigModal(${building.scene_building_id}, ${isConfigured})">
                        ${isConfigured ? '修改配置' : '配置'}
                    </button>
                    ${isConfigured ? `
                        <button class="btn btn-danger" onclick="showDeleteModal(${building.scene_building_id})">
                            删除配置
                        </button>
                    ` : ''}
                </div>
            </div>
        `;
    });

    container.innerHTML = html;
}

// 显示配置模态框
function showConfigModal(sceneBuildingId, isEdit = false) {
    currentBuildingId = sceneBuildingId;
    const building = buildings.find(b => b.scene_building_id === sceneBuildingId);
    
    if (!building) {
        showStatusMessage('找不到建筑信息', 'error');
        return;
    }
    
    // 设置模态框标题
    document.getElementById('modal-title').textContent = 
        isEdit ? `修改配置 - ${building.building_name}` : `配置 - ${building.building_name}`;
    
    // 设置表单数据
    document.getElementById('scene-building-id').value = sceneBuildingId;
    
    if (isEdit && building.config) {
        // 编辑模式，填充现有数据
        document.getElementById('item-search').value = building.config.item_name;
        document.getElementById('selected-item-id').value = building.config.required_item_id;
        document.getElementById('required-quantity').value = building.config.required_quantity;
        
        // 显示已选择的物品
        showSelectedItem(building.config.required_item_id, building.config.item_name, building.config.item_description);
    } else {
        // 新建模式，清空表单
        document.getElementById('config-form').reset();
        document.getElementById('selected-item-id').value = '';
        hideSelectedItem();
    }
    
    document.getElementById('config-modal').style.display = 'flex';
}

// 隐藏配置模态框
function hideConfigModal() {
    document.getElementById('config-modal').style.display = 'none';
    currentBuildingId = null;
}

// 显示删除确认模态框
function showDeleteModal(sceneBuildingId) {
    deleteTargetId = sceneBuildingId;
    document.getElementById('delete-modal').style.display = 'flex';
}

// 隐藏删除确认模态框
function hideDeleteModal() {
    document.getElementById('delete-modal').style.display = 'none';
    deleteTargetId = null;
}

// 搜索物品
function searchItems(query) {
    clearTimeout(searchTimeout);
    
    if (query.length < 2) {
        hideSearchResults();
        return;
    }
    
    searchTimeout = setTimeout(() => {
        const filteredItems = items.filter(item => 
            item.name.toLowerCase().includes(query.toLowerCase()) ||
            (item.description && item.description.toLowerCase().includes(query.toLowerCase()))
        ).slice(0, 10); // 限制显示10个结果
        
        displaySearchResults(filteredItems);
    }, 300);
}

// 显示搜索结果
function displaySearchResults(results) {
    const container = document.getElementById('search-results');
    
    if (results.length === 0) {
        container.innerHTML = '<div class="search-result-item">未找到匹配的物品</div>';
    } else {
        let html = '';
        results.forEach(item => {
            html += `
                <div class="search-result-item" onclick="selectItem(${item.id}, '${escapeHtml(item.name)}', '${escapeHtml(item.description || '')}')">
                    <div class="item-name">${escapeHtml(item.name)}</div>
                    ${item.description ? `<div class="item-description">${escapeHtml(item.description)}</div>` : ''}
                </div>
            `;
        });
        container.innerHTML = html;
    }
    
    container.style.display = 'block';
}

// 选择物品
function selectItem(itemId, itemName, itemDescription) {
    document.getElementById('item-search').value = itemName;
    document.getElementById('selected-item-id').value = itemId;
    showSelectedItem(itemId, itemName, itemDescription);
    hideSearchResults();
}

// 显示已选择的物品
function showSelectedItem(itemId, itemName, itemDescription) {
    document.getElementById('selected-item-name').textContent = itemName;
    document.getElementById('selected-item-description').textContent = itemDescription || '无描述';
    document.getElementById('selected-item-display').style.display = 'block';
}

// 隐藏已选择的物品显示
function hideSelectedItem() {
    document.getElementById('selected-item-display').style.display = 'none';
}

// 显示搜索结果
function showSearchResults() {
    const container = document.getElementById('search-results');
    if (container.innerHTML.trim() !== '') {
        container.style.display = 'block';
    }
}

// 隐藏搜索结果
function hideSearchResults() {
    setTimeout(() => {
        document.getElementById('search-results').style.display = 'none';
    }, 200);
}

// 处理配置表单提交
function handleConfigSubmit(event) {
    event.preventDefault();
    
    const formData = new FormData(event.target);
    formData.append('action', 'save_config');
    
    // 验证必填字段
    if (!formData.get('required_item_id')) {
        showStatusMessage('请选择重修物品', 'error');
        return;
    }
    
    fetch('api_attribute_reset_config.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            hideConfigModal();
            loadBuildings(); // 重新加载建筑列表
        } else {
            showStatusMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 确认删除配置
function confirmDelete() {
    if (!deleteTargetId) return;
    
    const formData = new FormData();
    formData.append('action', 'delete_config');
    formData.append('scene_building_id', deleteTargetId);
    
    fetch('api_attribute_reset_config.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            hideDeleteModal();
            loadBuildings(); // 重新加载建筑列表
        } else {
            showStatusMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 显示状态消息
function showStatusMessage(message, type) {
    // 创建或获取状态消息容器
    let container = document.getElementById('status-message-container');
    if (!container) {
        container = document.createElement('div');
        container.id = 'status-message-container';
        document.body.appendChild(container);
    }

    // 创建消息元素
    const messageEl = document.createElement('div');
    messageEl.className = `status-message ${type}`;
    messageEl.textContent = message;

    // 添加到容器
    container.appendChild(messageEl);

    // 3秒后自动移除
    setTimeout(() => {
        messageEl.classList.add('fade-out');
        setTimeout(() => {
            if (messageEl.parentNode) {
                messageEl.parentNode.removeChild(messageEl);
            }
        }, 500);
    }, 3000);
}

// HTML转义函数
function escapeHtml(text) {
    if (typeof text !== 'string') return text;
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}
