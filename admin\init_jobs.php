<?php
// admin/init_jobs.php - 初始化职业数据
require_once '../config/Database.php';
require_once 'auth.php';

header('Content-Type: application/json');

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 检查jobs表是否存在
    $tables = $pdo->query("SHOW TABLES LIKE 'jobs'")->fetchAll();
    if (empty($tables)) {
        // 创建jobs表
        $pdo->exec("
            CREATE TABLE `jobs` (
                `id` int(11) NOT NULL AUTO_INCREMENT,
                `name` varchar(50) NOT NULL,
                `description` text,
                PRIMARY KEY (`id`)
            ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4
        ");
    }
    
    // 检查是否已有数据
    $job_count = $pdo->query("SELECT COUNT(*) FROM jobs")->fetchColumn();
    
    if ($job_count == 0) {
        // 插入基础职业数据
        $pdo->exec("
            INSERT INTO jobs (id, name, description) VALUES 
            (1, '新手', '初始职业，刚进入游戏的玩家默认职业'),
            (2, '战士', '近战物理职业，拥有高血量和防御力，擅长近身战斗'),
            (3, '法师', '远程魔法职业，拥有强大的魔法攻击力，但防御较低'),
            (4, '射手', '远程物理职业，拥有高敏捷和暴击率，擅长远程攻击')
        ");
        
        echo json_encode([
            'success' => true,
            'message' => '职业数据初始化成功',
            'data' => [
                'inserted_jobs' => 4
            ]
        ], JSON_UNESCAPED_UNICODE);
    } else {
        // 获取现有职业数据
        $jobs = $pdo->query("SELECT * FROM jobs ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);
        
        echo json_encode([
            'success' => true,
            'message' => '职业数据已存在',
            'data' => [
                'existing_jobs' => $jobs
            ]
        ], JSON_UNESCAPED_UNICODE);
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'message' => '初始化失败: ' . $e->getMessage()
    ], JSON_UNESCAPED_UNICODE);
}
?>
