<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>正在建设 - 敬请期待</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        /* 背景装饰 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: 
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 119, 198, 0.2) 0%, transparent 50%);
            z-index: -1;
        }

        .container {
            text-align: center;
            z-index: 1;
        }

        .domain-text {
            font-size: 4rem;
            font-weight: 700;
            margin-bottom: 2rem;
            letter-spacing: 0.1em;
        }

        .letter {
            display: inline-block;
            color: #ffffff;
            text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            animation: bounce 2s infinite;
            transform-origin: center bottom;
        }

        .letter:nth-child(1) { animation-delay: 0s; }
        .letter:nth-child(2) { animation-delay: 0.1s; }
        .letter:nth-child(3) { animation-delay: 0.2s; }
        .letter:nth-child(4) { animation-delay: 0.3s; }
        .letter:nth-child(5) { animation-delay: 0.4s; }
        .letter:nth-child(6) { animation-delay: 0.5s; }
        .letter:nth-child(7) { animation-delay: 0.6s; }
        .letter:nth-child(8) { animation-delay: 0.7s; }
        .letter:nth-child(9) { animation-delay: 0.8s; }
        .letter:nth-child(10) { animation-delay: 0.9s; }
        .letter:nth-child(11) { animation-delay: 1s; }
        .letter:nth-child(12) { animation-delay: 1.1s; }

        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% {
                transform: translateY(0) scale(1);
            }
            40% {
                transform: translateY(-20px) scale(1.1);
            }
            60% {
                transform: translateY(-10px) scale(1.05);
            }
        }

        .subtitle {
            font-size: 1.5rem;
            color: rgba(255, 255, 255, 0.9);
            font-weight: 300;
            letter-spacing: 0.2em;
            margin-top: 1rem;
            animation: fadeInUp 1s ease-out 1.5s both;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .loading-dots {
            margin-top: 2rem;
            animation: fadeInUp 1s ease-out 2s both;
        }

        .dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.7);
            margin: 0 4px;
            animation: pulse 1.5s infinite ease-in-out;
        }

        .dot:nth-child(1) { animation-delay: 0s; }
        .dot:nth-child(2) { animation-delay: 0.3s; }
        .dot:nth-child(3) { animation-delay: 0.6s; }

        @keyframes pulse {
            0%, 80%, 100% {
                transform: scale(0.8);
                opacity: 0.5;
            }
            40% {
                transform: scale(1.2);
                opacity: 1;
            }
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .domain-text {
                font-size: 2.5rem;
            }
            .subtitle {
                font-size: 1.2rem;
            }
        }

        @media (max-width: 480px) {
            .domain-text {
                font-size: 2rem;
            }
            .subtitle {
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="domain-text">
            <span class="letter">x</span>
            <span class="letter">m</span>
            <span class="letter">u</span>
            <span class="letter">d</span>
            <span class="letter">.</span>
            <span class="letter">o</span>
            <span class="letter">n</span>
            <span class="letter">l</span>
            <span class="letter">i</span>
            <span class="letter">n</span>
            <span class="letter">e</span>
        </div>
        
        <div class="subtitle">敬请期待</div>
        
        <div class="loading-dots">
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
        </div>
    </div>
</body>
</html>
