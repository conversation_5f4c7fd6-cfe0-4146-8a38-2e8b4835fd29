/**
 * 全局Toast通知系统
 * 统一处理所有页面的Toast显示，确保样式和行为一致
 */

// 确保只定义一次全局showToast函数
if (typeof window.globalShowToast === 'undefined') {
    
    /**
     * 显示Toast通知
     * @param {string} message 消息内容
     * @param {string} type 消息类型 (success, error, warning, info)
     * @param {number} duration 显示时长（毫秒）
     */
    window.globalShowToast = function(message, type = 'success', duration = null) {
        // 获取或创建Toast元素
        let toast = document.getElementById('toast');
        if (!toast) {
            toast = document.createElement('div');
            toast.id = 'toast';
            toast.className = 'toast';
            document.body.appendChild(toast);
        }
        
        // 处理消息内容
        let displayMessage = message || '';
        
        // 限制消息长度，避免Toast过大
        if (displayMessage.length > 100) {
            displayMessage = displayMessage.substring(0, 97) + '...';
        }
        
        // 移除换行符，避免布局问题
        displayMessage = displayMessage.replace(/\n/g, ' ');
        
        // 移除多余的空格
        displayMessage = displayMessage.replace(/\s+/g, ' ').trim();
        
        // 设置Toast内容和样式
        toast.textContent = displayMessage;
        toast.className = `toast ${type}`;
        
        // 清除之前的定时器
        if (toast.hideTimer) {
            clearTimeout(toast.hideTimer);
        }
        
        // 显示Toast
        toast.classList.add('show');
        
        // 根据消息长度和类型调整显示时间
        if (duration === null) {
            if (type === 'error') {
                duration = Math.max(4000, displayMessage.length * 50); // 错误消息显示更久
            } else if (displayMessage.length > 50) {
                duration = 4000;
            } else {
                duration = 3000;
            }
        }
        
        // 设置自动隐藏
        toast.hideTimer = setTimeout(() => {
            toast.classList.remove('show');
            toast.hideTimer = null;
        }, duration);
        
        // 返回Toast元素，便于进一步操作
        return toast;
    };
    
    /**
     * 立即隐藏Toast
     */
    window.hideToast = function() {
        const toast = document.getElementById('toast');
        if (toast) {
            toast.classList.remove('show');
            if (toast.hideTimer) {
                clearTimeout(toast.hideTimer);
                toast.hideTimer = null;
            }
        }
    };
    
    /**
     * 兼容性函数：如果页面没有定义showToast，则使用全局版本
     */
    window.ensureShowToast = function() {
        if (typeof window.showToast === 'undefined') {
            window.showToast = window.globalShowToast;
        }
    };
    
    // 页面加载完成后确保showToast函数可用
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', window.ensureShowToast);
    } else {
        window.ensureShowToast();
    }
    
    // 为了向后兼容，也提供一个全局的showToast函数
    if (typeof window.showToast === 'undefined') {
        window.showToast = window.globalShowToast;
    }
}

// 导出函数供其他脚本使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        showToast: window.globalShowToast,
        hideToast: window.hideToast
    };
}
