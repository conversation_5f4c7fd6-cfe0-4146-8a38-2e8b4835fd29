<?php
// admin/api_redemption_codes.php
session_start();

// 检查管理员登录状态
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    http_response_code(403);
    echo json_encode(['success' => false, 'message' => '未授权访问']);
    exit;
}

require_once '../config/Database.php';

header('Content-Type: application/json; charset=utf-8');

$action = $_GET['action'] ?? $_POST['action'] ?? '';

try {
    $pdo = Database::getInstance()->getConnection();
    
    switch ($action) {
        case 'get_item_templates':
            getItemTemplates($pdo);
            break;
            
        case 'create':
            createRedemptionCode($pdo);
            break;
            
        case 'batch_create':
            batchCreateRedemptionCodes($pdo);
            break;
            
        case 'get_details':
            getCodeDetails($pdo);
            break;
            
        case 'get_usage_history':
            getUsageHistory($pdo);
            break;
            
        case 'toggle_status':
            toggleCodeStatus($pdo);
            break;
            
        default:
            throw new Exception('无效的操作');
    }
    
} catch (Exception $e) {
    echo json_encode(['success' => false, 'message' => $e->getMessage()]);
}

// 获取物品模板列表
function getItemTemplates($pdo) {
    $stmt = $pdo->query("
        SELECT id, name, category, stackable, max_stack 
        FROM item_templates 
        ORDER BY category, name
    ");
    $items = $stmt->fetchAll();
    
    echo json_encode(['success' => true, 'items' => $items]);
}

// 生成随机兑换码
function generateRedemptionCode() {
    $characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    $code = '';
    for ($i = 0; $i < 6; $i++) {
        $code .= $characters[random_int(0, strlen($characters) - 1)];
    }
    return $code;
}

// 检查兑换码是否已存在
function isCodeExists($pdo, $code) {
    $stmt = $pdo->prepare("SELECT COUNT(*) FROM redemption_codes WHERE code = ?");
    $stmt->execute([$code]);
    return $stmt->fetchColumn() > 0;
}

// 创建单个兑换码
function createRedemptionCode($pdo) {
    $name = trim($_POST['name'] ?? '');
    $totalUsageLimit = intval($_POST['total_usage_limit'] ?? 1);
    $playerUsageLimit = intval($_POST['player_usage_limit'] ?? 1);
    $expiresAt = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
    $items = $_POST['items'] ?? [];
    
    // 验证输入
    if (empty($name)) {
        throw new Exception('兑换码名称不能为空');
    }
    
    if ($totalUsageLimit < 1 || $playerUsageLimit < 1) {
        throw new Exception('使用次数限制必须大于0');
    }
    
    if (empty($items)) {
        throw new Exception('请至少配置一个兑换物品');
    }
    
    // 验证物品配置
    foreach ($items as $item) {
        if (empty($item['item_template_id']) || intval($item['quantity']) < 1) {
            throw new Exception('物品配置无效');
        }
    }
    
    $pdo->beginTransaction();
    
    try {
        // 生成唯一的兑换码
        do {
            $code = generateRedemptionCode();
        } while (isCodeExists($pdo, $code));
        
        // 插入兑换码记录
        $stmt = $pdo->prepare("
            INSERT INTO redemption_codes (code, name, total_usage_limit, player_usage_limit, expires_at, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        $stmt->execute([$code, $name, $totalUsageLimit, $playerUsageLimit, $expiresAt, $_SESSION['admin_id'] ?? null]);
        
        $codeId = $pdo->lastInsertId();
        
        // 插入物品配置
        $itemStmt = $pdo->prepare("
            INSERT INTO redemption_code_items (redemption_code_id, item_template_id, quantity, is_bound, sort_order) 
            VALUES (?, ?, ?, ?, ?)
        ");
        
        foreach ($items as $index => $item) {
            $itemStmt->execute([
                $codeId,
                intval($item['item_template_id']),
                intval($item['quantity']),
                isset($item['is_bound']) ? 1 : 0,
                $index
            ]);
        }
        
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => '兑换码创建成功', 'code' => $code]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// 批量创建兑换码
function batchCreateRedemptionCodes($pdo) {
    $namePrefix = trim($_POST['name_prefix'] ?? '');
    $count = intval($_POST['count'] ?? 1);
    $totalUsageLimit = intval($_POST['total_usage_limit'] ?? 1);
    $playerUsageLimit = intval($_POST['player_usage_limit'] ?? 1);
    $expiresAt = !empty($_POST['expires_at']) ? $_POST['expires_at'] : null;
    
    // 验证输入
    if (empty($namePrefix)) {
        throw new Exception('名称前缀不能为空');
    }
    
    if ($count < 1 || $count > 1000) {
        throw new Exception('生成数量必须在1-1000之间');
    }
    
    if ($totalUsageLimit < 1 || $playerUsageLimit < 1) {
        throw new Exception('使用次数限制必须大于0');
    }
    
    $pdo->beginTransaction();
    
    try {
        $stmt = $pdo->prepare("
            INSERT INTO redemption_codes (code, name, total_usage_limit, player_usage_limit, expires_at, created_by) 
            VALUES (?, ?, ?, ?, ?, ?)
        ");
        
        $createdCount = 0;
        for ($i = 1; $i <= $count; $i++) {
            // 生成唯一的兑换码
            do {
                $code = generateRedemptionCode();
            } while (isCodeExists($pdo, $code));
            
            $name = $namePrefix . '-' . str_pad($i, 3, '0', STR_PAD_LEFT);
            
            $stmt->execute([$code, $name, $totalUsageLimit, $playerUsageLimit, $expiresAt, $_SESSION['admin_id'] ?? null]);
            $createdCount++;
        }
        
        $pdo->commit();
        
        echo json_encode(['success' => true, 'message' => '批量生成成功', 'count' => $createdCount]);
        
    } catch (Exception $e) {
        $pdo->rollBack();
        throw $e;
    }
}

// 获取兑换码详情
function getCodeDetails($pdo) {
    $id = intval($_GET['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('无效的兑换码ID');
    }
    
    // 获取兑换码基本信息
    $stmt = $pdo->prepare("
        SELECT rc.*, 
               CASE 
                   WHEN rc.is_active = 0 THEN 'inactive'
                   WHEN rc.expires_at IS NOT NULL AND rc.expires_at <= NOW() THEN 'expired'
                   WHEN rc.current_usage_count >= rc.total_usage_limit THEN 'used_up'
                   ELSE 'active'
               END as status
        FROM redemption_codes rc 
        WHERE rc.id = ?
    ");
    $stmt->execute([$id]);
    $code = $stmt->fetch();
    
    if (!$code) {
        throw new Exception('兑换码不存在');
    }
    
    // 获取物品配置
    $itemStmt = $pdo->prepare("
        SELECT rci.*, it.name as item_name, it.category
        FROM redemption_code_items rci
        JOIN item_templates it ON rci.item_template_id = it.id
        WHERE rci.redemption_code_id = ?
        ORDER BY rci.sort_order
    ");
    $itemStmt->execute([$id]);
    $items = $itemStmt->fetchAll();
    
    $code['items'] = $items;
    
    echo json_encode(['success' => true, 'code' => $code]);
}

// 获取使用记录
function getUsageHistory($pdo) {
    $id = intval($_GET['id'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('无效的兑换码ID');
    }
    
    // 获取兑换码基本信息
    $codeStmt = $pdo->prepare("SELECT code, name, current_usage_count, total_usage_limit FROM redemption_codes WHERE id = ?");
    $codeStmt->execute([$id]);
    $codeInfo = $codeStmt->fetch();
    
    if (!$codeInfo) {
        throw new Exception('兑换码不存在');
    }
    
    // 获取使用记录
    $stmt = $pdo->prepare("
        SELECT rcu.used_at, rcu.ip_address, a.username
        FROM redemption_code_usage rcu
        JOIN accounts a ON rcu.player_id = a.id
        WHERE rcu.redemption_code_id = ?
        ORDER BY rcu.used_at DESC
    ");
    $stmt->execute([$id]);
    $usageHistory = $stmt->fetchAll();
    
    echo json_encode(['success' => true, 'usage_history' => $usageHistory, 'code_info' => $codeInfo]);
}

// 切换兑换码状态
function toggleCodeStatus($pdo) {
    $input = json_decode(file_get_contents('php://input'), true);
    $id = intval($input['id'] ?? 0);
    $isActive = intval($input['is_active'] ?? 0);
    
    if ($id <= 0) {
        throw new Exception('无效的兑换码ID');
    }
    
    $stmt = $pdo->prepare("UPDATE redemption_codes SET is_active = ? WHERE id = ?");
    $stmt->execute([$isActive, $id]);
    
    if ($stmt->rowCount() === 0) {
        throw new Exception('兑换码不存在或状态未改变');
    }
    
    echo json_encode(['success' => true, 'message' => '状态更新成功']);
}
?>
