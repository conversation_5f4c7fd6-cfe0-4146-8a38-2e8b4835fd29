/**
 * PVP战斗管理器
 * 处理玩家间PVP战斗的前端逻辑
 */
class PvpManager {
    constructor(gameClient) {
        this.gameClient = gameClient;
        this.pvpBattleActive = false;
        this.currentBattleState = null;
        this.pendingChallenges = [];
        this.currentIntention = null;
        this.actionQueue = [];
        this.atbStatus = null;
        this.activeView = 'main'; // 'main', 'battle', 'result'
        this.battleSkillsLoaded = false;  // 标记技能列表尚未加载
        this.battleLogExpanded = false;  // 战斗日志展开状态

        // 移除事件绑定，改为完全使用内联方式
        // this.bindEvents();
    }
    
    /**
     * 绑定PVP相关DOM事件
     * 注意：此方法已不再使用，保留以便参考
     */
    bindEvents() {
        // 在初始化时创建监听器
        document.addEventListener('click', (e) => {
            // 挑战玩家按钮
            if (e.target.classList.contains('pvp-challenge-btn')) {
                const playerId = e.target.dataset.playerId;
                if (playerId) {
                    this.sendChallengeRequest(playerId);
                }
            }
            
            // 接受挑战按钮
            if (e.target.classList.contains('pvp-accept-btn')) {
                const challengeId = e.target.dataset.challengeId;
                if (challengeId) {
                    this.acceptChallenge(challengeId);
                }
            }
            
            // 拒绝挑战按钮
            if (e.target.classList.contains('pvp-decline-btn')) {
                const challengeId = e.target.dataset.challengeId;
                if (challengeId) {
                    this.declineChallenge(challengeId);
                }
            }
        });
    }
    
    /**
     * 发送PVP挑战请求
     * @param {string} targetPlayerId 目标玩家ID
     */
    sendChallengeRequest(targetPlayerId) {
        this.gameClient.sendMessage(MessageProtocol.C2S_PVP_CHALLENGE, {
            target_player_id: targetPlayerId
        });
        
        // 显示发送挑战的提示
        this.gameClient.addLog(`你向玩家发起了PVP挑战，等待对方回应...`);
        
        // 隐藏玩家详情页面，返回主界面
        this.gameClient.hidePlayerDetailView();
    }
    
    /**
     * 接受PVP挑战
     * @param {string} challengeId 挑战ID
     */
    acceptChallenge(challengeId) {
        this.gameClient.sendMessage(MessageProtocol.C2S_PVP_ACCEPT_CHALLENGE, {
            challenge_id: challengeId
        });
        
        // 移除此挑战通知
        this.removeChallengeNotification(challengeId);
    }
    
    /**
     * 拒绝PVP挑战
     * @param {string} challengeId 挑战ID
     */
    declineChallenge(challengeId) {
        this.gameClient.sendMessage(MessageProtocol.C2S_PVP_DECLINE_CHALLENGE, {
            challenge_id: challengeId
        });
        
        // 移除此挑战通知
        this.removeChallengeNotification(challengeId);
    }
    
    /**
     * 移除挑战通知UI
     * @param {string} challengeId 挑战ID
     */
    removeChallengeNotification(challengeId) {
        const notification = document.getElementById(`pvp-challenge-${challengeId}`);
        if (notification) {
            notification.remove();
        }
        
        // 从待处理挑战列表中移除
        this.pendingChallenges = this.pendingChallenges.filter(
            challenge => challenge.id !== challengeId
        );
    }
    
    /**
     * 处理收到的PVP挑战
     * @param {Object} payload 挑战数据
     */
    handlePvpChallenge(payload) {
        const { challenge_id, challenger_id, challenger_name, expiry_time } = payload;
        
        // 保存到待处理挑战列表
        this.pendingChallenges.push({
            id: challenge_id,
            challengerId: challenger_id,
            challengerName: challenger_name,
            expiryTime: expiry_time
        });
        
        // 创建挑战通知UI
        this.createChallengeNotification(payload);
    }
    
    /**
     * 创建挑战通知UI
     * @param {Object} challengeData 挑战数据
     */
    createChallengeNotification(challengeData) {
        const { challenge_id, challenger_name } = challengeData;
        
        // 创建通知容器
        const notification = document.createElement('div');
        notification.id = `pvp-challenge-${challenge_id}`;
        notification.className = 'pvp-challenge-notification';
        
        // 设置通知内容
        notification.innerHTML = `
            <div class="pvp-challenge-header">收到PVP挑战!</div>
            <div class="pvp-challenge-content">
                <p>玩家 <strong>${challenger_name}</strong> 向你发起了决斗挑战!</p>
                <div class="pvp-challenge-actions">
                    <button class="btn-primary pvp-accept-btn" data-challenge-id="${challenge_id}">接受</button>
                    <button class="btn pvp-decline-btn" data-challenge-id="${challenge_id}">拒绝</button>
                </div>
            </div>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 直接绑定按钮的点击事件，而不是依赖事件委托
        const acceptBtn = notification.querySelector('.pvp-accept-btn');
        const declineBtn = notification.querySelector('.pvp-decline-btn');
        
        if (acceptBtn) {
            acceptBtn.onclick = () => this.acceptChallenge(challenge_id);
        }
        
        if (declineBtn) {
            declineBtn.onclick = () => this.declineChallenge(challenge_id);
        }
        
        // 设置自动过期
        setTimeout(() => {
            if (document.getElementById(`pvp-challenge-${challenge_id}`)) {
                this.removeChallengeNotification(challenge_id);
                this.gameClient.addLog(`来自 ${challenger_name} 的PVP挑战已过期。`);
            }
        }, 60000); // 1分钟后自动过期
    }
    
    /**
     * 处理PVP战斗开始
     * @param {Object} payload 战斗数据
     */
    handlePvpBattleStarted(payload) {
        const { battle_id, challenger, defender } = payload;
        
        // 保存当前战斗状态
        this.currentBattleState = payload;
        this.pvpBattleActive = true;
        this.battleSkillsLoaded = false;  // 标记技能列表尚未加载
        
        // 先隐藏其他视图
        this.gameClient.hideAllViews();
        
        // 显示战斗界面
        this.showPvpBattleView();
        
        // 更新战斗标题为"XX vs XX"格式
        const battleTitleEl = document.getElementById('pvpBattleTitle');
        if (battleTitleEl) {
            battleTitleEl.textContent = `${challenger.username} vs ${defender.username}`;
        }
        
        // 重置战斗意图显示区域
        const intentionDisplay = document.getElementById('pvpBattleIntentionDisplay');
        if (intentionDisplay) {
            intentionDisplay.textContent = '战斗准备中...';
            intentionDisplay.classList.remove('battle-ended'); // 移除战斗结束样式
        }
        
        // 清空之前的战斗日志，以免不同场战斗的日志混在一起
        const battleLogContainer = document.getElementById('pvpBattleLog');
        if (battleLogContainer) {
            battleLogContainer.innerHTML = '';
            // 初始化为收起状态
            this.battleLogExpanded = false;
            battleLogContainer.classList.remove('expanded');
            battleLogContainer.classList.add('collapsed');
        }
        
        // 更新战斗UI - 基本显示
        this.updatePvpBattleView(challenger, defender);
        
        // 添加分隔线和战斗开始日志
        // this.addPvpBattleLog(`─────────── 战斗开始 ────────────`);
        this.addPvpBattleLog(`PVP战斗开始！${challenger.username} VS ${defender.username}`);
        
        // 如果是强制攻击模式，添加特殊提示
        if (this.currentBattleState.is_direct_attack) {
            this.addPvpBattleLog(`⚠️ 警告：这是强制PVP模式，无法投降，失败方将掉落物品和金币！⚠️`);
        }
        
        // 在战斗开始时请求一次技能列表，避免每次状态更新都请求
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_BATTLE_SKILLS, {
            battle_type: 'pvp',
            battle_id: battle_id
        });
        
        // 初始化一个默认的攻击意图
        this.currentIntention = { action: 'attack' };
    }
    
    /**
     * 显示PVP战斗界面
     */
    showPvpBattleView() {
        // 隐藏其他视图
        document.getElementById('main-view').style.display = 'none';
        
        // 如果还没有PVP战斗视图，则创建
        if (!document.getElementById('pvpBattleView')) {
            this.createPvpBattleView();
        }
        
        // 显示PVP战斗视图
        document.getElementById('pvpBattleView').style.display = 'block';
        this.activeView = 'battle';
    }
    
    /**
     * 创建PVP战斗视图
     */
    createPvpBattleView() {
        const pvpBattleView = document.createElement('div');
        pvpBattleView.id = 'pvpBattleView';
        pvpBattleView.className = 'view';
        pvpBattleView.style.display = 'none';
        
        pvpBattleView.innerHTML = `
            <h2 id="pvpBattleTitle">PVP战斗</h2>
            <div class="pvp-battle-container">
                <div id="pvpChallengerInfo" class="pvp-player-info opponent-info"></div>
                <div id="pvpBattleIntentionDisplay" class="battle-intention">战斗准备中...</div>
                <div id="pvpBattleActionsContainer" class="battle-actions"></div>
                <div id="pvpDefenderInfo" class="pvp-player-info local-info"></div>
                <div id="pvpBattleStatusContainer" class="battle-status">
                    <div id="pvpRoundCounter" class="round-counter"></div>
                    <div id="pvpTimerBar" class="timer-bar"></div>
                </div>
            </div>
            <div id="pvpBattleLogContainer" class="battle-log-container">
                <div class="battle-log-header">
                    <h4>战斗日志</h4>
                    <button id="pvpBattleLogToggle" class="battle-log-toggle-btn collapsed" onclick="game.pvpManager.toggleBattleLog()">
                        <span class="toggle-icon">▶</span>
                        <span class="toggle-text">展开</span>
                    </button>
                </div>
                <div id="pvpBattleLog" class="battle-log collapsed"></div>
            </div>
            <div id="pvpBattleEndActions" class="battle-end-actions" style="display: none;">
                <p id="pvpBattleEndMessage" class="battle-end-message"></p>
                <button class="btn-primary" onclick="game.pvpManager.returnToSceneFromPvpBattle()">返回场景</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(pvpBattleView);
    }
    
    /**
     * 更新PVP战斗视图
     * @param {Object} challenger 挑战者数据
     * @param {Object} defender 被挑战者数据
     * @param {boolean} isBattleOver 战斗是否结束
     * @param {string} reason 结束原因
     */
    updatePvpBattleView(challenger, defender, isBattleOver = false, reason = '') {
        const localPlayerId = this.gameClient.currentPlayer.id;
        
        // 确定谁是对手，谁是本地玩家
        const opponent = (challenger.id == localPlayerId) ? defender : challenger;
        const localPlayer = (challenger.id == localPlayerId) ? challenger : defender;

        // 获取将要放置对手和本地玩家信息的DOM元素
        // 对手信息始终放在'pvpChallengerInfo'（上方），本地玩家信息放在'pvpDefenderInfo'（下方）
        const opponentInfoEl = document.getElementById('pvpChallengerInfo');
        const localPlayerInfoEl = document.getElementById('pvpDefenderInfo');

        if (opponentInfoEl) {
            opponentInfoEl.innerHTML = this.generatePlayerInfoHtml(opponent, 'opponent');
        }
        
        if (localPlayerInfoEl) {
            localPlayerInfoEl.innerHTML = this.generatePlayerInfoHtml(localPlayer, 'local');
        }
        
        // 更新回合计数
        const roundCounterEl = document.getElementById('pvpRoundCounter');
        if (roundCounterEl && this.atbStatus) {
            roundCounterEl.innerHTML = `回合: ${this.atbStatus.round_count || 0}/${this.atbStatus.max_rounds || 30}`;
        }
        
        // 获取战斗意图显示区域
        const intentionDisplay = document.getElementById('pvpBattleIntentionDisplay');
        
        // 处理战斗结束
        const actionsContainer = document.getElementById('pvpBattleActionsContainer');
        if (isBattleOver) {
            // 隐藏战斗结束按钮区域，因为我们现在直接在操作区域显示结果
            document.getElementById('pvpBattleEndActions').style.display = 'none';
            
            // 确定战斗结果和玩家是否获胜
            let resultClass = 'battle-result-draw';
            let resultText = '战斗平局';
            let stampClass = 'battle-stamp-draw';
            let stampText = '平局';
            
            if (this.currentBattleState.winner !== 'draw') {
                const isWinner = 
                    (this.currentBattleState.winner === 'challenger' && challenger.id === localPlayerId) || 
                    (this.currentBattleState.winner === 'defender' && defender.id === localPlayerId);
                
                resultClass = isWinner ? 'battle-result-win' : 'battle-result-lose';
                resultText = isWinner ? '战斗胜利' : '战斗失败';
                stampClass = isWinner ? 'battle-stamp-win' : 'battle-stamp-lose';
                stampText = isWinner ? '胜利' : '失败';
            }
            
            // 在操作区域显示战斗结果
            if (actionsContainer) {
                actionsContainer.innerHTML = `
                    <div class="battle-result-container">
                        <div class="battle-result ${resultClass}">
                            <div class="battle-result-text">${resultText}</div>
                            <div class="battle-result-reason">${reason}</div>
                            <div class="battle-stamp ${stampClass}">${stampText}</div>
                        </div>
                        <button class="btn-primary battle-return-btn" onclick="game.pvpManager.returnToSceneFromPvpBattle()">返回场景</button>
                    </div>
                `;
            }
            
            // 更新战斗意图显示区域
            if (intentionDisplay) {
                intentionDisplay.textContent = '战斗已结束';
                intentionDisplay.classList.add('battle-ended');
            }
        } else {
            // 非战斗结束状态，重置战斗意图显示区域
            if (intentionDisplay) {
                intentionDisplay.classList.remove('battle-ended');
                // 如果有当前意图，则显示当前意图；否则显示默认文本
                if (this.currentIntention && this.currentIntention.action) {
                    this.updateIntentionDisplay();
                } else {
                    intentionDisplay.textContent = '战斗准备中...';
                }
            }
            
            if (!this.battleSkillsLoaded) {
                // 只有在战斗开始时初始化按钮，避免每次更新都重新请求技能列表
                // 添加基本战斗按钮
                if (actionsContainer) {
                    // 清空当前按钮
                    actionsContainer.innerHTML = '';
                    
                    // 添加基本战斗按钮
                    actionsContainer.innerHTML = `
                        <button class="battle-action-btn" data-action="attack">攻击</button>
                        <button class="battle-action-btn" data-action="use_hp_potion">回血</button>
                        <button class="battle-action-btn" data-action="use_mp_potion">回蓝</button>
                        ${!this.currentBattleState.is_direct_attack ? '<button class="battle-action-btn" data-action="surrender">投降</button>' : ''}
                    `;
                    
                    // 绑定战斗按钮点击事件
                    const buttons = actionsContainer.querySelectorAll('.battle-action-btn');
                    buttons.forEach(button => {
                        button.addEventListener('click', (e) => {
                            const action = e.target.dataset.action;
                            this.setIntention({ action });
                        });
                    });
                }
            }
        }
        
        // 更新技能按钮状态（禁用冷却中和吟唱中的技能）
        this.updateSkillButtonsState();
    }
    
    /**
     * 生成玩家信息HTML
     * @param {Object} player 玩家数据
     * @param {string} role 角色（challenger或defender）
     * @returns {string} 玩家信息HTML
     */
    generatePlayerInfoHtml(player, role) {
        if (!player) return '';
        
        const attrs = player.attributes;
        const hpPercent = (attrs.max_hp > 0) ? Math.floor((attrs.hp / attrs.max_hp) * 100) : 0;
        const mpPercent = (attrs.max_mp > 0) ? Math.floor((attrs.mp / attrs.max_mp) * 100) : 0;
        
        // 玩家状态样式（死亡为灰色）
        const isDead = attrs.hp <= 0;
        let nameStyle = '';
        if (isDead) {
            nameStyle = 'color: #6c8095; font-style: italic;'; // 死亡玩家使用灰色斜体
        }
        
        // 生成ATB条
        let atbHtml = '';
        if (this.atbStatus) {
            // 正确判断当前玩家是挑战者还是防御者，以获取正确的ATB值
            const isChallenger = player.id == this.currentBattleState.challenger.id;
            const atbValue = isChallenger ? this.atbStatus.challengerATB : this.atbStatus.defenderATB;
            const atbMax = this.atbStatus.atbMax || 500;
            
            // 直接构建ATB条HTML，避免使用可能不兼容的gameClient方法
            atbHtml = `
                <div class="atb-bar-container">
                    <div class="atb-bar-label">行动:</div>
                    <div class="atb-bar">
                        <div class="atb-bar-fill" style="width: ${Math.floor((atbValue / atbMax) * 100)}%"></div>
                    </div>
                </div>
            `;
        }
        
        // 使用和PVE战斗相同的HP/MP条生成方法
        const hpBarId = role === 'opponent' ? 'opponent-hp-bar' : 'local-hp-bar';
        const mpBarId = role === 'opponent' ? 'opponent-mp-bar' : 'local-mp-bar';
        
        // 添加敌我标识标签
        const roleTagHtml = role === 'opponent' ? 
            '<div class="player-role-tag opponent-tag">对手</div>' : 
            '<div class="player-role-tag local-tag">我方</div>';
        
        return `
            ${roleTagHtml}
            <p style="margin-bottom:2px; ${nameStyle}"><b>${player.username}</b> (Lv.${player.level})</p>
            ${this.gameClient.generateStatBarHtml(attrs.hp, attrs.max_hp, 'hp', hpBarId)}
            ${attrs.max_mp > 0 ? this.gameClient.generateStatBarHtml(attrs.mp, attrs.max_mp, 'mp', mpBarId) : ''}
            ${atbHtml}
        `;
    }
    
    /**
     * 处理PVP战斗技能列表
     * @param {Object} payload 技能数据
     */
    handleBattleSkillsList(payload) {
        console.log('PvpManager收到战斗技能列表:', payload);
        const skillsContainer = document.getElementById('pvpBattleActionsContainer');
        if (!skillsContainer) {
            console.error('找不到pvpBattleActionsContainer元素');
            return;
        }
    
        // 移除任何现有的技能按钮和它们的换行符，以防止重复
        skillsContainer.querySelectorAll('.skill-btn, .skills-line-break').forEach(el => el.remove());

        // 如果有新技能要显示，在它们前面添加一个换行元素以确保它们从新行开始
        if (payload.skills && payload.skills.length > 0) {
            const lineBreak = document.createElement('div');
            lineBreak.className = 'skills-line-break';
            lineBreak.style.flexBasis = '100%';
            lineBreak.style.height = '0px'; // 在行之间创建一个小间隔
            skillsContainer.appendChild(lineBreak);

            // 创建技能容器
            const skillsHeader = document.createElement('div');
            skillsHeader.className = 'pvp-skills-container';
            // skillsHeader.innerHTML = '<div class="skills-header">可用技能:</div>';
            skillsContainer.appendChild(skillsHeader);

            // 添加新技能按钮
            payload.skills.forEach(skill => {
                const button = document.createElement('button');
                button.className = 'battle-action-btn skill-btn';
                button.dataset.skillId = skill.skill_template_id;
                // 技能名称只显示前两个字符
                const shortSkillName = skill.name.length > 2 ? skill.name.substring(0, 2) : skill.name;
                button.textContent = shortSkillName;
                
                // 监听点击事件
                button.addEventListener('click', () => {
                    const mpCost = skill.current_mp_cost || skill.mp_cost || 0;
                    const localPlayerId = this.gameClient.currentPlayer.id;
                    let player;
                    
                    if (this.currentBattleState.challenger.id === localPlayerId) {
                        player = this.currentBattleState.challenger;
                    } else {
                        player = this.currentBattleState.defender;
                    }
                    
                    if (player.attributes.mp < mpCost) {
                        this.addPvpBattleLog(`法力不足，无法施放【${skill.name}】！`);
                        return;
                    }
                    
                    this.setIntention({
                        action: 'skill',
                        skill_id: skill.skill_template_id,
                        skill_name: skill.name
                    });
                });
                
                skillsHeader.appendChild(button);
            });
        }
        
        // 标记技能列表已加载，避免重复请求
        this.battleSkillsLoaded = true;
        
        // 立即更新技能按钮状态
        this.updateSkillButtonsState();

        // 更新常规按钮状态
        this.updateBattleActionButtons();
    }
    
    /**
     * 更新技能按钮状态（禁用冷却中和吟唱中的技能）
     */
    updateSkillButtonsState() {
        if (!this.currentBattleState) return;
        
        const localPlayerId = this.gameClient.currentPlayer.id;
        let player;
        
        // 确定当前玩家
        if (this.currentBattleState.challenger.id === localPlayerId) {
            player = this.currentBattleState.challenger;
        } else if (this.currentBattleState.defender.id === localPlayerId) {
            player = this.currentBattleState.defender;
        } else {
            return; // 不是战斗参与者
        }
        
        // 检查是否是当前玩家的回合
        const isMyTurn = this.currentBattleState.actor_id === localPlayerId;
        
        // 检查玩家是否在吟唱中
        const isPlayerCasting = player && player.casting_info;

        // 检查玩家是否被沉默
        const isPlayerSilenced = this.isPlayerSilenced(this.currentBattleState, localPlayerId);

        // 检查玩家是否被眩晕
        const isPlayerStunned = this.isPlayerStunned(this.currentBattleState, localPlayerId);

        // 获取技能冷却信息
        const skillCooldowns = this.currentBattleState.skill_cooldowns?.[localPlayerId] || {};
        
        // 更新所有技能按钮状态
        document.querySelectorAll('.skill-btn').forEach(button => {
            const skillId = parseInt(button.dataset.skillId, 10);
            const cooldownTurns = skillCooldowns[skillId] || 0;
            
            // 减少一回合的冷却，但只在当前玩家的回合
            const isCooldown = cooldownTurns > 0;
            
            // 如果玩家在吟唱中、被眩晕、被沉默或技能在冷却中，禁用按钮
            if (isPlayerCasting || isPlayerStunned || isPlayerSilenced || isCooldown) {
                button.disabled = true;

                // 清除之前的状态样式
                button.classList.remove('cooldown', 'casting', 'silenced', 'stunned');

                // 添加视觉提示（优先级：眩晕 > 沉默 > 冷却 > 吟唱）
                if (isPlayerStunned) {
                    button.classList.add('stunned');
                    button.title = '眩晕中';
                    // 修改按钮文字显示眩晕状态
                    const originalText = button.dataset.originalText || button.textContent;
                    if (!button.dataset.originalText) {
                        button.dataset.originalText = button.textContent;
                    }
                    button.textContent = '眩晕';
                } else if (isPlayerSilenced) {
                    button.classList.add('silenced');
                    button.title = '沉默中';
                    // 修改按钮文字显示沉默状态
                    const originalText = button.dataset.originalText || button.textContent;
                    if (!button.dataset.originalText) {
                        button.dataset.originalText = button.textContent;
                    }
                    button.textContent = '沉默';
                } else if (isCooldown) {
                    button.classList.add('cooldown');
                    button.title = `冷却中 (${cooldownTurns}回合)`;
                } else if (isPlayerCasting) {
                    button.classList.add('casting');
                    button.title = '吟唱中';
                }
            } else {
                button.disabled = false;
                button.classList.remove('cooldown', 'casting', 'silenced', 'stunned');
                button.title = '';
                // 恢复原始按钮文字
                if (button.dataset.originalText) {
                    button.textContent = button.dataset.originalText;
                }
            }
        });
    }
    
    /**
     * 设置战斗意图
     * @param {string} intention 意图类型
     */
    setIntention(intention) {
        // 检查是否是直接攻击模式下的投降尝试
        if (intention.action === 'surrender' && this.currentBattleState && this.currentBattleState.is_direct_attack) {
            // 在直接攻击模式下不允许投降
            this.addPvpBattleLog("在强制PVP模式下无法投降！");
            return;
        }
        
        this.currentIntention = intention;
        
        // 发送意图到服务器
        this.gameClient.sendMessage(MessageProtocol.C2S_PVP_SET_INTENTION, {
            battle_id: this.currentBattleState.battle_id,
            intention: intention
        });
        
        // 更新意图显示
        this.updateIntentionDisplay();
    }
    
    /**
     * 更新意图显示
     */
    updateIntentionDisplay() {
        const displayEl = document.getElementById('pvpBattleIntentionDisplay');
        if (!displayEl) return;

        // 检查玩家是否被眩晕
        const localPlayerId = this.gameClient.currentPlayer.id;
        const stunInfo = this.getPlayerStunInfo(this.currentBattleState, localPlayerId);

        if (stunInfo && stunInfo.isStunned) {
            const remainingText = stunInfo.remainingTurns > 0 ? ` (${stunInfo.remainingTurns}回合)` : '';
            displayEl.textContent = `眩晕中${remainingText}`;
            return;
        }

        // 检查玩家是否在吟唱中
        let player = null;

        if (this.currentBattleState) {
            if (this.currentBattleState.challenger.id === localPlayerId) {
                player = this.currentBattleState.challenger;
            } else if (this.currentBattleState.defender.id === localPlayerId) {
                player = this.currentBattleState.defender;
            }
        }

        // 如果玩家在吟唱中，显示吟唱状态
        if (player && player.casting_info) {
            const skillName = player.casting_info.skill_name;
            const current = player.casting_info.rounds_passed;
            const total = player.casting_info.total_rounds;
            displayEl.textContent = `吟唱中【${skillName}】... (${current}/${total})`;
            return;
        }
        
        // 如果不在吟唱中，显示正常的意图
        if (!this.currentIntention) {
            displayEl.textContent = '请选择你的行动...';
            return;
        }
        
        let intentionText = '准备攻击';
        
        // 高亮显示当前选择的动作按钮
        document.querySelectorAll('.battle-action-btn').forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.action === this.currentIntention.action) {
                btn.classList.add('active');
            }
        });
        
        switch(this.currentIntention.action) {
            case 'attack':
                intentionText = '准备攻击对手';
                break;
            case 'use_hp_potion':
                intentionText = '准备回血';
                break;
            case 'use_mp_potion':
                intentionText = '准备回蓝';
                break;
            case 'skill':
                // 如果是技能，显示技能名称
                intentionText = `准备施放【${this.currentIntention.skill_name || '技能'}】`;
                // 高亮显示当前选择的技能按钮
                document.querySelectorAll('.skill-btn').forEach(btn => {
                    if (btn.textContent === this.currentIntention.skill_name) {
                        btn.classList.add('active');
                    }
                });
                break;
            case 'surrender':
                intentionText = '准备投降';
                break;
        }
        
        displayEl.textContent = intentionText;
    }
    
    /**
     * 处理PVP战斗更新
     * @param {Object} payload 更新数据
     */
    handlePvpBattleUpdate(payload) {
        const { battle_state, action_result, actor_id, action_type } = payload;
        
        // 更新战斗状态
        this.currentBattleState = battle_state;
        
        // 检查当前是否在战斗界面，如果不在则自动切换到战斗界面
        if (this.activeView !== 'battle' && !battle_state.is_over) {
            console.log('检测到PVP战斗进行中，自动切换到战斗界面');
            this.pvpBattleActive = true;
            this.showPvpBattleView();
            
            // 更新战斗标题
            const battleTitleEl = document.getElementById('pvpBattleTitle');
            if (battleTitleEl) {
                battleTitleEl.textContent = `${battle_state.challenger.username} vs ${battle_state.defender.username}`;
            }
            
            // 添加战斗恢复提示
            this.addPvpBattleLog(`战斗界面已恢复，战斗继续进行中...`);
            
            // 请求战斗技能列表
            this.requestBattleSkills(battle_state.battle_id);
        }
        
        // 更新战斗日志
        if (action_result && action_result.log) {
            action_result.log.forEach(message => {
                this.addPvpBattleLog(message);
            });
        }
        
        // 处理伤害/治疗效果动画
        this.processBattleEffects(payload);
        
        // 更新战斗UI
        this.updatePvpBattleView(
            battle_state.challenger, 
            battle_state.defender,
            battle_state.is_over,
            battle_state.is_over ? this.generateBattleEndReason(battle_state) : ''
        );
        
        // 更新技能按钮状态（确保在战斗状态更新后刷新按钮状态）
        this.updateSkillButtonsState();

        // 更新常规按钮状态（确保眩晕等状态效果能及时反映）
        this.updateBattleActionButtons();

        // 更新意图显示（确保在吟唱状态变化时更新提示）
        this.updateIntentionDisplay();
        
        // 如果行动者是当前玩家，则重置当前意图为"攻击"
        // 这样每次行动后会自动切换回普通攻击状态
        if (actor_id === this.gameClient.currentPlayer.id) {
            this.currentIntention = { action: 'attack' };
            this.updateIntentionDisplay();
        }
        
        // 如果战斗结束，保存结果
        if (battle_state.is_over && !this.battleResultSaved) {
            this.battleResultSaved = true;
            this.savePvpBattleResult(battle_state);
        }
    }
    
    /**
     * 处理战斗效果（伤害/治疗动画等）
     * @param {Object} payload 更新数据
     */
    processBattleEffects(payload) {
        const { action_result, actor_id, action_type } = payload;
        
        if (!action_result || !this.currentBattleState) return;

        const localPlayerId = this.gameClient.currentPlayer.id;
        const { challenger, defender } = this.currentBattleState;
        
        // 确定目标玩家ID（被攻击者）
        const targetPlayerId = (actor_id === challenger.id) ? defender.id : challenger.id;

        // 本地玩家的信息始终在'pvpDefenderInfo'（底部）
        // 对手的信息始终在'pvpChallengerInfo'（顶部）
        const targetElementId = (targetPlayerId === localPlayerId) ? 'pvpDefenderInfo' : 'pvpChallengerInfo';
        const actorElementId = (actor_id === localPlayerId) ? 'pvpDefenderInfo' : 'pvpChallengerInfo';
        
        const targetElement = document.getElementById(targetElementId);
        const actorElement = document.getElementById(actorElementId);
        
        if (!targetElement || !actorElement) return;

        // 处理伤害效果
        if (action_result.damage && action_result.damage > 0) {
            // 添加伤害数字和抖动效果
            this.gameClient.addDamageEffect(targetElementId, action_result.damage);
            
            // 使用PVP专用的抖动动画类
            targetElement.classList.add('pvp-shake-animation');
            setTimeout(() => {
                targetElement.classList.remove('pvp-shake-animation');
            }, 1000);
            
            // 如果是技能伤害，可以添加特殊效果
            if (action_type === 'skill') {
                // 可以在这里添加技能特效，比如闪光等
                targetElement.classList.add('skill-damage-effect');
                setTimeout(() => {
                    targetElement.classList.remove('skill-damage-effect');
                }, 1000);
            }
        }
        
        // 处理闪避效果
        if (action_result.is_dodged) {
            this.addDodgeEffect(targetElementId);
        }

        // 处理治疗效果
        if (action_result.heal_amount && action_result.heal_amount > 0) {
            // 根据动作类型确定治疗类型
            let healType = 'hp';
            if (action_type === 'use_mp_potion') {
                healType = 'mp';
            }

            // 治疗应用于施法者，而不是目标
            this.gameClient.addHealEffect(actorElementId, action_result.heal_amount, healType);
        }

        // 处理MP恢复效果（当没有heal_amount但有mp_restored时）
        if (action_result.mp_restored && action_result.mp_restored > 0 && (!action_result.heal_amount || action_result.heal_amount === 0)) {
            this.gameClient.addHealEffect(actorElementId, action_result.mp_restored, 'mp');
        }
    }
    
    /**
     * 添加闪避效果动画
     * @param {string} targetId 目标元素ID
     */
    addDodgeEffect(targetId) {
        // 直接使用GameClient的闪避效果函数，确保一致性
        this.gameClient.addDodgeEffect(targetId);
    }
    
    /**
     * 生成战斗结束原因文本
     * @param {Object} battleState 战斗状态
     * @returns {string} 结束原因文本
     */
    generateBattleEndReason(battleState) {
        if (battleState.winner === 'draw') {
            return '战斗结束，双方平局！';
        }
        
        if (battleState.winner === 'challenger') {
            return `${battleState.challenger.username} 获胜！`;
        }
        
        if (battleState.winner === 'defender') {
            return `${battleState.defender.username} 获胜！`;
        }
        
        if (battleState.round_count >= 30) {
            return '战斗超时，根据剩余生命值决定胜负！';
        }
        
        return '战斗结束！';
    }
    
    /**
     * 保存PVP战斗结果
     * @param {Object} battleState 战斗状态
     */
    savePvpBattleResult(battleState) {
        // 这里可以执行一些结果保存相关的逻辑
        // 实际上数据已经在服务器端保存了
        console.log('PVP战斗结果已保存', battleState);
    }
    
    /**
     * 添加PVP战斗日志
     * @param {string} message 日志消息
     */
    addPvpBattleLog(message) {
        const logContainer = document.getElementById('pvpBattleLog');
        if (!logContainer) return;

        const logEntry = document.createElement('div');
        logEntry.className = 'battle-log-entry';

        // 美化日志内容
        const beautifiedMessage = this.beautifyLogMessage(message);
        logEntry.innerHTML = beautifiedMessage;

        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    /**
     * 美化战斗日志消息
     * @param {string} message 原始消息
     * @returns {string} 美化后的HTML消息
     */
    beautifyLogMessage(message) {
        // 处理特殊分隔线
        if (message.includes('━━━━')) {
            return `<div class="log-separator">${message}</div>`;
        }

        // 处理战斗开始消息
        if (message.includes('PVP战斗开始')) {
            return `<div class="log-battle-start">${message}</div>`;
        }

        // 处理警告消息
        if (message.includes('⚠️') || message.includes('警告')) {
            return `<div class="log-warning">${message}</div>`;
        }

        // 处理伤害消息
        if (message.includes('造成') && message.includes('伤害')) {
            return `<div class="log-damage">${this.highlightNumbers(message)}</div>`;
        }

        // 处理治疗消息
        if (message.includes('恢复') && (message.includes('生命值') || message.includes('法力值'))) {
            return `<div class="log-heal">${this.highlightNumbers(message)}</div>`;
        }

        // 处理技能消息
        if (message.includes('施放') || message.includes('使用技能')) {
            return `<div class="log-skill">${this.highlightSkillNames(message)}</div>`;
        }

        // 处理闪避消息
        if (message.includes('闪避') || message.includes('躲避')) {
            return `<div class="log-dodge">${message}</div>`;
        }

        // 处理状态效果消息
        if (message.includes('眩晕') || message.includes('沉默') || message.includes('中毒')) {
            return `<div class="log-status">${message}</div>`;
        }

        // 处理死亡消息
        if (message.includes('死亡') || message.includes('击败')) {
            return `<div class="log-death">${message}</div>`;
        }

        // 处理胜利/失败消息
        if (message.includes('获胜') || message.includes('胜利') || message.includes('失败')) {
            return `<div class="log-result">${message}</div>`;
        }

        // 默认样式
        return `<div class="log-normal">${message}</div>`;
    }

    /**
     * 高亮数字
     * @param {string} message 消息
     * @returns {string} 高亮后的消息
     */
    highlightNumbers(message) {
        return message.replace(/(\d+)/g, '<span class="log-number">$1</span>');
    }

    /**
     * 高亮技能名称
     * @param {string} message 消息
     * @returns {string} 高亮后的消息
     */
    highlightSkillNames(message) {
        return message.replace(/【([^】]+)】/g, '<span class="log-skill-name">【$1】</span>');
    }

    /**
     * 切换战斗日志展开/收起
     */
    toggleBattleLog() {
        const logContainer = document.getElementById('pvpBattleLog');
        const toggleBtn = document.getElementById('pvpBattleLogToggle');
        const toggleIcon = toggleBtn?.querySelector('.toggle-icon');
        const toggleText = toggleBtn?.querySelector('.toggle-text');

        if (!logContainer || !toggleBtn) return;

        // 切换展开状态
        this.battleLogExpanded = !this.battleLogExpanded;

        if (this.battleLogExpanded) {
            // 展开日志 - 增加高度
            logContainer.classList.remove('collapsed');
            logContainer.classList.add('expanded');
            toggleIcon.textContent = '▼';
            toggleText.textContent = '收起';
            toggleBtn.classList.remove('collapsed');
        } else {
            // 收起日志 - 恢复默认高度
            logContainer.classList.remove('expanded');
            logContainer.classList.add('collapsed');
            toggleIcon.textContent = '▶';
            toggleText.textContent = '展开';
            toggleBtn.classList.add('collapsed');
        }
    }
    
    /**
     * 请求战斗技能列表
     * @param {string} battleId 战斗ID
     */
    requestBattleSkills(battleId) {
        if (!battleId || this.battleSkillsLoaded) return;
        
        console.log('请求PVP战斗技能列表');
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_BATTLE_SKILLS, {
            battle_type: 'pvp',
            battle_id: battleId
        });
    }

    /**
     * 处理ATB状态更新
     * @param {Object} payload ATB状态数据
     */
    handleAtbStatusUpdate(payload) {
        // 保存旧状态用于比较
        const oldStatus = this.atbStatus;
        
        // 更新ATB状态
        this.atbStatus = payload;
        
        // 检查当前是否在战斗界面，如果不在则自动切换到战斗界面
        if (this.currentBattleState && this.activeView !== 'battle') {
            console.log('检测到PVP战斗ATB更新，自动切换到战斗界面');
            this.pvpBattleActive = true;
            this.showPvpBattleView();
            
            // 更新战斗标题
            const battleTitleEl = document.getElementById('pvpBattleTitle');
            if (battleTitleEl && this.currentBattleState) {
                const challenger = this.currentBattleState.challenger;
                const defender = this.currentBattleState.defender;
                battleTitleEl.textContent = `${challenger.username} vs ${defender.username}`;
            }
            
            // 添加战斗恢复提示
            this.addPvpBattleLog(`战斗界面已恢复，战斗继续进行中...`);
            
            // 如果当前战斗状态存在，更新战斗UI
            if (this.currentBattleState) {
                this.updatePvpBattleView(
                    this.currentBattleState.challenger,
                    this.currentBattleState.defender,
                    this.currentBattleState.is_over
                );
                
                // 请求战斗技能列表
                this.requestBattleSkills(this.currentBattleState.battle_id);
            }
        }
        
        // 检查是否需要更新回合计数
        const roundChanged = !oldStatus || oldStatus.round_count !== payload.round_count;
        if (roundChanged) {
            const roundCounterEl = document.getElementById('pvpRoundCounter');
            if (roundCounterEl) {
                roundCounterEl.innerHTML = `回合: ${payload.round_count || 0}/${payload.max_rounds || 30}`;
            }
        }
        
        // 更新计时器
        this.updateTimerBar(payload.roundTimeRemaining);
        
        // 更新ATB条（不触发完整的UI重新渲染）
        const atbMax = payload.atbMax || 500;
        
        // 更新挑战者ATB
        const challengerAtbEl = document.querySelector('#pvpChallengerInfo .atb-bar-fill');
        if (challengerAtbEl) {
            const percent = Math.floor((payload.challengerATB / atbMax) * 100);
            challengerAtbEl.style.width = `${percent}%`;
        }
        
        // 更新防御者ATB
        const defenderAtbEl = document.querySelector('#pvpDefenderInfo .atb-bar-fill');
        if (defenderAtbEl) {
            const percent = Math.floor((payload.defenderATB / atbMax) * 100);
            defenderAtbEl.style.width = `${percent}%`;
        }
    }
    
    /**
     * 更新ATB条显示
     */
    updateAtbBars() {
        if (!this.atbStatus) return;
        
        const atbMax = this.atbStatus.atbMax || 500;
        
        // 更新挑战者ATB
        const challengerAtbEl = document.querySelector('#pvpChallengerInfo .atb-bar-fill');
        if (challengerAtbEl) {
            const percent = Math.floor((this.atbStatus.challengerATB / atbMax) * 100);
            challengerAtbEl.style.width = `${percent}%`;
        }
        
        // 更新防御者ATB
        const defenderAtbEl = document.querySelector('#pvpDefenderInfo .atb-bar-fill');
        if (defenderAtbEl) {
            const percent = Math.floor((this.atbStatus.defenderATB / atbMax) * 100);
            defenderAtbEl.style.width = `${percent}%`;
        }
        
        // 强制重新渲染玩家信息，确保ATB条显示正确
        this.updatePvpBattleView(
            this.currentBattleState.challenger,
            this.currentBattleState.defender,
            this.currentBattleState.is_over
        );
    }
    
    /**
     * 更新计时器条
     * @param {number} remainingTime 剩余时间（毫秒）
     */
    updateTimerBar(remainingTime) {
        const timerBarEl = document.getElementById('pvpTimerBar');
        if (!timerBarEl) return;
        
        const percent = Math.floor((remainingTime / 3000) * 100); // 3000毫秒是回合时间
        
        timerBarEl.innerHTML = `
            <div class="timer-bar-inner" style="width: ${percent}%"></div>
            <div class="timer-bar-text">${Math.ceil(remainingTime / 1000)}秒</div>
        `;
    }
    
    /**
     * 处理PVP战斗结束
     * @param {Object} payload 结束数据
     */
    handlePvpBattleEnded(payload) {
        const { battle_state, player_attributes } = payload;
        
        // 更新战斗状态
        this.currentBattleState = battle_state;
        this.pvpBattleActive = false;
        
        // 显示战斗结束信息
        const endMessage = this.generateBattleEndReason(battle_state);
        
        // 添加战斗结束日志
        this.addPvpBattleLog(`━━━━ 战斗结束 ━━━━`);
        this.addPvpBattleLog(endMessage);

        // 战斗结束后自动收起战斗日志，并显示展开按钮
        setTimeout(() => {
            const logContainer = document.getElementById('pvpBattleLog');
            const toggleBtn = document.getElementById('pvpBattleLogToggle');
            const toggleIcon = toggleBtn?.querySelector('.toggle-icon');
            const toggleText = toggleBtn?.querySelector('.toggle-text');

            if (logContainer && toggleBtn) {
                // 设置为收起状态
                this.battleLogExpanded = false;
                logContainer.classList.remove('expanded');
                logContainer.classList.add('collapsed');
                toggleIcon.textContent = '▶';
                toggleText.textContent = '展开';
                toggleBtn.classList.add('collapsed');
                toggleBtn.style.display = 'flex'; // 确保按钮可见
            }
        }, 1000); // 1秒后自动收起

        // 更新UI
        this.updatePvpBattleView(
            battle_state.challenger,
            battle_state.defender,
            true,
            endMessage
        );
        
        // 更新玩家的HP和MP
        if (player_attributes) {
            // 更新玩家属性
            if (this.gameClient.player) {
                this.gameClient.player.hp = player_attributes.hp;
                this.gameClient.player.max_hp = player_attributes.max_hp;
                this.gameClient.player.mp = player_attributes.mp;
                this.gameClient.player.max_mp = player_attributes.max_mp;
                
                
                // 更新UI
                this.gameClient.updatePlayerInfo();
            }
        }
        
        // 重置状态
        this.currentIntention = null;
        this.actionQueue = [];
        this.battleResultSaved = false;
    }
    
    /**
     * 从PVP战斗返回场景
     */
    returnToSceneFromPvpBattle() {
        // 重置战斗意图显示区域
        const intentionDisplay = document.getElementById('pvpBattleIntentionDisplay');
        if (intentionDisplay) {
            intentionDisplay.textContent = '战斗准备中...';
            intentionDisplay.classList.remove('battle-ended'); // 移除战斗结束样式
        }
        
        // 隐藏PVP战斗视图
        const pvpBattleView = document.getElementById('pvpBattleView');
        if (pvpBattleView) {
            pvpBattleView.style.display = 'none';
        }
        
        // 显示主视图
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
        
        // 重置状态
        this.pvpBattleActive = false;
        this.currentBattleState = null;
        this.currentIntention = null;
        this.actionQueue = [];
        this.atbStatus = null;
        this.battleSkillsLoaded = false; // 重置技能列表加载状态
        this.battleLogExpanded = false; // 重置战斗日志展开状态
        
        // 刷新场景
        this.gameClient.refreshScene();
    }
    
    /**
     * 处理PVP排行榜数据
     * @param {Object} payload 排行榜数据
     */
    handlePvpLeaderboard(payload) {
        const { leaderboard } = payload;
        
        // 显示排行榜视图
        this.showPvpLeaderboardView(leaderboard);
    }
    
    /**
     * 处理等级排行榜数据
     * @param {Object} payload 排行榜数据
     */
    handleLevelRanking(payload) {
        const { leaderboard } = payload;
        
        // 显示等级排行榜视图
        this.showLevelRankingView(leaderboard);
    }
    
    /**
     * 处理英雄排行榜数据
     * @param {Object} payload 排行榜数据
     */
    handleHeroRanking(payload) {
        const { leaderboard } = payload;
        
        // 显示英雄排行榜视图
        this.showHeroRankingView(leaderboard);
    }
    
    /**
     * 处理黑手排行榜数据
     * @param {Object} payload 排行榜数据
     */
    handleVillainRanking(payload) {
        const { leaderboard } = payload;
        
        // 显示黑手排行榜视图
        this.showVillainRankingView(leaderboard);
    }
    
    /**
     * 显示PVP排行榜视图
     * @param {Array} leaderboard 排行榜数据
     */
    showPvpLeaderboardView(leaderboard) {
        // 创建排行榜视图（如果不存在）
        if (!document.getElementById('pvpLeaderboardView')) {
            this.createPvpLeaderboardView();
        }
        
        // 填充排行榜数据
        const tableBody = document.getElementById('pvpLeaderboardTable');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        leaderboard.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            // 设置排名样式
            let rankClass = '';
            if (index === 0) rankClass = 'rank-first';
            else if (index === 1) rankClass = 'rank-second';
            else if (index === 2) rankClass = 'rank-third';
            
            row.innerHTML = `
                <td class="${rankClass}">${index + 1}</td>
                <td>${entry.player_name}</td>
                <td>${entry.wins}</td>
                <td>${entry.losses}</td>
                <td>${entry.draws}</td>
                <td>${Math.floor(entry.win_rate)}%</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // 显示排行榜视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('pvpLeaderboardView').style.display = 'block';
        this.activeView = 'leaderboard';
    }
    
    /**
     * 创建PVP排行榜视图
     */
    createPvpLeaderboardView() {
        const leaderboardView = document.createElement('div');
        leaderboardView.id = 'pvpLeaderboardView';
        leaderboardView.className = 'view';
        leaderboardView.style.display = 'none';
        
        leaderboardView.innerHTML = `
            <h3>PVP排行榜</h3>
            <div class="pvp-leaderboard-container">
                <table class="pvp-leaderboard-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>玩家</th>
                            <th>胜利</th>
                            <th>失败</th>
                            <th>平局</th>
                            <th>胜率</th>
                        </tr>
                    </thead>
                    <tbody id="pvpLeaderboardTable">
                        <!-- 排行榜数据将在这里填充 -->
                    </tbody>
                </table>
            </div>
            <div class="pvp-leaderboard-actions">
                <button class="btn-primary" onclick="game.pvpManager.hidePvpLeaderboardView()">返回</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(leaderboardView);
    }
    
    /**
     * 隐藏PVP排行榜视图
     */
    hidePvpLeaderboardView() {
        document.getElementById('pvpLeaderboardView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
    }
    
    /**
     * 显示等级排行榜视图
     * @param {Array} leaderboard 排行榜数据
     */
    showLevelRankingView(leaderboard) {
        // 创建等级排行榜视图（如果不存在）
        if (!document.getElementById('levelRankingView')) {
            this.createLevelRankingView();
        }
        
        // 填充排行榜数据
        const tableBody = document.getElementById('levelRankingTable');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        leaderboard.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            // 设置排名样式
            let rankClass = '';
            if (index === 0) rankClass = 'rank-first';
            else if (index === 1) rankClass = 'rank-second';
            else if (index === 2) rankClass = 'rank-third';
            
            row.innerHTML = `
                <td class="${rankClass}">${index + 1}</td>
                <td>${entry.player_name}</td>
                <td>${entry.level}</td>
                <td>${entry.job || '无'}</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // 显示排行榜视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('levelRankingView').style.display = 'block';
        this.activeView = 'level-ranking';
    }
    
    /**
     * 创建等级排行榜视图
     */
    createLevelRankingView() {
        const rankingView = document.createElement('div');
        rankingView.id = 'levelRankingView';
        rankingView.className = 'view';
        rankingView.style.display = 'none';
        
        rankingView.innerHTML = `
            <h3>等级排行榜</h3>
            <div class="ranking-container">
                <table class="ranking-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>玩家</th>
                            <th>等级</th>
                            <th>职业</th>
                        </tr>
                    </thead>
                    <tbody id="levelRankingTable">
                        <!-- 排行榜数据将在这里填充 -->
                    </tbody>
                </table>
            </div>
            <div class="ranking-actions">
                <button class="btn-primary" onclick="game.pvpManager.hideLevelRankingView()">返回</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(rankingView);
    }
    
    /**
     * 隐藏等级排行榜视图
     */
    hideLevelRankingView() {
        document.getElementById('levelRankingView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
    }
    
    /**
     * 显示英雄排行榜视图
     * @param {Array} leaderboard 排行榜数据
     */
    showHeroRankingView(leaderboard) {
        // 创建英雄排行榜视图（如果不存在）
        if (!document.getElementById('heroRankingView')) {
            this.createHeroRankingView();
        }
        
        // 填充排行榜数据
        const tableBody = document.getElementById('heroRankingTable');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        leaderboard.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            // 设置排名样式
            let rankClass = '';
            if (index === 0) rankClass = 'rank-first';
            else if (index === 1) rankClass = 'rank-second';
            else if (index === 2) rankClass = 'rank-third';
            
            row.innerHTML = `
                <td class="${rankClass}">${index + 1}</td>
                <td>${entry.player_name}</td>
                <td>${entry.level}</td>
                <td>${entry.alignment_value}</td>
                <td>${entry.title || '无'}</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // 显示排行榜视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('heroRankingView').style.display = 'block';
        this.activeView = 'hero-ranking';
    }
    
    /**
     * 创建英雄排行榜视图
     */
    createHeroRankingView() {
        const rankingView = document.createElement('div');
        rankingView.id = 'heroRankingView';
        rankingView.className = 'view';
        rankingView.style.display = 'none';
        
        rankingView.innerHTML = `
            <h3>英雄排行榜</h3>
            <div class="ranking-container">
                <table class="ranking-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>玩家</th>
                            <th>等级</th>
                            <th>善恶值</th>
                            <th>称号</th>
                        </tr>
                    </thead>
                    <tbody id="heroRankingTable">
                        <!-- 排行榜数据将在这里填充 -->
                    </tbody>
                </table>
            </div>
            <div class="ranking-actions">
                <button class="btn-primary" onclick="game.pvpManager.hideHeroRankingView()">返回</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(rankingView);
    }
    
    /**
     * 隐藏英雄排行榜视图
     */
    hideHeroRankingView() {
        document.getElementById('heroRankingView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
    }
    
    /**
     * 显示黑手排行榜视图
     * @param {Array} leaderboard 排行榜数据
     */
    showVillainRankingView(leaderboard) {
        // 创建黑手排行榜视图（如果不存在）
        if (!document.getElementById('villainRankingView')) {
            this.createVillainRankingView();
        }
        
        // 填充排行榜数据
        const tableBody = document.getElementById('villainRankingTable');
        if (!tableBody) return;
        
        tableBody.innerHTML = '';
        
        leaderboard.forEach((entry, index) => {
            const row = document.createElement('tr');
            
            // 设置排名样式
            let rankClass = '';
            if (index === 0) rankClass = 'rank-first';
            else if (index === 1) rankClass = 'rank-second';
            else if (index === 2) rankClass = 'rank-third';
            
            row.innerHTML = `
                <td class="${rankClass}">${index + 1}</td>
                <td>${entry.player_name}</td>
                <td>${entry.level}</td>
                <td>${entry.alignment_value}</td>
                <td>${entry.title || '无'}</td>
            `;
            
            tableBody.appendChild(row);
        });
        
        // 显示排行榜视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('villainRankingView').style.display = 'block';
        this.activeView = 'villain-ranking';
    }
    
    /**
     * 创建黑手排行榜视图
     */
    createVillainRankingView() {
        const rankingView = document.createElement('div');
        rankingView.id = 'villainRankingView';
        rankingView.className = 'view';
        rankingView.style.display = 'none';
        
        rankingView.innerHTML = `
            <h3>黑手排行榜</h3>
            <div class="ranking-container">
                <table class="ranking-table">
                    <thead>
                        <tr>
                            <th>排名</th>
                            <th>玩家</th>
                            <th>等级</th>
                            <th>善恶值</th>
                            <th>称号</th>
                        </tr>
                    </thead>
                    <tbody id="villainRankingTable">
                        <!-- 排行榜数据将在这里填充 -->
                    </tbody>
                </table>
            </div>
            <div class="ranking-actions">
                <button class="btn-primary" onclick="game.pvpManager.hideVillainRankingView()">返回</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(rankingView);
    }
    
    /**
     * 隐藏黑手排行榜视图
     */
    hideVillainRankingView() {
        document.getElementById('villainRankingView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
    }
    
    /**
     * 请求查看PVP排行榜
     */
    requestPvpLeaderboard() {
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_PVP_LEADERBOARD);
    }
    
    /**
     * 请求查看等级排行榜
     */
    requestLevelRanking() {
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_LEVEL_RANKING);
    }
    
    /**
     * 请求查看英雄排行榜（善恶值高的玩家）
     */
    requestHeroRanking() {
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_HERO_RANKING);
    }
    
    /**
     * 请求查看黑手排行榜（善恶值低的玩家）
     */
    requestVillainRanking() {
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_VILLAIN_RANKING);
    }
    
    /**
     * 处理PVP统计数据
     * @param {Object} payload 统计数据
     */
    handlePvpStats(payload) {
        const { stats } = payload;
        
        // 显示统计视图
        this.showPvpStatsView(stats);
    }
    
    /**
     * 显示PVP统计视图
     * @param {Object} stats 统计数据
     */
    showPvpStatsView(stats) {
        // 创建统计视图（如果不存在）
        if (!document.getElementById('pvpStatsView')) {
            this.createPvpStatsView();
        }
        
        // 填充统计数据
        const statsContainer = document.getElementById('pvpStatsContainer');
        if (!statsContainer) return;
        
        const winRate = stats.total_battles > 0 
            ? Math.round((stats.wins / stats.total_battles) * 100) 
            : 0;
        
        statsContainer.innerHTML = `
            <div class="pvp-stat-row">
                <div class="pvp-stat-item">
                    <div class="pvp-stat-label">总场次</div>
                    <div class="pvp-stat-value">${stats.total_battles}</div>
                </div>
                <div class="pvp-stat-item">
                    <div class="pvp-stat-label">胜率</div>
                    <div class="pvp-stat-value">${winRate}%</div>
                </div>
            </div>
            <div class="pvp-stat-row">
                <div class="pvp-stat-item">
                    <div class="pvp-stat-label">胜利</div>
                    <div class="pvp-stat-value win">${stats.wins}</div>
                </div>
                <div class="pvp-stat-item">
                    <div class="pvp-stat-label">失败</div>
                    <div class="pvp-stat-value loss">${stats.losses}</div>
                </div>
                <div class="pvp-stat-item">
                    <div class="pvp-stat-label">平局</div>
                    <div class="pvp-stat-value draw">${stats.draws}</div>
                </div>
            </div>
        `;
        
        // 添加最近战斗记录
        const battleRecordsContainer = document.getElementById('pvpBattleRecordsContainer');
        if (battleRecordsContainer) {
            // 清空之前的记录
            battleRecordsContainer.innerHTML = '<h4 class="pvp-section-title">最近战斗记录</h4>';
            
            if (stats.battle_records && stats.battle_records.length > 0) {
                // 创建表格显示战斗记录
                const table = document.createElement('table');
                table.className = 'pvp-battle-records-table';
                
                // 创建表头
                const thead = document.createElement('thead');
                thead.innerHTML = `
                    <tr>
                        <th>时间</th>
                        <th>对手</th>
                        <th>我的角色</th>
                        <th>回合数</th>
                        <th>结果</th>
                    </tr>
                `;
                table.appendChild(thead);
                
                // 创建表格内容
                const tbody = document.createElement('tbody');
                stats.battle_records.forEach(record => {
                    const row = document.createElement('tr');
                    
                    // 根据结果设置样式类
                    let resultClass = '';
                    if (record.result === '胜利') resultClass = 'win';
                    else if (record.result === '失败') resultClass = 'loss';
                    else resultClass = 'draw';
                    
                    row.innerHTML = `
                        <td>${record.battle_time}</td>
                        <td>${record.opponent_name}</td>
                        <td>${record.player_role}</td>
                        <td>${record.rounds}</td>
                        <td class="${resultClass}">${record.result}</td>
                    `;
                    
                    tbody.appendChild(row);
                });
                table.appendChild(tbody);
                
                battleRecordsContainer.appendChild(table);
            } else {
                // 显示无记录信息
                const noRecords = document.createElement('div');
                noRecords.className = 'pvp-no-records';
                noRecords.textContent = '无战斗记录';
                battleRecordsContainer.appendChild(noRecords);
            }
        }
        
        // 显示统计视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('pvpStatsView').style.display = 'block';
        this.activeView = 'stats';
    }
    
    /**
     * 创建PVP统计视图
     */
    createPvpStatsView() {
        const statsView = document.createElement('div');
        statsView.id = 'pvpStatsView';
        statsView.className = 'view card-container';
        statsView.style.display = 'none';
        
        statsView.innerHTML = `
            <h3 class="pvp-stats-title">我的战绩</h3>
            <div id="pvpStatsContainer" class="pvp-stats-container">
                <!-- 统计数据将在这里填充 -->
            </div>
            <div id="pvpBattleRecordsContainer" class="pvp-battle-records-container">
                <!-- 战斗记录将在这里填充 -->
            </div>
            <div class="pvp-stats-actions">
                <button class="btn-primary" onclick="game.pvpManager.hidePvpStatsView()">返回</button>
            </div>
        `;
        
        // 添加到游戏容器
        document.querySelector('.game-content').appendChild(statsView);
    }
    
    /**
     * 隐藏PVP统计视图
     */
    hidePvpStatsView() {
        document.getElementById('pvpStatsView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.activeView = 'main';
    }
    
    /**
     * 请求查看个人PVP统计
     */
    requestPvpStats() {
        this.gameClient.sendMessage(MessageProtocol.C2S_GET_PVP_STATS);
    }

    /**
     * 检查玩家是否被沉默
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {boolean} 是否被沉默
     */
    isPlayerSilenced(battleState, playerId) {
        if (!battleState.active_effects) {
            return false;
        }

        for (const effect of battleState.active_effects) {
            if (effect.target_id == playerId && effect.effects.silence && effect.effects.silence > 0) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取玩家眩晕信息
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {Object|null} 眩晕信息对象，包含isStunned和remainingTurns，如果未被眩晕则返回null
     */
    getPlayerStunInfo(battleState, playerId) {
        if (!battleState || !battleState.active_effects) {
            return null;
        }

        for (const effect of battleState.active_effects) {
            if (effect.target_id == playerId && effect.effects && effect.effects.stun && effect.effects.stun > 0) {
                return {
                    isStunned: true,
                    remainingTurns: effect.remaining_turns || 0
                };
            }
        }

        return null;
    }

    /**
     * 检查玩家是否被眩晕（兼容性方法）
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {boolean} 是否被眩晕
     */
    isPlayerStunned(battleState, playerId) {
        const stunInfo = this.getPlayerStunInfo(battleState, playerId);
        return stunInfo ? stunInfo.isStunned : false;
    }

    /**
     * 更新PVP战斗中的常规按钮状态
     */
    updateBattleActionButtons() {
        if (!this.currentBattleState) {
            return;
        }

        const localPlayerId = this.gameClient.currentPlayer.id;
        const isPlayerStunned = this.isPlayerStunned(this.currentBattleState, localPlayerId);

        // 获取PVP战斗按钮（包括常规按钮，排除技能按钮）
        const pvpButtons = document.querySelectorAll('.battle-action-btn:not(.skill-btn)');

        pvpButtons.forEach(button => {
            if (isPlayerStunned) {
                // 眩晕状态下禁用所有按钮
                button.disabled = true;
                button.classList.add('stunned-disabled');
                button.title = '眩晕中，无法行动';

                // 保存原始文字（如果还没保存）
                if (!button.dataset.originalText) {
                    button.dataset.originalText = button.textContent;
                }

                // 显示眩晕状态
                if (button.textContent && !button.textContent.includes('眩晕')) {
                    button.textContent = '眩晕';
                }
            } else {
                // 恢复正常状态
                button.disabled = false;
                button.classList.remove('stunned-disabled');
                button.title = '';

                // 恢复原始文字
                if (button.dataset.originalText) {
                    button.textContent = button.dataset.originalText;
                }
            }
        });
    }
}