<?php
function encryptAES($data, $key, $iv) {
    // 确保密钥长度为32字节（AES-256）
    $key = hash('sha256', $key, true);
    // openssl_encrypt 默认返回 base64 编码，所以不需要再次编码
    $encrypted = openssl_encrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return $encrypted;
}

function decryptAES($data, $key, $iv) {
    // 确保密钥长度为32字节（AES-256）
    $key = hash('sha256', $key, true);
    // 直接解密，因为 openssl_decrypt 会自动处理 base64 解码
    $decrypted = openssl_decrypt($data, 'AES-256-CBC', $key, 0, $iv);
    return $decrypted;
}

// $key = 'YourSecretKey'; // 密钥，必须是 16、24 或 32 字符长度的字符串
// $iv = 'YourIV1234567890'; // 初始化向量，必须是 16 字符长度的字符串
// $data = 'Pqzc8F7ugQKn6p8JFn8b3Q==';
 
// // $encryptedData = encryptAES($data, $key, $iv);
// // echo "Encrypted Data: $encryptedData\n";
// // echo "<br/>";
// $decryptedData = decryptAES($data, $key, $iv);
// echo "Decrypted Data: $decryptedData\n";

?>