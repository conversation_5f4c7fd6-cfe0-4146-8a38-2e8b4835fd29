<?php
/**
 * 稀有度配置管理界面
 */

require_once '../config/Database.php';
require_once '../classes/RarityManager.php';

// 检查管理员权限
session_start();
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit;
}

$rarityManager = new RarityManager();
$message = '';
$messageType = '';

// 处理配置更新
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['action'])) {
        switch ($_POST['action']) {
            case 'update_config':
                // 这里可以添加配置更新逻辑
                $message = '配置更新功能待实现（需要修改配置文件）';
                $messageType = 'info';
                break;
                
            case 'test_equipment':
                $equipmentName = $_POST['equipment_name'] ?? '';
                if ($equipmentName) {
                    $rarityInfo = $rarityManager->detectRarity($equipmentName);
                    $multiplier = $rarityManager->getRarityMultiplier($equipmentName);
                    
                    $message = sprintf(
                        '装备 "%s" 检测结果：品级 [%s]，加成系数 %.1fx，插槽数 %d',
                        $equipmentName,
                        $rarityInfo['rarity'],
                        $multiplier,
                        $rarityInfo['config']['slots']
                    );
                    $messageType = 'success';
                }
                break;
        }
    }
}

$allRarities = $rarityManager->getAllRarities();
$config = $rarityManager->getConfig();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>稀有度配置管理</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .header { text-align: center; margin-bottom: 30px; padding-bottom: 20px; border-bottom: 2px solid #007bff; }
        .section { margin-bottom: 30px; padding: 20px; border: 1px solid #ddd; border-radius: 5px; }
        .section h3 { margin-top: 0; color: #007bff; }
        .rarity-table { width: 100%; border-collapse: collapse; margin-top: 15px; }
        .rarity-table th, .rarity-table td { padding: 10px; text-align: left; border-bottom: 1px solid #ddd; }
        .rarity-table th { background-color: #f8f9fa; font-weight: bold; }
        .rarity-name { font-weight: bold; padding: 3px 8px; border-radius: 3px; color: white; text-shadow: 1px 1px 1px rgba(0,0,0,0.5); }
        .form-group { margin-bottom: 15px; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select { width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box; }
        .btn { padding: 10px 20px; border: none; border-radius: 4px; cursor: pointer; text-decoration: none; display: inline-block; margin-right: 10px; }
        .btn-primary { background-color: #007bff; color: white; }
        .btn-success { background-color: #28a745; color: white; }
        .btn-info { background-color: #17a2b8; color: white; }
        .message { padding: 10px; border-radius: 4px; margin-bottom: 20px; }
        .message.success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .message.error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .message.info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .config-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .test-result { background-color: #f8f9fa; padding: 15px; border-radius: 5px; margin-top: 10px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>装备稀有度配置管理</h1>
            <p>管理装备品级和凝练加成系数</p>
        </div>

        <?php if ($message): ?>
            <div class="message <?php echo $messageType; ?>">
                <?php echo htmlspecialchars($message); ?>
            </div>
        <?php endif; ?>

        <div class="config-grid">
            <div class="section">
                <h3>当前稀有度配置</h3>
                <table class="rarity-table">
                    <thead>
                        <tr>
                            <th>品级</th>
                            <th>加成系数</th>
                            <th>插槽数</th>
                            <th>颜色</th>
                            <th>描述</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($allRarities as $rarity => $rarityConfig): ?>
                            <tr>
                                <td>
                                    <span class="rarity-name" style="background-color: <?php echo $rarityConfig['color']; ?>">
                                        [<?php echo htmlspecialchars($rarity); ?>]
                                    </span>
                                </td>
                                <td><?php echo $rarityConfig['multiplier']; ?>x</td>
                                <td><?php echo $rarityConfig['slots']; ?></td>
                                <td><?php echo $rarityConfig['color']; ?></td>
                                <td><?php echo htmlspecialchars($rarityConfig['description']); ?></td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>

            <div class="section">
                <h3>装备稀有度测试</h3>
                <form method="post">
                    <input type="hidden" name="action" value="test_equipment">
                    <div class="form-group">
                        <label for="equipment_name">装备名称：</label>
                        <input type="text" id="equipment_name" name="equipment_name" 
                               placeholder="例如：[良]力量之戒" 
                               value="<?php echo htmlspecialchars($_POST['equipment_name'] ?? ''); ?>">
                    </div>
                    <button type="submit" class="btn btn-info">测试识别</button>
                </form>

                <div class="section">
                    <h4>测试示例</h4>
                    <ul>
                        <li><code>[良]力量之戒</code> - 良级装备</li>
                        <li><code>[优]星辉法杖</code> - 优级装备</li>
                        <li><code>[珍]日冕圣剑</code> - 珍级装备</li>
                        <li><code>普通装备</code> - 无品级标识</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>系统配置信息</h3>
            <div class="config-grid">
                <div>
                    <h4>识别配置</h4>
                    <p><strong>识别模式：</strong> <?php echo $config['rarity_detection']['strict_mode'] ? '严格模式' : '宽松模式'; ?></p>
                    <p><strong>默认品级：</strong> [<?php echo $config['rarity_detection']['default_rarity']; ?>]</p>
                    <p><strong>识别模式：</strong> <code><?php echo htmlspecialchars($config['rarity_detection']['pattern']); ?></code></p>
                </div>
                <div>
                    <h4>凝练加成配置</h4>
                    <p><strong>加成启用：</strong> <?php echo $config['refine_bonus']['enabled'] ? '是' : '否'; ?></p>
                    <p><strong>计算方式：</strong> <?php echo $config['refine_bonus']['calculation_method']; ?></p>
                    <p><strong>系数范围：</strong> <?php echo $config['refine_bonus']['min_multiplier']; ?>x - <?php echo $config['refine_bonus']['max_multiplier']; ?>x</p>
                    <p><strong>每级递增：</strong> <?php echo $config['refine_bonus']['increment_per_level']; ?></p>
                </div>
            </div>
        </div>

        <div class="section">
            <h3>操作</h3>
            <a href="index.php" class="btn btn-primary">返回管理首页</a>
            <a href="../test_rarity_system.php" class="btn btn-success" target="_blank">运行测试脚本</a>
        </div>
    </div>
</body>
</html>
