/* 任务系统相关样式 */

/* 任务视图 */
#questView, #questLogView, #npcQuestView {
    width: 100%;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow-y: auto;
    padding: 10px;
    box-sizing: border-box;
    margin-bottom: 10px;
}

/* 任务详情 */
.quest-title {
    margin-top: 0;
    color: var(--main-color);
    font-size: 18px;
    border-bottom: 1px solid var(--border-color);
    padding-bottom: 10px;
    text-align: center;
}

.quest-description {
    color: var(--main-color);
    margin: 15px 0;
    line-height: 1.6;
    font-size: 14px;
    padding: 0 5px;
}

.quest-progress, .quest-rewards {
    margin-bottom: 15px;
    background-color: rgba(255, 255, 255, 0.2);
    border-radius: 5px;
    padding: 10px;
    border: 1px solid var(--border-color);
}

.quest-progress h5, .quest-rewards h5 {
    font-size: 16px;
    color: var(--main-color);
    margin: 0 0 10px 0;
    border-bottom: 1px dotted var(--border-color);
    padding-bottom: 5px;
}

.objectives-list, .rewards-list {
    list-style-type: none;
    padding-left: 0;
    margin: 0;
}

.objectives-list li, .rewards-list li {
    padding: 8px 5px;
    border-bottom: 1px dotted var(--border-color);
    font-size: 14px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.objectives-list li:last-child, .rewards-list li:last-child {
    border-bottom: none;
}

.objective-progress {
    font-weight: bold;
    color: var(--main-color);
}

.objective-progress.completed {
    color: var(--success-color);
}

/* 任务交付信息样式 */
.quest-turn-in-info {
    margin-top: 10px;
    padding: 8px 10px;
    background-color: rgba(255, 236, 179, 0.3);
    border-left: 3px solid #ffc107;
    border-radius: 3px;
    font-size: 14px;
    color: #664d03;
    line-height: 1.4;
}

.quest-turn-in {
    margin-top: 5px;
    font-size: 12px;
    color: #664d03;
    background-color: rgba(255, 236, 179, 0.3);
    padding: 3px 6px;
    border-radius: 3px;
    border-left: 2px solid #ffc107;
}

/* 任务操作按钮 */
.quest-actions {
    margin-top: 20px;
    display: flex;
    justify-content: center;
    gap: 10px;
}

.action-button {
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s;
    font-family: inherit;
}

.primary-button {
    background-color: var(--main-color);
    color: white;
}

.primary-button:hover {
    background-color: #1a2a3a;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.primary-button:disabled {
    background-color: var(--disabled-color);
    cursor: not-allowed;
    box-shadow: none;
}

.success-button {
    background-color: #28a745;
    color: white;
}

.success-button:hover {
    background-color: #218838;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.success-button:disabled {
    background-color: #8ebe9a;
    cursor: not-allowed;
    box-shadow: none;
}

.danger-button {
    background-color: #dc3545;
    color: white;
}

.danger-button:hover {
    background-color: #c82333;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.danger-button:disabled {
    background-color: #e48a91;
    cursor: not-allowed;
    box-shadow: none;
}

/* 任务日志 */
.quest-filter {
    display: flex;
    gap: 5px;
    margin-bottom: 15px;
    justify-content: center;
}

.filter-button {
    padding: 6px 12px;
    background-color: var(--bg-color);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.2s;
}

.filter-button.active {
    background-color: var(--main-color);
    color: white;
    border-color: var(--main-color);
}

.filter-button:hover:not(.active) {
    background-color: rgba(0, 0, 0, 0.05);
}

.quest-list {
    max-height: 65vh;
    overflow-y: auto;
    padding: 5px;
}

/* 任务列表项样式 */
.quest-list-item {
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.2s;
    background-color: rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.quest-list-item:hover {
    background-color: rgba(255, 255, 255, 0.5);
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    transform: translateY(-2px);
}

.quest-info {
    flex-grow: 1;
}

.quest-title {
    font-weight: bold;
    font-size: 16px;
    margin: 0 0 5px 0;
    padding: 0;
    border: none;
    text-align: left;
}

.quest-description {
    font-size: 13px;
    color: #555;
    margin: 0 0 8px 0;
    padding: 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 进度条已移除 */

/* NPC任务列表样式 */
.quest-category-header {
    font-weight: bold;
    padding: 10px;
    margin: 15px 0 5px 0;
    border-bottom: 1px solid var(--border-color);
    color: var(--main-color);
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 4px 4px 0 0;
}

.npc-quest-list {
    list-style-type: none;
    padding: 0;
    margin: 0 0 15px 0;
    border: 1px solid var(--border-color);
    border-radius: 0 0 4px 4px;
    background-color: rgba(255, 255, 255, 0.3);
}

.npc-quest-item {
    padding: 10px;
    border-bottom: 1px dotted var(--border-color);
    cursor: pointer;
    display: flex;
    align-items: center;
    transition: background-color 0.2s;
}

.npc-quest-item:last-child {
    border-bottom: none;
}

.npc-quest-item:hover {
    background-color: rgba(255, 255, 255, 0.5);
}

.quest-status-icon {
    width: 20px;
    height: 20px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.available-quest .quest-status-icon {
    color: #dc3545;
    font-weight: bold;
}

.available-quest .quest-status-icon::before {
    content: "!";
}

.completable-quest .quest-status-icon {
    color: #28a745;
}

.completable-quest .quest-status-icon::before {
    content: "✓";
}

.quest-title-text {
    flex-grow: 1;
    font-size: 14px;
}

.quest-level {
    margin-left: 10px;
    font-size: 12px;
    color: #555;
    background-color: rgba(0, 0, 0, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
}

/* 放弃任务确认区域 */
.abandon-confirm-area {
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid #dc3545;
    border-radius: 5px;
    padding: 15px;
    margin-top: 20px;
}

.abandon-title {
    color: #dc3545;
    font-size: 16px;
    margin: 0 0 10px 0;
    text-align: center;
}

#abandonQuestMessage {
    font-size: 14px;
    margin-bottom: 15px;
    text-align: center;
}

.confirmation-actions {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.secondary-button {
    background-color: #6c757d;
    color: white;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

.secondary-button:hover {
    background-color: #5a6268;
}

/* 空任务列表 */
.empty-quest-list {
    padding: 20px;
    text-align: center;
}

.empty-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    border: 1px dashed var(--border-color);
}

.empty-message:before {
    content: "📜";
    font-size: 32px;
    margin-bottom: 10px;
}

.empty-message p {
    margin: 0;
    color: #555;
    font-size: 14px;
}

/* 已完成任务样式 */
.completed-objective {
    color: #28a745;
    font-weight: bold;
}

/* 响应式调整 */
@media (max-width: 500px) {
    .quest-actions {
        flex-direction: column;
        gap: 5px;
    }
    
    .action-button {
        width: 100%;
        padding: 10px;
    }
} 