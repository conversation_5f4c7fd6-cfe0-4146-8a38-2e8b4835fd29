<?php
/**
 * 安全的JS文件加载器
 * 使用方法：js/js.php?f=a
 */
require_once '../config/aes.php';
require_once '../config/config.php';
//开启session
session_start();

class SecureJSLoader {
    private $config;
    private $allowedReferers;
    private $jsFilePath;
    private $debugMode; // 是否为调试模式

    //从config.ini文件中获取调试参数
    private function loadConfig() {
        $configFile = '../config.ini';
        $config = parse_ini_file($configFile, true);
        return $config['debug']['debug_mode'];
    }
    
    public function __construct() {
        $this->debugMode = $this->loadConfig();
        $allowed_domains = allowed_domains;
        // 处理debugMode参数
        if ($this->debugMode) {
            // 开启调试模式
            $this->debugMode = true;
            //生成JS调试参数文件 debug.js,文件内容配置全局调试模式参数
            $this->generateDebugFile('true');
            
            $this->config = [
                'security' => [
                    'allowed_domains' => $allowed_domains,
                ],
                'content' => [
                    'file_mapping' => [
                        'a' => 'anti-debug-gentle.js',
                        'b' => 'building-manager.js',
                        'c' => 'crypto-js.min.js',
                        'd' => 'MessageProtocol.js',
                        'e' => 'UnencryptedMessageProtocol.js',
                        'f' => 'SecureMessageProtocol.js',
                        'g' => 'gameclient.js',
                        'h' => 'html.js',
                        'i' => 'skill-manager.js',
                        'j' => 'npc-manager.js',
                        'k' => 'quest-manager.js',
                        'l' => 'pvp-manager.js',
                        'u' => 'debug.js'
                    ],
                    'allowed_files' => [
                        'anti-debug-gentle.js', 'building-manager.js', 'crypto-js.min.js',
                        'MessageProtocol.js', 'UnencryptedMessageProtocol.js', 'SecureMessageProtocol.js',
                        'gameclient.js', 'html.js', 'skill-manager.js', 'npc-manager.js',
                        'quest-manager.js', 'pvp-manager.js','debug.js',
                    ],
                ],
            ];
        } else {
            // 关闭调试模式
            $this->debugMode = false;
            //检测是否存在调试文件，有则删除
            $this->generateDebugFile('false');

            // 直接内置配置
            $this->config = [
                'security' => [
                    'allowed_domains' => $allowed_domains,
                ],
                'content' => [
                    'file_mapping' => [
                        'a' => 'anti-debug-gentle_of.js',
                        'b' => 'building-manager_of.js',
                        'c' => 'crypto-js.min_of.js',
                        'd' => 'MessageProtocol_of.js',
                        'e' => 'UnencryptedMessageProtocol_of.js',
                        'f' => 'SecureMessageProtocol_of.js',
                        'g' => 'gameclient_of.js',
                        'h' => 'html_of.js',
                        'i' => 'skill-manager_of.js',
                        'j' => 'npc-manager_of.js',
                        'k' => 'quest-manager_of.js',
                        'l' => 'pvp-manager_of.js',
                        'u' => 'debug_of.js'
                    ],
                    'allowed_files' => [
                        'anti-debug-gentle_of.js', 'building-manager_of.js', 'crypto-js.min_of.js',
                        'MessageProtocol_of.js', 'UnencryptedMessageProtocol_of.js', 'SecureMessageProtocol_of.js',
                        'gameclient_of.js', 'html_of.js', 'skill-manager_of.js', 'npc-manager_of.js',
                        'quest-manager_of.js', 'pvp-manager_of.js','debug_of.js',
                    ],
                ],
            ];
        }

        $this->allowedReferers = $this->config['security']['allowed_domains'];

        // 获取文件代码参数
        $fileCode = $_GET['f'] ?? 'h'; // 默认为 'h' (html.js)

        // 验证文件代码并获取真实文件名
        $realFileName = $this->validateAndGetFileName($fileCode);
        if (!$realFileName) {
            $this->denyAccess('Invalid file code: ' . $fileCode);
        }

        $this->jsFilePath = __DIR__ . '/' . $realFileName;
    }

    //生成调试文件
    private function generateDebugFile($modebool) {
        $debugContent = '
        window.debugMode = '.$modebool.';
        window.chatLevel = '.chat_level.';
        window.privateChatRateLimit = '.private_chat_rate_limit.';
        window.publicChatRateLimit = '.public_chat_rate_limit.';
        window.ase_key = \''.aes_key.'\';
        window.ase_iv = \''.aes_iv.'\';
        ';
        file_put_contents(__DIR__ . '/debug.js', $debugContent);
    }

    // 验证时间戳签名（允许±10秒时间差）
    private function validateTimestampToken($token, $secretKey, $maxTimeDiff = 10) {
        $key = aes_key; // 密钥，必须是 16、24 或 32 字符长度的字符串
        $iv = aes_iv; // 初始化向量，必须是 16 字符长度的字符串

        // 解码Base64
        $decoded = base64_decode($token);
        if ($decoded === false) return false;
        
        // 分割为时间戳、盐值和签名
        $parts = explode(':', $decoded);
        if (count($parts) !== 3) return false;
        
        list($timestamp, $salt, $signature) = $parts;
        
        //解密时间戳
        $timestamp = decryptAES($timestamp, $key, $iv);

        // 验证时间戳格式
        if (!is_numeric($timestamp)) return false;
        
        // 检查时间差
        $currentTime = time();
        if (abs($currentTime - (int)$timestamp) > $maxTimeDiff) {
            return false;
        }
        
        //使用加密的时间戳生成签名（使用加密的时间戳）
        $timestamp = encryptAES($timestamp, $key, $iv);

        // 重新计算签名
        $data = $timestamp . ':' . $salt;
        $expectedSignature = hash_hmac('sha256', $data, $secretKey);
        
        // 安全比较签名（防止时序攻击）
        return hash_equals($expectedSignature, $signature);
    }

    // 验证请求中的token
    private function validateRequestToken() {
        $secretKey = $_SESSION['secretKey'];

        $token = $_GET['v'] ?? '';

        if (empty($token)) {
            return false;
        }

        return $this->validateTimestampToken($token, $secretKey, 10);
    }

    /**
     * 验证文件代码并返回真实文件名
     */
    private function validateAndGetFileName($fileCode) {
        // 1. 检查文件代码格式
        if (!preg_match('/^[a-z]$/', $fileCode)) {
            return false;
        }

        // 2. 检查文件映射
        $fileMapping = $this->config['content']['file_mapping'] ?? [];
        if (!isset($fileMapping[$fileCode])) {
            return false;
        }

        $realFileName = $fileMapping[$fileCode];

        // 3. 检查文件是否在白名单中
        $allowedFiles = $this->config['content']['allowed_files'] ?? [];
        if (!in_array($realFileName, $allowedFiles)) {
            return false;
        }

        // 4. 检查文件是否存在
        $fullPath = __DIR__ . '/' . $realFileName;
        if (!file_exists($fullPath)) {
            return false;
        }

        return $realFileName;
    }

    public function validateAccess() {
        // 1. 检查HTTP方法
        if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
            $this->denyAccess('Invalid method');
        }

        // 2. 检查基本的User-Agent
        $ua = $_SERVER['HTTP_USER_AGENT'] ?? '';
        if (empty($ua) || stripos($ua, 'bot') !== false) {
            $this->denyAccess('Invalid user agent');
        }

        // 3. 验证时间戳签名
        if (!$this->validateRequestToken()) {
            $this->denyAccess('Invalid or expired token');
        }

        // 4. 检查Referer
        $referer = $_SERVER['HTTP_REFERER'] ?? '';
        if (empty($referer) || !in_array(parse_url($referer)['host'], $this->allowedReferers)) {
            $this->denyAccess('Invalid referer');
        }

        return true;
    }

    private function denyAccess($reason) {
        header('Content-Type: application/javascript; charset=utf-8');
        // echo '// Access denied: ' . $reason;
        exit;
    }

    public function loadAndProcessJS() {
        if (!file_exists($this->jsFilePath)) {
            $this->denyAccess('File not found: ' . $this->jsFilePath);
        }

        $content = file_get_contents($this->jsFilePath);
        if ($content === false) {
            $this->denyAccess('Read error');
        }

        // 设置响应头并输出处理后的内容
        header('Content-Type: application/javascript; charset=utf-8');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        echo $content;
    }
}

// 执行加载
try {
    $loader = new SecureJSLoader();
    $loader->validateAccess();
    $loader->loadAndProcessJS();
} catch (Exception $e) {
    header('Content-Type: application/javascript; charset=utf-8');
    // echo '// Error: ' . $e->getMessage();
}
?>
