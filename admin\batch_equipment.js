// 批量装备创建器JavaScript - 基于现有系统逻辑
let qualityConfig = {};
let slotCoefficients = {};
let equipmentTemplates = [];
let monsterEquipmentTemplates = [];
let selectedTemplates = [];
let currentTemplateType = 'player'; // 'player' 或 'monster'

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('批量装备创建器初始化...');
    initializePage();
    setupEventListeners();
    loadQualityConfig();
    loadSlotCoefficients();
});

function initializePage() {
    // 初始化数据源选择
    const dataSource = document.getElementById('data-source');
    if (dataSource) {
        dataSource.addEventListener('change', handleDataSourceChange);
        handleDataSourceChange(); // 初始化显示
    }
}

function setupEventListeners() {
    // 加载模板按钮
    const loadTemplateBtn = document.getElementById('load-template-btn');
    if (loadTemplateBtn) {
        loadTemplateBtn.addEventListener('click', loadEquipmentTemplates);
    }
    
    // 预览和创建按钮现在使用onclick属性，这里保留备用的事件监听器
    const previewBtn = document.getElementById('preview-btn');
    const createBatchBtn = document.getElementById('create-batch-btn');

    // 备用事件监听器（如果onclick属性失效时使用）
    if (previewBtn && !previewBtn.onclick) {
        previewBtn.addEventListener('click', () => previewGeneration(false));
    }
    if (createBatchBtn && !createBatchBtn.onclick) {
        createBatchBtn.addEventListener('click', () => previewGeneration(true));
    }
    
    // 确认创建按钮
    const confirmCreateBtn = document.getElementById('confirm-create-btn');
    if (confirmCreateBtn) {
        confirmCreateBtn.addEventListener('click', confirmBatchCreation);
    }
    
    // 取消预览按钮
    const cancelPreviewBtn = document.getElementById('cancel-preview-btn');
    if (cancelPreviewBtn) {
        cancelPreviewBtn.addEventListener('click', cancelPreview);
    }
    
    // JSON验证按钮
    const validateJsonBtn = document.getElementById('validate-json-btn');
    if (validateJsonBtn) {
        validateJsonBtn.addEventListener('click', validateJsonData);
    }
    
    // 文件加载按钮
    const loadFileBtn = document.getElementById('load-file-btn');
    if (loadFileBtn) {
        loadFileBtn.addEventListener('click', () => {
            document.getElementById('json-file').click();
        });
    }
    
    // 文件输入
    const jsonFile = document.getElementById('json-file');
    if (jsonFile) {
        jsonFile.addEventListener('change', handleFileLoad);
    }
    
    // 模板过滤器
    const slotFilter = document.getElementById('template-slot-filter');
    const jobFilter = document.getElementById('template-job-filter');
    if (slotFilter) slotFilter.addEventListener('change', filterTemplates);
    if (jobFilter) jobFilter.addEventListener('change', filterTemplates);
}

function loadQualityConfig() {
    const qualityRows = document.querySelectorAll('[data-quality]');
    qualityRows.forEach(row => {
        const quality = row.dataset.quality;
        qualityConfig[quality] = {
            sockets: parseInt(row.querySelector('[data-attr="sockets"]').value),
            attack: parseInt(row.querySelector('[data-attr="attack"]').value),
            element: parseInt(row.querySelector('[data-attr="element"]').value),
            speed: parseInt(row.querySelector('[data-attr="speed"]').value),
            stats: parseInt(row.querySelector('[data-attr="stats"]').value),
            defense: parseInt(row.querySelector('[data-attr="defense"]').value)
        };
    });
    console.log('品质配置加载完成:', qualityConfig);
}

function loadSlotCoefficients() {
    const slotRows = document.querySelectorAll('[data-slot]');
    slotRows.forEach(row => {
        const slot = row.dataset.slot;
        slotCoefficients[slot] = {
            attack: parseFloat(row.querySelector('[data-coeff="attack"]').value),
            element_damage: parseFloat(row.querySelector('[data-coeff="element_damage"]').value),
            element_resistance: parseFloat(row.querySelector('[data-coeff="element_resistance"]').value),
            attack_speed: parseFloat(row.querySelector('[data-coeff="attack_speed"]').value),
            stats: parseFloat(row.querySelector('[data-coeff="stats"]').value),
            defense: parseFloat(row.querySelector('[data-coeff="defense"]').value)
        };
    });
    console.log('部位系数加载完成:', slotCoefficients);
}

function handleDataSourceChange() {
    const dataSource = document.getElementById('data-source').value;
    console.log('数据源切换到:', dataSource);

    // 隐藏所有输入区域
    document.getElementById('manual-input').style.display = 'none';
    document.getElementById('template-input').style.display = 'none';
    document.getElementById('json-input').style.display = 'none';

    // 显示对应的输入区域
    switch(dataSource) {
        case 'manual':
            currentTemplateType = 'player'; // 重置为默认值
            document.getElementById('manual-input').style.display = 'block';
            break;
        case 'template':
            currentTemplateType = 'player';
            selectedTemplates = []; // 清空选择
            clearTemplateList(); // 清空显示
            document.getElementById('template-input').style.display = 'block';
            console.log('切换到玩家装备模板，currentTemplateType =', currentTemplateType);
            break;
        case 'monster-template':
            currentTemplateType = 'monster';
            selectedTemplates = []; // 清空选择
            clearTemplateList(); // 清空显示
            document.getElementById('template-input').style.display = 'block';
            console.log('切换到怪物装备模板，currentTemplateType =', currentTemplateType);
            break;
        case 'json':
            currentTemplateType = 'player'; // 重置为默认值
            document.getElementById('json-input').style.display = 'block';
            break;
    }
}

function loadEquipmentTemplates() {
    // 根据当前模板类型选择文件
    const templateFile = currentTemplateType === 'monster' ? 'monster_equipment_templates.json' : 'equipment_templates.json';
    const templateTypeName = currentTemplateType === 'monster' ? '怪物' : '玩家';

    fetch(templateFile)
        .then(response => {
            if (!response.ok) {
                throw new Error(`无法加载${templateTypeName}装备模板文件`);
            }
            return response.json();
        })
        .then(data => {
            // 为每个模板添加ID
            const templates = data.map((template, index) => ({
                ...template,
                id: index + 1
            }));

            // 根据类型存储到对应的数组
            if (currentTemplateType === 'monster') {
                monsterEquipmentTemplates = templates;
            } else {
                equipmentTemplates = templates;
            }

            displayTemplates();
            console.log(`${templateTypeName}装备模板加载完成:`, templates.length, '件装备');
        })
        .catch(error => {
            console.error('加载装备模板失败:', error);
            alert('加载装备模板失败: ' + error.message);
            
            // 使用备用的示例数据
            equipmentTemplates = [
                {
                    id: 1,
                    name: '烈焰巨剑',
                    slot: 'TwoHanded',
                    grants_job_id: 2,
                    quality: 2,
                    description: '长达两米的巨型双手剑',
                    element_type: 'fire'
                }
            ];
            displayTemplates();
        });
}

function displayTemplates() {
    const templateList = document.getElementById('template-list');
    if (!templateList) return;
    
    templateList.innerHTML = '';
    
    const filteredTemplates = getFilteredTemplates();
    
    filteredTemplates.forEach(template => {
        const templateItem = document.createElement('div');
        templateItem.className = 'template-item';
        templateItem.dataset.templateId = template.id;
        
        // 根据装备类型显示不同的职业信息
        let jobLabel = '';
        if (template.slot === 'TwoHanded' || template.slot === 'RightHand') {
            // 武器显示授予职业
            const jobName = getJobNameById(template.grants_job_id) || '无';
            jobLabel = `授予: ${jobName}`;
        } else {
            // 其他装备显示职业限制
            const jobName = getJobNameById(template.job_restriction) || '无';
            jobLabel = `限制: ${jobName}`;
        }
        
        // 添加数据属性用于过滤和选择
        templateItem.dataset.templateId = template.id;
        templateItem.dataset.slot = template.slot;
        templateItem.dataset.quality = template.quality;
        templateItem.dataset.job = getJobFilterValue(template);

        // 为怪物装备添加特殊标识
        const monsterTypeInfo = currentTemplateType === 'monster' && template.monster_type
            ? `<span class="monster-type">怪物类型: ${template.monster_type}</span>`
            : '';

        templateItem.innerHTML = `
            <div class="template-checkbox"></div>
            <div class="template-content">
                <h4>${template.name}${currentTemplateType === 'monster' ? ' <span class="monster-badge">[怪物]</span>' : ''}</h4>
                <div class="template-meta">
                    <span>部位: ${getSlotName(template.slot)}</span>
                    <span>${jobLabel}</span>
                    <span>品质: ${getQualityName(template.quality)}</span>
                    <span>元素: ${template.element_type || '无'}</span>
                    ${monsterTypeInfo}
                </div>
            </div>
        `;

        templateItem.addEventListener('click', () => toggleTemplateSelection(templateItem));
        templateList.appendChild(templateItem);
    });
}

function getJobFilterValue(template) {
    if (template.slot === 'TwoHanded' || template.slot === 'RightHand') {
        // 武器根据授予职业分类
        if (template.grants_job_id === 2) return 'warrior';
        if (template.grants_job_id === 3) return 'mage';
        if (template.grants_job_id === 4) return 'archer';
        return 'none';
    } else {
        // 其他装备根据职业限制分类
        if (template.job_restriction === 2) return 'warrior';
        if (template.job_restriction === 3) return 'mage';
        if (template.job_restriction === 4) return 'archer';
        return 'none';
    }
}

function getFilteredTemplates() {
    const slotFilter = document.getElementById('template-slot-filter').value;
    const jobFilter = document.getElementById('template-job-filter').value;
    const qualityFilter = document.getElementById('template-quality-filter').value;

    // 根据当前模板类型选择数据源
    const templates = currentTemplateType === 'monster' ? monsterEquipmentTemplates : equipmentTemplates;

    return templates.filter(template => {
        const slotMatch = slotFilter === 'all' || template.slot === slotFilter;
        
        let jobMatch = true;
        if (jobFilter !== 'all') {
            if (template.slot === 'TwoHanded' || template.slot === 'RightHand') {
                // 武器按授予职业过滤
                const jobId = getJobIdByName(jobFilter);
                jobMatch = template.grants_job_id === jobId;
            } else {
                // 其他装备按职业限制过滤
                const jobId = getJobIdByName(jobFilter);
                jobMatch = (jobFilter === 'none' && !template.job_restriction) ||
                          template.job_restriction === jobId;
            }
        }
        
        let qualityMatch = true;
        if (qualityFilter !== 'all') {
            const quality = template.quality;
            switch(qualityFilter) {
                case '0-1':
                    qualityMatch = quality >= 0 && quality <= 1;
                    break;
                case '2-3':
                    qualityMatch = quality >= 2 && quality <= 3;
                    break;
                case '4-5':
                    qualityMatch = quality >= 4 && quality <= 5;
                    break;
                case '6':
                    qualityMatch = quality === 6;
                    break;
            }
        }

        return slotMatch && jobMatch && qualityMatch;
    });
}

function toggleTemplateSelection(templateElement) {
    const templateId = parseInt(templateElement.dataset.templateId);
    console.log('点击模板ID:', templateId);

    if (!templateId) {
        console.error('模板元素缺少templateId属性');
        return;
    }

    const index = selectedTemplates.indexOf(templateId);

    if (index > -1) {
        selectedTemplates.splice(index, 1);
        templateElement.classList.remove('selected');
        console.log('取消选择模板:', templateId);
    } else {
        selectedTemplates.push(templateId);
        templateElement.classList.add('selected');
        console.log('选择模板:', templateId);
    }

    updateSelectedCount();
    console.log('当前已选择模板:', selectedTemplates);
}

function filterTemplates() {
    displayTemplates();
}

function previewGeneration(directCreate = false) {
    const dataSource = document.getElementById('data-source').value;
    let equipmentData = [];

    try {
        switch(dataSource) {
            case 'manual':
                equipmentData = parseManualInput();
                break;
            case 'template':
            case 'monster-template':
                equipmentData = generateFromTemplates();
                break;
            case 'json':
                equipmentData = parseJsonInput();
                break;
        }

        if (equipmentData.length === 0) {
            alert('没有找到有效的装备数据');
            return;
        }

        if (directCreate) {
            createBatchEquipment(equipmentData);
        } else {
            displayPreview(equipmentData);
        }

    } catch (error) {
        alert('数据解析错误: ' + error.message);
        console.error('预览生成错误:', error);
    }
}

function parseManualInput() {
    const inputText = document.getElementById('equipment-data').value.trim();
    if (!inputText) {
        throw new Error('请输入装备数据');
    }
    
    // 解析手动输入的装备数据
    // 格式：名称|部位|职业|品质|描述|元素类型
    const lines = inputText.split('\n').filter(line => line.trim());
    const equipmentData = [];
    
    lines.forEach((line, index) => {
        try {
            const parts = line.split('|').map(part => part.trim());
            if (parts.length < 4) {
                throw new Error(`第${index + 1}行格式错误，至少需要4个字段`);
            }
            
            const [name, slot, job, quality, description = '', elementType = ''] = parts;
            
            const template = {
                id: Date.now() + index,
                name: name,
                slot: slot,
                job_setting: job,
                quality: parseInt(quality),
                description: description,
                element_type: elementType || null
            };
            
            equipmentData.push(generateEquipmentFromTemplate(template));
        } catch (error) {
            throw new Error(`第${index + 1}行解析错误: ${error.message}`);
        }
    });
    
    return equipmentData;
}

function generateFromTemplates() {
    // 根据当前模板类型选择数据源
    const templates = currentTemplateType === 'monster' ? monsterEquipmentTemplates : equipmentTemplates;
    const templateTypeName = currentTemplateType === 'monster' ? '怪物' : '玩家';

    console.log('当前选中的模板:', selectedTemplates);
    console.log(`可用的${templateTypeName}装备模板数量:`, templates.length);

    if (selectedTemplates.length === 0) {
        throw new Error('请选择至少一个装备模板');
    }

    const equipmentData = [];

    selectedTemplates.forEach(templateId => {
        const template = templates.find(t => t.id === templateId);
        if (template) {
            console.log(`生成${templateTypeName}装备:`, template.name);
            equipmentData.push(generateEquipmentFromTemplate(template));
        } else {
            console.warn('未找到模板ID:', templateId);
        }
    });

    console.log(`生成的${templateTypeName}装备数据:`, equipmentData);
    return equipmentData;
}

function parseJsonInput() {
    const jsonText = document.getElementById('json-data').value.trim();
    if (!jsonText) {
        throw new Error('请输入JSON数据');
    }

    try {
        const jsonData = JSON.parse(jsonText);
        const equipmentData = [];

        if (Array.isArray(jsonData)) {
            jsonData.forEach((template, index) => {
                try {
                    equipmentData.push(generateEquipmentFromTemplate(template));
                } catch (error) {
                    throw new Error(`第${index + 1}个装备解析错误: ${error.message}`);
                }
            });
        } else {
            equipmentData.push(generateEquipmentFromTemplate(jsonData));
        }

        return equipmentData;
    } catch (error) {
        if (error instanceof SyntaxError) {
            throw new Error('JSON格式错误: ' + error.message);
        }
        throw error;
    }
}

function generateEquipmentFromTemplate(template) {
    // 对于怪物装备，虽然品质是0级（普级），但数值按照3级（优级）计算
    const isMonsterEquipment = currentTemplateType === 'monster';
    const qualityForCalculation = isMonsterEquipment && template.quality === 0 ? 3 : template.quality;

    const quality = qualityConfig[qualityForCalculation];
    const coefficients = slotCoefficients[template.slot];

    if (!quality || !coefficients) {
        throw new Error(`无效的品质或部位配置: ${qualityForCalculation}, ${template.slot}`);
    }

    console.log(`装备 ${template.name} 数值计算信息:`, {
        originalQuality: template.quality,
        calculationQuality: qualityForCalculation,
        isMonsterEquipment: isMonsterEquipment,
        qualityConfig: quality
    });

    // 生成属性
    const stats = {};

    // 应用随机系数
    const randomFactor = template.random_factor || 1.0;

    // 基础攻击力（仅武器）
    if (template.slot === 'TwoHanded' || template.slot === 'RightHand') {
        const attackValue = Math.round(quality.attack * coefficients.attack * randomFactor);
        if (attackValue > 0) {
            stats.attack = attackValue;
        }
    }

    // 防御力（仅防具）
    if (template.slot !== 'TwoHanded' && template.slot !== 'RightHand') {
        const defenseValue = Math.round(quality.defense * coefficients.defense * randomFactor);
        if (defenseValue > 0) {
            stats.defense = defenseValue;
        }
    }

    // 攻击速度（主要给武器和饰品）
    const speedValue = Math.round(quality.speed * coefficients.attack_speed * randomFactor);
    if (speedValue > 0) {
        stats.attack_speed = speedValue;
    }

    // 四维属性（根据装备类型和职业倾向分配）
    const statsValue = Math.round(quality.stats * coefficients.stats * randomFactor);
    if (statsValue > 0) {
        addAttributesByEquipmentType(stats, template.slot, template.grants_job_id, statsValue);
    }

    // 元素伤害（仅武器和部分饰品）
    if (template.element_type && shouldHaveElementDamage(template.slot) && coefficients.element_damage > 0) {
        const baseElementValue = quality.element * coefficients.element_damage * randomFactor;
        const elementValue = Math.round(baseElementValue);
        if (elementValue > 0) {
            const elementKey = template.element_type + '_damage';
            stats[elementKey] = elementValue;
        }

        // 副元素伤害（高品质装备）
        if (template.secondary_element && template.quality >= 3) {
            const secondaryCoeff = getSecondaryElementCoeff(template.quality);
            const secondaryValue = Math.round(baseElementValue * secondaryCoeff);
            if (secondaryValue > 0) {
                const secondaryKey = template.secondary_element + '_damage';
                stats[secondaryKey] = secondaryValue;
            }
        }
    }

    // 元素抗性（防具和饰品）
    if (template.element_type && shouldHaveElementResistance(template.slot) && coefficients.element_resistance > 0) {
        const baseResistanceValue = quality.element * coefficients.element_resistance * randomFactor;
        const resistanceValue = Math.round(baseResistanceValue);
        if (resistanceValue > 0) {
            const resistanceKey = template.element_type + '_resistance';
            stats[resistanceKey] = resistanceValue;
        }

        // 副元素抗性（高品质装备）
        if (template.secondary_element && template.quality >= 3) {
            const secondaryCoeff = getSecondaryElementCoeff(template.quality);
            const secondaryValue = Math.round(baseResistanceValue * secondaryCoeff);
            if (secondaryValue > 0) {
                const secondaryKey = template.secondary_element + '_resistance';
                stats[secondaryKey] = secondaryValue;
            }
        }
    }

    // 添加品质前缀到装备名称
    const qualityPrefix = getQualityPrefix(template.quality);
    const equipmentName = qualityPrefix ? `[${qualityPrefix}]${template.name}` : template.name;

    const equipmentType = currentTemplateType === 'monster' ? 'monster' : 'player';
    console.log('生成装备时的类型信息:', {
        currentTemplateType: currentTemplateType,
        equipmentType: equipmentType,
        templateName: template.name
    });

    // 插槽数量使用原始品质配置（怪物装备保持0级的插槽数量）
    const originalQuality = qualityConfig[template.quality];

    const result = {
        name: equipmentName,
        description: template.description,
        slot: template.slot,
        job_restriction: getJobRestriction(template.slot, template),
        grants_job_id: getGrantsJobId(template.slot, template),
        sockets: originalQuality ? originalQuality.sockets : 0,
        quality: template.quality,
        stats: stats,
        buy_price: calculatePrice(template.quality),
        sell_price: Math.round(calculatePrice(template.quality) * 0.3),
        equipment_type: equipmentType
    };

    // 调试信息（可选）
    if (window.DEBUG_BATCH_EQUIPMENT) {
        console.log('生成装备数据:', {
            name: template.name,
            slot: template.slot,
            final_grants_job_id: result.grants_job_id
        });
    }

    return result;
}

function getJobRestriction(slot, template) {
    // 根据现有系统逻辑，武器通常没有职业限制
    if (slot === 'TwoHanded' || slot === 'RightHand') {
        return null;
    }

    // 其他装备可能有职业限制
    if (template.job_restriction !== undefined) {
        return template.job_restriction;
    }

    // 如果指定了其他职业信息，可能是职业限制
    return null;
}

function getGrantsJobId(slot, template) {
    // 如果模板明确指定了grants_job_id，直接使用它
    if (template.grants_job_id !== undefined && template.grants_job_id !== null) {
        return template.grants_job_id;
    }

    // 如果模板明确指定了grants_job，转换为ID
    if (template.grants_job) {
        return getJobIdByName(template.grants_job);
    }

    // 处理手动输入的job_setting字段
    if (template.job_setting) {
        if (template.job_setting === '通用') {
            return null;
        }
        return getJobIdByName(template.job_setting);
    }

    // 为了兼容旧格式，如果是武器且有job_restriction，当作grants_job处理
    if ((slot === 'TwoHanded' || slot === 'RightHand') && template.job_restriction) {
        return getJobIdByName(template.job_restriction);
    }

    return null;
}

function addAttributesByEquipmentType(stats, slot, grantsJobId, baseValue) {
    // 根据装备部位和职业倾向分配四维属性（基于现有装备数据分析）
    switch(slot) {
        case 'TwoHanded':
            // 双手武器：职业主属性+辅助属性（符合职业倾向）
            if (grantsJobId === 2) { // 战士武器
                // 力量为主，体质为辅（符合战士倾向：力量/体质 > 敏捷）
                stats.strength = Math.round(baseValue * 0.8);
                if (baseValue >= 6) stats.constitution = Math.round(baseValue * 0.5);
            } else if (grantsJobId === 3) { // 法师武器
                // 智慧为主，体质为辅（符合法师倾向：智慧/体质）
                stats.intelligence = Math.round(baseValue * 0.8);
                if (baseValue >= 6) stats.constitution = Math.round(baseValue * 0.5);
            } else if (grantsJobId === 4) { // 射手武器
                // 敏捷为主，力量为辅（符合射手倾向：敏捷 > 其他）
                stats.agility = Math.round(baseValue * 0.8);
                if (baseValue >= 6) stats.strength = Math.round(baseValue * 0.4);
            }
            break;

        case 'RightHand':
            // 右手武器：职业主属性平衡分配
            if (grantsJobId === 2) { // 战士武器
                // 力量+体质平衡（符合战士倾向：力量/体质 > 敏捷）
                stats.strength = Math.round(baseValue * 0.7);
                stats.constitution = Math.round(baseValue * 0.6);
            } else if (grantsJobId === 3) { // 法师武器
                // 智慧+体质平衡（符合法师倾向：智慧/体质）
                stats.intelligence = baseValue;
                stats.constitution = baseValue;
            } else if (grantsJobId === 4) { // 射手武器
                // 敏捷+力量组合（符合射手倾向：敏捷为主）
                stats.agility = Math.round(baseValue * 0.8);
                stats.strength = Math.round(baseValue * 0.4);
            }
            break;

        case 'Head':
            // 头部装备：防御为主，体质为辅（参考ID56: defense:20, constitution:20）
            if (baseValue >= 6) {
                stats.constitution = baseValue;
            }
            break;

        case 'Body':
            // 身体装备：体质为主（参考ID4,13,70: constitution主导）
            stats.constitution = Math.round(baseValue * 0.8);
            break;

        case 'LeftHand':
            // 左手装备：防御为主，不加四维属性（参考ID15: 只有defense）
            // 不添加四维属性，让防御系数处理
            break;

        case 'Neck':
            // 颈部装备：攻击+攻速组合（参考ID52,72）
            // 不添加四维属性，主要靠攻击和攻速
            break;

        case 'Back':
            // 背部装备：敏捷为主（参考ID53,64: agility主导）
            stats.agility = Math.round(baseValue * 0.8);
            break;

        case 'Finger':
            // 戒指：全属性平衡（参考ID74: 四维全有）
            const ringValue = Math.round(baseValue * 0.6);
            stats.strength = ringValue;
            stats.intelligence = ringValue;
            stats.agility = ringValue;
            stats.constitution = ringValue;
            break;

        default:
            // 默认情况：不添加四维属性
            break;
    }
}

function shouldHaveElementDamage(slot) {
    // 只有武器和特定饰品才有元素伤害
    const damageSlots = ['TwoHanded', 'RightHand', 'Finger'];
    return damageSlots.includes(slot);
}

function shouldHaveElementResistance(slot) {
    // 防具和饰品才有元素抗性，武器没有
    const resistanceSlots = ['LeftHand', 'Head', 'Neck', 'Body', 'Finger', 'Back'];
    return resistanceSlots.includes(slot);
}

function getSecondaryElementCoeff(quality) {
    // 副元素系数，根据品质递增
    const coeffs = {
        0: 0,    // 普级无副元素
        1: 0,    // 凡级无副元素
        2: 0.6,  // 良级副元素60%
        3: 0.7,  // 优级副元素70%
        4: 0.8,  // 珍级副元素80%
        5: 0.9,  // 极级副元素90%
        6: 1.0   // 玄级副元素100%
    };
    return coeffs[quality] || 0;
}

function calculatePrice(quality) {
    const basePrices = [50, 100, 150, 200, 300, 500, 800];
    return basePrices[quality] || 50;
}

function displayPreview(equipmentData) {
    const previewSection = document.getElementById('preview-section');
    const previewContent = document.getElementById('preview-content');

    if (!previewSection || !previewContent) {
        console.error('预览区域元素未找到');
        return;
    }

    previewContent.innerHTML = '';

    equipmentData.forEach((equipment) => {
        const previewItem = document.createElement('div');
        previewItem.className = 'preview-item';

        // 格式化属性显示
        const statsHtml = Object.entries(equipment.stats)
            .map(([key, value]) => {
                const displayName = ATTRIBUTE_NAME_MAP[key] || key;
                return `<div class="stat-line">${displayName}: ${value}</div>`;
            })
            .join('');

        // 职业信息显示
        let jobInfo = '';
        if (equipment.grants_job_id) {
            const jobName = getJobNameById(equipment.grants_job_id);
            jobInfo += `<span>授予职业: ${jobName}</span>`;
        }
        if (equipment.job_restriction) {
            const jobName = getJobNameById(equipment.job_restriction);
            jobInfo += `<span>职业限制: ${jobName}</span>`;
        }

        previewItem.innerHTML = `
            <h4>${equipment.name}</h4>
            <div class="template-meta">
                <span>部位: ${getSlotName(equipment.slot)}</span>
                <span>品质: ${getQualityName(equipment.quality)}</span>
                <span>插槽: ${equipment.sockets}个</span>
                ${jobInfo}
            </div>
            <div class="item-stats">
                ${statsHtml}
            </div>
            <div class="item-prices">
                <span>购买价格: ${equipment.buy_price} 金币</span>
                <span>出售价格: ${equipment.sell_price} 金币</span>
            </div>
        `;

        previewContent.appendChild(previewItem);
    });

    previewSection.style.display = 'block';
    previewSection.scrollIntoView({ behavior: 'smooth' });

    // 存储数据供确认创建使用
    window.pendingEquipmentData = equipmentData;
}

function confirmBatchCreation() {
    if (window.pendingEquipmentData) {
        createBatchEquipment(window.pendingEquipmentData);
    }
}

function cancelPreview() {
    document.getElementById('preview-section').style.display = 'none';
    window.pendingEquipmentData = null;
}

function createBatchEquipment(equipmentData) {
    const createBtn = document.getElementById('confirm-create-btn') ||
                     document.getElementById('create-batch-btn');

    if (createBtn) {
        createBtn.disabled = true;
        createBtn.innerHTML = '<span class="loading"></span> 创建中...';
    }

    console.log('提交的数据:', {
        action: 'create_batch',
        equipment_data: equipmentData
    });

    fetch('batch_equipment_api.php', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({
            action: 'create_batch',
            equipment_data: equipmentData
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayResult(data, 'success');
            // 清空表单
            clearForms();
        } else {
            displayResult(data, 'error');
        }
    })
    .catch(error => {
        console.error('创建失败:', error);
        displayResult({
            success: false,
            message: '网络错误: ' + error.message
        }, 'error');
    })
    .finally(() => {
        if (createBtn) {
            createBtn.disabled = false;
            createBtn.innerHTML = '确认创建';
        }
    });
}

function displayResult(data, type) {
    const resultSection = document.getElementById('result-section');
    const resultContent = document.getElementById('result-content');

    if (!resultSection || !resultContent) return;

    resultContent.className = `result-content result-${type}`;

    if (data.success) {
        resultContent.innerHTML = `
            <h3>✅ 创建成功</h3>
            <p>${data.message}</p>
            ${data.created_items ? `<p>成功创建 ${data.created_items.length} 件装备</p>` : ''}
            ${data.created_items ? `
                <div class="created-items">
                    ${data.created_items.map(item => `
                        <div class="created-item">
                            <strong>${item.name}</strong> (ID: ${item.id})
                        </div>
                    `).join('')}
                </div>
            ` : ''}
        `;
    } else {
        resultContent.innerHTML = `
            <h3>❌ 创建失败</h3>
            <p>${data.message}</p>
            ${data.errors ? `
                <div class="error-details">
                    <h4>详细错误:</h4>
                    <ul>
                        ${data.errors.map(error => `<li>${error}</li>`).join('')}
                    </ul>
                </div>
            ` : ''}
        `;
    }

    resultSection.style.display = 'block';
    resultSection.scrollIntoView({ behavior: 'smooth' });

    // 隐藏预览区域
    document.getElementById('preview-section').style.display = 'none';
}

function clearForms() {
    // 清空手动输入
    const equipmentData = document.getElementById('equipment-data');
    if (equipmentData) equipmentData.value = '';

    // 清空JSON输入
    const jsonData = document.getElementById('json-data');
    if (jsonData) jsonData.value = '';

    // 清空模板选择
    selectedTemplates = [];
    const selectedItems = document.querySelectorAll('.template-item.selected');
    selectedItems.forEach(item => item.classList.remove('selected'));

    // 清空待处理数据
    window.pendingEquipmentData = null;
}

function validateJsonData() {
    const jsonText = document.getElementById('json-data').value.trim();
    if (!jsonText) {
        alert('请输入JSON数据');
        return;
    }

    try {
        const jsonData = JSON.parse(jsonText);
        alert('JSON格式验证通过！');
        console.log('验证的JSON数据:', jsonData);
    } catch (error) {
        alert('JSON格式错误: ' + error.message);
    }
}

function handleFileLoad(event) {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = function(e) {
        document.getElementById('json-data').value = e.target.result;
    };
    reader.readAsText(file);
}

// 工具函数
function getJobIdByName(jobName) {
    const jobMap = {
        '冒险家': 1,
        '战士': 2,
        '法师': 3,
        '射手': 4,
        'warrior': 2,
        'mage': 3,
        'archer': 4,
        'none': null
    };
    return jobMap[jobName] || null;
}

function getJobNameById(jobId) {
    if (!jobId) return '无';
    const job = ALL_JOBS.find(j => j.id == jobId);
    return job ? job.name : '未知';
}

function getSlotName(slot) {
    const slotMap = {
        'TwoHanded': '双手',
        'RightHand': '右手',
        'LeftHand': '左手',
        'Head': '头部',
        'Neck': '颈部',
        'Body': '身体',
        'Finger': '手指',
        'Back': '背部'
    };
    return slotMap[slot] || slot;
}

function getQualityName(quality) {
    const qualityMap = {
        0: '[普]',
        1: '[凡]',
        2: '[良]',
        3: '[优]',
        4: '[珍]',
        5: '[极]',
        6: '[玄]'
    };
    return qualityMap[quality] || `[${quality}]`;
}

function getQualityPrefix(quality) {
    const qualityMap = {
        0: null,  // 普级装备不加前缀
        1: '凡',
        2: '良',
        3: '优',
        4: '珍',
        5: '极',
        6: '玄'
    };
    return qualityMap[quality] || null;
}

// 全局函数供HTML onclick使用
window.handlePreviewClick = function() {
    previewGeneration(false);
};

window.handleCreateClick = function() {
    previewGeneration(true);
};

// 批量选择功能
function selectAllTemplates() {
    selectedTemplates = [];
    const templateItems = document.querySelectorAll('.template-item');
    templateItems.forEach(item => {
        if (item.style.display !== 'none') {
            item.classList.add('selected');
            const templateId = parseInt(item.dataset.templateId);
            if (templateId && !selectedTemplates.includes(templateId)) {
                selectedTemplates.push(templateId);
            }
        }
    });
    updateSelectedCount();
    console.log('全选后的模板:', selectedTemplates);
}

function selectNoneTemplates() {
    selectedTemplates = [];
    const templateItems = document.querySelectorAll('.template-item');
    templateItems.forEach(item => {
        item.classList.remove('selected');
    });
    updateSelectedCount();
}

function selectBySlot() {
    const slotFilter = document.getElementById('template-slot-filter').value;
    if (slotFilter === 'all') {
        alert('请先选择一个装备部位');
        return;
    }

    selectNoneTemplates();
    const templateItems = document.querySelectorAll('.template-item');
    templateItems.forEach(item => {
        const slot = item.dataset.slot;
        if (slot === slotFilter && item.style.display !== 'none') {
            item.classList.add('selected');
            const templateId = parseInt(item.dataset.templateId);
            if (templateId && !selectedTemplates.includes(templateId)) {
                selectedTemplates.push(templateId);
            }
        }
    });
    updateSelectedCount();
}

function selectByJob() {
    const jobFilter = document.getElementById('template-job-filter').value;
    if (jobFilter === 'all') {
        alert('请先选择一个职业类型');
        return;
    }

    selectNoneTemplates();
    const templateItems = document.querySelectorAll('.template-item');
    templateItems.forEach(item => {
        const job = item.dataset.job;
        if (job === jobFilter && item.style.display !== 'none') {
            item.classList.add('selected');
            const templateId = parseInt(item.dataset.templateId);
            if (templateId && !selectedTemplates.includes(templateId)) {
                selectedTemplates.push(templateId);
            }
        }
    });
    updateSelectedCount();
}

function selectByQuality() {
    const qualityFilter = document.getElementById('template-quality-filter').value;
    if (qualityFilter === 'all') {
        alert('请先选择一个品质等级');
        return;
    }

    selectNoneTemplates();
    const templateItems = document.querySelectorAll('.template-item');
    templateItems.forEach(item => {
        const quality = parseInt(item.dataset.quality);
        let shouldSelect = false;

        switch(qualityFilter) {
            case '0-1':
                shouldSelect = quality >= 0 && quality <= 1;
                break;
            case '2-3':
                shouldSelect = quality >= 2 && quality <= 3;
                break;
            case '4-5':
                shouldSelect = quality >= 4 && quality <= 5;
                break;
            case '6':
                shouldSelect = quality === 6;
                break;
        }

        if (shouldSelect && item.style.display !== 'none') {
            item.classList.add('selected');
            const templateId = parseInt(item.dataset.templateId);
            if (templateId && !selectedTemplates.includes(templateId)) {
                selectedTemplates.push(templateId);
            }
        }
    });
    updateSelectedCount();
}

function updateSelectedCount() {
    const selectedItems = document.querySelectorAll('.template-item.selected');
    document.getElementById('selected-count').textContent = selectedItems.length;
}

function clearTemplateList() {
    const templateList = document.getElementById('template-list');
    templateList.innerHTML = '<p>请点击"加载装备模板"按钮加载模板数据</p>';
    updateSelectedCount();
}

// 这个函数已经在上面定义了，删除重复定义
