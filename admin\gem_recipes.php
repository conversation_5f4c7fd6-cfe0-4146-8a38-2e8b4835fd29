<?php
// 设置页面标题和当前页面标识
$pageTitle = '宝石合成配方管理';
$currentPage = 'gem_recipes';

ob_start();
?>
<link rel="stylesheet" href="recipes.css">
<?php
$extra_css = ob_get_clean();

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

// 分类中文翻译映射
$category_translation = [
    'Equipment' => '装备', 'Gem' => '宝石', 'Material' => '材料',
    'Potion' => '药品', 'Rune' => '符石', 'Misc' => '杂物', 'Scroll' => '书卷'
];

// 从 item_templates 表动态获取物品列表
$stmt_items = $db->query("SELECT `id`, `name`, `category` FROM `item_templates` WHERE `category` = 'Gem' ORDER BY `name`");
$all_items = $stmt_items->fetchAll(PDO::FETCH_ASSOC);
$stmt_items->closeCursor();

// 向物品数据中添加中文分类名
foreach ($all_items as &$item) {
    $item['category_cn'] = $category_translation[$item['category']] ?? $item['category'];
}
unset($item); // 断开最后一个元素的引用

// 筛选出宝石
$gem_items = array_filter($all_items, function($item) {
    return $item['category'] === 'Gem';
});

// 获取所有唯一的物品分类
$item_categories = array_unique(array_column($all_items, 'category'));
sort($item_categories);

ob_start();
?>
<script>
// 将PHP数据传递给JS
const allItemsData = <?= json_encode($all_items, JSON_UNESCAPED_UNICODE); ?>;
const gemItemsData = <?= json_encode($gem_items, JSON_UNESCAPED_UNICODE); ?>;
</script>
<script src="gem_recipes.js" defer></script>
<?php
$extra_js = ob_get_clean();

require_once 'layout_header.php';

// 获取要编辑的配方ID
$edit_id = isset($_GET['edit']) ? intval($_GET['edit']) : 0;
$edit_data = null;
$edit_materials = [];

if ($edit_id > 0) {
    // 获取配方详情
    $stmt = $db->prepare("SELECT * FROM gem_recipe_templates WHERE id = ?");
    $stmt->bindParam(1, $edit_id);
    $stmt->execute();
    $edit_data = $stmt->fetch(PDO::FETCH_ASSOC);

    // 获取配方材料
    $stmt = $db->prepare("SELECT * FROM gem_recipe_materials WHERE recipe_id = ?");
    $stmt->bindParam(1, $edit_id);
    $stmt->execute();
    $edit_materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
}
?>

<div class="main-content-area card">
    <div class="card-header">
        <div class="search-and-actions">
            <input type="text" id="search-input" class="form-control search-input" placeholder="按名称或ID搜索...">
            <button id="search-button" class="btn btn-secondary">搜索</button>
            <button id="add-recipe-btn" class="btn btn-primary">添加宝石配方</button>
        </div>
    </div>
    <div class="table-container">
        <table class="data-table">
            <thead>
                <tr>
                    <th>ID</th>
                    <th>配方名称</th>
                    <th>产出物品</th>
                    <th>产出数量</th>
                    <th>合成等级</th>
                    <th>材料总数</th>
                    <th>操作</th>
                </tr>
            </thead>
            <tbody id="recipes-tbody">
                <!-- 宝石配方数据将通过JS动态渲染 -->
            </tbody>
        </table>
    </div>
    <div class="pagination">
        <!-- 分页控件将通过JS动态渲染 -->
    </div>
</div>

<?php require_once 'layout_footer.php'; ?>

<!-- Recipe Modal -->
<div id="recipeModal" class="modal-overlay" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modalTitle">宝石配方信息</h2>
            <button class="modal-close">&times;</button>
        </div>
        <form id="recipe-form" style="padding: 20px; max-height: 70vh; overflow-y: auto;">
            <input type="hidden" id="recipe-id" name="id">

            <div class="form-group">
                <label for="recipe-name">配方名称</label>
                <input type="text" id="recipe-name" name="name" required value="<?= $edit_data ? htmlspecialchars($edit_data['name']) : '' ?>">
            </div>

            <div class="form-group">
                <label for="recipe-result_item_id">产出物品</label>
                <select id="recipe-result_item_id" name="result_item_id" required>
                    <option value="">选择物品</option>
                    <?php foreach ($all_items as $item): ?>
                        <?php $selected = $edit_data && $edit_data['result_item_id'] == $item['id'] ? 'selected' : ''; ?>
                        <option value="<?= $item['id'] ?>" <?= $selected ?>><?= htmlspecialchars($item['name']) ?> [<?= $item['category'] ?>]</option>
                    <?php endforeach; ?>
                </select>
            </div>

            <div class="form-group">
                <label for="recipe-result_quantity">产出数量</label>
                <input type="number" id="recipe-result_quantity" name="result_quantity" min="1" value="<?= $edit_data ? $edit_data['result_quantity'] : '1' ?>" required>
            </div>

            <div class="form-group">
                <label for="recipe-craft_level">合成等级</label>
                <select id="recipe-craft_level" name="craft_level" required>
                    <?php for ($i = 1; $i <= 6; $i++): ?>
                        <?php $selected = $edit_data && $edit_data['craft_level'] == $i ? 'selected' : ''; ?>
                        <option value="<?= $i ?>" <?= $selected ?>><?= $i ?> 级</option>
                    <?php endfor; ?>
                </select>
            </div>

            <div class="form-group">
                <label for="recipe-description">配方描述</label>
                <textarea id="recipe-description" name="description" rows="3"><?= $edit_data ? htmlspecialchars($edit_data['description']) : '' ?></textarea>
            </div>

            <fieldset>
                <legend>配方材料</legend>
                <div id="materials-container">
                    <?php
                    // 如果是编辑模式，显示现有材料
                    if (!empty($edit_materials)) {
                        foreach ($edit_materials as $index => $material) {
                            ?>
                            <div class="material-row">
                                <select name="material_ids[]" class="material-select">
                                    <option value="">选择材料</option>
                                    <?php foreach ($gem_items as $item): ?>
                                        <?php $selected = $material['material_item_id'] == $item['id'] ? 'selected' : ''; ?>
                                        <option value="<?= $item['id'] ?>" <?= $selected ?>><?= htmlspecialchars($item['name']) ?> [<?= $item['category'] ?>]</option>
                                    <?php endforeach; ?>
                                </select>
                                <input type="number" name="material_quantities[]" min="1" value="<?= $material['quantity'] ?>" placeholder="数量" required>
                                <button type="button" class="btn btn-danger remove-material">删除</button>
                            </div>
                            <?php
                        }
                    }
                    ?>
                </div>
                <button type="button" id="add-material" class="btn btn-secondary">添加材料</button>
            </fieldset>

            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存配方</button>
                <button type="button" class="btn btn-secondary" id="cancel-recipe-btn">取消</button>
            </div>
        </form>
    </div>
</div>


