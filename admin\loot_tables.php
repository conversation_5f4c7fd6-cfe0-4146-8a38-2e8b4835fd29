<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once '../config/Database.php';

$db = Database::getInstance()->getConnection();

// 获取所有物品模板用于"添加物品"下拉菜单，包含分类和装备类型信息
$stmt = $db->query("
    SELECT
        it.id,
        it.name,
        it.category,
        it.equipment_type,
        it.description,
        ed.slot
    FROM item_templates it
    LEFT JOIN equipment_details ed ON it.id = ed.item_template_id
    ORDER BY it.category, it.name ASC
");
$allItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
$stmt->closeCursor();

// 装备部位显示名称映射
function getSlotDisplayName($slot) {
    $slotNames = [
        'TwoHanded' => '双手',
        'RightHand' => '右手',
        'LeftHand' => '左手',
        'Head' => '头部',
        'Neck' => '颈部',
        'Body' => '身体',
        'Finger' => '手指',
        'Back' => '背部'
    ];
    return $slotNames[$slot] ?? $slot;
}

$pageTitle = '掉落表管理';
$currentPage = 'loot_tables'; // 用于导航栏高亮
require_once 'layout_header.php';
?>

<div class="page-content-grid">
    <div class="sidebar-secondary">
        <h3>掉落表</h3>
        <ul id="loot-table-list">
            <!-- 掉落表将通过 JS 动态加载 -->
            <li class="loading">加载中...</li>
        </ul>
        <div class="sidebar-actions">
            <input type="text" id="new-table-name" placeholder="新掉落表名称" style="width: 100%; margin-bottom: 5px;">
            <button class="btn btn-primary btn-block" onclick="app.createTable()">创建新表</button>
        </div>
    </div>
    <div class="main-content-area card">
        <div id="details-view" style="display: none;">
            <div class="card-content">
                <form id="table-details-form" onsubmit="app.saveTableDetails(event)">
                    <div class="form-group">
                        <label for="tableNameInput">名称</label>
                        <input type="text" id="tableNameInput" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="tableDescriptionInput">描述</label>
                        <textarea id="tableDescriptionInput" class="form-control" rows="3"></textarea>
                    </div>
                    <div class="form-actions" style="text-align: left;">
                        <button type="submit" class="btn btn-primary">保存表信息</button>
                        <button type="button" class="btn btn-danger" onclick="app.deleteTable()">删除此表</button>
                    </div>
                </form>
                
                <hr>

                <h3>表内物品</h3>
                <table id="item-entries-table" class="table-striped">
                    <thead>
                        <tr>
                            <th>物品名称</th>
                            <th>掉落率 (%)</th>
                            <th>数量范围</th>
                            <th class="actions-column">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <!-- 物品条目将通过 JS 动态加载 -->
                    </tbody>
                </table>
                <p id="no-items-message" style="display: none; text-align: center; padding: 20px;">此掉落表为空。</p>

                <hr>
                
                <form id="add-entry-form" class="form-inline-group" onsubmit="app.addEntry(event)">
                    <h4>添加新物品</h4>

                    <!-- 物品筛选器 -->
                    <div class="item-filters" style="margin-bottom: 15px;">
                        <div class="filter-row">
                            <label>筛选条件：</label>
                            <select id="category-filter" onchange="filterItems()">
                                <option value="">全部分类</option>
                                <option value="Equipment">装备</option>
                                <option value="Gem">宝石</option>
                                <option value="Material">材料</option>
                                <option value="Potion">药品</option>
                                <option value="Rune">符石</option>
                                <option value="Misc">杂物</option>
                                <option value="Scroll">书卷</option>
                            </select>

                            <select id="equipment-type-filter" onchange="filterItems()" style="display: none;">
                                <option value="">全部类型</option>
                                <option value="player">玩家装备</option>
                                <option value="monster">怪物装备</option>
                            </select>

                            <select id="slot-filter" onchange="filterItems()" style="display: none;">
                                <option value="">全部部位</option>
                                <option value="TwoHanded">双手</option>
                                <option value="RightHand">右手</option>
                                <option value="LeftHand">左手</option>
                                <option value="Head">头部</option>
                                <option value="Neck">颈部</option>
                                <option value="Body">身体</option>
                                <option value="Finger">手指</option>
                                <option value="Back">背部</option>
                            </select>

                            <input type="text" id="name-filter" placeholder="搜索物品名称..." onkeyup="filterItems()" style="width: 200px;">

                            <button type="button" onclick="clearFilters()" class="btn btn-sm btn-secondary">清除筛选</button>
                        </div>
                    </div>

                    <!-- 物品选择 -->
                    <div class="form-row">
                        <select name="item_template_id" id="item-select" required style="min-width: 300px;">
                            <option value="">-- 选择物品 --</option>
                            <?php foreach ($allItems as $item): ?>
                                <option value="<?= $item['id'] ?>"
                                        data-category="<?= $item['category'] ?>"
                                        data-equipment-type="<?= $item['equipment_type'] ?? '' ?>"
                                        data-slot="<?= $item['slot'] ?? '' ?>"
                                        data-name="<?= strtolower($item['name']) ?>">
                                    <?= htmlspecialchars($item['name']) ?>
                                    <?php if ($item['category'] === 'Equipment'): ?>
                                        [<?= $item['equipment_type'] === 'monster' ? '怪物' : '玩家' ?>]
                                        <?php if ($item['slot']): ?>
                                            (<?= getSlotDisplayName($item['slot']) ?>)
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </option>
                            <?php endforeach; ?>
                        </select>

                        <input type="number" name="drop_chance" placeholder="概率(%)" value="100" step="0.1" min="0" max="100" required>
                        <input type="number" name="min_quantity" placeholder="最小数量" value="1" min="1" required>
                        <input type="number" name="max_quantity" placeholder="最大数量" value="1" min="1" required>
                        <button type="submit" class="btn btn-primary">添加</button>
                    </div>
                </form>
            </div>
        </div>
        <div id="welcome-message" class="center-text-placeholder">
            <h3>← 请从左侧列表选择一个掉落表进行编辑，或创建一个新的。</h3>
        </div>
    </div>
</div>

<div id="status-message-container"></div>

<?php ob_start(); ?>
<style>
/* 可以在 style.css 中添加这些样式 */
.page-content-grid { display: grid; grid-template-columns: 250px 1fr; gap: 20px; }
.sidebar-secondary { background-color: #f8f9fa; padding: 15px; border-radius: 5px; height: fit-content; }
.sidebar-secondary h3 { margin-top: 0; }
#loot-table-list { list-style: none; padding: 0; margin: 0 0 15px 0; }
#loot-table-list li a { display: block; padding: 8px 12px; text-decoration: none; color: #333; border-radius: 4px; }
#loot-table-list li a:hover { background-color: #e9ecef; }
#loot-table-list li.active a { background-color: var(--primary-color); color: #fff; }
.sidebar-actions { border-top: 1px solid #dee2e6; padding-top: 15px; }

.table-striped tbody tr:nth-of-type(odd) { background-color: rgba(0,0,0,.05); }
.actions-column { width: 15%; text-align: right; }

.form-inline-group { display: flex; flex-wrap: wrap; gap: 10px; align-items: flex-end; }
.form-inline-group > * { flex: 1 1 auto; }
.form-inline-group h4 { width: 100%; margin: 10px 0 5px 0; flex-basis: 100%; }
.form-inline-group select, .form-inline-group input { width: auto; }

/* 物品筛选器样式 */
.item-filters {
    background-color: #f8f9fa;
    padding: 15px;
    border-radius: 5px;
    border: 1px solid #dee2e6;
}

.filter-row {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.filter-row label {
    font-weight: 600;
    color: #495057;
    margin-right: 5px;
}

.filter-row select, .filter-row input {
    padding: 5px 8px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.form-row {
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.form-row select, .form-row input {
    padding: 8px 12px;
    border: 1px solid #ced4da;
    border-radius: 4px;
}

#table-name-header { cursor: pointer; }
#table-name-header:hover::after { content: ' (点击编辑)'; font-size: 0.8em; color: #6c757d; }
</style>

<script>
class LootTableApp {
    constructor() {
        this.currentTableId = null;
        this.cache = new Map(); // 用于缓存掉落表数据
        this.loadTables();
    }

    async apiCall(action, body) {
        const formData = new URLSearchParams();
        formData.append('action', action);
        for (const key in body) {
            formData.append(key, body[key]);
        }

        try {
            const response = await fetch('api_loot_tables.php', { method: 'POST', body: formData });
            const result = await response.json();
            if (!result.success) {
                throw new Error(result.message || '未知错误');
            }
            return result;
        } catch (error) {
            this.showStatus(`操作失败: ${error.message}`, true);
            throw error;
        }
    }

    async loadTables() {
        try {
            const result = await this.apiCall('get_tables');
            const listEl = document.getElementById('loot-table-list');
            listEl.innerHTML = '';
            if (result.data.length === 0) {
                listEl.innerHTML = '<li>无掉落表</li>';
            } else {
                result.data.forEach(table => {
                    const li = document.createElement('li');
                    li.dataset.id = table.id;
                    li.innerHTML = `<a href="#" onclick="app.selectLootTable(${table.id})">${this.escapeHTML(table.name)}</a>`;
                    listEl.appendChild(li);
                });
            }
        } catch (error) {
            // error is already handled in apiCall
        }
    }

    async selectLootTable(id) {
        if (this.currentTableId === id && this.cache.has(id)) {
             this.renderDetails(this.cache.get(id));
             return;
        }
        this.currentTableId = id;
        
        try {
             const response = await fetch(`api_loot_tables.php?action=get_table_details&id=${id}`);
             const result = await response.json();
             if(!result.success) throw new Error(result.message);
             
             this.cache.set(id, result.data);
             this.renderDetails(result.data);

             document.querySelectorAll('#loot-table-list li').forEach(li => li.classList.remove('active'));
             document.querySelector(`#loot-table-list li[data-id="${id}"]`).classList.add('active');
        } catch(error) {
            this.showStatus(error.message, true);
        }
    }
    
    renderDetails(data) {
        document.getElementById('welcome-message').style.display = 'none';
        document.getElementById('details-view').style.display = 'block';

        document.getElementById('tableNameInput').value = data.name;
        document.getElementById('tableDescriptionInput').value = data.description || '';
        
        const tableBody = document.querySelector('#item-entries-table tbody');
        const noItemsMsg = document.getElementById('no-items-message');
        tableBody.innerHTML = '';

        if (data.entries && data.entries.length > 0) {
            noItemsMsg.style.display = 'none';
            data.entries.forEach(entry => {
                const tr = document.createElement('tr');
                tr.dataset.id = entry.id;
                tr.innerHTML = `
                    <td>${this.escapeHTML(entry.name)}</td>
                    <td>${entry.drop_chance}</td>
                    <td>${entry.min_quantity} - ${entry.max_quantity}</td>
                    <td class="actions-column">
                        <button class="btn btn-secondary btn-sm" onclick="app.editEntry(this, ${entry.id})">编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="app.deleteEntry(${entry.id})">删除</button>
                    </td>
                `;
                tableBody.appendChild(tr);
            });
        } else {
            noItemsMsg.style.display = 'block';
        }
        document.querySelector('#add-entry-form').reset();
        this.loadTables();
    }
    
    async createTable() {
        const nameInput = document.getElementById('new-table-name');
        const name = nameInput.value.trim();
        if (!name) {
            this.showStatus('请输入掉落表名称', true);
            return;
        }

        try {
            const result = await this.apiCall('create_table', { name });
            this.showStatus(result.message);
            nameInput.value = '';
            this.loadTables();
            this.selectLootTable(result.id);
        } catch (error) {}
    }
    
    async deleteTable() {
        if (!this.currentTableId) return;
        const tableName = this.cache.get(this.currentTableId)?.name || '';
        if (!confirm(`确定要删除掉落表 "${tableName}" 吗？此操作不可恢复。`)) return;

        try {
            const result = await this.apiCall('delete_table', { id: this.currentTableId });
            this.showStatus(result.message);
            this.cache.delete(this.currentTableId);
            this.currentTableId = null;
            document.getElementById('details-view').style.display = 'none';
            document.getElementById('welcome-message').style.display = 'block';
            this.loadTables();
        } catch (error) {}
    }

    async saveTableDetails(event) {
        event.preventDefault();
        if (!this.currentTableId) return;

        const name = document.getElementById('tableNameInput').value.trim();
        const description = document.getElementById('tableDescriptionInput').value.trim();

        if (!name) {
            this.showStatus('掉落表名称不能为空', true);
            return;
        }

        try {
            const result = await this.apiCall('update_table_name', { 
                id: this.currentTableId, 
                name: name,
                description: description
            });
            this.showStatus(result.message);
            // Update UI
            const cachedData = this.cache.get(this.currentTableId);
            if (cachedData) {
                cachedData.name = name;
                cachedData.description = description;
            }
            document.querySelector(`#loot-table-list li[data-id="${this.currentTableId}"] a`).textContent = name;
        } catch (error) {}
    }

    async addEntry(event) {
        event.preventDefault();
        if (!this.currentTableId) return;
        
        const form = event.target;
        const body = {
            loot_table_id: this.currentTableId,
            item_template_id: form.elements['item_template_id'].value,
            drop_chance: form.elements['drop_chance'].value,
            min_quantity: form.elements['min_quantity'].value,
            max_quantity: form.elements['max_quantity'].value
        };

        try {
            const result = await this.apiCall('add_entry', body);
            this.showStatus(result.message);
            // Manually add to cache and re-render
            const cachedData = this.cache.get(this.currentTableId);
            if(cachedData) {
                cachedData.entries.push(result.new_entry);
                this.renderDetails(cachedData);
            }
            form.reset();
        } catch (error) {}
    }
    
    editEntry(button, entryId) {
        const row = button.closest('tr');
        const cells = row.querySelectorAll('td');
        const [name, chance, quantityRange] = Array.from(cells).map(c => c.textContent);
        const [min, max] = quantityRange.split(' - ');

        row.innerHTML = `
            <td>${name}</td>
            <td><input type="number" class="form-control-sm" value="${chance}" step="0.1" min="0" max="100"></td>
            <td>
                <input type="number" class="form-control-sm" value="${min}" min="1" style="width: 60px;"> - 
                <input type="number" class="form-control-sm" value="${max}" min="1" style="width: 60px;">
            </td>
            <td class="actions-column">
                <button class="btn btn-success btn-sm" onclick="app.saveEntry(${entryId}, this)">保存</button>
                <button class="btn btn-secondary btn-sm" onclick="app.cancelEdit(${entryId})">取消</button>
            </td>
        `;
    }

    async saveEntry(entryId, button) {
        const row = button.closest('tr');
        const inputs = row.querySelectorAll('input');
        const body = {
            id: entryId,
            drop_chance: inputs[0].value,
            min_quantity: inputs[1].value,
            max_quantity: inputs[2].value
        };
        try {
            const result = await this.apiCall('update_entry', body);
            this.showStatus(result.message);
            // Refresh details from cache
            this.cache.delete(this.currentTableId);
            this.selectLootTable(this.currentTableId);
        } catch(error){}
    }
    
    cancelEdit(entryId) {
        // Just re-render from cache to cancel
        this.selectLootTable(this.currentTableId);
    }
    
    async deleteEntry(entryId) {
        if (!confirm('确定要删除这个物品吗？')) return;
        try {
            const result = await this.apiCall('delete_entry', { id: entryId });
            this.showStatus(result.message);
            // Refresh details
            this.cache.delete(this.currentTableId);
            this.selectLootTable(this.currentTableId);
        } catch(error) {}
    }

    showStatus(message, isError = false) {
        const container = document.getElementById('status-message-container');
        const statusEl = document.createElement('div');
        statusEl.textContent = message;
        statusEl.className = isError ? 'status-message error' : 'status-message success';
        container.appendChild(statusEl);
        setTimeout(() => statusEl.remove(), 3000);
    }

    escapeHTML(str) {
        const p = document.createElement('p');
        p.appendChild(document.createTextNode(str));
        return p.innerHTML;
    }
}

const app = new LootTableApp();

// 物品筛选功能
function filterItems() {
    const categoryFilter = document.getElementById('category-filter').value;
    const equipmentTypeFilter = document.getElementById('equipment-type-filter').value;
    const slotFilter = document.getElementById('slot-filter').value;
    const nameFilter = document.getElementById('name-filter').value.toLowerCase();
    const itemSelect = document.getElementById('item-select');
    const equipmentTypeFilterDiv = document.getElementById('equipment-type-filter');
    const slotFilterDiv = document.getElementById('slot-filter');

    // 显示或隐藏装备相关筛选器
    if (categoryFilter === 'Equipment') {
        equipmentTypeFilterDiv.style.display = 'inline-block';
        slotFilterDiv.style.display = 'inline-block';
    } else {
        equipmentTypeFilterDiv.style.display = 'none';
        slotFilterDiv.style.display = 'none';
        equipmentTypeFilterDiv.value = ''; // 重置装备类型筛选
        slotFilterDiv.value = ''; // 重置部位筛选
    }

    // 筛选选项
    const options = itemSelect.querySelectorAll('option');
    let visibleCount = 0;

    options.forEach(option => {
        if (option.value === '') {
            // 保持默认选项可见
            option.style.display = 'block';
            return;
        }

        const category = option.dataset.category;
        const equipmentType = option.dataset.equipmentType;
        const slot = option.dataset.slot;
        const name = option.dataset.name;

        let shouldShow = true;

        // 分类筛选
        if (categoryFilter && category !== categoryFilter) {
            shouldShow = false;
        }

        // 装备类型筛选
        if (equipmentTypeFilter && equipmentType !== equipmentTypeFilter) {
            shouldShow = false;
        }

        // 装备部位筛选
        if (slotFilter && slot !== slotFilter) {
            shouldShow = false;
        }

        // 名称搜索
        if (nameFilter && !name.includes(nameFilter)) {
            shouldShow = false;
        }

        option.style.display = shouldShow ? 'block' : 'none';
        if (shouldShow) visibleCount++;
    });

    // 更新选择框的提示文本
    const defaultOption = itemSelect.querySelector('option[value=""]');
    if (visibleCount === 0) {
        defaultOption.textContent = '-- 没有符合条件的物品 --';
    } else {
        defaultOption.textContent = `-- 选择物品 (${visibleCount}项) --`;
    }
}

function clearFilters() {
    document.getElementById('category-filter').value = '';
    document.getElementById('equipment-type-filter').value = '';
    document.getElementById('slot-filter').value = '';
    document.getElementById('name-filter').value = '';
    document.getElementById('equipment-type-filter').style.display = 'none';
    document.getElementById('slot-filter').style.display = 'none';
    filterItems();
}

// 页面加载完成后初始化筛选器
document.addEventListener('DOMContentLoaded', function() {
    // 初始化筛选器状态
    filterItems();
});
</script>
<?php 
$extra_js = ob_get_clean();
// Add status message styles if not already in a global css file
$extra_css = '
#status-message-container { position: fixed; bottom: 20px; left: 50%; transform: translateX(-50%); z-index: 1100; }
.status-message { padding: 10px 20px; border-radius: 5px; color: #fff; margin-top: 5px; }
.status-message.success { background-color: #28a745; }
.status-message.error { background-color: #dc3545; }
';
require_once 'layout_footer.php'; 
?> 