// 建筑类型映射表
const buildingTypeMap = {
    'SHOP': '商店',
    'DIAMOND_SHOP': '钻石商店',
    'FORGE': '装备锻造',
    'CRAFTING': '材料合成台',
    'GEM_CRAFTING': '宝石合成台',
    'REFINE': '装备凝练',
    'REVIVE_POINT': '复活点',
    'TELEPORTER': '传送点',
    'WAREHOUSE': '仓库',
    'ATTRIBUTE_RESET': '属性重修',
    'INN': '旅馆',
    'BANK': '银行',
    'GUILD_HALL': '公会大厅',
    'QUEST_BOARD': '任务告示板'
};

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 加载建筑列表
    loadBuildingList();
});

// 加载建筑列表
function loadBuildingList() {
    fetch('api_buildings.php?action=get_for_list')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                renderBuildingList(data.data);
            } else {
                showStatusMessage('加载建筑列表失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 渲染建筑列表（卡片视图）
function renderBuildingList(buildings) {
    // 卡片视图
    const buildingGrid = document.getElementById('building-grid');
    buildingGrid.innerHTML = '';
    
    if (buildings.length === 0) {
        buildingGrid.innerHTML = '<div class="building-card"><p style="text-align:center;">暂无建筑数据</p></div>';
        return;
    }
    
    buildings.forEach(building => {
        // 处理场景信息
        let scenesHtml = '';
        if (building.scenes && building.scenes.length > 0) {
            scenesHtml = '<div class="building-scenes"><strong>已分配场景:</strong> <ul>';
            building.scenes.forEach(scene => {
                scenesHtml += `<li>
                    ${escapeHtml(scene.name)}
                    <button class="btn-icon remove-scene" onclick="removeScene(${building.id}, '${scene.id}', event)" title="移除此场景">
                        <span>&times;</span>
                    </button>
                </li>`;
            });
            scenesHtml += '</ul></div>';
        } else {
            scenesHtml = '<div class="building-scenes"><em>未分配到任何场景</em></div>';
        }
        
        // 卡片视图
        const card = document.createElement('div');
        card.className = 'building-card';
        card.innerHTML = `
            <div class="building-info">
                <h3>${escapeHtml(building.name)}</h3>
                <div class="building-type">${buildingTypeMap[building.type] || building.type}</div>
                <div class="building-description">${escapeHtml(building.description || '')}</div>
            </div>
            ${scenesHtml}
            <div class="building-actions">
                <button class="btn btn-sm" onclick="showAssignScenesModal(${building.id})">分配场景</button>
                <button class="btn btn-sm btn-secondary" onclick="showEditModal(${building.id})">编辑</button>
                <button class="btn btn-sm btn-danger" onclick="confirmDelete(${building.id})">删除</button>
            </div>
        `;
        buildingGrid.appendChild(card);
    });
}

// 显示创建模态框
function showCreateModal() {
    // 重置表单
    document.getElementById('create-building-form').reset();
    
    // 显示模态框
    document.getElementById('create-building-modal').style.display = 'flex';
}

// 显示编辑模态框
function showEditModal(id) {
    // 加载建筑数据
    fetch(`api_buildings.php?action=get&id=${id}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const building = data.data;
                
                // 填充表单
                document.getElementById('edit-id').value = building.id;
                document.getElementById('edit-name').value = building.name;
                document.getElementById('edit-type').value = building.type;
                document.getElementById('edit-description').value = building.description || '';
                
                // 显示模态框
                document.getElementById('edit-building-modal').style.display = 'flex';
            } else {
                showStatusMessage('加载建筑数据失败: ' + data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 显示场景分配模态框
function showAssignScenesModal(buildingId) {
    // 设置建筑ID
    document.getElementById('assign-building-id').value = buildingId;
    
    // 加载所有场景
    fetch('api_buildings.php?action=get_scenes')
        .then(response => response.json())
        .then(scenesData => {
            if (!scenesData.success) {
                showStatusMessage('加载场景数据失败: ' + scenesData.message, 'error');
                return;
            }
            
            // 加载该建筑已分配的场景
            fetch(`api_buildings.php?action=get_building_scenes&building_id=${buildingId}`)
                .then(response => response.json())
                .then(assignedData => {
                    if (!assignedData.success) {
                        showStatusMessage('加载已分配场景失败: ' + assignedData.message, 'error');
                        return;
                    }
                    
                    const sceneSelect = document.getElementById('scene-select');
                    sceneSelect.innerHTML = '';
                    
                    // 填充场景选项
                    scenesData.data.forEach(scene => {
                        const option = document.createElement('option');
                        option.value = scene.id;
                        option.textContent = scene.name;
                        
                        // 如果场景已分配给该建筑，则选中
                        if (assignedData.data.includes(scene.id)) {
                            option.selected = true;
                        }
                        
                        sceneSelect.appendChild(option);
                    });
                    
                    // 显示模态框
                    document.getElementById('assign-scenes-modal').style.display = 'flex';
                })
                .catch(error => {
                    console.error('Error:', error);
                    showStatusMessage('网络错误，请重试', 'error');
                });
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
}

// 移除场景关联
function removeScene(buildingId, sceneId, event) {
    // 阻止事件冒泡，避免触发卡片点击事件
    event.stopPropagation();
    
    if (confirm(`确定要移除此场景关联吗？`)) {
        // 创建一个只包含这个场景ID的数组，表示要保留的场景
        const formData = new FormData();
        formData.append('action', 'remove_scene');
        formData.append('building_id', buildingId);
        formData.append('scene_id', sceneId);
        
        fetch('api_buildings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatusMessage(data.message, 'success');
                loadBuildingList(); // 重新加载建筑列表
            } else {
                showStatusMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
    }
}

// 处理表单提交
function handleFormSubmit(event, action) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', action);
    
    fetch('api_buildings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            hideActiveModal();
            loadBuildingList();
        } else {
            showStatusMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 处理场景分配
function handleAssignScenes(event) {
    event.preventDefault();
    
    const form = event.target;
    const formData = new FormData(form);
    formData.append('action', 'assign_scenes');
    
    fetch('api_buildings.php', {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            showStatusMessage(data.message, 'success');
            hideActiveModal();
            loadBuildingList(); // 重新加载建筑列表
        } else {
            showStatusMessage(data.message, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showStatusMessage('网络错误，请重试', 'error');
    });
}

// 确认删除
function confirmDelete(id) {
    if (confirm('确定要删除这个建筑吗？相关的场景关联也会被删除。')) {
        const formData = new FormData();
        formData.append('action', 'delete');
        formData.append('id', id);
        
        fetch('api_buildings.php', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showStatusMessage(data.message, 'success');
                loadBuildingList();
            } else {
                showStatusMessage(data.message, 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showStatusMessage('网络错误，请重试', 'error');
        });
    }
}

// 隐藏当前活动的模态框
function hideActiveModal() {
    document.querySelectorAll('.modal-overlay').forEach(modal => {
        modal.style.display = 'none';
    });
}

// 显示状态消息
function showStatusMessage(message, type) {
    const container = document.getElementById('status-message-container');
    const messageElement = document.createElement('div');
    messageElement.className = `status-message ${type}`;
    messageElement.textContent = message;
    
    container.appendChild(messageElement);
    
    // 3秒后自动移除
    setTimeout(() => {
        messageElement.classList.add('fade-out');
        setTimeout(() => {
            container.removeChild(messageElement);
        }, 500);
    }, 3000);
}

// HTML转义函数
function escapeHtml(unsafe) {
    if (typeof unsafe !== 'string') {
        return '';
    }
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}