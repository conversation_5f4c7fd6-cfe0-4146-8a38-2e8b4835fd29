{"file:///c%3A/Users/<USER>/Desktop/game/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E4%BB%BB%E5%8A%A1%E8%AE%BE%E8%AE%A1%20-%20%E5%89%AF%E6%9C%AC.md": {"language": "<PERSON><PERSON>", "code": 1815, "comment": 0, "blank": 186}, "file:///c%3A/Users/<USER>/Desktop/game/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E4%BB%BB%E5%8A%A1%E8%AE%BE%E8%AE%A1.md": {"language": "<PERSON><PERSON>", "code": 424, "comment": 0, "blank": 58}, "file:///c%3A/Users/<USER>/Desktop/game/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8C%E5%9C%B0%E5%9B%BE%E8%AE%BE%E8%AE%A1.md": {"language": "<PERSON><PERSON>", "code": 438, "comment": 0, "blank": 97}, "file:///c%3A/Users/<USER>/Desktop/game/%E6%B8%B8%E6%88%8F%E4%BB%BB%E5%8A%A1%E7%B3%BB%E7%BB%9F%E5%AE%9E%E7%8E%B0%E8%AE%BE%E8%AE%A1.md": {"language": "<PERSON><PERSON>", "code": 371, "comment": 0, "blank": 63}, "file:///c%3A/Users/<USER>/Desktop/game/start_game.sh": {"language": "<PERSON> Script", "code": 22, "comment": 7, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/game/%E6%B8%B8%E6%88%8F%E4%B8%96%E7%95%8CNPC%E8%AE%BE%E8%AE%A1.md": {"language": "<PERSON><PERSON>", "code": 434, "comment": 0, "blank": 48}, "file:///c%3A/Users/<USER>/Desktop/game/classes/SceneManager.php": {"language": "PHP", "code": 205, "comment": 11, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/game/classes/RefineCalculator.php": {"language": "PHP", "code": 167, "comment": 34, "blank": 46}, "file:///c%3A/Users/<USER>/Desktop/game/classes/QuestManager.php": {"language": "PHP", "code": 976, "comment": 227, "blank": 203}, "file:///c%3A/Users/<USER>/Desktop/game/classes/NPCManager.php": {"language": "PHP", "code": 329, "comment": 124, "blank": 84}, "file:///c%3A/Users/<USER>/Desktop/game/classes/SecureMessageProtocol.php": {"language": "PHP", "code": 34, "comment": 13, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/game/classes/GemCraftingManager.php": {"language": "PHP", "code": 388, "comment": 117, "blank": 69}, "file:///c%3A/Users/<USER>/Desktop/game/classes/LootManager.php": {"language": "PHP", "code": 233, "comment": 78, "blank": 59}, "file:///c%3A/Users/<USER>/Desktop/game/classes/PvpBattleSystem.php": {"language": "PHP", "code": 1093, "comment": 243, "blank": 223}, "file:///c%3A/Users/<USER>/Desktop/game/classes/CraftingManager.php": {"language": "PHP", "code": 336, "comment": 110, "blank": 59}, "file:///c%3A/Users/<USER>/Desktop/game/classes/MessageProtocol.php": {"language": "PHP", "code": 166, "comment": 24, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/game/README.md": {"language": "<PERSON><PERSON>", "code": 54, "comment": 0, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/game/game.html": {"language": "HTML", "code": 23, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/classes/ChatHandler.php": {"language": "PHP", "code": 344, "comment": 98, "blank": 87}, "file:///c%3A/Users/<USER>/Desktop/game/classes/BattleSystem.php": {"language": "PHP", "code": 1021, "comment": 263, "blank": 234}, "file:///c%3A/Users/<USER>/Desktop/game/css/c.css": {"language": "PostCSS", "code": 24, "comment": 3, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/js/skill-manager.js": {"language": "JavaScript", "code": 301, "comment": 102, "blank": 83}, "file:///c%3A/Users/<USER>/Desktop/game/js/UnencryptedMessageProtocol.js": {"language": "JavaScript", "code": 24, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/game/css/r.css": {"language": "PostCSS", "code": 59, "comment": 0, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/game/database/security_tables.sql": {"language": "MS SQL", "code": 55, "comment": 5, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/game/classes/DialogueScriptProcessor.php": {"language": "PHP", "code": 425, "comment": 86, "blank": 91}, "file:///c%3A/Users/<USER>/Desktop/game/js/updatejs.php": {"language": "PHP", "code": 354, "comment": 54, "blank": 61}, "file:///c%3A/Users/<USER>/Desktop/game/js/SecureMessageProtocol.js": {"language": "JavaScript", "code": 103, "comment": 11, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/game/css/q.css": {"language": "PostCSS", "code": 340, "comment": 13, "blank": 59}, "file:///c%3A/Users/<USER>/Desktop/game/database/game_battle.sql": {"language": "MS SQL", "code": 839, "comment": 580, "blank": 279}, "file:///c%3A/Users/<USER>/Desktop/game/js/quest-manager.js": {"language": "JavaScript", "code": 716, "comment": 242, "blank": 165}, "file:///c%3A/Users/<USER>/Desktop/game/database/gem_crafting_tables.sql": {"language": "MS SQL", "code": 64, "comment": 13, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/game/index.html": {"language": "HTML", "code": 55, "comment": 0, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/game/css/p.css": {"language": "PostCSS", "code": 802, "comment": 20, "blank": 132}, "file:///c%3A/Users/<USER>/Desktop/game/database/deploy_gem_crafting.sql": {"language": "MS SQL", "code": 72, "comment": 27, "blank": 20}, "file:///c%3A/Users/<USER>/Desktop/game/css/n.css": {"language": "PostCSS", "code": 302, "comment": 8, "blank": 52}, "file:///c%3A/Users/<USER>/Desktop/game/js/secure-config.php": {"language": "PHP", "code": 140, "comment": 73, "blank": 55}, "file:///c%3A/Users/<USER>/Desktop/game/js/npc-manager.js": {"language": "JavaScript", "code": 567, "comment": 228, "blank": 136}, "file:///c%3A/Users/<USER>/Desktop/game/js/pvp-manager.js": {"language": "JavaScript", "code": 1062, "comment": 381, "blank": 260}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/autoload.php": {"language": "PHP", "code": 20, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/css/g.css": {"language": "PostCSS", "code": 1096, "comment": 26, "blank": 158}, "file:///c%3A/Users/<USER>/Desktop/game/server/refine_handlers.php": {"language": "PHP", "code": 357, "comment": 101, "blank": 87}, "file:///c%3A/Users/<USER>/Desktop/game/server/crafting_handlers.php": {"language": "PHP", "code": 30, "comment": 28, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/game/server/gem_crafting_handlers.php": {"language": "PHP", "code": 31, "comment": 28, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/game/server/websocket_server.php": {"language": "PHP", "code": 234, "comment": 19, "blank": 61}, "file:///c%3A/Users/<USER>/Desktop/game/server/pvp_handler.php": {"language": "PHP", "code": 1170, "comment": 423, "blank": 312}, "file:///c%3A/Users/<USER>/Desktop/game/js/MessageProtocol.js": {"language": "JavaScript", "code": 130, "comment": 9, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/game/js/js.php": {"language": "PHP", "code": 169, "comment": 35, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/game/server/improved_websocket_server.php": {"language": "PHP", "code": 4843, "comment": 881, "blank": 1018}, "file:///c%3A/Users/<USER>/Desktop/game/server/cache_control.php": {"language": "PHP", "code": 68, "comment": 31, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/game/js/building-manager.js": {"language": "JavaScript", "code": 2434, "comment": 574, "blank": 443}, "file:///c%3A/Users/<USER>/Desktop/game/js/anti-debug-gentle.js": {"language": "JavaScript", "code": 231, "comment": 76, "blank": 57}, "file:///c%3A/Users/<USER>/Desktop/game/js/html.js": {"language": "JavaScript", "code": 469, "comment": 29, "blank": 25}, "file:///c%3A/Users/<USER>/Desktop/game/js/gameclient.js": {"language": "JavaScript", "code": 4515, "comment": 651, "blank": 834}, "file:///c%3A/Users/<USER>/Desktop/game/js/debug-loader.php": {"language": "PHP", "code": 155, "comment": 21, "blank": 39}, "file:///c%3A/Users/<USER>/Desktop/game/config.ini": {"language": "Ini", "code": 11, "comment": 8, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/game/composer.json": {"language": "JSON", "code": 5, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/composer.lock": {"language": "JSON", "code": 82, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/js/debug.js": {"language": "JavaScript", "code": 2, "comment": 1, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/platform_check.php": {"language": "PHP", "code": 21, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/api/get_players.php": {"language": "PHP", "code": 14, "comment": 1, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/js/crypto-js.min.js": {"language": "JavaScript", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/game/api/security_violation.php": {"language": "PHP", "code": 90, "comment": 15, "blank": 20}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/InstalledVersions.php": {"language": "PHP", "code": 178, "comment": 133, "blank": 49}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/installed.json": {"language": "JSON", "code": 72, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/ClassLoader.php": {"language": "PHP", "code": 286, "comment": 235, "blank": 59}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/installed.php": {"language": "PHP", "code": 32, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/autoload_static.php": {"language": "PHP", "code": 28, "comment": 1, "blank": 8}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/autoload_psr4.php": {"language": "PHP", "code": 6, "comment": 1, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/autoload_real.php": {"language": "PHP", "code": 25, "comment": 4, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/autoload_namespaces.php": {"language": "PHP", "code": 5, "comment": 1, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/composer/autoload_classmap.php": {"language": "PHP", "code": 6, "comment": 1, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/config/formulas.php": {"language": "PHP", "code": 464, "comment": 248, "blank": 140}, "file:///c%3A/Users/<USER>/Desktop/game/config/RedisManager.php": {"language": "PHP", "code": 56, "comment": 19, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/game/config/config.php": {"language": "PHP", "code": 8, "comment": 13, "blank": 9}, "file:///c%3A/Users/<USER>/Desktop/game/config/Database.php": {"language": "PHP", "code": 74, "comment": 14, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_jobs.php": {"language": "PHP", "code": 18, "comment": 1, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/config/ConnectionPool.php": {"language": "PHP", "code": 91, "comment": 6, "blank": 18}, "file:///c%3A/Users/<USER>/Desktop/game/admin/auth.php": {"language": "PHP", "code": 15, "comment": 5, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_npcs.php": {"language": "PHP", "code": 251, "comment": 30, "blank": 47}, "file:///c%3A/Users/<USER>/Desktop/game/admin/battle_logs.css": {"language": "PostCSS", "code": 244, "comment": 9, "blank": 42}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_loot_tables.php": {"language": "PHP", "code": 125, "comment": 11, "blank": 27}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_gem_recipes.php": {"language": "PHP", "code": 180, "comment": 10, "blank": 36}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_skills.php": {"language": "PHP", "code": 96, "comment": 8, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_shop.php": {"language": "PHP", "code": 75, "comment": 3, "blank": 16}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_teleporter.php": {"language": "PHP", "code": 202, "comment": 17, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_pvp_battle_logs.php": {"language": "PHP", "code": 184, "comment": 19, "blank": 41}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_item_templates.php": {"language": "PHP", "code": 28, "comment": 3, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_recipes.php": {"language": "PHP", "code": 137, "comment": 5, "blank": 22}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_scenes.php": {"language": "PHP", "code": 560, "comment": 42, "blank": 126}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_quests.php": {"language": "PHP", "code": 269, "comment": 30, "blank": 47}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_items.php": {"language": "PHP", "code": 252, "comment": 14, "blank": 51}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_player_inventory.php": {"language": "PHP", "code": 307, "comment": 26, "blank": 62}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_monsters.php": {"language": "PHP", "code": 286, "comment": 27, "blank": 63}, "file:///c%3A/Users/<USER>/Desktop/game/404.html": {"language": "HTML", "code": 7, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/game/.user.ini": {"language": "Ini", "code": 1, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/game/admin/pvp_battle_logs.css": {"language": "PostCSS", "code": 303, "comment": 8, "blank": 54}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_battle_logs.php": {"language": "PHP", "code": 346, "comment": 43, "blank": 68}, "file:///c%3A/Users/<USER>/Desktop/game/admin/player_inventory.css": {"language": "PostCSS", "code": 460, "comment": 17, "blank": 82}, "file:///c%3A/Users/<USER>/Desktop/game/admin/api_buildings.php": {"language": "PHP", "code": 204, "comment": 16, "blank": 47}, "file:///c%3A/Users/<USER>/Desktop/game/admin/player_inventory.php": {"language": "PHP", "code": 379, "comment": 4, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/game/admin/recipes.js": {"language": "JavaScript", "code": 233, "comment": 17, "blank": 42}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/README.md": {"language": "<PERSON><PERSON>", "code": 255, "comment": 0, "blank": 88}, "file:///c%3A/Users/<USER>/Desktop/game/admin/scenes.css": {"language": "PostCSS", "code": 756, "comment": 29, "blank": 94}, "file:///c%3A/Users/<USER>/Desktop/game/admin/player_inventory.js": {"language": "JavaScript", "code": 556, "comment": 60, "blank": 125}, "file:///c%3A/Users/<USER>/Desktop/game/admin/scenes.php": {"language": "PHP", "code": 250, "comment": 0, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/game/admin/scenes.js": {"language": "JavaScript", "code": 1120, "comment": 110, "blank": 220}, "file:///c%3A/Users/<USER>/Desktop/game/admin/scenes_bak.js": {"language": "JavaScript", "code": 740, "comment": 56, "blank": 148}, "file:///c%3A/Users/<USER>/Desktop/game/admin/refine_tiers.php": {"language": "PHP", "code": 449, "comment": 30, "blank": 71}, "file:///c%3A/Users/<USER>/Desktop/game/admin/refine_scores.php": {"language": "PHP", "code": 169, "comment": 7, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/game/admin/refine_combos.php": {"language": "PHP", "code": 205, "comment": 8, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/game/admin/refine_elements.php": {"language": "PHP", "code": 158, "comment": 7, "blank": 18}, "file:///c%3A/Users/<USER>/Desktop/game/admin/recipes.css": {"language": "PostCSS", "code": 93, "comment": 4, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/game/admin/refine_attribute_bonuses.php": {"language": "PHP", "code": 211, "comment": 7, "blank": 21}, "file:///c%3A/Users/<USER>/Desktop/game/admin/scene_npcs.php": {"language": "PHP", "code": 256, "comment": 23, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/game/admin/recipes.php": {"language": "PHP", "code": 242, "comment": 21, "blank": 35}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Worker.php": {"language": "PHP", "code": 1643, "comment": 871, "blank": 244}, "file:///c%3A/Users/<USER>/Desktop/game/admin/pvp_battle_logs.php": {"language": "PHP", "code": 158, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Timer.php": {"language": "PHP", "code": 112, "comment": 85, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/game/admin/pvp_battle_logs.js": {"language": "JavaScript", "code": 236, "comment": 20, "blank": 36}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Text.php": {"language": "PHP", "code": 26, "comment": 40, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Ws.php": {"language": "PHP", "code": 311, "comment": 94, "blank": 28}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/ProtocolInterface.php": {"language": "PHP", "code": 9, "comment": 39, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/admin/quests.php": {"language": "PHP", "code": 753, "comment": 31, "blank": 94}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Websocket.php": {"language": "PHP", "code": 395, "comment": 127, "blank": 41}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http.php": {"language": "PHP", "code": 212, "comment": 89, "blank": 23}, "file:///c%3A/Users/<USER>/Desktop/game/admin/style.css": {"language": "PostCSS", "code": 809, "comment": 17, "blank": 82}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Frame.php": {"language": "PHP", "code": 23, "comment": 34, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/admin/shop_config_modal.php": {"language": "PHP", "code": 38, "comment": 0, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/admin/quest_editor.php": {"language": "PHP", "code": 682, "comment": 42, "blank": 94}, "file:///c%3A/Users/<USER>/Desktop/game/admin/security_violations.css": {"language": "PostCSS", "code": 333, "comment": 30, "blank": 68}, "file:///c%3A/Users/<USER>/Desktop/game/admin/npcs.php": {"language": "PHP", "code": 1376, "comment": 90, "blank": 190}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Lib/Timer.php": {"language": "PHP", "code": 3, "comment": 18, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/admin/security_violations.php": {"language": "PHP", "code": 290, "comment": 14, "blank": 27}, "file:///c%3A/Users/<USER>/Desktop/game/admin/skills.php": {"language": "PHP", "code": 779, "comment": 30, "blank": 94}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Select.php": {"language": "PHP", "code": 218, "comment": 107, "blank": 33}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Session.php": {"language": "PHP", "code": 188, "comment": 226, "blank": 48}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/React/StreamSelectLoop.php": {"language": "PHP", "code": 9, "comment": 16, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/ServerSentEvents.php": {"language": "PHP", "code": 31, "comment": 30, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/React/ExtEventLoop.php": {"language": "PHP", "code": 9, "comment": 16, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Response.php": {"language": "PHP", "code": 244, "comment": 176, "blank": 39}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/React/Base.php": {"language": "PHP", "code": 131, "comment": 106, "blank": 28}, "file:///c%3A/Users/<USER>/Desktop/game/admin/monsters.php": {"language": "PHP", "code": 1045, "comment": 51, "blank": 148}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/React/ExtLibEventLoop.php": {"language": "PHP", "code": 10, "comment": 16, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Request.php": {"language": "PHP", "code": 412, "comment": 237, "blank": 46}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Chunk.php": {"language": "PHP", "code": 14, "comment": 30, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Uv.php": {"language": "PHP", "code": 148, "comment": 90, "blank": 23}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Swoole.php": {"language": "PHP", "code": 205, "comment": 60, "blank": 19}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Libevent.php": {"language": "PHP", "code": 132, "comment": 69, "blank": 25}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/EventInterface.php": {"language": "PHP", "code": 17, "comment": 78, "blank": 13}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Event.php": {"language": "PHP", "code": 121, "comment": 69, "blank": 26}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Events/Ev.php": {"language": "PHP", "code": 108, "comment": 68, "blank": 14}, "file:///c%3A/Users/<USER>/Desktop/game/admin/loot_tables.js": {"language": "JavaScript", "code": 28, "comment": 1, "blank": 3}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Lib/Constants.php": {"language": "PHP", "code": 21, "comment": 18, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/composer.json": {"language": "JSON", "code": 38, "comment": 0, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/admin/loot_tables.php": {"language": "PHP", "code": 336, "comment": 9, "blank": 45}, "file:///c%3A/Users/<USER>/Desktop/game/admin/logout.php": {"language": "PHP", "code": 13, "comment": 2, "blank": 4}, "file:///c%3A/Users/<USER>/Desktop/game/admin/items.php": {"language": "PHP", "code": 178, "comment": 15, "blank": 24}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Autoloader.php": {"language": "PHP", "code": 32, "comment": 32, "blank": 5}, "file:///c%3A/Users/<USER>/Desktop/game/admin/layout_footer.php": {"language": "PHP", "code": 12, "comment": 0, "blank": 0}, "file:///c%3A/Users/<USER>/Desktop/game/admin/gem_recipes_enhanced.css": {"language": "PostCSS", "code": 489, "comment": 19, "blank": 88}, "file:///c%3A/Users/<USER>/Desktop/game/admin/index.php": {"language": "PHP", "code": 71, "comment": 1, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/game/admin/install_groups.php": {"language": "PHP", "code": 226, "comment": 24, "blank": 27}, "file:///c%3A/Users/<USER>/Desktop/game/admin/items.js": {"language": "JavaScript", "code": 475, "comment": 130, "blank": 78}, "file:///c%3A/Users/<USER>/Desktop/game/admin/gem_recipes.js": {"language": "JavaScript", "code": 342, "comment": 9, "blank": 12}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/.github/FUNDING.yml": {"language": "YAML", "code": 2, "comment": 1, "blank": 2}, "file:///c%3A/Users/<USER>/Desktop/game/admin/gem_recipes.php": {"language": "PHP", "code": 222, "comment": 10, "blank": 29}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Session/RedisSessionHandler.php": {"language": "PHP", "code": 80, "comment": 55, "blank": 20}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Session/SessionHandlerInterface.php": {"language": "PHP", "code": 12, "comment": 93, "blank": 10}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Connection/UdpConnection.php": {"language": "PHP", "code": 91, "comment": 101, "blank": 17}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Connection/TcpConnection.php": {"language": "PHP", "code": 517, "comment": 380, "blank": 87}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Session/RedisClusterSessionHandler.php": {"language": "PHP", "code": 26, "comment": 15, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Connection/ConnectionInterface.php": {"language": "PHP", "code": 25, "comment": 87, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Protocols/Http/Session/FileSessionHandler.php": {"language": "PHP", "code": 92, "comment": 76, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Connection/AsyncTcpConnection.php": {"language": "PHP", "code": 222, "comment": 127, "blank": 30}, "file:///c%3A/Users/<USER>/Desktop/game/vendor/workerman/workerman/Connection/AsyncUdpConnection.php": {"language": "PHP", "code": 115, "comment": 71, "blank": 18}, "file:///c%3A/Users/<USER>/Desktop/game/admin/layout_header.php": {"language": "PHP", "code": 92, "comment": 5, "blank": 7}, "file:///c%3A/Users/<USER>/Desktop/game/admin/clear_cache.php": {"language": "PHP", "code": 45, "comment": 16, "blank": 15}, "file:///c%3A/Users/<USER>/Desktop/game/admin/clear_server_cache.php": {"language": "PHP", "code": 25, "comment": 6, "blank": 6}, "file:///c%3A/Users/<USER>/Desktop/game/admin/dashboard.php": {"language": "PHP", "code": 84, "comment": 9, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/game/admin/buildings.js": {"language": "JavaScript", "code": 263, "comment": 32, "blank": 35}, "file:///c%3A/Users/<USER>/Desktop/game/admin/npc_dialogues.php": {"language": "PHP", "code": 735, "comment": 55, "blank": 99}, "file:///c%3A/Users/<USER>/Desktop/game/admin/items.css": {"language": "PostCSS", "code": 383, "comment": 8, "blank": 60}, "file:///c%3A/Users/<USER>/Desktop/game/admin/battle_logs.js": {"language": "JavaScript", "code": 229, "comment": 19, "blank": 37}, "file:///c%3A/Users/<USER>/Desktop/game/admin/dialogue_editor.php": {"language": "PHP", "code": 1011, "comment": 73, "blank": 131}, "file:///c%3A/Users/<USER>/Desktop/game/admin/buildings.php": {"language": "PHP", "code": 300, "comment": 2, "blank": 32}, "file:///c%3A/Users/<USER>/Desktop/game/admin/battle_logs.php": {"language": "PHP", "code": 157, "comment": 0, "blank": 11}, "file:///c%3A/Users/<USER>/Desktop/game/admin/pinyin.js": {"language": "JavaScript", "code": 2, "comment": 1, "blank": 1}, "file:///c%3A/Users/<USER>/Desktop/game/admin/dialogues.php": {"language": "PHP", "code": 689, "comment": 39, "blank": 91}}