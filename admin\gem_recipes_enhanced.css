/* Gem Recipes Enhanced Styles */

/* Container and Layout */
.container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 20px;
}

/* Header Section */
.header-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #e9ecef;
}

.header-section h1 {
    margin: 0;
    color: #2c3e50;
    font-size: 2rem;
    font-weight: 600;
}

.header-actions {
    display: flex;
    gap: 10px;
}

/* Filters Section */
.filters-section {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 25px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.search-box {
    max-width: 400px;
}

.search-box .form-control {
    padding: 12px 16px;
    border: 1px solid #ced4da;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.search-box .form-control:focus {
    border-color: #80bdff;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* Recipes List */
.recipes-list {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Table Styles */
.table-responsive {
    border-radius: 8px;
    overflow: hidden;
}

.table {
    width: 100%;
    margin-bottom: 0;
    border-collapse: collapse;
    font-size: 0.9rem;
}

.table thead th {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    font-weight: 600;
    padding: 16px 12px;
    text-align: left;
    border: none;
    position: sticky;
    top: 0;
    z-index: 10;
}

.table tbody td {
    padding: 14px 12px;
    border-bottom: 1px solid #e9ecef;
    vertical-align: middle;
}

.table tbody tr {
    transition: background-color 0.15s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody tr:nth-child(even) {
    background-color: rgba(0,0,0,0.02);
}

/* Column Specific Styles */
.table td:first-child {
    font-weight: 600;
    color: #495057;
    min-width: 60px;
}

.table td:nth-child(2) {
    font-weight: 500;
    color: #2c3e50;
    min-width: 150px;
}

.table td:nth-child(3) {
    min-width: 120px;
}

.table td:nth-child(4),
.table td:nth-child(5) {
    text-align: center;
    min-width: 80px;
    font-weight: 500;
}

.table td:nth-child(6) {
    text-align: center;
    min-width: 100px;
}

.table td:last-child {
    text-align: center;
    min-width: 120px;
}

/* Button Styles */
.btn {
    display: inline-block;
    font-weight: 500;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 6px;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.btn-primary {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-secondary {
    color: #6c757d;
    background-color: #f8f9fa;
    border-color: #ced4da;
}

.btn-secondary:hover {
    color: #545b62;
    background-color: #e2e6ea;
    border-color: #adb5bd;
}

.btn-success {
    color: #fff;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border-color: #28a745;
}

.btn-success:hover {
    background: linear-gradient(135deg, #218838 0%, #1ea085 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-warning {
    color: #212529;
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
    border-color: #ffc107;
}

.btn-warning:hover {
    background: linear-gradient(135deg, #e0a800 0%, #e8650e 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-danger {
    color: #fff;
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
    border-color: #dc3545;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #c82333 0%, #d91a72 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
    border-radius: 4px;
}

/* Action Buttons Container */
.action-buttons {
    display: flex;
    gap: 6px;
    justify-content: center;
}

/* Pagination */
.pagination-container {
    padding: 20px;
    background: #f8f9fa;
    border-top: 1px solid #e9ecef;
}

.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    margin: 0;
    padding: 0;
    list-style: none;
}

.pagination .page-item {
    margin: 0;
}

.pagination .page-link {
    display: block;
    padding: 8px 12px;
    color: #667eea;
    text-decoration: none;
    background-color: #fff;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.15s ease;
}

.pagination .page-link:hover {
    color: #495057;
    background-color: #e9ecef;
    border-color: #adb5bd;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    color: #fff;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* Loading State */
.loading {
    text-align: center;
    padding: 40px;
    color: #6c757d;
    font-style: italic;
}

.loading::after {
    content: '';
    display: inline-block;
    width: 20px;
    height: 20px;
    margin-left: 10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 0.35em 0.65em;
    font-size: 0.75em;
    font-weight: 600;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.375rem;
}

.badge-primary {
    color: #fff;
    background-color: #667eea;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-info {
    color: #fff;
    background-color: #17a2b8;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header-section {
        flex-direction: column;
        gap: 15px;
        align-items: flex-start;
    }
    
    .header-section h1 {
        font-size: 1.5rem;
    }
    
    .table {
        font-size: 0.8rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 10px 8px;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 4px;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.7rem;
    }
}

@media (max-width: 576px) {
    .search-box {
        max-width: 100%;
    }

    .filters-section {
        padding: 15px;
    }

    .table-responsive {
        font-size: 0.75rem;
    }
}

/* Enhanced Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1050;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.6);
    backdrop-filter: blur(3px);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    justify-content: center;
    align-items: center;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background-color: #fff;
    border-radius: 12px;
    width: 90%;
    max-width: 700px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    animation: slideIn 0.3s ease;
}

@keyframes slideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    padding: 20px 25px;
    border-radius: 12px 12px 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.modal-header .close {
    color: #fff;
    font-size: 1.5rem;
    font-weight: bold;
    cursor: pointer;
    background: none;
    border: none;
    padding: 5px;
    border-radius: 50%;
    transition: background-color 0.2s ease;
}

.modal-header .close:hover {
    background-color: rgba(255,255,255,0.2);
}

.modal-body {
    padding: 25px;
}

/* Form Styles */
.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 600;
    color: #495057;
    font-size: 0.9rem;
}

.form-control {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e9ecef;
    border-radius: 6px;
    font-size: 0.9rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    background-color: #fff;
}

.form-control:focus {
    border-color: #667eea;
    outline: 0;
    box-shadow: 0 0 0 0.2rem rgba(102,126,234,0.25);
}

.form-control:invalid {
    border-color: #dc3545;
}

/* Material Row Styles */
.material-row {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.material-row .material-select {
    flex: 1;
    min-width: 200px;
}

.material-row .quantity-input {
    width: 100px;
    flex-shrink: 0;
}

.material-row .remove-material-btn {
    flex-shrink: 0;
    padding: 8px 12px;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e9ecef;
}

/* Materials Container */
#materials-container {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    min-height: 60px;
}

#materials-container:empty::before {
    content: '点击下方按钮添加宝石材料';
    color: #6c757d;
    font-style: italic;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 40px;
}

#add-material-btn {
    width: 100%;
    padding: 10px;
    border: 2px dashed #667eea;
    background-color: transparent;
    color: #667eea;
    border-radius: 6px;
    transition: all 0.2s ease;
}

#add-material-btn:hover {
    background-color: #667eea;
    color: #fff;
    transform: translateY(-1px);
}

/* Responsive Modal */
@media (max-width: 768px) {
    .modal-content {
        width: 95%;
        margin: 10px;
    }

    .modal-body {
        padding: 20px;
    }

    .material-row {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .material-row .quantity-input {
        width: 100%;
    }

    .form-actions {
        flex-direction: column;
    }
}
