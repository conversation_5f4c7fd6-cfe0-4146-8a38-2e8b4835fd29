<?php
require_once 'auth.php';
require_once '../config/Database.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $pdo = $db->getConnection();
    $action = $_POST['action'] ?? $_GET['action'] ?? '';
    $response = ['success' => false, 'message' => '未知操作'];

    switch ($action) {
        case 'get_continents':
            $stmt = $pdo->prepare("
                SELECT c.*,
                       COUNT(DISTINCT z.id) as zone_count,
                       COUNT(DISTINCT s.id) as scene_count
                FROM continents c
                LEFT JOIN zones z ON c.id = z.continent
                LEFT JOIN scenes s ON z.id = s.zone_id
                GROUP BY c.id
                ORDER BY c.z_level, c.sort_order, c.id
            ");
            $stmt->execute();
            $continents = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            $response = [
                'success' => true,
                'continents' => $continents
            ];
            break;

        case 'get_zones':
            $continentId = $_POST['continent_id'] ?? $_GET['continent_id'] ?? '';
            if (empty($continentId)) {
                $response['message'] = '缺少大陆ID参数';
                break;
            }

            $stmt = $pdo->prepare("
                SELECT z.*, COUNT(s.id) as scene_count
                FROM zones z
                LEFT JOIN scenes s ON z.id = s.zone_id
                WHERE z.continent = ?
                GROUP BY z.id
                ORDER BY z.min_level, z.id
            ");
            $stmt->execute([$continentId]);
            $zones = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'zones' => $zones
            ];
            break;

        case 'get_all_zones':
            $stmt = $pdo->prepare("
                SELECT z.*, COUNT(s.id) as scene_count,
                       c.display_name as continent_name
                FROM zones z
                LEFT JOIN scenes s ON z.id = s.zone_id
                LEFT JOIN continents c ON z.continent = c.id
                GROUP BY z.id
                ORDER BY c.sort_order, z.min_level, z.id
            ");
            $stmt->execute();
            $zones = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'zones' => $zones
            ];
            break;

        case 'get_scenes':
            $zoneId = $_POST['zone_id'] ?? '';
            if (empty($zoneId)) {
                $response['message'] = '缺少区域ID参数';
                break;
            }

            $stmt = $pdo->prepare("
                SELECT s.id, s.name, s.description, s.x, s.y, s.z, s.is_safe_zone, s.max_players
                FROM scenes s
                WHERE s.zone_id = ?
                ORDER BY s.z, s.y, s.x
            ");
            $stmt->execute([$zoneId]);
            $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'scenes' => $scenes
            ];
            break;

        case 'get_next_z_level':
            $stmt = $pdo->prepare("SELECT COALESCE(MAX(z_level), -1) + 1 FROM continents");
            $stmt->execute();
            $nextZ = $stmt->fetchColumn();

            $response = [
                'success' => true,
                'next_z_level' => $nextZ
            ];
            break;

        case 'get_used_z_levels':
            $stmt = $pdo->prepare("SELECT z_level, id, display_name FROM continents ORDER BY z_level");
            $stmt->execute();
            $usedLevels = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = [
                'success' => true,
                'used_z_levels' => $usedLevels
            ];
            break;

        case 'sync_layers':
            // 同步所有大陆到scene_layers表
            $pdo->beginTransaction();
            try {
                // 获取所有大陆
                $stmt = $pdo->prepare("SELECT id, display_name, z_level FROM continents ORDER BY z_level");
                $stmt->execute();
                $continents = $stmt->fetchAll(PDO::FETCH_ASSOC);

                $syncCount = 0;
                foreach ($continents as $continent) {
                    $layerName = $continent['display_name'] . "层";
                    $layerDescription = "大陆\"{$continent['display_name']}\"的场景图层 (Z={$continent['z_level']})";

                    $stmt = $pdo->prepare("
                        INSERT INTO scene_layers (z, name, description, created_at)
                        VALUES (?, ?, ?, NOW())
                        ON DUPLICATE KEY UPDATE
                        name = VALUES(name),
                        description = VALUES(description),
                        updated_at = NOW()
                    ");
                    $stmt->execute([$continent['z_level'], $layerName, $layerDescription]);
                    $syncCount++;
                }

                $pdo->commit();
                $response = [
                    'success' => true,
                    'message' => "成功同步 {$syncCount} 个大陆图层"
                ];
            } catch (Exception $e) {
                $pdo->rollback();
                $response['message'] = '同步失败：' . $e->getMessage();
            }
            break;

        case 'create_continent':
            $id = trim($_POST['id'] ?? '');
            $displayName = trim($_POST['display_name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $color = $_POST['color'] ?? '#007bff';
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            $isActive = (int)($_POST['is_active'] ?? 1);
            // 处理Z坐标输入：空字符串或未设置都视为自动分配
            $zLevel = null;
            if (isset($_POST['z_level'])) {
                $zLevelInput = trim($_POST['z_level']);
                if ($zLevelInput !== '' && is_numeric($zLevelInput)) {
                    $zLevel = (int)$zLevelInput;
                    // 验证Z坐标范围
                    if ($zLevel < 0) {
                        $response['message'] = 'Z坐标不能为负数';
                        break;
                    }
                }
            }

            if (empty($id) || empty($displayName)) {
                $response['message'] = '大陆ID和显示名称不能为空';
                break;
            }

            // 验证ID格式
            if (!preg_match('/^[a-z_]+$/', $id)) {
                $response['message'] = '大陆ID只能包含小写字母和下划线';
                break;
            }

            // 检查ID是否已存在
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM continents WHERE id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() > 0) {
                $response['message'] = '大陆ID已存在';
                break;
            }

            // 如果没有指定Z坐标，获取下一个可用的Z坐标
            if ($zLevel === null) {
                $stmt = $pdo->prepare("SELECT COALESCE(MAX(z_level), -1) + 1 FROM continents");
                $stmt->execute();
                $zLevel = $stmt->fetchColumn();
            } else {
                // 检查Z坐标是否已被使用
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM continents WHERE z_level = ?");
                $stmt->execute([$zLevel]);
                if ($stmt->fetchColumn() > 0) {
                    $response['message'] = "Z坐标 {$zLevel} 已被其他大陆使用";
                    break;
                }
            }

            $pdo->beginTransaction();
            try {
                // 创建大陆
                $stmt = $pdo->prepare("
                    INSERT INTO continents (id, name, display_name, description, color, sort_order, z_level, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                ");
                $stmt->execute([$id, $displayName, $displayName, $description, $color, $sortOrder, $zLevel, $isActive]);

                // 创建对应的场景图层记录
                $layerName = $displayName . "层";
                $layerDescription = "大陆\"{$displayName}\"的场景图层 (Z={$zLevel})";

                $stmt = $pdo->prepare("
                    INSERT INTO scene_layers (z, name, description, created_at)
                    VALUES (?, ?, ?, NOW())
                    ON DUPLICATE KEY UPDATE
                    name = VALUES(name),
                    description = VALUES(description),
                    updated_at = NOW()
                ");
                $stmt->execute([$zLevel, $layerName, $layerDescription]);

                $pdo->commit();
                $response = [
                    'success' => true,
                    'message' => '大陆创建成功，图层已同步'
                ];
            } catch (Exception $e) {
                $pdo->rollback();
                $response['message'] = '创建失败：' . $e->getMessage();
            }
            break;

        case 'update_continent':
            $id = trim($_POST['id'] ?? '');
            $displayName = trim($_POST['display_name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $color = $_POST['color'] ?? '#007bff';
            $sortOrder = (int)($_POST['sort_order'] ?? 0);
            $isActive = (int)($_POST['is_active'] ?? 1);
            // 处理Z坐标输入：空字符串或未设置都视为不修改
            $zLevel = null;
            if (isset($_POST['z_level'])) {
                $zLevelInput = trim($_POST['z_level']);
                if ($zLevelInput !== '' && is_numeric($zLevelInput)) {
                    $zLevel = (int)$zLevelInput;
                    // 验证Z坐标范围
                    if ($zLevel < 0) {
                        $response['message'] = 'Z坐标不能为负数';
                        break;
                    }
                }
            }

            if (empty($id) || empty($displayName)) {
                $response['message'] = '大陆ID和显示名称不能为空';
                break;
            }

            // 获取当前大陆的Z坐标
            $stmt = $pdo->prepare("SELECT z_level FROM continents WHERE id = ?");
            $stmt->execute([$id]);
            $currentZLevel = $stmt->fetchColumn();

            if ($currentZLevel === false) {
                $response['message'] = '大陆不存在';
                break;
            }

            // 如果要修改Z坐标
            if ($zLevel !== null && $zLevel != $currentZLevel) {
                // 检查新Z坐标是否已被使用
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM continents WHERE z_level = ? AND id != ?");
                $stmt->execute([$zLevel, $id]);
                if ($stmt->fetchColumn() > 0) {
                    $response['message'] = "Z坐标 {$zLevel} 已被其他大陆使用";
                    break;
                }

                // 检查当前Z层是否有场景
                $stmt = $pdo->prepare("SELECT COUNT(*) FROM scenes WHERE z = ?");
                $stmt->execute([$currentZLevel]);
                if ($stmt->fetchColumn() > 0) {
                    $response['message'] = "当前Z层 {$currentZLevel} 存在场景，无法修改Z坐标";
                    break;
                }

                $pdo->beginTransaction();
                try {
                    // 更新包含Z坐标
                    $stmt = $pdo->prepare("
                        UPDATE continents
                        SET name = ?, display_name = ?, description = ?, color = ?, sort_order = ?, z_level = ?, is_active = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$displayName, $displayName, $description, $color, $sortOrder, $zLevel, $isActive, $id]);

                    // 更新旧图层名称（如果存在）
                    $layerName = $displayName . "层";
                    $layerDescription = "大陆\"{$displayName}\"的场景图层 (Z={$currentZLevel})";
                    $stmt = $pdo->prepare("
                        UPDATE scene_layers
                        SET name = ?, description = ?, updated_at = NOW()
                        WHERE z = ?
                    ");
                    $stmt->execute([$layerName, $layerDescription, $currentZLevel]);

                    // 如果Z坐标发生变化，需要处理图层迁移
                    if ($zLevel != $currentZLevel) {
                        // 删除旧图层记录
                        $stmt = $pdo->prepare("DELETE FROM scene_layers WHERE z = ?");
                        $stmt->execute([$currentZLevel]);

                        // 创建新图层记录
                        $newLayerDescription = "大陆\"{$displayName}\"的场景图层 (Z={$zLevel})";
                        $stmt = $pdo->prepare("
                            INSERT INTO scene_layers (z, name, description, created_at)
                            VALUES (?, ?, ?, NOW())
                            ON DUPLICATE KEY UPDATE
                            name = VALUES(name),
                            description = VALUES(description),
                            updated_at = NOW()
                        ");
                        $stmt->execute([$zLevel, $layerName, $newLayerDescription]);
                    }

                    $pdo->commit();
                } catch (Exception $e) {
                    $pdo->rollback();
                    throw $e;
                }
            } else {
                $pdo->beginTransaction();
                try {
                    // 不修改Z坐标，只更新大陆信息
                    $stmt = $pdo->prepare("
                        UPDATE continents
                        SET name = ?, display_name = ?, description = ?, color = ?, sort_order = ?, is_active = ?
                        WHERE id = ?
                    ");
                    $stmt->execute([$displayName, $displayName, $description, $color, $sortOrder, $isActive, $id]);

                    // 更新图层名称和描述
                    $layerName = $displayName . "层";
                    $layerDescription = "大陆\"{$displayName}\"的场景图层 (Z={$currentZLevel})";
                    $stmt = $pdo->prepare("
                        UPDATE scene_layers
                        SET name = ?, description = ?, updated_at = NOW()
                        WHERE z = ?
                    ");
                    $stmt->execute([$layerName, $layerDescription, $currentZLevel]);

                    $pdo->commit();
                } catch (Exception $e) {
                    $pdo->rollback();
                    throw $e;
                }
            }
            
            if ($stmt->rowCount() > 0) {
                $response = [
                    'success' => true,
                    'message' => '大陆更新成功'
                ];
            } else {
                $response['message'] = '大陆不存在或无变化';
            }
            break;

        case 'delete_continent':
            $id = trim($_POST['id'] ?? '');
            if (empty($id)) {
                $response['message'] = '缺少大陆ID参数';
                break;
            }

            // 获取大陆的Z坐标
            $stmt = $pdo->prepare("SELECT z_level FROM continents WHERE id = ?");
            $stmt->execute([$id]);
            $zLevel = $stmt->fetchColumn();

            if ($zLevel === false) {
                $response['message'] = '大陆不存在';
                break;
            }

            // 检查该Z层是否有场景
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM scenes WHERE z = ?");
            $stmt->execute([$zLevel]);
            $sceneCount = $stmt->fetchColumn();

            if ($sceneCount > 0) {
                $response['message'] = "无法删除大陆：Z层 {$zLevel} 存在 {$sceneCount} 个场景，请先删除所有场景";
                break;
            }

            $pdo->beginTransaction();
            try {
                // 删除相关区域（应该没有场景了）
                $stmt = $pdo->prepare("DELETE FROM zones WHERE continent = ?");
                $stmt->execute([$id]);

                // 删除大陆
                $stmt = $pdo->prepare("DELETE FROM continents WHERE id = ?");
                $stmt->execute([$id]);

                if ($stmt->rowCount() > 0) {
                    // 删除对应的图层记录
                    $stmt = $pdo->prepare("DELETE FROM scene_layers WHERE z = ?");
                    $stmt->execute([$zLevel]);

                    $pdo->commit();
                    $response = [
                        'success' => true,
                        'message' => '大陆删除成功，图层已同步'
                    ];
                } else {
                    $pdo->rollback();
                    $response['message'] = '大陆不存在';
                }
            } catch (Exception $e) {
                $pdo->rollback();
                $response['message'] = '删除失败：' . $e->getMessage();
            }
            break;

        case 'create_zone':
            $id = trim($_POST['id'] ?? '');
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $continent = trim($_POST['continent'] ?? '');
            $minLevel = (int)($_POST['min_level'] ?? 1);
            $maxLevel = !empty($_POST['max_level']) ? (int)$_POST['max_level'] : null;
            
            if (empty($id) || empty($name) || empty($continent)) {
                $response['message'] = '区域ID、名称和所属大陆不能为空';
                break;
            }
            
            // 验证ID格式
            if (!preg_match('/^[a-z0-9_]+$/', $id)) {
                $response['message'] = '区域ID只能包含小写字母、数字和下划线';
                break;
            }
            
            // 检查ID是否已存在
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM zones WHERE id = ?");
            $stmt->execute([$id]);
            if ($stmt->fetchColumn() > 0) {
                $response['message'] = '区域ID已存在';
                break;
            }

            // 检查大陆是否存在
            $stmt = $pdo->prepare("SELECT COUNT(*) FROM continents WHERE id = ?");
            $stmt->execute([$continent]);
            if ($stmt->fetchColumn() == 0) {
                $response['message'] = '指定的大陆不存在';
                break;
            }

            $stmt = $pdo->prepare("
                INSERT INTO zones (id, name, description, continent, min_level, max_level)
                VALUES (?, ?, ?, ?, ?, ?)
            ");
            $stmt->execute([$id, $name, $description, $continent, $minLevel, $maxLevel]);
            
            $response = [
                'success' => true,
                'message' => '区域创建成功'
            ];
            break;

        case 'update_zone':
            $id = trim($_POST['id'] ?? '');
            $name = trim($_POST['name'] ?? '');
            $description = trim($_POST['description'] ?? '');
            $continent = trim($_POST['continent'] ?? '');
            $minLevel = (int)($_POST['min_level'] ?? 1);
            $maxLevel = !empty($_POST['max_level']) ? (int)$_POST['max_level'] : null;
            
            if (empty($id) || empty($name) || empty($continent)) {
                $response['message'] = '区域ID、名称和所属大陆不能为空';
                break;
            }
            
            $stmt = $pdo->prepare("
                UPDATE zones
                SET name = ?, description = ?, continent = ?, min_level = ?, max_level = ?
                WHERE id = ?
            ");
            $stmt->execute([$name, $description, $continent, $minLevel, $maxLevel, $id]);
            
            if ($stmt->rowCount() > 0) {
                $response = [
                    'success' => true,
                    'message' => '区域更新成功'
                ];
            } else {
                $response['message'] = '区域不存在或无变化';
            }
            break;

        case 'delete_zone':
            $id = trim($_POST['id'] ?? '');
            if (empty($id)) {
                $response['message'] = '缺少区域ID参数';
                break;
            }
            
            $pdo->beginTransaction();
            try {
                // 删除相关场景
                $stmt = $pdo->prepare("DELETE FROM scenes WHERE zone_id = ?");
                $stmt->execute([$id]);

                // 删除区域
                $stmt = $pdo->prepare("DELETE FROM zones WHERE id = ?");
                $stmt->execute([$id]);

                if ($stmt->rowCount() > 0) {
                    $pdo->commit();
                    $response = [
                        'success' => true,
                        'message' => '区域删除成功'
                    ];
                } else {
                    $pdo->rollback();
                    $response['message'] = '区域不存在';
                }
            } catch (Exception $e) {
                $pdo->rollback();
                $response['message'] = '删除失败：' . $e->getMessage();
            }
            break;

        default:
            $response['message'] = '不支持的操作：' . $action;
            break;
    }

} catch (Exception $e) {
    $response = [
        'success' => false,
        'message' => '服务器错误：' . $e->getMessage()
    ];
}

echo json_encode($response, JSON_UNESCAPED_UNICODE);
?>
