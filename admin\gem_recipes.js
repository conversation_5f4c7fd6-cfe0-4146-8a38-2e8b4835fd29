document.addEventListener('DOMContentLoaded', function() {
    const API_URL = 'api_gem_recipes.php';

    // Modal elements
    const modal = document.getElementById('recipeModal');
    const modalTitle = document.getElementById('modalTitle');
    const recipeForm = document.getElementById('recipe-form');
    const materialsContainer = document.getElementById('materials-container');
    const resultItemSelect = document.getElementById('recipe-result_item_id');

    // Main page elements
    const tbody = document.getElementById('recipes-tbody');
    const paginationContainer = document.querySelector('.pagination');
    const searchInput = document.getElementById('search-input');
    const addRecipeBtn = document.getElementById('add-recipe-btn');
    const addMaterialBtn = document.getElementById('add-material');
    const searchButton = document.getElementById('search-button');

    let currentPage = 1;
    let currentSearch = '';

    // Initialize
    fetchRecipes();

    // Define hideRecipeModal function first
    function hideRecipeModal() {
        modal.style.display = 'none';
    }

    // Make it globally available
    window.hideRecipeModal = hideRecipeModal;

    // Event listeners
    addRecipeBtn.addEventListener('click', showAddModal);
    if (addMaterialBtn) addMaterialBtn.addEventListener('click', addMaterialRow);
    if (searchButton) searchButton.addEventListener('click', handleSearch);
    recipeForm.addEventListener('submit', handleFormSubmit);
    searchInput.addEventListener('input', debounce(handleSearch, 300));

    // Close modal when clicking outside
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            hideRecipeModal();
        }
    });

    // Close modal with X button
    const closeBtn = modal.querySelector('.modal-close');
    if (closeBtn) closeBtn.addEventListener('click', hideRecipeModal);

    // Close modal with cancel button
    const cancelBtn = document.getElementById('cancel-recipe-btn');
    if (cancelBtn) cancelBtn.addEventListener('click', hideRecipeModal);

    // Fetch and render recipes
    async function fetchRecipes(page = 1, search = '') {
        try {
            const response = await fetch(`${API_URL}?action=get_recipes&page=${page}&search=${search}`);
            const data = await response.json();
            if (data.success) {
                renderTable(data.recipes);
                renderPagination(data.pagination);
            } else {
                tbody.innerHTML = `<tr><td colspan="7">加载失败: ${data.message}</td></tr>`;
            }
        } catch (error) {
            tbody.innerHTML = `<tr><td colspan="7">加载出错: ${error.message}</td></tr>`;
        }
    }

    // Render table rows
    function renderTable(recipes) {
        tbody.innerHTML = '';

        // Update recipes count
        const countElement = document.getElementById('recipes-count');
        if (countElement) {
            countElement.textContent = `共 ${recipes.length} 个配方`;
        }

        if (recipes.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; padding: 40px; color: #6c757d;">
                        <i class="fas fa-search" style="font-size: 2rem; margin-bottom: 10px; display: block;"></i>
                        没有找到匹配的宝石配方
                    </td>
                </tr>
            `;
            return;
        }

        recipes.forEach(recipe => {
            const tr = document.createElement('tr');
            tr.innerHTML = `
                <td style="font-weight: 600; color: #495057;">
                    #${recipe.id}
                </td>
                <td style="font-weight: 500;">
                    <div style="color: #2c3e50;">${escapeHTML(recipe.name)}</div>
                    ${recipe.description ? `<small style="color: #6c757d; line-height: 1.3;">${escapeHTML(recipe.description)}</small>` : ''}
                </td>
                <td>
                    <div style="display: flex; align-items: center; gap: 8px;">
                        <i class="fas fa-gem" style="color: #e74c3c;"></i>
                        <span style="font-weight: 500;">${escapeHTML(recipe.item_name || 'N/A')}</span>
                    </div>
                </td>
                <td style="text-align: center;">
                    <span class="badge badge-primary">${recipe.result_quantity}</span>
                </td>
                <td style="text-align: center;">
                    <span class="badge badge-info">Lv.${recipe.craft_level}</span>
                </td>
                <td style="text-align: center;">
                    <span class="badge badge-success">${recipe.material_count} 种</span>
                </td>
                <td>
                    <div class="action-buttons">
                        <button class="btn btn-sm btn-warning edit-btn" data-id="${recipe.id}" title="编辑配方">
                            <i class="fas fa-edit"></i> 编辑
                        </button>
                        <button class="btn btn-sm btn-danger delete-btn" data-id="${recipe.id}" title="删除配方">
                            <i class="fas fa-trash"></i> 删除
                        </button>
                    </div>
                </td>
            `;
            tbody.appendChild(tr);
        });

        // Add event listeners to edit and delete buttons
        tbody.querySelectorAll('.edit-btn').forEach(btn => {
            btn.addEventListener('click', (e) => editRecipe(e.target.dataset.id));
        });
        tbody.querySelectorAll('.delete-btn').forEach(btn => {
            btn.addEventListener('click', (e) => deleteRecipe(e.target.dataset.id));
        });
    }

    // Render pagination controls
    function renderPagination(pagination) {
        paginationContainer.innerHTML = '';
        if (pagination.total_pages <= 1) return;

        // Create pagination wrapper
        const paginationWrapper = document.createElement('div');
        paginationWrapper.className = 'pagination';

        // Add pagination info
        const paginationInfo = document.createElement('div');
        paginationInfo.style.cssText = 'margin-right: 20px; color: #6c757d; font-size: 0.875rem; display: flex; align-items: center;';
        paginationInfo.innerHTML = `
            <i class="fas fa-info-circle" style="margin-right: 5px;"></i>
            第 ${pagination.current_page} 页，共 ${pagination.total_pages} 页
        `;
        paginationWrapper.appendChild(paginationInfo);

        // Previous button
        if (pagination.current_page > 1) {
            const prevButton = document.createElement('button');
            prevButton.innerHTML = '<i class="fas fa-chevron-left"></i> 上一页';
            prevButton.className = 'btn btn-sm btn-secondary';
            prevButton.addEventListener('click', () => {
                currentPage = pagination.current_page - 1;
                fetchRecipes(currentPage, currentSearch);
            });
            paginationWrapper.appendChild(prevButton);
        }

        // Page numbers (show max 5 pages around current)
        const startPage = Math.max(1, pagination.current_page - 2);
        const endPage = Math.min(pagination.total_pages, pagination.current_page + 2);

        for (let i = startPage; i <= endPage; i++) {
            const pageButton = document.createElement('button');
            pageButton.textContent = i;
            pageButton.className = 'btn btn-sm ' + (i === pagination.current_page ? 'btn-primary' : 'btn-secondary');
            pageButton.addEventListener('click', () => {
                currentPage = i;
                fetchRecipes(currentPage, currentSearch);
            });
            paginationWrapper.appendChild(pageButton);
        }

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            const nextButton = document.createElement('button');
            nextButton.innerHTML = '下一页 <i class="fas fa-chevron-right"></i>';
            nextButton.className = 'btn btn-sm btn-secondary';
            nextButton.addEventListener('click', () => {
                currentPage = pagination.current_page + 1;
                fetchRecipes(currentPage, currentSearch);
            });
            paginationWrapper.appendChild(nextButton);
        }

        paginationContainer.appendChild(paginationWrapper);
    }

    // Show add modal
    function showAddModal() {
        modalTitle.textContent = '宝石配方信息';
        recipeForm.reset();
        document.getElementById('recipe-id').value = '';
        materialsContainer.innerHTML = '';
        addMaterialRow(); // Add one material row by default
        modal.style.display = 'block';
    }

    // Show edit modal
    async function editRecipe(id) {
        try {
            const response = await fetch(`${API_URL}?action=get_recipe&id=${id}`);
            const data = await response.json();
            if (data.success) {
                const recipe = data.recipe;
                modalTitle.textContent = '宝石配方信息';
                document.getElementById('recipe-id').value = recipe.id;
                document.getElementById('recipe-name').value = recipe.name;
                document.getElementById('recipe-result_item_id').value = recipe.result_item_id;
                document.getElementById('recipe-result_quantity').value = recipe.result_quantity;
                document.getElementById('recipe-craft_level').value = recipe.craft_level;
                document.getElementById('recipe-description').value = recipe.description || '';

                // Clear and populate materials
                materialsContainer.innerHTML = '';
                if (recipe.materials && recipe.materials.length > 0) {
                    recipe.materials.forEach(material => {
                        addMaterialRow(material.material_item_id, material.quantity);
                    });
                } else {
                    addMaterialRow(); // Add one empty row
                }

                modal.style.display = 'block';
            } else {
                alert('获取配方详情失败: ' + data.message);
            }
        } catch (error) {
            alert('获取配方详情出错: ' + error.message);
        }
    }

    // Delete recipe
    async function deleteRecipe(id) {
        if (!confirm('确定要删除这个宝石配方吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await fetch(`${API_URL}?action=delete_recipe`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ id: parseInt(id) })
            });
            const data = await response.json();
            if (data.success) {
                alert(data.message);
                fetchRecipes(currentPage, currentSearch);
            } else {
                alert('删除失败: ' + data.message);
            }
        } catch (error) {
            alert('删除出错: ' + error.message);
        }
    }

    // Add material row
    function addMaterialRow(selectedItemId = '', quantity = 1) {
        const materialRow = document.createElement('div');
        materialRow.className = 'material-row';

        materialRow.innerHTML = `
            <select name="material_ids[]" class="material-select">
                <option value="">选择材料</option>
                ${gemItemsData.map(item =>
                    `<option value="${item.id}" ${selectedItemId == item.id ? 'selected' : ''}>${escapeHTML(item.name)} [${item.category}]</option>`
                ).join('')}
            </select>
            <input type="number" name="material_quantities[]" min="1" value="${quantity}" placeholder="数量" required>
            <button type="button" class="btn btn-danger remove-material">删除</button>
        `;

        // Add remove event listener
        materialRow.querySelector('.remove-material').addEventListener('click', function() {
            materialRow.remove();
        });

        materialsContainer.appendChild(materialRow);
    }

    // Handle form submit
    async function handleFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(recipeForm);
        const materials = [];

        // Collect materials
        const materialSelects = materialsContainer.querySelectorAll('select[name="material_ids[]"]');
        const quantityInputs = materialsContainer.querySelectorAll('input[name="material_quantities[]"]');

        for (let i = 0; i < materialSelects.length; i++) {
            const materialId = materialSelects[i].value;
            const quantity = parseInt(quantityInputs[i].value);
            
            if (materialId && quantity > 0) {
                materials.push({
                    material_item_id: parseInt(materialId),
                    quantity: quantity
                });
            }
        }

        const requestData = {
            id: parseInt(formData.get('id')) || 0,
            name: formData.get('name'),
            result_item_id: parseInt(formData.get('result_item_id')),
            result_quantity: parseInt(formData.get('result_quantity')),
            craft_level: parseInt(formData.get('craft_level')),
            description: formData.get('description'),
            materials: materials
        };

        try {
            const response = await fetch(`${API_URL}?action=save_recipe`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(requestData)
            });
            const data = await response.json();
            if (data.success) {
                alert(data.message);
                closeModal();
                fetchRecipes(currentPage, currentSearch);
            } else {
                alert('保存失败: ' + data.message);
            }
        } catch (error) {
            alert('保存出错: ' + error.message);
        }
    }

    // Handle search
    function handleSearch() {
        currentSearch = searchInput.value.trim();
        currentPage = 1;
        fetchRecipes(currentPage, currentSearch);
    }

    // Close modal
    function closeModal() {
        modal.style.display = 'none';
    }



    // Global function for edit recipe
    window.editRecipe = editRecipe;

    // Utility functions
    function escapeHTML(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});
