<?php
// classes/LootManager.php

class LootManager {
    private $db;

    public function __construct(Database $db, RedisManager $redisManager) {
        $this->db = $db;
    }

    /**
     * Handles all drop logic when a monster is killed.
     * @param array $monster The defeated monster's data.
     * @param array $playersInBattle An array of player data who were in the battle.
     * @param string $sceneId The scene ID where the battle occurred.
     * @return array The list of items that were dropped with their protection info.
     */
    public function handleMonsterDrop($monster, $playersInBattle, $sceneId) {
        $monsterTemplateId = $monster['monster_template_id'] ?? null;
        
        if (!$monsterTemplateId) {
            error_log("掉落逻辑失败: 无法在战斗数据中确定 monster_template_id。");
            return [];
        }

        $stmt = $this->db->query("SELECT loot_table_id FROM monster_templates WHERE id = ?", [$monsterTemplateId]);
        $lootTableId = $stmt->fetchColumn();
        $stmt->closeCursor();

        if (!$lootTableId) {
            return [];
        }

        $stmt = $this->db->query(
            "SELECT lte.*, it.name 
             FROM loot_table_entries lte
             JOIN item_templates it ON lte.item_template_id = it.id
             WHERE lte.loot_table_id = ?", 
            [$lootTableId]
        );
        $lootEntries = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        $droppedItems = [];
        $itemProtections = []; // 记录每个物品的保护玩家ID
        
        foreach ($lootEntries as $entry) {
            $roll = mt_rand(1, 10000) / 100; // Roll from 0.01 to 100.00
            if ($roll <= $entry['drop_chance']) {
                $quantity = mt_rand($entry['min_quantity'], $entry['max_quantity']);
                $droppedItems[] = [
                    'item_template_id' => $entry['item_template_id'],
                    'quantity' => $quantity,
                    'name' => $entry['name']
                ];
            }
        }
        
        if (!empty($droppedItems)) {
            // 获取伤害贡献数据
            $damageContributions = $monster['damage_contributions'] ?? [];
            
            // 如果没有伤害贡献数据，则所有玩家平等分配
            if (empty($damageContributions)) {
                $playerIds = array_keys($playersInBattle);
                $itemProtections = $this->dropItemsToScene($sceneId, $droppedItems, $playerIds);
            } else {
                // 根据伤害贡献分配掉落物
                $itemProtections = $this->dropItemsToSceneWithContribution($sceneId, $droppedItems, $playersInBattle, $damageContributions);
            }
        }

        // 返回掉落物品和保护信息
        $result = [];
        foreach ($droppedItems as $index => $item) {
            $protectedPlayerId = $itemProtections[$index] ?? null;
            $result[] = [
                'item_template_id' => $item['item_template_id'],
                'quantity' => $item['quantity'],
                'name' => $item['name'],
                'protected_player_id' => $protectedPlayerId
            ];
        }
        
        return $result;
    }

    /**
     * Adds items to the scene (database) and sets protection (Redis).
     * @param string $sceneId The ID of the scene where items are dropped.
     * @param array $items An array of items to drop.
     * @param array $protectedForPlayerIds An array of player IDs who get pickup protection.
     * @return array Array of player IDs who got protection for each item, indexed by item position.
     */
    private function dropItemsToScene($sceneId, $items, $protectedForPlayerIds) {
        $conn = $this->db->getConnection();
        
        $protections = [];
        
        RedisManager::getInstance()->with(function($redis) use ($conn, $sceneId, $items, $protectedForPlayerIds, &$protections) {
            $redisPipe = $redis->pipeline();

            foreach ($items as $index => $item) {
                $stmt = $conn->prepare(
                    "INSERT INTO scene_items (scene_id, item_template_id, quantity, dropped_at) VALUES (?, ?, ?, NOW())"
                );
                $stmt->execute([$sceneId, $item['item_template_id'], $item['quantity']]);
                $sceneItemId = $conn->lastInsertId();

                // Set 5-minute protection in Redis for the players
                if ($sceneItemId && !empty($protectedForPlayerIds)) {
                    $protectionKey = "scene_item_protection:{$sceneItemId}";
                    $redisPipe->sadd($protectionKey, ...$protectedForPlayerIds);
                    $redisPipe->expire($protectionKey, 300); // 5 minutes
                    
                    // 随机选择一个玩家作为物品保护者
                    $selectedPlayerId = $protectedForPlayerIds[array_rand($protectedForPlayerIds)];
                    $protections[$index] = $selectedPlayerId;
                }
            }
            $redisPipe->exec();
        });
        
        return $protections;
    }
    
    /**
     * Adds items to the scene with protection based on damage contribution.
     * @param string $sceneId The ID of the scene where items are dropped.
     * @param array $items An array of items to drop.
     * @param array $playersInBattle An array of player data who were in the battle.
     * @param array $damageContributions An array of damage contributions keyed by player ID.
     * @return array Array of player IDs who got protection for each item, indexed by item position.
     */
    private function dropItemsToSceneWithContribution($sceneId, $items, $playersInBattle, $damageContributions) {
        $conn = $this->db->getConnection();
        $protections = [];
        
        // 计算总伤害
        $totalDamage = array_sum($damageContributions);
        
        // 如果总伤害为0，则所有玩家平等分配
        if ($totalDamage <= 0) {
            $playerIds = array_keys($playersInBattle);
            return $this->dropItemsToScene($sceneId, $items, $playerIds);
        }
        
        // 计算每个玩家的伤害贡献比例
        $contributionRatios = [];
        foreach ($damageContributions as $playerId => $damage) {
            $contributionRatios[$playerId] = $damage / $totalDamage;
        }
        
        RedisManager::getInstance()->with(function($redis) use ($conn, $sceneId, $items, $playersInBattle, $contributionRatios, &$protections) {
            $redisPipe = $redis->pipeline();

            // 为每个掉落物分配保护玩家
            foreach ($items as $index => $item) {
                // 插入物品到场景
                $stmt = $conn->prepare(
                    "INSERT INTO scene_items (scene_id, item_template_id, quantity, instance_data, dropped_at) VALUES (?, ?, ?, ?, NOW())"
                );
                $stmt->execute([$sceneId, $item['item_template_id'], $item['quantity'], $item['instance_data'] ?? null]);
                $sceneItemId = $conn->lastInsertId();
                
                if (!$sceneItemId) continue;
                
                // 根据伤害贡献比例选择一个玩家获得保护
                $selectedPlayerId = $this->selectPlayerByContribution($contributionRatios);
                
                if ($selectedPlayerId) {
                    $protectionKey = "scene_item_protection:{$sceneItemId}";
                    $redisPipe->sadd($protectionKey, $selectedPlayerId);
                    $redisPipe->expire($protectionKey, 300); // 5 minutes protection
                    
                    // 记录该物品的保护玩家ID
                    $protections[$index] = $selectedPlayerId;
                }
            }
            
            $redisPipe->exec();
        });
        
        return $protections;
    }
    
    /**
     * Selects a player based on their damage contribution ratio.
     * @param array $contributionRatios An array of contribution ratios keyed by player ID.
     * @return string|null The selected player ID or null if no player is selected.
     */
    private function selectPlayerByContribution($contributionRatios) {
        // 使用轮盘赌选择法，根据贡献比例选择玩家
        $roll = mt_rand(1, 10000) / 10000; // Roll between 0.0001 and 1.0000
        $cumulativeProbability = 0;
        
        foreach ($contributionRatios as $playerId => $ratio) {
            $cumulativeProbability += $ratio;
            if ($roll <= $cumulativeProbability) {
                return $playerId;
            }
        }
        
        // 如果由于浮点数精度问题没有选中任何玩家，则返回贡献最高的玩家
        arsort($contributionRatios);
        return key($contributionRatios);
    }

    /**
     * Handles a player's attempt to pick up an item.
     * @param int $playerId The ID of the player picking up the item.
     * @param int $sceneItemId The ID of the scene_items entry.
     * @return array A result array with 'success' and 'message'.
     */
    public function pickupItem($playerId, $sceneItemId) {
        $conn = $this->db->getConnection();
        $conn->beginTransaction();

        try {
            // 1. Lock and check the item on the ground
            $stmt = $conn->prepare("SELECT * FROM scene_items WHERE id = ? FOR UPDATE");
            $stmt->execute([$sceneItemId]);
            $sceneItem = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$sceneItem) {
                throw new Exception("物品已经消失了。");
            }

            // 2. Check protection
            $protectionKey = "scene_item_protection:{$sceneItem['id']}";
            $isProtected = RedisManager::getInstance()->with(function($redis) use ($protectionKey) {
                return $redis->exists($protectionKey);
            });

            if ($isProtected) {
                // 获取所有有保护权的玩家
                 $protectedData = RedisManager::getInstance()->with(function($redis) use ($protectionKey, $playerId) {
                    return [
                        'players' => $redis->smembers($protectionKey),
                        'is_member' => $redis->sismember($protectionKey, $playerId)
                    ];
                });

                // 如果当前玩家不在保护列表中
                if (!$protectedData['is_member']) {
                    // 获取保护玩家的名字
                    $stmt = $conn->prepare("SELECT username FROM accounts WHERE id = ?");
                    $stmt->execute([$protectedData['players'][0]]);
                    $protectedPlayerName = $stmt->fetchColumn() ?: '其他玩家';
                    $stmt->closeCursor();
                    
                    throw new Exception("这个物品受到{$protectedPlayerName}的掉落保护，你现在不能拾取。");
                }
            }

            // 3. Add item to player's inventory
            $this->addItemToPlayer($conn, $playerId, $sceneItem['item_template_id'], $sceneItem['quantity'], $sceneItem['instance_data']);
            
            // 调试输出
            error_log("[LootManager] Successfully picked up item, template_id: {$sceneItem['item_template_id']}, quantity: {$sceneItem['quantity']}");
            
            // 4. Remove item from the ground
            $deleteStmt = $conn->prepare("DELETE FROM scene_items WHERE id = ?");
            $deleteStmt->execute([$sceneItemId]);

            // 5. Commit transaction
            $conn->commit();
            
            // If item was taken, clear protection.
            RedisManager::getInstance()->with(function($redis) use ($protectionKey) {
                $redis->del($protectionKey);
            });

            return [
                'success' => true, 
                'message' => '【物品获得】成功拾取了 ' . $sceneItem['quantity'] . ' 个 ' . $this->getItemName($conn, $sceneItem['item_template_id']) . '！', 
                'scene_id' => $sceneItem['scene_id'],
                'item_template_id' => $sceneItem['item_template_id'], // 确保返回物品模板ID
                'quantity' => $sceneItem['quantity']                 // 确保返回数量
            ];

        } catch (Exception $e) {
            $conn->rollBack();
            return ['success' => false, 'message' => $e->getMessage()];
        }
    }

    /**
     * Adds an item to a player's inventory, handling stacking logic correctly.
     * @param PDO $conn The database connection object.
     * @param int $playerId The ID of the player.
     * @param int $itemTemplateId The ID of the item template.
     * @param int $quantity The quantity of the item to add.
     * @param string|null $instanceData Specific data for non-stackable items.
     */
    public function addItemToPlayer($conn, $playerId, $itemTemplateId, $quantity, $instanceData = null) {
        // Get item properties
        $itemStmt = $conn->prepare("SELECT stackable, max_stack FROM item_templates WHERE id = ?");
        $itemStmt->execute([$itemTemplateId]);
        $item = $itemStmt->fetch(PDO::FETCH_ASSOC);
        $itemStmt->closeCursor();

        if (!$item) {
            throw new Exception("试图添加一个不存在的物品模板ID: {$itemTemplateId}");
        }

        $isStackable = (bool)$item['stackable'];
        $maxStack = (int)$item['max_stack'];
        $quantityToAdd = $quantity;

        if ($isStackable && $quantityToAdd > 0) {
            // 对于可堆叠物品，如果有instance_data，则不能与其他物品合并
            if ($instanceData === null) {
                // Find existing, non-full stacks that are NOT bound and have no instance_data
                $stacksStmt = $conn->prepare(
                    "SELECT id, quantity FROM player_inventory
                     WHERE player_id = ? AND item_template_id = ? AND quantity < ? AND is_equipped = 0 AND is_bound = 0 AND instance_data IS NULL
                     ORDER BY quantity DESC FOR UPDATE"
                );
                $stacksStmt->execute([$playerId, $itemTemplateId, $maxStack]);
                $existingStacks = $stacksStmt->fetchAll(PDO::FETCH_ASSOC);
                $stacksStmt->closeCursor();

                // Fill existing stacks first
                foreach ($existingStacks as $stack) {
                    if ($quantityToAdd <= 0) break;

                    $spaceAvailable = $maxStack - $stack['quantity'];
                    $amountToFill = min($quantityToAdd, $spaceAvailable);

                    $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?");
                    $updateStmt->execute([$amountToFill, $stack['id']]);

                    $quantityToAdd -= $amountToFill;
                }
            }
        }

        // If there's still quantity left, or the item is not stackable, create new stacks
        while ($quantityToAdd > 0) {
            if ($isStackable) {
                $newStackQuantity = min($quantityToAdd, $maxStack);
                $insertStmt = $conn->prepare(
                    "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound) VALUES (?, ?, ?, ?, 0)"
                );
                $insertStmt->execute([$playerId, $itemTemplateId, $newStackQuantity, $instanceData]);
                $quantityToAdd -= $newStackQuantity;
            } else {
                // For non-stackable items, insert them one by one
                $insertStmt = $conn->prepare(
                    "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound) VALUES (?, ?, 1, ?, 0)"
                );
                $insertStmt->execute([$playerId, $itemTemplateId, $instanceData]);
                $quantityToAdd--;
            }
        }
    }
    
    /**
     * 获取物品名称
     * @param PDO $conn 数据库连接
     * @param int $itemTemplateId 物品模板ID
     * @return string 物品名称
     */
    private function getItemName($conn, $itemTemplateId) {
        $stmt = $conn->prepare("SELECT name FROM item_templates WHERE id = ?");
        $stmt->execute([$itemTemplateId]);
        $name = $stmt->fetchColumn();
        $stmt->closeCursor();
        return $name ?: '未知物品';
    }
} 