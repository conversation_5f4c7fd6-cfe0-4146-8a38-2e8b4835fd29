<!-- Shop Config Modal -->
<div id="shop-config-modal" class="modal">
    <div class="modal-content large">
        <span class="close-button">&times;</span>
        <h2 id="shop-modal-title">配置商店商品</h2>
        <p>正在为场景: <strong id="shop-config-scene-name"></strong> 中的建筑: <strong id="shop-config-building-name"></strong> 配置商品。</p>
        
        <input type="hidden" id="shop-config-scene-building-id">
        
        <!-- 分类筛选器将由JS动态添加到这里 -->
        
        <div class="form-group">
            <label>添加新商品:</label>
            <div class="shop-item-selection">
                <div class="form-group" style="flex: 3;">
                    <label for="shop-item-select">商品</label>
                    <select id="shop-item-select" data-placeholder="搜索物品..."></select>
                </div>
                <div class="form-group" style="flex: 1;">
                    <label for="shop-item-stock">库存</label>
                    <input type="number" id="shop-item-stock" placeholder="留空为无限" min="1">
                </div>
                <div class="form-group" style="flex: 1;">
                    <label for="shop-item-sort-order">排序</label>
                    <input type="number" id="shop-item-sort-order" placeholder="数字越小越靠前" value="0" min="0">
                </div>
                <button type="button" id="shop-add-item-btn" class="btn btn-secondary">添加商品</button>
            </div>
        </div>

        <div class="form-group">
            <label>已售商品:</label>
            <div id="shop-item-list" class="shop-item-list-container">
                <!-- 商品列表将动态填充 -->
                <p class="loading-text">正在加载商品...</p>
            </div>
        </div>
        
        <div class="form-actions">
            <button type="button" id="save-shop-config-btn" class="btn btn-primary">保存所有更改</button>
        </div>
    </div>
</div> 