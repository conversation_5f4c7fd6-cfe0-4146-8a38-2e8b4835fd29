<?php
$pageTitle = '建筑管理';
$currentPage = 'buildings';
ob_start();
?>
<style>
    .building-card {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        margin-bottom: 15px;
        padding: 15px;
        background-color: var(--card-bg);
        transition: transform 0.2s, box-shadow 0.2s;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        position: relative;
        overflow: hidden;
    }
    
    .building-card:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 4px;
        background-color: var(--primary-color);
        opacity: 0.7;
    }
    
    .building-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }
    
    .building-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        padding: 10px;
    }
    
    .building-card h3 {
        margin-top: 0;
        margin-bottom: 10px;
        color: var(--primary-color);
        border-bottom: 1px dashed rgba(0,0,0,0.1);
        padding-bottom: 8px;
    }
    
    .building-type {
        display: inline-block;
        background-color: var(--secondary-color);
        color: white;
        padding: 3px 8px;
        border-radius: 4px;
        font-size: 0.8em;
        margin-bottom: 10px;
    }
    
    .building-description {
        color: var(--text-color);
        margin-bottom: 15px;
        font-size: 0.9em;
    }
    
    .building-scenes {
        margin-bottom: 15px;
        font-size: 0.9em;
        border-top: 1px solid var(--border-color, #eee);
        padding-top: 10px;
    }
    
    .building-scenes ul {
        margin: 5px 0;
        padding-left: 20px;
    }
    
    .building-scenes li {
        margin-bottom: 3px;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }
    
    .btn-icon.remove-scene {
        background: none;
        border: none;
        color: #999;
        cursor: pointer;
        font-size: 16px;
        padding: 2px 5px;
        border-radius: 3px;
        transition: all 0.2s;
    }
    
    .btn-icon.remove-scene:hover {
        color: #f44336;
        background-color: rgba(244, 67, 54, 0.1);
    }
    
    .btn-icon.remove-scene span {
        display: inline-block;
        line-height: 1;
    }
    
    .building-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    
    /* 模态框样式 */
    .modal-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }
    
    .modal-content {
        background-color: var(--card-bg, #fff);
        border-radius: 5px;
        padding: 20px;
        width: 80%;
        max-width: 600px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
        border: 1px solid var(--border-color, #ddd);
        opacity: 1;
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .modal-header h2 {
        margin: 0;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-color);
    }
    
    /* 状态消息样式 */
    #status-message-container {
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1100;
    }
    
    .status-message {
        padding: 10px 15px;
        margin-bottom: 10px;
        border-radius: 4px;
        color: white;
        opacity: 1;
        transition: opacity 0.5s;
    }
    
    .status-message.success {
        background-color: #4CAF50;
    }
    
    .status-message.error {
        background-color: #F44336;
    }
    
    .status-message.fade-out {
        opacity: 0;
    }
</style>
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="buildings.js" defer></script>
<?php
$extra_js = ob_get_clean();
require_once 'layout_header.php';
?>

<div class="main-content-area card">
    <div class="card-header">
        <button class="btn btn-primary" onclick="showCreateModal()">创建新建筑</button>
    </div>
    
    <!-- 卡片视图 -->
    <div id="card-view">
        <div id="building-grid" class="building-grid">
            <!-- 建筑卡片将通过JavaScript动态添加 -->
            <div class="building-card loading">
                <p style="text-align:center;">正在加载建筑数据...</p>
            </div>
        </div>
    </div>
    
</div>

<!-- 创建建筑模态框 -->
<div id="create-building-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>创建新建筑</h2>
            <button class="modal-close" onclick="hideActiveModal()">&times;</button>
        </div>
        <form id="create-building-form" onsubmit="handleFormSubmit(event, 'create')">
            <div class="form-group">
                <label for="create-name">名称:</label>
                <input type="text" id="create-name" name="name" required>
            </div>
            <div class="form-group">
                <label for="create-type">建筑类型:</label>
                <select id="create-type" name="type" required>
                    <option value="">请选择类型</option>
                    <option value="SHOP">杂货商店</option>
                    <option value="SHOP">武器商店</option>
                    <option value="SHOP">防具商店</option>
                    <option value="SHOP">魔法商店</option>
                    <option value="DIAMOND_SHOP">钻石商店</option>
                    <option value="CRAFTING">材料合成台</option>
                    <option value="GEM_CRAFTING">宝石合成台</option>
                    <option value="FORGE">装备锻造</option>
                    <option value="REFINE">装备凝练</option>
                    <option value="REVIVE_POINT">复活点</option>
                    <option value="TELEPORTER">传送点</option>
                    <option value="WAREHOUSE">仓库</option>
                    <option value="ATTRIBUTE_RESET">属性重修</option>
                    <option value="BANK">银行</option>
                    <option value="GUILD_HALL">公会大厅</option>
                    <option value="QUEST_BOARD">任务告示板</option>
                </select>
            </div>
            <div class="form-group">
                <label for="create-description">描述:</label>
                <textarea id="create-description" name="description" rows="3"></textarea>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="hideActiveModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑建筑模态框 -->
<div id="edit-building-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>编辑建筑</h2>
            <button class="modal-close" onclick="hideActiveModal()">&times;</button>
        </div>
        <form id="edit-building-form" onsubmit="handleFormSubmit(event, 'update')">
            <input type="hidden" id="edit-id" name="id">
            <div class="form-group">
                <label for="edit-name">名称:</label>
                <input type="text" id="edit-name" name="name" required>
            </div>
            <div class="form-group">
                <label for="edit-type">建筑类型:</label>
                <select id="edit-type" name="type" required>
                    <option value="">请选择类型</option>
                    <option value="SHOP">杂货商店</option>
                    <option value="SHOP">武器商店</option>
                    <option value="SHOP">防具商店</option>
                    <option value="SHOP">魔法商店</option>
                    <option value="DIAMOND_SHOP">钻石商店</option>
                    <option value="FORGE">装备锻造</option>
                    <option value="CRAFTING">材料合成台</option>
                    <option value="GEM_CRAFTING">宝石合成台</option>
                    <option value="REFINE">装备凝练</option>
                    <option value="REVIVE_POINT">复活点</option>
                    <option value="TELEPORTER">传送点</option>
                    <option value="WAREHOUSE">仓库</option>
                    <option value="ATTRIBUTE_RESET">属性重修</option>
                    <option value="BANK">银行</option>
                    <option value="GUILD_HALL">公会大厅</option>
                    <option value="QUEST_BOARD">任务告示板</option>
                </select>
            </div>
            <div class="form-group">
                <label for="edit-description">描述:</label>
                <textarea id="edit-description" name="description" rows="3"></textarea>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="hideActiveModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 场景分配模态框 -->
<div id="assign-scenes-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2>分配场景</h2>
            <button class="modal-close" onclick="hideActiveModal()">&times;</button>
        </div>
        <form id="assign-scenes-form" onsubmit="handleAssignScenes(event)">
            <input type="hidden" id="assign-building-id" name="building_id">
            <div class="form-group">
                <label for="scene-select">选择场景:</label>
                <select id="scene-select" name="scenes[]" multiple size="10">
                    <!-- 场景选项将通过JS动态加载 -->
                </select>
                <small>按住Ctrl键可多选</small>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="hideActiveModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<div id="status-message-container"></div>

<?php require_once 'layout_footer.php'; ?> 