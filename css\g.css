:root {
    --main-color: #0d1a26; --bg-color: #c4d7e8; --border-color: #6c8095;
    --link-color: #003366; --disabled-color: #9aaab7; --error-color: #a02c2c;
}
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, "Noto Sans", sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", '宋体', 'SimSun';
    font-size: 16px;
    line-height: 1.5;
    margin: 20px 0;
    padding: 0;
    background-color: #333;
    color: var(--main-color);
    display: flex;
    justify-content: center;
}
.container {
    max-width: 400px; width: 100%; background-color: var(--bg-color);
    border: 10px solid #222; border-radius: 5px; padding: 15px; box-sizing: border-box;
    display: flex; flex-direction: column;
}
.game-header h1 { font-size: 20px; text-align: center; margin: 0 0 10px 0; border-bottom: 1px solid var(--border-color); padding-bottom: 5px; }
.connection-status { font-size: 12px; text-align: center; padding: 2px; background: var(--border-color); color: var(--bg-color); margin-bottom: 10px; }
.form-section { padding: 10px; border: 1px solid var(--border-color); margin-bottom: 10px; }
.form-section h4 { margin-top: 0; text-align: center; border-bottom: 1px dotted var(--border-color); padding-bottom: 5px; margin-bottom: 10px;}
.form-group { margin-bottom: 10px; }
.form-group label { display: block; margin-bottom: 3px; font-size: 14px; }
.form-group input {
    width: 100%; padding: 5px; box-sizing: border-box; background: #fff;
    border: 1px solid var(--border-color); font-family: inherit; font-size: 16px;
}
.form-actions { margin-top: 15px; text-align: center;}
.btn-primary {
    display: inline-block; padding: 5px 15px; background: var(--main-color);
    color: var(--bg-color); text-decoration: none; border: 1px solid var(--main-color);
    cursor: pointer;
}
.form-switch-link { color: var(--link-color); text-decoration: underline; cursor: pointer; }
.error-message { color: var(--error-color); font-size: 14px; margin-top: 10px; text-align: center; min-height: 16px; }
.success-message { color: #00662a; font-size: 14px; margin-top: 10px; text-align: center; min-height: 16px; }

a { color: var(--link-color); text-decoration: underline; }
.game-content { display: none; flex-direction: column; position: relative; }
.section-divider { border: none; border-top: 1px solid var(--border-color); margin: 10px 0; }
.link-list a { display: block; margin: 3px 0; }
.link-list a:hover, .link-list a.active { background-color: var(--main-color); color: var(--bg-color); }
.hp-bar { font-family: monospace; white-space: pre; font-size: 14px; }
.btn {
    color: var(--link-color); text-decoration: underline; background: none; border: none; padding: 0;
    margin: 0 5px; font-family: inherit; font-size: inherit; cursor: pointer;
}

/* 地图网格样式 */
.map-scene-card {
    width: 58px; height: 48px; border: 1px solid var(--border-color);
    background-color: #fff; border-radius: 3px; padding: 2px;
    font-size: 10px; line-height: 1.1; text-align: center;
    display: flex; flex-direction: column; justify-content: center;
    cursor: pointer; position: relative; overflow: visible;
    box-sizing: border-box;
}

.map-scene-card:hover {
    background-color: #f0f0f0;
}

.map-scene-card.clickable {
    transition: all 0.2s ease;
}

.map-scene-card.clickable:hover {
    background-color: #e3f2fd;
    border-color: var(--main-color);
    transform: scale(1.05);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.map-scene-card.current-position {
    border: 2px solid var(--main-color); background-color: #e8f4fd;
    font-weight: bold; box-shadow: 0 0 4px rgba(13, 26, 38, 0.3);
}

.map-scene-card.safe-zone {
    background-color: #e8f5e9; border-color: #4caf50;
}

.map-scene-card.danger-zone {
    background-color: #ffebee; border-color: #f44336;
}

.map-scene-card.empty {
    background-color: #f5f5f5; border-style: dashed; border-color: #ccc;
    cursor: default;
}

.map-scene-card.empty:hover {
    background-color: #f5f5f5;
}

.map-scene-name {
    font-size: 10px; font-weight: bold;
    overflow: hidden;
    line-height: 1.1;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    word-break: break-all;
    height: 33px;
}

/* 连接线样式 - 更明显的连接线 */
.map-scene-card.has-east::after {
    content: ''; position: absolute; right: -2px; top: 50%;
    width: 2px; height: 2px; background-color: var(--main-color);
    transform: translateY(-50%); z-index: 10;
}

.map-scene-card.has-south::before {
    content: ''; position: absolute; bottom: -2px; left: 50%;
    width: 2px; height: 2px; background-color: var(--main-color);
    transform: translateX(-50%); z-index: 10;
}
.btn:disabled { color: var(--disabled-color); text-decoration: none; cursor: not-allowed; }

/* 小按钮样式 */
.btn-small {
    background: var(--main-color);
    color: var(--bg-color);
    border: 1px solid var(--main-color);
    border-radius: 3px;
    padding: 2px 8px;
    font-size: 12px;
    cursor: pointer;
    font-family: inherit;
    display: inline-block;
    text-decoration: none;
}

.btn-small:hover {
    background: var(--link-color);
    border-color: var(--link-color);
    color: var(--bg-color);
}

#battleView { border: 1px solid var(--border-color); padding: 10px; }
#battleMonsterInfo { position: relative; }
.battle-player-info { position: relative; }
#battleActions { margin-top: 10px; padding-top: 10px; border-top: 1px solid var(--border-color); }
#battleLogContainer {
    margin-top: 10px; height: 300px; overflow-y: auto;
    border: 1px dashed var(--border-color); padding: 5px; font-size: 14px;
}
#battleEndActions {
    display: none; margin-top: 10px; border-top: 1px solid var(--border-color); padding-top: 10px; text-align: center;
}

/* HP Bar Styles */
.hp-bar-container {
    background-color: rgba(0, 0, 0, 0.25);
    border: 1px solid #222;
    border-radius: 5px;
    height: 20px;
    position: relative;
    max-width: 100%;
    box-sizing: border-box;
    margin: 4px 0;
}
.hp-bar-inner {
    background-color: #c04040;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 30px 30px;
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease-in-out;
}
.hp-bar-text {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #fff;
    font-size: 13px;
    font-weight: bold;
    text-shadow: 1px 1px 1px #000;
    white-space: nowrap;
}
.mp-bar-inner {
    background-color: #4060c0;
    background-image: linear-gradient(45deg, rgba(255, 255, 255, .15) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, .15) 50%, rgba(255, 255, 255, .15) 75%, transparent 75%, transparent);
    background-size: 30px 30px;
    height: 100%;
    border-radius: 3px;
    transition: width 0.3s ease-in-out;
}
.atb-bar-container {
    background-color: rgba(0, 0, 0, 0.25);
    border: 1px solid #222;
    border-radius: 5px;
    height: 12px;
    position: relative;
    max-width: 100%;
    box-sizing: border-box;
    margin: 4px 0;
}
.atb-bar-inner {
    background-color: #d4a000;
    height: 100%;
    border-radius: 3px;
    transition: width 0.1s linear;
}
.round-timer {
    text-align: center;
    font-size: 14px;
    font-weight: bold;
    margin: 5px 0;
    color: #6c8095;
}
.round-timer-bar-container {
    background-color: rgba(0, 0, 0, 0.25);
    border: 1px solid #222;
    border-radius: 5px;
    height: 8px;
    position: relative;
    max-width: 100%;
    box-sizing: border-box;
    margin: 4px 0;
}
.round-timer-bar {
    background-color: #6c8095;
    height: 100%;
    border-radius: 3px;
    transition: width 0.1s linear;
}

/* 战斗结果视觉效果 */
.battle-result-icon {
    position: absolute;
    top: 5px;
    right: 5px;
    font-size: 28px;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    z-index: 10;
    transform: rotate(15deg);
}
.victory-icon {
    color: #28a745;
}
.defeat-icon {
    color: #dc3545;
}
.flee-icon {
    color: #6c757d;
}

/* 状态条抖动和数字飘出效果 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

.shake-animation {
    animation: shake 1s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes heal-pulse {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    40% { box-shadow: 0 0 0 15px rgba(40, 167, 69, 0.5); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0.3); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

.heal-animation {
    animation: heal-pulse 2.5s ease-out;
}

@keyframes mana-pulse {
    0% { box-shadow: 0 0 0 0 rgba(64, 96, 192, 0.7); }
    40% { box-shadow: 0 0 0 15px rgba(64, 96, 192, 0.5); }
    70% { box-shadow: 0 0 0 10px rgba(64, 96, 192, 0.3); }
    100% { box-shadow: 0 0 0 0 rgba(64, 96, 192, 0); }
}

.mana-animation {
    animation: mana-pulse 2.5s ease-out;
}

@keyframes float-up {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.8);
    }
    5% {
        opacity: 1;
        transform: translateY(-8px) scale(1.3);
    }
    15% {
        opacity: 1;
        transform: translateY(-15px) scale(1.1);
    }
    40% {
        opacity: 0.95;
        transform: translateY(-25px) scale(1.05);
    }
    70% {
        opacity: 0.8;
        transform: translateY(-35px) scale(1);
    }
    90% {
        opacity: 0.4;
        transform: translateY(-45px) scale(0.9);
    }
    100% {
        opacity: 0;
        transform: translateY(-50px) scale(0.8);
    }
}

.damage-number {
    position: absolute;
    color: #ff2233;
    font-weight: bold;
    font-size: 26px;
    text-shadow: 3px 3px 4px rgba(0,0,0,0.9), 0 0 8px rgba(255,34,51,0.6);
    z-index: 150;
    pointer-events: none;
    animation: float-up 3.5s ease-out forwards;
    white-space: nowrap;
    letter-spacing: 1px;
    font-family: 'Arial Black', Arial, sans-serif;
}

.heal-number {
    position: absolute;
    color: #22cc44;
    font-weight: bold;
    font-size: 26px;
    text-shadow: 3px 3px 4px rgba(0,0,0,0.9), 0 0 8px rgba(34,204,68,0.6);
    z-index: 150;
    pointer-events: none;
    animation: float-up 3.5s ease-out forwards;
    white-space: nowrap;
    letter-spacing: 1px;
    font-family: 'Arial Black', Arial, sans-serif;
}

.mana-number {
    position: absolute;
    color: #3366ff;
    font-weight: bold;
    font-size: 26px;
    text-shadow: 3px 3px 4px rgba(0,0,0,0.9), 0 0 8px rgba(51,102,255,0.6);
    z-index: 150;
    pointer-events: none;
    animation: float-up 3.5s ease-out forwards;
    white-space: nowrap;
    letter-spacing: 1px;
    font-family: 'Arial Black', Arial, sans-serif;
}

@keyframes dodge-flash {
    0% {
        opacity: 0;
        transform: scale(0.3) rotate(-15deg);
    }
    10% {
        opacity: 1;
        transform: scale(2.2) rotate(8deg);
    }
    25% {
        opacity: 1;
        transform: scale(1.8) rotate(-5deg);
    }
    40% {
        opacity: 0.95;
        transform: scale(1.5) rotate(3deg);
    }
    60% {
        opacity: 0.8;
        transform: scale(1.2) rotate(-2deg);
    }
    80% {
        opacity: 0.5;
        transform: scale(1.0) rotate(1deg);
    }
    95% {
        opacity: 0.2;
        transform: scale(0.8) rotate(0deg);
    }
    100% {
        opacity: 0;
        transform: scale(0.6) rotate(0deg);
    }
}

.dodge-text {
    position: absolute;
    color: #ff6b35;
    font-weight: bold;
    font-size: 48px;
    text-shadow:
        4px 4px 8px rgba(0,0,0,0.9),
        0 0 16px rgba(255,107,53,0.9),
        0 0 24px rgba(255,107,53,0.7),
        0 0 32px rgba(255,107,53,0.5);
    z-index: 160;
    pointer-events: none;
    animation: dodge-flash 2s ease-out forwards;
    white-space: nowrap;
    font-family: 'Arial Black', Arial, sans-serif;
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: radial-gradient(circle, rgba(255,107,53,0.4) 0%, rgba(255,107,53,0.2) 50%, transparent 80%);
    border-radius: 50%;
    border: 2px solid rgba(255,107,53,0.6);
}
.item-quantity {
    font-size: 14px;
    color: #666;
    margin-left: 5px;
}

.skill-book-button {
    background-color: #6a5acd !important; /* Slategray */
    color: white !important;
    border-color: #6a5acd !important;
}
.skill-book-button:hover {
    background-color: #0d1a26;
    color: #fff;
}

/* Skills View Specific Styles */
#skillsListContainer {
    display: flex;
    flex-direction: column;
    gap: 10px;
    max-height: 300px;
    overflow-y: auto;
    padding-right: 5px;
}

/* 技能职业筛选按钮样式 */
.skill-filter-btn {
    padding: 4px 8px;
    font-size: 12px;
    border-radius: 3px;
    border: 1px solid #ccc;
    background-color: #f8f9fa;
    color: #495057;
    cursor: pointer;
    transition: all 0.2s;
}

.skill-filter-btn:hover {
    background-color: #e9ecef;
    border-color: #adb5bd;
}

.skill-filter-btn.active {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.skill-filter-btn.active:hover {
    background-color: #0056b3;
    border-color: #0056b3;
}

.skill-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.skill-item:hover {
    background-color: #f9f9f9;
}

.skill-item-icon {
    width: 40px;
    height: 40px;
    margin-right: 15px;
    border-radius: 5px;
    border: 1px solid #ddd;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    background-color: #f0f0f0;
}

.skill-item-info {
    flex-grow: 1;
}

.skill-item-name {
    font-weight: bold;
    font-size: 16px;
}

.skill-item-level {
    font-size: 12px;
    color: #666;
    background-color: #e9ecef;
    padding: 2px 5px;
    border-radius: 10px;
    margin-left: 8px;
}

.skill-item-type {
    font-size: 12px;
    color: #666;
    background-color: #eee;
    padding: 1px 4px;
    border-radius: 3px;
    margin-left: auto;
}

@media (max-width: 480px) {
    body {
        margin: 0;
        font-size: 15px;
    }
    .container {
        max-width: 100%;
        border: 5px solid #222;
        padding: 10px;
        min-height: 100vh;
        border-radius: 0;
    }
}

.view-content {
    padding-top: 10px;
}

.currency-display-bar {
    text-align: center;
    padding: 0;
    margin: 0;
    background-color: transparent;
    display: flex;
    justify-content: center;
    gap: 20px;
    font-weight: bold;
}

/* 战斗日志样式 */
.battle-log-container {
    margin-top: 15px;
}

.battle-log-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 5px;
}

.battle-log-header h4 {
    margin: 0;
    font-size: 16px;
    color: var(--text-color);
}

.battle-log-toggle-btn {
    display: flex;
    align-items: center;
    gap: 5px;
    padding: 4px 8px;
    background: var(--button-bg);
    border: 1px solid var(--border-color);
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    color: var(--text-color);
    transition: all 0.2s ease;
}

.battle-log-toggle-btn:hover {
    background: var(--button-hover-bg);
    transform: translateY(-1px);
}

.battle-log-toggle-btn.collapsed {
    background: #e8f5e8;
    border-color: #28a745;
    color: #28a745;
}

.toggle-icon {
    font-size: 10px;
    transition: transform 0.2s ease;
}

.battle-log {
    font-size: 14px;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    height: 150px;
    overflow-y: auto;
    background: #fafafa;
    transition: all 0.3s ease;
}

.battle-log.expanded {
    height: auto;
    max-height: 80vh;
}

.battle-log.collapsed {
    height: 150px;
}

/* 战斗日志条目样式 */
.battle-log-entry {
    margin: 2px 0;
    padding: 2px 4px;
    border-radius: 2px;
    line-height: 1.4;
}

/* 不同类型日志的样式 */
.log-separator {
    text-align: center;
    font-weight: bold;
    color: #6c757d;
    margin: 8px 0;
    padding: 4px 0;
    border-top: 1px solid #dee2e6;
    border-bottom: 1px solid #dee2e6;
}

.log-battle-start {
    background: linear-gradient(135deg, #007bff, #0056b3);
    color: white;
    font-weight: bold;
    text-align: center;
    padding: 6px;
    border-radius: 4px;
    margin: 4px 0;
    box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.log-warning {
    background: linear-gradient(135deg, #ffc107, #e0a800);
    color: #212529;
    font-weight: bold;
    padding: 4px 6px;
    border-radius: 3px;
    border-left: 4px solid #ff6b35;
}

.log-damage {
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    color: #c62828;
    border-left: 3px solid #f44336;
    padding: 3px 6px;
}

.log-heal {
    background: linear-gradient(135deg, #e8f5e8, #c8e6c9);
    color: #2e7d32;
    border-left: 3px solid #4caf50;
    padding: 3px 6px;
}

.log-skill {
    background: linear-gradient(135deg, #f3e5f5, #e1bee7);
    color: #7b1fa2;
    border-left: 3px solid #9c27b0;
    padding: 3px 6px;
}

.log-dodge {
    background: linear-gradient(135deg, #fff3e0, #ffe0b2);
    color: #ef6c00;
    border-left: 3px solid #ff9800;
    padding: 3px 6px;
    font-style: italic;
}

.log-status {
    background: linear-gradient(135deg, #fce4ec, #f8bbd9);
    color: #ad1457;
    border-left: 3px solid #e91e63;
    padding: 3px 6px;
}

.log-death {
    background: linear-gradient(135deg, #efebe9, #d7ccc8);
    color: #5d4037;
    border-left: 3px solid #795548;
    padding: 3px 6px;
    font-weight: bold;
}

.log-result {
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    color: #1565c0;
    font-weight: bold;
    text-align: center;
    padding: 6px;
    border-radius: 4px;
    margin: 4px 0;
    box-shadow: 0 2px 4px rgba(21,101,192,0.2);
}

.log-normal {
    color: var(--text-color);
    padding: 2px 4px;
}

/* 数字高亮 */
.log-number {
    font-weight: bold;
    color: #dc3545;
    background: rgba(220, 53, 69, 0.1);
    padding: 1px 3px;
    border-radius: 2px;
}

.log-heal .log-number {
    color: #28a745;
    background: rgba(40, 167, 69, 0.1);
}

/* 技能名称高亮 */
.log-skill-name {
    font-weight: bold;
    color: #6f42c1;
    background: rgba(111, 66, 193, 0.1);
    padding: 1px 3px;
    border-radius: 2px;
}

/* 建筑相关样式 */
.scene-building-group {
    margin: 5px 0;
    padding: 2px 5px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.scene-building-group:hover {
    background-color: rgba(0, 0, 0, 0.1);
}

.shop-currencies {
    margin: 5px auto;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    text-align: center;
    font-size: 14px;
    display: block;
    width: fit-content;
}

.currency-gold {
    color: #cc9900;
    font-weight: bold;
}

.currency-diamond {
    color: #3399ff;
    font-weight: bold;
}

.shop-items-list {
    list-style: none;
    padding: 0;
    margin: 15px 0;
}

.shop-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    margin-bottom: 5px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.5);
}

.shop-item:hover {
    background-color: rgba(255, 255, 255, 0.8);
}

.shop-item-info {
    display: flex;
    flex-direction: column;
}

.shop-item-name {
    font-weight: bold;
    margin-bottom: 3px;
}

.shop-item-price {
    font-size: 14px;
    color: #cc9900;
}

.shop-item-actions {
    display: flex;
    align-items: center;
    gap: 5px;
}

.shop-item-quantity {
    width: 40px;
    text-align: center;
    padding: 2px;
    border: 1px solid var(--border-color);
    border-radius: 3px;
}

/* 购买成功/失败提示样式 */
.shop-notification {
    margin: 10px 0;
    padding: 8px;
    border-radius: 4px;
    text-align: center;
}

.shop-notification-success {
    background-color: rgba(40, 167, 69, 0.2);
    border: 1px solid #28a745;
    color: #28a745;
}

.shop-notification-error {
    background-color: rgba(220, 53, 69, 0.2);
    border: 1px solid #dc3545;
    color: #dc3545;
}

/* 商店模式切换按钮样式 */
.shop-mode-buttons {
    display: flex;
    justify-content: center;
    gap: 10px;
    margin-bottom: 10px;
}

.shop-mode-buttons .btn {
    padding: 5px 15px;
    border-radius: 4px;
    font-weight: bold;
    min-width: 80px;
}

.shop-mode-buttons .btn.buy-mode {
    border: 2px solid #28a745;
    color: #28a745;
}

.shop-mode-buttons .btn.buy-mode.active {
    background-color: #28a745;
    color: white;
}

.shop-mode-buttons .btn.sell-mode {
    border: 2px solid #dc3545;
    color: #dc3545;
}

.shop-mode-buttons .btn.sell-mode.active {
    background-color: #dc3545;
    color: white;
}

/* 钻石商店特殊样式 */
.diamond-shop-header {
    text-align: center;
    margin-bottom: 15px;
    padding: 10px;
    background: linear-gradient(135deg, #e3f2fd, #bbdefb);
    border-radius: 8px;
    border: 2px solid #3399ff;
}

.diamond-shop-header .shop-title {
    font-size: 18px;
    font-weight: bold;
    color: #1976d2;
    margin-bottom: 5px;
}

.diamond-shop-header .shop-description {
    font-size: 14px;
    color: #424242;
    font-style: italic;
}

.diamond-shop-item {
    border: 2px solid #3399ff;
    background: linear-gradient(135deg, rgba(51, 153, 255, 0.1), rgba(51, 153, 255, 0.05));
}

.diamond-shop-item:hover {
    background: linear-gradient(135deg, rgba(51, 153, 255, 0.2), rgba(51, 153, 255, 0.1));
    box-shadow: 0 2px 8px rgba(51, 153, 255, 0.3);
}

.diamond-price {
    color: #3399ff !important;
    font-weight: bold;
    font-size: 15px;
}

.diamond-buy-btn {
    background: linear-gradient(135deg, #3399ff, #2196f3);
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 4px;
    font-weight: bold;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.diamond-buy-btn:hover {
    background: linear-gradient(135deg, #2196f3, #1976d2);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(51, 153, 255, 0.4);
}

.diamond-buy-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(51, 153, 255, 0.4);
}

/* 传送点界面样式 */
.teleporter-view {
    padding: 10px;
    background-color: #f5f5f5;
    border-radius: 5px;
}

.teleporter-view h2 {
    color: #3a5a97;
    margin-bottom: 10px;
    text-align: center;
}

.teleporter-description {
    color: #666;
    text-align: center;
    margin-bottom: 15px;
}

.destinations-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.destination-item {
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 5px;
    padding: 10px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.destination-item h3 {
    margin: 0 0 5px 0;
    color: #2c3e50;
}

.destination-item p {
    color: #7f8c8d;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.destination-item button {
    width: 100%;
}

.item-requirement {
    background-color: #fff3e0;
    border: 1px solid #ffe0b2;
    border-radius: 4px;
    padding: 5px 8px;
    margin: 5px 0 10px;
    font-size: 14px;
}

.required-item {
    color: #e65100;
    font-weight: bold;
}

/* 合成系统相关样式 */
.crafting-view {
    padding: 10px 0;
}

.crafting-section {
    margin: 15px 0;
    padding-top: 15px;
    border-top: 1px solid var(--border-color);
}

.materials-list, .recipes-list {
    max-height: 300px;
    overflow-y: auto;
}

.materials-list-items, .recipe-materials-list {
    padding-left: 0;
    list-style-type: none;
}

.material-item, .recipe-item {
    padding: 8px;
    margin-bottom: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
}

.recipe-item {
    background-color: rgba(255, 255, 255, 0.03);
}

.material-name, .recipe-name {
    font-weight: bold;
}

.material-quantity, .recipe-result, .recipe-fee {
    display: block;
    margin-top: 4px;
    color: #aaa;
}

.recipe-header {
    margin-bottom: 10px;
}

.recipe-materials {
    margin: 10px 0;
}

.recipe-materials-title {
    font-weight: bold;
    margin-bottom: 5px;
    display: block;
}

.recipe-materials-list {
    margin: 5px 0;
}

.recipe-material {
    margin: 3px 0;
    padding-left: 10px;
}

.recipe-material.sufficient {
    color: #4caf50;
}

.recipe-material.insufficient {
    color: #f44336;
}

.recipe-warning {
    color: #ff9800;
    margin: 5px 0;
    padding: 5px;
    border-left: 3px solid #ff9800;
}

.recipe-actions {
    margin-top: 10px;
    text-align: right;
}

.btn-disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 模态对话框样式 */
.modal-dialog {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: var(--bg-color);
    border: 2px solid var(--border-color);
    border-radius: 5px;
    width: 80%;
    max-width: 300px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    z-index: 1001;
}

.modal-header {
    padding: 10px;
    background-color: var(--main-color);
    border-bottom: 1px solid var(--border-color);
    text-align: center;
}

.modal-header h3 {
    margin: 0;
    font-size: 18px;
    color: var(--bg-color);
}

.modal-body {
    padding: 15px;
    text-align: center;
}

.modal-footer {
    padding: 10px;
    text-align: center;
    border-top: 1px solid var(--border-color);
    display: flex;
    justify-content: space-around;
}

.modal-footer button {
    min-width: 80px;
}

.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

/* 凝练界面样式 */
.refine-view {
    padding: 15px;
}

.refine-equipment-selection,
.refine-materials-selection {
    margin-bottom: 20px;
    padding: 15px;
    border: 1px solid #ddd;
    border-radius: 5px;
    background-color: #f9f9f9;
}

/* 凝练费用显示样式 */
.refine-cost-display {
    margin-bottom: 15px;
    padding: 12px;
    border: 2px solid #28a745;
    border-radius: 8px;
    background: linear-gradient(135deg, #d4edda, #c3e6cb);
    text-align: center;
}

.refine-cost-display.insufficient-gold {
    border-color: #dc3545;
    background: linear-gradient(135deg, #f8d7da, #f5c6cb);
}

.cost-estimate {
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 8px;
}

.cost-info, .tier-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.cost-label, .tier-label {
    font-size: 12px;
    color: #666;
    margin-bottom: 2px;
}

.cost-amount {
    font-size: 16px;
    font-weight: bold;
    color: #28a745;
}

.tier-level {
    font-size: 16px;
    font-weight: bold;
    color: #007bff;
}

.refine-cost-display .warning {
    margin-top: 8px;
    padding: 6px;
    background-color: rgba(220, 53, 69, 0.1);
    border: 1px solid #dc3545;
    border-radius: 4px;
    color: #dc3545;
    font-weight: bold;
    font-size: 14px;
}

.refine-cost-display.insufficient-gold .cost-amount {
    color: #dc3545;
}

/* PVE技能状态样式（移植自PVP系统） */
.btn.btn-skill.cooldown {
    background-color: #8a8a8a !important;
    color: #e0e0e0 !important;
    cursor: not-allowed !important;
    opacity: 0.6 !important;
    position: relative;
    overflow: hidden;
    border-color: #666 !important;
}

.btn-skill.casting {
    background-color: #ff9800 !important;
    color: white !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
}

.btn-skill.buff-phase {
    background-color: #9c27b0 !important;
    color: white !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
}

.btn-skill.silenced {
    background-color: #dc3545 !important;
    color: white !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
    border-color: #c82333 !important;
}

.btn-skill.stunned {
    background-color: #6f42c1 !important;
    color: white !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
    border-color: #5a2d91 !important;
}

/* 冷却状态的动画效果 */
.btn-skill.cooldown::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgba(255, 255, 255, 0.1) 2px,
        rgba(255, 255, 255, 0.1) 4px
    );
    animation: cooldown-stripes 1s linear infinite;
}

.btn-skill.casting::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 3px,
        rgba(255, 255, 255, 0.2) 3px,
        rgba(255, 255, 255, 0.2) 6px
    );
    animation: casting-pulse 0.8s ease-in-out infinite alternate;
}

.btn-skill.buff-phase::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle,
        rgba(255, 255, 255, 0.2) 30%,
        transparent 70%
    );
    animation: buff-glow 1.2s ease-in-out infinite alternate;
}

.btn-skill.silenced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: repeating-linear-gradient(
        -45deg,
        transparent,
        transparent 3px,
        rgba(255, 255, 255, 0.3) 3px,
        rgba(255, 255, 255, 0.3) 6px
    );
    animation: silence-block 1.5s linear infinite;
}

.btn-skill.stunned::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.4) 20%,
        transparent 40%,
        rgba(255, 255, 255, 0.2) 60%,
        transparent 80%
    );
    animation: stun-dizzy 2s ease-in-out infinite;
}

@keyframes cooldown-stripes {
    0% { transform: translateX(-8px); }
    100% { transform: translateX(8px); }
}

@keyframes casting-pulse {
    0% { opacity: 0.6; }
    100% { opacity: 1; }
}

@keyframes buff-glow {
    0% { opacity: 0.4; transform: scale(0.9); }
    100% { opacity: 0.8; transform: scale(1.1); }
}

@keyframes silence-block {
    0% { transform: translateX(-12px); }
    100% { transform: translateX(12px); }
}

@keyframes stun-dizzy {
    0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    25% { transform: rotate(2deg) scale(1.05); opacity: 1; }
    50% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    75% { transform: rotate(-2deg) scale(0.95); opacity: 1; }
    100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
}

/* 眩晕状态下的常规按钮样式 */
.btn.stunned-disabled,
button.btn.stunned-disabled,
button.stunned-disabled {
    background-color: #6f42c1 !important;
    background: #6f42c1 !important;
    color: white !important;
    cursor: not-allowed !important;
    border-color: #5a2d91 !important;
    border: 1px solid #5a2d91 !important;
    text-decoration: none !important;
    position: relative;
    overflow: hidden;
    padding: 4px 8px !important;
}

.btn.stunned-disabled::after,
button.btn.stunned-disabled::after,
button.stunned-disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.3) 20%,
        transparent 40%,
        rgba(255, 255, 255, 0.2) 60%,
        transparent 80%
    );
    animation: stun-dizzy 2s ease-in-out infinite;
    z-index: 1;
}

/* 私聊界面样式 */
#private-chat-view {
    width: 100%;
    max-width: 100%;
    box-sizing: border-box;
    overflow: hidden;
}

.private-chat-container {
    width: 100%;
    height: 500px;
    border: 1px solid var(--border-color);
    border-radius: 5px;
    overflow: hidden;
    background: rgba(255, 255, 255, 0.9);
}

.private-chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.5);
    gap: 10px;
}

.private-chat-header h3 {
    margin: 0;
    font-size: 16px;
    flex: 1;
    text-align: center;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: 1px solid #dc3545;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    text-decoration: none;
    display: inline-block;
}

.btn-danger:hover {
    background: #c82333;
    border-color: #bd2130;
}

/* 删除确认界面动画 */
@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 公聊频率限制样式 */
.chat-rate-limit {
    margin-top: 5px;
    transition: all 0.3s ease;
}

.chat-rate-limit div {
    animation: shake 0.5s ease-in-out;
}

.private-chat-contacts-view,
.private-chat-conversation-view {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    padding-bottom: 10px; /* 增加底部间距，避免遮挡 */
    box-sizing: border-box;
}

.private-chat-contacts-list {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
}

.no-contacts-message {
    text-align: center;
    color: var(--disabled-color);
    padding: 20px;
    font-style: italic;
}

.private-chat-contact-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid var(--border-color);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.private-chat-contact-item:hover {
    background: rgba(0, 0, 0, 0.05);
}

.private-chat-contact-info {
    flex: 1;
}

.private-chat-contact-name {
    font-weight: bold;
    margin-bottom: 2px;
}

.private-chat-contact-preview {
    font-size: 12px;
    color: var(--disabled-color);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 200px;
}

.private-chat-contact-time {
    font-size: 11px;
    color: var(--disabled-color);
}

.private-chat-unread-badge {
    background: #ff4444;
    color: white;
    border-radius: 10px;
    padding: 2px 6px;
    font-size: 10px;
    font-weight: bold;
    margin-left: 10px;
}

.private-chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    background: rgba(255, 255, 255, 0.3);
    max-height: 350px;
}

.private-chat-input-area {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    border-top: 1px solid var(--border-color);
    background: rgba(255, 255, 255, 0.5);
    min-height: 60px; /* 确保有足够的高度 */
    box-sizing: border-box;
}

.private-chat-input-area input {
    flex: 1;
    padding: 8px;
    border: 1px solid var(--border-color);
    border-radius: 4px;
    font-family: inherit;
    font-size: 14px;
}

.private-chat-input-area button {
    padding: 8px 16px;
    background: var(--main-color);
    color: var(--bg-color);
    border: 1px solid var(--main-color);
    border-radius: 4px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
}

.private-chat-input-area button:hover {
    background: var(--link-color);
    border-color: var(--link-color);
}

.private-chat-input-area button:disabled {
    background: var(--disabled-color);
    border-color: var(--disabled-color);
    cursor: not-allowed;
}

.private-chat-rate-limit {
    text-align: center;
    padding: 5px;
    background: rgba(255, 107, 107, 0.1);
    border-top: 1px solid #ff6b6b;
    transition: all 0.3s ease;
}

/* 私聊错误提示动画 */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

.private-chat-rate-limit.error {
    background: rgba(255, 107, 107, 0.2);
    border-color: #ff6b6b;
    animation: shake 0.5s ease-in-out;
}

/* 私聊按钮样式 */
.btn-secondary {
    background: #6c757d;
    color: white;
    border: 1px solid #6c757d;
    padding: 8px 16px;
    border-radius: 4px;
    cursor: pointer;
    font-family: inherit;
    font-size: 14px;
    text-decoration: none;
    display: inline-block;
}

.btn-secondary:hover {
    background: #5a6268;
    border-color: #5a6268;
    color: white;
    text-decoration: none;
}

/* 聊天消息样式优化 */
.chat-message.own {
    text-align: right;
}

.chat-message.own .chat-name {
    color: var(--main-color);
    font-weight: bold;
}

.chat-name.clickable {
    cursor: pointer;
    text-decoration: underline;
}

.chat-name.clickable:hover {
    color: var(--link-color);
}

/* 聊天时间样式 */
.chat-time {
    font-size: 12px;
    color: #888;
    margin-bottom: 2px;
}

/* 聊天名称行样式 */
.chat-name-line {
    line-height: 1.4;
}

/* 聊天通知样式 */
.chat-notification-bubble {
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    max-width: 300px;
    word-wrap: break-word;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.chat-notification-bubble.private {
    background: rgba(128, 0, 128, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.chat-notification-prefix {
    color: #ffeb3b;
    font-weight: bold;
    margin-right: 4px;
}

.chat-notification-name {
    font-weight: bold;
    margin-right: 4px;
}

.chat-notification-content {
    color: #ffffff;
}

/* 游戏通知样式 */
.game-notification {
    position: absolute;
    right: 15px;
    top: 80px;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 12px 16px;
    border-radius: 6px;
    font-size: 14px;
    max-width: 280px;
    word-wrap: break-word;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 100;
    opacity: 0;
    transform: translateX(100%);
    transition: all 0.3s ease;
}

.game-notification.show {
    opacity: 1;
    transform: translateX(0);
}

.game-notification.success {
    background: rgba(40, 167, 69, 0.9);
    border-left: 4px solid #28a745;
}

.game-notification.error {
    background: rgba(220, 53, 69, 0.9);
    border-left: 4px solid #dc3545;
}

.game-notification.info {
    background: rgba(23, 162, 184, 0.9);
    border-left: 4px solid #17a2b8;
}

/* 小屏幕适配 */
@media (max-width: 480px) {
    .game-notification {
        right: 10px;
        left: auto;  /* 改为auto，避免占据整个屏幕宽度 */
        max-width: calc(100vw - 40px);  /* 限制最大宽度，留出边距 */
        bottom: 80px;
        top: auto;
        position: absolute;
    }
}

/* 稀有度系统样式 */
.rarity-info {
    display: flex;
    align-items: center;
    gap: 5px;
    margin-top: 5px;
    font-size: 12px;
}

.rarity-label {
    color: var(--main-color);
    font-weight: normal;
}

.rarity-name {
    font-weight: bold;
    text-shadow: 1px 1px 1px rgba(0, 0, 0, 0.3);
}

.rarity-bonus {
    color: #28a745;
    font-weight: bold;
}

/* 稀有度颜色定义 */
.rarity-普 { color: #FFFFFF !important; }
.rarity-凡 { color: #CCCCCC !important; }
.rarity-良 { color: #00FF00 !important; }
.rarity-优 { color: #0080FF !important; }
.rarity-珍 { color: #8000FF !important; }
.rarity-极 { color: #FF8000 !important; }
.rarity-玄 { color: #FF0080 !important; }
.rarity-灵 { color: #FF0000 !important; }
.rarity-圣 { color: #FFD700 !important; }

/* 凝练费用显示中的稀有度信息 */
.refine-cost-display .rarity-info {
    justify-content: center;
    padding: 3px 0;
    border-top: 1px dotted var(--border-color);
}

.refine-cost-display .rarity-name {
    margin: 0 3px;
}

/* 装备名称中的稀有度标识 */
.equipment-name .rarity-prefix {
    font-weight: bold;
    margin-right: 2px;
}

/* 工具提示中的稀有度信息 */
.tooltip .rarity-section {
    border-top: 1px solid #ddd;
    margin-top: 8px;
    padding-top: 8px;
}

.tooltip .rarity-section .rarity-title {
    font-weight: bold;
    color: var(--main-color);
    margin-bottom: 4px;
}

.tooltip .rarity-section .rarity-details {
    font-size: 12px;
    color: #666;
}