/* PVP战斗相关样式 */

/* PVP挑战通知 */
.pvp-challenge-notification {
    position: fixed;
    top: 100px;
    left: 50%;
    transform: translateX(-50%);
    width: 300px;
    background-color: var(--bg-color);
    border: 2px solid #e74c3c;
    border-radius: 5px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.2);
    z-index: 1000;
    animation: challenge-notification 0.5s ease-in-out;
}

@keyframes challenge-notification {
    0% { transform: translateX(-50%) translateY(-20px); opacity: 0; }
    100% { transform: translateX(-50%) translateY(0); opacity: 1; }
}

.pvp-challenge-header {
    background-color: #e74c3c;
    color: white;
    padding: 8px;
    text-align: center;
    font-weight: bold;
    border-radius: 3px 3px 0 0;
}

.pvp-challenge-content {
    padding: 15px;
    text-align: center;
}

.pvp-challenge-actions {
    display: flex;
    justify-content: space-between;
    margin-top: 10px;
}

.pvp-challenge-actions button {
    flex: 1;
    margin: 0 5px;
}

/* PVP战斗视图 */
#pvpBattleView {
    padding: 15px;
    padding-top: 5px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

.pvp-battle-container {
    display: flex;
    flex-direction: column;
    align-items: stretch;
    gap: 5px;
    margin-bottom: 10px;
}

.pvp-player-info {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative; /* 用于定位伤害和治疗效果 */
}

/* 对手信息 (上方) */
.opponent-info {
    border-color: #e74c3c;
    margin-bottom: 5px;
    position: relative;
}

/* 本地玩家信息 (下方) */
.local-info {
    border-color: #3498db;
    margin-top: 5px;
    position: relative;
}

/* 敌我标识标签 */
.player-role-tag {
    position: absolute;
    top: -12px;
    padding: 2px 10px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 10px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.2);
}

.opponent-tag {
    right: 10px;
    background-color: #e74c3c;
    color: white;
}

.local-tag {
    right: 10px;
    background-color: #3498db;
    color: white;
}

.player-card {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    position: relative;
}

.player-card.opponent {
    border-color: #e74c3c;
}

.player-card.local {
    border-color: #3498db;
}

.player-name {
    font-size: 1.2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.player-level {
    font-size: 0.9em;
    color: #777;
    margin-bottom: 15px;
}

.stat-bar-container {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
}

.stat-bar-label {
    width: 40px;
    font-size: 0.9em;
    font-weight: bold;
}

.stat-bar {
    flex-grow: 1;
    height: 22px;
    background-color: #e0e0e0;
    border-radius: 5px;
    position: relative;
    overflow: hidden;
}

.stat-bar-fill {
    height: 100%;
    border-radius: 5px;
    transition: width 0.3s ease-in-out;
}

.stat-bar.hp-bar .stat-bar-fill { background-color: #2ecc71; }
.stat-bar.mp-bar .stat-bar-fill { background-color: #3498db; }

.stat-bar-text {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 0.8em;
    color: #fff;
    text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
    font-weight: bold;
}

.atb-bar-container {
    display: flex;
    align-items: center;
    margin-top: 8px;
}

.atb-bar-label {
    width: 40px;
    font-size: 12px;
    font-weight: bold;
}

.atb-bar {
    flex: 1;
    height: 12px;
    background-color: #ddd;
    border-radius: 3px;
    position: relative;
    overflow: hidden;
}

.atb-bar-fill {
    height: 100%;
    background-color: #f39c12;
    transition: width 0.3s linear;
}

.battle-intention {
    padding: 6px;
    margin: 5px 0;
    text-align: center;
    font-weight: bold;
    background-color: rgba(0, 0, 0, 0.05);
    border-radius: 5px;
    color: #444;
}

.battle-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 6px;
    justify-content: center;
    margin-bottom: 10px;
}

.battle-action-btn {
    padding: 8px 15px;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    transition: all 0.2s;
}

.battle-action-btn:hover {
    background-color: #2980b9;
}

.battle-action-btn.active {
    background-color: #2ecc71;
}

/* PVP技能按钮样式 */
.pvp-skills-container {
    width: 100%;
    margin-top: 3px;
    padding-top: 5px;
    border-top: 1px solid var(--border-color);
}

.pvp-skills-container .skills-header {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.battle-action-btn.skill-btn {
    background-color: #4b6b9b;
    color: white;
    border: none;
    padding: 5px 10px;
    margin: 2px;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.battle-action-btn.skill-btn:hover {
    background-color: #385787;
}

.battle-action-btn.skill-btn.active {
    background-color: #294a7e;
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.3) inset;
}

/* 技能冷却状态样式 */
.battle-action-btn.skill-btn.cooldown {
    background-color: #8a8a8a;
    color: #e0e0e0;
    cursor: not-allowed;
    position: relative;
    overflow: hidden;
}

.battle-action-btn.skill-btn.cooldown::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.3);
    background-image: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.1) 50%, rgba(255, 255, 255, 0.1) 75%, transparent 75%, transparent);
    background-size: 10px 10px;
    z-index: 1;
}

/* 技能吟唱状态样式 */
.battle-action-btn.skill-btn.casting {
    background-color: #c17d10;
    color: #f8e9d2;
    cursor: not-allowed;
    animation: casting-pulse 1.5s infinite;
}

/* 技能沉默状态样式 */
.battle-action-btn.skill-btn.silenced {
    background-color: #dc3545;
    color: white;
    cursor: not-allowed;
    position: relative;
    overflow: hidden;
}

/* 技能眩晕状态样式 */
.battle-action-btn.skill-btn.stunned {
    background-color: #6f42c1;
    color: white;
    cursor: not-allowed;
    position: relative;
    overflow: hidden;
}

.battle-action-btn.skill-btn.silenced::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: repeating-linear-gradient(
        -45deg,
        transparent,
        transparent 3px,
        rgba(255, 255, 255, 0.3) 3px,
        rgba(255, 255, 255, 0.3) 6px
    );
    animation: silence-block 1.5s linear infinite;
    z-index: 1;
}

.battle-action-btn.skill-btn.stunned::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.4) 20%,
        transparent 40%,
        rgba(255, 255, 255, 0.2) 60%,
        transparent 80%
    );
    animation: stun-dizzy 2s ease-in-out infinite;
    z-index: 1;
}

@keyframes casting-pulse {
    0% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.4); }
    70% { box-shadow: 0 0 0 5px rgba(255, 193, 7, 0); }
    100% { box-shadow: 0 0 0 0 rgba(255, 193, 7, 0); }
}

@keyframes silence-block {
    0% { transform: translateX(-12px); }
    100% { transform: translateX(12px); }
}

@keyframes stun-dizzy {
    0% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    25% { transform: rotate(2deg) scale(1.05); opacity: 1; }
    50% { transform: rotate(0deg) scale(1); opacity: 0.8; }
    75% { transform: rotate(-2deg) scale(0.95); opacity: 1; }
    100% { transform: rotate(0deg) scale(1); opacity: 0.8; }
}

/* 眩晕状态下的常规按钮样式 */
.battle-action-btn.stunned-disabled {
    background-color: #6f42c1 !important;
    color: white !important;
    cursor: not-allowed !important;
    position: relative;
    overflow: hidden;
}

.battle-action-btn.stunned-disabled::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(
        circle at center,
        rgba(255, 255, 255, 0.3) 20%,
        transparent 40%,
        rgba(255, 255, 255, 0.2) 60%,
        transparent 80%
    );
    animation: stun-dizzy 2s ease-in-out infinite;
    z-index: 1;
}

.battle-status {
    margin-bottom: 10px;
}

.round-counter {
    text-align: center;
    font-weight: bold;
    margin-bottom: 5px;
}

.timer-bar {
    height: 8px;
    background-color: #ddd;
    border-radius: 4px;
    position: relative;
    overflow: hidden;
}

.timer-bar-inner {
    height: 100%;
    background-color: #f39c12;
    transition: width 0.1s linear;
}

.timer-bar-text {
    position: absolute;
    top: -20px;
    right: 0;
    font-size: 12px;
    color: #666;
}

.battle-log-container {
    margin-top: 10px;
    border-top: 1px solid #ccc;
    padding-top: 5px;
}

.battle-log {
    max-height: 180px;
    overflow-y: auto;
    padding: 5px;
    background-color: rgba(0, 0, 0, 0.02);
    border-radius: 3px;
}

.battle-log-entry {
    padding: 3px 0;
    border-bottom: 1px dotted #ddd;
    font-size: 14px;
}

.battle-end-actions {
    margin-top: 15px;
    padding-top: 10px;
    border-top: 1px solid var(--border-color);
    text-align: center;
}

.battle-end-message {
    font-weight: bold;
    margin-bottom: 10px;
    color: #e74c3c;
}

/* PVP排行榜视图 */
#pvpLeaderboardView {
    padding: 15px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
}

.pvp-leaderboard-container {
    margin: 15px 0;
    max-height: 400px;
    overflow-y: auto;
}

.pvp-leaderboard-table {
    width: 100%;
    border-collapse: collapse;
}

.pvp-leaderboard-table th,
.pvp-leaderboard-table td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid #ddd;
}

.pvp-leaderboard-table th {
    background-color: rgba(0, 0, 0, 0.05);
    font-weight: bold;
}

.pvp-leaderboard-table tr:hover {
    background-color: rgba(0, 0, 0, 0.02);
}

.rank-first {
    color: #f39c12;
    font-weight: bold;
}

.rank-second {
    color: #7f8c8d;
    font-weight: bold;
}

.rank-third {
    color: #d35400;
    font-weight: bold;
}

.pvp-leaderboard-actions {
    text-align: center;
    margin-top: 15px;
}

/* PVP统计视图 */
#pvpStatsView {
    padding: 10px;
    border: 1px solid var(--border-color);
    background-color: var(--bg-color);
    max-width: 90%;
    width: auto;
    margin: 0 auto;
    border-radius: 8px;
    overflow: hidden;
}

.pvp-stats-container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    margin: 5px 0;
    gap: 10px;
}

.pvp-stat-row {
    display: flex;
    justify-content: center;
    gap: 10px;
    flex-wrap: wrap;
}

.pvp-stat-item {
    text-align: center;
    min-width: 60px;
    padding: 5px 8px;
    background-color: rgba(0,0,0,0.03);
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.pvp-stat-label {
    font-size: 11px;
    color: #666;
    margin-bottom: 2px;
    font-weight: 500;
}

.pvp-stat-value {
    font-size: 16px;
    font-weight: bold;
}

.pvp-stat-value.win {
    color: #2ecc71;
}

.pvp-stat-value.loss {
    color: #e74c3c;
}

.pvp-stat-value.draw {
    color: #f39c12;
}

.pvp-stats-title {
    text-align: center;
    font-size: 16px;
    margin: 3px 0 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid rgba(0,0,0,0.1);
    color: #34495e;
}

.pvp-stats-actions {
    text-align: center;
    margin-top: 10px;
}

/* 玩家详情页PVP挑战按钮 */
.player-detail-actions {
    margin-top: 15px;
    text-align: center;
    padding-top: 10px;
    border-top: 1px dashed var(--border-color);
}

/* 动画效果 */
@keyframes pvp-shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
    20%, 40%, 60%, 80% { transform: translateX(3px); }
}

.pvp-shake-animation {
    animation: pvp-shake 1s cubic-bezier(.36,.07,.19,.97) both;
}

@keyframes pvp-float-up {
    0% {
        opacity: 0;
        transform: translateY(0) scale(0.5);
    }
    10% {
        opacity: 1;
        transform: translateY(-5px) scale(1.5);
    }
    50% {
        opacity: 0.9;
        transform: translateY(-15px) scale(1.2);
    }
    80% {
        opacity: 0.7;
        transform: translateY(-25px) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-30px) scale(0.8);
    }
}

/* PVP专用的伤害数字样式，避免与PVE冲突 */
.pvp-damage-number {
    position: absolute;
    color: #e74c3c;
    font-weight: bold;
    font-size: 24px;
    text-shadow: 2px 2px 3px rgba(0,0,0,0.8);
    z-index: 100;
    pointer-events: none;
    animation: pvp-float-up 2s ease-out forwards;
}

.pvp-heal-number {
    position: absolute;
    color: #2ecc71;
    font-weight: bold;
    font-size: 24px;
    text-shadow: 2px 2px 3px rgba(0,0,0,0.8);
    z-index: 100;
    pointer-events: none;
    animation: pvp-float-up 2s ease-out forwards;
}

.pvp-mana-number {
    position: absolute;
    color: #3498db;
    font-weight: bold;
    font-size: 24px;
    text-shadow: 2px 2px 3px rgba(0,0,0,0.8);
    z-index: 100;
    pointer-events: none;
    animation: pvp-float-up 2s ease-out forwards;
}

/* 闪避动画效果 */
.dodge-text {
    position: absolute;
    color: #f39c12;
    font-weight: bold;
    font-size: 22px;
    text-shadow: 2px 2px 3px rgba(0,0,0,0.8);
    z-index: 100;
    pointer-events: none;
    animation: dodge-animation 1s ease-out forwards;
}

@keyframes dodge-animation {
    0% { opacity: 0; transform: scale(0.5); }
    20% { opacity: 1; transform: scale(1.2); }
    80% { opacity: 1; transform: scale(1); }
    100% { opacity: 0; transform: scale(0.8); }
}

#pvpBattleTitle {
    text-align: center;
    font-size: 1.4em;
    margin-top: -5px;
    margin-bottom: 5px;
    color: #e74c3c;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
    border-bottom: 2px solid #e74c3c;
    padding-bottom: 2px;
    font-weight: bold;
}

/* 技能伤害特效 */
@keyframes skill-damage-flash {
    0% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
    20% { box-shadow: 0 0 20px 10px rgba(231, 76, 60, 0.7); }
    100% { box-shadow: 0 0 0 0 rgba(231, 76, 60, 0); }
}

.skill-damage-effect {
    animation: skill-damage-flash 1s ease-out;
}

/* 战斗记录相关样式 */
.pvp-battle-records-container {
    margin-top: 20px;
    padding: 10px;
    background-color: rgba(0, 0, 0, 0.1);
    border-radius: 5px;
}

.pvp-section-title {
    font-size: 16px;
    margin-bottom: 10px;
    color: #f0f0f0;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
    padding-bottom: 5px;
}

.pvp-battle-records-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 10px;
    font-size: 14px;
}

.pvp-battle-records-table th, 
.pvp-battle-records-table td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.pvp-battle-records-table th {
    background-color: rgba(0, 0, 0, 0.2);
    color: #f0f0f0;
}

.pvp-battle-records-table tr:nth-child(even) {
    background-color: rgba(255, 255, 255, 0.05);
}

.pvp-battle-records-table tr:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

.pvp-battle-records-table td.win {
    color: #4caf50;
}

.pvp-battle-records-table td.loss {
    color: #f44336;
}

.pvp-battle-records-table td.draw {
    color: #ff9800;
}

.pvp-no-records {
    text-align: center;
    padding: 20px;
    color: #aaa;
    font-style: italic;
}

/* 战斗结果相关样式 */
.battle-result-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px;
    width: 75%;
    gap: 15px;
}

.battle-result {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 100%;
    padding: 15px;
    padding-right: 80px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    animation: result-appear 0.5s ease-out;
    position: relative;
    overflow: visible;
}

.battle-result-win {
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.2), rgba(76, 175, 80, 0.4));
    border: 2px solid #4caf50;
}

.battle-result-lose {
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.2), rgba(244, 67, 54, 0.4));
    border: 2px solid #f44336;
}

.battle-result-draw {
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.2), rgba(255, 193, 7, 0.4));
    border: 2px solid #ffc107;
}

.battle-result-text {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10px;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
    text-align: center;
}

.battle-result-win .battle-result-text {
    color: #4caf50;
}

.battle-result-lose .battle-result-text {
    color: #f44336;
}

.battle-result-draw .battle-result-text {
    color: #ffc107;
}

.battle-result-reason {
    font-size: 16px;
    color: #ddd;
    text-align: center;
    max-width: 80%;
}

.battle-return-btn {
    padding: 10px 20px;
    font-size: 16px;
    min-width: 150px;
    transition: all 0.3s ease;
}

.battle-return-btn:hover {
    transform: scale(1.05);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.battle-ended {
    color: #aaa;
    font-style: italic;
}

@keyframes result-appear {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 战斗结果盖章样式 */
.battle-stamp {
    position: absolute;
    top: 50%;
    right: 5px;
    transform: translateY(-50%) rotate(15deg);
    font-size: 28px;
    font-weight: bold;
    padding: 6px 10px;
    border: 3px solid;
    border-radius: 8px;
    opacity: 0.85;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
    box-shadow: 2px 2px 8px rgba(0, 0, 0, 0.3);
    animation: stamp-appear 0.8s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    z-index: 10;
    pointer-events: none;
    font-family: "隶书", "楷体", serif;
    letter-spacing: 2px;
}

.battle-stamp::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 1px dashed;
    border-color: inherit;
    border-radius: 8px;
    opacity: 0.7;
}

.battle-stamp-win {
    color: #4caf50;
    border-color: #4caf50;
    background: linear-gradient(135deg, rgba(76, 175, 80, 0.05), rgba(76, 175, 80, 0.15));
}

.battle-stamp-lose {
    color: #f44336;
    border-color: #f44336;
    background: linear-gradient(135deg, rgba(244, 67, 54, 0.05), rgba(244, 67, 54, 0.15));
}

.battle-stamp-draw {
    color: #ffc107;
    border-color: #ffc107;
    background: linear-gradient(135deg, rgba(255, 193, 7, 0.05), rgba(255, 193, 7, 0.15));
}

.battle-result {
    position: relative;
    overflow: visible;
}

@keyframes stamp-appear {
    0% {
        opacity: 0;
        transform: translateY(-50%) rotate(20deg) scale(3);
    }
    50% {
        opacity: 0.9;
    }
    70% {
        transform: translateY(-50%) rotate(20deg) scale(0.9);
    }
    100% {
        opacity: 0.8;
        transform: translateY(-50%) rotate(20deg) scale(1);
    }
}

/* 统一排行榜样式 - 确保所有排行榜视图使用相同的样式 */
#levelRankingView,
#heroRankingView,
#villainRankingView {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid var(--border-color);
}

.level-ranking-container,
.hero-ranking-container,
.villain-ranking-container {
    padding: 10px;
    background-color: var(--bg-color);
    border-radius: 5px;
}

.level-ranking-table,
.hero-ranking-table,
.villain-ranking-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.level-ranking-table th,
.level-ranking-table td,
.hero-ranking-table th,
.hero-ranking-table td,
.villain-ranking-table th,
.villain-ranking-table td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.level-ranking-table th,
.hero-ranking-table th,
.villain-ranking-table th {
    background-color: var(--header-bg-color);
    color: var(--header-text-color);
}

.level-ranking-table tr:hover,
.hero-ranking-table tr:hover,
.villain-ranking-table tr:hover {
    background-color: var(--hover-color);
}

.level-ranking-actions,
.hero-ranking-actions,
.villain-ranking-actions {
    text-align: center;
    margin-top: 15px;
}

/* 如果排行榜使用了通用类名，统一所有排行榜视图样式 */
.ranking-view {
    margin-top: 15px;
    padding: 10px;
    border: 1px solid var(--border-color);
}

.ranking-container {
    padding: 10px;
    background-color: var(--bg-color);
    border-radius: 5px;
}

.ranking-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 15px;
}

.ranking-table th,
.ranking-table td {
    padding: 8px;
    text-align: center;
    border-bottom: 1px solid var(--border-color);
}

.ranking-table th {
    background-color: var(--header-bg-color);
    color: var(--header-text-color);
}

.ranking-table tr:hover {
    background-color: var(--hover-color);
}

.ranking-actions {
    text-align: center;
    margin-top: 15px;
} 