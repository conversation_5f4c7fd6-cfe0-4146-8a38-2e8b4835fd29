<?php
session_start();
header('Content-Type: application/json');

// 安全检查
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';

$db = Database::getInstance()->getConnection();
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$response = ['success' => false, 'message' => '无效的物品操作'];

try {
    switch ($action) {
        case 'get_all_simple':
            // 获取一个简化的物品列表，用于下拉选择器
            $stmt = $db->prepare("SELECT id, name, item_id, category FROM item_templates ORDER BY name ASC");
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $response = ['success' => true, 'items' => $items];
            break;

        // 以后可以添加其他case，如 get_all_detailed, create, update, delete等

        default:
            $response['message'] = "未知的物品操作: " . htmlspecialchars($action);
            break;
    }
} catch (Exception $e) {
    $response['message'] = '操作失败: ' . $e->getMessage();
    error_log("Item Templates API Error: " . $e->getMessage());
}

echo json_encode($response); 