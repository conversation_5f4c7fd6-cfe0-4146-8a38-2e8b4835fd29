/**
 * PVP战斗日志管理页面脚本
 */
$(document).ready(function() {
    let currentPage = 1;
    let currentFilters = {};

    const BATTLE_RESULT_MAP = {
        'challenger_win': '挑战者胜利',
        'defender_win': '防守者胜利',
        'draw': '平局',
        'timeout': '超时',
        'surrender': '投降'
    };
    
    // 获取并显示日志
    function fetchLogs(page = 1, filters = {}) {
        currentPage = page;
        currentFilters = filters;
        
        const params = new URLSearchParams({
            action: 'get_logs',
            page: currentPage,
            page_size: 20,
            ...currentFilters
        });

        $.ajax({
            url: `api_pvp_battle_logs.php?${params.toString()}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderTable(response.data.logs);
                    renderPagination(response.data.pagination);
                    
                    // 更新分页信息
                    $('#showing-from').text(response.data.pagination.showing_from);
                    $('#showing-to').text(response.data.pagination.showing_to);
                    $('#total-logs').text(response.data.pagination.total_records);
                } else {
                    showToast(response.message || '加载日志失败', 'error');
                }
            },
            error: function(xhr, status, error) {
                showToast(`请求错误: ${error}`, 'error');
            }
        });
    }

    // 渲染表格内容
    function renderTable(logs) {
        const $tbody = $('#logs-table-body');
        $tbody.empty();
        if (logs.length === 0) {
            $tbody.html('<tr><td colspan="8" class="text-center">没有找到符合条件的PVP战斗日志。</td></tr>');
            return;
        }

        logs.forEach(log => {
            const duration = log.start_time && log.end_time ? calculateDuration(log.start_time, log.end_time) : 'N/A';
            
            const row = `
                <tr>
                    <td>${log.id}</td>
                    <td>${log.start_time}</td>
                    <td>${log.scene_name || '未知场景'}</td>
                    <td>${log.challenger_name} (ID: ${log.challenger_id})</td>
                    <td>${log.defender_name} (ID: ${log.defender_id})</td>
                    <td><span class="badge result-${log.battle_result}">${BATTLE_RESULT_MAP[log.battle_result] || log.battle_result}</span></td>
                    <td>${log.total_damage}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-details-btn" data-id="${log.id}">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    // 渲染分页控件
    function renderPagination(pagination) {
        const { current_page, total_pages } = pagination;
        const $paginationControls = $('.pagination-controls');
        $paginationControls.empty();

        if (total_pages <= 1) return;

        let paginationHtml = '';

        // 首页和上一页按钮
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="1" ${current_page === 1 ? 'disabled' : ''}>首页</button>`;
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${current_page - 1}" ${current_page === 1 ? 'disabled' : ''}>上一页</button>`;

        // 页码按钮
        const pagesToShow = 5;
        let startPage = Math.max(1, current_page - Math.floor(pagesToShow / 2));
        let endPage = Math.min(total_pages, startPage + pagesToShow - 1);
        
        if (endPage - startPage + 1 < pagesToShow) {
            startPage = Math.max(1, endPage - pagesToShow + 1);
        }

        if (startPage > 1) {
            paginationHtml += `<span class="page-ellipsis">...</span>`;
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<button class="btn btn-sm page-btn ${i === current_page ? 'active' : ''}" data-page="${i}">${i}</button>`;
        }

        if (endPage < total_pages) {
            paginationHtml += `<span class="page-ellipsis">...</span>`;
        }

        // 下一页和末页按钮
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${current_page + 1}" ${current_page === total_pages ? 'disabled' : ''}>下一页</button>`;
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${total_pages}" ${current_page === total_pages ? 'disabled' : ''}>末页</button>`;

        $paginationControls.html(paginationHtml);
    }
    
    // 获取并显示日志详情
    function fetchLogDetail(logId) {
        $.ajax({
            url: `api_pvp_battle_logs.php?action=get_log_detail&id=${logId}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderLogDetailModal(response.data);
                } else {
                    showToast(response.message || '加载详情失败', 'error');
                }
            },
            error: function() {
                showToast('请求详情失败', 'error');
            }
        });
    }

    // 渲染详情模态框
    function renderLogDetailModal(log) {
        $('#modal-battle-id').text(log.id);
        $('#modal-scene-name').text(log.scene_name || 'N/A');
        $('#modal-battle-result').html(`<span class="badge result-${log.battle_result}">${BATTLE_RESULT_MAP[log.battle_result] || log.battle_result}</span>`);
        $('#modal-start-time').text(log.start_time || 'N/A');
        $('#modal-end-time').text(log.end_time || 'N/A');
        $('#modal-total-damage').text(log.total_damage || '0');
        $('#modal-duration').text(log.start_time && log.end_time ? calculateDuration(log.start_time, log.end_time) : 'N/A');
        
        // 显示挑战者信息
        $('#modal-challenger').html(`
            <strong>ID:</strong> ${log.challenger_id}<br>
            <strong>名称:</strong> ${log.challenger_name}
        `);
        
        // 显示防守者信息
        $('#modal-defender').html(`
            <strong>ID:</strong> ${log.defender_id}<br>
            <strong>名称:</strong> ${log.defender_name}
        `);
        
        // 显示战斗日志
        const logEntries = log.log_data ? JSON.parse(log.log_data) : [];
        const logHtml = logEntries.map(entry => {
            let entryClass = '';
            if (entry.includes('【暴击！】')) entryClass = 'log-critical';
            else if (entry.includes('【闪避！】')) entryClass = 'log-dodge';
            else if (entry.includes('恢复了') || entry.includes('治疗')) entryClass = 'log-heal';
            else if (entry.includes('被击败了') || entry.includes('已被击败')) entryClass = 'log-defeat';
            return `<div class="log-entry ${entryClass}">${entry}</div>`;
        }).join('');
        $('#modal-battle-log').html(logHtml || '<div class="log-entry">没有战斗日志记录</div>');
        
        $('#export-log-btn').data('id', log.id);

        $('#logDetailModal').fadeIn();
    }
    
    // 事件处理
    $('#search-btn').on('click', function() {
        const filters = {
            battle_result: $('#battle-result-filter').val(),
            start_date: $('#date-range-start').val(),
            end_date: $('#date-range-end').val(),
            scene_id: $('#scene-filter').val(),
            player_name: $('#player-search').val()
        };
        fetchLogs(1, filters);
    });

    $('#reset-btn').on('click', function() {
        $('#battle-result-filter, #scene-filter, #player-search, #date-range-start, #date-range-end').val('');
        $('#cleanup-date').val('');
        fetchLogs(1, {});
    });

    $('.pagination-controls').on('click', '.page-btn', function() {
        if ($(this).is(':disabled')) return;
        const page = $(this).data('page');
        fetchLogs(page, currentFilters);
    });

    $(document).on('click', '.view-details-btn', function() {
        const logId = $(this).data('id');
        fetchLogDetail(logId);
    });

    // 模态框关闭
    $('.modal-close-button, .modal-close').on('click', function() {
        $('#logDetailModal').fadeOut();
    });
    $(window).on('click', function(event) {
        if ($(event.target).is('#logDetailModal')) {
            $('#logDetailModal').fadeOut();
        }
    });
    
    // 导出按钮
    $('#export-log-btn').on('click', function() {
        const logId = $(this).data('id');
        window.location.href = `api_pvp_battle_logs.php?action=export_log&id=${logId}`;
    });

    // 清理按钮处理
    $('#cleanup-btn').on('click', function() {
        const cleanupDate = $('#cleanup-date').val();
        if (!cleanupDate) {
            showToast('请先选择一个清理截止日期。', 'error');
            return;
        }

        const confirmation = confirm(`您确定要删除 ${cleanupDate} 之前的所有PVP战斗日志吗？此操作不可撤销！`);
        if (confirmation) {
            $.ajax({
                url: 'api_pvp_battle_logs.php',
                method: 'POST',
                data: {
                    action: 'clear_old_logs',
                    cleanup_date: cleanupDate
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        fetchLogs(1, currentFilters); // 刷新日志
                    } else {
                        showToast(response.message || '清理失败', 'error');
                    }
                },
                error: function() {
                    showToast('请求失败，无法清理日志。', 'error');
                }
            });
        }
    });

    // 获取初始数据
    fetchLogs();
    
    // 获取场景列表
    $.getJSON('api_pvp_battle_logs.php?action=get_scenes', function(response) {
        if (response.success) {
            const $sceneFilter = $('#scene-filter');
            response.data.forEach(scene => {
                $sceneFilter.append(`<option value="${scene.id}">${scene.name}</option>`);
            });
        }
    });

    // 辅助函数
    function calculateDuration(start, end) {
        const startDate = new Date(start);
        const endDate = new Date(end);
        const diffSeconds = Math.round((endDate - startDate) / 1000);
        if (isNaN(diffSeconds) || diffSeconds < 0) return 'N/A';
        const minutes = Math.floor(diffSeconds / 60);
        const seconds = diffSeconds % 60;
        return `${minutes}分 ${seconds}秒`;
    }

    function showToast(message, type = 'success') {
        const $toast = $('#toast');
        $toast.text(message).addClass(type).fadeIn();
        setTimeout(() => {
            $toast.fadeOut(() => $toast.removeClass(type));
        }, 3000);
    }
}); 