<?php
$pageTitle = '玩家装备数据管理';
$currentPage = 'player_equipment_refine';
$extra_css = '
    <link rel="stylesheet" href="player_inventory.css">
    <link rel="stylesheet" href="player_equipment_refine.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <style>
        /* 装备数据专用样式 */
        .refine-info {
            background-color: rgba(76, 175, 80, 0.05);
            border-left: 3px solid #4caf50;
            padding: 10px;
            margin: 10px 0;
        }
        .base-stats-info {
            background-color: rgba(33, 150, 243, 0.05);
            border-left: 3px solid #2196f3;
            padding: 10px;
            margin: 10px 0;
        }
        .gem-info {
            background-color: rgba(156, 39, 176, 0.05);
            border-left: 3px solid #9c27b0;
            padding: 10px;
            margin: 10px 0;
        }
        .binding-info {
            background-color: rgba(255, 152, 0, 0.05);
            border-left: 3px solid #ff9800;
            padding: 10px;
            margin: 10px 0;
        }
        .refine-tier {
            font-weight: bold;
            color: #4caf50;
        }
        .refine-value {
            color: #2196f3;
            font-weight: bold;
        }
        .refine-bonuses, .base-stats, .gem-stats {
            margin-top: 8px;
        }
        .refine-bonus-item, .stat-item-display, .gem-item-display {
            display: inline-block;
            background-color: #e8f5e8;
            padding: 2px 6px;
            margin: 2px;
            border-radius: 3px;
            font-size: 12px;
        }
        .stat-item-display {
            background-color: #e3f2fd;
        }
        .gem-item-display {
            background-color: #f3e5f5;
        }
        .bound-indicator {
            color: #ff9800;
            font-weight: bold;
        }
        .equipped-indicator {
            color: #4caf50;
            font-weight: bold;
        }
        .equipment-slot {
            font-weight: bold;
            color: #666;
        }
        .equipment-name {
            color: #333;
            font-weight: bold;
        }
        .no-refine {
            color: #999;
            font-style: italic;
        }
        .player-info {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 15px;
        }
        .equipment-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
            gap: 15px;
        }
        .equipment-card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            background-color: #fff;
        }
        .equipment-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #eee;
        }
        .search-filters {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .filter-row {
            display: flex;
            gap: 15px;
            align-items: end;
            margin-bottom: 10px;
            flex-wrap: wrap;
        }
        .filter-row:last-child {
            margin-bottom: 0;
        }
        .filter-group {
            display: flex;
            flex-direction: column;
            gap: 5px;
            min-width: 120px;
        }
        .filter-group.buttons {
            flex-direction: row;
            gap: 10px;
            align-items: center;
        }
        .filter-group label {
            font-weight: 500;
            font-size: 14px;
        }
        .stats-summary {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }
        .stat-item {
            text-align: center;
        }
        .stat-number {
            font-size: 24px;
            font-weight: bold;
            color: #1976d2;
        }
        .stat-label {
            font-size: 14px;
            color: #666;
        }
        .distribution-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-top: 15px;
        }
        .tier-distribution, .value-distribution {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
        }
        .tier-distribution h4, .value-distribution h4 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }
    </style>
';
require_once 'layout_header.php'; 
require_once '../config/Database.php';

// 获取数据库连接
$db = Database::getInstance();
$conn = $db->getConnection();

// 属性中文映射
$attributeMap = [
    'hp' => '生命',
    'max_hp' => '最大生命',
    'mp' => '魔力',
    'max_mp' => '最大魔力',
    'strength' => '力量',
    'agility' => '敏捷',
    'constitution' => '体质',
    'intelligence' => '智力',
    'attack' => '攻击力',
    'defense' => '防御力',
    'attack_speed' => '攻击速度',
    'fire_damage' => '火属性伤害',
    'ice_damage' => '冰属性伤害',
    'wind_damage' => '风属性伤害',
    'electric_damage' => '雷属性伤害',
    'fire_resistance' => '火属性抗性',
    'ice_resistance' => '冰属性抗性',
    'wind_resistance' => '风属性抗性',
    'electric_resistance' => '雷属性抗性',
    'dodge_bonus' => '闪避加成',
    'karma' => '善恶值',
    'potential_points' => '潜力点',
    'knowledge_points' => '知识点',
    'experience' => '经验值',
    'level' => '等级'
];

// 属性显示函数
function getAttributeDisplayName($attribute, $attributeMap) {
    return $attributeMap[$attribute] ?? $attribute;
}

// 排序函数
function getOrderByClause($sortBy) {
    switch ($sortBy) {
        case 'refine_value_desc':
            return "CASE WHEN pi.instance_data LIKE '%\"refined\":true%' THEN 1 ELSE 0 END DESC, pi.is_equipped DESC, pi.player_id";
        case 'refine_value_asc':
            return "CASE WHEN pi.instance_data LIKE '%\"refined\":true%' THEN 0 ELSE 1 END, pi.is_equipped DESC, pi.player_id";
        case 'player_name':
            return "a.username ASC, pi.is_equipped DESC";
        case 'item_name':
            return "it.name ASC, pi.is_equipped DESC";
        case 'slot':
            return "ed.slot ASC, pi.is_equipped DESC";
        case 'level':
            return "pa.level DESC, pi.is_equipped DESC";
        default:
            return "CASE WHEN pi.instance_data LIKE '%\"refined\":true%' THEN 1 ELSE 0 END DESC, pi.is_equipped DESC, pi.player_id";
    }
}

// 处理搜索和筛选参数
$searchPlayer = $_GET['search_player'] ?? '';
$filterTier = $_GET['filter_tier'] ?? '';
$filterSlot = $_GET['filter_slot'] ?? '';
$filterBound = $_GET['filter_bound'] ?? '';
$filterEquipped = $_GET['filter_equipped'] ?? '';
$filterRefined = $_GET['filter_refined'] ?? '';
$sortBy = $_GET['sort_by'] ?? 'refine_value_desc';
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 20;
$offset = ($page - 1) * $limit;

// 构建查询条件
$whereConditions = [];
$params = [];

if (!empty($searchPlayer)) {
    $whereConditions[] = "a.username LIKE ?";
    $params[] = "%{$searchPlayer}%";
}

if (!empty($filterTier)) {
    $whereConditions[] = "pi.instance_data LIKE ?";
    $params[] = '%"refine_tier":' . $filterTier . '%';
}

if (!empty($filterSlot)) {
    $whereConditions[] = "ed.slot = ?";
    $params[] = $filterSlot;
}

if ($filterBound !== '') {
    $whereConditions[] = "pi.is_bound = ?";
    $params[] = $filterBound;
}

if ($filterEquipped !== '') {
    $whereConditions[] = "pi.is_equipped = ?";
    $params[] = $filterEquipped;
}

if ($filterRefined !== '') {
    if ($filterRefined == '1') {
        $whereConditions[] = "pi.instance_data LIKE '%\"refined\":true%'";
    } else {
        $whereConditions[] = "(pi.instance_data NOT LIKE '%\"refined\":true%' OR pi.instance_data IS NULL)";
    }
}

$whereClause = '';
if (!empty($whereConditions)) {
    $whereClause = 'AND ' . implode(' AND ', $whereConditions);
}

// 获取统计数据 - 兼容MySQL 5.5
$statsQuery = "
    SELECT
        COUNT(DISTINCT pi.player_id) as total_players,
        COUNT(*) as total_equipment,
        SUM(CASE WHEN pi.instance_data LIKE '%\"refined\":true%' THEN 1 ELSE 0 END) as total_refined,
        SUM(CASE WHEN pi.is_bound = 1 THEN 1 ELSE 0 END) as total_bound,
        SUM(CASE WHEN pi.is_equipped = 1 THEN 1 ELSE 0 END) as total_equipped
    FROM player_inventory pi
    JOIN item_templates it ON pi.item_template_id = it.id
    JOIN equipment_details ed ON it.id = ed.item_template_id
    JOIN accounts a ON pi.player_id = a.id
    WHERE it.category = 'Equipment'
    {$whereClause}
";

$statsStmt = $conn->prepare($statsQuery);
$statsStmt->execute($params);
$stats = $statsStmt->fetch(PDO::FETCH_ASSOC);

// 计算凝练值统计 - 需要单独查询因为MySQL 5.5不支持JSON函数
$refineStatsQuery = "
    SELECT pi.instance_data
    FROM player_inventory pi
    JOIN item_templates it ON pi.item_template_id = it.id
    JOIN equipment_details ed ON it.id = ed.item_template_id
    JOIN accounts a ON pi.player_id = a.id
    WHERE it.category = 'Equipment'
    AND pi.instance_data LIKE '%\"refined\":true%'
    {$whereClause}
";

$refineStatsStmt = $conn->prepare($refineStatsQuery);
$refineStatsStmt->execute($params);
$refineData = $refineStatsStmt->fetchAll(PDO::FETCH_ASSOC);

$refineValues = [];
foreach ($refineData as $row) {
    $instanceData = json_decode($row['instance_data'], true);
    if (isset($instanceData['refine_value'])) {
        $refineValues[] = floatval($instanceData['refine_value']);
    }
}

$stats['avg_refine_value'] = !empty($refineValues) ? array_sum($refineValues) / count($refineValues) : 0;
$stats['max_refine_value'] = !empty($refineValues) ? max($refineValues) : 0;

// 计算各档位统计
$tierStats = [];
$refineValueRanges = [
    '0-5' => 0, '5-10' => 0, '10-15' => 0, '15-20' => 0, '20+' => 0
];

foreach ($refineData as $row) {
    $instanceData = json_decode($row['instance_data'], true);
    if (isset($instanceData['refine_tier'])) {
        $tier = $instanceData['refine_tier'];
        $tierStats[$tier] = ($tierStats[$tier] ?? 0) + 1;
    }

    if (isset($instanceData['refine_value'])) {
        $value = floatval($instanceData['refine_value']);
        if ($value < 5) {
            $refineValueRanges['0-5']++;
        } elseif ($value < 10) {
            $refineValueRanges['5-10']++;
        } elseif ($value < 15) {
            $refineValueRanges['10-15']++;
        } elseif ($value < 20) {
            $refineValueRanges['15-20']++;
        } else {
            $refineValueRanges['20+']++;
        }
    }
}

// 获取装备数据
$query = "
    SELECT
        pi.id as inventory_id,
        pi.player_id,
        pi.item_template_id,
        pi.quantity,
        pi.is_equipped,
        pi.instance_data,
        pi.is_bound,
        it.name as item_name,
        it.description as item_description,
        ed.slot,
        ed.stats as base_stats,
        ed.sockets,
        a.username,
        pa.level,
        pa.current_scene_id
    FROM player_inventory pi
    JOIN item_templates it ON pi.item_template_id = it.id
    JOIN equipment_details ed ON it.id = ed.item_template_id
    JOIN accounts a ON pi.player_id = a.id
    LEFT JOIN player_attributes pa ON a.id = pa.account_id
    WHERE it.category = 'Equipment'
    {$whereClause}
    ORDER BY
        " . getOrderByClause($sortBy) . "
    LIMIT ? OFFSET ?
";

$stmt = $conn->prepare($query);
$stmt->execute(array_merge($params, [$limit, $offset]));
$equipmentData = $stmt->fetchAll(PDO::FETCH_ASSOC);

// 如果是按凝练值排序，需要在PHP中进行二次排序
if ($sortBy === 'refine_value_desc' || $sortBy === 'refine_value_asc') {
    usort($equipmentData, function($a, $b) use ($sortBy) {
        $aData = json_decode($a['instance_data'], true) ?: [];
        $bData = json_decode($b['instance_data'], true) ?: [];

        $aValue = isset($aData['refine_value']) ? floatval($aData['refine_value']) : 0;
        $bValue = isset($bData['refine_value']) ? floatval($bData['refine_value']) : 0;

        if ($sortBy === 'refine_value_desc') {
            return $bValue <=> $aValue; // 降序
        } else {
            return $aValue <=> $bValue; // 升序
        }
    });
}

// 获取总数用于分页
$countQuery = "
    SELECT COUNT(*) as total
    FROM player_inventory pi
    JOIN item_templates it ON pi.item_template_id = it.id
    JOIN equipment_details ed ON it.id = ed.item_template_id
    JOIN accounts a ON pi.player_id = a.id
    WHERE it.category = 'Equipment'
    {$whereClause}
";

$countStmt = $conn->prepare($countQuery);
$countStmt->execute($params);
$totalCount = $countStmt->fetch(PDO::FETCH_ASSOC)['total'];
$totalPages = ceil($totalCount / $limit);

// 获取凝练档位选项
$tierQuery = "SELECT tier, prefix_equipment FROM refine_tiers ORDER BY tier";
$tierStmt = $conn->prepare($tierQuery);
$tierStmt->execute();
$tiers = $tierStmt->fetchAll(PDO::FETCH_ASSOC);

// 装备槽位选项
$slots = [
    'Head' => '头部',
    'Neck' => '颈部', 
    'Body' => '身体',
    'LeftHand' => '左手',
    'RightHand' => '右手',
    'TwoHanded' => '双手',
    'Finger' => '手指',
    'Back' => '背部'
];
?>

<div class="page-content">
    <!-- 统计概览 -->
    <div class="stats-summary">
        <h3>装备数据统计</h3>
        <div class="stats-grid">
            <div class="stat-item">
                <div class="stat-number"><?= number_format($stats['total_players']) ?></div>
                <div class="stat-label">拥有装备的玩家</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= number_format($stats['total_equipment']) ?></div>
                <div class="stat-label">装备总数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= number_format($stats['total_refined']) ?></div>
                <div class="stat-label">凝练装备数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= number_format($stats['total_bound']) ?></div>
                <div class="stat-label">绑定装备数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= number_format($stats['total_equipped']) ?></div>
                <div class="stat-label">已装备数</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $stats['avg_refine_value'] ? number_format($stats['avg_refine_value'], 2) : '0.00' ?></div>
                <div class="stat-label">平均凝练值</div>
            </div>
            <div class="stat-item">
                <div class="stat-number"><?= $stats['max_refine_value'] ? number_format($stats['max_refine_value'], 2) : '0.00' ?></div>
                <div class="stat-label">最高凝练值</div>
            </div>
        </div>
    </div>

    <!-- 凝练分布统计 -->
    <div class="stats-summary">
        <h3>凝练分布统计</h3>
        <div class="distribution-stats">
            <div class="tier-distribution">
                <h4>档位分布</h4>
                <div class="stats-grid">
                    <?php foreach ($tierStats as $tier => $count): ?>
                        <?php
                        $tierInfo = '';
                        foreach ($tiers as $t) {
                            if ($t['tier'] == $tier) {
                                $tierInfo = $t['prefix_equipment'];
                                break;
                            }
                        }
                        ?>
                        <div class="stat-item">
                            <div class="stat-number"><?= number_format($count) ?></div>
                            <div class="stat-label"><?= $tier ?>档 - <?= htmlspecialchars($tierInfo) ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>

            <div class="value-distribution">
                <h4>凝练值分布</h4>
                <div class="stats-grid">
                    <?php foreach ($refineValueRanges as $range => $count): ?>
                        <div class="stat-item">
                            <div class="stat-number"><?= number_format($count) ?></div>
                            <div class="stat-label"><?= $range ?></div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>

    <!-- 搜索和筛选 -->
    <div class="search-filters">
        <form method="GET" action="">
            <div class="filter-row">
                <div class="filter-group">
                    <label for="search_player">玩家搜索:</label>
                    <input type="text" id="search_player" name="search_player" 
                           value="<?= htmlspecialchars($searchPlayer) ?>" 
                           placeholder="用户名或角色名" style="width: 200px;">
                </div>
                <div class="filter-group">
                    <label for="filter_tier">凝练档位:</label>
                    <select id="filter_tier" name="filter_tier" style="width: 150px;">
                        <option value="">全部档位</option>
                        <?php foreach ($tiers as $tier): ?>
                            <option value="<?= $tier['tier'] ?>" <?= $filterTier == $tier['tier'] ? 'selected' : '' ?>>
                                <?= $tier['tier'] ?>档 - <?= htmlspecialchars($tier['prefix_equipment']) ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filter_slot">装备部位:</label>
                    <select id="filter_slot" name="filter_slot" style="width: 120px;">
                        <option value="">全部部位</option>
                        <?php foreach ($slots as $slot => $name): ?>
                            <option value="<?= $slot ?>" <?= $filterSlot == $slot ? 'selected' : '' ?>>
                                <?= $name ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            <div class="filter-row">
                <div class="filter-group">
                    <label for="filter_bound">绑定状态:</label>
                    <select id="filter_bound" name="filter_bound" style="width: 120px;">
                        <option value="">全部</option>
                        <option value="1" <?= $filterBound === '1' ? 'selected' : '' ?>>已绑定</option>
                        <option value="0" <?= $filterBound === '0' ? 'selected' : '' ?>>未绑定</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filter_equipped">装备状态:</label>
                    <select id="filter_equipped" name="filter_equipped" style="width: 120px;">
                        <option value="">全部</option>
                        <option value="1" <?= $filterEquipped === '1' ? 'selected' : '' ?>>已装备</option>
                        <option value="0" <?= $filterEquipped === '0' ? 'selected' : '' ?>>未装备</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="filter_refined">凝练状态:</label>
                    <select id="filter_refined" name="filter_refined" style="width: 120px;">
                        <option value="">全部</option>
                        <option value="1" <?= $filterRefined === '1' ? 'selected' : '' ?>>已凝练</option>
                        <option value="0" <?= $filterRefined === '0' ? 'selected' : '' ?>>未凝练</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label for="sort_by">排序方式:</label>
                    <select id="sort_by" name="sort_by" style="width: 150px;">
                        <option value="refine_value_desc" <?= $sortBy === 'refine_value_desc' ? 'selected' : '' ?>>凝练值(高到低)</option>
                        <option value="refine_value_asc" <?= $sortBy === 'refine_value_asc' ? 'selected' : '' ?>>凝练值(低到高)</option>
                        <option value="player_name" <?= $sortBy === 'player_name' ? 'selected' : '' ?>>玩家名称</option>
                        <option value="item_name" <?= $sortBy === 'item_name' ? 'selected' : '' ?>>装备名称</option>
                        <option value="slot" <?= $sortBy === 'slot' ? 'selected' : '' ?>>装备部位</option>
                        <option value="level" <?= $sortBy === 'level' ? 'selected' : '' ?>>玩家等级</option>
                    </select>
                </div>
                <div class="filter-group buttons">
                    <button type="submit" class="btn btn-primary">搜索</button>
                    <a href="?" class="btn btn-secondary">重置</a>
                    <button type="button" id="export-json" class="btn btn-info">导出JSON</button>
                    <button type="button" id="export-csv" class="btn btn-success">导出CSV</button>
                </div>
            </div>
        </form>
    </div>

    <!-- 装备列表 -->
    <div class="equipment-grid">
        <?php if (empty($equipmentData)): ?>
            <div style="grid-column: 1 / -1; text-align: center; padding: 40px; color: #666;">
                <p>没有找到符合条件的装备</p>
            </div>
        <?php else: ?>
            <?php foreach ($equipmentData as $equipment): ?>
                <?php
                $instanceData = json_decode($equipment['instance_data'], true) ?: [];
                $displayName = $instanceData['display_name'] ?? $equipment['item_name'];
                $baseStats = json_decode($equipment['base_stats'], true) ?: [];
                ?>
                <div class="equipment-card">
                    <div class="equipment-header">
                        <div>
                            <div class="equipment-name" style="cursor: pointer; color: #007bff;"
                                 data-inventory-id="<?= $equipment['inventory_id'] ?>"
                                 title="点击查看详情">
                                <?= htmlspecialchars($displayName) ?>
                                <?php if ($equipment['is_bound']): ?>
                                    <span class="bound-indicator">[绑]</span>
                                <?php endif; ?>
                                <?php if ($equipment['is_equipped']): ?>
                                    <span class="equipped-indicator">[装备中]</span>
                                <?php endif; ?>
                            </div>
                            <div class="equipment-slot"><?= $slots[$equipment['slot']] ?? $equipment['slot'] ?></div>
                        </div>
                        <div style="text-align: right; font-size: 12px; color: #666;">
                            ID: <?= $equipment['inventory_id'] ?> | 数量: <?= $equipment['quantity'] ?>
                        </div>
                    </div>
                    
                    <div class="player-info">
                        <strong>玩家:</strong> <?= htmlspecialchars($equipment['username']) ?><br>
                        <strong>等级:</strong> <?= $equipment['level'] ?> |
                        <strong>场景:</strong> <?= htmlspecialchars($equipment['current_scene_id']) ?><br>
                        <strong>状态:</strong>
                        <?= $equipment['is_equipped'] ? '<span class="equipped-indicator">已装备</span>' : '未装备' ?> |
                        <?= $equipment['is_bound'] ? '<span class="bound-indicator">已绑定</span>' : '未绑定' ?>
                        <?php if ($equipment['sockets'] > 0): ?>
                            | <strong>插槽:</strong> <?= $equipment['sockets'] ?>
                        <?php endif; ?>
                    </div>

                    <!-- 基础属性 -->
                    <?php if (!empty($baseStats)): ?>
                        <div class="base-stats-info">
                            <div><strong>基础属性:</strong></div>
                            <div class="base-stats">
                                <?php foreach ($baseStats as $attr => $value): ?>
                                    <span class="stat-item-display">
                                        <?= htmlspecialchars(getAttributeDisplayName($attr, $attributeMap)) ?>: <?= $value > 0 ? '+' : '' ?><?= $value ?>
                                    </span>
                                <?php endforeach; ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <!-- 宝石属性 -->
                    <?php if (!empty($instanceData['gem_stats']) || !empty($instanceData['gem_bonuses']) || !empty($instanceData['sockets'])): ?>
                        <div class="gem-info">
                            <div><strong>宝石属性:</strong></div>

                            <?php
                            // 优先使用gem_stats，如果没有则使用gem_bonuses
                            $gemStats = $instanceData['gem_stats'] ?? $instanceData['gem_bonuses'] ?? [];
                            ?>

                            <?php if (!empty($gemStats)): ?>
                                <div class="gem-stats">
                                    <?php foreach ($gemStats as $attr => $value): ?>
                                        <span class="gem-item-display">
                                            <?= htmlspecialchars(getAttributeDisplayName($attr, $attributeMap)) ?>: <?= $value > 0 ? '+' : '' ?><?= $value ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>

                            <?php if (!empty($instanceData['sockets'])): ?>
                                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                                    <strong>镶嵌宝石:</strong>
                                    <?php foreach ($instanceData['sockets'] as $socket): ?>
                                        <?php if (isset($socket['gem_name'])): ?>
                                            <span style="margin-right: 8px;"><?= htmlspecialchars($socket['gem_name']) ?></span>
                                        <?php endif; ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php elseif (!empty($instanceData['socketed_gems'])): ?>
                                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                                    <strong>镶嵌宝石:</strong>
                                    <?php foreach ($instanceData['socketed_gems'] as $gem): ?>
                                        <?= htmlspecialchars($gem['name'] ?? '未知宝石') ?>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- 凝练信息 -->
                    <?php if (isset($instanceData['refined']) && $instanceData['refined']): ?>
                        <div class="refine-info">
                            <div><strong>凝练信息:</strong></div>
                            <div class="refine-tier">
                                档位: <?= $instanceData['refine_tier'] ?? 'N/A' ?> -
                                <?= htmlspecialchars($instanceData['refine_prefix'] ?? 'N/A') ?>
                            </div>
                            <div class="refine-value">
                                凝练值: <?= number_format($instanceData['refine_value'] ?? 0, 2) ?>
                            </div>
                            <?php if (!empty($instanceData['refine_bonuses'])): ?>
                                <div class="refine-bonuses">
                                    <strong>凝练加成:</strong><br>
                                    <?php
                                    $bonusTypes = $instanceData['refine_bonus_types'] ?? [];
                                    foreach ($instanceData['refine_bonuses'] as $attr => $value):
                                        $bonusType = $bonusTypes[$attr] ?? 'flat';
                                        $displayValue = $bonusType === 'percentage' ? "+{$value}%" : "+{$value}";
                                    ?>
                                        <span class="refine-bonus-item">
                                            <?= htmlspecialchars(getAttributeDisplayName($attr, $attributeMap)) ?>: <?= $displayValue ?>
                                        </span>
                                    <?php endforeach; ?>
                                </div>
                            <?php endif; ?>
                            <?php if (!empty($instanceData['refine_timestamp'])): ?>
                                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                                    凝练时间: <?= $instanceData['refine_timestamp'] ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>

                    <!-- 绑定信息 -->
                    <?php if ($equipment['is_bound']): ?>
                        <div class="binding-info">
                            <div><strong>绑定状态:</strong> 该装备已绑定，无法交易</div>
                        </div>
                    <?php endif; ?>

                    <!-- 装备描述 -->
                    <?php if (!empty($equipment['item_description'])): ?>
                        <div style="margin-top: 10px; padding: 8px; background-color: #f9f9f9; border-radius: 4px; font-size: 12px; color: #666;">
                            <strong>描述:</strong> <?= htmlspecialchars($equipment['item_description']) ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>

    <!-- 分页 -->
    <?php if ($totalPages > 1): ?>
        <div class="pagination" style="margin-top: 20px; text-align: center;">
            <?php
            $currentUrl = $_SERVER['REQUEST_URI'];
            $urlParts = parse_url($currentUrl);
            parse_str($urlParts['query'] ?? '', $queryParams);
            
            for ($i = 1; $i <= $totalPages; $i++):
                $queryParams['page'] = $i;
                $pageUrl = $urlParts['path'] . '?' . http_build_query($queryParams);
            ?>
                <a href="<?= htmlspecialchars($pageUrl) ?>" 
                   class="btn <?= $i == $page ? 'btn-primary' : 'btn-secondary' ?>" 
                   style="margin: 0 2px;">
                    <?= $i ?>
                </a>
            <?php endfor; ?>
        </div>
        <div style="text-align: center; margin-top: 10px; color: #666;">
            第 <?= $page ?> 页，共 <?= $totalPages ?> 页 | 总计 <?= number_format($totalCount) ?> 件凝练装备
        </div>
    <?php endif; ?>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
<script>
// 属性中文映射
const attributeMap = {
    'hp': '生命',
    'max_hp': '最大生命',
    'mp': '魔力',
    'max_mp': '最大魔力',
    'strength': '力量',
    'agility': '敏捷',
    'constitution': '体质',
    'intelligence': '智力',
    'attack': '攻击力',
    'defense': '防御力',
    'attack_speed': '攻击速度',
    'fire_damage': '火属性伤害',
    'ice_damage': '冰属性伤害',
    'wind_damage': '风属性伤害',
    'electric_damage': '雷属性伤害',
    'fire_resistance': '火属性抗性',
    'ice_resistance': '冰属性抗性',
    'wind_resistance': '风属性抗性',
    'electric_resistance': '雷属性抗性',
    'dodge_bonus': '闪避加成',
    'karma': '善恶值',
    'potential_points': '潜力点',
    'knowledge_points': '知识点',
    'experience': '经验值',
    'level': '等级'
};

function getAttributeDisplayName(attribute) {
    return attributeMap[attribute] || attribute;
}

$(document).ready(function() {
    // 初始化Select2
    $('#filter_tier, #filter_slot, #filter_bound, #filter_equipped, #filter_refined, #sort_by').select2({
        minimumResultsForSearch: Infinity
    });

    // 装备详情查看功能
    $('.equipment-card').on('click', '.equipment-name', function(e) {
        e.preventDefault();
        const inventoryId = $(this).closest('.equipment-card').find('[data-inventory-id]').data('inventory-id');
        if (inventoryId) {
            showEquipmentDetail(inventoryId);
        }
    });

    // 导出功能
    $('#export-json').on('click', function() {
        const params = new URLSearchParams(window.location.search);
        params.set('action', 'export_equipment_data');
        params.set('format', 'json');
        window.open('/admin/api_player_equipment.php?' + params.toString());
    });

    $('#export-csv').on('click', function() {
        const params = new URLSearchParams(window.location.search);
        params.set('action', 'export_equipment_data');
        params.set('format', 'csv');
        window.open('/admin/api_player_equipment.php?' + params.toString());
    });
});

/**
 * 显示装备详情
 */
function showEquipmentDetail(inventoryId) {
    $.ajax({
        url: '/admin/api_player_equipment.php',
        method: 'GET',
        data: {
            action: 'get_equipment_detail',
            inventory_id: inventoryId
        },
        success: function(data) {
            showEquipmentModal(data);
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || '获取装备详情失败';
            alert('错误: ' + error);
        }
    });
}

/**
 * 显示装备详情模态框
 */
function showEquipmentModal(equipment) {
    const instanceData = equipment.parsed_instance_data || {};
    const baseStats = equipment.parsed_base_stats || {};

    let modalHtml = `
        <div id="equipment-modal" style="
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.5); z-index: 1000; display: flex;
            align-items: center; justify-content: center;
        ">
            <div style="
                background: white; padding: 20px; border-radius: 8px;
                max-width: 600px; max-height: 80vh; overflow-y: auto;
                width: 90%;
            ">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3>装备详情</h3>
                    <button onclick="closeEquipmentModal()" style="
                        background: none; border: none; font-size: 20px; cursor: pointer;
                    ">&times;</button>
                </div>

                <div class="equipment-detail-content">
                    <h4>${equipment.item_name}</h4>
                    <p><strong>玩家:</strong> ${equipment.username}</p>
                    <p><strong>装备ID:</strong> ${equipment.inventory_id}</p>
                    <p><strong>模板ID:</strong> ${equipment.item_template_id}</p>
                    <p><strong>部位:</strong> ${equipment.slot}</p>
                    <p><strong>数量:</strong> ${equipment.quantity}</p>
                    <p><strong>状态:</strong>
                        ${equipment.is_equipped ? '<span style="color: #4caf50;">已装备</span>' : '未装备'} |
                        ${equipment.is_bound ? '<span style="color: #ff9800;">已绑定</span>' : '未绑定'}
                    </p>

                    ${Object.keys(baseStats).length > 0 ? `
                        <div style="margin: 15px 0; padding: 10px; background: #f0f8ff; border-radius: 4px;">
                            <strong>基础属性:</strong><br>
                            ${Object.entries(baseStats).map(([key, value]) =>
                                `<span style="display: inline-block; margin: 2px 5px; padding: 2px 6px; background: #e3f2fd; border-radius: 3px; font-size: 12px;">
                                    ${getAttributeDisplayName(key)}: ${value > 0 ? '+' : ''}${value}
                                </span>`
                            ).join('')}
                        </div>
                    ` : ''}

                    ${(instanceData.gem_stats || instanceData.gem_bonuses || instanceData.sockets) ? `
                        <div style="margin: 15px 0; padding: 10px; background: #f3e5f5; border-radius: 4px;">
                            <strong>宝石属性:</strong><br>
                            ${(() => {
                                const gemStats = instanceData.gem_stats || instanceData.gem_bonuses || {};
                                return Object.keys(gemStats).length > 0 ?
                                    Object.entries(gemStats).map(([key, value]) =>
                                        `<span style="display: inline-block; margin: 2px 5px; padding: 2px 6px; background: #f3e5f5; border-radius: 3px; font-size: 12px;">
                                            ${getAttributeDisplayName(key)}: ${value > 0 ? '+' : ''}${value}
                                        </span>`
                                    ).join('') : '';
                            })()}
                            ${instanceData.sockets ? `
                                <div style="margin-top: 8px; font-size: 12px; color: #666;">
                                    <strong>镶嵌宝石:</strong>
                                    ${instanceData.sockets.map(socket =>
                                        socket.gem_name ? `<span style="margin-right: 8px;">${socket.gem_name}</span>` : ''
                                    ).join('')}
                                </div>
                            ` : ''}
                        </div>
                    ` : ''}

                    ${instanceData.refined ? `
                        <div style="margin: 15px 0; padding: 10px; background: #e8f5e8; border-radius: 4px;">
                            <strong>凝练信息:</strong><br>
                            <p>档位: ${instanceData.refine_tier} - ${instanceData.refine_prefix}</p>
                            <p>凝练值: ${instanceData.refine_value}</p>
                            ${instanceData.refine_bonuses ? `
                                <div>
                                    <strong>凝练加成:</strong><br>
                                    ${Object.entries(instanceData.refine_bonuses).map(([key, value]) => {
                                        const bonusTypes = instanceData.refine_bonus_types || {};
                                        const bonusType = bonusTypes[key] || 'flat';
                                        const displayValue = bonusType === 'percentage' ? `+${value}%` : `+${value}`;
                                        return `<span style="display: inline-block; margin: 2px 5px; padding: 2px 6px; background: #e8f5e8; border-radius: 3px; font-size: 12px;">
                                            ${getAttributeDisplayName(key)}: ${displayValue}
                                        </span>`;
                                    }).join('')}
                                </div>
                            ` : ''}
                            ${instanceData.refine_timestamp ? `<p style="font-size: 12px; color: #666;">凝练时间: ${instanceData.refine_timestamp}</p>` : ''}
                        </div>
                    ` : '<p style="color: #999; font-style: italic;">未凝练</p>'}

                    ${equipment.item_description ? `
                        <div style="margin: 15px 0; padding: 10px; background: #f9f9f9; border-radius: 4px;">
                            <strong>描述:</strong> ${equipment.item_description}
                        </div>
                    ` : ''}
                </div>
            </div>
        </div>
    `;

    $('body').append(modalHtml);
}

/**
 * 关闭装备详情模态框
 */
function closeEquipmentModal() {
    $('#equipment-modal').remove();
}

// 点击模态框外部关闭
$(document).on('click', '#equipment-modal', function(e) {
    if (e.target === this) {
        closeEquipmentModal();
    }
});
</script>

<?php require_once 'layout_footer.php'; ?>
