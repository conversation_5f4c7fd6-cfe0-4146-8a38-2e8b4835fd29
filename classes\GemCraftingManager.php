<?php
// classes/GemCraftingManager.php

/**
 * 宝石合成系统管理类
 * 用于处理宝石合成相关的逻辑
 */
class GemCraftingManager {
    private $db;
    
    /**
     * 构造函数
     * @param PDO $db 数据库连接
     */
    public function __construct($db) {
        $this->db = $db;
    }
    
    /**
     * 获取玩家可用的宝石合成配方列表
     * @param int $playerId 玩家ID
     * @param int $sceneBuildingId 场景建筑ID
     * @return array 配方列表及相关数据
     */
    public function getAvailableGemRecipes($playerId, $sceneBuildingId) {
        // 获取所有宝石配方
        $allRecipes = $this->getAllGemRecipes();
        
        // 获取玩家宝石物品
        $playerGems = $this->getPlayerGems($playerId);
        
        // 获取玩家金币数量
        $playerGold = $this->getPlayerGold($playerId);
        
        // 过滤出玩家拥有材料的配方
        $availableRecipes = [];
        
        foreach ($allRecipes as $recipe) {
            $recipe['materials'] = $this->getGemRecipeMaterials($recipe['id']);
            $recipe['can_craft'] = true;
            $recipe['missing_materials'] = [];
            
            // 计算合成费用
            $resultItemPrice = $this->getItemBuyPrice($recipe['result_item_id']);
            $recipe['craft_fee'] = Formulas::calculateCraftingFee(
                $recipe['craft_level'], 
                $resultItemPrice, 
                $recipe['result_quantity']
            );
            
            // 检查金币是否足够
            if ($playerGold < $recipe['craft_fee']) {
                $recipe['can_craft'] = false;
            }
            
            // 检查玩家是否拥有配方中的任意材料
            $hasAnyMaterial = false;
            
            // 检查每种材料是否足够
            foreach ($recipe['materials'] as &$material) {
                $playerHasQuantity = 0;
                
                // 查找玩家是否拥有该材料
                foreach ($playerGems as $playerGem) {
                    if ($playerGem['item_template_id'] == $material['material_item_id']) {
                        $playerHasQuantity = $playerGem['quantity'];
                        if ($playerHasQuantity > 0) {
                            $hasAnyMaterial = true;
                        }
                        break;
                    }
                }
                
                // 更新材料数据
                $material['player_quantity'] = $playerHasQuantity;
                $material['player_has_enough'] = $playerHasQuantity >= $material['required_quantity'];
                
                // 如果任何一种材料不足，则不能合成
                if (!$material['player_has_enough']) {
                    $recipe['can_craft'] = false;
                    $recipe['missing_materials'][] = [
                        'name' => $material['name'],
                        'missing_quantity' => $material['required_quantity'] - $playerHasQuantity
                    ];
                }
            }
            
            $recipe['player_gold'] = $playerGold;
            
            // 只有当玩家拥有配方中的任意材料时，才添加到可用配方列表
            if ($hasAnyMaterial) {
                $availableRecipes[] = $recipe;
            }
        }
        
        return [
            'recipes' => $availableRecipes,
            'player_gems' => $playerGems,
            'player_gold' => $playerGold
        ];
    }
    
    /**
     * 获取所有宝石合成配方
     * @return array 配方列表
     */
    private function getAllGemRecipes() {
        $stmt = $this->db->prepare(
            "SELECT 
                rt.id, rt.name, rt.result_item_id, rt.result_quantity, rt.craft_level, rt.description, 
                it.name as result_item_name
             FROM gem_recipe_templates rt
             JOIN item_templates it ON rt.result_item_id = it.id
             ORDER BY rt.craft_level ASC, rt.name ASC"
        );
        $stmt->execute();
        $recipes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $recipes;
    }
    
    /**
     * 获取宝石配方所需的材料
     * @param int $recipeId 配方ID
     * @return array 材料列表
     */
    private function getGemRecipeMaterials($recipeId) {
        $stmt = $this->db->prepare(
            "SELECT 
                rm.id, rm.recipe_id, rm.material_item_id, rm.quantity as required_quantity,
                it.name, it.category
             FROM gem_recipe_materials rm
             JOIN item_templates it ON rm.material_item_id = it.id
             WHERE rm.recipe_id = ?"
        );
        $stmt->execute([$recipeId]);
        $materials = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $materials;
    }
    
    /**
     * 获取玩家拥有的宝石物品
     * @param int $playerId 玩家ID
     * @return array 宝石物品列表
     */
    private function getPlayerGems($playerId) {
        $stmt = $this->db->prepare(
            "SELECT 
                pi.id as inventory_id, pi.item_template_id, pi.quantity, pi.instance_data, pi.is_bound,
                it.name, it.category, it.description
             FROM player_inventory pi
             JOIN item_templates it ON pi.item_template_id = it.id
             WHERE pi.player_id = ? AND pi.is_equipped = 0 AND it.category = 'Gem'
             ORDER BY it.name ASC, pi.is_bound ASC"
        );
        $stmt->execute([$playerId]);
        $gems = $stmt->fetchAll(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $gems;
    }
    
    /**
     * 获取玩家金币数量
     * @param int $playerId 玩家ID
     * @return int 金币数量
     */
    private function getPlayerGold($playerId) {
        $stmt = $this->db->prepare(
            "SELECT gold FROM player_attributes WHERE account_id = ?"
        );
        $stmt->execute([$playerId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $result ? (int)$result['gold'] : 0;
    }
    
    /**
     * 获取物品的购买价格
     * @param int $itemId 物品模板ID
     * @return int 购买价格
     */
    private function getItemBuyPrice($itemId) {
        $stmt = $this->db->prepare(
            "SELECT buy_price FROM item_templates WHERE id = ?"
        );
        $stmt->execute([$itemId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        error_log("GemCraftingManager::getItemBuyPrice - 获取物品购买价格: itemId={$itemId}, 价格=" . ($result ? $result['buy_price'] : "未设置"));
        return $result && $result['buy_price'] ? (int)$result['buy_price'] : 100; // 默认价格100金币
    }
    
    /**
     * 执行宝石合成
     * @param int $playerId 玩家ID
     * @param int $recipeId 配方ID
     * @param bool $useBound 是否优先使用绑定材料
     * @return array 合成结果
     */
    public function craftGem($playerId, $recipeId, $useBound = false) {
        // 获取配方信息
        $recipe = $this->getGemRecipe($recipeId);
        if (!$recipe) {
            return ['success' => false, 'message' => '找不到指定的宝石配方。'];
        }
        
        // 获取配方材料
        $materials = $this->getGemRecipeMaterials($recipeId);
        if (empty($materials)) {
            return ['success' => false, 'message' => '该配方没有定义所需材料。'];
        }
        
        // 获取玩家宝石
        $playerGems = $this->getPlayerGems($playerId);
        $playerGold = $this->getPlayerGold($playerId);
        
        // 计算合成费用
        $resultItemPrice = $this->getItemBuyPrice($recipe['result_item_id']);
        $craftFee = Formulas::calculateCraftingFee(
            $recipe['craft_level'], 
            $resultItemPrice, 
            $recipe['result_quantity']
        );
        
        // 检查金币是否足够
        if ($playerGold < $craftFee) {
            return [
                'success' => false, 
                'message' => "你的金币不足，需要 {$craftFee} 金币。"
            ];
        }
        
        // 检查材料是否足够
        $missingMaterials = [];
        $materialsToDeduct = []; // 存储要扣除的材料信息
        $hasBoundMaterial = false; // 标记是否使用了绑定材料

        foreach ($materials as $material) {
            $requiredQuantity = $material['required_quantity'];
            $materialItemId = $material['material_item_id'];

            // 获取该材料的所有版本（绑定和不绑定）
            $boundGems = [];
            $unboundGems = [];

            foreach ($playerGems as $playerGem) {
                if ($playerGem['item_template_id'] == $materialItemId) {
                    if ((int)$playerGem['is_bound'] === 1) {
                        $boundGems[] = $playerGem;
                    } else {
                        $unboundGems[] = $playerGem;
                    }
                }
            }

            // 计算总数量
            $boundTotal = array_sum(array_column($boundGems, 'quantity'));
            $unboundTotal = array_sum(array_column($unboundGems, 'quantity'));
            $totalAvailable = $boundTotal + $unboundTotal;

            // 检查是否有足够的材料
            if ($totalAvailable < $requiredQuantity) {
                $missingMaterials[] = [
                    'name' => $material['name'],
                    'required' => $requiredQuantity,
                    'has' => $totalAvailable
                ];
                continue;
            }

            // 根据useBound参数决定使用策略
            $remainingNeeded = $requiredQuantity;
            $selectedGems = [];

            if ($useBound) {
                // 优先使用绑定材料
                foreach ($boundGems as $gem) {
                    if ($remainingNeeded <= 0) break;

                    $useQuantity = min($remainingNeeded, $gem['quantity']);
                    $selectedGems[] = [
                        'inventory_id' => $gem['inventory_id'],
                        'quantity' => $useQuantity,
                        'is_bound' => true
                    ];
                    $remainingNeeded -= $useQuantity;
                    $hasBoundMaterial = true;
                }

                // 如果绑定材料不够，使用不绑定材料补充
                foreach ($unboundGems as $gem) {
                    if ($remainingNeeded <= 0) break;

                    $useQuantity = min($remainingNeeded, $gem['quantity']);
                    $selectedGems[] = [
                        'inventory_id' => $gem['inventory_id'],
                        'quantity' => $useQuantity,
                        'is_bound' => false
                    ];
                    $remainingNeeded -= $useQuantity;
                }
            } else {
                // 优先使用不绑定材料
                foreach ($unboundGems as $gem) {
                    if ($remainingNeeded <= 0) break;

                    $useQuantity = min($remainingNeeded, $gem['quantity']);
                    $selectedGems[] = [
                        'inventory_id' => $gem['inventory_id'],
                        'quantity' => $useQuantity,
                        'is_bound' => false
                    ];
                    $remainingNeeded -= $useQuantity;
                }

                // 如果不绑定材料不够，使用绑定材料补充
                foreach ($boundGems as $gem) {
                    if ($remainingNeeded <= 0) break;

                    $useQuantity = min($remainingNeeded, $gem['quantity']);
                    $selectedGems[] = [
                        'inventory_id' => $gem['inventory_id'],
                        'quantity' => $useQuantity,
                        'is_bound' => true
                    ];
                    $remainingNeeded -= $useQuantity;
                    $hasBoundMaterial = true;
                }
            }

            // 保存材料的扣除信息
            foreach ($selectedGems as $selectedGem) {
                $materialsToDeduct[] = [
                    'inventory_id' => $selectedGem['inventory_id'],
                    'quantity' => $selectedGem['quantity'],
                    'name' => $material['name'],
                    'is_bound' => $selectedGem['is_bound']
                ];
            }
        }
        
        if (!empty($missingMaterials)) {
            $message = '宝石材料不足：';
            foreach ($missingMaterials as $index => $item) {
                $message .= "{$item['name']} (需要 {$item['required']} 个，拥有 {$item['has']} 个)";
                if ($index < count($missingMaterials) - 1) {
                    $message .= '，';
                }
            }
            return ['success' => false, 'message' => $message];
        }
        
        try {
            // 开始事务
            $this->db->beginTransaction();
            error_log("GemCraftingManager::craftGem - 开始宝石合成: playerId={$playerId}, recipeId={$recipeId}");
            
            // 1. 扣除金币
            $goldResult = $this->deductPlayerGold($playerId, $craftFee);
            error_log("GemCraftingManager::craftGem - 扣除金币: 金额={$craftFee}, 结果=" . ($goldResult ? "成功" : "失败"));
            
            if (!$goldResult) {
                throw new Exception("扣除金币失败");
            }
            
            // 2. 扣除材料
            foreach ($materialsToDeduct as $material) {
                $inventoryId = $material['inventory_id'];
                $quantity = $material['quantity'];
                $name = $material['name'];
                
                if ($inventoryId) {
                    error_log("GemCraftingManager::craftGem - 准备扣除宝石: inventoryId={$inventoryId}, 宝石={$name}, 数量={$quantity}");
                    $deductResult = $this->deductPlayerGem($inventoryId, $quantity);
                    
                    if (!$deductResult) {
                        throw new Exception("扣除宝石 {$name} 失败");
                    }
                }
            }
            
            // 3. 添加产出物品，使用绑定状态标记
            $addResult = $this->addItemToPlayerInventory(
                $playerId,
                $recipe['result_item_id'],
                $recipe['result_quantity'],
                $hasBoundMaterial // 如果使用了绑定材料，则产物也是绑定的
            );
            
            error_log("GemCraftingManager::craftGem - 添加产出宝石: 物品ID={$recipe['result_item_id']}, 数量={$recipe['result_quantity']}, 绑定={$hasBoundMaterial}, 结果=" . ($addResult ? "成功" : "失败"));
            
            if (!$addResult) {
                throw new Exception("添加产出宝石失败");
            }
            
            // 提交事务
            $this->db->commit();
            error_log("GemCraftingManager::craftGem - 宝石合成完成: 事务已提交");
            
            // 在消息中添加绑定状态提示
            $bindMessage = $hasBoundMaterial ? "（已绑定）" : "";
            
            return [
                'success' => true,
                'message' => "成功合成 {$recipe['result_quantity']} 个 {$recipe['result_item_name']}{$bindMessage}！",
                'recipe_name' => $recipe['name'],
                'result_item_name' => $recipe['result_item_name'],
                'result_quantity' => $recipe['result_quantity'],
                'is_bound' => $hasBoundMaterial
            ];
        } catch (Exception $e) {
            // 回滚事务
            $this->db->rollBack();
            error_log("GemCraftingManager::craftGem - 宝石合成失败: " . $e->getMessage());
            return [
                'success' => false,
                'message' => '宝石合成过程中发生错误: ' . $e->getMessage()
            ];
        }
    }

    /**
     * 获取单个宝石配方详情
     * @param int $recipeId 配方ID
     * @return array|bool 配方信息或false
     */
    private function getGemRecipe($recipeId) {
        $stmt = $this->db->prepare(
            "SELECT
                rt.id, rt.name, rt.result_item_id, rt.result_quantity, rt.craft_level, rt.description,
                it.name as result_item_name
             FROM gem_recipe_templates rt
             JOIN item_templates it ON rt.result_item_id = it.id
             WHERE rt.id = ?"
        );
        $stmt->execute([$recipeId]);
        $recipe = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();
        return $recipe;
    }

    /**
     * 扣除玩家金币
     * @param int $playerId 玩家ID
     * @param int $amount 金额
     * @return bool 是否成功
     */
    private function deductPlayerGold($playerId, $amount) {
        $stmt = $this->db->prepare(
            "UPDATE player_attributes SET gold = gold - ? WHERE account_id = ? AND gold >= ?"
        );
        $stmt->execute([$amount, $playerId, $amount]);
        return $stmt->rowCount() > 0;
    }

    /**
     * 扣除玩家宝石
     * @param int $inventoryId 库存ID
     * @param int $quantity 数量
     * @return bool 是否成功
     */
    private function deductPlayerGem($inventoryId, $quantity) {
        // 先检查是否有足够的数量
        $stmt = $this->db->prepare(
            "SELECT quantity FROM player_inventory WHERE id = ?"
        );
        $stmt->execute([$inventoryId]);
        $item = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if (!$item || $item['quantity'] < $quantity) {
            error_log("GemCraftingManager::deductPlayerGem - 宝石不足: inventoryId={$inventoryId}, 需要={$quantity}, 拥有=" . ($item ? $item['quantity'] : 0));
            return false;
        }

        try {
            // 如果扣除后数量为0，删除物品
            if ($item['quantity'] == $quantity) {
                $stmt = $this->db->prepare(
                    "DELETE FROM player_inventory WHERE id = ?"
                );
                $stmt->execute([$inventoryId]);
                $result = $stmt->rowCount() > 0;
                error_log("GemCraftingManager::deductPlayerGem - 删除宝石: inventoryId={$inventoryId}, 结果=" . ($result ? "成功" : "失败"));
                return $result;
            } else {
                // 否则减少数量
                $stmt = $this->db->prepare(
                    "UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?"
                );
                $stmt->execute([$quantity, $inventoryId]);
                $result = $stmt->rowCount() > 0;
                error_log("GemCraftingManager::deductPlayerGem - 减少宝石数量: inventoryId={$inventoryId}, 数量={$quantity}, 结果=" . ($result ? "成功" : "失败"));
                return $result;
            }
        } catch (Exception $e) {
            error_log("GemCraftingManager::deductPlayerGem - 异常: " . $e->getMessage());
            throw $e; // 重新抛出异常，让上层事务处理
        }
    }

    /**
     * 向玩家背包添加物品
     * @param int $playerId 玩家ID
     * @param int $itemTemplateId 物品模板ID
     * @param int $quantity 数量
     * @param bool $isBound 是否绑定
     * @return bool 是否成功
     */
    private function addItemToPlayerInventory($playerId, $itemTemplateId, $quantity, $isBound = false) {
        // 检查物品是否可堆叠
        $stmt = $this->db->prepare(
            "SELECT stackable, max_stack FROM item_templates WHERE id = ?"
        );
        $stmt->execute([$itemTemplateId]);
        $itemTemplate = $stmt->fetch(PDO::FETCH_ASSOC);
        $stmt->closeCursor();

        if (!$itemTemplate) {
            return false;
        }

        // 如果物品可堆叠，检查玩家是否已有该物品
        if ($itemTemplate['stackable']) {
            $stmt = $this->db->prepare(
                "SELECT id, quantity FROM player_inventory
                 WHERE player_id = ? AND item_template_id = ? AND is_equipped = 0
                 AND is_bound = ? LIMIT 1"
            );
            $stmt->execute([$playerId, $itemTemplateId, $isBound ? 1 : 0]);
            $existingItem = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            if ($existingItem) {
                // 已有物品，增加数量
                $newQuantity = $existingItem['quantity'] + $quantity;

                // 检查是否超过最大堆叠数量
                $maxStack = $itemTemplate['max_stack'] ?? 999;
                if ($newQuantity > $maxStack) {
                    // 如果超过最大堆叠数量，分开存储
                    $toAdd = $maxStack - $existingItem['quantity'];
                    $remaining = $quantity - $toAdd;

                    // 更新现有物品到最大堆叠数量
                    $stmt = $this->db->prepare(
                        "UPDATE player_inventory SET quantity = ? WHERE id = ?"
                    );
                    $stmt->execute([$maxStack, $existingItem['id']]);

                    // 递归调用自己添加剩余的物品
                    return $this->addItemToPlayerInventory($playerId, $itemTemplateId, $remaining, $isBound);
                } else {
                    // 未超过最大堆叠数量，直接更新
                    $stmt = $this->db->prepare(
                        "UPDATE player_inventory SET quantity = quantity + ? WHERE id = ?"
                    );
                    $stmt->execute([$quantity, $existingItem['id']]);
                    return $stmt->rowCount() > 0;
                }
            }
        }

        // 没有现有物品或物品不可堆叠，创建新物品
        $stmt = $this->db->prepare(
            "INSERT INTO player_inventory (player_id, item_template_id, quantity, is_equipped, is_bound)
             VALUES (?, ?, ?, 0, ?)"
        );
        $stmt->execute([$playerId, $itemTemplateId, $quantity, $isBound ? 1 : 0]);
        return $stmt->rowCount() > 0;
    }
}
