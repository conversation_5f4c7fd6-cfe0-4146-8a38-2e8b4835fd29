<?php
$pageTitle = "凝练组合系数管理";
$currentPage = "refine_combos";

require_once '../config/Database.php';
$db = Database::getInstance()->getConnection();

include 'layout_header.php';

// 获取所有组合系数
$combos_query = "SELECT id, combo_type, factor, description, priority FROM combo_factors ORDER BY priority DESC";
$combos_stmt = $db->prepare($combos_query);
$combos_stmt->execute();
$combos = $combos_stmt->fetchAll(PDO::FETCH_ASSOC);

// 处理表单提交
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (isset($_POST['save_combos'])) {
        foreach ($_POST['factors'] as $id => $factor) {
            $priority = $_POST['priorities'][$id] ?? 0;
            $description = $_POST['descriptions'][$id] ?? '';
            
            $update_query = "UPDATE combo_factors SET factor = ?, priority = ?, description = ? WHERE id = ?";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([$factor, $priority, $description, $id]);
        }
        echo "<div class='alert alert-success'>组合系数已更新</div>";
    }
    
    if (isset($_POST['add_combo'])) {
        $combo_type = $_POST['combo_type'];
        $factor = $_POST['factor'];
        $description = $_POST['description'];
        $priority = $_POST['priority'];
        
        // 检查是否已存在
        $check_query = "SELECT id FROM combo_factors WHERE combo_type = ?";
        $check_stmt = $db->prepare($check_query);
        $check_stmt->execute([$combo_type]);
        
        if ($check_stmt->rowCount() > 0) {
            // 更新
            $update_query = "UPDATE combo_factors SET factor = ?, description = ?, priority = ? WHERE combo_type = ?";
            $update_stmt = $db->prepare($update_query);
            $update_stmt->execute([$factor, $description, $priority, $combo_type]);
            echo "<div class='alert alert-success'>组合系数已更新</div>";
        } else {
            // 插入
            $insert_query = "INSERT INTO combo_factors (combo_type, factor, description, priority) VALUES (?, ?, ?, ?)";
            $insert_stmt = $db->prepare($insert_query);
            $insert_stmt->execute([$combo_type, $factor, $description, $priority]);
            echo "<div class='alert alert-success'>组合系数已添加</div>";
        }
        
        // 刷新页面
        header("Location: refine_combos.php");
        exit;
    }
    
    if (isset($_POST['delete_combo']) && isset($_POST['combo_id'])) {
        $combo_id = $_POST['combo_id'];
        $delete_query = "DELETE FROM combo_factors WHERE id = ?";
        $delete_stmt = $db->prepare($delete_query);
        $delete_stmt->execute([$combo_id]);
        
        echo "<div class='alert alert-success'>组合系数已删除</div>";
        header("Location: refine_combos.php");
        exit;
    }
}

// 组合类型中文名对照
$comboTypes = [
    'elemental_sequence' => '相生序列',
    'conflict_sequence' => '相克序列',
    'all_elements' => '五行俱全',
    'same_element' => '同属性材料',
    'default' => '无特殊组合'
];
?>

<div class="container-fluid">
    <p>管理不同组合类型的系数，用于装备凝练系统。</p>
    
    <div class="row">
        <div class="col-md-8">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-table mr-1"></i>
                    组合系数表
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="table-responsive">
                            <table class="table table-bordered" width="100%" cellspacing="0">
                                <thead>
                                    <tr>
                                        <th>组合类型</th>
                                        <th>系数</th>
                                        <th>优先级</th>
                                        <th>描述</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($combos as $combo): ?>
                                    <tr>
                                        <td>
                                            <?= isset($comboTypes[$combo['combo_type']]) 
                                                ? $comboTypes[$combo['combo_type']] 
                                                : htmlspecialchars($combo['combo_type']) ?>
                                            <small class="text-muted d-block"><?= htmlspecialchars($combo['combo_type']) ?></small>
                                        </td>
                                        <td>
                                            <input type="number" step="0.01" min="0.5" max="2" 
                                                class="form-control" 
                                                name="factors[<?= $combo['id'] ?>]" 
                                                value="<?= $combo['factor'] ?>">
                                        </td>
                                        <td>
                                            <input type="number" step="1" min="0" max="100" 
                                                class="form-control" 
                                                name="priorities[<?= $combo['id'] ?>]" 
                                                value="<?= $combo['priority'] ?>">
                                        </td>
                                        <td>
                                            <input type="text" class="form-control" 
                                                name="descriptions[<?= $combo['id'] ?>]" 
                                                value="<?= htmlspecialchars($combo['description']) ?>">
                                        </td>
                                        <td>
                                            <form method="post" action="" style="display:inline">
                                                <input type="hidden" name="combo_id" value="<?= $combo['id'] ?>">
                                                <button type="submit" name="delete_combo" class="btn btn-sm btn-danger" onclick="return confirm('确定要删除这个组合系数吗？')">删除</button>
                                            </form>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                        <button type="submit" name="save_combos" class="btn btn-primary">保存所有系数</button>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-md-4">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-plus mr-1"></i>
                    添加/修改组合系数
                </div>
                <div class="card-body">
                    <form method="post" action="">
                        <div class="form-group">
                            <label for="combo_type">组合类型:</label>
                            <select class="form-control" id="combo_type" name="combo_type" required>
                                <option value="elemental_sequence">相生序列 (elemental_sequence)</option>
                                <option value="conflict_sequence">相克序列 (conflict_sequence)</option>
                                <option value="all_elements">五行俱全 (all_elements)</option>
                                <option value="same_element">同属性材料 (same_element)</option>
                                <option value="default">无特殊组合 (default)</option>
                                <option value="custom">自定义组合类型</option>
                            </select>
                        </div>
                        
                        <div class="form-group" id="custom_type_group" style="display:none;">
                            <label for="custom_type">自定义组合类型名称:</label>
                            <input type="text" class="form-control" id="custom_type" name="custom_type">
                        </div>
                        
                        <div class="form-group">
                            <label for="factor">系数:</label>
                            <input type="number" step="0.01" min="0.5" max="2" class="form-control" id="factor" name="factor" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="priority">优先级 (数字越大优先级越高):</label>
                            <input type="number" step="1" min="0" max="100" class="form-control" id="priority" name="priority" required>
                        </div>
                        
                        <div class="form-group">
                            <label for="description">描述:</label>
                            <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                        </div>
                        
                        <button type="submit" name="add_combo" class="btn btn-success">添加/修改</button>
                    </form>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-info-circle mr-1"></i>
                    帮助信息
                </div>
                <div class="card-body">
                    <p>组合系数影响凝练最终结果的RV值。</p>
                    <p>凝练值（RV）计算公式:</p>
                    <p><code>RV = (材料总分值 × 组合系数) × (1 ± 5%)</code></p>
                    <p>优先级决定了当多个组合条件同时满足时，哪个组合系数会被应用。</p>
                    <p>默认组合类型:</p>
                    <ul>
                        <li><strong>相生序列</strong>: 材料顺序符合五行相生 (木→火→土→金→水)</li>
                        <li><strong>相克序列</strong>: 材料顺序符合五行相克 (金→木→土→水→火)</li>
                        <li><strong>五行俱全</strong>: 五种元素各选一个材料，不考虑顺序</li>
                        <li><strong>同属性材料</strong>: 所有材料属于同一元素类型</li>
                        <li><strong>无特殊组合</strong>: 默认基础系数</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    $('#combo_type').change(function() {
        if ($(this).val() === 'custom') {
            $('#custom_type_group').show();
        } else {
            $('#custom_type_group').hide();
        }
    });
    
    // 表单提交前处理自定义类型
    $('form').submit(function() {
        if ($('#combo_type').val() === 'custom' && $('#custom_type').val().trim() !== '') {
            $('#combo_type').after('<input type="hidden" name="combo_type" value="' + $('#custom_type').val().trim() + '">');
            $('#combo_type').attr('name', '');
        }
    });
});
</script>

<?php include 'layout_footer.php'; ?> 