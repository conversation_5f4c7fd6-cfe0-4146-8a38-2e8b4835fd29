<?php
$pageTitle = '仓库管理';
$currentPage = 'warehouse_expansion';
ob_start();
?>
<style>
    .expansion-item-card {
        border: 1px solid var(--border-color);
        border-radius: 6px;
        margin-bottom: 10px;
        padding: 10px;
        background-color: var(--card-bg);
        transition: transform 0.2s, box-shadow 0.2s;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }

    .expansion-item-card:hover {
        transform: translateY(-1px);
        box-shadow: 0 3px 8px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }

    .expansion-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 8px;
        padding: 5px;
    }

    .expansion-item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
    }

    .expansion-item-name {
        font-weight: bold;
        color: var(--primary-color);
        font-size: 1em;
    }

    .expansion-status {
        padding: 2px 6px;
        border-radius: 3px;
        font-size: 0.75em;
        font-weight: bold;
    }
    
    .expansion-status.active {
        background-color: #4CAF50;
        color: white;
    }
    
    .expansion-status.inactive {
        background-color: #f44336;
        color: white;
    }
    
    .expansion-details {
        margin-bottom: 8px;
    }

    .expansion-detail-item {
        font-size: 0.85em;
        line-height: 1.3;
        color: #666;
        text-align: center;
    }

    .expansion-actions {
        display: flex;
        justify-content: flex-end;
        gap: 6px;
    }
    
    .modal-overlay {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        justify-content: center;
        align-items: center;
    }
    
    .modal-content {
        background-color: var(--card-bg, #fff);
        border-radius: 5px;
        padding: 20px;
        width: 80%;
        max-width: 500px;
        max-height: 90vh;
        overflow-y: auto;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    .modal-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    
    .modal-close {
        background: none;
        border: none;
        font-size: 24px;
        cursor: pointer;
        color: var(--text-color);
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-group label {
        display: block;
        margin-bottom: 5px;
        font-weight: bold;
    }
    
    .form-group input,
    .form-group select {
        width: 100%;
        padding: 8px;
        border: 1px solid var(--border-color);
        border-radius: 4px;
        font-size: 14px;
    }
    
    .form-actions {
        display: flex;
        justify-content: flex-end;
        gap: 10px;
        margin-top: 20px;
    }
    
    .checkbox-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .checkbox-group input[type="checkbox"] {
        width: auto;
    }

    /* 气泡样式提示 */
    .toast {
        position: fixed;
        top: 20px;
        right: 20px;
        background-color: #333;
        color: #fff;
        padding: 12px 20px;
        border-radius: 4px;
        font-size: 14px;
        font-weight: bold;
        max-width: 300px;
        min-width: 200px;
        word-wrap: break-word;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 1060;
        transform: translateX(400px);
        opacity: 0;
        transition: all 0.3s ease;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .toast.show {
        transform: translateX(0);
        opacity: 1;
    }

    .toast.success {
        background-color: #28a745;
    }

    .toast.error {
        background-color: #dc3545;
    }

    .toast.warning {
        background-color: #ffc107;
        color: #212529;
    }

    /* 扩容记录区域样式 */
    .expansion-records-section {
        margin-top: 20px;
        border-top: 2px solid var(--border-color);
        padding-top: 15px;
    }

    .expansion-records-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }

    .expansion-records-filters {
        display: flex;
        gap: 10px;
        align-items: center;
        flex-wrap: wrap;
    }

    .expansion-records-table {
        width: 100%;
        border-collapse: collapse;
        background-color: var(--card-bg);
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    }

    .expansion-records-table th,
    .expansion-records-table td {
        padding: 12px;
        text-align: left;
        border-bottom: 1px solid var(--border-color);
    }

    .expansion-records-table th {
        background-color: var(--primary-color);
        color: white;
        font-weight: bold;
    }

    .expansion-records-table tbody tr:hover {
        background-color: rgba(0,0,0,0.05);
    }

    .expansion-records-table tbody tr:nth-child(even) {
        background-color: rgba(0,0,0,0.02);
    }

    .pagination-controls {
        display: flex;
        justify-content: center;
        align-items: center;
        margin-top: 20px;
        gap: 10px;
    }

    .pagination-info {
        color: var(--text-color);
        font-size: 0.9em;
    }

    .no-records {
        text-align: center;
        padding: 40px;
        color: #666;
        font-style: italic;
    }

    /* 页面布局优化 */
    .page-content {
        display: flex;
        flex-direction: column;
        height: calc(100vh - 120px);
    }

    .main-content-area.card {
        margin-bottom: 15px;
    }

    .expansion-records-section {
        flex: 1;
        display: flex;
        flex-direction: column;
    }

    .expansion-records-header {
        flex-shrink: 0;
    }

    #expansion-records-container {
        flex: 1;
        overflow-y: auto;
    }

    /* 标签页样式 */
    .tab-container {
        display: flex;
        border-bottom: 2px solid var(--border-color);
        margin-bottom: 20px;
    }

    .tab-button {
        padding: 10px 20px;
        background: none;
        border: none;
        border-bottom: 3px solid transparent;
        cursor: pointer;
        font-size: 16px;
        font-weight: bold;
        color: var(--text-color);
        transition: all 0.3s ease;
    }

    .tab-button:hover {
        background-color: rgba(0,0,0,0.05);
    }

    .tab-button.active {
        color: var(--primary-color);
        border-bottom-color: var(--primary-color);
        background-color: rgba(var(--primary-color-rgb, 0,123,255), 0.1);
    }

    .tab-content {
        display: none;
    }

    .tab-content.active {
        display: block;
    }

    /* 仓库库存样式 */
    .warehouse-inventory-section {
        margin-top: 20px;
    }

    .warehouse-list {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
    }

    .warehouse-card {
        border: 1px solid var(--border-color);
        border-radius: 8px;
        padding: 15px;
        background-color: var(--card-bg);
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: transform 0.2s, box-shadow 0.2s;
        height: 100%;
        display: flex;
        flex-direction: column;
    }

    .warehouse-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        border-color: var(--primary-color);
    }

    .warehouse-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        padding-bottom: 8px;
        border-bottom: 1px solid var(--border-color);
    }

    .warehouse-name {
        font-weight: bold;
        color: var(--primary-color);
        font-size: 1.1em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 200px;
    }

    .warehouse-stats {
        display: flex;
        flex-direction: column;
        gap: 8px;
        font-size: 0.9em;
        flex-grow: 1;
        margin-bottom: 10px;
    }

    .stat-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .stat-label {
        color: #666;
        margin-right: 10px;
    }

    .stat-value {
        font-weight: bold;
        color: var(--text-color);
    }

    /* 移除容量条相关样式，因为仓库没有总容量限制 */

    .warehouse-actions {
        margin-top: auto;
        display: flex;
        gap: 8px;
        justify-content: flex-end;
    }

    .warehouse-actions .btn {
        width: 100%;
    }

    /* 模态框内表格样式优化 */
    #warehouse-items-modal .expansion-records-table {
        margin-bottom: 0;
        border: none;
    }

    #warehouse-items-modal .expansion-records-table th {
        background-color: #f8f9fa;
        color: var(--text-color);
        font-size: 0.9em;
        padding: 8px 12px;
    }

    #warehouse-items-modal .expansion-records-table td {
        padding: 8px 12px;
        font-size: 0.9em;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .warehouse-list {
            grid-template-columns: 1fr;
            padding: 10px;
        }

        .warehouse-card {
            margin-bottom: 10px;
        }

        .card-header {
            flex-direction: column;
            gap: 15px;
        }

        .expansion-records-filters {
            flex-direction: column;
            width: 100%;
        }

        #warehouse-search {
            min-width: 100% !important;
        }

        .modal-content {
            width: 95% !important;
            max-width: none !important;
        }
    }
</style>
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="warehouse_management.js" defer></script>
<script src="global-toast.js" defer></script>
<?php
$extra_js = ob_get_clean();
require_once 'layout_header.php';
?>

<!-- 标签页导航 -->
<div class="tab-container">
    <button class="tab-button active" onclick="switchTab('expansion')">扩容物品管理</button>
    <button class="tab-button" onclick="switchTab('inventory')">仓库库存查看</button>
</div>

<!-- 扩容物品管理标签页 -->
<div id="expansion-tab" class="tab-content active">
    <div class="main-content-area card" style="margin-bottom: 15px;">
        <div class="card-header">
            <h3 style="margin: 0;">仓库扩容物品管理</h3>
            <button class="btn btn-primary" onclick="showCreateModal()">添加扩容物品</button>
        </div>

        <div id="expansion-grid" class="expansion-grid" style="max-height: 250px; overflow-y: auto;">
            <!-- 扩容物品卡片将通过JavaScript动态添加 -->
            <div class="expansion-item-card loading">
                <p style="text-align:center;">正在加载扩容物品数据...</p>
            </div>
        </div>
    </div>

    <!-- 玩家扩容记录区域 -->
    <div class="main-content-area card expansion-records-section" style="flex: 1; min-height: 500px;">
        <div class="expansion-records-header">
            <h3 style="margin: 0;">玩家扩容记录</h3>
            <div class="expansion-records-filters">
                <input type="text" id="player-search" placeholder="搜索玩家名称..." style="padding: 5px 10px; border: 1px solid var(--border-color); border-radius: 4px;" onkeypress="handleSearchKeyPress(event)">
                <select id="item-filter" style="padding: 5px 10px; border: 1px solid var(--border-color); border-radius: 4px;" onchange="loadExpansionRecords(1)">
                    <option value="">所有扩容物品</option>
                </select>
                <button class="btn btn-primary" onclick="loadExpansionRecords(1)">搜索</button>
                <button class="btn btn-secondary" onclick="clearFilters()">清空筛选</button>
            </div>
        </div>

        <div id="expansion-records-container" style="min-height: 400px; overflow-y: auto;">
            <div class="no-records">
                <p>正在加载扩容记录...</p>
            </div>
        </div>

        <div id="records-pagination" class="pagination-controls" style="display: none;">
            <button class="btn btn-sm btn-secondary" onclick="changePage(-1)">上一页</button>
            <span class="pagination-info" id="pagination-info"></span>
            <button class="btn btn-sm btn-secondary" onclick="changePage(1)">下一页</button>
        </div>
    </div>
</div>

<!-- 仓库库存查看标签页 -->
<div id="inventory-tab" class="tab-content">
    <div class="main-content-area card warehouse-inventory-section">
        <div class="card-header" style="display: flex; justify-content: space-between; align-items: center;">
            <h3 style="margin: 0;">仓库库存查看</h3>
            <div class="expansion-records-filters" style="display: flex; gap: 10px; align-items: center;">
                <input type="text" id="warehouse-search" placeholder="搜索仓库位置..."
                       style="padding: 8px 12px; border: 1px solid var(--border-color); border-radius: 4px; min-width: 250px;"
                       onkeypress="handleWarehouseSearchKeyPress(event)">
                <button class="btn btn-primary" onclick="loadWarehouseInventory()">
                    <i class="fas fa-search"></i> 搜索
                </button>
                <button class="btn btn-secondary" onclick="clearWarehouseFilters()">
                    <i class="fas fa-times"></i> 清空
                </button>
            </div>
        </div>

        <div id="warehouse-list" class="warehouse-list">
            <div class="warehouse-card" style="display: flex; justify-content: center; align-items: center; min-height: 150px;">
                <p style="text-align:center; font-size: 1.1em;">
                    <i class="fas fa-spinner fa-spin" style="margin-right: 10px;"></i> 正在加载仓库数据...
                </p>
            </div>
        </div>

        <div id="warehouse-pagination" class="pagination-controls" style="display: none; margin: 20px 0;">
            <button class="btn btn-sm btn-secondary" onclick="changeWarehousePage(-1)">
                <i class="fas fa-chevron-left"></i> 上一页
            </button>
            <span class="pagination-info" id="warehouse-pagination-info" style="margin: 0 15px;"></span>
            <button class="btn btn-sm btn-secondary" onclick="changeWarehousePage(1)">
                下一页 <i class="fas fa-chevron-right"></i>
            </button>
        </div>
    </div>
</div>

<!-- 创建/编辑扩容物品模态框 -->
<div id="expansion-modal" class="modal-overlay">
    <div class="modal-content">
        <div class="modal-header">
            <h2 id="modal-title">添加扩容物品</h2>
            <button class="modal-close" onclick="hideModal()">&times;</button>
        </div>
        <form id="expansion-form" onsubmit="handleFormSubmit(event)">
            <input type="hidden" id="expansion-id" name="id">
            <div class="form-group">
                <label for="item-template-id">扩容物品:</label>
                <select id="item-template-id" name="item_template_id" required>
                    <option value="">请选择物品</option>
                </select>
            </div>
            <div class="form-group">
                <label for="expansion-amount">扩容数量:</label>
                <input type="number" id="expansion-amount" name="expansion_amount" min="1" max="100" required>
                <small>每次使用该物品增加的仓库容量</small>
            </div>
            <div class="form-group">
                <label for="max-uses">最大使用次数:</label>
                <input type="number" id="max-uses" name="max_uses" min="1" placeholder="留空表示无限制">
                <small>每个玩家最多可以使用该物品的次数，留空表示无限制</small>
            </div>
            <div class="form-group">
                <div class="checkbox-group">
                    <input type="checkbox" id="is-active" name="is_active" checked>
                    <label for="is-active">启用该扩容物品</label>
                </div>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="hideModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 仓库物品详情模态框 -->
<div id="warehouse-items-modal" class="modal-overlay">
    <div class="modal-content" style="max-width: 900px; width: 90%;">
        <div class="modal-header">
            <h2 id="warehouse-modal-title" style="color: var(--primary-color);">
                <i class="fas fa-warehouse"></i> 仓库物品详情
            </h2>
            <button class="modal-close" onclick="hideWarehouseItemsModal()" style="font-size: 28px; color: #999;">&times;</button>
        </div>
        <div id="warehouse-items-container" style="max-height: 600px; overflow-y: auto; padding: 10px;">
            <div class="no-records" style="text-align: center; padding: 40px;">
                <p style="font-size: 1.1em;">
                    <i class="fas fa-spinner fa-spin" style="margin-right: 10px;"></i> 正在加载物品数据...
                </p>
            </div>
        </div>
    </div>
</div>

<div id="status-message-container"></div>

<?php require_once 'layout_footer.php'; ?>
