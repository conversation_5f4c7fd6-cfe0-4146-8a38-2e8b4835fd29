<?php
// api/get_players.php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');

require_once '../config/Database.php';

try {
    $db = Database::getInstance()->getConnection();
    $stmt = $db->prepare("SELECT id, username, level, hp, max_hp, attack FROM players ORDER BY id");
    $stmt->execute();
    $players = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    echo json_encode($players);
} catch (Exception $e) {
    echo json_encode(['error' => $e->getMessage()]);
}
?>
