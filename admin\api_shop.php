<?php
session_start();
header('Content-Type: application/json');

// 安全检查
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';

$db = Database::getInstance()->getConnection();
$action = $_GET['action'] ?? $_POST['action'] ?? '';
$response = ['success' => false, 'message' => '无效的商店操作'];

try {
    switch ($action) {
        case 'get_shop_items':
            $scene_building_id = filter_input(INPUT_GET, 'scene_building_id', FILTER_VALIDATE_INT);
            if (!$scene_building_id) {
                throw new Exception("无效的商店实例ID。");
            }

            $stmt = $db->prepare("
                SELECT 
                    si.id as shop_item_id, 
                    si.stock,
                    si.sort_order,
                    it.id as item_template_id,
                    it.name as item_name,
                    it.buy_price
                FROM shop_items si
                JOIN item_templates it ON si.item_template_id = it.id
                WHERE si.scene_building_id = ?
                ORDER BY si.sort_order ASC, it.name ASC
            ");
            $stmt->execute([$scene_building_id]);
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);

            $response = ['success' => true, 'data' => $items];
            break;
        
        case 'add_item':
            $scene_building_id = filter_input(INPUT_POST, 'scene_building_id', FILTER_VALIDATE_INT);
            $item_template_id = filter_input(INPUT_POST, 'item_template_id', FILTER_VALIDATE_INT);
            $stock = filter_input(INPUT_POST, 'stock', FILTER_VALIDATE_INT);
            $sort_order = filter_input(INPUT_POST, 'sort_order', FILTER_VALIDATE_INT);

            if (!$scene_building_id || !$item_template_id) {
                throw new Exception("缺少商店ID或物品ID。");
            }
            
            // 如果stock为空或无效，则设为NULL
            $stock = ($stock === false || $stock === '') ? null : $stock;
            // 如果sort_order为空或无效，则设为0
            $sort_order = ($sort_order === false || $sort_order === '') ? 0 : $sort_order;

            $stmt = $db->prepare("
                INSERT INTO shop_items (scene_building_id, item_template_id, stock, sort_order)
                VALUES (?, ?, ?, ?)
                ON DUPLICATE KEY UPDATE stock = VALUES(stock), sort_order = VALUES(sort_order)
            ");
            $stmt->execute([$scene_building_id, $item_template_id, $stock, $sort_order]);

            $response = ['success' => true, 'message' => '商品已成功添加或更新。'];
            break;

        case 'remove_item':
            $shop_item_id = filter_input(INPUT_POST, 'shop_item_id', FILTER_VALIDATE_INT);
            if (!$shop_item_id) {
                throw new Exception("无效的商品条目ID。");
            }

            $stmt = $db->prepare("DELETE FROM shop_items WHERE id = ?");
            $stmt->execute([$shop_item_id]);

            if ($stmt->rowCount() > 0) {
                $response = ['success' => true, 'message' => '商品已成功移除。'];
            } else {
                throw new Exception("找不到要删除的商品，或已被删除。");
            }
            break;

        default:
            $response['message'] = "未知的商店操作: " . htmlspecialchars($action);
            break;
    }
} catch (Exception $e) {
    $response['message'] = '操作失败: ' . $e->getMessage();
    error_log("Shop API Error: " . $e->getMessage());
}

echo json_encode($response); 