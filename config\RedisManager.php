<?php
// config/RedisManager.php - 使用连接池的重构版本

use Swoole\Database\RedisConfig;
use Swoole\Database\RedisPool;

class RedisManager {
    private static $instance = null;
    private ?RedisPool $pool = null;

    private function __construct() {
        $config = (new RedisConfig)
            ->withHost('127.0.0.1')
            ->withPort(6379)
            ->withAuth('')
            ->withDbIndex(0)
            ->withTimeout(1.0);
            
        $this->pool = new RedisPool($config, 64); // 创建一个最大并发为64的连接池
    }

    public static function getInstance(): self {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 从连接池获取一个Redis连接
     * @return \Swoole\ConnectionPool\ConnectionProxy|false
     */
    public function get() {
        if ($this->pool === null) {
            return false;
        }
        return $this->pool->get();
    }

    /**
     * 将Redis连接放回连接池
     * @param \Swoole\ConnectionPool\ConnectionProxy $redis
     */
    public function put($redis) {
        if ($this->pool === null || $redis === null) {
            return;
        }
        $this->pool->put($redis);
    }
    
    /**
     * 关闭连接池
     */
    public function close() {
        if ($this->pool) {
            $this->pool->close();
            $this->pool = null;
        }
    }
    
    /**
     * 执行一个需要Redis操作的闭包函数，自动处理连接的获取和释放
     * @param callable $callback
     * @param mixed|null $default
     * @return mixed
     */
    public function with(callable $callback, $default = null) {
        $redis = null;
        try {
            $redis = $this->get();
            if ($redis) {
                return $callback($redis);
            }
        } catch (Throwable $t) {
            error_log("Redis 'with' callback error: " . $t->getMessage());
            // 可以在这里处理异常，或者重新抛出
        } finally {
            if ($redis) {
                $this->put($redis);
            }
        }
        return $default;
    }
}
