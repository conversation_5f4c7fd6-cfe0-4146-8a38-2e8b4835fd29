<?php
session_start();
header('Content-Type: application/json');

// 安全检查
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    echo json_encode(['success' => false, 'message' => '未授权']);
    exit;
}

require_once __DIR__ . '/../config/Database.php';

// 获取数据库连接
$db = Database::getInstance()->getConnection();

$action = $_POST['action'] ?? $_GET['action'] ?? '';
$response = ['success' => false, 'message' => '无效的操作'];

try {
    switch ($action) {
        case 'get_warehouse_inventory':
            // 获取仓库库存概览
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = max(1, min(100, intval($_GET['limit'] ?? 12)));
            $offset = ($page - 1) * $limit;

            $warehouseSearch = trim($_GET['warehouse_search'] ?? '');

            // 构建查询条件
            $whereConditions = [];
            $params = [];

            if ($warehouseSearch) {
                $whereConditions[] = "s.name LIKE ?";
                $params[] = "%{$warehouseSearch}%";
            }

            $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // 获取总记录数
            $countSql = "
                SELECT COUNT(DISTINCT sb.id) as total
                FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                JOIN scenes s ON sb.scene_id = s.id
                LEFT JOIN warehouse_config wc ON sb.id = wc.scene_building_id
                WHERE b.type = 'WAREHOUSE'
                " . ($whereClause ? 'AND ' . $whereClause : '');

            $countStmt = $db->prepare($countSql);
            $countStmt->execute($params);
            $totalRecords = $countStmt->fetchColumn();
            $countStmt->closeCursor();

            $totalPages = ceil($totalRecords / $limit);

            // 获取仓库数据
            $sql = "
                SELECT
                    sb.id as scene_building_id,
                    s.name as scene_name,
                    COALESCE(wc.base_capacity, 50) as base_capacity,
                    COALESCE(wc.max_capacity, 200) as max_capacity,
                    (
                        SELECT COUNT(DISTINCT ws.player_id)
                        FROM warehouse_storage ws
                        WHERE ws.scene_building_id = sb.id
                    ) as player_count,
                    (
                        SELECT COUNT(DISTINCT ws.item_template_id)
                        FROM warehouse_storage ws
                        WHERE ws.scene_building_id = sb.id
                    ) as item_types,
                    (
                        SELECT COALESCE(SUM(ws.quantity), 0)
                        FROM warehouse_storage ws
                        WHERE ws.scene_building_id = sb.id
                    ) as total_items
                FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                JOIN scenes s ON sb.scene_id = s.id
                LEFT JOIN warehouse_config wc ON sb.id = wc.scene_building_id
                WHERE b.type = 'WAREHOUSE'
                " . ($whereClause ? 'AND ' . $whereClause : '') . "
                ORDER BY s.name ASC
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $warehouses = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            $response = [
                'success' => true,
                'data' => [
                    'warehouses' => $warehouses,
                    'pagination' => [
                        'current_page' => $page,
                        'total_pages' => $totalPages,
                        'total_records' => $totalRecords,
                        'per_page' => $limit
                    ]
                ]
            ];
            break;

        case 'get_warehouse_items':
            // 获取特定仓库的物品详情
            $sceneBuildingId = filter_input(INPUT_GET, 'scene_building_id', FILTER_VALIDATE_INT);
            if (!$sceneBuildingId) {
                throw new Exception("无效的仓库ID");
            }

            // 验证是否为仓库建筑
            $buildingStmt = $db->prepare("
                SELECT b.type
                FROM scene_buildings sb
                JOIN buildings b ON sb.building_id = b.id
                WHERE sb.id = ?
            ");
            $buildingStmt->execute([$sceneBuildingId]);
            $building = $buildingStmt->fetch(PDO::FETCH_ASSOC);
            $buildingStmt->closeCursor();

            if (!$building || $building['type'] !== 'WAREHOUSE') {
                throw new Exception("指定的建筑不是仓库");
            }

            // 获取仓库物品
            $itemsStmt = $db->prepare("
                SELECT
                    ws.*,
                    ws.player_id,
                    a.username as player_name,
                    it.name as item_name,
                    it.category
                FROM warehouse_storage ws
                JOIN accounts a ON ws.player_id = a.id
                JOIN item_templates it ON ws.item_template_id = it.id
                WHERE ws.scene_building_id = ?
                ORDER BY a.username ASC, it.category ASC, it.name ASC
            ");
            $itemsStmt->execute([$sceneBuildingId]);
            $items = $itemsStmt->fetchAll(PDO::FETCH_ASSOC);
            $itemsStmt->closeCursor();

            $response = ['success' => true, 'data' => $items];
            break;
        case 'get_expansion_records':
            // 获取扩容记录
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = max(1, min(100, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;

            $playerSearch = trim($_GET['player_search'] ?? '');
            $itemFilter = intval($_GET['item_filter'] ?? 0);

            // 构建查询条件
            $whereConditions = [];
            $params = [];

            if ($playerSearch) {
                $whereConditions[] = "a.username LIKE ?";
                $params[] = "%{$playerSearch}%";
            }

            if ($itemFilter > 0) {
                $whereConditions[] = "pwe.item_template_id = ?";
                $params[] = $itemFilter;
            }

            $whereClause = $whereConditions ? 'WHERE ' . implode(' AND ', $whereConditions) : '';

            // 获取总记录数
            $countSql = "
                SELECT COUNT(*) as total
                FROM player_warehouse_expansions pwe
                JOIN accounts a ON pwe.player_id = a.id
                JOIN item_templates it ON pwe.item_template_id = it.id
                LEFT JOIN scenes s ON s.id = (
                    SELECT sb.scene_id FROM scene_buildings sb WHERE sb.id = pwe.scene_building_id LIMIT 1
                )
                {$whereClause}
            ";

            $countStmt = $db->prepare($countSql);
            $countStmt->execute($params);
            $totalRecords = $countStmt->fetchColumn();
            $countStmt->closeCursor();

            $totalPages = ceil($totalRecords / $limit);

            // 获取记录数据
            $sql = "
                SELECT
                    pwe.*,
                    a.username as player_name,
                    it.name as item_name,
                    s.name as scene_name
                FROM player_warehouse_expansions pwe
                JOIN accounts a ON pwe.player_id = a.id
                JOIN item_templates it ON pwe.item_template_id = it.id
                LEFT JOIN scenes s ON s.id = (
                    SELECT sb.scene_id FROM scene_buildings sb WHERE sb.id = pwe.scene_building_id LIMIT 1
                )
                {$whereClause}
                ORDER BY pwe.created_at DESC
                LIMIT ? OFFSET ?
            ";

            $params[] = $limit;
            $params[] = $offset;

            $stmt = $db->prepare($sql);
            $stmt->execute($params);
            $records = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            $response = [
                'success' => true,
                'data' => [
                    'records' => $records,
                    'pagination' => [
                        'current_page' => $page,
                        'total_pages' => $totalPages,
                        'total_records' => $totalRecords,
                        'per_page' => $limit
                    ]
                ]
            ];
            break;

        case 'get_expansion_items_for_filter':
            // 获取用于筛选的扩容物品列表
            $stmt = $db->prepare("
                SELECT DISTINCT wei.item_template_id, it.name as item_name
                FROM warehouse_expansion_items wei
                JOIN item_templates it ON wei.item_template_id = it.id
                WHERE wei.is_active = 1
                ORDER BY it.name ASC
            ");
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();

            $response = ['success' => true, 'data' => $items];
            break;

        case 'get_list':
            // 获取扩容物品列表
            $stmt = $db->prepare("
                SELECT wei.*, it.name as item_name, it.category
                FROM warehouse_expansion_items wei
                JOIN item_templates it ON wei.item_template_id = it.id
                ORDER BY wei.expansion_amount DESC, it.name ASC
            ");
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            $response = ['success' => true, 'data' => $items];
            break;

        case 'get':
            $id = filter_input(INPUT_GET, 'id', FILTER_VALIDATE_INT);
            if (!$id) {
                throw new Exception("无效的ID");
            }
            
            $stmt = $db->prepare("
                SELECT wei.*, it.name as item_name, it.category
                FROM warehouse_expansion_items wei
                JOIN item_templates it ON wei.item_template_id = it.id
                WHERE wei.id = ?
            ");
            $stmt->execute([$id]);
            $item = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$item) {
                throw new Exception("找不到指定的扩容物品配置");
            }
            
            $response = ['success' => true, 'data' => $item];
            break;

        case 'create':
        case 'update':
            $isCreate = ($action === 'create');
            
            if (!$isCreate) {
                $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
                if (!$id) throw new Exception("更新操作需要一个有效的ID");
            }

            $itemTemplateId = filter_input(INPUT_POST, 'item_template_id', FILTER_VALIDATE_INT);
            $expansionAmount = filter_input(INPUT_POST, 'expansion_amount', FILTER_VALIDATE_INT);
            $maxUses = filter_input(INPUT_POST, 'max_uses', FILTER_VALIDATE_INT);
            $isActive = filter_input(INPUT_POST, 'is_active', FILTER_VALIDATE_BOOLEAN);

            if (!$itemTemplateId || !$expansionAmount) {
                throw new Exception("物品ID和扩容数量不能为空");
            }

            if ($expansionAmount <= 0) {
                throw new Exception("扩容数量必须大于0");
            }
            //如果数量为空则设为null
            if (empty($maxUses)) {
                $maxUses = null;
            }
            // 检查物品是否存在
            $stmt = $db->prepare("SELECT name FROM item_templates WHERE id = ?");
            $stmt->execute([$itemTemplateId]);
            $itemExists = $stmt->fetch(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            if (!$itemExists) {
                throw new Exception("指定的物品不存在");
            }

            $db->beginTransaction();
            try {
                if ($isCreate) {
                    // 检查是否已存在相同物品的配置
                    $stmt = $db->prepare("SELECT id FROM warehouse_expansion_items WHERE item_template_id = ?");
                    $stmt->execute([$itemTemplateId]);
                    if ($stmt->fetch()) {
                        throw new Exception("该物品已配置为扩容物品");
                    }
                    $stmt->closeCursor();

                    $sql = "INSERT INTO warehouse_expansion_items (item_template_id, expansion_amount, max_uses, is_active) 
                            VALUES (?, ?, ?, ?)";
                    $params = [$itemTemplateId, $expansionAmount, $maxUses, $isActive ? 1 : 0];
                } else {
                    // 检查是否已存在相同物品的配置（排除当前记录）
                    $stmt = $db->prepare("SELECT id FROM warehouse_expansion_items WHERE item_template_id = ? AND id != ?");
                    $stmt->execute([$itemTemplateId, $id]);
                    if ($stmt->fetch()) {
                        throw new Exception("该物品已配置为扩容物品");
                    }
                    $stmt->closeCursor();

                    $sql = "UPDATE warehouse_expansion_items SET 
                            item_template_id = ?, expansion_amount = ?, max_uses = ?, is_active = ?, updated_at = NOW()
                            WHERE id = ?";
                    $params = [$itemTemplateId, $expansionAmount, $maxUses, $isActive ? 1 : 0, $id];
                }
                
                $stmt = $db->prepare($sql);
                $stmt->execute($params);
                
                $db->commit();
                
                $response = [
                    'success' => true, 
                    'message' => $isCreate ? '扩容物品配置创建成功' : '扩容物品配置更新成功'
                ];
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;

        case 'delete':
            $id = filter_input(INPUT_POST, 'id', FILTER_VALIDATE_INT);
            if (!$id) {
                throw new Exception("无效的ID");
            }
            
            $db->beginTransaction();
            try {
                $stmt = $db->prepare("DELETE FROM warehouse_expansion_items WHERE id = ?");
                $stmt->execute([$id]);
                
                if ($stmt->rowCount() === 0) {
                    throw new Exception("找不到要删除的扩容物品配置");
                }
                
                $db->commit();
                $response = ['success' => true, 'message' => '扩容物品配置删除成功'];
            } catch (Exception $e) {
                $db->rollBack();
                throw $e;
            }
            break;

        case 'get_available_items':
            // 获取可用作扩容物品的物品列表（排除已配置的）
            $stmt = $db->prepare("
                SELECT it.id, it.name, it.category
                FROM item_templates it
                WHERE it.id NOT IN (SELECT item_template_id FROM warehouse_expansion_items)
                ORDER BY it.category, it.name
            ");
            $stmt->execute();
            $items = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $stmt->closeCursor();
            
            $response = ['success' => true, 'data' => $items];
            break;

        default:
            throw new Exception("未知的操作: " . $action);
    }
} catch (Exception $e) {
    error_log("Warehouse expansion API error: " . $e->getMessage());
    $response = ['success' => false, 'message' => $e->getMessage()];
}

echo json_encode($response);
?>
