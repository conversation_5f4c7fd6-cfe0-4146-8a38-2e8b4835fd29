/* Security Violations Page Styles */

/* Container and Layout */
.container-fluid {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Row and Column Layout */
.row {
    display: flex;
    flex-wrap: wrap;
    margin: 0 -15px;
}

.col-md-6 {
    flex: 0 0 50%;
    max-width: 50%;
    padding: 0 15px;
    display: flex;
    flex-direction: column;
}

/* 确保并列卡片高度一致 */
.row .col-md-6 .card {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.row .col-md-6 .card .card-body {
    flex: 1;
}

.mb-4 {
    margin-bottom: 1.5rem;
}

/* Card Styles */
.card {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin-bottom: 20px;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
    padding: 15px 20px;
    border-radius: 8px 8px 0 0;
}

.card-header h5 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.card-body {
    padding: 20px;
}

/* Alert Styles */
.alert {
    padding: 12px 16px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

/* Table Styles */
.table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.table th,
.table td {
    padding: 12px 15px;
    vertical-align: top;
    border-bottom: 1px solid #dee2e6;
    text-align: left;
}

.table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #dee2e6;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0,0,0,.05);
}

.table tbody tr:hover {
    background-color: rgba(0,0,0,.075);
}

.table-sm th,
.table-sm td {
    padding: 8px 12px;
}

.table-responsive {
    display: block;
    width: 100%;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 0.25em 0.4em;
    font-size: 75%;
    font-weight: 700;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

/* Button Styles */
.btn {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    white-space: nowrap;
    vertical-align: middle;
    user-select: none;
    border: 1px solid transparent;
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    line-height: 1.5;
    border-radius: 0.25rem;
    text-decoration: none;
    cursor: pointer;
    transition: color 0.15s ease-in-out, background-color 0.15s ease-in-out, border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.5;
    border-radius: 0.2rem;
}

.btn-danger {
    color: #fff;
    background-color: #dc3545;
    border-color: #dc3545;
}

.btn-danger:hover {
    color: #fff;
    background-color: #c82333;
    border-color: #bd2130;
}

.btn-warning {
    color: #212529;
    background-color: #ffc107;
    border-color: #ffc107;
}

.btn-warning:hover {
    color: #212529;
    background-color: #e0a800;
    border-color: #d39e00;
}

/* Pagination Styles */
.pagination {
    display: flex;
    padding-left: 0;
    list-style: none;
    border-radius: 0.25rem;
    margin: 20px 0;
}

.page-item {
    margin: 0 2px;
}

.page-item.active .page-link {
    z-index: 3;
    color: #fff;
    background-color: #007bff;
    border-color: #007bff;
}

.page-link {
    position: relative;
    display: block;
    padding: 0.5rem 0.75rem;
    margin-left: -1px;
    line-height: 1.25;
    color: #007bff;
    background-color: #fff;
    border: 1px solid #dee2e6;
    text-decoration: none;
    border-radius: 0.25rem;
}

.page-link:hover {
    z-index: 2;
    color: #0056b3;
    text-decoration: none;
    background-color: #e9ecef;
    border-color: #dee2e6;
}

/* Text Utilities */
.text-muted {
    color: #6c757d;
}

/* Small Text */
small {
    font-size: 0.875em;
}

/* Responsive Design */
/* 在平板尺寸下保持并列显示 */
@media (min-width: 769px) and (max-width: 991px) {
    .col-md-6 {
        flex: 0 0 50%;
        max-width: 50%;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .table th,
    .table td {
        padding: 10px 8px;
    }
}

/* 只在手机尺寸下堆叠显示 */
@media (max-width: 768px) {
    .col-md-6 {
        flex: 0 0 100%;
        max-width: 100%;
        margin-bottom: 20px;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .table th,
    .table td {
        padding: 8px 10px;
    }

    .card-body {
        padding: 15px;
    }
}

/* Additional Spacing */
h2 {
    margin-bottom: 20px;
    color: #333;
}

/* Form Styles for Inline Forms */
form[style*="display:inline"] {
    display: inline-block !important;
}

/* Enhanced Table Styling */
.table thead th {
    position: sticky;
    top: 0;
    z-index: 10;
    background-color: #f8f9fa;
}

.table td {
    vertical-align: middle;
}

/* IP Address Column */
.table td:nth-child(2) {
    min-width: 120px;
}

/* Time Column */
.table td:nth-child(1) {
    min-width: 100px;
}

/* Reason Column */
.table td:nth-child(3) {
    min-width: 120px;
}

/* Action Column */
.table td:last-child {
    min-width: 80px;
    text-align: center;
}

/* Statistics Cards Enhancement */
.card-body p {
    margin-bottom: 0;
    color: #6c757d;
    font-style: italic;
}

/* 统计卡片特殊样式 */
.row.mb-4 .card {
    min-height: 300px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.row.mb-4 .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 统计卡片标题样式 */
.row.mb-4 .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 2px solid #dee2e6;
}

.row.mb-4 .card-header h5 {
    color: #495057;
    font-weight: 700;
}

/* 确保统计表格在卡片内正确显示 */
.row.mb-4 .table-sm {
    margin-bottom: 0;
}

.row.mb-4 .card-body {
    overflow-y: auto;
}

/* Better spacing for badges */
.badge + .badge {
    margin-left: 5px;
}

/* Improved button spacing */
.btn + .btn {
    margin-left: 5px;
}

/* Loading state for tables */
.table-loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.table-loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Better mobile table handling */
@media (max-width: 576px) {
    .table-responsive {
        border: none;
    }

    .table td,
    .table th {
        padding: 8px 6px;
        font-size: 0.8rem;
    }

    .badge {
        font-size: 0.65rem;
        padding: 0.2em 0.3em;
    }

    .btn-sm {
        padding: 0.2rem 0.4rem;
        font-size: 0.7rem;
    }
}
