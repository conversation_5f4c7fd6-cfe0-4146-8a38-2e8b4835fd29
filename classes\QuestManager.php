<?php
/**
 * QuestManager.php - 任务管理类
 * 管理游戏中的任务系统，包括任务接取、完成、放弃和进度更新等功能
 */

class QuestManager {
    private $dbFactory;
    private array $questCache = [];
    private array $questObjectivesCache = [];
    
    /**
     * 构造函数
     * @param PDO|callable $db 数据库连接对象或连接工厂函数
     */
    public function __construct($db) {
        if ($db instanceof PDO) {
            $this->dbFactory = function() use ($db) { return $db; };
        } else {
            $this->dbFactory = $db;
        }
    }
    
    /**
     * 获取数据库连接
     * @return PDO
     */
    private function getConnection() {
        return call_user_func($this->dbFactory);
    }
    
    /**
     * 清除任务缓存
     */
    public function clearCache() {
        $this->questCache = [];
        $this->questObjectivesCache = [];
        
        return [
            'success' => true,
            'message' => '任务缓存已成功清除'
        ];
    }
    
    /**
     * 获取所有任务
     * @return array 任务数组
     */
    public function getAllQuests(): array {
        if (empty($this->questCache)) {
            $conn = $this->getConnection();
            $stmt = $conn->query("
                SELECT q.*, 
                       n1.name AS giver_npc_name, 
                       n2.name AS receiver_npc_name
                FROM quests q
                LEFT JOIN npc_templates n1 ON q.giver_npc_id = n1.id
                LEFT JOIN npc_templates n2 ON q.receiver_npc_id = n2.id
                ORDER BY q.min_level, q.id
            ");
            $this->questCache = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        return $this->questCache;
    }
    
    /**
     * 获取任务目标
     * @param int $questId 任务ID
     * @return array 任务目标数组
     */
    public function getQuestObjectives($questId): array {
        if (!isset($this->questObjectivesCache[$questId])) {
            $conn = $this->getConnection();
            $stmt = $conn->prepare("
                SELECT * FROM quest_objectives 
                WHERE quest_id = ? 
                ORDER BY id
            ");
            $stmt->execute([$questId]);
            $this->questObjectivesCache[$questId] = $stmt->fetchAll(PDO::FETCH_ASSOC);
        }
        
        return $this->questObjectivesCache[$questId];
    }
    
    /**
     * 获取任务详情
     * @param int $questId 任务ID
     * @return array|null 任务详情
     */
    public function getQuestDetails($questId) {
        $conn = $this->getConnection();
        $stmt = $conn->prepare("
            SELECT q.*, 
                   n1.name AS giver_npc_name, 
                   n2.name AS receiver_npc_name
            FROM quests q
            LEFT JOIN npc_templates n1 ON q.giver_npc_id = n1.id
            LEFT JOIN npc_templates n2 ON q.receiver_npc_id = n2.id
            WHERE q.id = ?
        ");
        $stmt->execute([$questId]);
        $quest = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$quest) {
            return null;
        }
        
        // 获取任务目标
        $quest['objectives'] = $this->getQuestObjectives($questId);
        foreach ($quest['objectives'] as &$objective) {
            $this->addObjectiveTargetDetails($objective);
        }
        unset($objective);
        
        // 解析JSON字段
        if (!empty($quest['reward_items'])) {
            $quest['reward_items'] = json_decode($quest['reward_items'], true);
            $this->enrichRewardItems($quest['reward_items']);
        } else {
            $quest['reward_items'] = [];
        }
        
        if (!empty($quest['prerequisite_quests'])) {
            $quest['prerequisite_quests'] = json_decode($quest['prerequisite_quests'], true);
        } else {
            $quest['prerequisite_quests'] = [];
        }
        
        return $quest;
    }
    
    /**
     * 获取NPC可提供的任务列表
     * @param int $npcId NPC ID
     * @param int $playerId 玩家ID
     * @return array 可用任务列表
     */
    public function getAvailableQuestsFromNPC($npcId, $playerId) {
        $conn = $this->getConnection();

        // Step 1: Get the NPC's template ID from its instance ID
        $stmt = $conn->prepare("SELECT template_id FROM npc_instances WHERE id = ?");
        $stmt->execute([$npcId]);
        $npcInstance = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$npcInstance) {
            error_log("Could not find NPC instance with ID: {$npcId}");
            return []; // No NPC instance found
        }
        $templateId = $npcInstance['template_id'];
        error_log("[Quest] NPC instance {$npcId} has template ID {$templateId}. Checking for available quests.");

        // Step 2: Get all quests given by this NPC template
        $stmt = $conn->prepare("SELECT * FROM quests WHERE giver_npc_id = ?");
        $stmt->execute([$templateId]);
        $allQuests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log("[Quest] Found " . count($allQuests) . " quests for NPC template ID {$templateId}.");

        $availableQuests = [];
        
        foreach ($allQuests as $quest) {
            // 检查玩家是否满足接取任务的条件
            if ($this->canPlayerAcceptQuest($playerId, $quest)) {
                // 获取任务目标
                $quest['objectives'] = $this->getQuestObjectives($quest['id']);
                foreach ($quest['objectives'] as &$objective) {
                    $this->addObjectiveTargetDetails($objective);
                }
                unset($objective);
                
                // 解析JSON字段
                if (!empty($quest['reward_items'])) {
                    $quest['reward_items'] = json_decode($quest['reward_items'], true);
                    $this->enrichRewardItems($quest['reward_items']);
                } else {
                    $quest['reward_items'] = [];
                }
                
                if (!empty($quest['prerequisite_quests'])) {
                    $quest['prerequisite_quests'] = json_decode($quest['prerequisite_quests'], true);
                } else {
                    $quest['prerequisite_quests'] = [];
                }
                
                $availableQuests[] = $quest;
                error_log("[Quest] Quest ID: {$quest['id']} ({$quest['title']}) is available for player {$playerId}");
            } else {
                error_log("[Quest] Quest ID: {$quest['id']} is NOT available for player {$playerId}");
            }
        }
        
        error_log("[Quest] Total available quests from NPC {$npcId} for player {$playerId}: " . count($availableQuests));
        return $availableQuests;
    }
    
    /**
     * 检查玩家是否可以接取任务
     * @param int $playerId 玩家ID
     * @param array $quest 任务信息
     * @return bool 是否可以接取
     */
    private function canPlayerAcceptQuest($playerId, $quest) {
        $conn = $this->getConnection();
        $questId = $quest['id'];
        $logPrefix = "[任务检查 {$questId} 玩家 {$playerId}] ";

        // 检查玩家等级
        $stmt = $conn->prepare("
            SELECT level FROM player_attributes 
            WHERE account_id = ?
        ");
        $stmt->execute([$playerId]);
        $playerAttr = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$playerAttr) {
            error_log($logPrefix . "未找到玩家属性，返回失败。");
            return false;
        }

        if ($playerAttr['level'] < $quest['min_level']) {
            error_log($logPrefix . "等级检查失败。玩家等级: {$playerAttr['level']}, 需要等级: {$quest['min_level']}。");
            return false;
        }
        error_log($logPrefix . "等级检查通过。玩家等级: {$playerAttr['level']}, 需要等级: {$quest['min_level']}。");

        // 检查玩家是否已经接取或完成了此任务
        $stmt = $conn->prepare("
            SELECT status, started_at, completed_at FROM player_quests 
            WHERE player_quests.player_id = ? AND player_quests.quest_id = ?
            ORDER BY id DESC 
            LIMIT 1
        ");
        $stmt->execute([$playerId, $questId]);
        $playerQuest = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($playerQuest) {
            error_log($logPrefix . "玩家已有此任务记录，状态为: '{$playerQuest['status']}'。");
            if (!$quest['is_repeatable']) {
                error_log($logPrefix . "重复性检查失败。此任务不可重复接取。");
                return false; // 不可重复任务，已经有记录就不能再接
            } else if ($playerQuest['status'] === 'active') {
                error_log($logPrefix . "重复性检查失败。可重复任务当前已处于进行中。");
                return false; // 可重复任务，但正在进行中，不能再接
            } else if ($quest['is_repeatable']) {
                // 检查是否已在同一天完成或接取过（日常任务限制）
                $today = date('Y-m-d');
                
                // 获取最后一次接取或完成的日期
                $lastDate = null;
                if (!empty($playerQuest['completed_at'])) {
                    $lastDate = date('Y-m-d', strtotime($playerQuest['completed_at']));
                } else if (!empty($playerQuest['started_at'])) {
                    $lastDate = date('Y-m-d', strtotime($playerQuest['started_at']));
                }
                
                if ($lastDate === $today) {
                    error_log($logPrefix . "日常重复性检查失败。今天已经接取过此任务。");
                    return false; // 今天已经接取过此任务
                }
                
                error_log($logPrefix . "日常重复性检查通过。上次日期: {$lastDate}, 今天: {$today}");
            }
            
            error_log($logPrefix . "重复性检查通过。任务可重复且今天未接取。");
        } else {
             error_log($logPrefix . "玩家没有此任务的记录。检查通过。");
        }
        
        // 检查前置任务要求
        $prerequisites = [];
        if (!empty($quest['prerequisite_quests'])) {
             if (is_string($quest['prerequisite_quests'])) {
                $prerequisites = json_decode($quest['prerequisite_quests'], true) ?: [];
            } elseif (is_array($quest['prerequisite_quests'])) {
                $prerequisites = $quest['prerequisite_quests'];
            }
        }

        if (!empty($prerequisites)) {
            error_log($logPrefix . "检查前置任务要求: " . json_encode($prerequisites));
            foreach ($prerequisites as $preQuestId) {
                $stmt = $conn->prepare("
                    SELECT status FROM player_quests 
                    WHERE player_id = ? AND quest_id = ? AND status = 'completed'
                ");
                $stmt->execute([$playerId, $preQuestId]);
                $preQuest = $stmt->fetch(PDO::FETCH_ASSOC);
                
                if (!$preQuest) {
                    error_log($logPrefix . "前置任务检查失败。前置任务 {$preQuestId} 未完成。");
                    return false; // 前置任务未完成
                }
            }
            error_log($logPrefix . "所有前置任务检查通过。");
        } else {
            error_log($logPrefix . "此任务没有前置任务要求。检查通过。");
        }
        
        error_log($logPrefix . "所有检查通过。任务可接取。");
        return true;
    }
    
    /**
     * 玩家接取任务
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @return array 操作结果
     */
    public function acceptQuest($playerId, $questId) {
        $logPrefix = "[接受任务 {$questId} 玩家 {$playerId}] ";
        error_log($logPrefix . "尝试接受任务。");

        // 获取任务信息
        $quest = $this->getQuestDetails($questId);
        
        if (!$quest) {
            error_log($logPrefix . "失败。未找到任务详情。");
            return ['success' => false, 'message' => '任务不存在'];
        }
        
        // 检查玩家是否能接取该任务
        error_log($logPrefix . "正在执行玩家接任务条件检查...");
        if (!$this->canPlayerAcceptQuest($playerId, $quest)) {
            error_log($logPrefix . "失败。玩家不满足接取任务条件。");
            return ['success' => false, 'message' => '无法接取任务，请检查等级要求或前置任务是否完成'];
        }
        error_log($logPrefix . "玩家接取任务条件检查通过。");
        
        // 为玩家创建任务记录
        $objectives = $this->getQuestObjectives($questId);
        $objectivesProgress = [];
        
        foreach ($objectives as $objective) {
            $objectiveId = (int)$objective['id']; // 确保使用整数键
            $objectivesProgress[$objectiveId] = 0; // 初始进度为0
        }
        
        try {
            $conn = $this->getConnection();
            $conn->beginTransaction();
            error_log($logPrefix . "数据库事务已开启。");
            
            // 检查是否已经存在记录 (为可重复任务)
            $stmt = $conn->prepare("
                SELECT id FROM player_quests 
                WHERE player_id = ? AND quest_id = ?
            ");
            $stmt->execute([$playerId, $questId]);
            $playerQuest = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($playerQuest) {
                error_log($logPrefix . "找到现有任务记录。更新状态为'进行中'。");
                // 更新现有记录
                $stmt = $conn->prepare("
                    UPDATE player_quests 
                    SET status = 'active', 
                        objectives_progress = ?,
                        started_at = NOW()
                    WHERE id = ?
                ");
                $stmt->execute([json_encode($objectivesProgress), $playerQuest['id']]);
            } else {
                error_log($logPrefix . "无现有任务记录。插入新记录。");
                // 插入新记录
                $stmt = $conn->prepare("
                    INSERT INTO player_quests (player_id, quest_id, status, objectives_progress, started_at) 
                    VALUES (?, ?, 'active', ?, NOW())
                ");
                $stmt->execute([$playerId, $questId, json_encode($objectivesProgress)]);
            }
            
            // 检查背包中是否已有收集任务所需物品
            $this->checkInventoryForCollectObjectives($playerId, $questId, $objectives);
            
            $conn->commit();
            error_log($logPrefix . "成功。数据库事务已提交。");
            
            return [
                'success' => true,
                'message' => '任务接受成功！'
            ];
        } catch (Exception $e) {
            if (isset($conn) && $conn->inTransaction()) {
                $conn->rollBack();
            }
            error_log($logPrefix . "失败。数据库事务已回滚。错误: " . $e->getMessage());
            return ['success' => false, 'message' => '接受任务时发生错误'];
        }
    }
    
    /**
     * 检查玩家背包中是否已有收集任务所需物品
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @param array $objectives 任务目标数组
     */
    private function checkInventoryForCollectObjectives($playerId, $questId, $objectives) {
        $conn = $this->getConnection();
        $collectObjectives = [];
        
        // 筛选出收集类型的任务目标
        foreach ($objectives as $objective) {
            if ($objective['type'] === 'collect') {
                $collectObjectives[] = $objective;
            }
        }
        
        if (empty($collectObjectives)) {
            return; // 没有收集类型的任务目标，直接返回
        }
        
        error_log("[CheckInventory] Player {$playerId}, Quest {$questId}: Checking inventory for collect objectives. Found " . count($collectObjectives) . " collect objectives.");
        
        // 获取玩家背包中的物品
        $itemTargetIds = array_column($collectObjectives, 'target_id');
        $placeholders = implode(',', array_fill(0, count($itemTargetIds), '?'));
        
        if (empty($placeholders)) {
            return; // 安全检查
        }
        
        $stmt = $conn->prepare("
            SELECT item_template_id, SUM(quantity) as total_quantity 
            FROM player_inventory 
            WHERE player_id = ? AND item_template_id IN ({$placeholders})
            GROUP BY item_template_id
        ");
        
        $params = [$playerId];
        foreach ($itemTargetIds as $id) {
            $params[] = $id;
        }
        
        $stmt->execute($params);
        $inventoryItems = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);
        
        if (empty($inventoryItems)) {
            error_log("[CheckInventory] No matching items found in inventory.");
            return;
        }
        
        // 获取当前任务进度
        $stmt = $conn->prepare("
            SELECT objectives_progress 
            FROM player_quests 
            WHERE player_id = ? AND quest_id = ? AND status = 'active'
        ");
        $stmt->execute([$playerId, $questId]);
        $progressData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$progressData) {
            error_log("[CheckInventory] No active quest record found.");
            return;
        }
        
        $objectivesProgress = json_decode($progressData['objectives_progress'], true) ?: [];
        $updated = false;
        
        // 更新任务进度
        foreach ($collectObjectives as $objective) {
            $objectiveId = (int)$objective['id'];
            $targetId = (int)$objective['target_id'];
            $requiredQuantity = (int)$objective['quantity'];
            
            if (isset($inventoryItems[$targetId])) {
                $availableQuantity = (int)$inventoryItems[$targetId];
                $currentProgress = isset($objectivesProgress[$objectiveId]) ? (int)$objectivesProgress[$objectiveId] : 0;
                
                // 如果背包中有物品且数量与当前进度不同，更新为实际数量
                if ($availableQuantity > 0 && $currentProgress != $availableQuantity) {
                    $objectivesProgress[$objectiveId] = $availableQuantity;
                    $updated = true;
                    error_log("[CheckInventory] Updated objective {$objectiveId} progress to {$availableQuantity}. Required: {$requiredQuantity}, Player has: {$availableQuantity}.");
                }
            }
        }
        
        // 如果有更新，保存到数据库
        if ($updated) {
            $stmt = $conn->prepare("
                UPDATE player_quests 
                SET objectives_progress = ? 
                WHERE player_id = ? AND quest_id = ? AND status = 'active'
            ");
            $stmt->execute([json_encode($objectivesProgress), $playerId, $questId]);
            error_log("[CheckInventory] Saved updated objectives progress for player {$playerId}, quest {$questId}.");
        }
    }
    
    /**
     * 获取玩家的任务列表
     * @param int $playerId 玩家ID
     * @param string $status 任务状态（可选，不指定则获取所有）
     * @return array 玩家任务列表
     */
    public function getPlayerQuests($playerId, $status = null) {
        $conn = $this->getConnection();
        $params = [$playerId];
        $statusFilter = '';
        $limitClause = '';

        if ($status) {
            $statusFilter = "AND pq.status = ?";
            $params[] = $status;

            // 如果是获取已完成的任务，限制只返回最新的5个
            if ($status === 'completed') {
                $limitClause = "LIMIT 5";
            }
        }

        $stmt = $conn->prepare("
            SELECT pq.*, q.title, q.description, q.type,
                   q.reward_gold, q.reward_exp, q.reward_items,
                   q.giver_npc_id, q.receiver_npc_id,
                   nt_giver.name AS giver_npc_name,
                   nt_receiver.name AS receiver_npc_name
            FROM player_quests pq
            JOIN quests q ON pq.quest_id = q.id
            LEFT JOIN npc_templates nt_giver ON q.giver_npc_id = nt_giver.id
            LEFT JOIN npc_templates nt_receiver ON q.receiver_npc_id = nt_receiver.id
            WHERE pq.player_id = ? $statusFilter
            ORDER BY pq.status, pq.completed_at DESC, pq.started_at DESC
            $limitClause
        ");

        $stmt->execute($params);
        $playerQuests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取每个任务的目标详情
        foreach ($playerQuests as &$playerQuest) {
            // 解析任务进度JSON
            if (!empty($playerQuest['objectives_progress'])) {
                $playerQuest['objectives_progress'] = json_decode($playerQuest['objectives_progress'], true);
            } else {
                $playerQuest['objectives_progress'] = [];
            }
            
            // 解析奖励物品JSON
            if (!empty($playerQuest['reward_items'])) {
                $playerQuest['reward_items'] = json_decode($playerQuest['reward_items'], true);
                $this->enrichRewardItems($playerQuest['reward_items']);
            } else {
                $playerQuest['reward_items'] = [];
            }
            
            // 获取任务目标
            $objectives = $this->getQuestObjectives($playerQuest['quest_id']);
            $playerQuest['objectives'] = [];
            
            foreach ($objectives as $objective) {
                $objectiveId = (int)$objective['id'];
                $progress = isset($playerQuest['objectives_progress'][$objectiveId]) ? (int)$playerQuest['objectives_progress'][$objectiveId] : 0;
                $objective['progress'] = $progress;
                $objective['completed'] = ($progress >= $objective['quantity']);
                
                // 添加目标对象的详情（NPC、物品、怪物）
                $this->addObjectiveTargetDetails($objective);
                
                $playerQuest['objectives'][] = $objective;
            }
            
            // 计算总体进度
            $totalObjectives = count($playerQuest['objectives']);
            $completedObjectives = 0;
            
            foreach ($playerQuest['objectives'] as $objective) {
                if ($objective['completed']) {
                    $completedObjectives++;
                }
            }
            
            $playerQuest['progress_percentage'] = $totalObjectives > 0 ? 
                round(($completedObjectives / $totalObjectives) * 100) : 0;
                
            // 添加简化的任务交付信息
            if (!empty($playerQuest['receiver_npc_name'])) {
                $playerQuest['receiver_location'] = [
                    'npc_name' => $playerQuest['receiver_npc_name']
                ];
            }
        }
        
        return $playerQuests;
    }
    
    /**
     * 添加任务目标对象的详细信息
     * @param array &$objective 任务目标引用
     */
    private function addObjectiveTargetDetails(&$objective) {
        if (empty($objective['target_id'])) {
            return;
        }

        $conn = $this->getConnection();
        
        switch ($objective['type']) {
            case 'kill':
                // 获取怪物详情
                $stmt = $conn->prepare("
                    SELECT name, description FROM monster_templates
                    WHERE id = ?
                ");
                $stmt->execute([$objective['target_id']]);
                $monster = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $objective['target_details'] = [
                    'type' => 'monster',
                    'name' => $monster ? $monster['name'] : '未知生物',
                    'description' => $monster ? $monster['description'] : ''
                ];
                break;
                
            case 'collect':
                // 获取物品详情
                $stmt = $conn->prepare("
                    SELECT name, description, category FROM item_templates
                    WHERE id = ?
                ");
                $stmt->execute([$objective['target_id']]);
                $item = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $objective['target_details'] = $item ? [
                    'type' => 'item',
                    'name' => $item['name'],
                    'description' => $item['description'],
                    'category' => $item['category']
                ] : [
                    'type' => 'item',
                    'name' => '未知物品',
                    'description' => '',
                    'category' => 'Misc'
                ];
                break;
                
            case 'dialogue':
            case 'visit':
                // 获取NPC名称 - 目标ID应为NPC模板ID
                $stmt = $conn->prepare("
                    SELECT name 
                    FROM npc_templates
                    WHERE id = ?
                ");
                $stmt->execute([$objective['target_id']]);
                $npc = $stmt->fetch(PDO::FETCH_ASSOC);
                
                $objective['target_details'] = [
                    'type' => 'npc',
                    'name' => $npc ? $npc['name'] : '未知人物'
                ];
                break;
        }
    }
    
    /**
     * 更新任务进度
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @param int $objectiveId 目标ID
     * @param int $increment 增量
     * @return array 更新结果
     */
    public function updateQuestProgress($playerId, $questId, $objectiveId, $increment = 1) {
        error_log("[QuestManager] updateQuestProgress: player {$playerId}, quest {$questId}, objective {$objectiveId}, increment {$increment}");
        try {
            $conn = $this->getConnection();
            $conn->beginTransaction();
            
            // 获取当前任务进度
            $stmt = $conn->prepare("
                SELECT objectives_progress FROM player_quests 
                WHERE player_id = ? AND quest_id = ? AND status = 'active'
            ");
            $stmt->execute([$playerId, $questId]);
            $progressData = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$progressData) {
                $conn->rollBack();
                error_log("[QuestManager] updateQuestProgress: No active quest found for player {$playerId}, quest {$questId}");
                return ['success' => false, 'message' => '没有找到对应的进行中任务'];
            }
            
            // 获取任务目标详情
            $objective = null;
            $allObjectives = $this->getQuestObjectives($questId);
            foreach ($allObjectives as $obj) {
                if ($obj['id'] == $objectiveId) {
                    $objective = $obj;
                    break;
                }
            }
            
            if (!$objective) {
                $conn->rollBack();
                error_log("[QuestManager] updateQuestProgress: Objective {$objectiveId} not found for quest {$questId}");
                return ['success' => false, 'message' => '任务目标不存在'];
            }

            // 更新进度
            // 确保objectives_progress是一个数组
            if (is_string($progressData['objectives_progress'])) {
                $progressData['objectives_progress'] = json_decode($progressData['objectives_progress'], true) ?: [];
            }
            
            // 确保objectiveId是整数
            $objectiveId = (int)$objectiveId;
            $currentProgress = isset($progressData['objectives_progress'][$objectiveId]) ? (int)$progressData['objectives_progress'][$objectiveId] : 0;
            error_log("[QuestManager] Current progress: {$currentProgress} for objective {$objectiveId}");
            
            // 处理进度增减
            $newProgress = $currentProgress + $increment;
            
            // 确保进度不会小于0，但允许大于目标值
            $newProgress = max(0, $newProgress);
            error_log("[QuestManager] New progress: {$newProgress} for objective {$objectiveId} (target: {$objective['quantity']})");
            
            // 只有当进度有变化时才更新数据库
            if ($newProgress !== $currentProgress) {
                error_log("[QuestManager] Progress changed, updating database");
                $progressData['objectives_progress'][$objectiveId] = $newProgress;
                
                // 更新数据库
                $stmt = $conn->prepare("
                    UPDATE player_quests 
                    SET objectives_progress = ? 
                    WHERE player_id = ? AND quest_id = ? AND status = 'active'
                ");
                $stmt->execute([json_encode($progressData['objectives_progress']), $playerId, $questId]);
                error_log("[QuestManager] Database updated with new progress JSON: " . json_encode($progressData['objectives_progress']));
            } else {
                error_log("[QuestManager] No progress change, skipping database update");
            }
            
            $conn->commit();
            
            // 检查任务是否可以完成
            $canComplete = true;
            foreach ($allObjectives as $obj) {
                $objId = (int)$obj['id'];
                $progress = isset($progressData['objectives_progress'][$objId]) ? (int)$progressData['objectives_progress'][$objId] : 0;
                if ($progress < $obj['quantity']) {
                    $canComplete = false;
                    break;
                }
            }
            
            error_log("[QuestManager] Quest {$questId} can complete: " . ($canComplete ? 'true' : 'false'));
            
            return [
                'success' => true,
                'message' => '任务进度已更新',
                'objective_id' => $objectiveId,
                'old_progress' => $currentProgress,
                'new_progress' => $newProgress,
                'target' => $objective['quantity'],
                'can_complete' => $canComplete
            ];
        } catch (Exception $e) {
            if (isset($conn) && $conn->inTransaction()) {
                $conn->rollBack();
            }
            error_log("[ERROR] 更新任务进度时出错: " . $e->getMessage() . "\n" . $e->getTraceAsString());
            return ['success' => false, 'message' => '更新任务进度时发生错误'];
        }
    }
    
    /**
     * 完成任务
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @return array 操作结果
     */
    public function completeQuest($playerId, $questId) {
        try {
            $conn = $this->getConnection();
            $conn->beginTransaction();
            
            // 验证任务是否可以完成
            $stmt = $conn->prepare("
                SELECT q.*, pq.status, pq.objectives_progress
                FROM player_quests pq
                JOIN quests q ON pq.quest_id = q.id
                WHERE pq.player_id = ? AND pq.quest_id = ?
            ");
            $stmt->execute([$playerId, $questId]);
            $playerQuest = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$this->canPlayerCompleteQuest($playerQuest, $questId)) {
                $conn->rollBack();
                return ['success' => false, 'message' => '任务尚未完成或不满足交付条件'];
            }
            
            // 获取任务目标
            $objectives = $this->getQuestObjectives($questId);
            $objectivesProgress = json_decode($playerQuest['objectives_progress'], true) ?: [];
            
            // 检查并扣除收集任务所需物品
            $collectResult = $this->processCollectObjectives($playerId, $objectives, $objectivesProgress);
            if (!$collectResult['success']) {
                $conn->rollBack();
                return $collectResult;
            }
            
            // 更新任务状态
            $stmt = $conn->prepare("
                UPDATE player_quests 
                SET status = 'completed', completed_at = NOW() 
                WHERE player_id = ? AND quest_id = ?
            ");
            $stmt->execute([$playerId, $questId]);
            
            // 发放奖励
            $rewards = [];
            if ($playerQuest['reward_exp'] > 0) {
                $rewards['exp'] = $playerQuest['reward_exp'];
            }

            // 发放金币
            if ($playerQuest['reward_gold'] > 0) {
                $stmt = $conn->prepare("
                    UPDATE player_attributes SET gold = gold + ?
                    WHERE account_id = ?
                ");
                $stmt->execute([$playerQuest['reward_gold'], $playerId]);
                $rewards['gold'] = $playerQuest['reward_gold'];
            }

            // 发放物品奖励
            if (!empty($playerQuest['reward_items'])) {
                $rewardItems = json_decode($playerQuest['reward_items'], true);
                $rewardedItemsInfo = [];

                error_log("[Quest Reward] Player {$playerId}: Processing item rewards: " . json_encode($rewardItems));

                if (is_array($rewardItems)) {
                    foreach ($rewardItems as $item) {
                        $itemTemplateId = $item['id'] ?? $item['item_id'] ?? $item['template_id'] ?? null;
                        $quantity = $item['quantity'] ?? 1;

                        if ($itemTemplateId) {
                            error_log("[Quest Reward] Player {$playerId}: Adding item template {$itemTemplateId} x{$quantity}");
                            $itemInfo = $this->addItemToPlayerInventory($playerId, $itemTemplateId, $quantity);
                            if ($itemInfo) {
                               $rewardedItemsInfo[] = $itemInfo;
                               error_log("[Quest Reward] Player {$playerId}: Successfully added {$itemInfo['name']} x{$itemInfo['quantity']}");
                            } else {
                               error_log("[Quest Reward] Player {$playerId}: Failed to add item template {$itemTemplateId}");
                            }
                        }
                    }
                }
                $rewards['items'] = $rewardedItemsInfo;
                error_log("[Quest Reward] Player {$playerId}: Total items rewarded: " . count($rewardedItemsInfo));
            }

            $conn->commit();
            
            return [
                'success' => true,
                'message' => "任务 '{$playerQuest['title']}' 完成！",
                'rewards' => $rewards
            ];
            
        } catch (Exception $e) {
            if (isset($conn) && $conn->inTransaction()) {
                $conn->rollBack();
            }
            error_log("完成任务时出错: " . $e->getMessage());
            return ['success' => false, 'message' => '完成任务时发生错误'];
        }
    }
    
    /**
     * 处理收集任务目标，检查并扣除所需物品
     * @param int $playerId 玩家ID
     * @param array $objectives 任务目标数组
     * @param array $objectivesProgress 任务进度数组
     * @return array 处理结果
     */
    private function processCollectObjectives($playerId, $objectives, $objectivesProgress) {
        $conn = $this->getConnection();
        $collectObjectives = [];
        
        // 筛选出收集类型的任务目标
        foreach ($objectives as $objective) {
            if ($objective['type'] === 'collect') {
                $objectiveId = (int)$objective['id'];
                $progress = isset($objectivesProgress[$objectiveId]) ? (int)$objectivesProgress[$objectiveId] : 0;
                
                // 只处理已完成的收集目标
                if ($progress >= $objective['quantity']) {
                    $collectObjectives[] = [
                        'id' => $objectiveId,
                        'target_id' => (int)$objective['target_id'],
                        'quantity' => (int)$objective['quantity']
                    ];
                }
            }
        }
        
        if (empty($collectObjectives)) {
            return ['success' => true]; // 没有收集类型的任务目标，直接返回成功
        }
        
        error_log("[ProcessCollect] Player {$playerId}: Processing " . count($collectObjectives) . " collect objectives.");
        
        // 检查玩家背包中是否有足够的物品
        foreach ($collectObjectives as $objective) {
            $targetId = $objective['target_id'];
            $requiredQuantity = $objective['quantity'];
            
            $stmt = $conn->prepare("
                SELECT SUM(quantity) as total_quantity 
                FROM player_inventory 
                WHERE player_id = ? AND item_template_id = ?
            ");
            $stmt->execute([$playerId, $targetId]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            
            $availableQuantity = (int)($result['total_quantity'] ?? 0);
            
            if ($availableQuantity < $requiredQuantity) {
                // 获取物品名称
                $stmt = $conn->prepare("SELECT name FROM item_templates WHERE id = ?");
                $stmt->execute([$targetId]);
                $itemName = $stmt->fetchColumn() ?: "未知物品";
                
                error_log("[ProcessCollect] Player {$playerId}: Not enough items. Required {$requiredQuantity} of item {$targetId}, but only has {$availableQuantity}.");
                return [
                    'success' => false, 
                    'message' => "完成任务所需的物品不足: {$itemName} ({$availableQuantity}/{$requiredQuantity})"
                ];
            }
        }
        
        // 扣除物品
        foreach ($collectObjectives as $objective) {
            $targetId = $objective['target_id'];
            $requiredQuantity = $objective['quantity'];
            $remainingToRemove = $requiredQuantity;
            
            // 获取玩家背包中的该物品条目
            $stmt = $conn->prepare("
                SELECT id, quantity 
                FROM player_inventory 
                WHERE player_id = ? AND item_template_id = ? 
                ORDER BY quantity DESC
            ");
            $stmt->execute([$playerId, $targetId]);
            $inventoryItems = $stmt->fetchAll(PDO::FETCH_ASSOC);
            
            foreach ($inventoryItems as $item) {
                if ($remainingToRemove <= 0) {
                    break;
                }
                
                $itemId = $item['id'];
                $itemQuantity = (int)$item['quantity'];
                $toRemove = min($remainingToRemove, $itemQuantity);
                
                if ($toRemove >= $itemQuantity) {
                    // 如果需要移除的数量大于等于该堆叠的数量，删除整个条目
                    $stmt = $conn->prepare("DELETE FROM player_inventory WHERE id = ?");
                    $stmt->execute([$itemId]);
                } else {
                    // 否则减少数量
                    $stmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity - ? WHERE id = ?");
                    $stmt->execute([$toRemove, $itemId]);
                }
                
                $remainingToRemove -= $toRemove;
            }
            
            // 获取物品名称用于日志
            $stmt = $conn->prepare("SELECT name FROM item_templates WHERE id = ?");
            $stmt->execute([$targetId]);
            $itemName = $stmt->fetchColumn() ?: "未知物品";
            
            error_log("[ProcessCollect] Player {$playerId}: Removed {$requiredQuantity} of item {$targetId} ({$itemName}).");
        }
        
        return ['success' => true];
    }
    
    
    /**
     * 添加物品到玩家背包
     * @param int $playerId 玩家ID
     * @param int $templateId 物品模板ID
     * @param int $quantity 数量
     * @return array|null 物品信息
     */
    private function addItemToPlayerInventory($playerId, $templateId, $quantity) {
        if ($quantity <= 0) return null;

        $conn = $this->getConnection();
        
        try {
            // 获取物品信息
            $itemStmt = $conn->prepare("SELECT * FROM item_templates WHERE id = ?");
            $itemStmt->execute([$templateId]);
            $itemTemplate = $itemStmt->fetch(PDO::FETCH_ASSOC);

            if (!$itemTemplate) {
                error_log("物品模板未找到: ID {$templateId}");
                return null;
            }

            // 检查物品是否可堆叠
            if ($itemTemplate['stackable']) {
                $remainingQuantity = $quantity;
                $maxStack = (int)$itemTemplate['max_stack'];

                // 查找背包中未满的同类物品堆叠
                $invStmt = $conn->prepare(
                    "SELECT * FROM player_inventory WHERE player_id = ? AND item_template_id = ? AND quantity < ? AND is_bound = 1"
                );
                $invStmt->execute([$playerId, $templateId, $maxStack]);
                $existingStacks = $invStmt->fetchAll(PDO::FETCH_ASSOC);

                // 填充现有堆叠
                foreach ($existingStacks as $stack) {
                    if ($remainingQuantity <= 0) break;
                    
                    $spaceLeft = $maxStack - (int)$stack['quantity'];
                    $toAdd = min($remainingQuantity, $spaceLeft);

                    $updateStmt = $conn->prepare("UPDATE player_inventory SET quantity = quantity + ? WHERE id = ? AND is_bound = 1");
                    $updateStmt->execute([$toAdd, $stack['id']]);
                    $remainingQuantity -= $toAdd;
                }

                // 如果还有剩余，创建新堆叠
                while ($remainingQuantity > 0) {
                    $toAdd = min($remainingQuantity, $maxStack);
                    $insertStmt = $conn->prepare(
                        "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound) VALUES (?, ?, ?, ?, ?)"
                    );
                    $insertStmt->execute([$playerId, $templateId, $toAdd, null, 1]);
                    $remainingQuantity -= $toAdd;
                }
            } else {
                // 不可堆叠物品，为每个物品创建新条目
                $insertStmt = $conn->prepare(
                    "INSERT INTO player_inventory (player_id, item_template_id, quantity, instance_data, is_bound) VALUES (?, ?, ?, ?, ?)"
                );
                for ($i = 0; $i < $quantity; $i++) {
                    $insertStmt->execute([$playerId, $templateId, 1, null, 1]);
                }
            }
            
            return ['name' => $itemTemplate['name'], 'quantity' => $quantity];

        } catch (Exception $e) {
            error_log("添加物品到背包失败: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * 玩家放弃任务
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @return array 操作结果
     */
    public function abandonQuest($playerId, $questId) {
        try {
            $conn = $this->getConnection();
            
            // 获取任务详情，检查是否可放弃
            $quest = $this->getQuestDetails($questId);
            if (!$quest) {
                return ['success' => false, 'message' => '任务不存在'];
            }
            
            // 检查玩家是否正在进行此任务
            $stmt = $conn->prepare("
                SELECT status FROM player_quests 
                WHERE player_id = ? AND quest_id = ?
            ");
            $stmt->execute([$playerId, $questId]);
            $playerQuest = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if (!$playerQuest || $playerQuest['status'] !== 'active') {
                return ['success' => false, 'message' => '无法放弃此任务'];
            }
            
            // 删除任务记录
            $stmt = $conn->prepare("
                DELETE FROM player_quests 
                WHERE player_id = ? AND quest_id = ?
            ");
            $stmt->execute([$playerId, $questId]);
            
            return ['success' => true, 'message' => "任务 '{$quest['title']}' 已放弃"];
            
        } catch (Exception $e) {
            error_log("放弃任务时出错: " . $e->getMessage());
            return ['success' => false, 'message' => '放弃任务时发生错误'];
        }
    }
    
    /**
     * 检查特定目标类型的任务是否有更新
     * @param int $playerId 玩家ID
     * @param string $objectiveType 目标类型(dialogue, collect, kill, visit)
     * @param int $targetId 目标ID
     * @param int $increment 增量
     * @return array 更新任务列表
     */
    public function checkQuestObjectiveUpdates($playerId, $objectiveType, $targetId, $increment = 1) {
        error_log("[QuestManager] Starting checkQuestObjectiveUpdates for player {$playerId}, type {$objectiveType}, target {$targetId}, increment {$increment}");
        $conn = $this->getConnection();
        
        // 查找玩家所有正在进行的任务
        $stmt = $conn->prepare("
            SELECT pq.quest_id, q.title, q.receiver_npc_id, nt.name as receiver_npc_name
            FROM player_quests pq
            JOIN quests q ON pq.quest_id = q.id
            LEFT JOIN npc_templates nt ON q.receiver_npc_id = nt.id
            WHERE pq.player_id = ? AND pq.status = 'active'
        ");
        $stmt->execute([$playerId]);
        $activeQuests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log("[QuestManager] Found " . count($activeQuests) . " active quests for player {$playerId}");
        
        $updatedQuests = [];
        
        foreach ($activeQuests as $quest) {
            // 获取该任务的所有目标
            $objectives = $this->getQuestObjectives($quest['quest_id']);
            error_log("[QuestManager] Quest {$quest['quest_id']} ({$quest['title']}) has " . count($objectives) . " objectives");
            
            foreach ($objectives as $objective) {
                // 检查目标是否匹配
                if ($objective['type'] == $objectiveType && $objective['target_id'] == $targetId) {
                    error_log("[QuestManager] Found matching objective: type={$objective['type']}, target_id={$objective['target_id']}, id={$objective['id']} for quest {$quest['quest_id']}");
                    
                    // 对于收集类型任务，当增量为负数时，表示物品减少（如使用或出售物品）
                    if ($objectiveType == 'collect' && $increment < 0) {
                        // 获取当前进度
                        $currentProgress = $this->getCurrentObjectiveProgress($playerId, $quest['quest_id'], $objective['id']);
                        error_log("[QuestManager] Current progress for objective {$objective['id']}: {$currentProgress}");
                        
                        // 只有当前进度大于0时才更新
                        if ($currentProgress > 0) {
                            // 物品数量减少不会使任务进度为负数
                            $result = $this->updateQuestProgress($playerId, $quest['quest_id'], $objective['id'], $increment);
                            
                            if ($result['success']) {
                                // 添加目标对象的详细信息
                                $this->addObjectiveTargetDetails($objective);
                                
                                // 构建更新信息
                                $updatedQuests[] = [
                                    'quest_id' => $quest['quest_id'],
                                    'title' => $quest['title'],
                                    'objective_id' => $objective['id'],
                                    'description' => isset($objective['description']) ? $objective['description'] : "收集 {$objective['target_details']['name']}",
                                    'new_progress' => $result['new_progress'],
                                    'target' => $result['target'],
                                    'can_complete' => $result['can_complete'],
                                    'receiver_npc_name' => $quest['receiver_npc_name']
                                ];
                                
                                error_log("[QuestManager] Updated quest progress for quest {$quest['quest_id']}, new progress: {$result['new_progress']}/{$result['target']}");
                            }
                        } else {
                            error_log("[QuestManager] Skip updating quest {$quest['quest_id']} since current progress is 0");
                        }
                    } else {
                        // 对于增量为正数的情况（物品增加、杀怪等）
                        $result = $this->updateQuestProgress($playerId, $quest['quest_id'], $objective['id'], $increment);
                        
                        if ($result['success']) {
                            // 添加目标对象的详细信息
                            $this->addObjectiveTargetDetails($objective);
                            
                            // 构建更新信息
                            $updatedQuests[] = [
                                'quest_id' => $quest['quest_id'],
                                'title' => $quest['title'],
                                'objective_id' => $objective['id'],
                                'description' => isset($objective['description']) ? $objective['description'] : ($objectiveType == 'kill' ? "击杀 {$objective['target_details']['name']}" : "收集 {$objective['target_details']['name']}"),
                                'new_progress' => $result['new_progress'],
                                'target' => $result['target'],
                                'can_complete' => $result['can_complete'],
                                'receiver_npc_name' => $quest['receiver_npc_name']
                            ];
                            
                            error_log("[QuestManager] Updated quest progress for quest {$quest['quest_id']}, new progress: {$result['new_progress']}/{$result['target']}");
                        }
                    }
                }
            }
        }
        
        error_log("[QuestManager] checkQuestObjectiveUpdates returning " . count($updatedQuests) . " updated quests");
        return $updatedQuests;
    }
    
    /**
     * 获取当前任务目标的进度
     * @param int $playerId 玩家ID
     * @param int $questId 任务ID
     * @param int $objectiveId 目标ID
     * @return int 当前进度
     */
    private function getCurrentObjectiveProgress($playerId, $questId, $objectiveId) {
        $conn = $this->getConnection();
        $stmt = $conn->prepare("
            SELECT objectives_progress FROM player_quests 
            WHERE player_id = ? AND quest_id = ? AND status = 'active'
        ");
        $stmt->execute([$playerId, $questId]);
        $progressData = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if (!$progressData) {
            error_log("[QuestManager] No active quest record found for player {$playerId}, quest {$questId}");
            return 0;
        }
        
        $objectivesProgress = json_decode($progressData['objectives_progress'], true) ?: [];
        $progress = isset($objectivesProgress[$objectiveId]) ? (int)$objectivesProgress[$objectiveId] : 0;
        error_log("[QuestManager] getCurrentObjectiveProgress: {$progress} for player {$playerId}, quest {$questId}, objective {$objectiveId}");
        return $progress;
    }
    
    /**
     * 获取NPC可完成的任务列表
     * @param int $npcId NPC ID
     * @param int $playerId 玩家ID
     * @return array 可完成的任务列表
     */
    public function getCompletableQuestsForNPC($npcId, $playerId) {
        $conn = $this->getConnection();
        
        // Step 1: Get the NPC's template ID from its instance ID
        $stmt = $conn->prepare("SELECT template_id FROM npc_instances WHERE id = ?");
        $stmt->execute([$npcId]);
        $npcInstance = $stmt->fetch(PDO::FETCH_ASSOC);

        if (!$npcInstance) {
            error_log("[Quest] Could not find NPC instance with ID: {$npcId} for completing quests.");
            return [];
        }
        $templateId = $npcInstance['template_id'];
        error_log("[Quest] NPC instance {$npcId} has template ID {$templateId}. Checking for completable quests.");

        // Step 2: Find all active quests for the player that can be turned in to this NPC template
        $stmt = $conn->prepare("
            SELECT 
                pq.quest_id,
                pq.status,
                pq.objectives_progress,
                q.title,
                q.description,
                q.receiver_npc_id,
                nt.name AS receiver_npc_name
            FROM player_quests pq
            JOIN quests q ON pq.quest_id = q.id
            LEFT JOIN npc_templates nt ON q.receiver_npc_id = nt.id
            WHERE pq.player_id = ? AND pq.status = 'active' AND q.receiver_npc_id = ?
        ");
        $stmt->execute([$playerId, $templateId]);
        $playerQuests = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        error_log("[Quest] Found " . count($playerQuests) . " active quests for player {$playerId} to be turned into NPC template {$templateId}");
        
        $completableQuests = [];
        foreach ($playerQuests as $playerQuest) {
            error_log("[Quest] Checking if quest ID {$playerQuest['quest_id']} is completable...");
            
            if ($this->canPlayerCompleteQuest($playerQuest, $playerQuest['quest_id'])) {
                // 获取完整任务详情
                $questDetails = $this->getQuestDetails($playerQuest['quest_id']);
                if ($questDetails) {
                    // 确保进度数据是数组格式
                    $objectivesProgress = [];
                    if (!empty($playerQuest['objectives_progress'])) {
                        if (is_string($playerQuest['objectives_progress'])) {
                            $objectivesProgress = json_decode($playerQuest['objectives_progress'], true) ?: [];
                        } else if (is_array($playerQuest['objectives_progress'])) {
                            $objectivesProgress = $playerQuest['objectives_progress'];
                        }
                    }
                    
                    // 更新任务目标的进度信息
                    foreach ($questDetails['objectives'] as &$objective) {
                        $objectiveId = (int)$objective['id'];
                        $progress = isset($objectivesProgress[$objectiveId]) ? (int)$objectivesProgress[$objectiveId] : 0;
                        $objective['progress'] = $progress;
                        $objective['completed'] = ($progress >= $objective['quantity']);
                    }
                    unset($objective); // 解除引用
                    
                    // 确保显示交付NPC的名称
                    if (!empty($playerQuest['receiver_npc_name'])) {
                        $questDetails['receiver_npc_name'] = $playerQuest['receiver_npc_name'];
                    }
                    
                    $questDetails['player_progress'] = $playerQuest; // 附加上玩家进度
                    $completableQuests[] = $questDetails;
                    error_log("[Quest] Quest ID: {$playerQuest['quest_id']} ({$questDetails['title']}) is completable.");
                }
            } else {
                error_log("[Quest] Quest ID: {$playerQuest['quest_id']} is not completable yet.");
            }
        }
        
        error_log("[Quest] Total completable quests at NPC {$npcId} for player {$playerId}: " . count($completableQuests));
        return $completableQuests;
    }
    
    /**
     * 检查玩家是否可以完成任务
     * @param array $playerQuest 玩家任务数据
     * @param int $questId 任务ID
     * @return bool
     */
    private function canPlayerCompleteQuest($playerQuest, $questId) {
        if ($playerQuest['status'] !== 'active') {
            return false;
        }

        $objectives = $this->getQuestObjectives($questId);
        
        // 确保进度数据是一个数组
        $progress = [];
        if (!empty($playerQuest['objectives_progress'])) {
            if (is_string($playerQuest['objectives_progress'])) {
                $progress = json_decode($playerQuest['objectives_progress'], true) ?: [];
            } else if (is_array($playerQuest['objectives_progress'])) {
                $progress = $playerQuest['objectives_progress'];
            }
        }

        foreach ($objectives as $objective) {
            $objId = (int)$objective['id'];
            $currentProgress = isset($progress[$objId]) ? (int)$progress[$objId] : 0;
            if ($currentProgress < $objective['quantity']) {
                return false; // 任意一个目标未完成，则任务未完成
            }
        }

        return true;
    }
    
    /**
     * 为任务奖励物品数组添加物品名称
     * @param array &$items 物品奖励数组的引用
     */
    private function enrichRewardItems(array &$items): void
    {
        if (empty($items) || !is_array($items)) {
            return;
        }

        $conn = $this->getConnection();
        
        // 确保使用正确的键名，并过滤掉无效的条目
        $itemIds = array_map('intval', array_filter(array_column($items, 'id')));

        // 如果没有有效的物品ID，直接返回，避免SQL错误
        if (empty($itemIds)) {
            foreach ($items as &$item) {
                if (!isset($item['name'])) {
                    $item['name'] = '未知物品';
                }
            }
            return;
        }
        
        $placeholders = implode(',', array_fill(0, count($itemIds), '?'));
        
        $stmt = $conn->prepare("SELECT id, name FROM item_templates WHERE id IN ($placeholders)");
        $stmt->execute($itemIds);
        $itemDetails = $stmt->fetchAll(PDO::FETCH_UNIQUE | PDO::FETCH_ASSOC);
        
        foreach ($items as &$item) {
            $itemId = (int)($item['id'] ?? 0);
            if ($itemId > 0 && isset($itemDetails[$itemId])) {
                $item['name'] = $itemDetails[$itemId]['name'];
            } else {
                 if (!isset($item['name'])) {
                    $item['name'] = '未知物品';
                 }
            }
        }
    }

    /**
     * 获取玩家对某个任务的状态
     * @param int $playerId
     * @param int $questId
     * @return array|null
     */
    public function getQuestStatus(int $playerId, int $questId): ?array {
        $conn = $this->getConnection();
        $stmt = $conn->prepare("SELECT quest_id, status, objectives_progress FROM player_quests WHERE player_id = ? AND quest_id = ?");
        $stmt->execute([$playerId, $questId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($result) {
            // 确保返回结果中包含quest_id
            $result['quest_id'] = (int)$result['quest_id'];
        }
        
        return $result ?: null;
    }
} 