function displayLootTableDetails(table) {
    currentLootTableId = table.id;
    $('#lootTableName').val(table.name);
    $('#lootTableDescription').val(table.description);
    $('#loot-table-details').show();
    displayLootTableEntries(table.entries);
}

function saveLootTable() {
    const name = $('#lootTableName').val();
    const description = $('#lootTableDescription').val();
    if (!name) {
        alert('表名不能为空');
        return;
    }

    const url = currentLootTableId ? `api_loot_tables.php?id=${currentLootTableId}` : 'api_loot_tables.php';
    const method = currentLootTableId ? 'PUT' : 'POST';

    $.ajax({
        url: url,
        method: method,
        contentType: 'application/json',
        data: JSON.stringify({ name: name, description: description }),
        success: function(response) {
            loadLootTables();
            if (method === 'POST') {
                // ... existing code ...
            }
        }
    });
} 