<?php
$pageTitle = '场景管理';
$currentPage = 'scenes';
ob_start();
?>
<link rel="stylesheet" href="scenes.css">
<?php
$extra_css = ob_get_clean();

ob_start();
?>
<script src="scenes.js" defer></script>
<?php
$extra_js = ob_get_clean();
require_once 'layout_header.php';
?>

<div class="map-container">
    <div id="map-controls">
        <label>地图范围: </label>
        <input type="number" id="min-x" value="-10" style="width: 60px;">
        <input type="number" id="min-y" value="-10" style="width: 60px;">
        <span>到</span>
        <input type="number" id="max-x" value="10" style="width: 60px;">
        <input type="number" id="max-y" value="10" style="width: 60px;">
        <button id="redraw-map" class="btn btn-secondary">重绘地图</button>
        <button id="toggle-navigation" class="btn btn-sm btn-success" title="当前：键盘导航模式，点击切换到坐标输入模式">🎮 键盘导航</button>
        <span class="control-divider">|</span>
        <span class="keyboard-hint" title="使用方向键或WASD移动视野，Shift加速，Home回到原点，Esc清除选择">方向键/WASD移动，Shift加速，Home回原点，Esc清除选择</span>
        <span class="control-divider">|</span>
        <label>图层: </label>
        <div id="layer-selector"></div>
        <button id="add-layer-btn" class="btn btn-sm btn-info" title="增加新图层">+</button>
        <span class="control-divider">|</span>
        <button id="manage-continents-btn" class="btn btn-sm btn-warning" title="管理大陆和区域">🌍 大陆区域</button>
    </div>
    <div id="map-grid-container">
        <div id="map-grid"></div>
    </div>
    <div id="map-legend">
        <div class="legend-item"><span class="map-cell existing safe"></span> 安全区场景</div>
        <div class="legend-item"><span class="map-cell existing unsafe"></span> 非安全区场景</div>
        <div class="legend-item"><span class="map-cell empty"></span> 空白区域</div>
        <div class="legend-item"><span class="map-cell selected"></span> 选中区域</div>
    </div>
</div>

<!-- Scene Edit/Create Modal -->
<div id="scene-modal" class="modal">
    <div class="modal-content large">
        <span class="close-button">&times;</span>
        <h2 id="modal-title">编辑场景</h2>
        <form id="scene-form">
            <input type="hidden" id="scene-id" name="id">
            <input type="hidden" id="scene-x" name="x">
            <input type="hidden" id="scene-y" name="y">
            <input type="hidden" id="scene-z" name="z">
            <div class="form-row">
                <div class="form-group">
                    <label for="name">场景名称:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                 <div class="form-group">
                    <label for="max_players">最大玩家数:</label>
                    <input type="number" id="max_players" name="max_players" value="10" required>
                </div>
                <div class="form-group">
                    <label for="is_safe_zone">安全区:</label>
                    <select id="is_safe_zone" name="is_safe_zone">
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="form-row">
                <div class="form-group">
                    <label for="zone_id">所属区域:</label>
                    <select id="zone_id" name="zone_id" required>
                        <option value="">选择区域...</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="description">场景描述:</label>
                <textarea id="description" name="description" rows="3"></textarea>
            </div>
            <div class="form-group">
                <label>怪物配置:</label>
                <div class="monster-selection">
                    <select id="monster-select"></select>
                    <input type="number" id="monster-quantity" min="1" value="1">
                    <button type="button" id="add-monster-btn" class="btn btn-secondary">添加怪物</button>
                </div>
                <div id="monster-list" class="monster-list-container"></div>
            </div>
            
            <!-- 建筑配置部分 -->
            <div class="form-group">
                <label>建筑配置:</label>
                <div class="building-selection">
                    <select id="building-select"></select>
                    <button type="button" id="add-building-btn" class="btn btn-secondary">添加建筑</button>
                </div>
                <div id="building-list" class="building-list-container"></div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">应用修改</button>
                <button type="button" id="configure-npcs-btn" class="btn btn-info" style="display: none;">配置NPC</button>
                <button type="button" id="delete-scene-btn" class="btn btn-danger" style="display: none;">删除此场景</button>
            </div>
        </form>
    </div>
</div>

<!-- Batch Actions Panel -->
<div id="batch-actions-panel" style="display: none;">
    <span>已选中 <span id="selection-count">0</span> 个</span>
    <button id="batch-create-btn" class="btn btn-primary">创建场景</button>
    <button id="batch-edit-btn" class="btn btn-primary">修改场景</button>
    <button id="batch-delete-btn" class="btn btn-danger">删除场景</button>
    <button class="close-button" onclick="hideBatchActions()">&times;</button>
</div>

<!-- Batch Create Modal -->
<div id="batch-create-modal" class="modal">
    <div class="modal-content large">
        <span class="close-button">&times;</span>
        <h2>批量创建场景</h2>
        <form id="batch-create-form">
            <div class="form-row">
                <div class="form-group" style="flex-grow: 3;">
                    <label for="batch-create-name">场景名称前缀:</label>
                    <input type="text" id="batch-create-name" name="name" placeholder="例如：恶魔广场">
                </div>
                <div class="form-group" style="display: flex; align-items: flex-end; justify-content: center;">
                    <input type="checkbox" id="batch-create-suffix-toggle" name="use_suffix" checked>
                    <label for="batch-create-suffix-toggle" style="margin-left: 5px; margin-bottom: 0;">附加中文数字后缀</label>
                </div>
                <div class="form-group" style="display: flex; align-items: flex-end;">
                    <label for="batch-create-safe-zone" style="margin-right: 10px; margin-bottom: 0;">安全区:</label>
                    <select id="batch-create-safe-zone" name="is_safe_zone">
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <div class="form-group">
                <label for="batch-create-description">场景描述:</label>
                <textarea id="batch-create-description" name="description" rows="3" placeholder="为所有新场景设置统一的描述。"></textarea>
            </div>
            <div class="form-group">
                <label for="batch-create-zone-id">所属区域:</label>
                <select id="batch-create-zone-id" name="zone_id" required>
                    <option value="">选择区域...</option>
                </select>
            </div>
            <div class="form-group">
                <label>怪物配置:</label>
                <div class="monster-selection">
                    <select id="batch-create-monster-select"></select>
                    <input type="number" id="batch-create-monster-quantity" min="1" value="1">
                    <button type="button" id="batch-create-add-monster-btn" class="btn btn-secondary">添加怪物</button>
                </div>
                <div id="batch-create-monster-list" class="monster-list-container"></div>
            </div>
            
            <!-- 批量创建建筑配置部分 -->
            <div class="form-group">
                <label>建筑配置:</label>
                <div class="building-selection">
                    <select id="batch-create-building-select"></select>
                    <button type="button" id="batch-create-add-building-btn" class="btn btn-secondary">添加建筑</button>
                </div>
                <div id="batch-create-building-list" class="building-list-container"></div>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">确认创建</button>
            </div>
        </form>
    </div>
</div>

<!-- Batch Edit Modal -->
<div id="batch-edit-modal" class="modal">
    <div class="modal-content large">
        <span class="close-button">&times;</span>
        <h2>批量修改场景</h2>
        <form id="batch-edit-form">
            <div class="form-row">
                <div class="form-group" style="flex-grow: 3;">
                    <label for="batch-edit-name">场景名称前缀:</label>
                    <input type="text" id="batch-edit-name" name="name" placeholder="例如：恶魔广场">
                </div>
                <div class="form-group" style="display: flex; align-items: flex-end; justify-content: center;">
                    <input type="checkbox" id="batch-edit-suffix-toggle" name="use_suffix" checked>
                    <label for="batch-edit-suffix-toggle" style="margin-left: 5px; margin-bottom: 0;">附加中文数字后缀</label>
                </div>
                <div class="form-group" style="display: flex; flex-direction: column; width: 120px;">
                    <label for="batch-edit-start-number">开始数字:</label>
                    <input type="number" id="batch-edit-start-number" name="start_number" class="form-control" min="1" value="1">
                </div>
                <div class="form-group" style="display: flex; align-items: flex-end;">
                    <label for="batch-edit-safe-zone" style="margin-right: 10px; margin-bottom: 0;">安全区:</label>
                    <select id="batch-edit-safe-zone" name="is_safe_zone">
                        <option value="">不修改</option>
                        <option value="1">是</option>
                        <option value="0">否</option>
                    </select>
                </div>
            </div>
            <small>留空则不修改名称。如果勾选后缀，将按坐标顺序重命名为 "前缀（一）", "前缀（二）" 等中文序号。</small>
            <div class="form-group">
                <label for="batch-edit-description">场景描述:</label>
                <textarea id="batch-edit-description" name="description" rows="3" placeholder="输入新的描述以覆盖所有选中场景。留空则不修改。"></textarea>
            </div>
            <div class="form-group">
                <label for="batch-edit-zone-id">所属区域:</label>
                <select id="batch-edit-zone-id" name="zone_id">
                    <option value="">不修改</option>
                </select>
            </div>
            <div class="form-group">
                <label>怪物配置:</label>
                <div class="monster-selection">
                    <select id="batch-monster-select"></select>
                    <input type="number" id="batch-monster-quantity" min="1" value="1">
                    <button type="button" id="batch-add-monster-btn" class="btn btn-secondary">添加怪物</button>
                </div>
                <div id="batch-monster-list" class="monster-list-container"></div>
                <small>此处的怪物配置将<strong style="color: #c00;">覆盖</strong>所有选中场景的现有怪物。</small>
            </div>
            
            <!-- 批量建筑配置部分 -->
            <div class="form-group">
                <label>建筑配置:</label>
                <div class="building-selection">
                    <select id="batch-building-select"></select>
                    <button type="button" id="batch-add-building-btn" class="btn btn-secondary">添加建筑</button>
                </div>
                <div id="batch-building-list" class="building-list-container"></div>
                <small>此处的建筑配置将<strong style="color: #c00;">覆盖</strong>所有选中场景的现有建筑。</small>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">应用批量修改</button>
            </div>
        </form>
    </div>
</div>

<!-- Layer Name Modal -->
<div id="layer-name-modal" class="modal">
    <div class="modal-content">
        <span class="close-button">&times;</span>
        <h2 id="layer-modal-title">设置图层名称</h2>
        <form id="layer-name-form">
            <input type="hidden" id="layer-z" name="z">
            <div class="form-group">
                <label for="layer-name">图层名称:</label>
                <input type="text" id="layer-name" name="name" required maxlength="100" placeholder="请输入图层名称">
            </div>
            <div class="form-group">
                <label for="layer-description">图层描述:</label>
                <textarea id="layer-description" name="description" rows="3" placeholder="可选：输入图层描述"></textarea>
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存</button>
                <button type="button" class="btn btn-secondary" onclick="document.getElementById('layer-name-modal').style.display='none'">取消</button>
            </div>
        </form>
    </div>
</div>

<div id="toast"></div>

<!-- 全局提示框元素 -->
<div id="global-tooltip" class="global-tooltip"></div>

<!-- Shop Config Modal -->
<?php require_once 'shop_config_modal.php'; ?>

<!-- Teleporter Config Modal -->
<div id="teleporter-config-modal" class="modal">
    <div class="modal-content large">
        <span class="close-button">&times;</span>
        <h2>配置传送点</h2>
        <div class="modal-header-info">
            <p>场景: <span id="teleporter-config-scene-name"></span></p>
            <p>建筑: <span id="teleporter-config-building-name"></span></p>
        </div>
        <input type="hidden" id="teleporter-config-scene-building-id">
        <input type="hidden" id="teleporter-config-scene-id">
        <input type="hidden" id="teleporter-config-building-id">
        
        <div class="form-group">
            <label>添加目标场景:</label>
            <div class="continent-zone-filter-container" style="margin-bottom: 10px;">
                <div style="display: flex; gap: 10px; align-items: center; margin-bottom: 10px;">
                    <label>按大陆筛选:</label>
                    <select id="teleporter-continent-filter" style="min-width: 150px;">
                        <option value="">所有大陆</option>
                    </select>
                    <label style="margin-left: 15px;">按区域筛选:</label>
                    <select id="teleporter-zone-filter" style="min-width: 150px;">
                        <option value="">所有区域</option>
                    </select>
                </div>
            </div>
            <div class="destination-selection" style="display: flex; gap: 10px; margin-bottom: 15px;">
                <select id="destination-scene-select" style="flex: 1; min-width: 300px;">
                    <option value="">选择目标场景...</option>
                </select>
                <button type="button" id="add-destination-btn" class="btn btn-primary">添加目标场景</button>
            </div>
        </div>
        
        <div id="destination-list" class="destination-list-container" style="max-height: 400px; overflow-y: auto;">
            <p class="loading-text">正在加载目标场景列表...</p>
        </div>
    </div>
</div>

<!-- Item Cost Config Modal -->
<div id="item-cost-modal" class="modal">
    <div class="modal-content">
        <span class="close-button">&times;</span>
        <h2>配置传送消耗物品</h2>
        <div class="modal-header-info">
            <p>目标场景: <span id="item-cost-scene-name"></span></p>
        </div>
        <input type="hidden" id="item-cost-destination-id">

        <form id="item-cost-form">
            <div class="form-group">
                <label for="required-item-select">消耗物品:</label>
                <select id="required-item-select" name="required_item_id">
                    <option value="">无需消耗物品</option>
                </select>
            </div>
            <div class="form-group">
                <label for="required-quantity">消耗数量:</label>
                <input type="number" id="required-quantity" name="required_quantity" min="1" value="1">
            </div>
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">保存配置</button>
                <button type="button" class="btn btn-secondary" onclick="document.getElementById('item-cost-modal').style.display='none'">取消</button>
            </div>
        </form>
    </div>
</div>

<?php require_once 'layout_footer.php'; ?> 