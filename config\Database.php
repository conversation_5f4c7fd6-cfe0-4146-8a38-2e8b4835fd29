<?php
// config/Database.php
require_once 'config.php';

use Swoole\Coroutine;

class Database {
    private static $instance = null;
    // This static array will hold connections, keyed by coroutine ID.
    private static $connections = [];
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $options;

    private function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
        $this->options = [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false,
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci",
            // 添加连接超时设置
            PDO::ATTR_TIMEOUT => 10,
            // 设置持久连接为false，避免连接复用问题
            PDO::ATTR_PERSISTENT => false
        ];
    }

    public static function getInstance() {
        if (self::$instance == null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    /**
     * Gets a PDO connection.
     * This method is safe for both Swoole coroutine and traditional FPM environments.
     */
    public function getConnection() {
        // Check if we are in a Swoole Coroutine environment.
        if (class_exists('Swoole\Coroutine') && Coroutine::getCid() > 0) {
            // Coroutine-safe logic for the game server.
            $cid = Coroutine::getCid();
            if (isset(self::$connections[$cid])) {
                // 检查连接是否仍然有效
                if ($this->isConnectionValid(self::$connections[$cid])) {
                    return self::$connections[$cid];
                } else {
                    // 连接已失效，移除并重新创建
                    unset(self::$connections[$cid]);
                }
            }

            $pdo = $this->createPdoConnection();
            self::$connections[$cid] = $pdo;

            // Clean up the connection when the coroutine exits.
            Coroutine::defer(function () use ($cid) {
                unset(self::$connections[$cid]);
            });

            return $pdo;
        } else {
            // Standard singleton logic for the admin panel (non-coroutine).
            if (isset(self::$connections['default'])) {
                // 检查连接是否仍然有效
                if ($this->isConnectionValid(self::$connections['default'])) {
                    return self::$connections['default'];
                } else {
                    // 连接已失效，移除并重新创建
                    unset(self::$connections['default']);
                }
            }

            $pdo = $this->createPdoConnection();
            self::$connections['default'] = $pdo;
            return $pdo;
        }
    }
    
    /**
     * Creates a new PDO connection instance.
     * @return PDO
     */
    private function createPdoConnection() {
        try {
            $pdo = new PDO(
                "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4",
                $this->username,
                $this->password,
                $this->options
            );

            // 设置MySQL会话超时参数，防止连接被服务器断开
            // 设置为1小时，与健康检查频率匹配
            $pdo->exec("SET SESSION wait_timeout = 3600");
            $pdo->exec("SET SESSION interactive_timeout = 3600");

            // 设置自动重连
            $pdo->exec("SET SESSION sql_mode = 'STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO'");

            // 记录连接创建时间，用于连接保活
            $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

            error_log("New database connection created successfully");
            return $pdo;
        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw $e;
        }
    }

    /**
     * 检查数据库连接是否有效
     * @param PDO $connection
     * @return bool
     */
    private function isConnectionValid($connection) {
        try {
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            error_log("Connection validation failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * 重连数据库（清除所有缓存的连接）
     */
    public function reconnect() {
        // 清除所有缓存的连接
        self::$connections = [];
        error_log("Database connections cleared for reconnection");
    }

    public function query($sql, $params = []) {
        $maxRetries = 3;
        $retryCount = 0;

        while ($retryCount < $maxRetries) {
            try {
                $connection = $this->getConnection();
                $stmt = $connection->prepare($sql);
                $stmt->execute($params);
                return $stmt;
            } catch (PDOException $e) {
                $retryCount++;
                $errorMessage = $e->getMessage();

                // 检查是否是连接相关的错误
                $connectionErrors = [
                    'MySQL server has gone away',
                    'Lost connection to MySQL server',
                    'Connection timed out',
                    'Can\'t connect to MySQL server',
                    'MySQL server has gone away during query'
                ];

                $isConnectionError = false;
                foreach ($connectionErrors as $errorPattern) {
                    if (strpos($errorMessage, $errorPattern) !== false) {
                        $isConnectionError = true;
                        break;
                    }
                }

                // 如果是连接问题且还有重试机会，则重试
                if ($isConnectionError && $retryCount < $maxRetries) {
                    error_log("Database connection error detected: {$errorMessage}, attempting retry {$retryCount}/{$maxRetries}");
                    $this->reconnect();

                    // 在重试前等待一小段时间
                    if (class_exists('Swoole\Coroutine') && \Swoole\Coroutine::getCid() > 0) {
                        \Swoole\Coroutine\System::sleep(0.1 * $retryCount); // 递增延迟
                    } else {
                        usleep(100000 * $retryCount); // 100ms * retry count
                    }
                    continue;
                }

                // 其他错误或重试次数用完，抛出异常
                error_log("Database query failed after {$retryCount} retries: " . $errorMessage);
                error_log("SQL: " . $sql);
                error_log("Params: " . json_encode($params));
                throw $e;
            }
        }
    }

    public function lastInsertId() {
        return $this->getConnection()->lastInsertId();
    }

    /**
     * 执行连接保活检查
     * 这个方法可以被定时器调用来保持连接活跃
     */
    public function keepAlive() {
        try {
            $connection = $this->getConnection();
            $connection->query('SELECT 1');
            return true;
        } catch (PDOException $e) {
            error_log("Database keep-alive failed: " . $e->getMessage());
            $this->reconnect();
            return false;
        }
    }

    /**
     * 获取连接状态信息
     * @return array
     */
    public function getConnectionStatus() {
        $status = [
            'total_connections' => count(self::$connections),
            'coroutine_mode' => class_exists('Swoole\Coroutine') && \Swoole\Coroutine::getCid() > 0,
            'current_cid' => class_exists('Swoole\Coroutine') ? \Swoole\Coroutine::getCid() : 'N/A'
        ];

        try {
            $connection = $this->getConnection();
            $stmt = $connection->query('SHOW STATUS LIKE "Threads_connected"');
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $status['mysql_threads_connected'] = $result['Value'] ?? 'Unknown';

            $stmt = $connection->query('SELECT CONNECTION_ID() as connection_id');
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $status['current_connection_id'] = $result['connection_id'] ?? 'Unknown';

            $status['connection_valid'] = true;
        } catch (PDOException $e) {
            $status['connection_valid'] = false;
            $status['last_error'] = $e->getMessage();
        }

        return $status;
    }
}
