<?php
// admin/dialogue_editor.php
session_start();

// 引入数据库配置
require_once '../config/Database.php';

// 检查是否提供了对话树ID
if (!isset($_GET['id']) || empty($_GET['id'])) {
    header('Location: dialogues.php');
    exit;
}

$dialogue_id = (int)$_GET['id'];
$pageTitle = '对话编辑器';
$currentPage = 'dialogues';
$extra_css = '
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
<style>
    .content-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 20px;
    }
    .header-actions {
        display: flex;
        gap: 10px;
    }
    .dialogue-editor {
        display: flex;
        height: calc(100vh - 200px);
        min-height: 500px;
        margin-top: 20px;
    }
    .dialogue-tree {
        width: 30%;
        border-right: 1px solid #ddd;
        overflow-y: auto;
        padding-right: 15px;
    }
    .dialogue-content {
        width: 70%;
        padding-left: 15px;
        overflow-y: auto;
    }
    .node-list {
        list-style: none;
        padding: 0;
    }
    .node-item {
        padding: 10px;
        border: 1px solid #ddd;
        margin-bottom: 10px;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.2s;
    }
    .node-item:hover {
        background-color: #f9f9f9;
    }
    .node-item.active {
        border-color: #4CAF50;
        background-color: #E8F5E9;
    }
    .node-item .node-title {
        font-weight: bold;
        margin-bottom: 5px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .node-item .node-preview {
        font-size: 0.9em;
        color: #666;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    .node-actions {
        margin-bottom: 15px;
    }
    .node-editor {
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 15px;
        margin-bottom: 15px;
    }
    .node-connections {
        margin-top: 15px;
        border-top: 1px solid #eee;
        padding-top: 15px;
    }
    .connection-item {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }
    .connection-item select {
        flex: 1;
        margin-right: 10px;
    }
    .no-node-selected {
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
        color: #666;
    }
    .CodeMirror {
        height: 150px;
        border: 1px solid #ddd;
    }

    /* --- Visual Script Builder Styles --- */
    .script-builder { border: 1px solid #ccc; border-radius: 4px; padding: 10px; margin-top: 5px; background: #fafafa; }
    .script-list { display: flex; flex-direction: column; gap: 10px; }
    .script-item { display: flex; gap: 8px; align-items: center; padding: 8px; background: #fff; border: 1px solid #ddd; border-radius: 4px; }
    .script-item-params { display: flex; gap: 8px; align-items: center; flex-wrap: wrap; }
    .script-item select, .script-item input { padding: 5px; border-radius: 3px; border: 1px solid #ccc; }
    .logical-operator-group { margin-bottom: 10px; }
    .btn-add-script, .btn-remove-script {
        border: none; background: none; cursor: pointer; padding: 5px;
    }
    .btn-add-script { color: #28a745; font-size: 1.2em; }
    .btn-remove-script { color: #dc3545; }

    /* 物品选择器样式 */
    .item-selector-container {
        position: relative;
        display: inline-block;
        min-width: 200px;
    }
    .item-selector-input {
        width: 100%;
        padding: 5px 30px 5px 8px;
        border: 1px solid #ccc;
        border-radius: 3px;
        background: white;
        cursor: pointer;
    }
    .item-selector-arrow {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        pointer-events: none;
        color: #666;
    }
    .item-selector-dropdown {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: white;
        border: 1px solid #ccc;
        border-top: none;
        border-radius: 0 0 3px 3px;
        max-height: 300px;
        overflow-y: auto;
        z-index: 1000;
        display: none;
    }
    .item-selector-search {
        width: 100%;
        padding: 8px;
        border: none;
        border-bottom: 1px solid #eee;
        outline: none;
    }
    .item-selector-categories {
        padding: 5px 0;
    }
    .item-category {
        padding: 5px 10px;
        font-weight: bold;
        background: #f5f5f5;
        border-bottom: 1px solid #eee;
        color: #666;
        font-size: 12px;
    }
    .item-option {
        padding: 8px 15px;
        cursor: pointer;
        border-bottom: 1px solid #f0f0f0;
    }
    .item-option:hover {
        background: #f0f0f0;
    }
    .item-option.selected {
        background: #007bff;
        color: white;
    }
</style>';

// 初始化变量
$success_message = '';
$error_message = '';
$dialogue = null;
$nodes = [];
$selected_node = null;
$all_items = [];
$all_quests = [];

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有物品和任务，用于UI构建
    $all_items = $pdo->query("SELECT id, name, category FROM item_templates ORDER BY category, name")->fetchAll(PDO::FETCH_ASSOC);
    $all_quests = $pdo->query("SELECT id, title FROM quests ORDER BY title")->fetchAll(PDO::FETCH_ASSOC);

    // 获取所有职业，用于职业条件选择
    $all_jobs = $pdo->query("SELECT id, name FROM jobs ORDER BY id")->fetchAll(PDO::FETCH_ASSOC);

    // 获取玩家属性表的所有字段
    $columns_query = "SHOW COLUMNS FROM player_attributes";
    $columns_stmt = $pdo->prepare($columns_query);
    $columns_stmt->execute();
    $table_columns_info = $columns_stmt->fetchAll(PDO::FETCH_ASSOC);

    $allowed_types = ['int', 'decimal', 'bigint', 'float', 'double'];
    $excluded_columns = [
        'id', 'account_id', 'native_job_id',
        'experience_to_next_level', 'combat_hp_potion_id',
        'combat_mp_potion_id', 'current_scene_id', 'job'
    ];

    $player_attributes = [];
    foreach ($table_columns_info as $column) {
        $is_allowed = false;
        foreach ($allowed_types as $type) {
            if (strpos(strtolower($column['Type']), $type) !== false) {
                $is_allowed = true;
                break;
            }
        }

        if ($is_allowed && !in_array($column['Field'], $excluded_columns)) {
            $player_attributes[] = $column['Field'];
        }
    }
    
    // 属性中文名对照
    $attributeNames = [
        'level' => '等级', 'experience' => '经验值', 'hp' => '当前生命值', 'mp' => '当前法力值',
        'attack' => '攻击力', 'defense' => '防御力', 'max_hp' => '最大生命值', 'max_mp' => '最大法力值',
        'strength' => '力量', 'agility' => '敏捷', 'constitution' => '体质', 'intelligence' => '智力',
        'potential_points' => '潜力点', 'knowledge_points' => '知识点', 'gold' => '金币', 'diamonds' => '钻石',
        'karma' => '善恶值', 'rage' => '怒气', 'current_job_id' => '当前职业',
        'fire_damage' => '火系伤害', 'fire_resistance' => '火系抗性', 'ice_damage' => '冰系伤害', 'ice_resistance' => '冰系抗性',
        'wind_damage' => '风系伤害', 'wind_resistance' => '风系抗性', 'electric_damage' => '电系伤害', 'electric_resistance' => '电系抗性',
        'attack_speed' => '攻击速度', 'dodge_bonus' => '闪避加成'
    ];

    // 获取对话树信息
    $stmt = $pdo->prepare("SELECT * FROM dialogue_trees WHERE id = :id");
    $stmt->execute([':id' => $dialogue_id]);
    $dialogue = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$dialogue) {
        $error_message = "找不到指定的对话树";
    } else {
        // 处理节点添加/编辑/删除
        if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
            if ($_POST['action'] === 'add_node') {
                // 添加新节点
                $content = $_POST['content'] ?? '';
                $is_player_choice = isset($_POST['is_player_choice']) ? 1 : 0;
                
                $stmt = $pdo->prepare("
                    INSERT INTO dialogue_nodes 
                    (dialogue_tree_id, content, is_player_choice, condition_script, action_script)
                    VALUES 
                    (:dialogue_tree_id, :content, :is_player_choice, NULL, NULL)
                ");
                $stmt->execute([
                    ':dialogue_tree_id' => $dialogue_id,
                    ':content' => $content,
                    ':is_player_choice' => $is_player_choice
                ]);
                $success_message = '对话节点添加成功！';
                
            } elseif ($_POST['action'] === 'update_node' && isset($_POST['node_id'])) {
                // 更新节点
                $node_id = (int)$_POST['node_id'];
                $content = $_POST['content'] ?? '';
                $is_player_choice = isset($_POST['is_player_choice']) ? 1 : 0;
                // 从隐藏的textarea获取JSON数据
                $condition_script = $_POST['condition_script'] ?? null;
                $action_script = $_POST['action_script'] ?? null;
                $next_node_ids = isset($_POST['next_node_ids']) ? json_encode(array_values(array_filter($_POST['next_node_ids']))) : '[]';
                
                $stmt = $pdo->prepare("
                    UPDATE dialogue_nodes 
                    SET content = :content,
                        is_player_choice = :is_player_choice,
                        condition_script = :condition_script,
                        action_script = :action_script,
                        next_node_ids = :next_node_ids,
                        updated_at = NOW()
                    WHERE id = :id AND dialogue_tree_id = :dialogue_tree_id
                ");
                $stmt->execute([
                    ':content' => $content,
                    ':is_player_choice' => $is_player_choice,
                    ':condition_script' => empty($condition_script) ? null : $condition_script,
                    ':action_script' => empty($action_script) ? null : $action_script,
                    ':next_node_ids' => $next_node_ids,
                    ':id' => $node_id,
                    ':dialogue_tree_id' => $dialogue_id
                ]);
                $success_message = '对话节点更新成功！';
                
            } elseif ($_POST['action'] === 'delete_node' && isset($_POST['node_id'])) {
                // 删除节点
                $node_id = (int)$_POST['node_id'];
                
                // 先移除其他节点对此节点的引用
                $stmt = $pdo->prepare("
                    SELECT id, next_node_ids FROM dialogue_nodes 
                    WHERE dialogue_tree_id = :dialogue_tree_id
                ");
                $stmt->execute([':dialogue_tree_id' => $dialogue_id]);
                $all_nodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
                
                foreach ($all_nodes as $node) {
                    if (!empty($node['next_node_ids'])) {
                        $next_ids = json_decode($node['next_node_ids'], true);
                        if (is_array($next_ids) && in_array($node_id, $next_ids)) {
                            // 移除引用
                            $next_ids = array_diff($next_ids, [$node_id]);
                            $stmt = $pdo->prepare("
                                UPDATE dialogue_nodes 
                                SET next_node_ids = :next_node_ids 
                                WHERE id = :id
                            ");
                            $stmt->execute([
                                ':next_node_ids' => json_encode($next_ids),
                                ':id' => $node['id']
                            ]);
                        }
                    }
                }
                
                // 删除节点
                $stmt = $pdo->prepare("DELETE FROM dialogue_nodes WHERE id = :id AND dialogue_tree_id = :dialogue_tree_id");
                $stmt->execute([':id' => $node_id, ':dialogue_tree_id' => $dialogue_id]);
                $success_message = '对话节点删除成功！';
                // 重定向以避免在已删除的节点上出错
                header("Location: dialogue_editor.php?id=$dialogue_id");
                exit;
            }
        }
        
        // 获取所有对话节点
        $stmt = $pdo->prepare("
            SELECT * FROM dialogue_nodes 
            WHERE dialogue_tree_id = :dialogue_tree_id 
            ORDER BY id
        ");
        $stmt->execute([':dialogue_tree_id' => $dialogue_id]);
        $nodes = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // 获取选中的节点（如果有）
        if (isset($_GET['node_id'])) {
            $node_id = (int)$_GET['node_id'];
            foreach ($nodes as $node) {
                if ($node['id'] == $node_id) {
                    $selected_node = $node;
                    break;
                }
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}

// 引入页面头部
require_once 'layout_header.php';
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <?php if ($dialogue): ?>
        <div class="content-header">
            <h2>对话编辑器: <?php echo htmlspecialchars($dialogue['name']); ?></h2>
            <div class="header-actions">
                <button id="clear-cache-btn" class="btn btn-warning">清除缓存</button>
                <a href="dialogues.php" class="btn">返回对话树列表</a>
            </div>
        </div>
        
        <!-- 缓存清除通知 -->
        <div id="cache-notification" class="alert alert-success" style="display: none;"></div>
        
        <div class="dialogue-editor">
            <div class="dialogue-tree">
                <div class="node-actions">
                    <button id="add-node-btn" class="btn btn-primary">添加对话节点</button>
                </div>
                
                <ul class="node-list">
                    <?php if (empty($nodes)): ?>
                        <li class="empty-list">没有对话节点，请添加一个</li>
                    <?php else: ?>
                        <?php foreach ($nodes as $node): ?>
                            <li class="node-item <?php echo ($selected_node && $selected_node['id'] == $node['id']) ? 'active' : ''; ?>" data-id="<?php echo $node['id']; ?>">
                                <div class="node-title">节点 #<?php echo $node['id']; ?> <?php echo $node['is_player_choice'] ? '[玩家选择]' : ''; ?></div>
                                <div class="node-preview"><?php echo htmlspecialchars(mb_substr($node['content'], 0, 50) . (mb_strlen($node['content']) > 50 ? '...' : '')); ?></div>
                            </li>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </ul>
            </div>
            
            <div class="dialogue-content">
                <?php if ($selected_node): ?>
                    <form method="post" action="dialogue_editor.php?id=<?php echo $dialogue_id; ?>&node_id=<?php echo $selected_node['id']; ?>" id="edit-node-form">
                        <input type="hidden" name="action" value="update_node">
                        <input type="hidden" name="node_id" value="<?php echo $selected_node['id']; ?>">
                        
                        <div class="node-editor">
                            <h3>编辑对话节点 #<?php echo $selected_node['id']; ?></h3>
                            
                            <div class="form-group">
                                <label for="content">对话内容</label>
                                <textarea id="content" name="content" rows="5" class="form-control"><?php echo htmlspecialchars($selected_node['content']); ?></textarea>
                            </div>
                            
                            <div class="form-group">
                                <label>
                                    <input type="checkbox" name="is_player_choice" <?php echo $selected_node['is_player_choice'] ? 'checked' : ''; ?>>
                                    这是玩家选择项
                                </label>
                                <p class="form-help">如果勾选，此节点内容将作为玩家的对话选项显示</p>
                            </div>
                            
                            <!-- 条件脚本构建器 -->
                            <div class="form-group">
                                <label for="condition-builder">条件脚本 (决定此节点是否出现)</label>
                                <div id="condition-builder" class="script-builder">
                                    <div class="logical-operator-group">
                                        <select id="condition-logical-operator" class="form-control">
                                            <option value="AND">所有条件都必须满足 (AND)</option>
                                            <option value="OR">任意一个条件满足即可 (OR)</option>
                                        </select>
                                    </div>
                                    <div id="condition-list" class="script-list"></div>
                                    <button type="button" id="add-condition-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加条件</button>
                                </div>
                                <textarea name="condition_script" id="condition_script" style="display:none;"><?php echo htmlspecialchars($selected_node['condition_script'] ?? ''); ?></textarea>
                            </div>

                            <!-- 动作脚本构建器 -->
                            <div class="form-group">
                                <label for="action-builder">动作脚本 (选择此节点后执行)</label>
                                <div id="action-builder" class="script-builder">
                                    <div id="action-list" class="script-list"></div>
                                    <button type="button" id="add-action-btn" class="btn-add-script"><i class="fas fa-plus-circle"></i> 添加动作</button>
                                </div>
                                <textarea name="action_script" id="action_script" style="display:none;"><?php echo htmlspecialchars($selected_node['action_script'] ?? ''); ?></textarea>
                            </div>
                            
                            <div class="node-connections">
                                <h4>连接到其他节点</h4>
                                
                                <?php 
                                $next_node_ids = [];
                                if (!empty($selected_node['next_node_ids'])) {
                                    $next_node_ids = json_decode($selected_node['next_node_ids'], true);
                                }
                                if (!is_array($next_node_ids)) {
                                    $next_node_ids = [];
                                }
                                ?>
                                
                                <div id="connections-container">
                                    <!-- 连接将由JS动态添加 -->
                                </div>
                                <button type="button" id="add-connection" class="btn btn-sm">添加连接</button>
                            </div>
                            
                            <div class="form-actions">
                                <button type="submit" class="btn btn-primary">保存节点</button>
                                <button type="button" id="delete-node" class="btn btn-danger">删除节点</button>
                            </div>
                        </div>
                    </form>
                <?php else: ?>
                    <div class="no-node-selected">
                        <div class="text-center">
                            <h3>没有选择节点</h3>
                            <p>请从左侧选择一个节点进行编辑，或添加一个新节点</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
        
        <!-- 添加节点模态框 -->
        <div id="add-node-modal" class="modal" style="display: none;">
            <div class="modal-content">
                <span class="close">&times;</span>
                <h3>添加对话节点</h3>
                
                <form method="post" action="dialogue_editor.php?id=<?php echo $dialogue_id; ?>" id="add-node-form">
                    <input type="hidden" name="action" value="add_node">
                    
                    <div class="form-group">
                        <label for="add-content">对话内容</label>
                        <textarea id="add-content" name="content" rows="5" class="form-control" required></textarea>
                    </div>
                    
                    <div class="form-group">
                        <label>
                            <input type="checkbox" name="is_player_choice">
                            这是玩家选择项
                        </label>
                        <p class="form-help">如果勾选，此节点内容将作为玩家的对话选项显示</p>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">添加节点</button>
                        <button type="button" class="btn modal-cancel">取消</button>
                    </div>
                </form>
            </div>
        </div>
    <?php endif; ?>
</div>

<!-- 删除确认模态框 -->
<div id="delete-confirm-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p>确定要删除这个对话节点吗？此操作无法撤销。</p>
        
        <form method="post" action="dialogue_editor.php?id=<?php echo $dialogue_id; ?>" id="delete-node-form">
            <input type="hidden" name="action" value="delete_node">
            <input type="hidden" name="node_id" id="delete-node-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/codemirror.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/codemirror/5.65.2/mode/php/php.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // PHP数据注入到JS
    const ALL_ITEMS = <?php echo json_encode($all_items); ?>;
    const ALL_QUESTS = <?php echo json_encode($all_quests); ?>;
    const NODES = <?php echo json_encode($nodes); ?>;
    const SELECTED_NODE_ID = <?php echo $selected_node ? $selected_node['id'] : 'null'; ?>;
    const NEXT_NODE_IDS = <?php echo json_encode($next_node_ids ?? []); ?>;
    const PLAYER_ATTRIBUTES = <?php echo json_encode($player_attributes ?? []); ?>;
    const ATTRIBUTE_NAMES = <?php echo json_encode($attributeNames ?? []); ?>;
    const ALL_JOBS = <?php echo json_encode($all_jobs ?? []); ?>;

    // 模态框和通用元素
    var addModal = document.getElementById('add-node-modal');
    var deleteModal = document.getElementById('delete-confirm-modal');
    var cacheNotification = document.getElementById('cache-notification');
    
    // 清除缓存按钮
    document.getElementById('clear-cache-btn').addEventListener('click', function() {
        // 禁用按钮，防止重复点击
        this.disabled = true;
        this.textContent = '正在清除...';
        
        // 发送AJAX请求清除缓存
        fetch('clear_cache.php')
            .then(response => response.json())
            .then(data => {
                // 显示通知
                cacheNotification.textContent = data.message;
                cacheNotification.style.display = 'block';
                cacheNotification.className = data.success ? 'alert alert-success' : 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
                
                // 3秒后隐藏通知
                setTimeout(function() {
                    cacheNotification.style.display = 'none';
                }, 3000);
            })
            .catch(error => {
                console.error('清除缓存出错:', error);
                cacheNotification.textContent = '清除缓存请求失败，请检查网络连接';
                cacheNotification.style.display = 'block';
                cacheNotification.className = 'alert alert-danger';
                
                // 恢复按钮状态
                this.disabled = false;
                this.textContent = '清除缓存';
            });
    });
    
    // 初始化代码编辑器
    if (document.getElementById('condition-script')) {
        var conditionEditor = CodeMirror.fromTextArea(document.getElementById('condition-script'), {
            mode: 'application/x-httpd-php',
            theme: 'material-darker',
            lineNumbers: true,
            indentUnit: 4
        });
    }
    
    if (document.getElementById('action-script')) {
        var actionEditor = CodeMirror.fromTextArea(document.getElementById('action-script'), {
            mode: 'application/x-httpd-php',
            theme: 'material-darker',
            lineNumbers: true,
            indentUnit: 4
        });
    }
    
    // 节点列表点击事件
    document.querySelectorAll('.node-item').forEach(function(item) {
        item.addEventListener('click', function() {
            var nodeId = this.dataset.id;
            window.location.href = 'dialogue_editor.php?id=<?php echo $dialogue_id; ?>&node_id=' + nodeId;
        });
    });
    
    // 添加节点按钮
    document.getElementById('add-node-btn').addEventListener('click', function() {
        addModal.style.display = 'block';
    });
    
    // 删除节点按钮
    if (document.getElementById('delete-node')) {
        document.getElementById('delete-node').addEventListener('click', function(e) {
            e.preventDefault();
            var nodeId = document.querySelector('input[name="node_id"]').value;
            document.getElementById('delete-node-id').value = nodeId;
            deleteModal.style.display = 'block';
        });
    }
    
    // 添加连接按钮
    if (document.getElementById('add-connection')) {
        document.getElementById('add-connection').addEventListener('click', function() {
            var container = document.getElementById('connections-container');
            var template = `
                <div class="connection-item">
                    <select name="next_node_ids[]" class="form-control">
                        <option value="">-- 选择下一个节点 --</option>
                        <?php foreach ($nodes as $node): ?>
                            <?php if ($selected_node && $node['id'] != $selected_node['id']): ?>
                                <option value="<?php echo $node['id']; ?>">
                                    #<?php echo $node['id']; ?> - <?php echo htmlspecialchars(mb_substr($node['content'], 0, 30)); ?>
                                </option>
                            <?php endif; ?>
                        <?php endforeach; ?>
                    </select>
                    <button type="button" class="btn btn-sm remove-connection">删除</button>
                </div>
            `;
            container.insertAdjacentHTML('beforeend', template);
            
            // 绑定新添加的删除按钮事件
            container.lastElementChild.querySelector('.remove-connection').addEventListener('click', removeConnection);
        });
    }
    
    // 删除连接按钮
    document.querySelectorAll('.remove-connection').forEach(function(btn) {
        btn.addEventListener('click', removeConnection);
    });
    
    function removeConnection() {
        var item = this.closest('.connection-item');
        item.parentNode.removeChild(item);
    }
    
    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            addModal.style.display = 'none';
            deleteModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == addModal) {
            addModal.style.display = 'none';
        }
        if (event.target == deleteModal) {
            deleteModal.style.display = 'none';
        }
    });

    // --- Visual Script Builder (Conditions) ---
    const conditionBuilder = {
        container: document.getElementById('condition-list'),
        hiddenInput: document.getElementById('condition_script'),
        logicalOpSelect: document.getElementById('condition-logical-operator'),
        
        init: function() {
            if (!this.container) return;
            this.logicalOpSelect.addEventListener('change', () => this.serialize());
            document.getElementById('add-condition-btn').addEventListener('click', () => this.add());
            this.deserialize();
        },
        
        add: function(data = {}) {
            const item = document.createElement('div');
            item.className = 'script-item';
            item.innerHTML = `
                <select class="condition-type">
                    <option value="">-- 选择条件类型 --</option>
                    <option value="player_attribute">玩家属性</option>
                    <option value="has_item">拥有物品</option>
                    <option value="does_not_have_item">不拥有物品</option>
                    <option value="quest_status">任务状态</option>
                </select>
                <div class="script-item-params"></div>
                <button type="button" class="btn-remove-script"><i class="fas fa-trash"></i></button>
            `;
            this.container.appendChild(item);
            
            const typeSelect = item.querySelector('.condition-type');
            typeSelect.addEventListener('change', () => this.updateParams(item));
            item.querySelector('.btn-remove-script').addEventListener('click', () => {
                item.remove();
                this.serialize();
            });

            if (data.type) {
                typeSelect.value = data.type;
                this.updateParams(item, data);
            }
        },

        updateParams: function(item, data = {}) {
            const container = item.querySelector('.script-item-params');
            const type = item.querySelector('.condition-type').value;
            container.innerHTML = ''; // Clear previous params

            switch (type) {
                case 'player_attribute':
                    const attrOptions = PLAYER_ATTRIBUTES.map(attr => {
                        const displayName = ATTRIBUTE_NAMES[attr] ? `${ATTRIBUTE_NAMES[attr]} (${attr})` : attr;
                        return `<option value="${attr}" ${data.attribute === attr ? 'selected' : ''}>${displayName}</option>`;
                    }).join('');

                    container.innerHTML = `
                        <select name="attribute" class="attr-select">
                            <option value="">-- 选择属性 --</option>
                            ${attrOptions}
                        </select>
                        <select name="operator">
                            ${['==', '!=', '>', '>=', '<', '<='].map(op => `<option value="${op}" ${data.operator === op ? 'selected' : ''}>${op}</option>`).join('')}
                        </select>
                        <span class="value-input-container">
                            <input type="number" name="value" placeholder="值" value="${data.value || ''}">
                        </span>
                    `;

                    // 为属性选择添加事件监听器
                    const attrSelect = container.querySelector('.attr-select');
                    const valueContainer = container.querySelector('.value-input-container');
                    const self = this;

                    function updateValueInput() {
                        const selectedAttr = attrSelect.value;
                        if (selectedAttr === 'current_job_id') {
                            // 显示职业下拉选择
                            const jobOptions = ALL_JOBS.map(job =>
                                `<option value="${job.id}" ${data.value == job.id ? 'selected' : ''}>${job.name}</option>`
                            ).join('');
                            valueContainer.innerHTML = `
                                <select name="value">
                                    <option value="">-- 选择职业 --</option>
                                    ${jobOptions}
                                </select>
                            `;
                        } else {
                            // 显示数字输入框
                            valueContainer.innerHTML = `<input type="number" name="value" placeholder="值" value="${data.value || ''}">`;
                        }

                        // 为新创建的元素绑定change事件监听器
                        valueContainer.querySelectorAll('input, select').forEach(el => {
                            el.addEventListener('change', () => self.serialize());
                        });
                    }

                    attrSelect.addEventListener('change', updateValueInput);
                    // 初始化时也要调用一次
                    updateValueInput();
                    break;
                case 'has_item':
                case 'does_not_have_item':
                    const qty_display = (type === 'has_item') ? '' : 'style="display:none"';
                    container.innerHTML = `
                        <div class="item-selector-container">
                            <input type="hidden" name="item_id" value="${data.item_id || ''}">
                            <input type="text" class="item-selector-input" placeholder="-- 选择物品 --" readonly>
                            <span class="item-selector-arrow">▼</span>
                            <div class="item-selector-dropdown">
                                <input type="text" class="item-selector-search" placeholder="搜索物品...">
                                <div class="item-selector-categories"></div>
                            </div>
                        </div>
                        <input type="number" name="quantity" placeholder="数量" min="1" value="${data.quantity || 1}" ${qty_display}>
                    `;
                    this.initItemSelector(container.querySelector('.item-selector-container'), data.item_id);
                    break;
                case 'quest_status':
                     container.innerHTML = `
                        <select name="quest_id">
                           <option value="">-- 选择任务 --</option>
                           ${ALL_QUESTS.map(q => `<option value="${q.id}" ${data.quest_id == q.id ? 'selected' : ''}>${q.title}</option>`).join('')}
                        </select>
                        <select name="status">
                            <option value="not_started" ${data.status === 'not_started' ? 'selected' : ''}>未开始</option>
                            <option value="active" ${data.status === 'active' ? 'selected' : ''}>进行中</option>
                            <option value="completed" ${data.status === 'completed' ? 'selected' : ''}>已完成</option>
                        </select>
                    `;
                    break;
            }
            container.querySelectorAll('input, select').forEach(el => el.addEventListener('change', () => this.serialize()));
            this.serialize();
        },

        serialize: function() {
            const conditions = [];
            this.container.querySelectorAll('.script-item').forEach(item => {
                const type = item.querySelector('.condition-type').value;
                if (!type) return;
                
                const condition = { type };
                item.querySelector('.script-item-params').querySelectorAll('input, select').forEach(el => {
                    condition[el.name] = el.value;
                });
                conditions.push(condition);
            });
            
            const data = {
                logical_operator: this.logicalOpSelect.value,
                conditions: conditions
            };
            this.hiddenInput.value = conditions.length > 0 ? JSON.stringify(data, null, 2) : '';
        },

        deserialize: function() {
            try {
                const data = JSON.parse(this.hiddenInput.value);
                if (data && data.conditions) {
                    this.logicalOpSelect.value = data.logical_operator || 'AND';
                    data.conditions.forEach(cond => this.add(cond));
                }
            } catch (e) { /* Do nothing on parsing error */ }
        },

        // 初始化物品选择器
        initItemSelector: function(container, selectedItemId) {
            const self = this;
            const hiddenInput = container.querySelector('input[name="item_id"]');
            const displayInput = container.querySelector('.item-selector-input');
            const dropdown = container.querySelector('.item-selector-dropdown');
            const searchInput = container.querySelector('.item-selector-search');
            const categoriesContainer = container.querySelector('.item-selector-categories');

            // 按分类组织物品
            const itemsByCategory = {};
            ALL_ITEMS.forEach(item => {
                if (!itemsByCategory[item.category]) {
                    itemsByCategory[item.category] = [];
                }
                itemsByCategory[item.category].push(item);
            });

            // 渲染分类和物品
            function renderItems(searchTerm = '') {
                categoriesContainer.innerHTML = '';

                Object.keys(itemsByCategory).sort().forEach(category => {
                    const items = itemsByCategory[category].filter(item =>
                        !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );

                    if (items.length === 0) return;

                    // 分类标题
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'item-category';
                    categoryDiv.textContent = category;
                    categoriesContainer.appendChild(categoryDiv);

                    // 物品选项
                    items.forEach(item => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'item-option';
                        itemDiv.textContent = item.name;
                        itemDiv.dataset.itemId = item.id;
                        itemDiv.dataset.itemName = item.name;

                        if (item.id == selectedItemId) {
                            itemDiv.classList.add('selected');
                            displayInput.value = item.name;
                        }

                        itemDiv.addEventListener('click', () => {
                            hiddenInput.value = item.id;
                            displayInput.value = item.name;
                            dropdown.style.display = 'none';

                            // 清除其他选中状态
                            categoriesContainer.querySelectorAll('.item-option').forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            itemDiv.classList.add('selected');

                            // 触发序列化
                            self.serialize();
                        });

                        categoriesContainer.appendChild(itemDiv);
                    });
                });
            }

            // 初始渲染
            renderItems();

            // 点击输入框显示下拉菜单
            displayInput.addEventListener('click', () => {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                if (dropdown.style.display === 'block') {
                    searchInput.focus();
                }
            });

            // 搜索功能
            searchInput.addEventListener('input', (e) => {
                renderItems(e.target.value);
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', (e) => {
                if (!container.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // 阻止下拉菜单内的点击事件冒泡
            dropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    };

    if (SELECTED_NODE_ID) {
        conditionBuilder.init();
    }
    
    const actionBuilder = {
        container: document.getElementById('action-list'),
        hiddenInput: document.getElementById('action_script'),

        init: function() {
            if (!this.container) return;
            document.getElementById('add-action-btn').addEventListener('click', () => this.add());
            this.deserialize();
        },
        add: function(data = {}) {
            const item = document.createElement('div');
            item.className = 'script-item';
            item.innerHTML = `
                <select class="action-type">
                    <option value="">-- 选择动作类型 --</option>
                    <option value="give_item">给予物品</option>
                    <option value="remove_item">移除物品</option>
                    <option value="give_gold">给予金币</option>
                    <option value="remove_gold">移除金币</option>
                    <option value="start_quest">开始任务</option>
                    <option value="update_quest_progress">更新任务进度</option>
                </select>
                <div class="script-item-params"></div>
                <button type="button" class="btn-remove-script"><i class="fas fa-trash"></i></button>
            `;
            this.container.appendChild(item);
            
            const typeSelect = item.querySelector('.action-type');
            typeSelect.addEventListener('change', () => this.updateParams(item));
             item.querySelector('.btn-remove-script').addEventListener('click', () => {
                item.remove();
                this.serialize();
            });

            if (data.type) {
                typeSelect.value = data.type;
                this.updateParams(item, data);
            }
        },
        updateParams: function(item, data={}) {
            const container = item.querySelector('.script-item-params');
            const type = item.querySelector('.action-type').value;
            container.innerHTML = '';

            switch (type) {
                case 'give_item':
                case 'remove_item':
                     container.innerHTML = `
                        <div class="item-selector-container">
                            <input type="hidden" name="item_id" value="${data.item_id || ''}">
                            <input type="text" class="item-selector-input" placeholder="-- 选择物品 --" readonly>
                            <span class="item-selector-arrow">▼</span>
                            <div class="item-selector-dropdown">
                                <input type="text" class="item-selector-search" placeholder="搜索物品...">
                                <div class="item-selector-categories"></div>
                            </div>
                        </div>
                        <input type="number" name="quantity" placeholder="数量" min="1" value="${data.quantity || 1}">
                    `;
                    this.initItemSelector(container.querySelector('.item-selector-container'), data.item_id);
                    break;
                case 'give_gold':
                case 'remove_gold':
                    container.innerHTML = `<input type="number" name="amount" placeholder="数量" min="1" value="${data.amount || 1}">`;
                    break;
                case 'start_quest':
                    container.innerHTML = `
                        <select name="quest_id">
                           <option value="">-- 选择任务 --</option>
                           ${ALL_QUESTS.map(q => `<option value="${q.id}" ${data.quest_id == q.id ? 'selected' : ''}>${q.title}</option>`).join('')}
                        </select>
                    `;
                    break;
                case 'update_quest_progress':
                    let questOptions = ALL_QUESTS.map(q => 
                        `<option value="${q.id}" ${data.quest_id == q.id ? 'selected' : ''}>${q.title}</option>`
                    ).join('');
                    
                    // 使用一个较大的数字，让管理员选择任务目标ID
                    container.innerHTML = `
                        <select name="quest_id" class="quest-select">
                           <option value="">-- 选择任务 --</option>
                           ${questOptions}
                        </select>
                        <input type="number" name="objective_id" placeholder="任务目标ID" min="1" value="${data.objective_id || ''}">
                        <input type="number" name="increment" placeholder="增加值" value="${data.increment || 1}">
                        <small>提示：任务目标ID可以在任务编辑页面查看</small>
                    `;
                    
                    break;
            }
            container.querySelectorAll('input, select').forEach(el => el.addEventListener('change', () => this.serialize()));
            this.serialize();
        },
        serialize: function() {
            const actions = [];
            this.container.querySelectorAll('.script-item').forEach(item => {
                const type = item.querySelector('.action-type').value;
                if (!type) return;
                
                const action = { type };
                item.querySelector('.script-item-params').querySelectorAll('input, select').forEach(el => {
                    action[el.name] = el.value;
                });
                actions.push(action);
            });
            this.hiddenInput.value = actions.length > 0 ? JSON.stringify({ actions }, null, 2) : '';
        },
        deserialize: function() {
             try {
                const data = JSON.parse(this.hiddenInput.value);
                if (data && data.actions) {
                    data.actions.forEach(act => this.add(act));
                }
            } catch (e) { /* Do nothing */ }
        },

        // 初始化物品选择器
        initItemSelector: function(container, selectedItemId) {
            const self = this;
            const hiddenInput = container.querySelector('input[name="item_id"]');
            const displayInput = container.querySelector('.item-selector-input');
            const dropdown = container.querySelector('.item-selector-dropdown');
            const searchInput = container.querySelector('.item-selector-search');
            const categoriesContainer = container.querySelector('.item-selector-categories');

            // 按分类组织物品
            const itemsByCategory = {};
            ALL_ITEMS.forEach(item => {
                if (!itemsByCategory[item.category]) {
                    itemsByCategory[item.category] = [];
                }
                itemsByCategory[item.category].push(item);
            });

            // 渲染分类和物品
            function renderItems(searchTerm = '') {
                categoriesContainer.innerHTML = '';

                Object.keys(itemsByCategory).sort().forEach(category => {
                    const items = itemsByCategory[category].filter(item =>
                        !searchTerm || item.name.toLowerCase().includes(searchTerm.toLowerCase())
                    );

                    if (items.length === 0) return;

                    // 分类标题
                    const categoryDiv = document.createElement('div');
                    categoryDiv.className = 'item-category';
                    categoryDiv.textContent = category;
                    categoriesContainer.appendChild(categoryDiv);

                    // 物品选项
                    items.forEach(item => {
                        const itemDiv = document.createElement('div');
                        itemDiv.className = 'item-option';
                        itemDiv.textContent = item.name;
                        itemDiv.dataset.itemId = item.id;
                        itemDiv.dataset.itemName = item.name;

                        if (item.id == selectedItemId) {
                            itemDiv.classList.add('selected');
                            displayInput.value = item.name;
                        }

                        itemDiv.addEventListener('click', () => {
                            hiddenInput.value = item.id;
                            displayInput.value = item.name;
                            dropdown.style.display = 'none';

                            // 清除其他选中状态
                            categoriesContainer.querySelectorAll('.item-option').forEach(opt => {
                                opt.classList.remove('selected');
                            });
                            itemDiv.classList.add('selected');

                            // 触发序列化
                            self.serialize();
                        });

                        categoriesContainer.appendChild(itemDiv);
                    });
                });
            }

            // 初始渲染
            renderItems();

            // 点击输入框显示下拉菜单
            displayInput.addEventListener('click', () => {
                dropdown.style.display = dropdown.style.display === 'block' ? 'none' : 'block';
                if (dropdown.style.display === 'block') {
                    searchInput.focus();
                }
            });

            // 搜索功能
            searchInput.addEventListener('input', (e) => {
                renderItems(e.target.value);
            });

            // 点击外部关闭下拉菜单
            document.addEventListener('click', (e) => {
                if (!container.contains(e.target)) {
                    dropdown.style.display = 'none';
                }
            });

            // 阻止下拉菜单内的点击事件冒泡
            dropdown.addEventListener('click', (e) => {
                e.stopPropagation();
            });
        }
    };

    if (SELECTED_NODE_ID) {
        actionBuilder.init();
    }
    
    // --- Node Connections ---
    const connectionsContainer = document.getElementById('connections-container');
    
    function addConnection(selectedId = '') {
        const item = document.createElement('div');
        item.className = 'connection-item';
        const selectOptions = NODES.filter(n => n.id != SELECTED_NODE_ID)
                                 .map(n => `<option value="${n.id}" ${n.id == selectedId ? 'selected' : ''}>#${n.id} - ${String(n.content).substring(0, 30)}</option>`)
                                 .join('');
        item.innerHTML = `
            <select name="next_node_ids[]" class="form-control">
                <option value="">-- 选择下一个节点 --</option>
                ${selectOptions}
            </select>
            <button type="button" class="btn btn-sm btn-danger remove-connection"><i class="fas fa-trash"></i></button>
        `;
        connectionsContainer.appendChild(item);
        item.querySelector('.remove-connection').addEventListener('click', () => item.remove());
    }
    
    if (connectionsContainer) {
        document.getElementById('add-connection').addEventListener('click', () => addConnection());
        // Initialize existing connections
        if (NEXT_NODE_IDS && NEXT_NODE_IDS.length > 0) {
            NEXT_NODE_IDS.forEach(id => addConnection(id));
        } else {
            addConnection(); // Add one empty connection by default
        }
    }

    // --- General Event Listeners ---
    
    // Node list click
    document.querySelectorAll('.node-item').forEach(item => {
        item.addEventListener('click', function() {
            window.location.href = `dialogue_editor.php?id=<?php echo $dialogue_id; ?>&node_id=${this.dataset.id}`;
        });
    });
    
    // Add/delete node modals
    document.getElementById('add-node-btn').addEventListener('click', () => { addModal.style.display = 'block'; });
    if (document.getElementById('delete-node')) {
        document.getElementById('delete-node').addEventListener('click', e => {
            e.preventDefault();
            document.getElementById('delete-node-id').value = SELECTED_NODE_ID;
            deleteModal.style.display = 'block';
        });
    }

    // Close modals
    document.querySelectorAll('.close, .modal-cancel').forEach(el => {
        el.addEventListener('click', () => {
            addModal.style.display = 'none';
            deleteModal.style.display = 'none';
        });
    });
});
</script>

<?php require_once 'layout_footer.php'; ?> 