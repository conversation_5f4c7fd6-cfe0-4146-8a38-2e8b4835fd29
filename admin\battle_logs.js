/**
 * 战斗日志管理页面脚本
 */
$(document).ready(function() {
    let currentPage = 1;
    let currentFilters = {};

    const BATTLE_RESULT_MAP = {
        'victory': '胜利',
        'defeated': '失败',
        'flee': '逃跑',
        'all_fled': '全部逃跑',
        'last_player_left': '最后玩家离开',
        'disconnect': '连接断开'
    };
    
    // Function to fetch and display logs
    function fetchLogs(page = 1, filters = {}) {
        currentPage = page;
        currentFilters = filters;
        
        const params = new URLSearchParams({
            action: 'get_logs',
            page: currentPage,
            page_size: 20,
            ...currentFilters
        });

        $.ajax({
            url: `api_battle_logs.php?${params.toString()}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderTable(response.data.logs);
                    renderPagination(response.data.pagination);
                } else {
                    showToast(response.message || '加载日志失败', 'error');
                }
            },
            error: function(xhr, status, error) {
                showToast(`请求错误: ${error}`, 'error');
            }
        });
    }

    // Function to render the table body
    function renderTable(logs) {
        const $tbody = $('#logs-table-body');
        $tbody.empty();
        if (logs.length === 0) {
            $tbody.html('<tr><td colspan="8" class="text-center">没有找到符合条件的战斗日志。</td></tr>');
            return;
        }

        logs.forEach(log => {
            const participants = log.participants ? JSON.parse(log.participants) : [];
            const playerNames = participants.map(p => p.name).join(', ');
            const duration = log.start_time && log.end_time ? calculateDuration(log.start_time, log.end_time) : 'N/A';
            
            const row = `
                <tr>
                    <td>${log.id}</td>
                    <td>${log.start_time}</td>
                    <td>${log.scene_name || '未知场景'}</td>
                    <td>${log.monster_name || '未知怪物'}</td>
                    <td>${playerNames}</td>
                    <td><span class="badge result-${log.battle_result}">${BATTLE_RESULT_MAP[log.battle_result] || log.battle_result}</span></td>
                    <td>${duration}</td>
                    <td>
                        <button class="btn btn-sm btn-info view-details-btn" data-id="${log.id}">
                            <i class="fas fa-eye"></i> 详情
                        </button>
                    </td>
                </tr>
            `;
            $tbody.append(row);
        });
    }

    // Function to render pagination controls
    function renderPagination(pagination) {
        const { current_page, total_pages } = pagination;
        const $paginationControls = $('.pagination-controls');
        $paginationControls.empty();

        if (total_pages <= 1) return;

        let paginationHtml = '';

        // First and Previous buttons
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="1" ${current_page === 1 ? 'disabled' : ''}>首页</button>`;
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${current_page - 1}" ${current_page === 1 ? 'disabled' : ''}>上一页</button>`;

        // Page number buttons
        const pagesToShow = 5;
        let startPage = Math.max(1, current_page - Math.floor(pagesToShow / 2));
        let endPage = Math.min(total_pages, startPage + pagesToShow - 1);
        
        if (endPage - startPage + 1 < pagesToShow) {
            startPage = Math.max(1, endPage - pagesToShow + 1);
        }

        if (startPage > 1) {
            paginationHtml += `<span class="page-ellipsis">...</span>`;
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `<button class="btn btn-sm page-btn ${i === current_page ? 'active' : ''}" data-page="${i}">${i}</button>`;
        }

        if (endPage < total_pages) {
            paginationHtml += `<span class="page-ellipsis">...</span>`;
        }

        // Next and Last buttons
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${current_page + 1}" ${current_page === total_pages ? 'disabled' : ''}>下一页</button>`;
        paginationHtml += `<button class="btn btn-sm page-btn" data-page="${total_pages}" ${current_page === total_pages ? 'disabled' : ''}>末页</button>`;

        $paginationControls.html(paginationHtml);
    }
    
    // Function to fetch and display log details
    function fetchLogDetail(logId) {
        $.ajax({
            url: `api_battle_logs.php?action=get_log_detail&id=${logId}`,
            method: 'GET',
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    renderLogDetailModal(response.data);
                } else {
                    showToast(response.message || '加载详情失败', 'error');
                }
            },
            error: function() {
                showToast('请求详情失败', 'error');
            }
        });
    }

    // Function to render the detail modal
    function renderLogDetailModal(log) {
        $('#modal-battle-id').text(log.id);
        $('#modal-scene-name').text(log.scene_name || 'N/A');
        $('#modal-battle-result').html(`<span class="badge result-${log.battle_result}">${BATTLE_RESULT_MAP[log.battle_result] || log.battle_result}</span>`);
        $('#modal-start-time').text(log.start_time || 'N/A');
        $('#modal-end-time').text(log.end_time || 'N/A');
        $('#modal-duration').text(log.start_time && log.end_time ? calculateDuration(log.start_time, log.end_time) : 'N/A');
        $('#modal-monster-name').text(`${log.monster_name || '未知'} (Lv. ${log.monster_level || '?'})`);
        
        // ** Display Monster Instance ID **
        $('#modal-monster-instance-id').text(log.monster_instance_id || 'N/A');

        const participants = log.participants ? JSON.parse(log.participants) : [];
        const participantsHtml = participants.map(p => `<span class="participant-tag">${p.name}</span>`).join(' ');
        $('#modal-participants').html(participantsHtml || '无参与者信息');
        
        const logEntries = log.log_data ? JSON.parse(log.log_data) : [];
        const logHtml = logEntries.map(entry => {
            let entryClass = '';
            if (entry.includes('【暴击！】')) entryClass = 'log-critical';
            else if (entry.includes('【闪避！】')) entryClass = 'log-dodge';
            else if (entry.includes('【战利品】') || entry.includes('【经验奖励】')) entryClass = 'log-reward';
            else if (entry.includes('被击败了') || entry.includes('已被击败')) entryClass = 'log-defeat';
            return `<div class="log-entry ${entryClass}">${entry}</div>`;
        }).join('');
        $('#modal-battle-log').html(logHtml);
        
        $('#export-log-btn').data('id', log.id);

        $('#logDetailModal').fadeIn();
    }
    
    // Event handlers
    $('#search-btn').on('click', function() {
        const filters = {
            battle_result: $('#battle-result-filter').val(),
            start_date: $('#date-range-start').val(),
            end_date: $('#date-range-end').val(),
            scene_id: $('#scene-filter').val(),
            player_name: $('#player-search').val(),
            monster_name: $('#monster-search').val()
        };
        fetchLogs(1, filters);
    });

    $('#reset-btn').on('click', function() {
        $('#battle-result-filter, #scene-filter, #player-search, #monster-search, #date-range-start, #date-range-end').val('');
        $('#cleanup-date').val('');
        fetchLogs(1, {});
    });

    $('.pagination-controls').on('click', '.page-btn', function() {
        if ($(this).is(':disabled')) return;
        const page = $(this).data('page');
        fetchLogs(page, currentFilters);
    });

    $(document).on('click', '.view-details-btn', function() {
        const logId = $(this).data('id');
        fetchLogDetail(logId);
    });

    // Modal close
    $('.modal-close-button, .modal-close').on('click', function() {
        $('#logDetailModal').fadeOut();
    });
    $(window).on('click', function(event) {
        if ($(event.target).is('#logDetailModal')) {
            $('#logDetailModal').fadeOut();
        }
    });
    
    // Export button
    $('#export-log-btn').on('click', function() {
        const logId = $(this).data('id');
        window.location.href = `api_battle_logs.php?action=export_log&id=${logId}`;
    });

    // Cleanup button handler
    $('#cleanup-btn').on('click', function() {
        const cleanupDate = $('#cleanup-date').val();
        if (!cleanupDate) {
            showToast('请先选择一个清理截止日期。', 'error');
            return;
        }

        const confirmation = confirm(`您确定要删除 ${cleanupDate} 之前的所有战斗日志吗？此操作不可撤销！`);
        if (confirmation) {
            $.ajax({
                url: 'api_battle_logs.php',
                method: 'POST',
                data: {
                    action: 'clear_old_logs',
                    cleanup_date: cleanupDate
                },
                dataType: 'json',
                success: function(response) {
                    if (response.success) {
                        showToast(response.message, 'success');
                        fetchLogs(1, currentFilters); // Refresh logs
                    } else {
                        showToast(response.message || '清理失败', 'error');
                    }
                },
                error: function() {
                    showToast('请求失败，无法清理日志。', 'error');
                }
            });
        }
    });

    // Fetch initial data
    fetchLogs();
    
    // Fetch scenes for filter dropdown
    $.getJSON('api_battle_logs.php?action=get_scenes', function(response) {
        if (response.success) {
            const $sceneFilter = $('#scene-filter');
            response.data.forEach(scene => {
                $sceneFilter.append(`<option value="${scene.id}">${scene.name}</option>`);
            });
        }
    });

    // Helper functions
    function calculateDuration(start, end) {
        const startDate = new Date(start);
        const endDate = new Date(end);
        const diffSeconds = Math.round((endDate - startDate) / 1000);
        if (isNaN(diffSeconds) || diffSeconds < 0) return 'N/A';
        const minutes = Math.floor(diffSeconds / 60);
        const seconds = diffSeconds % 60;
        return `${minutes}分 ${seconds}秒`;
    }

    function showToast(message, type = 'success') {
        const $toast = $('#toast');
        $toast.text(message).addClass(type).fadeIn();
        setTimeout(() => {
            $toast.fadeOut(() => $toast.removeClass(type));
        }, 3000);
    }
}); 