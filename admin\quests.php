<?php
$pageTitle = '任务管理';
$currentPage = 'quests';

// 引入额外的CSS
ob_start();
?>
<style>
    .quest-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        gap: 20px;
        margin-top: 20px;
    }
    .quest-card {
        border: 1px solid #ddd;
        border-radius: 5px;
        padding: 15px;
        background: #fff;
        box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        transition: transform 0.2s, box-shadow 0.2s;
    }
    .quest-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    }
    .quest-card h3 {
        margin-top: 0;
        border-bottom: 1px solid #eee;
        padding-bottom: 10px;
        color: #333;
    }
    .quest-card .quest-type {
        display: inline-block;
        padding: 3px 8px;
        border-radius: 3px;
        font-size: 0.8em;
        margin-right: 5px;
        background-color: #e9ecef;
    }
    .quest-card .quest-type.dialogue { background-color: #c8e6c9; }
    .quest-card .quest-type.collection { background-color: #ffecb3; }
    .quest-card .quest-type.kill { background-color: #ffcdd2; }
    <!-- .quest-card .quest-type.escort { background-color: #bbdefb; } -->
    <!-- .quest-card .quest-type.exploration { background-color: #d1c4e9; } -->
    
    .quest-card .quest-level {
        font-weight: bold;
        color: #555;
    }
    .quest-card .quest-description {
        margin: 10px 0;
        color: #666;
        max-height: 80px;
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 3;
        -webkit-box-orient: vertical;
    }
    .quest-card .quest-npcs {
        font-size: 0.9em;
        color: #666;
    }
    .quest-card .quest-rewards {
        margin-top: 10px;
        font-size: 0.9em;
    }
    .quest-card .quest-rewards .reward-item {
        display: inline-block;
        margin-right: 10px;
    }
    .quest-card .quest-actions {
        margin-top: 15px;
        display: flex;
        justify-content: flex-end;
        gap: 10px;
    }
    .filters {
        display: flex;
        flex-wrap: wrap;
        gap: 15px;
        margin-bottom: 20px;
        padding: 15px;
        background: #f9f9f9;
        border-radius: 5px;
        align-items: center;
    }
    .filters select, .filters input {
        padding: 8px;
        border-radius: 4px;
        border: 1px solid #ddd;
    }
    .filters .filter-group {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .quest-count {
        margin-left: auto;
        font-weight: bold;
        color: #555;
    }
    
    /* 分组样式 */
    .header-actions {
        display: flex;
        gap: 10px;
    }
    .groups-container {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        padding: 15px;
    }
    .group-item {
        display: flex;
        align-items: center;
        justify-content: flex-start; /* 左对齐内容 */
        padding: 8px 120px 8px 15px; /* 右边预留120px给按钮 */
        background-color: #f8f9fa;
        border: 1px solid #ddd;
        border-radius: 4px;
        color: #333;
        text-decoration: none;
        position: relative;
        min-width: 200px; /* 确保有足够的宽度 */
    }
    .group-item:hover {
        background-color: #e9ecef;
    }
    .group-item.active {
        background-color: #007bff;
        color: white;
        border-color: #007bff;
    }
    .group-count {
        margin-left: 5px;
        font-size: 0.9em;
        color: inherit;
        opacity: 0.8;
    }
    .group-actions {
        position: absolute;
        right: 8px;
        top: 50%;
        transform: translateY(-50%);
        display: flex;
        gap: 5px;
        opacity: 0;
        visibility: hidden;
        transition: all 0.2s ease-in-out;
        background: rgba(255, 255, 255, 0.95);
        padding: 2px 6px;
        border-radius: 4px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.15);
    }
    .group-item:hover .group-actions {
        opacity: 1;
        visibility: visible;
    }
    .group-actions .btn {
        padding: 2px 6px;
        font-size: 0.75em;
        border: 1px solid #ddd;
        background: white;
        color: #666;
        white-space: nowrap;
        border-radius: 3px;
        transition: all 0.2s ease;
    }
    .group-actions .btn:hover {
        background: #f8f9fa;
        border-color: #007bff;
        color: #007bff;
        transform: translateY(-1px);
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }

    .group-actions .btn-danger:hover {
        background: #dc3545;
        border-color: #dc3545;
        color: white;
    }

    .group-item.active .group-actions {
        background: rgba(0, 123, 255, 0.95);
    }

    .group-item.active .group-actions button {
        background: rgba(255, 255, 255, 0.9);
        color: #007bff;
        border-color: rgba(255, 255, 255, 0.7);
    }

    .group-item.active .group-actions button:hover {
        background: white;
        color: #007bff;
        border-color: white;
    }

    .group-item.active .group-actions button.btn-danger:hover {
        background: #dc3545;
        color: white;
        border-color: #dc3545;
    }
    
    /* 分组类型颜色 */
    .group-type-main { border-left: 4px solid #28a745; }
    .group-type-side { border-left: 4px solid #fd7e14; }
    .group-type-daily { border-left: 4px solid #17a2b8; }
    .group-type-achievement { border-left: 4px solid #6f42c1; }
    .group-type-hidden { border-left: 4px solid #6c757d; }
    
    /* 徽章颜色 */
    .badge-main { background-color: #28a745; color: white; }
    .badge-side { background-color: #fd7e14; color: white; }
    .badge-daily { background-color: #17a2b8; color: white; }
    .badge-achievement { background-color: #6f42c1; color: white; }
    .badge-hidden { background-color: #6c757d; color: white; }
</style>
<?php
$extra_css = ob_get_clean();

// 引入页面头部
require_once 'layout_header.php';

// 引入数据库配置
require_once '../config/Database.php';

// 初始化变量
$success_message = '';
$error_message = '';
$quests = [];
$npc_templates = [];
$groups = []; // 添加分组变量

try {
    $pdo = Database::getInstance()->getConnection();
    
    // 获取所有任务分组
    $stmt = $pdo->query("
        SELECT * FROM quest_groups
        ORDER BY sort_order, name
    ");
    $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 获取所有NPC模板（用于筛选和表单选择）
    $stmt = $pdo->query("SELECT id, name FROM npc_templates ORDER BY name");
    $npc_templates = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 构建查询
    $filter_group = isset($_GET['group']) ? (int)$_GET['group'] : null;
    
    $sql = "
        SELECT q.*, 
               giver.name AS giver_name,
               receiver.name AS receiver_name,
               qg.name AS group_name,
               qg.type AS group_type
        FROM quests q
        LEFT JOIN npc_templates giver ON q.giver_npc_id = giver.id
        LEFT JOIN npc_templates receiver ON q.receiver_npc_id = receiver.id
        LEFT JOIN quest_groups qg ON q.group_id = qg.id
    ";
    
    $params = [];
    if ($filter_group) {
        $sql .= " WHERE q.group_id = :group_id";
        $params[':group_id'] = $filter_group;
    }
    
    $sql .= " ORDER BY qg.sort_order, qg.name, q.min_level, q.title";
    
    $stmt = $pdo->prepare($sql);
    $stmt->execute($params);
    $quests = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 处理添加/编辑/删除分组
    if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
        if ($_POST['action'] === 'add_group') {
            // 添加新分组
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'side';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    INSERT INTO quest_groups 
                    (name, type, description, sort_order)
                    VALUES 
                    (:name, :type, :description, :sort_order)
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order
                ]);
                $success_message = '分组添加成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM quest_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } elseif ($_POST['action'] === 'update_group' && isset($_POST['id'])) {
            // 更新分组
            $id = (int)$_POST['id'];
            $name = $_POST['name'] ?? '';
            $type = $_POST['type'] ?? 'side';
            $description = $_POST['description'] ?? '';
            $sort_order = (int)($_POST['sort_order'] ?? 0);
            
            if (empty($name)) {
                $error_message = "分组名称不能为空";
            } else {
                $stmt = $pdo->prepare("
                    UPDATE quest_groups 
                    SET name = :name,
                        type = :type,
                        description = :description,
                        sort_order = :sort_order
                    WHERE id = :id
                ");
                $stmt->execute([
                    ':name' => $name,
                    ':type' => $type,
                    ':description' => $description,
                    ':sort_order' => $sort_order,
                    ':id' => $id
                ]);
                $success_message = '分组更新成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM quest_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        } elseif ($_POST['action'] === 'delete_group' && isset($_POST['id'])) {
            // 删除分组
            $id = (int)$_POST['id'];
            
            // 先检查是否有任务使用此分组
            $stmt = $pdo->prepare("
                SELECT COUNT(*) FROM quests WHERE group_id = :id
            ");
            $stmt->execute([':id' => $id]);
            $count = $stmt->fetchColumn();
            
            if ($count > 0) {
                $error_message = "无法删除此分组，有 {$count} 个任务属于该分组";
            } else {
                $stmt = $pdo->prepare("DELETE FROM quest_groups WHERE id = :id");
                $stmt->execute([':id' => $id]);
                
                $success_message = '分组删除成功！';
                
                // 重新获取分组列表
                $stmt = $pdo->query("
                    SELECT * FROM quest_groups
                    ORDER BY sort_order, name
                ");
                $groups = $stmt->fetchAll(PDO::FETCH_ASSOC);
            }
        }
    }
} catch (PDOException $e) {
    $error_message = "数据库错误: " . $e->getMessage();
}
?>

<div class="page-content">
    <?php if (!empty($success_message)): ?>
        <div class="alert alert-success"><?php echo $success_message; ?></div>
    <?php endif; ?>
    
    <?php if (!empty($error_message)): ?>
        <div class="alert alert-danger"><?php echo $error_message; ?></div>
    <?php endif; ?>
    
    <div class="content-header">
        <div class="header-actions">
            <button id="add-group-btn" class="btn btn-success">添加分组</button>
            <button id="add-quest-btn" class="btn btn-primary">创建新任务</button>
        </div>
    </div>
    
    <!-- 分组管理 -->
    <div class="card mb-4">
        <div class="groups-container">
            <a href="quests.php" class="group-item <?php echo !isset($_GET['group']) ? 'active' : ''; ?>">
                <div>全部任务</div>
            </a>
            <?php foreach($groups as $group): ?>
                <a href="quests.php?group=<?php echo $group['id']; ?>" 
                   class="group-item <?php echo (isset($_GET['group']) && $_GET['group'] == $group['id']) ? 'active' : ''; ?> group-type-<?php echo $group['type']; ?>">
                    <div>
                        <?php echo htmlspecialchars($group['name']); ?> 
                        <span class="group-count">(<?php 
                            // 计算该分组下的任务数量
                            $stmt = $pdo->prepare("SELECT COUNT(*) FROM quests WHERE group_id = :group_id");
                            $stmt->execute([':group_id' => $group['id']]);
                            echo $stmt->fetchColumn(); 
                        ?>)</span>
                    </div>
                    <div class="group-actions">
                        <button class="btn btn-sm edit-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>"
                                data-type="<?php echo $group['type']; ?>"
                                data-sort="<?php echo $group['sort_order']; ?>"
                                data-description="<?php echo htmlspecialchars($group['description'] ?? ''); ?>">编辑</button>
                        <button class="btn btn-sm btn-danger delete-group" data-id="<?php echo $group['id']; ?>" 
                                data-name="<?php echo htmlspecialchars($group['name']); ?>">删除</button>
                    </div>
                </a>
            <?php endforeach; ?>
        </div>
    </div>
    
    <div class="filters">
        <div class="filter-group">
            <label for="filter-type">类型:</label>
            <select id="filter-type">
                <option value="">全部</option>
                <option value="dialogue">对话</option>
                <option value="collection">收集</option>
                <option value="kill">击杀</option>
                <!-- <option value="escort">护送</option> -->
                <!-- <option value="exploration">探索</option> -->
            </select>
        </div>
        
        <div class="filter-group">
            <label for="filter-level">等级:</label>
            <input type="number" id="filter-level-min" placeholder="最低" min="1" style="width: 70px;">
            <span>-</span>
            <input type="number" id="filter-level-max" placeholder="最高" min="1" style="width: 70px;">
        </div>
        
        <div class="filter-group">
            <label for="filter-npc">NPC:</label>
            <select id="filter-npc">
                <option value="">全部</option>
                <?php foreach ($npc_templates as $npc): ?>
                    <option value="<?php echo $npc['id']; ?>"><?php echo htmlspecialchars($npc['name']); ?></option>
                <?php endforeach; ?>
            </select>
        </div>
        
        <div class="filter-group">
            <label for="filter-search">搜索:</label>
            <input type="text" id="filter-search" placeholder="任务名称或描述">
        </div>
        
        <div class="quest-count">共 <?php echo count($quests); ?> 个任务</div>
    </div>
    
    <div class="quest-grid">
        <?php if (empty($quests)): ?>
            <p class="empty-state">还没有创建任何任务。点击"创建新任务"按钮开始添加。</p>
        <?php else: ?>
            <?php foreach ($quests as $quest): ?>
                <div class="quest-card" 
                     data-id="<?php echo $quest['id']; ?>"
                     data-type="<?php echo $quest['type']; ?>"
                     data-level="<?php echo $quest['min_level']; ?>"
                     data-giver="<?php echo $quest['giver_npc_id']; ?>"
                     data-receiver="<?php echo $quest['receiver_npc_id']; ?>">
                    <h3><?php echo htmlspecialchars($quest['title']); ?></h3>
                    <div>
                        <span class="quest-type <?php echo $quest['type']; ?>"><?php echo ucfirst($quest['type']); ?></span>
                        <span class="quest-level">Lv.<?php echo $quest['min_level']; ?></span>
                        <?php if ($quest['is_repeatable']): ?>
                            <span class="badge">可重复</span>
                        <?php endif; ?>
                        <?php if (!empty($quest['group_name'])): ?>
                            <span class="badge badge-<?php echo $quest['group_type']; ?>"><?php echo htmlspecialchars($quest['group_name']); ?></span>
                        <?php endif; ?>
                    </div>
                    <div class="quest-description"><?php echo htmlspecialchars($quest['description']); ?></div>
                    <div class="quest-npcs">
                        <div>发布: <?php echo htmlspecialchars($quest['giver_name']); ?></div>
                        <?php if ($quest['receiver_npc_id']): ?>
                            <div>接收: <?php echo htmlspecialchars($quest['receiver_name']); ?></div>
                        <?php endif; ?>
                    </div>
                    <div class="quest-rewards">
                        <div class="reward-item">
                            <i class="fas fa-coins"></i> <?php echo $quest['reward_gold']; ?> 金币
                        </div>
                        <div class="reward-item">
                            <i class="fas fa-star"></i> <?php echo $quest['reward_exp']; ?> 经验
                        </div>
                        <?php if (!empty($quest['reward_items'])): ?>
                            <div class="reward-item">
                                <i class="fas fa-box"></i> 物品奖励
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="quest-actions">
                        <a href="quest_editor.php?id=<?php echo $quest['id']; ?>" class="btn btn-sm">编辑</a>
                        <button class="btn btn-sm btn-danger delete-quest" data-id="<?php echo $quest['id']; ?>" data-title="<?php echo htmlspecialchars($quest['title']); ?>">删除</button>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
</div>

<!-- 创建任务模态框 -->
<div id="add-quest-modal" class="modal" style="display: none;">
    <div class="modal-content large">
        <span class="close">&times;</span>
        <h3>创建新任务</h3>
        
        <form method="post" action="quest_editor.php">
            <div class="form-row">
                <div class="form-group">
                    <label for="title">任务名称</label>
                    <input type="text" id="title" name="title" required>
                </div>
                <div class="form-group">
                    <label for="type">任务类型</label>
                    <select id="type" name="type" required>
                        <option value="dialogue">对话</option>
                        <option value="collection">收集</option>
                        <option value="kill">击杀</option>
                        <!-- <option value="escort">护送</option> -->
                        <!-- <option value="exploration">探索</option> -->
                    </select>
                </div>
            </div>
            
            <div class="form-group">
                <label for="group_id">所属分组</label>
                <select id="group_id" name="group_id">
                    <option value="">-- 选择分组 --</option>
                    <?php foreach ($groups as $group): ?>
                        <option value="<?php echo $group['id']; ?>"><?php echo htmlspecialchars($group['name']); ?></option>
                    <?php endforeach; ?>
                </select>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="giver_npc_id">发布NPC</label>
                    <select id="giver_npc_id" name="giver_npc_id" required>
                        <option value="">-- 选择发布NPC --</option>
                        <?php foreach ($npc_templates as $npc): ?>
                            <option value="<?php echo $npc['id']; ?>"><?php echo htmlspecialchars($npc['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="form-group">
                    <label for="receiver_npc_id">接收NPC (可选)</label>
                    <select id="receiver_npc_id" name="receiver_npc_id">
                        <option value="">-- 选择接收NPC --</option>
                        <?php foreach ($npc_templates as $npc): ?>
                            <option value="<?php echo $npc['id']; ?>"><?php echo htmlspecialchars($npc['name']); ?></option>
                        <?php endforeach; ?>
                    </select>
                </div>
            </div>
            
            <div class="form-row">
                <div class="form-group">
                    <label for="min_level">最低等级要求</label>
                    <input type="number" id="min_level" name="min_level" min="1" value="1" required>
                </div>
                <div class="form-group">
                    <label for="is_repeatable">
                        <input type="checkbox" id="is_repeatable" name="is_repeatable" value="1">
                        可重复任务
                    </label>
                </div>
            </div>
            
            <div class="form-group">
                <label for="description">任务描述</label>
                <textarea id="description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">创建任务</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除确认模态框 -->
<div id="delete-quest-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-quest-message"></p>
        
        <form method="post" action="api_quests.php">
            <input type="hidden" name="action" value="delete_quest">
            <input type="hidden" name="quest_id" id="delete-quest-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 添加分组模态框 -->
<div id="add-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>添加任务分组</h3>
        
        <form method="post" action="quests.php">
            <input type="hidden" name="action" value="add_group">
            
            <div class="form-group">
                <label for="add-group-name">名称</label>
                <input type="text" id="add-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="add-group-type">类型</label>
                <select id="add-group-type" name="type" class="form-control">
                    <option value="main">主线</option>
                    <option value="side">支线</option>
                    <option value="daily">每日</option>
                    <option value="achievement">成就</option>
                    <option value="hidden">隐藏</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="add-group-sort">排序顺序</label>
                <input type="number" id="add-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="add-group-description">描述</label>
                <textarea id="add-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">添加</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 编辑分组模态框 -->
<div id="edit-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>编辑任务分组</h3>
        
        <form method="post" action="quests.php">
            <input type="hidden" name="action" value="update_group">
            <input type="hidden" name="id" id="edit-group-id" value="">
            
            <div class="form-group">
                <label for="edit-group-name">名称</label>
                <input type="text" id="edit-group-name" name="name" required>
            </div>
            
            <div class="form-group">
                <label for="edit-group-type">类型</label>
                <select id="edit-group-type" name="type" class="form-control">
                    <option value="main">主线</option>
                    <option value="side">支线</option>
                    <option value="daily">每日</option>
                    <option value="achievement">成就</option>
                    <option value="hidden">隐藏</option>
                </select>
            </div>
            
            <div class="form-group">
                <label for="edit-group-sort">排序顺序</label>
                <input type="number" id="edit-group-sort" name="sort_order" value="0" min="0" class="form-control">
            </div>
            
            <div class="form-group">
                <label for="edit-group-description">描述</label>
                <textarea id="edit-group-description" name="description" rows="3"></textarea>
            </div>
            
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">更新</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 删除分组确认模态框 -->
<div id="delete-group-modal" class="modal" style="display: none;">
    <div class="modal-content">
        <span class="close">&times;</span>
        <h3>确认删除</h3>
        <p id="delete-group-message"></p>
        
        <form method="post" action="quests.php">
            <input type="hidden" name="action" value="delete_group">
            <input type="hidden" name="id" id="delete-group-id" value="">
            
            <div class="form-actions">
                <button type="submit" class="btn btn-danger">确认删除</button>
                <button type="button" class="btn modal-cancel">取消</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 模态框元素
    const addQuestModal = document.getElementById('add-quest-modal');
    const deleteQuestModal = document.getElementById('delete-quest-modal');
    const addGroupModal = document.getElementById('add-group-modal');
    const editGroupModal = document.getElementById('edit-group-modal');
    const deleteGroupModal = document.getElementById('delete-group-modal');
    
    // 打开创建任务模态框
    document.getElementById('add-quest-btn').addEventListener('click', function() {
        addQuestModal.style.display = 'block';
    });
    
    // 打开添加分组模态框
    document.getElementById('add-group-btn').addEventListener('click', function() {
        addGroupModal.style.display = 'block';
    });
    
    // 打开删除确认模态框
    document.querySelectorAll('.delete-quest').forEach(function(btn) {
        btn.addEventListener('click', function() {
            const questId = this.dataset.id;
            const questTitle = this.dataset.title;
            
            document.getElementById('delete-quest-id').value = questId;
            document.getElementById('delete-quest-message').textContent = `确定要删除任务 "${questTitle}" 吗？这将同时删除所有相关的任务目标和玩家进度数据。`;
            
            deleteQuestModal.style.display = 'block';
        });
    });
    
    // 编辑分组按钮事件
    document.querySelectorAll('.edit-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const id = this.dataset.id;
            const name = this.dataset.name;
            const type = this.dataset.type;
            const sort = this.dataset.sort;
            const description = this.dataset.description;
            
            document.getElementById('edit-group-id').value = id;
            document.getElementById('edit-group-name').value = name;
            document.getElementById('edit-group-type').value = type;
            document.getElementById('edit-group-sort').value = sort;
            document.getElementById('edit-group-description').value = description;
            
            editGroupModal.style.display = 'block';
        });
    });
    
    // 删除分组按钮事件
    document.querySelectorAll('.delete-group').forEach(function(btn) {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            
            const id = this.dataset.id;
            const name = this.dataset.name;
            
            document.getElementById('delete-group-id').value = id;
            document.getElementById('delete-group-message').textContent = `确定要删除分组 "${name}" 吗？这将解除该分组与所有任务的关联。`;
            
            deleteGroupModal.style.display = 'block';
        });
    });
    
    // 关闭模态框
    document.querySelectorAll('.close, .modal-cancel').forEach(function(el) {
        el.addEventListener('click', function() {
            addQuestModal.style.display = 'none';
            deleteQuestModal.style.display = 'none';
            addGroupModal.style.display = 'none';
            editGroupModal.style.display = 'none';
            deleteGroupModal.style.display = 'none';
        });
    });
    
    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        if (event.target == addQuestModal) {
            addQuestModal.style.display = 'none';
        }
        if (event.target == deleteQuestModal) {
            deleteQuestModal.style.display = 'none';
        }
        if (event.target == addGroupModal) {
            addGroupModal.style.display = 'none';
        }
        if (event.target == editGroupModal) {
            editGroupModal.style.display = 'none';
        }
        if (event.target == deleteGroupModal) {
            deleteGroupModal.style.display = 'none';
        }
    });
    
    // 处理删除任务表单的异步提交
    const deleteQuestForm = deleteQuestModal.querySelector('form');
    deleteQuestForm.addEventListener('submit', function(event) {
        event.preventDefault(); // 阻止表单的默认跳转行为
        
        const formData = new FormData(this);
        const submitButton = this.querySelector('button[type="submit"]');
        submitButton.disabled = true;
        submitButton.textContent = '删除中...';
        
        fetch(this.getAttribute('action'), {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，重新加载页面以显示最新列表
                location.reload();
            } else {
                alert('删除失败: ' + (data.message || '未知错误'));
                submitButton.disabled = false;
                submitButton.textContent = '确认删除';
            }
        })
        .catch(error => {
            console.error('删除任务时发生错误:', error);
            alert('删除任务时发生网络或服务器错误。');
            submitButton.disabled = false;
            submitButton.textContent = '确认删除';
        });
    });
    
    // 任务筛选功能
    const filterType = document.getElementById('filter-type');
    const filterLevelMin = document.getElementById('filter-level-min');
    const filterLevelMax = document.getElementById('filter-level-max');
    const filterNpc = document.getElementById('filter-npc');
    const filterSearch = document.getElementById('filter-search');
    const questCount = document.querySelector('.quest-count');
    
    function filterQuests() {
        const type = filterType.value;
        const levelMin = filterLevelMin.value ? parseInt(filterLevelMin.value) : 0;
        const levelMax = filterLevelMax.value ? parseInt(filterLevelMax.value) : Infinity;
        const npc = filterNpc.value;
        const search = filterSearch.value.toLowerCase();
        
        let visibleCount = 0;
        
        document.querySelectorAll('.quest-card').forEach(function(card) {
            const cardType = card.dataset.type;
            const cardLevel = parseInt(card.dataset.level);
            const cardGiver = card.dataset.giver;
            const cardReceiver = card.dataset.receiver;
            const cardTitle = card.querySelector('h3').textContent.toLowerCase();
            const cardDesc = card.querySelector('.quest-description').textContent.toLowerCase();
            
            let visible = true;
            
            if (type && cardType !== type) {
                visible = false;
            }
            
            if (cardLevel < levelMin || cardLevel > levelMax) {
                visible = false;
            }
            
            if (npc && cardGiver !== npc && cardReceiver !== npc) {
                visible = false;
            }
            
            if (search && !cardTitle.includes(search) && !cardDesc.includes(search)) {
                visible = false;
            }
            
            if (visible) {
                card.style.display = '';
                visibleCount++;
            } else {
                card.style.display = 'none';
            }
        });
        
        questCount.textContent = `显示 ${visibleCount} / ${document.querySelectorAll('.quest-card').length} 个任务`;
    }
    
    // 添加筛选事件监听器
    filterType.addEventListener('change', filterQuests);
    filterLevelMin.addEventListener('input', filterQuests);
    filterLevelMax.addEventListener('input', filterQuests);
    filterNpc.addEventListener('change', filterQuests);
    filterSearch.addEventListener('input', filterQuests);
});
</script>

<?php require_once 'layout_footer.php'; ?> 