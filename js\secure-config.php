<?php
/**
 * 安全JS加载器配置文件
 * 集中管理所有安全相关设置
 */

return [
    // 基础安全设置
    'security' => [
        // 主密钥（请修改为您自己的密钥）
        'secret_key' => 'your_ultra_secret_key_2024_' . date('Ymd'),
        
        // 允许的域名列表
        'allowed_domains' => [
            'localhost',
            '127.0.0.1',
            'wx.xstudio.net.cn',
            // 添加您的域名
            // 'yourdomain.com',
            // 'www.yourdomain.com',
        ],
        
        // 允许的IP地址（可选，留空表示不限制）
        'allowed_ips' => [
            // '*************',
            // '*********',
        ],
        
        // 时间戳验证窗口（秒）
        'timestamp_window' => 300, // 5分钟
        
        // 是否启用严格模式
        'strict_mode' => true,
    ],
    
    // 频率限制设置
    'rate_limit' => [
        // 是否启用频率限制
        'enabled' => true,
        
        // 时间窗口（秒）
        'window' => 60, // 1分钟
        
        // 最大请求次数
        'max_requests' => 10,
        
        // 封禁时间（秒）
        'ban_duration' => 300, // 5分钟
    ],
    
    // 反调试设置
    'anti_debug' => [
        // 是否启用反调试
        'enabled' => true,
        
        // 检测间隔（毫秒）
        'check_interval' => 1000,
        
        // 调试检测阈值（毫秒）
        'debug_threshold' => 100,
        
        // 是否检测开发者工具
        'detect_devtools' => true,
        
        // 开发者工具检测间隔（毫秒）
        'devtools_check_interval' => 500,
        
        // 开发者工具尺寸阈值
        'devtools_size_threshold' => 200,
    ],
    
    // 内容处理设置
    'content' => [
        // 是否压缩JS
        'minify' => true,
        
        // 是否移除注释
        'remove_comments' => true,
        
        // 是否添加混淆
        'obfuscate' => false, // 简单混淆，可能影响功能
        
        // 文件映射字典 - 使用简短代码映射到实际文件
        'file_mapping' => [
            'a' => 'anti-debug-gentle.js',
            'b' => 'building-manager.js',
            'c' => 'crypto-js.min.js',
            'd' => 'MessageProtocol.js',
            'e' => 'UnencryptedMessageProtocol.js',
            'f' => 'SecureMessageProtocol.js',
            'g' => 'gameclient.js',
            'h' => 'html.js',
            'i' => 'skill-manager.js',
            'j' => 'npc-manager.js',
            'k' => 'quest-manager.js',
            'l' => 'pvp-manager.js',
        ],

        // 文件白名单 - 只有这些文件允许被访问
        'allowed_files' => [
            'UnencryptedMessageProtocol.js',
            'SecureMessageProtocol.js',
            'html.js',
            'gameclient.js',
            'MessageProtocol.js',
            'anti-debug-gentle.js',
            'crypto-js.min.js',
            'skill-manager.js',
            'building-manager.js',
            'npc-manager.js',
            'pvp-manager.js',
            'quest-manager.js',
        ],

        // 文件访问权限控制
        'file_permissions' => [
            'UnencryptedMessageProtocol.js' => ['read'],
            'SecureMessageProtocol.js' => ['read'],
            'html.js' => ['read'],
            'gameclient.js' => ['read'],
            'MessageProtocol.js' => ['read'],
            'anti-debug-gentle.js' => ['read'],
            'crypto-js.min.js' => ['read'],
            'skill-manager.js' => ['read'],
            'building-manager.js' => ['read'],
            'npc-manager.js' => ['read'],
            'pvp-manager.js' => ['read'],
            'quest-manager.js' => ['read'],
        ],
        
        // 是否添加完整性检查
        'integrity_check' => true,
    ],
    
    // 日志设置
    'logging' => [
        // 是否启用日志
        'enabled' => true,
        
        // 日志级别：'error', 'warning', 'info', 'debug'
        'level' => 'warning',
        
        // 日志文件路径（留空使用系统默认）
        'file_path' => '',
        
        // 是否记录成功访问
        'log_success' => false,
        
        // 是否记录详细信息
        'detailed' => true,
    ],
    
    // 响应设置
    'response' => [
        // 缓存控制
        'cache_control' => 'no-cache, no-store, must-revalidate',
        
        // 是否添加安全头
        'security_headers' => true,
        
        // 自定义响应头
        'custom_headers' => [
            'X-Powered-By' => 'SecureJS/1.0',
            'X-Content-Type-Options' => 'nosniff',
            'X-Frame-Options' => 'SAMEORIGIN',
        ],
        
        // 错误时返回的假内容
        'fake_content' => [
            '// Loading system...',
            'console.log("Initializing security checks...");',
            'var systemReady = false;',
            'setTimeout(function() { systemReady = true; }, 2000);',
            '// Security validation complete',
        ],
    ],
    
    // 高级安全选项
    'advanced' => [
        // 是否启用指纹验证
        'fingerprint_check' => false,
        
        // 是否检查HTTP头顺序
        'header_order_check' => false,
        
        // 是否启用蜜罐检测
        'honeypot' => false,
        
        // 是否启用地理位置限制
        'geo_restriction' => false,
        
        // 允许的国家代码（需要GeoIP数据库）
        'allowed_countries' => ['CN', 'US', 'GB'],
        
        // 是否启用机器人检测
        'bot_detection' => true,
        
        // 机器人检测规则
        'bot_patterns' => [
            '/bot/i',
            '/crawler/i',
            '/spider/i',
            '/scraper/i',
        ],
    ],
    
    // 开发模式设置
    'development' => [
        // 是否为开发模式
        'enabled' => false,
        
        // 开发模式下允许的IP
        'allowed_dev_ips' => [
            '127.0.0.1',
            '::1',
        ],
        
        // 是否在开发模式下禁用某些安全检查
        'disable_security_in_dev' => true,
        
        // 是否显示调试信息
        'show_debug_info' => false,
    ],
    
    // 性能设置
    'performance' => [
        // 是否启用内容缓存
        'enable_cache' => false,
        
        // 缓存时间（秒）
        'cache_duration' => 3600, // 1小时
        
        // 缓存目录
        'cache_dir' => sys_get_temp_dir() . '/secure-js-cache',
        
        // 是否启用gzip压缩
        'gzip_compression' => true,
        
        // 压缩级别（1-9）
        'gzip_level' => 6,
    ],
    
    // 通知设置
    'notifications' => [
        // 是否启用邮件通知
        'email_enabled' => false,
        
        // 通知邮箱
        'email_to' => '<EMAIL>',
        
        // 发送邮箱
        'email_from' => '<EMAIL>',
        
        // 通知阈值（每小时最大可疑访问次数）
        'alert_threshold' => 50,
        
        // 是否启用Webhook通知
        'webhook_enabled' => false,
        
        // Webhook URL
        'webhook_url' => '',
        
        // Webhook密钥
        'webhook_secret' => '',
    ],
];
?>
