function showLogin() {
    document.getElementById('loginSection').style.display = 'block';
    document.getElementById('registerSection').style.display = 'none';
    document.getElementById('registerError').textContent = '';
    document.getElementById('registerSuccess').textContent = '';
}

function showRegister() {
    document.getElementById('loginSection').style.display = 'none';
    document.getElementById('registerSection').style.display = 'block';
    document.getElementById('loginError').textContent = '';
}

document.getElementById('loginButton').addEventListener('click', (e) => {
    e.preventDefault();
    const loginName = document.getElementById('username').value.trim();
    const pass = document.getElementById('password').value.trim();
    if (!loginName || !pass) {
        document.getElementById('loginError').textContent = '账号和密码不能为空。';
        return;
    }
    document.getElementById('loginError').textContent = '';
    game.expectingUnencryptedAuthResponse = true;
    game.sendMessage(MessageProtocol.C2S_LOGIN, { login_name: loginName, password: pass }, true);
});

document.getElementById('registerButton').addEventListener('click', (e) => {
    e.preventDefault();
    const loginName = document.getElementById('reg_login_name').value.trim();
    const nickname = document.getElementById('reg_nickname').value.trim();
    const pass = document.getElementById('reg_password').value.trim();
    if (!loginName || !nickname || !pass || pass.length < 6) {
        document.getElementById('registerError').textContent = '账号、昵称和密码均不能为空，且密码最少6位。';
        return;
    }
    document.getElementById('registerError').textContent = '';
    document.getElementById('registerSuccess').textContent = '';
    game.expectingUnencryptedAuthResponse = true;
    game.sendMessage(MessageProtocol.C2S_REGISTER, { login_name: loginName, nickname: nickname, password: pass }, true);
});

document.getElementById('showRegisterLink').addEventListener('click', (e) => { e.preventDefault(); showRegister(); });
document.getElementById('showLoginLink').addEventListener('click', (e) => { e.preventDefault(); showLogin(); });

function move(direction) { game.sendMessage(MessageProtocol.C2S_MOVE, { direction: direction }); }
function enterScene(sceneId) { game.sendMessage(MessageProtocol.C2S_ENTER_SCENE, { scene_id: sceneId }); }

function showMonsterDetails(monsterId) {
    this.sendMessage(MessageProtocol.C2S_GET_MONSTER_DETAILS, { monster_id: monsterId });
}

function hideMonsterDetailView() {
    document.getElementById('monsterDetailView').style.display = 'none';
    document.getElementById('main-view').style.display = 'block';
}

class GameClient {
    constructor() {
        this.ws = null;
        this.sessionKey = null;
        this.currentPlayer = null;
        this.currentScene = null; 
        this.scenes = new Map(); // Use a Map to store discovered scenes by ID
        this.players = []; 
        this.heartbeatInterval = null;
        this.currentExits = null;
        this.isConnecting = false; this.pendingMessages = []; this.pongTimeout = null;

        // Battle state
        this.battleState = null; // Holds { player, monster, playerATB, monsterATB, etc. }
        this.battleLoopInterval = null;
        this.battleLogCache = new Set(); // 用于存储已显示的战斗日志，防止重复
        this.battleLogExpanded = false; // 跟踪战斗日志是否展开

        this.inactivityTimer = null;
        this.pendingResumeData = null;
        this.coordinatesAssigned = false;
        this.inventory = { equipped: [], backpack: [], currencies: { gold: 0, diamonds: 0 } };
        this.inventoryCacheTime = 0;
        this.inventoryView = { currentPage: 1, itemsPerPage: 10, currentFilter: 'All' };
        this.itemDetailOrigin = null; // To track where item detail view was opened from
        this.isAwaitingEquipResponse = false;
        this.expectingUnencryptedAuthResponse = false;
        this.categoryMap = { 'Equipment': '装备', 'Gem': '宝石', 'Material': '材料', 'Potion': '药品', 'Rune': '符石', 'Misc': '杂物', 'Scroll': '书卷' };
        this.playerAttributeMap = {
            'hp': '生命', 'max_hp': '最大生命', 'mp': '魔力', 'max_mp': '最大魔力', 'strength': '力量', 'agility': '敏捷', 'constitution': '体质', 
            'intelligence': '智慧', 'attack': '攻击', 'defense': '防御', 'attack_speed': '攻速', 'fire_resistance': '火抗', 'ice_resistance': '冰抗', 
            'wind_resistance': '风抗', 'electric_resistance': '电抗', 'fire_damage': '火伤', 'wind_damage': '风裂', 'electric_damage': '电击', 'ice_damage': '冰冻',
            'experience': '经验', 'potential_points': '潜力', 'knowledge_points': '知识', 'diamonds': '钻石', 'gold': '金币'
        };

        // 初始化WebSocket协议处理
        this.unencryptedProtocol = new UnencryptedMessageProtocol();
        this.secureProtocol = new SecureMessageProtocol();
        // 初始化技能管理器
        this.skillManager = new SkillManager(this);
        // 初始化建筑管理器
        this.buildingManager = new BuildingManager(this);
        // 初始化NPC管理器
        this.npcManager = new NPCManager(this);
        // 初始化任务管理器
        this.questManager = new QuestManager(this);
        // 初始化PVP管理器
        this.pvpManager = new PvpManager(this);

        // 公告相关属性
        this.currentAnnouncement = null;
        this.announcementList = [];

        // 公聊频率限制
        this.publicChatMessageCount = 0;
        this.publicChatLastMinute = Math.floor(Date.now() / 60000);

        // 注册处理玩家复活消息
        document.addEventListener('player-revived', this.handlePlayerRevivedEvent.bind(this));

        // 设置兑换码事件
        this.setupRedemptionCodeEvents();

        this.checkForStoredSession();
        this.connectWebSocket();
    }

    // 设置兑换码相关事件
    setupRedemptionCodeEvents() {
        // 使用延迟执行，确保DOM已加载
        setTimeout(() => {
            const input = document.getElementById('redemption-code-input');
            if (input) {
                // 回车键兑换
                input.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        e.preventDefault();
                        this.redeemCode();
                    }
                });

                // 自动转换为大写
                input.addEventListener('input', (e) => {
                    e.target.value = e.target.value.toUpperCase();
                });

                // 只允许输入字母和数字
                input.addEventListener('keydown', (e) => {
                    const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Enter', 'ArrowLeft', 'ArrowRight'];
                    const isAlphaNumeric = /^[a-zA-Z0-9]$/.test(e.key);

                    if (!allowedKeys.includes(e.key) && !isAlphaNumeric) {
                        e.preventDefault();
                    }
                });
            }
        }, 100);

        // 添加兑换码界面的CSS样式
        this.addRedemptionCodeStyles();

        // 添加公告界面的CSS样式
        this.addAnnouncementStyles();
    }

    // 添加兑换码界面样式
    addRedemptionCodeStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #redemptionCodeView {
                max-width: 100%;
                overflow-x: hidden;
                word-wrap: break-word;
            }

            #redemptionCodeView .redemption-code-container {
                max-width: 100%;
                box-sizing: border-box;
            }

            #redemptionCodeView .redemption-input {
                transition: all 0.3s ease;
                box-sizing: border-box;
            }

            #redemptionCodeView .redemption-input:focus {
                border-color: var(--main-color);
                box-shadow: 0 0 3px rgba(52, 152, 219, 0.3);
                outline: none;
            }

            #redemptionCodeView .btn {
                box-sizing: border-box;
                white-space: nowrap;
            }

            #redemptionCodeView .btn-primary {
                background-color: var(--main-color);
                color: white;
                border: 1px solid var(--main-color);
                transition: all 0.3s ease;
            }

            #redemptionCodeView .btn-primary:hover:not(:disabled) {
                background-color: #2980b9;
                border-color: #2980b9;
                transform: translateY(-1px);
            }

            #redemptionCodeView .btn-primary:disabled {
                opacity: 0.6;
                cursor: not-allowed;
                transform: none;
            }

            #redemption-result {
                animation: slideDown 0.3s ease-out;
                max-width: 100%;
                box-sizing: border-box;
            }

            @keyframes slideDown {
                from {
                    opacity: 0;
                    transform: translateY(-10px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }

            .redemption-tips {
                max-width: 100%;
                box-sizing: border-box;
            }

            .redemption-tips ul {
                max-width: 100%;
                box-sizing: border-box;
            }

            .redemption-tips ul li {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .redemption-tips ul li:before {
                content: "• ";
                color: var(--main-color);
                font-weight: bold;
            }

            /* 确保按钮容器不超出界面 */
            #redemptionCodeView .redemption-actions {
                display: flex;
                justify-content: center;
                gap: 5px;
                flex-wrap: wrap;
                max-width: 100%;
            }

            #redemptionCodeView .redemption-actions .btn {
                flex: 0 0 auto;
                max-width: calc(50% - 5px);
            }

            /* 响应式调整 */
            @media (max-width: 400px) {
                #redemptionCodeView {
                    padding: 8px;
                }

                #redemptionCodeView .redemption-input-section {
                    padding: 8px;
                }

                #redemptionCodeView .redemption-input {
                    max-width: 160px;
                    font-size: 13px;
                    padding: 5px 6px;
                }

                #redemptionCodeView .btn {
                    padding: 4px 10px;
                    font-size: 11px;
                    min-width: 45px;
                }

                #redemptionCodeView .redemption-actions .btn {
                    max-width: calc(50% - 3px);
                }
            }

            @media (max-width: 320px) {
                #redemptionCodeView .redemption-input {
                    max-width: 140px;
                    font-size: 12px;
                }

                #redemptionCodeView .btn {
                    padding: 4px 8px;
                    font-size: 10px;
                    min-width: 40px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    // 添加公告界面样式
    addAnnouncementStyles() {
        const style = document.createElement('style');
        style.textContent = `
            #announcementListView {
                max-width: 100%;
                overflow-x: hidden;
                word-wrap: break-word;
            }

            .announcement-list-container {
                max-width: 100%;
                box-sizing: border-box;
            }

            .announcement-item {
                transition: background-color 0.3s ease;
            }

            .announcement-item:hover {
                background-color: rgba(52, 152, 219, 0.1);
                border-radius: 4px;
            }

            .announcement-item:last-child {
                border-bottom: none !important;
            }

            .announcement-header h4 {
                transition: color 0.3s ease;
            }

            .announcement-item:hover .announcement-header h4 {
                color: var(--main-color);
            }

            .announcement-summary {
                word-wrap: break-word;
                overflow-wrap: break-word;
            }

            .loading {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.5; }
                100% { opacity: 1; }
            }

            /* 响应式调整 */
            @media (max-width: 400px) {
                #announcementListView {
                    padding: 8px;
                }

                .announcement-item {
                    padding: 8px 0;
                }

                .announcement-header h4 {
                    font-size: 13px;
                }

                .announcement-summary {
                    font-size: 12px;
                }
            }
        `;
        document.head.appendChild(style);
    }

    setupPointAllocatorControls() {
        const attributes = ['strength', 'agility', 'constitution', 'intelligence'];
        attributes.forEach(attr => {
            const input = document.getElementById(`${attr}_points`);
            if (input && input.parentElement) {
                // 防止重复添加
                if (input.parentElement.querySelector('.point-adjust-btn')) {
                    return;
                }

                input.style.width = '45px';
                input.style.textAlign = 'center';

                const wrapper = document.createElement('span');
                wrapper.style.marginLeft = '5px';
                wrapper.className = 'point-adjust-btn-wrapper';

                const minusBtn = document.createElement('button');
                minusBtn.textContent = '-';
                minusBtn.className = 'btn-small point-adjust-btn';
                minusBtn.type = 'button';
                minusBtn.style.marginRight = '2px';
                minusBtn.onclick = () => this.adjustPointAllocation(attr, -1);

                const plusBtn = document.createElement('button');
                plusBtn.textContent = '+';
                plusBtn.className = 'btn-small point-adjust-btn';
                plusBtn.type = 'button';
                plusBtn.onclick = () => this.adjustPointAllocation(attr, 1);

                wrapper.appendChild(minusBtn);
                wrapper.appendChild(plusBtn);

                input.after(wrapper);
            }
        });
        
        // 修改：不再创建新的div，而是查找并修改现有的标题
        const allocator = document.getElementById('potentialPointsAllocator');
        if (allocator) {
            const titleElement = allocator.querySelector('h5'); // 修改为h5以匹配HTML中的元素
            if (titleElement) {
                // 如果还没有添加过状态信息
                if (!titleElement.querySelector('#points-distribution-summary')) {
                    const summarySpan = document.createElement('span');
                    summarySpan.id = 'points-distribution-summary';
                    summarySpan.style.marginLeft = '10px';
                    summarySpan.style.fontWeight = titleElement.style.fontWeight || 'normal';
                    summarySpan.style.fontSize = titleElement.style.fontSize || 'inherit';
                    titleElement.appendChild(summarySpan);
                    
                    // 初始更新一次状态
                    this.updatePointAllocationSummary();
                }
            }
        }
    }
    
    adjustPointAllocation(attribute, amount) {
        const input = document.getElementById(`${attribute}_points`);
        if (!input) return;

        let currentValue = parseInt(input.value) || 0;
        const potentialPoints = this.currentPlayer.attributes.potential_points || 0;

        const allocatedPoints = ['strength', 'agility', 'constitution', 'intelligence'].reduce((total, attr) => {
            return total + (parseInt(document.getElementById(`${attr}_points`).value) || 0);
        }, 0);

        if (amount > 0) { // Increment
            if (allocatedPoints < potentialPoints) {
                currentValue += 1;
                input.value = currentValue;
            }
        } else { // Decrement
            if (currentValue > 0) {
                currentValue -= 1;
                input.value = currentValue;
            }
        }
        
        this.updatePointAllocationSummary();
    }

    normalizePlayer(player) {
        if (!player) { return null; }
        
        // If the server is already sending the correct nested structure, do nothing.
        if (player.attributes) {
            return player;
        }

        // --- Transformation Logic ---
        // If we are here, it means the server sent a flat structure.
        // We will transform it into the nested structure the client expects.
        console.warn('Player data from server is in a flat format. Normalizing to nested attributes structure. Please update server to send {id, username, attributes: {...}}.', player);
        
        const attributes = {};
        // Define keys that should remain at the top level of the player object.
        const topLevelKeys = ['id', 'username', 'password', 'password_hash', 'status'];

        // Iterate over all keys in the received player object.
        for (const key in player) {
            // Check if the key is a direct property of the object and not in our exclusion list.
            if (Object.prototype.hasOwnProperty.call(player, key) && !topLevelKeys.includes(key)) {
                // If it's an attribute, move it to the attributes object.
                attributes[key] = player[key];
                // Remove the key from the top-level player object to avoid duplication.
                delete player[key];
            }
        }

        // Assign the newly populated attributes object to the player.
        player.attributes = attributes;
        
        return player;
    }
    
    checkForStoredSession() {
        const storedSession = localStorage.getItem('gameSession');
        if (storedSession) {
            try {
                const sessionData = JSON.parse(storedSession);
                // 增加详细的日志记录
                console.log("会话恢复数据:", {
                    playerId: sessionData.playerId, 
                    sessionKeyLength: sessionData.sessionKey ? sessionData.sessionKey.length : 'N/A'
                });

                // 立即设置会话密钥，以便解密服务器响应
                this.sessionKey = this.base64ToBytes(sessionData.sessionKey);
                this.pendingResumeData = sessionData; 
                console.log("预加载会话密钥成功，将尝试恢复会话。");
            } catch (e) {
                console.error("解析存储的会话失败:", e);
                localStorage.removeItem('gameSession');
                this.sessionKey = null; 
            }
        } else {
            console.log("未找到本地存储的会话。");
        }
    }

    connectWebSocket() {
        if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) return;
        this.isConnecting = true;
        this.updateConnectionStatus(false);
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        const host = window.location.hostname;
        this.ws = new WebSocket(`${protocol}//${host}:8080`);
        this.ws.binaryType = 'arraybuffer';
            
            this.ws.onopen = () => {
                this.isConnecting = false;
                this.updateConnectionStatus(true);
                this.heartbeat();
                
                if (this.pendingResumeData) {
                     console.log("Connection open, sending resume request.");
                     this.expectingUnencryptedAuthResponse = true;
                     this.sendMessage(
                        MessageProtocol.C2S_RESUME_SESSION, 
                        { 
                            player_id: this.pendingResumeData.playerId, 
                            session_key: this.pendingResumeData.sessionKey 
                        },
                        true // Force unencrypted
                    );
                    this.pendingResumeData = null;
                }

                this.pendingMessages.forEach(msg => this.sendMessage(msg.opcode, msg.payload));
                this.pendingMessages = [];
            };
            
            this.ws.onmessage = (event) => {
            if (this.pongTimeout) { clearTimeout(this.pongTimeout); this.pongTimeout = null; }
            this.handleServerMessage(event.data);
            };
            
            this.ws.onclose = (event) => {
            console.log('WebSocket closed:', event);
            this.isConnecting = false; this.ws = null;
            clearInterval(this.heartbeatInterval);
                this.updateConnectionStatus(false);
                    this.attemptReconnect();
        };
        this.ws.onerror = (error) => { console.error('WebSocket Error:', error); this.ws.close(); };
    }

    attemptReconnect() {
        this.sessionKey = null; // Invalidate session on disconnect
        document.getElementById('gameContent').style.display = 'none';
        document.getElementById('authSection').style.display = 'block';
        setTimeout(() => this.connectWebSocket(), 3000);
    }
    
    heartbeat() {
        clearInterval(this.heartbeatInterval);
        this.heartbeatInterval = setInterval(() => {
            if (this.isConnected()) {
                this.sendMessage(MessageProtocol.PING);
                this.pongTimeout = setTimeout(() => { this.ws.close(); }, 5000);
            }
        }, 30000);
    }

    updateConnectionStatus(connected) {
        const connectionStatus = document.getElementById('connectionStatus');
        
        // 检查是否有正在显示的聊天通知
        if (connectionStatus.querySelector('.chat-notification-bubble')) {
            // 如果有聊天通知正在显示，只更新保存的状态，不立即更改显示
            connectionStatus.setAttribute('data-original-text', connected ? '已连接' : '连接断开...');
        } else {
            // 没有聊天通知，直接更新显示
            connectionStatus.textContent = connected ? '已连接' : '连接断开...';
        }
        
        document.getElementById('loginButton').disabled = !connected;
        document.getElementById('registerButton').disabled = !connected;
    }

    handleServerMessage(binaryData) {
        let decodedMessage;

        const isUnencrypted = !this.sessionKey || this.expectingUnencryptedAuthResponse;
        
        if (this.expectingUnencryptedAuthResponse) {
            this.expectingUnencryptedAuthResponse = false;
        }

        if (isUnencrypted) {
            // Unencrypted messages are only for pre-authentication (login, register, error).
            const view = new DataView(binaryData);
            const opcode = view.getUint8(0);
            console.log("Decoding as unencrypted message. Opcode: 0x" + opcode.toString(16));
            decodedMessage = this.unencryptedProtocol.decode(binaryData);
        } else {
            // All post-authentication messages are encrypted.
            console.log("Decoding as secure message.");
            decodedMessage = this.secureProtocol.decode(binaryData, this.sessionKey);
        }

        if (!decodedMessage) {
            console.error(`Failed to decode message (was treated as ${isUnencrypted ? 'unencrypted' : 'encrypted'}).`);
            if (!isUnencrypted) { // Only logout if a secure message failed to decode
                 this.addLog("会话消息处理失败，请重新登录。");
                 this.logout();
            }
            return;
        }

        const { opcode, payload } = decodedMessage;
        
        // 添加更多调试信息
        const opcodeHex = opcode.toString(16).padStart(2, '0');
        const opcodeConstName = Object.keys(MessageProtocol).find(key => MessageProtocol[key] === opcode) || 'UNKNOWN';
        // console.log(`收到消息: 0x${opcodeHex} (${opcodeConstName})`, payload);
        if (opcode !== MessageProtocol.PONG && opcode !== MessageProtocol.S2C_ATB_STATUS_UPDATE && opcode !== MessageProtocol.S2C_BATTLE_UPDATE && opcode !== MessageProtocol.S2C_PVP_ATB_STATUS_UPDATE && opcode !== MessageProtocol.S2C_PVP_BATTLE_UPDATE) console.log('S2C:', {opcode: Object.keys(MessageProtocol).find(key => MessageProtocol[key] === opcode), payload});

        this.resetInactivityTimer();

        switch (opcode) {
            case MessageProtocol.S2C_LOGIN_SUCCESS: this.handleLoginSuccess(payload.player, payload.session_key); break;
            case MessageProtocol.S2C_REGISTER_SUCCESS: this.handleRegisterSuccess(); break;
            case MessageProtocol.S2C_ERROR: this.handleError(payload.message, payload.context); break;

            case MessageProtocol.S2C_SCENE_ENTERED: this.handleSceneEntered(payload.scene, payload.players, payload.exits); break;
            case MessageProtocol.S2C_INFO_MESSAGE: this.handleInfoMessage(payload); break;
            case MessageProtocol.S2C_SCENE_PLAYER_CHANGE: this.handleScenePlayerChange(payload); break;
            case MessageProtocol.S2C_PLAYER_HEALED: this.handlePlayerHealed(payload); break;
            case MessageProtocol.S2C_PLAYER_REVIVED: this.handlePlayerRevived(payload.player); break;
            case MessageProtocol.S2C_SCENE_RESET: this.handleSceneReset(payload.scene); break;
            case MessageProtocol.S2C_BATTLE_STARTED: this.handleBattleStarted(payload); break;
            case MessageProtocol.S2C_BATTLE_UPDATE: this.handleBattleUpdate(payload); break;
            case MessageProtocol.S2C_BATTLE_TURN_UPDATE: this.handleBattleUpdate(payload); break;
            case MessageProtocol.S2C_BATTLE_ENDED: this.handleBattleEnded(payload); break;
            case MessageProtocol.S2C_BATTLE_LOG: this.handleBattleLog(payload); break;
            case MessageProtocol.S2C_ATB_STATUS_UPDATE: this.handleAtbStatusUpdate(payload); break;
            case MessageProtocol.S2C_ITEM_DROPPED: this.handleItemDropped(payload); break;
            case MessageProtocol.S2C_ITEM_BOUND: this.handleItemBound(payload); break;
            case MessageProtocol.S2C_INVENTORY_DATA: 
                // 如果我们正在等待宝石镶嵌结果，并且刚刚收到了一个镶嵌成功的信息消息，
                // 那么这个背包更新很可能是由于宝石镶嵌导致的
                if (this.waitingForGemSocketingResult) {
                    this.waitingForGemSocketingResult = false;
                    
                    // 构建一个宝石镶嵌结果对象
                    if (this.lastGemSocketingInfo) {
                        const result = {
                            success: true,
                            message: this.lastGemSocketingMessage || '镶嵌成功！',
                            weapon_name: this.lastGemSocketingInfo.weaponName || '装备',
                            gem_name: this.lastGemSocketingInfo.gemName || '宝石',
                            effects: this.lastGemSocketingInfo.effects || {}
                        };
                        
                        // 显示宝石镶嵌结果
                        this.showGemSocketingResult(result);
                        
                        // 清除临时信息
                        this.lastGemSocketingInfo = null;
                        this.lastGemSocketingMessage = null;
                    }
                }
                
                this.handleInventoryData(payload); 
                break;
            case MessageProtocol.S2C_PLAYER_ATTRIBUTE_UPDATE: this.handlePlayerAttributeUpdate(payload); break;
            case MessageProtocol.S2C_MONSTER_DETAILS: this.handleMonsterDetails(payload); break;
            case MessageProtocol.S2C_PLAYER_DETAILS: this.handlePlayerDetails(payload); break;
            case MessageProtocol.S2C_COMBAT_POTION_CONFIG_DATA: this.handleCombatPotionConfigData(payload); break;
            case MessageProtocol.S2C_PLAYER_SKILLS_LIST: this.handlePlayerSkillsList(payload); break;
            case MessageProtocol.S2C_BATTLE_SKILLS_LIST: 
                    // 根据战斗类型，将技能列表发送给不同的管理器
                    if (payload.battle_type === 'pvp') {
                        this.pvpManager.handleBattleSkillsList(payload);
                    } else {
                        this.skillManager.handleBattleSkillsList(payload);
                    }
                    break;
            case MessageProtocol.S2C_BUILDING_DATA: 
                console.log('收到建筑数据，准备处理:', payload);
                this.buildingManager.handleBuildingData(payload); 
                break;
            case MessageProtocol.S2C_SHOP_NOTIFICATION:
                console.log('收到商店通知，准备处理:', payload);
                this.buildingManager.handleShopNotification(payload);
                break;
            case MessageProtocol.S2C_NPC_DIALOGUE:
                console.log('收到NPC对话数据，准备处理:', payload);
                this.npcManager.handleNpcDialogue(payload);
                break;
            case MessageProtocol.S2C_NPC_DETAIL:
                console.log('收到NPC详情数据，准备处理:', payload);
                this.npcManager.handleNpcDetail(payload);
                break;
            case MessageProtocol.S2C_SCENE_NPCS:
                console.log('收到场景NPC数据，准备处理:', payload);
                if (this.currentScene && payload.scene_id === this.currentScene.id) {
                    this.currentScene.npcs = payload.npcs;
                    this.updateSceneInfo();
                }
                break;
            case MessageProtocol.S2C_QUEST_LIST:
                console.log('收到任务列表数据，准备处理:', payload);
                if (this.questManager) {
                    this.questManager.handleQuestListData(payload);
                }
                break;
            case MessageProtocol.S2C_QUEST_UPDATE:
                console.log('收到任务更新数据，准备处理:', payload);
                if (this.questManager) {
                    this.questManager.handleQuestUpdateData(payload);
                }
                break;
            case MessageProtocol.S2C_RECIPES_DATA:
                this.buildingManager.handleRecipesData(payload);
                break;
            case MessageProtocol.S2C_CRAFT_RESULT:
                this.buildingManager.handleCraftResult(payload);
                break;
            case MessageProtocol.S2C_GEM_RECIPES_DATA:
                this.buildingManager.handleGemRecipesData(payload);
                break;
            case MessageProtocol.S2C_GEM_CRAFT_RESULT:
                this.buildingManager.handleGemCraftResult(payload);
                break;
            case MessageProtocol.S2C_REFINE_MATERIALS_DATA:
                this.buildingManager.handleRefineMaterialsData(payload);
                break;
            case MessageProtocol.S2C_REFINE_COST_ESTIMATE:
                this.buildingManager.handleRefineCostEstimate(payload);
                break;
            case MessageProtocol.S2C_REFINE_RESULT:
                this.buildingManager.handleRefineResult(payload);
                break;
            // 处理PVP相关消息
            case MessageProtocol.S2C_PVP_CHALLENGE:
                this.pvpManager.handlePvpChallenge(payload);
                break;
                
            case MessageProtocol.S2C_PVP_CHALLENGE_RESPONSE:
                this.addLog(payload.message);
                break;
                
            case MessageProtocol.S2C_PVP_BATTLE_STARTED:
                this.pvpManager.handlePvpBattleStarted(payload);
                break;
                
            case MessageProtocol.S2C_PVP_BATTLE_UPDATE:
                this.pvpManager.handlePvpBattleUpdate(payload);
                break;
                
            case MessageProtocol.S2C_PVP_ATB_STATUS_UPDATE:
                this.pvpManager.handleAtbStatusUpdate(payload);
                break;
                
            case MessageProtocol.S2C_PVP_BATTLE_ENDED:
                this.pvpManager.handlePvpBattleEnded(payload);
                break;
                
            case MessageProtocol.S2C_PVP_LEADERBOARD:
                this.pvpManager.handlePvpLeaderboard(payload);
                break;
                
            case MessageProtocol.S2C_PVP_STATS:
                this.pvpManager.handlePvpStats(payload);
                break;
                
            // 处理排行榜相关消息
            case MessageProtocol.S2C_LEVEL_RANKING:
                this.pvpManager.handleLevelRanking(payload);
                break;
                
            case MessageProtocol.S2C_HERO_RANKING:
                this.pvpManager.handleHeroRanking(payload);
                break;
                
            case MessageProtocol.S2C_VILLAIN_RANKING:
                this.pvpManager.handleVillainRanking(payload);
                break;
                
            case MessageProtocol.S2C_PUBLIC_CHAT_MESSAGE:
                this.handlePublicChatMessage(payload);
                break;
            case MessageProtocol.S2C_PRIVATE_CHAT_MESSAGE:
                this.handlePrivateChatMessage(payload);
                break;
            case MessageProtocol.S2C_PRIVATE_CHAT_HISTORY:
                this.handlePrivateChatHistory(payload);
                break;
            case MessageProtocol.S2C_CHAT_CONTACTS_LIST:
                this.handleChatContactsList(payload);
                break;
            case MessageProtocol.S2C_CHAT_CONTACT_UPDATED:
                this.handleChatContactUpdated(payload);
                break;
                
            case MessageProtocol.S2C_PUBLIC_CHAT_HISTORY:
                this.handlePublicChatHistory(payload);
                break;

            case MessageProtocol.S2C_SUCCESS:
                this.handleSuccessMessage(payload);
                break;

            // 处理交易相关消息
            case MessageProtocol.S2C_TRADE_REQUEST_RECEIVED:
                this.handleTradeRequestReceived(payload);
                break;
            case MessageProtocol.S2C_TRADE_STARTED:
                this.handleTradeStarted(payload);
                break;
            case MessageProtocol.S2C_TRADE_UPDATE:
                this.handleTradeUpdate(payload);
                break;
            case MessageProtocol.S2C_TRADE_CONFIRMED:
                this.handleTradeConfirmed(payload);
                break;
            case MessageProtocol.S2C_TRADE_FINAL_CONFIRMED:
                this.handleTradeFinalConfirmed(payload);
                break;
            case MessageProtocol.S2C_TRADE_COMPLETED:
                this.handleTradeCompleted(payload);
                break;
            case MessageProtocol.S2C_TRADE_CANCELLED:
                this.handleTradeCancelled(payload);
                break;
            case MessageProtocol.S2C_TRADE_ERROR:
                this.handleTradeError(payload);
                break;

            // 公告相关消息处理
            case MessageProtocol.S2C_LATEST_ANNOUNCEMENT:
                this.handleLatestAnnouncement(payload);
                break;
            case MessageProtocol.S2C_ANNOUNCEMENT_DETAIL:
                this.handleAnnouncementDetail(payload);
                break;
            case MessageProtocol.S2C_ANNOUNCEMENT_LIST:
                this.handleAnnouncementList(payload);
                break;
            case MessageProtocol.S2C_REDEEM_CODE_RESULT:
                this.handleRedemptionCodeResult(payload);
                break;
            case MessageProtocol.S2C_REFINE_LEADERBOARD:
                this.handleRefineLeaderboard(payload);
                break;

            default:
                console.warn(`未处理的操作码: 0x${opcodeHex} (${opcodeConstName})`);
        }
    }

    base64ToBytes(base64) {
        try {
        const binString = atob(base64);
            const bytes = Uint8Array.from(binString, (m) => m.codePointAt(0));
            console.log("Base64解码结果:", {
                originalLength: base64.length,
                bytesLength: bytes.length
            });
            return bytes;
        } catch (e) {
            console.error("Base64解码失败:", e);
            return null;
        }
    }
    
    handleLoginSuccess(player, sessionKey) {
        this.currentPlayer = this.normalizePlayer(player);
        this.currentPlayer.status = this.currentPlayer.attributes.hp > 0 ? 'alive' : 'dead';
        
        this.sessionKey = this.base64ToBytes(sessionKey);
        
        const sessionData = {
            playerId: this.currentPlayer.id,
            sessionKey: sessionKey 
        };
        localStorage.setItem('gameSession', JSON.stringify(sessionData));

        document.getElementById('authSection').style.display = 'none';
        document.getElementById('gameContent').style.display = 'flex';
        
        this.updatePlayerInfo();
        this.addLog(`欢迎回来, ${this.currentPlayer.username}!`);

        // 初始化技能管理器
        this.skillManager.initialize();

        // 获取最新公告
        this.requestLatestAnnouncement();
        
        // Unified event listener for scene interactions
        const sceneInfoEl = document.getElementById('sceneInfo');
        sceneInfoEl.addEventListener('click', (event) => {
            // 获取点击的元素或其最近的a标签祖先
            const target = event.target;
            const link = target.tagName === 'A' ? target : target.closest('a');
            if (!link) return;

            // Handle item toggle
            if (link.classList.contains('toggle-items')) {
                event.preventDefault();
                const itemsContainer = document.getElementById('sceneItems');
                const isCurrentlyExpanded = itemsContainer.dataset.isExpanded === 'true';
                itemsContainer.dataset.isExpanded = !isCurrentlyExpanded;
                this.updateSceneInfo();
                return;
            }

            // Handle monster toggle
            if (link.classList.contains('toggle-monsters')) {
                event.preventDefault();
                const monstersContainer = document.getElementById('sceneMonsters');
                const isCurrentlyExpanded = monstersContainer.dataset.isExpanded === 'true';
                monstersContainer.dataset.isExpanded = !isCurrentlyExpanded;
                this.updateSceneInfo();
                return;
            }

            // Handle player toggle
            if (link.classList.contains('toggle-players')) {
                event.preventDefault();
                const playersContainer = document.getElementById('scenePlayers');
                const isCurrentlyExpanded = playersContainer.dataset.isExpanded === 'true';
                playersContainer.dataset.isExpanded = !isCurrentlyExpanded;
                this.updateSceneInfo();
                return;
            }

            // Handle item pickup
            if (link.classList.contains('pickup-item')) {
                event.preventDefault();
                const itemId = link.dataset.itemId;
                if (itemId) {
                    this.pickupItem(itemId);
                }
                return;
            }
        });

        if (this.currentPlayer.attributes.current_scene_id) {
            this.sendMessage(MessageProtocol.C2S_ENTER_SCENE, { scene_id: this.currentPlayer.attributes.current_scene_id });
        }
        
        this.startAutoSceneResetTimer(); // 启动自动重置计时器
    }
    
    handleRegisterSuccess() {
        document.getElementById('registerError').textContent = '';
        document.getElementById('registerSuccess').textContent = '注册成功！请登录。';
        showLogin();
    }

    handleError(message, context = null) {
        // Handle specific contextual errors first
        // 处理公聊错误 - 在公聊界面显示
        if (context === 'get_public_chat') {
            const publicChatContainer = document.getElementById('public-chat-messages');
            if (publicChatContainer) {
                publicChatContainer.innerHTML = '';
                const messageElement = document.createElement('div');
                messageElement.className = 'chat-message system error';
                messageElement.textContent = message;
                messageElement.style.color = '#ff4444';
                messageElement.style.fontWeight = 'bold';
                messageElement.style.padding = '8px';
                messageElement.style.backgroundColor = 'rgba(255, 0, 0, 0.1)';
                messageElement.style.borderRadius = '4px';
                messageElement.style.margin = '10px 0';
                publicChatContainer.appendChild(messageElement);
                return; // 错误已在公聊界面显示，完成处理
            }
        }
        
        // 处理私聊错误 - 在私聊界面显示
        if (context === 'private_chat') {
            const privateChatView = document.getElementById('private-chat-view');
            if (privateChatView && privateChatView.style.display === 'block') {
                this.showPrivateChatError(message);
                return; // 错误已在私聊界面显示，完成处理
            }
        }

        // 处理NPC对话错误 - 在NPC界面显示
        if (context === 'dialogue') {
            if (this.npcManager && this.npcManager.currentNpc) {
                this.npcManager.showNpcDetailMessage(message, 'error');
                return; // 错误已在NPC界面显示，完成处理
            }
        }
        
        if (context === 'item_equip_fail') {
            this.isAwaitingEquipResponse = false; // Reset the flag on failure

            // 使用新的消息函数显示装备失败消息
            this.showActionMessage(message, 'error');
            return;
        }
        
        // 处理凝练失败错误 - 在凝练界面显示
        if (context === 'refine_item_fail') {
            // 如果当前在凝练界面，显示错误消息
            if (this.buildingManager && 
                this.buildingManager.currentBuilding && 
                this.buildingManager.currentBuilding.type === 'REFINE' && 
                document.getElementById('buildingView').style.display === 'block') {
                
                // 使用BuildingManager的显示方法
                this.buildingManager.showRefineNotification(message, 'error');
                
                // 重置凝练按钮状态
                const refineBtn = document.querySelector('.refine-action .btn');
                if (refineBtn) {
                    refineBtn.disabled = false;
                    refineBtn.textContent = '开始凝练';
                }
                
                // 展开凝练说明以确保错误消息可见
                if (this.buildingManager) {
                    this.buildingManager.expandRefineInstruction();
                }
                
                return; // 错误已在凝练界面显示，完成处理
            }
            // 如果不在凝练界面，添加到游戏日志
            this.addLog(`❌ ${message}`);
            return;
        }

        // 处理仓库操作错误 - 在仓库界面显示
        if (this.buildingManager &&
            this.buildingManager.currentBuilding &&
            this.buildingManager.currentBuilding.type === 'WAREHOUSE' &&
            document.getElementById('buildingView').style.display === 'block') {

            // 在仓库界面显示错误消息
            this.buildingManager.showWarehouseMessage(message, 'error');
            return; // 错误已在仓库界面显示，完成处理
        }

        // 处理药水配置错误 - 只在配置界面显示，不写入日志
        if (context === 'config_error') {
            const configView = document.getElementById('configView');
            const configError = document.getElementById('configError');
            const configSuccess = document.getElementById('configSuccess');
            
            if (configView && configError && configView.style.display === 'block') {
                // 清除正在配置的提示
                if (configSuccess) {
                    configSuccess.textContent = '';
                    configSuccess.style.padding = '0';
                    configSuccess.style.borderColor = 'transparent';
                }
                
                // 显示错误消息
                configError.textContent = message;
                configError.style.borderColor = 'var(--error-color)';
                configError.style.padding = '5px';
                
                // 3秒后自动清除错误提示
                setTimeout(() => this.clearMessageElement('configError'), 3000);
                return; // 错误已在上下文中显示，完成处理
            }
            return; // 即使不在配置界面也不写入日志
        }

        // If an error occurred during socketing, restore the UI to the item detail view.
        if (this.socketingContext) {
            const weaponId = this.socketingContext.weaponInventoryId;
            const weapon = this.inventory.equipped.find(i => i.inventory_id === weaponId) || this.inventory.backpack.find(i => i.inventory_id === weaponId);
            if (weapon) {
                this.showItemDetails(weapon, this.itemDetailOrigin);
            } else {
                // Fallback if the weapon somehow disappeared
                this.hideItemDetailView(); 
            }
            this.socketingContext = null;
             document.getElementById('gemSocketingView').style.display = 'none';
        }

        // Fallback to generic error handling
        const authSection = document.getElementById('authSection');
        if (authSection && (authSection.style.display === 'block' || authSection.style.display === '')) { // Login/Register error
            document.getElementById('loginError').textContent = message;
            document.getElementById('registerError').textContent = message;
        } else { // Generic in-game error
            const allocatorError = document.getElementById('allocatorError');
            if (allocatorError && allocatorError.offsetParent !== null) {
                allocatorError.textContent = message;
            } else {
            this.addLog(`❌ ${message}`);
            }
        }
    }
    
    // 在物品详情页面显示消息
    showItemDetailMessage(message, type = 'success') {
        const itemDetailView = document.getElementById('itemDetailView');
        
        // 确保只在物品详情页面可见时显示消息
        if (itemDetailView && itemDetailView.style.display === 'block') {
            const messageElement = type === 'error' ? 
            document.getElementById('itemDetailError') : 
            document.getElementById('itemDetailSuccess');
            
            //获取物品数量
            const itemQuantity = document.getElementById('itemDetailQuantity');
            //如果为空，则记录到日志
            if (!itemQuantity) {
                if (type === 'error') {
                    this.addLog(`❌ ${message}`);
                } else {
                    this.addLog(message);
                }
            }

            if (messageElement) {
                messageElement.textContent = message;
                messageElement.style.borderColor = type === 'error' ? 'var(--error-color)' : '#00662a';
                messageElement.style.padding = '5px';
                
                // 不再自动清除消息，让它保持显示
                // setTimeout(() => {
                //     messageElement.textContent = '';
                //     messageElement.style.padding = '0';
                //     messageElement.style.borderColor = 'transparent';
                // }, 3000);
            }
            
        } else {
            // 如果物品详情页面不可见，则使用普通日志
            if (type === 'error') {
                this.addLog(`❌ ${message}`);
            } else {
                this.addLog(message);
            }
        }
    }
    
    handleInfoMessage(payload) {
        const message = payload.message;
        const type = payload.type || 'info';
        const context = payload.context || null;
        
        // 处理战斗意图更新消息 - 这种消息没有message属性，需要先处理
        if (context === 'battle_intention_update') {
            if (this.battleState) {
                this.battleState.currentIntention = payload.intention;
                this.updateBattleIntentionDisplay();
            }
            return; // 不在日志中显示这类消息
        }
        
        // 处理对话预加载消息（已废弃，服务器端已集成对话检查）
        if (type === 'dialogue_preload') {
            console.log('收到对话预加载响应（已废弃）:', payload);
            return; // 这是内部消息，不显示在日志中
        }
        
        // 检查message是否存在
        if (!message) {
            console.warn('收到的INFO消息没有message属性:', payload);
            return;
        }
        
        // 检查是否是宝石镶嵌相关的消息
        if (message.includes('镶嵌成功') || message.includes('镶嵌失败')) {
            // 如果我们正在等待宝石镶嵌结果，保存这个消息
            if (this.socketingContext) {
                this.lastGemSocketingMessage = message;
                // 不要在这里显示日志，等待背包更新后一起处理
                    return;
                }
        }
        
        // 处理物品使用相关消息 - 在物品界面显示
        if (context === 'item_use_success') {
            this.showActionMessage(message, 'success');
            return; // 不重复添加到游戏日志
        }

        if (context === 'item_use_fail') {
            this.showActionMessage(message, 'error');
            return; // 不重复添加到游戏日志
        }

        // 处理技能学习相关消息 - 在物品界面显示
        if (context === 'skill_learned') {
            this.showActionMessage(message, 'success');
            return; // 不重复添加到游戏日志
        }

        // 处理技能学习失败消息 - 在物品界面显示
        if (context === 'skill_learn_fail') {
            this.showActionMessage(message, 'error');
            return; // 不重复添加到游戏日志
        }

        // 处理所有类型的信息消息
        this.addLog(message);

        // 检查是否是特殊消息
        if (message.includes('你已经死亡')) {
            this.currentPlayer.status = 'dead';
            this.updatePlayerInfo();
            document.getElementById('reviveBtn').style.display = 'inline-block';
        } else if (message.includes('你已经复活')) {
            this.currentPlayer.status = 'alive';
            this.updatePlayerInfo();
            document.getElementById('reviveBtn').style.display = 'none';
            
            // 触发复活事件，让其他系统可以响应
            const reviveEvent = new CustomEvent('playerRevived', { detail: { player: this.currentPlayer } });
            document.dispatchEvent(reviveEvent);
        }
    }
    
    // 处理公聊消息
    handlePublicChatMessage(payload) {
        const { playerId, playerName, message, sentAt } = payload;
        
        // 显示聊天通知
        this.showChatNotification(playerName, message);
        
        // 更新聊天界面（如果打开的话）
        const chatContainer = document.getElementById('public-chat-messages');
        if (!chatContainer) return;
        
        // 检查并移除"暂无聊天记录"的提示
        const emptyMessages = chatContainer.querySelectorAll('.chat-message.system');
        emptyMessages.forEach(el => {
            if (el.textContent === '暂无聊天记录') {
                chatContainer.removeChild(el);
            }
        });
        
        const messageElement = document.createElement('div');
        messageElement.className = 'chat-message';
        
        const timestamp = new Date(sentAt).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        messageElement.innerHTML = `
            <div class="chat-time">${timestamp}</div>
            <div class="chat-name-line">
                <span class="chat-name clickable" data-player-id="${playerId}">${playerName}</span>:
                <span class="chat-content">${message}</span>
            </div>
        `;

        // 添加点击玩家名称查看详情功能
        const playerNameElement = messageElement.querySelector('.chat-name');
        playerNameElement.addEventListener('click', () => {
            this.showPlayerDetails(playerId);
        });

        chatContainer.appendChild(messageElement);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // 创建消息队列
    chatNotificationQueue = [];
    isProcessingChatQueue = false;
    
    // 显示聊天通知
    showChatNotification(playerName, message, type = 'public') {
        // 如果当前在对应的聊天界面，则不显示通知
        const chatView = document.getElementById('chat-view');
        const privateChatView = document.getElementById('private-chat-view');

        if (type === 'public' && chatView && chatView.style.display === 'block') return;
        if (type === 'private' && privateChatView && privateChatView.style.display === 'block') return;

        // 将消息添加到队列
        this.chatNotificationQueue.push({ playerName, message, type });
        
        // 如果队列长度超过3条，只保留第一条和最后两条
        if (this.chatNotificationQueue.length > 3) {
            // 保留第一条（正在显示的）
            const currentMessage = this.chatNotificationQueue[0];
            // 保留最后两条（最新的）
            const latestMessages = this.chatNotificationQueue.slice(-2);
            // 重置队列
            this.chatNotificationQueue = [currentMessage, ...latestMessages];
        }
        
        // 如果队列没有在处理中，开始处理队列
        if (!this.isProcessingChatQueue) {
            this.processChatNotificationQueue();
        }
    }
    
    // 处理聊天通知队列
    processChatNotificationQueue() {
        if (this.chatNotificationQueue.length === 0) {
            this.isProcessingChatQueue = false;
            return;
        }
        
        this.isProcessingChatQueue = true;
        
        // 获取队列中的第一条消息
        const { playerName, message, type = 'public' } = this.chatNotificationQueue.shift();
        
        // 获取connectionStatus元素
        const connectionStatus = document.getElementById('connectionStatus');
        if (!connectionStatus) {
            this.processChatNotificationQueue();
            return;
        }
        
        // 保存原始的连接状态文本，以便稍后恢复
        if (!connectionStatus.getAttribute('data-original-text')) {
            connectionStatus.setAttribute('data-original-text', connectionStatus.textContent);
        }
        
        // 更新connectionStatus的内容
        const typePrefix = type === 'private' ? '[私聊]' : '';
        const bubbleClass = type === 'private' ? 'chat-notification-bubble private' : 'chat-notification-bubble';

        connectionStatus.innerHTML = `
            <div class="${bubbleClass}">
                <span class="chat-notification-prefix">${typePrefix}</span>
                <span class="chat-notification-name">${playerName}:</span>
                <span class="chat-notification-content">${message}</span>
            </div>
        `;
        
        // 添加样式（如果还没添加）
        if (!document.getElementById('chat-notification-style')) {
            const style = document.createElement('style');
            style.id = 'chat-notification-style';
            style.textContent = `
                .connection-status {
                    position: relative;
                    height: 22px;
                    overflow: visible;
                    padding: 2px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                }
                
                .chat-notification-bubble {
                    background-color: #003366;
                    color: white;
                    border-radius: 4px;
                    padding: 2px 8px;
                    text-align: left;
                    font-size: 12px;
                    max-width: 100%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
                    animation: fadeInOut 5s ease-in-out;
                    height: 18px;
                    line-height: 18px;
                    width: 100%;
                }
                
                .chat-notification-name {
                    font-weight: bold;
                    color: #ffcc00;
                    margin-right: 3px;
                }
                
                .chat-notification-content {
                    color: white;
                }
                
                @keyframes fadeInOut {
                    0% { opacity: 0; }
                    10% { opacity: 1; }
                    90% { opacity: 1; }
                    100% { opacity: 0; }
                }
            `;
            document.head.appendChild(style);
        }
        
        // 添加点击事件，根据消息类型进入对应的聊天界面
        const notificationBubble = connectionStatus.querySelector('.chat-notification-bubble');
        if (notificationBubble) {
            notificationBubble.style.cursor = 'pointer';
            notificationBubble.addEventListener('click', () => {
                if (type === 'private') {
                    // 私聊消息，进入私聊联系人列表
                    this.showPrivateChatView();
                } else {
                    // 公聊消息，进入公聊界面
                    this.showChatView();
                }
            });
        }
        
        // 固定显示3秒后，处理下一条消息或恢复原始连接状态
        setTimeout(() => {
            if (this.chatNotificationQueue.length > 0) {
                // 如果队列中还有消息，继续处理下一条
                this.processChatNotificationQueue();
            } else {
                // 队列为空，恢复原始连接状态
                const originalText = connectionStatus.getAttribute('data-original-text');
                if (originalText) {
                    connectionStatus.textContent = originalText;
                }
                this.isProcessingChatQueue = false;
            }
        }, 3000);
    }
    
    // 隐藏聊天通知
    hideNotification() {
        // 清空消息队列
        this.chatNotificationQueue = [];
        this.isProcessingChatQueue = false;
        
        // 恢复连接状态
        const connectionStatus = document.getElementById('connectionStatus');
        if (connectionStatus) {
            const originalText = connectionStatus.getAttribute('data-original-text');
            if (originalText) {
                connectionStatus.textContent = originalText;
            }
        }
    }
    
    // 处理公聊历史消息
    handlePublicChatHistory(payload) {
        const chatContainer = document.getElementById('public-chat-messages');
        if (!chatContainer) return;
        
        chatContainer.innerHTML = ''; // 清空现有消息
        
        // 检查是否有错误消息
        if (payload.error) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message system error';
            messageElement.textContent = payload.error;
            chatContainer.appendChild(messageElement);
            return;
        }
        
        const { messages } = payload;
        if (!messages || !messages.length) {
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message system';
            messageElement.textContent = '暂无聊天记录';
            chatContainer.appendChild(messageElement);
            return;
        }
        
        messages.forEach(msg => {
            const { playerId, playerName, message, sentAt } = msg;
            const messageElement = document.createElement('div');
            messageElement.className = 'chat-message';

            const timestamp = new Date(sentAt).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
            messageElement.innerHTML = `
                <div class="chat-time">${timestamp}</div>
                <div class="chat-name-line">
                    <span class="chat-name clickable" data-player-id="${playerId}">${playerName}</span>:
                    <span class="chat-content">${message}</span>
                </div>
            `;

            // 添加点击玩家名称查看详情功能
            const playerNameElement = messageElement.querySelector('.chat-name');
            if (playerNameElement) {
                playerNameElement.addEventListener('click', () => {
                    this.showPlayerDetails(playerId);
                });
            }

            chatContainer.appendChild(messageElement);
        });
        
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }
    
    // 显示聊天界面
    showChatView() {
        this.hideAllViews();
        
        // 隐藏所有通知
        this.hideNotification();
        
        const chatView = document.getElementById('chat-view');
        if (!chatView) {
            const chatView = document.createElement('div');
            chatView.id = 'chat-view';
            chatView.className = 'view';
            
            chatView.innerHTML = `
                <div class="view-header">
                    <h2>公共聊天</h2>
                </div>
                <div id="public-chat-messages" class="chat-messages"></div>
                <div class="chat-input-area" id="public-chat-input-area">
                    <input type="text" id="chat-input" placeholder="输入聊天内容..." maxlength="50">
                    <span id="chat-char-count">0/50</span>
                    <button id="send-chat-btn">发送</button>
                </div>
                <div class="chat-rate-limit" id="public-chat-rate-limit" style="display: none;">
                    <div style="text-align: center; padding: 8px; background: rgba(255, 107, 107, 0.2); color: #ff6b6b; font-weight: bold; border: 1px solid #ff6b6b; border-radius: 4px;">
                        <small>⚠️ 发送太频繁！每分钟最多发送` + publicChatRateLimit + `条消息</small>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 10px;">
                    <button class="btn-primary" id="close-chat-btn">[返回]</button>
                </div>
            `;
            
            // 将聊天界面添加到游戏内容区域
            document.getElementById('gameContent').appendChild(chatView);
            
            // 添加内联样式解决聊天内容溢出问题
            const style = document.createElement('style');
            style.textContent = `
                .chat-messages {
                    max-height: 300px;
                    overflow-y: auto;
                    border: 1px solid var(--border-color);
                    padding: 5px;
                    margin-bottom: 10px;
                }
                .chat-message {
                    margin-bottom: 5px;
                    word-wrap: break-word;
                    overflow-wrap: break-word;
                }
                .chat-content {
                    word-break: break-all;
                    display: inline-block;
                }
                .chat-input-area {
                    display: flex;
                    margin-bottom: 10px;
                }
                #chat-input {
                    flex-grow: 1;
                    margin-right: 5px;
                }
                #chat-char-count {
                    margin: 0 5px;
                    align-self: center;
                }
            `;
            document.head.appendChild(style);
            
            // 绑定事件
            document.getElementById('close-chat-btn').addEventListener('click', () => {
                this.hideChatView();
            });
            
            document.getElementById('send-chat-btn').addEventListener('click', () => {
                this.sendChatMessage();
            });
            
            const chatInput = document.getElementById('chat-input');
            const charCount = document.getElementById('chat-char-count');
            
            chatInput.addEventListener('input', () => {
                const length = chatInput.value.length;
                charCount.textContent = `${length}/50`;
                
                // 当超过45个字符时，改变计数器颜色提醒用户
                if (length > 45) {
                    charCount.style.color = '#ff6b6b';
                } else {
                    charCount.style.color = '';
                }
            });
            
            chatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.sendChatMessage();
                }
            });
            
            // 获取聊天历史
            this.sendMessage(MessageProtocol.C2S_GET_PUBLIC_CHAT);
        } else {
            chatView.style.display = 'block';
        }
    }
    
    // 隐藏聊天界面
    hideChatView() {
        const chatView = document.getElementById('chat-view');
        if (chatView) {
            chatView.style.display = 'none';
        }
        
        // 隐藏通知
        this.hideNotification();
        
        // 显示场景视图
        this.updateSceneInfo();
        const sceneView = document.getElementById('sceneView');
        if (sceneView) {
            sceneView.style.display = 'block';
        }
        
        // 显示主视图
        const mainView = document.getElementById('main-view');
        if (mainView) {
            mainView.style.display = 'block';
        }
    }
    
    // 发送聊天消息
    sendChatMessage() {
        const inputElement = document.getElementById('chat-input');
        if (!inputElement) return;

        const message = inputElement.value.trim();
        if (!message) return;

        // 检查发送频率限制
        const currentMinute = Math.floor(Date.now() / 60000);
        if (currentMinute === this.publicChatLastMinute) {
            if (this.publicChatMessageCount >= publicChatRateLimit) {
                this.showPublicChatRateLimit();
                return;
            }
            this.publicChatMessageCount++;
        } else {
            this.publicChatLastMinute = currentMinute;
            this.publicChatMessageCount = 1;
        }

        // 检查消息长度
        if (message.length > 50) {
            // 显示错误提示
            const chatContainer = document.getElementById('public-chat-messages');
            if (chatContainer) {
                const errorElement = document.createElement('div');
                errorElement.className = 'chat-message system error';
                errorElement.textContent = '消息长度不能超过50个字符';
                chatContainer.appendChild(errorElement);
                chatContainer.scrollTop = chatContainer.scrollHeight;

                // 3秒后自动移除错误提示
                setTimeout(() => {
                    if (errorElement.parentNode === chatContainer) {
                        chatContainer.removeChild(errorElement);
                    }
                }, 3000);
            }
            return;
        }
        
        this.sendMessage(MessageProtocol.C2S_SEND_PUBLIC_CHAT, {
            message: message
        });
        
        inputElement.value = '';
        
        // 重置字符计数
        const charCount = document.getElementById('chat-char-count');
        if (charCount) {
            charCount.textContent = '0/50';
            charCount.style.color = '';
        }
    }

    // 显示公聊频率限制提示
    showPublicChatRateLimit() {
        console.log('=== 显示公聊频率限制提示 ===');
        const inputArea = document.getElementById('public-chat-input-area');
        const rateLimitArea = document.getElementById('public-chat-rate-limit');

        if (inputArea && rateLimitArea) {
            // 隐藏输入区域，显示频率限制提示
            inputArea.style.display = 'none';
            rateLimitArea.style.display = 'block';

            // 添加抖动动画效果
            rateLimitArea.style.animation = 'shake 0.5s ease-in-out';

            // 3秒后恢复输入区域
            setTimeout(() => {
                inputArea.style.display = 'flex';
                rateLimitArea.style.display = 'none';
                rateLimitArea.style.animation = '';
            }, 3000);
        }
    }
    
    // 更新物品详情页面的数量显示
    updateItemDetailQuantity() {
        const itemDetailView = document.getElementById('itemDetailView');
        if (!itemDetailView || itemDetailView.style.display !== 'block' || !this.currentDetailItem) {
            return;
        }
        
        // 保存当前的消息提示状态
        const successElement = document.getElementById('itemDetailSuccess');
        const errorElement = document.getElementById('itemDetailError');
        let successMessage = '';
        let errorMessage = '';
        let successBorderColor = '';
        let errorBorderColor = '';
        let successPadding = '';
        let errorPadding = '';
        
        if (successElement) {
            successMessage = successElement.textContent;
            successBorderColor = successElement.style.borderColor;
            successPadding = successElement.style.padding;
        }
        
        if (errorElement) {
            errorMessage = errorElement.textContent;
            errorBorderColor = errorElement.style.borderColor;
            errorPadding = errorElement.style.padding;
        }
        
        // 如果是可堆叠物品，减少数量
        if (this.currentDetailItem.stackable == 1 && this.currentDetailItem.quantity > 1) {
            this.currentDetailItem.quantity -= 1;
            
            // 更新数量显示
            const itemNameElement = document.getElementById('itemDetailName');
            let quantitySpan = itemNameElement.querySelector('.item-quantity');
            
            if (!quantitySpan) {
                quantitySpan = document.createElement('span');
                quantitySpan.className = 'item-quantity';
                quantitySpan.style.fontSize = '14px';
                quantitySpan.style.color = '#666';
                itemNameElement.appendChild(quantitySpan);
            }
            
            quantitySpan.textContent = ` (x${this.currentDetailItem.quantity})`;
            
            // 如果数量减少到1，下次使用后物品将消失，可以添加提示
            if (this.currentDetailItem.quantity === 1) {
                const actionButtons = document.getElementById('itemDetailActions').querySelectorAll('button');
                actionButtons.forEach(button => {
                    if (button.textContent.includes('使用') || button.textContent.includes('学习')) {
                        button.title = '这是最后一个物品';
                    }
                });
            }
        } else if (this.currentDetailItem.stackable == 1 && this.currentDetailItem.quantity === 1) {
            const actionButtons = document.getElementById('itemDetailActions').querySelectorAll('button');
            actionButtons.forEach(button => {
                if (button.textContent.includes('使用') || button.textContent.includes('学习')) {
                    button.title = '这是最后一个物品';
                }
            });
            // 如果是最后一个物品，不再自动关闭详情页面
            // 让用户自己决定何时关闭页面
            // setTimeout(() => this.hideItemDetailView(), 3000);
        }
        
        // 恢复消息提示状态
        if (successElement && successMessage) {
            successElement.textContent = successMessage;
            successElement.style.borderColor = successBorderColor;
            successElement.style.padding = successPadding;
        }
        
        if (errorElement && errorMessage) {
            errorElement.textContent = errorMessage;
            errorElement.style.borderColor = errorBorderColor;
            errorElement.style.padding = errorPadding;
        }
    }
    
    clearMessageElement(elementId) {
        const element = document.getElementById(elementId);
        if (element) {
            element.textContent = '';
            element.style.padding = '0';
            element.style.borderColor = 'transparent';
        }
    }

    clearMessages(elementId = null) {
        const elementsToClear = [];
        if (elementId) {
            elementsToClear.push(document.getElementById(elementId));
        } else {
           elementsToClear.push(
                document.getElementById('inventoryError'), document.getElementById('itemDetailError'),
                document.getElementById('inventorySuccess'), document.getElementById('itemDetailSuccess'),
                document.getElementById('configError'), document.getElementById('configSuccess')
           );
        }
        
        elementsToClear.forEach(div => {
            if(div) {
                div.textContent = '';
                div.style.borderColor = 'transparent';
                div.style.padding = '0';
            }
        });
    }
    
    updatePlayerInfo() {
        if (!this.currentPlayer || !this.currentPlayer.attributes) return;
        document.getElementById('playerName').textContent = `${this.currentPlayer.username} (Lv.${this.currentPlayer.attributes.level || 1})`;
        document.getElementById('playerHP').innerHTML = this.generateStatBarHtml(this.currentPlayer.attributes.hp, this.currentPlayer.attributes.max_hp, 'hp');
        document.getElementById('playerMP').innerHTML = this.generateStatBarHtml(this.currentPlayer.attributes.mp, this.currentPlayer.attributes.max_mp, 'mp');
        
        const reviveButton = document.getElementById('reviveBtn');
        if (reviveButton) {
            reviveButton.style.display = this.currentPlayer.status === 'dead' ? 'inline-block' : 'none';
        }
    }
    
    // 添加伤害效果（抖动和伤害数字飘出）
    addDamageEffect(targetId, damageAmount, playerId = null) {
        const targetElement = document.getElementById(targetId);
        if (!targetElement) return;

        // 添加抖动效果
        targetElement.classList.add('shake-animation');
        setTimeout(() => {
            targetElement.classList.remove('shake-animation');
        }, 1000);

        // 获取或创建独立的伤害效果容器
        let effectContainer = this.getOrCreateEffectContainer();

        // 创建伤害数字元素
        const damageNumber = document.createElement('div');
        damageNumber.className = 'damage-number';
        damageNumber.textContent = `-${damageAmount}`;

        // 获取目标元素的绝对位置（相对于视口）
        const targetRect = targetElement.getBoundingClientRect();

        // 由于效果容器是 position: fixed，直接使用视口坐标
        // 随机偏移（在目标元素范围内）
        const randomX = targetRect.left + Math.floor(Math.random() * Math.max(targetRect.width - 80, 20) + 10);
        const randomY = targetRect.top + Math.floor(Math.random() * Math.max(targetRect.height - 20, 10) + 5);

        damageNumber.style.position = 'absolute';
        damageNumber.style.left = `${randomX}px`;
        damageNumber.style.top = `${randomY}px`;

        // 添加到独立的效果容器
        effectContainer.appendChild(damageNumber);

        // 移除元素（动画结束后）
        setTimeout(() => {
            if (damageNumber.parentNode === effectContainer) {
                effectContainer.removeChild(damageNumber);
            }
        }, 3500); // 与CSS动画时间匹配
    }
    
    // 获取或创建独立的伤害效果容器
    getOrCreateEffectContainer() {
        let effectContainer = document.getElementById('battle-effect-container');
        if (!effectContainer) {
            effectContainer = document.createElement('div');
            effectContainer.id = 'battle-effect-container';
            effectContainer.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                pointer-events: none;
                z-index: 1000;
            `;
            document.body.appendChild(effectContainer);
            console.log('创建独立的伤害效果容器');
        }
        return effectContainer;
    }

    // 添加治疗效果（脉冲和治疗数字飘出）
    addHealEffect(targetId, healAmount, type = 'hp', playerId = null) {
        const targetElement = document.getElementById(targetId);
        if (!targetElement) return;

        // 添加脉冲效果
        const animationClass = type === 'hp' ? 'heal-animation' : 'mana-animation';
        targetElement.classList.add(animationClass);
        setTimeout(() => {
            targetElement.classList.remove(animationClass);
        }, 2500);

        // 获取或创建独立的伤害效果容器
        let effectContainer = this.getOrCreateEffectContainer();

        // 创建治疗数字元素
        const healNumber = document.createElement('div');
        healNumber.className = type === 'hp' ? 'heal-number' : 'mana-number';
        healNumber.textContent = `+${healAmount}`;

        // 获取目标元素的绝对位置（相对于视口）
        const targetRect = targetElement.getBoundingClientRect();

        // 由于效果容器是 position: fixed，直接使用视口坐标
        // 随机偏移（在目标元素范围内）
        const randomX = targetRect.left + Math.floor(Math.random() * Math.max(targetRect.width - 80, 20) + 10);
        const randomY = targetRect.top + Math.floor(Math.random() * Math.max(targetRect.height - 20, 10) + 5);

        healNumber.style.position = 'absolute';
        healNumber.style.left = `${randomX}px`;
        healNumber.style.top = `${randomY}px`;

        // 添加到独立的效果容器
        effectContainer.appendChild(healNumber);

        // 移除元素（动画结束后）
        setTimeout(() => {
            if (healNumber.parentNode === effectContainer) {
                effectContainer.removeChild(healNumber);
            }
        }, 3500); // 与CSS动画时间匹配
    }

    // 添加闪避效果
    addDodgeEffect(targetId, playerId = null) {
        const targetElement = document.getElementById(targetId);
        if (!targetElement) {
            console.log(`闪避效果：找不到目标元素 ${targetId}`);
            return;
        }

        // 获取或创建独立的伤害效果容器
        let effectContainer = this.getOrCreateEffectContainer();

        // 创建闪避文字元素
        const dodgeText = document.createElement('div');
        dodgeText.className = 'dodge-text';
        dodgeText.textContent = '闪';

        // 获取目标元素的绝对位置（相对于视口）
        const targetRect = targetElement.getBoundingClientRect();

        // 由于效果容器是 position: fixed，直接使用视口坐标
        // 居中显示（在目标元素中央）
        const centerX = targetRect.left + (targetRect.width / 2) - 25; // 减去文字宽度的一半（50px/2）
        const centerY = targetRect.top + (targetRect.height / 2) - 25; // 减去文字高度的一半（50px/2）

        dodgeText.style.position = 'absolute';
        dodgeText.style.left = `${centerX}px`;
        dodgeText.style.top = `${centerY}px`;

        console.log(`闪避效果位置: ${centerX}, ${centerY} (目标: ${targetId})`);

        // 添加到独立的效果容器
        effectContainer.appendChild(dodgeText);

        // 移除元素（动画结束后）
        setTimeout(() => {
            if (dodgeText.parentNode === effectContainer) {
                effectContainer.removeChild(dodgeText);
            }
        }, 2000); // 2秒后移除
    }

    // 清理所有伤害效果的辅助函数
    clearAllDamageEffects() {
        // 清理独立效果容器中的所有效果
        const effectContainer = document.getElementById('battle-effect-container');
        if (effectContainer) {
            const allEffects = effectContainer.querySelectorAll('.damage-number, .heal-number, .mana-number, .dodge-text');
            allEffects.forEach(effect => {
                if (effect.parentNode) {
                    effect.parentNode.removeChild(effect);
                }
            });
            console.log(`已清理独立容器中的 ${allEffects.length} 个战斗效果`);
        }

        // 也清理可能残留在战斗界面中的旧效果
        const battleView = document.getElementById('battleView');
        if (battleView) {
            const oldEffects = battleView.querySelectorAll('.damage-number, .heal-number, .mana-number, .dodge-text');
            oldEffects.forEach(effect => {
                if (effect.parentNode) {
                    effect.parentNode.removeChild(effect);
                }
            });
            if (oldEffects.length > 0) {
                console.log(`已清理战斗界面中的 ${oldEffects.length} 个旧战斗效果`);
            }
        }
    }

    // 测试战斗效果的函数（用于调试）
    testBattleEffects() {
        console.log('测试战斗效果...');

        // 测试怪物效果
        const monsterElement = document.getElementById('battleMonsterInfo');
        if (monsterElement) {
            console.log('找到怪物元素，添加测试效果');
            // 伤害效果
            this.addDamageEffect('battleMonsterInfo', 123);
            // 治疗效果
            setTimeout(() => {
                this.addHealEffect('battleMonsterInfo', 50, 'hp');
            }, 1000);
            // 闪避效果
            setTimeout(() => {
                this.addDodgeEffect('battleMonsterInfo');
            }, 2000);
        } else {
            console.log('未找到怪物元素');
        }

        // 测试玩家效果
        const playerElements = document.querySelectorAll('[id^="player-info-"]');
        if (playerElements.length > 0) {
            const firstPlayer = playerElements[0];
            const playerId = firstPlayer.id.replace('player-info-', '');
            console.log(`找到玩家元素，添加测试效果: ${firstPlayer.id}`);
            // 伤害效果
            this.addDamageEffect(firstPlayer.id, 456, playerId);
            // 魔法恢复效果
            setTimeout(() => {
                this.addHealEffect(firstPlayer.id, 30, 'mp', playerId);
            }, 1500);
            // 闪避效果
            setTimeout(() => {
                this.addDodgeEffect(firstPlayer.id, playerId);
            }, 3000);
        } else {
            console.log('未找到玩家元素');
        }
    }
    
    // 添加属性点分配效果
    addAttributePointEffect(attribute, amount) {
        console.log(`addAttributePointEffect 被调用: ${attribute}, +${amount}`);

        const targetElement = document.getElementById(`self-view-${attribute}`);
        console.log(`目标元素 self-view-${attribute}:`, targetElement);

        if (!targetElement) {
            console.log(`未找到目标元素: self-view-${attribute}`);
            return;
        }

        // The target is a <td>, which should be fine. We make it relative for positioning context.
        targetElement.style.position = 'relative';

        const effectNumber = document.createElement('div');
        // Reuse the 'heal-number' class as it likely has the desired floating text effect.
        effectNumber.className = 'heal-number';
        effectNumber.textContent = `+${amount}`;

        // Override position to be consistent, not random.
        // Let's place it near the center-right of the cell.
        effectNumber.style.position = 'absolute';
        effectNumber.style.right = '15px';
        effectNumber.style.top = '50%';
        effectNumber.style.transform = 'translateY(-50%)';

        console.log('创建漂浮数字元素:', effectNumber);
        targetElement.appendChild(effectNumber);
        console.log('漂浮数字元素已添加到目标元素');

        // The animation is likely defined in CSS and has a duration.
        // 5000ms is used for other effects, so it's a safe bet.
        setTimeout(() => {
            if (effectNumber.parentNode === targetElement) {
                targetElement.removeChild(effectNumber);
            }
            // Reset position style on parent if we are the last effect
            if (targetElement.querySelectorAll('.heal-number').length === 0) {
                 targetElement.style.position = '';
            }
        }, 5000);
    }
    
    updateSceneInfo() {
        if (!this.currentScene) return;

        // 更新场景名称，并在后面添加地图按钮
        const sceneNameEl = document.getElementById('sceneName');
        sceneNameEl.innerHTML = `你正在 ${this.currentScene.display_name || this.currentScene.name} <button class="btn" onclick="game.showMap()">[地图]</button>`;

        document.getElementById('sceneDescription').textContent = this.currentScene.description || '';

        // Players
        const playersEl = document.getElementById('scenePlayers');
        playersEl.innerHTML = ''; // Clear previous content
        const otherPlayers = this.players.filter(p => p.id !== this.currentPlayer.id);

        // Ensure expanded state is preserved across re-renders
        if (playersEl.dataset.isExpanded === undefined) {
            playersEl.dataset.isExpanded = 'false';
        }
        const isPlayersExpanded = playersEl.dataset.isExpanded === 'true';

        if (otherPlayers.length > 0) {
            playersEl.style.display = 'block';
            
            let html = '<b style="vertical-align: middle;">你遇到:</b> ';
            const displayPlayers = (isPlayersExpanded || otherPlayers.length <= 3) ? otherPlayers : otherPlayers.slice(0, 3);
            
            html += displayPlayers.map(p => {
                let displayName;
                let playerStyle = '';
                
                if (p.status === 'dead' || p.attributes.hp <= 0) {
                    // 保持死亡玩家的灰色样式
                    displayName = `${p.username}的灵魂`;
                    playerStyle = 'color: #6c8095; font-style: italic;';
                } else {
                    displayName = `${this.getHpStatusPrefix(p.attributes.hp, p.attributes.max_hp)}${p.username}`;
                    
                    // 根据善恶值设置颜色
                    const karma = p.attributes.karma || 0;
                    if (karma < 0) {
                        // 负数善恶值，根据绝对值大小设置红色到黑红色
                        const absKarma = Math.abs(karma);
                        if (absKarma >= 50) {
                            // 黑红色 - 善恶值 <= -50
                            playerStyle = 'color: #660000; font-weight: bold;';
                        } else if (absKarma >= 30) {
                            // 深红色 - 善恶值在 -30 到 -49 之间
                            playerStyle = 'color: #990000; font-weight: bold;';
                        } else if (absKarma >= 10) {
                            // 鲜红色 - 善恶值在 -10 到 -29 之间
                            playerStyle = 'color: #cc0000;';
                        } else {
                            // 浅红色 - 善恶值在 -1 到 -9 之间
                            playerStyle = 'color: #ff3333;';
                        }
                    } else {
                        // 保持善良玩家的绿色
                        playerStyle = 'color: #005a00;';
                    }
                }
                
                const jobName = p.attributes.job_name || '无业';
                displayName += ` (${jobName})`;
                
                const playerLink = `<a href="#" onclick="event.preventDefault(); game.showPlayerDetails(${p.id})" style="${playerStyle} text-decoration: none;">${displayName}</a>`;
                return `<span class="scene-player-group" style="display: inline-block; white-space: nowrap; margin: 0 4px; vertical-align: middle;">${playerLink}</span>`;
            }).join('、');

            if (otherPlayers.length > 3) {
                if (isPlayersExpanded) {
                    html += ` <a href="#" class="toggle-players" style="margin: 0 4px; vertical-align: middle;">[收起]</a>`;
                } else {
                    html += ` ... <a href="#" class="toggle-players" style="margin: 0 4px; vertical-align: middle;">[更多 (${otherPlayers.length})]</a>`;
                }
            }

            playersEl.innerHTML = html;
        } else {
            playersEl.style.display = 'none';
        }
        
        // NPCs - 使用NPC管理器更新场景中的NPC
        if (this.npcManager) {
            this.npcManager.updateSceneNpcs();
        } else {
            // 如果NPC管理器尚未初始化，创建NPC容器元素
            const sceneInfoEl = document.getElementById('sceneInfo');
            let npcsEl = document.getElementById('sceneNpcs');
            if (!npcsEl) {
                npcsEl = document.createElement('div');
                npcsEl.id = 'sceneNpcs';
                sceneInfoEl.appendChild(npcsEl);
                npcsEl.style.display = 'none';
            }
        }

        // Monsters
        const monstersEl = document.getElementById('sceneMonsters');
        monstersEl.innerHTML = ''; // Clear previous content
        const livingMonsters = Object.values(this.currentScene.monsters || {}).filter(m => m.hp > 0);
        
        // Ensure expanded state is preserved across re-renders
        // 如果dataset.isExpanded未设置，默认为false（收起状态）
        const isMonstersExpanded = monstersEl.dataset.isExpanded === 'true';
        // 确保dataset.isExpanded始终有值
        if (monstersEl.dataset.isExpanded === undefined) {
            monstersEl.dataset.isExpanded = 'false';
        } 

        if (livingMonsters.length > 0) {
            monstersEl.style.display = 'block';
            
            let html = '<b style="vertical-align: middle;">你遇到:</b> ';
            const displayMonsters = (isMonstersExpanded || livingMonsters.length <= 3) ? livingMonsters : livingMonsters.slice(0, 3);
            
            const canAttack = this.currentPlayer && this.currentPlayer.status !== 'dead';

            html += displayMonsters.map(m => {
                const displayName = `<a href="#" onclick="event.preventDefault(); game.showMonsterDetails('${m.id}')" style="color: #a02c2c; text-decoration: none;">${m.name}</a>`;
                const attackButton = canAttack ? ` <button class="btn" onclick="game.sendMessage(MessageProtocol.C2S_START_BATTLE, { monster_id: '${m.id}' })">[攻击]</button>` : '';
                return `<span class="scene-monster-group" style="display: inline-block; white-space: nowrap; margin: 0 4px; vertical-align: middle;">${displayName}${attackButton}</span>`;
            }).join('、');
    
            if (livingMonsters.length > 3) {
                if (isMonstersExpanded) {
                    html += ` <a href="#" class="toggle-monsters" style="margin: 0 4px; vertical-align: middle;">[收起]</a>`;
                } else {
                    html += ` ... <a href="#" class="toggle-monsters" style="margin: 0 4px; vertical-align: middle;">[更多 (${livingMonsters.length})]</a>`;
                }
            }
    
            monstersEl.innerHTML = html;
        } else {
            monstersEl.style.display = 'none';
        }
        
        // Items on the ground
        const itemsEl = document.getElementById('sceneItems');
        // Ensure expanded state is preserved across re-renders
        // 如果dataset.isExpanded未设置，默认为false（收起状态）
        const isItemsExpanded = itemsEl.dataset.isExpanded === 'true'; 
        // 确保dataset.isExpanded始终有值
        if (itemsEl.dataset.isExpanded === undefined) {
            itemsEl.dataset.isExpanded = 'false';
        }
        const items = this.currentScene.items || [];
        itemsEl.innerHTML = ''; // Clear previous content
    
        if (items.length > 0) {
            itemsEl.style.display = 'block';
            
            let html = '<b style="vertical-align: middle;">地上有：</b>';
            const displayItems = (isItemsExpanded || items.length <= 3) ? items : items.slice(0, 3);
            
            html += displayItems.map(item => {
                let displayName = item.display_name || item.name;
                if (item.quantity > 1) {
                    displayName += ` (x${item.quantity})`;
                }
                
                let itemStyle = '';
                
                // 根据保护状态设置样式
                if (item.is_protected) {
                    if (item.is_protected_for_you) {
                        // 为当前玩家保护的物品 - 绿色
                        itemStyle = 'color: #00662a; font-weight: bold;';
                    } else {
                        // 为其他玩家保护的物品 - 灰色删除线
                        itemStyle = 'color: #6c8095; text-decoration: line-through;';
                    }
                }
                
                return `<a href="#" class="pickup-item" data-item-id="${item.id}" style="margin: 0 4px; vertical-align: middle; ${itemStyle}">${displayName}</a>`;
            }).join('、');
    
            if (items.length > 3) {
                if (isItemsExpanded) {
                    html += ` <a href="#" class="toggle-items" style="margin: 0 4px; vertical-align: middle;">[收起]</a>`;
                } else {
                    html += ` ... <a href="#" class="toggle-items" style="margin: 0 4px; vertical-align: middle;">[更多 (${items.length})]</a>`;
                }
            }
    
            itemsEl.innerHTML = html;
        } else {
            itemsEl.style.display = 'none';
        }
        
        // 使用BuildingManager更新建筑信息
        this.buildingManager.updateSceneBuildings(this.currentScene);
    }

    handleSceneEntered(scene, players, exits) {
        document.getElementById('main-view').style.display = 'block';
        document.getElementById('battleView').style.display = 'none';
        document.getElementById('buildingView').style.display = 'none';
        document.getElementById('dialogueView').style.display = 'none';

        // Update our discovered scenes map with the current scene and its neighbors
        this.scenes.set(scene.id, scene);
        for (const dir in exits) {
            const exitScene = exits[dir];
            if (exitScene) {
                this.scenes.set(exitScene.id, exitScene);
            }
        }

        // Assign exits to the scene object from the payload (for currentExits)
        scene.exits = exits;

        // Now we can safely work with the 'scene' object. Ensure coordinates are numbers.
        scene.x = Number(scene.x);
        scene.y = Number(scene.y);

        // 确保保留物品的保护状态信息
        if (scene.items) {
            scene.items = scene.items.map(item => {
                // 确保保护状态标记被保留
                return {
                    ...item,
                    is_protected: item.is_protected || false,
                    is_protected_for_you: item.is_protected_for_you || false
                };
            });
        }
        
        // 设置当前场景
        this.currentScene = scene;
        
        // 处理场景中的NPC数据 - 在设置currentScene后更新NPC
        if (this.npcManager) {
            this.npcManager.updateSceneNpcs();
        }
        this.players = players.map(p => {
            const player = this.normalizePlayer(p);
            if (!player) return null;
            player.status = (player.attributes.hp || 0) > 0 ? 'alive' : 'dead';
            return player;
        }).filter(Boolean);
        this.currentExits = exits;

        this.updateSceneInfo();
        this.updateMovementUI();
        // this.addLog(`已进入场景: ${this.currentScene.name}`);

        // 检查场景中是否有存活的怪物
        let isAnyMonsterAlive = false;
        if (this.currentScene.monsters && Object.keys(this.currentScene.monsters).length > 0) {
            for (const monsterId in this.currentScene.monsters) {
                const monster = this.currentScene.monsters[monsterId];
                if (monster && monster.hp > 0) {
                    isAnyMonsterAlive = true;
                    break;
                }
            }
        }

        // 如果没有存活的怪物，则发出重置指令
        if (!isAnyMonsterAlive) {
            console.log(`场景 ${this.currentScene.name} 已清空，发送重置指令。`);
            this.sendMessage(MessageProtocol.C2S_RESET_SCENE, { scene_id: this.currentScene.id });
        }

        // 移除旧的事件监听器代码，因为我们现在使用统一的事件监听器
    }

    handlePlayerHealed(payload) {
        if (payload.player_id === this.currentPlayer.id) {
            this.currentPlayer.attributes.hp = payload.current_hp;
            this.addLog(`你恢复了生命值, 当前HP: ${payload.current_hp}。`);
        this.updatePlayerInfo();
        }
        const healedPlayer = this.players.find(p => p.id === payload.player_id);
        if (healedPlayer) {
            healedPlayer.attributes.hp = payload.current_hp;
            this.updateSceneInfo();
        }
    }

    handleSceneReset(scene) {
        if(this.currentScene && this.currentScene.id === scene.id) {
            // 保存旧的场景名称，以防新场景数据中名称丢失
            const prevSceneName = this.currentScene.name;
            
            // 更新当前场景数据
            this.currentScene = {...this.currentScene, ...scene};
            
            // 如果新场景数据中没有名称，使用旧名称
            if (!this.currentScene.name && prevSceneName) {
                this.currentScene.name = prevSceneName;
            }
            
            this.updateSceneInfo();
            
            // 显示场景重置日志，确保场景名称显示
            const displayName = this.currentScene.name || this.currentScene.id;
            this.addLog(`场景 [${displayName}] 已重置！`);
        }
    }

    handleScenePlayerChange(payload) {
        console.log('收到场景玩家变更消息:', payload);
        
        if (this.currentScene && payload.scene_id === this.currentScene.id) {
            this.players = payload.current_players.map(p => {
                const player = this.normalizePlayer(p);
                if (!player) return null;
                player.status = (player.attributes.hp || 0) > 0 ? 'alive' : 'dead';
                return player;
            }).filter(Boolean);
            
            if (payload.current_monsters) {
                this.currentScene.monsters = payload.current_monsters;
            }
            if (payload.current_items) {
                // 保留物品的保护状态信息
                this.currentScene.items = payload.current_items;
            }
            if (payload.current_npcs) {
                // 更新场景中的NPC，但保留现有的任务信息
                const existingNpcs = this.currentScene.npcs || [];
                const updatedNpcs = payload.current_npcs.map(newNpc => {
                    // 查找现有的NPC数据
                    const existingNpc = existingNpcs.find(npc => npc.id === newNpc.id);
                    if (existingNpc) {
                        // 保留现有的任务信息
                        return {
                            ...newNpc,
                            has_quests_for_player: existingNpc.has_quests_for_player
                        };
                    }
                    return newNpc;
                });
                this.currentScene.npcs = updatedNpcs;
            }
            
            // 只有在不处于战斗状态时，才从场景数据更新玩家状态
            // 这可以防止战斗中的血条被场景数据覆盖导致闪烁
            const selfInList = this.players.find(p => p.id === this.currentPlayer.id);
            if (selfInList && !this.battleState) {
                Object.assign(this.currentPlayer, selfInList);
                this.updatePlayerInfo();
            }
            
            // 添加玩家变更日志
            if (payload.action === 'leave' && payload.player_id) {
                const leavingPlayer = this.players.find(p => p.id === payload.player_id);
                if (leavingPlayer) {
                    this.addLog(`玩家 ${leavingPlayer.username} 离开了场景。`);
                }
            }
            
            this.updateSceneInfo();
        }
    }
    
    handlePlayerRevived(player) {
        const revivedPlayerFromServer = this.normalizePlayer(player);
        if (!revivedPlayerFromServer) return;

        if (this.currentPlayer.id === revivedPlayerFromServer.id) {
            this.currentPlayer = revivedPlayerFromServer;
            this.currentPlayer.status = 'alive';
            // this.addLog('你已经复活了！');
            this.updatePlayerInfo();
            
            // 触发玩家复活事件，以便其他组件可以响应
            const revivedEvent = new CustomEvent('player-revived', { 
                detail: { player: revivedPlayerFromServer } 
            });
            document.dispatchEvent(revivedEvent);
        }
        const revivedPlayerInList = this.players.find(p => p.id === revivedPlayerFromServer.id);
        if(revivedPlayerInList) {
            Object.assign(revivedPlayerInList, { ...revivedPlayerFromServer, status: 'alive' });
            this.updateSceneInfo();
        }
    }

    addLog(message) {
        const logEl = document.getElementById('log');
        const p = document.createElement('p');
        p.style.margin = '2px 0';
        p.innerHTML = `<span style="color: #666;">[${new Date().toLocaleTimeString()}]</span> ${message}`;
        logEl.insertBefore(p, logEl.firstChild);
        if (logEl.children.length > 100) {
            logEl.removeChild(logEl.lastChild);
        }
    }

    revivePlayer() {
        if (this.currentPlayer && this.currentPlayer.status === 'dead') {
            this.sendMessage(MessageProtocol.C2S_REVIVE);
        }
    }

    reviveCity() {
        this.sendMessage(MessageProtocol.C2S_REVIVE_CITY);
        // 关闭地图界面
        this.hideMap();
    }

    refreshScene() {
        if (this.currentScene && this.currentScene.id) {
            const sceneName = this.currentScene.name || this.currentScene.id;
            this.addLog(`正在刷新场景 [${sceneName}] 信息...`);
            this.sendMessage(MessageProtocol.C2S_ENTER_SCENE, { scene_id: this.currentScene.id });
        }
    }

    handleBattleStarted(payload) {
        console.log('收到战斗开始消息:', payload);

        try {
            document.getElementById('main-view').style.display = 'none';
            document.getElementById('battleView').style.display = 'block';

            // 清理所有旧的伤害效果
            this.clearAllDamageEffects();

        const intentionDisplay = document.getElementById('battleIntentionDisplay');
        if (intentionDisplay) {
            intentionDisplay.style.display = 'block';
        }

        // 清空战斗日志和按钮
        this.battleLogCache.clear();
        const battleLog = document.getElementById('battleLogContainer');
        battleLog.innerHTML = '';
        
        // 确保战斗日志区域有正确的ID
        const battleLogParent = battleLog.parentElement;
        if (battleLogParent) {
            battleLogParent.id = 'battleLogSection';
            
            // 确保有标题元素
            let battleLogTitle = battleLogParent.querySelector('h5') || battleLogParent.querySelector('h4');
            if (!battleLogTitle) {
                battleLogTitle = document.createElement('h5');
                battleLogTitle.textContent = '战斗日志';
                battleLogParent.insertBefore(battleLogTitle, battleLog);
            }
        }
        
        // 移除可能存在的展开/收起按钮
        const existingButton = document.getElementById('battleLogToggleBtn');
        if (existingButton) {
            existingButton.remove();
        }
        
        // 重置战斗日志展开状态为收起
        this.battleLogExpanded = false;
        this.updateBattleLogDisplay();
        
        const battleActionsContainer = document.getElementById('battleActionsContainer');
        battleActionsContainer.innerHTML = '';

        // 添加默认的战斗按钮
        battleActionsContainer.innerHTML += `
            <button class="btn" onclick="game.sendBattleAction('attack')">[攻击]</button>
            <button class="btn" onclick="game.sendBattleAction('use_hp_potion')">[回血]</button>
            <button class="btn" onclick="game.sendBattleAction('use_mp_potion')">[回蓝]</button>
            <button class="btn" onclick="game.sendBattleAction('flee')">[逃跑]</button>
        `;
        
        if (payload.log && Array.isArray(payload.log)) {
            payload.log.forEach(msg => this.addBattleLog(battleLog, msg));
        } else {
            this.addBattleLog(battleLog, '战斗开始！');
        }

        console.log('当前玩家ID:', this.currentPlayer.id);
        console.log('战斗中的玩家:', payload.all_players);

        const me = payload.all_players.find(p => p.id === this.currentPlayer.id);

        if (!me) {
            console.error('在战斗玩家列表中找不到当前玩家！');
            console.error('当前玩家ID:', this.currentPlayer.id);
            console.error('战斗玩家列表:', payload.all_players);
            this.addLog('战斗开始失败：找不到玩家信息');
            return;
        }

        this.battleState = {
            player: this.normalizePlayer(me),
            monster: payload.monster,
            all_players: payload.all_players.map(p => this.normalizePlayer(p)),
            playerATBs: payload.atb_status ? payload.atb_status.playerATBs : {},
            monsterATB: payload.atb_status ? payload.atb_status.monsterATB : 0,
            roundTimeRemaining: payload.atb_status ? payload.atb_status.roundTimeRemaining : 3000,
            atbMax: payload.atb_status ? payload.atb_status.atbMax : 500,
            isOver: false,
            currentIntention: 'attack',
            skillName: '', // 初始化技能名称
            active_effects: payload.active_effects || [] // 添加状态效果
        };

        if (payload.skills) {
            this.handleBattleSkillsList(payload);
        }
        
        this.updateBattleView(this.battleState.monster, this.battleState.all_players);
        this.updateBattleIntentionDisplay();

            // 初始化常规按钮状态
            if (this.skillManager) {
                this.skillManager.updateBattleActionButtons(this.battleState, this.currentPlayer.id);
            }

            console.log('战斗界面初始化完成');

        } catch (error) {
            console.error('处理战斗开始消息时出错:', error);
            this.addLog('战斗开始失败：' + error.message);

            // 发生错误时，确保回到主界面
            document.getElementById('battleView').style.display = 'none';
            document.getElementById('main-view').style.display = 'block';
        }
    }
    
    handleBattleUpdate(payload) {
        const normalizedPlayers = payload.all_players.map(p => this.normalizePlayer(p));
        
        // 保存旧的状态，用于比较变化
        const oldMonsterHp = this.battleState?.monster?.attributes?.hp;
        const oldPlayersHp = {};
        const oldPlayersMp = {};
        
        if (this.battleState && this.battleState.all_players) {
            this.battleState.all_players.forEach(p => {
                oldPlayersHp[p.id] = p.attributes.hp;
                oldPlayersMp[p.id] = p.attributes.mp;
            });
        }
        
        // 更新战斗状态对象，确保客户端状态与服务器同步
        if (this.battleState) {
            this.battleState.monster = payload.monster;
            this.battleState.all_players = normalizedPlayers;

            // 更新技能冷却状态（从PVP系统移植）
            if (payload.skill_cooldowns) {
                console.log('GameClient.handleBattleUpdate - 收到技能冷却数据:', payload.skill_cooldowns);
                this.battleState.skill_cooldowns = payload.skill_cooldowns;
            } else {
                console.log('GameClient.handleBattleUpdate - 没有收到技能冷却数据');
            }

            // 更新ATB状态
            if (payload.atb_status) {
                this.battleState.playerATBs = payload.atb_status.playerATBs;
                this.battleState.monsterATB = payload.atb_status.monsterATB;
                this.battleState.roundTimeRemaining = payload.atb_status.roundTimeRemaining;
                this.battleState.atbMax = payload.atb_status.atbMax; // 更新动态ATB最大值
            }

            // 更新状态效果
            if (payload.active_effects) {
                this.battleState.active_effects = payload.active_effects;
            }
            
            // 更新当前玩家的战斗中血量
            const me = normalizedPlayers.find(p => p.id === this.currentPlayer.id);
            if (me) {
                this.battleState.player = me;
                // 只在战斗中更新玩家血量，不调用updatePlayerInfo()
                this.currentPlayer.attributes.hp = me.attributes.hp;
                
                // 检查玩家是否刚刚被击败
                if (me.attributes.hp <= 0 && !document.getElementById('playerDefeatedActions')) {
                    // 更新战斗视图，这将显示"离开战场"按钮
                    this.updateBattleView(this.battleState.monster, this.battleState.all_players);
                }
            }
        }
        
        // 先更新视图
        this.updateBattleView(payload.monster, normalizedPlayers);

        // 延迟添加伤害和治疗效果（在界面更新后）
        setTimeout(() => {
            // 1. 检查怪物血量变化
            const newMonsterHp = payload.monster.attributes.hp;
            if (oldMonsterHp !== undefined && newMonsterHp < oldMonsterHp) {
                // 怪物受到伤害
                const damage = oldMonsterHp - newMonsterHp;
                this.addDamageEffect('battleMonsterInfo', damage);
            } else if (oldMonsterHp !== undefined && newMonsterHp > oldMonsterHp) {
                // 怪物恢复生命
                const heal = newMonsterHp - oldMonsterHp;
                this.addHealEffect('battleMonsterInfo', heal, 'hp');
            }

            // 2. 检查所有玩家的血量和魔法变化
            normalizedPlayers.forEach(player => {
                const playerId = player.id;
                const newHp = player.attributes.hp;
                const newMp = player.attributes.mp;

                // 检查血量变化
                if (oldPlayersHp[playerId] !== undefined) {
                    if (newHp < oldPlayersHp[playerId]) {
                        // 玩家受到伤害
                        const damage = oldPlayersHp[playerId] - newHp;
                        this.addDamageEffect(`player-info-${playerId}`, damage, playerId);
                    } else if (newHp > oldPlayersHp[playerId]) {
                        // 玩家恢复生命
                        const heal = newHp - oldPlayersHp[playerId];
                        this.addHealEffect(`player-info-${playerId}`, heal, 'hp', playerId);
                    }
                }

                // 检查魔法变化
                if (oldPlayersMp[playerId] !== undefined) {
                    if (newMp > oldPlayersMp[playerId]) {
                        // 玩家恢复魔法
                        const mana = newMp - oldPlayersMp[playerId];
                        this.addHealEffect(`player-info-${playerId}`, mana, 'mp', playerId);
                    }
                }
            });
        }, 50); // 延迟50毫秒，确保DOM更新完成

        // 更新技能按钮状态（应用PVP风格的状态管理）
        if (this.skillManager) {
            console.log('GameClient.handleBattleUpdate - 调用技能状态更新');
            this.skillManager.updatePveSkillButtonsState();

            // 更新常规按钮状态（攻击、逃跑、使用道具等）
            this.skillManager.updateBattleActionButtons(this.battleState, this.currentPlayer.id);

            // 更新状态效果显示
            if (this.battleState.active_effects) {
                this.skillManager.updateBattleEffectsDisplay(this.battleState.active_effects, this.currentPlayer.id);
            }
        } else {
            console.log('GameClient.handleBattleUpdate - skillManager 不存在');
        }
        
        // 只有当有日志消息时才添加到战斗日志
        if (payload.log && payload.log.length > 0) {
            const battleLog = document.getElementById('battleLogContainer');
            payload.log.forEach(msg => this.addBattleLog(battleLog, msg));
        }
    }

    handleBattleEnded(payload) {
        console.log('战斗结束:', payload.reason);
        
        // 立即清理战斗状态
        this.battleState = null;
        
        const battleLog = document.getElementById('battleLogContainer');
        if (payload.log && Array.isArray(payload.log)) {
            payload.log.forEach(msg => this.addBattleLog(battleLog, msg));
        }

        // 添加一个战斗结束标识符
        this.addBattleLog(battleLog, `战斗结束标识符: ${Date.now()}`);
        
        // 隐藏"正在准备"提示信息
        const intentionDisplay = document.getElementById('battleIntentionDisplay');
        if (intentionDisplay) {
            intentionDisplay.style.display = 'none';
        }
        
        // Update the view one last time with the final state
        const finalState = payload.final_state;
        if (finalState) {
            // 当玩家逃离战斗时，确保玩家列表不为空，这样才能显示逃跑图标
            let playersToShow = [];
            if (payload.reason === 'flee') {
                // 逃离战斗时，确保当前玩家在列表中以显示逃跑图标
                const playerCopy = JSON.parse(JSON.stringify(this.currentPlayer));
                playersToShow = [playerCopy];
            } else {
                // 其他情况下，只显示战斗中的玩家
                playersToShow = finalState.players.map(p => this.normalizePlayer(p));
            }
            
            this.updateBattleView(finalState.monster, playersToShow, true, payload.reason);
            
            // 更新当前玩家状态
            const self = finalState.players.find(p => p.id === this.currentPlayer.id);
            if (self) {
                this.currentPlayer = this.normalizePlayer(self);
            }
            
            // 不要更新this.players，因为这会包含场景中的所有玩家
            // 只在返回场景时再更新
            
            if (finalState.scene) {
                this.currentScene = finalState.scene;
            }
            
            this.updatePlayerInfo();
        }
        
        // 如果是逃跑成功，显示逃跑提示
        // 注意：实际的提示显示会在updateBattleView中处理，这里不需要额外操作
        
        // 重置战斗日志展开状态为收起
        this.battleLogExpanded = false;
        
        // 添加战斗日志展开/收起按钮
        this.addBattleLogToggleButton();
        
        // 在战斗完全结束后，清空战斗日志缓存，为下一场战斗做准备
        setTimeout(() => {
            this.battleLogCache.clear();
        }, 1000);
    }
    
    // 添加战斗日志展开/收起按钮
    addBattleLogToggleButton() {
        const battleLogSection = document.getElementById('battleLogSection');
        if (!battleLogSection) {
            console.error('找不到战斗日志区域元素');
            return;
        }
        
        // 移除可能已存在的按钮
        const existingButton = document.getElementById('battleLogToggleBtn');
        if (existingButton) {
            existingButton.remove();
        }
        
        // 创建新按钮
        const toggleButton = document.createElement('button');
        toggleButton.id = 'battleLogToggleBtn';
        toggleButton.className = 'btn'; // 使用与其他战斗按钮相同的类
        toggleButton.textContent = this.battleLogExpanded ? '[收起战斗日志]' : '[展开战斗日志]';
        toggleButton.style.marginLeft = '10px';
        toggleButton.style.verticalAlign = 'middle';
        toggleButton.style.fontSize = '14px'; // 调整字体大小与其他按钮一致
        toggleButton.onclick = () => this.toggleBattleLog();
        
        // 找到战斗日志的标题元素并添加按钮
        const battleLogTitle = battleLogSection.querySelector('h5') || battleLogSection.querySelector('h4');
        if (battleLogTitle) {
            battleLogTitle.appendChild(toggleButton);
        } else {
            // 如果找不到标题元素，直接添加到容器顶部
            battleLogSection.insertBefore(toggleButton, battleLogSection.firstChild);
        }
        
        // 设置初始状态
        this.updateBattleLogDisplay();
        
        console.log('战斗日志展开/收起按钮已添加');
    }
    
    // 切换战斗日志展开/收起状态
    toggleBattleLog() {
        this.battleLogExpanded = !this.battleLogExpanded;
        this.updateBattleLogDisplay();
        
        // 更新按钮文本
        const toggleButton = document.getElementById('battleLogToggleBtn');
        if (toggleButton) {
            toggleButton.textContent = this.battleLogExpanded ? '[收起战斗日志]' : '[展开战斗日志]';
        }
    }
    
    // 更新战斗日志显示
    updateBattleLogDisplay() {
        const battleLogContainer = document.getElementById('battleLogContainer');
        if (!battleLogContainer) return;
        
        if (this.battleLogExpanded) {
            battleLogContainer.style.maxHeight = '1000px'; // 展开时高度更大（原来的两倍）
            battleLogContainer.style.transition = 'max-height 0.3s ease-in-out';
        } else {
            battleLogContainer.style.maxHeight = '150px'; // 收起时恢复默认高度
            battleLogContainer.style.transition = 'max-height 0.3s ease-in-out';
        }
    }

    handleBattleLog(payload) {
        // 处理服务器发送的战斗日志消息
        if (payload && payload.log && Array.isArray(payload.log)) {
            const battleLog = document.getElementById('battleLogContainer');
            if (battleLog) {
                payload.log.forEach(msg => this.addBattleLog(battleLog, msg));
            }
        }
    }

    updateBattleView(monster, players, isBattleOver = false, reason = '') {
        const me = players.find(p => p.id == this.currentPlayer.id);
        if (me) {
            this.currentPlayer.attributes.hp = me.attributes.hp;
        }
        
        const monsterHp = monster.attributes.hp;
        const monsterMaxHp = monster.attributes.max_hp;
        const atbMax = this.battleState ? this.battleState.atbMax : 500;
        
        let monsterATB = 0;
        if (this.battleState && typeof this.battleState.monsterATB === 'number') {
            monsterATB = this.battleState.monsterATB;
        }
        
        let monsterResultIcon = '';
        if (isBattleOver) {
            if (reason === 'victory') {
                monsterResultIcon = '<div class="battle-result-icon defeat-icon">💀</div>';
            } else if (reason === 'defeated') {
                monsterResultIcon = '<div class="battle-result-icon victory-icon">🏆</div>';
            }
        }
        
        // 更新怪物信息，不保存伤害效果（让它们自然消失）
        const battleMonsterInfo = document.getElementById('battleMonsterInfo');

        // 更新基本信息
        battleMonsterInfo.innerHTML = `
            <p style="margin-bottom:2px;"><b>${monster.username || monster.name}</b></p>
            ${this.generateStatBarHtml(monsterHp, monsterMaxHp, 'hp', 'monster-hp-bar')}
            ${this.generateATBBarHtml(monsterATB, atbMax, 'monster-atb-bar')}
            ${monsterResultIcon}
        `;
        
        let timerHtml = '';
        if (this.battleState && !isBattleOver) {
            const remainingTime = Math.max(0, this.battleState.roundTimeRemaining / 1000).toFixed(1);
            timerHtml = `<div class="round-timer">回合倒计时: ${remainingTime}s</div>`;
        }
        
        let playersHtml = '';
        if (players && players.length > 0) {
            playersHtml = '';
            
            const me = players.find(p => p.id === this.currentPlayer.id);
            const otherPlayers = players.filter(p => p.id !== this.currentPlayer.id);
            const sortedPlayers = me ? [me, ...otherPlayers] : players;
            
            sortedPlayers.forEach(player => {
                const playerHp = player.attributes.hp;
                const playerMaxHp = player.attributes.max_hp;
                const playerMp = player.attributes.mp || 0;
                const playerMaxMp = player.attributes.max_mp || 0;
                const isCurrentPlayer = player.id === this.currentPlayer.id;
                const isDead = playerHp <= 0;
                
                // 根据玩家状态调整名称显示样式
                let playerNameStyle = '';
                if (isDead) {
                    playerNameStyle = 'color: #6c8095; font-style: italic;'; // 死亡玩家使用灰色斜体
                }
                
                const playerName = isCurrentPlayer ? `【我】 ${player.username}` : player.username;
                const playerId = player.id;
                
                let playerATB = 0;
                if (this.battleState && this.battleState.playerATBs && player && player.id) {
                    playerATB = this.battleState.playerATBs[player.id] || 0;
                }
                
                let playerResultIcon = '';
                // 玩家已经死亡，无论战斗是否结束都显示骷髅头
                if (playerHp <= 0) {
                    playerResultIcon = '<div class="battle-result-icon defeat-icon">💀</div>';
                } else if (isBattleOver) {
                    // 战斗结束时的其他图标
                    if (reason === 'victory') {
                        playerResultIcon = '<div class="battle-result-icon victory-icon">🏆</div>';
                    } else if (reason === 'flee' && isCurrentPlayer) {
                        playerResultIcon = '<div class="battle-result-icon flee-icon">🏳️</div>';
                    }
                }
                
                playersHtml += `
                    <div class="battle-player-info" style="margin-bottom: 10px;" id="player-info-${playerId}">
                        <p style="margin-bottom:2px; ${playerNameStyle}">${playerName}</p>
                        ${this.generateStatBarHtml(playerHp, playerMaxHp, 'hp', `player-hp-bar-${playerId}`)}
                        ${playerMaxMp > 0 ? this.generateStatBarHtml(playerMp, playerMaxMp, 'mp', `player-mp-bar-${playerId}`) : ''}
                        ${this.generateATBBarHtml(playerATB, atbMax, `player-atb-bar-${playerId}`)}
                        ${playerResultIcon}
                    </div>
                `;
            });
        }
        
        // 简化玩家信息更新，不保存伤害效果（因为玩家伤害效果较少且短暂）
        const battlePlayerInfo = document.getElementById('battlePlayerInfo');
        battlePlayerInfo.innerHTML = playersHtml;
        
        if (timerHtml) {
            const timerContainer = document.createElement('div');
            timerContainer.className = 'battle-round-timer';
            timerContainer.innerHTML = timerHtml;
            const battleMonsterInfo = document.getElementById('battleMonsterInfo');
            const battleIntentionDisplay = document.getElementById('battleIntentionDisplay');
            if (document.querySelector('.battle-round-timer')) {
                document.querySelector('.battle-round-timer').remove();
            }
            battleMonsterInfo.parentNode.insertBefore(timerContainer, battleIntentionDisplay);
        }
        
        const isPlayerAlive = me && me.attributes.hp > 0;

        if (isBattleOver || (this.battleState && this.battleState.isOver)) {
            document.getElementById('battleActionsContainer').style.display = 'none';
            if (document.querySelector('.battle-round-timer')) {
                document.querySelector('.battle-round-timer').remove();
            }
            let battleResultDiv = document.getElementById('battleResultActions');
            if (!battleResultDiv) {
                battleResultDiv = document.createElement('div');
                battleResultDiv.id = 'battleResultActions';
                battleResultDiv.style.marginTop = '15px';
                battleResultDiv.style.textAlign = 'center';
                battleResultDiv.style.padding = '10px';
                battleResultDiv.style.border = '1px dashed var(--border-color)';
                if (reason === 'victory') {
                    battleResultDiv.style.backgroundColor = 'rgba(40, 167, 69, 0.05)';
                } else if (reason === 'defeated' || !isPlayerAlive) {
                    battleResultDiv.style.backgroundColor = 'rgba(160, 44, 44, 0.05)';
                } else if (reason === 'flee') {
                    battleResultDiv.style.backgroundColor = 'rgba(108, 117, 125, 0.05)';
                }
                const playerInfoSection = document.getElementById('battlePlayerInfo').parentNode;
                playerInfoSection.parentNode.insertBefore(battleResultDiv, playerInfoSection);
            }
            let endMessage = '战斗结束！';
            let messageColor = '#333';
            if (reason === 'victory') {
                endMessage = '🎉 战斗胜利！ 🎉';
                messageColor = '#28a745';
            } else if (reason === 'defeated' || !isPlayerAlive) {
                endMessage = '你已在战斗中倒下...';
                messageColor = '#a02c2c';
            } else if (reason === 'flee') {
                endMessage = '你成功逃离了！';
                messageColor = '#6c757d';
            }
            battleResultDiv.innerHTML = `
                <p style="margin: 0 0 10px 0; color: ${messageColor}; font-weight: bold;">${endMessage}</p>
                <button class="btn-primary" onclick="game.returnToSceneFromBattle()">[返回场景]</button>
            `;
            const endActions = document.getElementById('battleEndActions');
            if (endActions) endActions.style.display = 'none';
            const defeatedActionsDiv = document.getElementById('playerDefeatedActions');
            if (defeatedActionsDiv) {
                defeatedActionsDiv.remove();
            }
        } else {
            document.getElementById('battleActionsContainer').style.display = 'flex';
            this.updateBattleIntentionDisplay();
            if (!isPlayerAlive) {
                let defeatedActionsDiv = document.getElementById('playerDefeatedActions');
                if (!defeatedActionsDiv) {
                    defeatedActionsDiv = document.createElement('div');
                    defeatedActionsDiv.id = 'playerDefeatedActions';
                    defeatedActionsDiv.style.marginTop = '15px';
                    defeatedActionsDiv.style.textAlign = 'center';
                    defeatedActionsDiv.style.padding = '10px';
                    defeatedActionsDiv.style.border = '1px dashed var(--border-color)';
                    defeatedActionsDiv.style.backgroundColor = 'rgba(160, 44, 44, 0.05)';
                    const playerInfoSection = document.getElementById('battlePlayerInfo').parentNode;
                    playerInfoSection.parentNode.insertBefore(defeatedActionsDiv, playerInfoSection);
                }
                defeatedActionsDiv.innerHTML = `
                    <p style="margin: 0 0 10px 0; color: #a02c2c;">你已在战斗中倒下</p>
                    <button class="btn-primary" onclick="game.returnToSceneFromBattle()">[离开战场]</button>
                `;
            } else {
                const defeatedActionsDiv = document.getElementById('playerDefeatedActions');
                if (defeatedActionsDiv) {
                    defeatedActionsDiv.remove();
                }
            }
        }
    }

    addBattleLog(container, message) {
        // 为消息生成一个简单的哈希值作为ID，结合时间戳以区分相同内容的不同消息
        const generateMessageId = (msg) => {
            let hash = 0;
            for (let i = 0; i < msg.length; i++) {
                const char = msg.charCodeAt(i);
                hash = ((hash << 5) - hash) + char;
                hash = hash & hash; // Convert to 32bit integer
            }
            // 添加时间戳，确保即使内容相同的消息也能被区分
            return hash.toString() + '_' + Date.now();
        };
        
        // 生成消息ID
        const messageId = generateMessageId(message);
        
        // 检查是否已经显示过这条消息
        if (this.battleLogCache.has(messageId)) {
            return;
        }
        
        // 创建日志元素并显示
        const p = document.createElement('p');
        p.style.margin = '4px 0';
        p.style.padding = '3px 5px';
        p.style.borderRadius = '3px';
        
        // 交替行背景色
        const logEntries = container.querySelectorAll('p');
        if (logEntries.length % 2 === 0) {
            p.style.backgroundColor = 'rgba(0, 0, 0, 0.03)';
        }
        
        // 根据消息内容设置不同的样式
        if (message.includes('战斗开始') || message.includes('战斗结束') || message.includes('胜利') || message.includes('失败') || message.includes('逃')) {
            p.style.fontWeight = 'bold';
            p.style.color = '#005a00';
            p.style.borderLeft = '3px solid #005a00';
            p.style.paddingLeft = '7px';
        } else if (message.includes('闪避') || message.includes('暴击')) {
            p.style.fontWeight = 'bold';
            p.style.color = '#d35400'; // 橙色
        } else if (message.includes('造成了') && message.includes('伤害')) {
            p.style.color = '#a02c2c';
        } else if (message.includes('恢复了')) {
            p.style.color = '#28a745';
        } else if (message.includes('经验值') || message.includes('【经验奖励】')) {
            // 经验值获得消息样式增强
            p.style.fontWeight = 'bold';
            p.style.color = '#8e44ad'; // 紫色
            p.style.fontSize = '1.1em';
            p.style.backgroundColor = 'rgba(142, 68, 173, 0.1)';
            p.style.borderLeft = '3px solid #8e44ad';
            p.style.paddingLeft = '7px';
            p.style.marginTop = '8px';
            p.style.marginBottom = '8px';
            // 添加经验图标
            p.innerHTML = '🌟 ' + message;
            container.appendChild(p);
            container.scrollTop = container.scrollHeight;
            this.battleLogCache.add(messageId);
            
            // 解析战斗日志，提取伤害和治疗信息
            this.parseBattleLogForEffects(message);
            return;
        } else if (message.includes('掉落') || message.includes('【战利品】') || message.includes('【掉落物品】') || (message.includes('获得了') && message.includes('拾取权'))) {
            // 掉落物品消息样式增强
            p.style.fontWeight = 'bold';
            p.style.color = '#d35400'; // 橙色
            p.style.fontSize = '1.1em';
            p.style.backgroundColor = 'rgba(211, 84, 0, 0.1)';
            p.style.borderLeft = '3px solid #d35400';
            p.style.paddingLeft = '7px';
            p.style.marginTop = '8px';
            p.style.marginBottom = '8px';
            // 添加宝箱图标
            p.innerHTML = '💎 ' + message;
            container.appendChild(p);
            container.scrollTop = container.scrollHeight;
            this.battleLogCache.add(messageId);
            return;
        } else if (message.includes('击败')) {
            // 击败怪物消息样式增强
            p.style.fontWeight = 'bold';
            p.style.color = '#a02c2c'; // 红色
            p.style.fontSize = '1.1em';
            p.style.backgroundColor = 'rgba(211, 84, 0, 0.1)';
            p.style.borderLeft = '3px solid #d35400';
            p.style.paddingLeft = '7px';
            p.style.marginTop = '8px';
            p.style.marginBottom = '8px';
            // 添加图标
            p.innerHTML = '⚔️ ' + message;
            container.appendChild(p);
            container.scrollTop = container.scrollHeight;
            this.battleLogCache.add(messageId);
            return;
        }
        
        p.textContent = message;
        
        // 添加一个隐藏的标识符，用于调试
        p.dataset.logId = messageId;
        
        container.appendChild(p);
        container.scrollTop = container.scrollHeight;
        
        // 将消息ID添加到缓存中
        this.battleLogCache.add(messageId);
        
        // 限制缓存大小，防止内存泄漏
        if (this.battleLogCache.size > 1000) {
            // 转换为数组，删除最早的500个元素
            const cacheArray = Array.from(this.battleLogCache);
            this.battleLogCache = new Set(cacheArray.slice(-500));
        }
        
        // 解析战斗日志，提取伤害和治疗信息
        this.parseBattleLogForEffects(message);
    }
    
    // 解析战斗日志，提取伤害和治疗信息
    parseBattleLogForEffects(message) {
        // 如果不在战斗中，直接返回
        if (!this.battleState) return;
        
        // 1. 解析伤害信息
        // 格式例如: "玩家名 对 敌方名 造成了 X 点伤害"
        // 或者: "敌方名 对 玩家名 造成了 X 点伤害"
        let damageMatch = message.match(/(.+?)对(.+?)造成了(\d+)点伤害/);
        
        if (!damageMatch) {
            // 尝试其他格式: "玩家名受到X点伤害"
            damageMatch = message.match(/(.+?)受到(\d+)点伤害/);
            if (damageMatch) {
                const defender = damageMatch[1].trim();
                const damage = parseInt(damageMatch[2]);
                
                // 确定是玩家还是怪物受到伤害
                if (defender.includes(this.battleState.monster.username)) {
                    // 怪物受到伤害
                    this.addDamageEffect('monster-hp-bar', damage);
                } else {
                    // 玩家受到伤害
                    // 查找被攻击的玩家
                    const targetPlayer = this.battleState.all_players.find(p => 
                        defender.includes(p.username));
                    
                    if (targetPlayer) {
                        this.addDamageEffect(`player-hp-bar-${targetPlayer.id}`, damage);
                    }
                }
                return;
            }
        } else {
            const attacker = damageMatch[1].trim();
            const defender = damageMatch[2].trim();
            const damage = parseInt(damageMatch[3]);
            
            // 确定是玩家还是怪物受到伤害
            if (defender.includes(this.battleState.monster.username)) {
                // 怪物受到伤害
                this.addDamageEffect('monster-hp-bar', damage);
            } else {
                // 玩家受到伤害
                // 查找被攻击的玩家
                const targetPlayer = this.battleState.all_players.find(p => 
                    defender.includes(p.username));
                
                if (targetPlayer) {
                    this.addDamageEffect(`player-hp-bar-${targetPlayer.id}`, damage);
                }
            }
            return;
        }
        
        // 2. 解析治疗信息
        // 格式例如: "玩家名 恢复了 X 点生命值"
        let healMatch = message.match(/(.+?)恢复了(\d+)点(生命值|魔力值)/);
        
        if (!healMatch) {
            // 尝试其他格式: "玩家名的生命值恢复了X点"
            healMatch = message.match(/(.+?)的(生命值|魔力值)恢复了(\d+)点/);
            if (healMatch) {
                const target = healMatch[1].trim();
                const type = healMatch[2] === '生命值' ? 'hp' : 'mp';
                const amount = parseInt(healMatch[3]);
                
                // 查找被治疗的玩家
                const targetPlayer = this.battleState.all_players.find(p => 
                    target.includes(p.username));
                
                if (targetPlayer) {
                    this.addHealEffect(`player-${type}-bar-${targetPlayer.id}`, amount, type);
                }
                return;
            }
        } else {
            const target = healMatch[1].trim();
            const amount = parseInt(healMatch[2]);
            const type = healMatch[3] === '生命值' ? 'hp' : 'mp';
            
            // 查找被治疗的玩家
            const targetPlayer = this.battleState.all_players.find(p => 
                target.includes(p.username));
            
            if (targetPlayer) {
                this.addHealEffect(`player-${type}-bar-${targetPlayer.id}`, amount, type);
            }
            return;
        }

        // 3. 解析闪避信息
        // 格式例如: "玩家名 闪避了 敌方名 的攻击"
        // 或者: "玩家名 成功闪避了攻击"
        let dodgeMatch = message.match(/(.+?)闪避了(.+?)的攻击/) ||
                        message.match(/(.+?)成功闪避了攻击/) ||
                        message.match(/(.+?)闪避了攻击/);

        if (dodgeMatch) {
            const dodger = dodgeMatch[1].trim();

            // 确定是玩家还是怪物闪避了攻击
            if (dodger.includes(this.battleState.monster.username)) {
                // 怪物闪避了攻击
                this.addDodgeEffect('battleMonsterInfo');
            } else {
                // 玩家闪避了攻击
                // 查找闪避的玩家
                const dodgePlayer = this.battleState.all_players.find(p =>
                    dodger.includes(p.username));

                if (dodgePlayer) {
                    this.addDodgeEffect(`player-info-${dodgePlayer.id}`, dodgePlayer.id);
                }
            }
            return;
        }
    }

    sendBattleAction(action, data = {}) {
        const payload = {
            action: action,
            ...data
        };
        this.sendMessage(MessageProtocol.C2S_BATTLE_ACTION, payload);
        
        // 更新客户端状态和行动意图显示
        if (this.battleState) {
        this.battleState.currentIntention = action;
            if (action === 'use_skill') {
                this.battleState.skillName = data.skill_name || '';
            }
        }
        
        // 更新行动意图显示
        const intentionDisplay = document.getElementById('battleIntentionDisplay');
        if (intentionDisplay) {
            let intentionText = '攻击'; // 默认
            if (action === 'flee') {
                intentionText = '逃跑';
            } else if (action === 'use_hp_potion') {
                intentionText = '使用HP药水';
            } else if (action === 'use_mp_potion') {
                intentionText = '使用MP药水';
            } else if (action === 'use_skill' && data.skill_name) {
                intentionText = `施放【${data.skill_name}】`;
            } else if (action === 'use_skill') {
                intentionText = '施放技能';
            }
            
            intentionDisplay.textContent = `正在准备：${intentionText}...`;
            intentionDisplay.style.display = 'block';
        }

        // 移除了1秒按钮冻结功能，现在依靠技能冷却系统来管理按钮状态
    }
    
    /**
     * 检查玩家是否被眩晕
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {Object|null} 眩晕信息对象，包含isStunned和remainingTurns，如果未被眩晕则返回null
     */
    getPlayerStunInfo(battleState, playerId) {
        if (!battleState || !battleState.active_effects) {
            return null;
        }

        for (const effect of battleState.active_effects) {
            if (effect.target_id == playerId && effect.effects && effect.effects.stun && effect.effects.stun > 0) {
                return {
                    isStunned: true,
                    remainingTurns: effect.remaining_turns || 0
                };
            }
        }

        return null;
    }

    /**
     * 检查玩家是否被眩晕（兼容性方法）
     * @param {Object} battleState 战斗状态
     * @param {string} playerId 玩家ID
     * @returns {boolean} 是否被眩晕
     */
    isPlayerStunned(battleState, playerId) {
        const stunInfo = this.getPlayerStunInfo(battleState, playerId);
        return stunInfo ? stunInfo.isStunned : false;
    }

    updateBattleIntentionDisplay() {
        const intentionDisplay = document.getElementById('battleIntentionDisplay');
        if (!intentionDisplay) return;

        if (!this.battleState || this.battleState.isOver) {
            intentionDisplay.style.display = 'none';
            return;
        }

        intentionDisplay.style.display = 'block';

        // 检查玩家是否被眩晕
        const stunInfo = this.getPlayerStunInfo(this.battleState, this.currentPlayer.id);

        if (stunInfo && stunInfo.isStunned) {
            const remainingText = stunInfo.remainingTurns > 0 ? ` (${stunInfo.remainingTurns}回合)` : '';
            intentionDisplay.textContent = `眩晕中${remainingText}`;
            return;
        }

        let intentionText = '攻击'; // 默认
        const currentAction = this.battleState.currentIntention;

        if (currentAction === 'flee') {
            intentionText = '逃跑';
        } else if (currentAction === 'use_hp_potion') {
            intentionText = '使用HP药水';
        } else if (currentAction === 'use_mp_potion') {
            intentionText = '使用MP药水';
        } else if (currentAction === 'use_skill') {
            if (this.battleState.skillName) {
                intentionText = `施放【${this.battleState.skillName}】`;
            } else {
                intentionText = '施放技能';
            }
        }

        intentionDisplay.textContent = `正在准备：${intentionText}...`;
    }

    sendFleeBattle() {
        this.sendMessage(MessageProtocol.C2S_FLEE_BATTLE);
        // The server will respond with BATTLE_ENDED, which will handle the view change.
    }

    returnToSceneFromBattle() {
        // 完全清理战斗状态
        this.battleState = null;
        this.inBattle = false;
        this.battleLogCache.clear();
        this.battleLogExpanded = false; // 重置战斗日志展开状态
        
        // 清理战斗UI元素
        document.getElementById('battleActionsContainer').innerHTML = ''; // 清空所有按钮
        document.getElementById('battleEndActions').style.display = 'none';
        
        // 隐藏"正在准备"提示信息
        const intentionDisplay = document.getElementById('battleIntentionDisplay');
        if (intentionDisplay) {
            intentionDisplay.style.display = 'none';
        }
        
        // 移除可能存在的玩家倒下后的操作区域
        const defeatedActionsDiv = document.getElementById('playerDefeatedActions');
        if (defeatedActionsDiv) {
            defeatedActionsDiv.remove();
        }
        
        // 移除战斗结果提示区域
        const battleResultDiv = document.getElementById('battleResultActions');
        if (battleResultDiv) {
            battleResultDiv.remove();
        }
        
        // 清空战斗日志
        const battleLog = document.getElementById('battleLogContainer');
        battleLog.innerHTML = '';
        
        // 隐藏战斗界面，显示场景界面
        document.getElementById('battleView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        
        // 获取最新的场景数据
        if (this.currentScene) {
            this.sendMessage(MessageProtocol.C2S_ENTER_SCENE, { scene_id: this.currentScene.id });
        }
    }

    generateStatBarHtml(current, max, type = 'hp', id = '') {
        const perc = max > 0 ? Math.max(0, Math.min(100, (current / max) * 100)) : 0;
        const innerBarClass = type === 'hp' ? 'hp-bar-inner' : 'mp-bar-inner';
        const barId = id ? `id="${id}"` : '';
        return `
            <div class="hp-bar-container" ${barId}>
                <div class="${innerBarClass}" style="width: ${perc}%;"></div>
                <div class="hp-bar-text">${current} / ${max}</div>
            </div>
        `;
    }

    generateATBBarHtml(current, max, id = '') {
        const perc = max > 0 ? Math.max(0, Math.min(100, (current / max) * 100)) : 0;
        const barId = id ? `id="${id}"` : '';
        return `
            <div class="atb-bar-container" ${barId}>
                <div class="atb-bar-inner" style="width: ${perc}%;"></div>
            </div>
        `;
    }

    isConnected() { return this.ws && this.ws.readyState === WebSocket.OPEN; }

    sendMessage(opcode, payload = {}, forceUnencrypted = false) {
        this.resetInactivityTimer(); // Reset timer on any user action
        
        // 添加调试信息
        const opcodeHex = opcode.toString(16).padStart(2, '0');
        const opcodeConstName = Object.keys(MessageProtocol).find(key => MessageProtocol[key] === opcode) || 'UNKNOWN';
        console.log(`发送消息: 0x${opcodeHex} (${opcodeConstName})`, payload);
        
        // 添加玩家ID到所有消息中
        if (this.currentPlayer && this.currentPlayer.id) {
            payload.player_id = this.currentPlayer.id;
        }
        
        let message;
        if (!this.sessionKey || forceUnencrypted) {
            message = this.unencryptedProtocol.encode(opcode, payload);
        } else {
            message = this.secureProtocol.encode(opcode, payload, this.sessionKey);
        }
        
        if (this.isConnected()) {
            if (message) {
                try {
                    this.ws.send(message);
                return true;
                } catch (e) {
                    console.error('发送消息失败:', e);
                    return false;
                }
            } else {
                console.error('消息编码失败');
                return false;
            }
        } else {
            if (this.sessionKey) this.pendingMessages.push({opcode, payload}); // Only queue authenticated messages
            if (!this.isConnecting) this.attemptReconnect();
                return false;
            }
    }

    getHpStatusPrefix(current, max) {
        if (current <= 0) return '';
        if (current >= max) return '健康的';
        if (current > max / 2) return '充沛的';
        return '虚弱的';
    }

    logout() {
        if (this.isConnected()) {
            this.sendMessage(MessageProtocol.C2S_LOGOUT, {}, true);
            this.ws.close();
        }
        // 添加这一行，清除本地存储的会话信息
        localStorage.removeItem('gameSession');
        localStorage.removeItem('gameSessionKey');
        this.secretKey = null;
        this.player = null;
        this.currentScene = null;

        const loginView = document.getElementById('login-view');
        const mainView = document.getElementById('main-view');
        const passwordField = document.getElementById('password');
        
        if (loginView) loginView.style.display = 'block';
        if (mainView) mainView.style.display = 'none';
        if (passwordField) passwordField.value = '';
        
        this.clearMessages();
        
        if (this.inactivityTimer) {
            clearTimeout(this.inactivityTimer);
            this.inactivityTimer = null;
        }
        if (this.autoResetTimer) {
            clearInterval(this.autoResetTimer);
            this.autoResetTimer = null;
        }
    }

    hideAllViews() {
        const viewIds = [
            'main-view', 'battleView', 'pvpBattleView', 'mapView', 'selfView', 'skillsView',
            'inventoryView', 'equipmentView', 'itemDetailView', 'gemSocketingView',
            'monsterDetailView', 'playerDetailView', 'buildingView', 'configView',
            'pvpLeaderboardView', 'pvpStatsView', 'chat-view', 'dropItemView', 'bindItemView',
            'levelRankingView', 'heroRankingView', 'villainRankingView', 'private-chat-view',
            'announcementView', 'announcementListView', 'redemptionCodeView', 'refineLeaderboardView'
        ];
        viewIds.forEach(id => {
            const view = document.getElementById(id);
            if (view) {
                view.style.display = 'none';
            }
        });
        
        // 移除所有PVP相关的UI，以防重叠
        document.querySelectorAll('.pvp-challenge-notification, #pvp-battle-container, #pvp-ui').forEach(el => el.remove());
    }

    resetInactivityTimer() {
        this.lastActivityTime = Date.now();
    }

    updateMovementUI() {
        const exits = this.currentExits || {};
        const movementEl = document.getElementById('movementActions');
        movementEl.innerHTML = '';

        const directionMap = { north: '▲ 北', south: '▼ 南', east: '▶ 东', west: '◀ 西' };
        let hasExits = false;

        for (const direction of ['north', 'east', 'south', 'west']) {
            const exitInfo = exits[direction]; // This is now a scene object {id, name, ...}
            if (exitInfo) {
                hasExits = true;
                    const p = document.createElement('p');
                    p.style.margin = '5px 0';
                p.innerHTML = `${directionMap[direction]}：<a href="#" onclick="event.preventDefault(); move('${direction}');">${exitInfo.name}</a>`;
                    movementEl.appendChild(p);
            }
        }

        if (!hasExits) {
            const p = document.createElement('p');
            p.style.margin = '5px 0';
            p.style.color = '#6c8095';
            p.textContent = '这里没有其他出口。';
            movementEl.appendChild(p);
        }
    }

    showMap() {
        if (!this.currentScene || !this.scenes) {
            this.addLog("无法获取地图信息。");
            return;
        }

        console.log("当前场景信息:", this.currentScene);
        console.log("所有场景信息:", this.scenes);

        this.assignSceneCoordinates();

        this.currentScene.x = Number(this.currentScene.x);
        this.currentScene.y = Number(this.currentScene.y);

        if (isNaN(this.currentScene.x) || isNaN(this.currentScene.y)) {
            this.addLog("当前场景没有坐标信息，无法生成地图。");
            console.error("坐标分配失败", this.currentScene);
            return;
        }

        const radius = 2;
        const { x: currentX, y: currentY } = this.currentScene;
        const minX = currentX - radius;
        const maxX = currentX + radius;
        const minY = currentY - radius;
        const maxY = currentY + radius;

        const sceneCoords = new Map();
        // Iterate over the values of the Map object
        for (const s of this.scenes.values()) {
            const x = Number(s.x);
            const y = Number(s.y);
            // 移除对exits的严格检查
            if (!isNaN(x) && !isNaN(y)) {
                sceneCoords.set(`${x},${y}`, s);
            }
        };

        if (sceneCoords.size === 0) {
             this.addLog("附近没有可显示的地点。");
             console.error("场景坐标映射为空", this.scenes);
             return;
        }

        // 创建网格布局
        const mapGrid = document.getElementById('mapGrid');
        mapGrid.innerHTML = '';

        // 设置网格列数为5 (radius * 2 + 1)
        const gridSize = radius * 2 + 1;
        mapGrid.style.gridTemplateColumns = `repeat(${gridSize}, 1fr)`;

        // 从上到下，从左到右创建网格
        for (let y = maxY; y >= minY; y--) {
            for (let x = minX; x <= maxX; x++) {
                const scene = sceneCoords.get(`${x},${y}`);
                const card = document.createElement('div');

                if (scene) {
                    card.className = 'map-scene-card';

                    // 判断场景类型并添加相应样式
                    if (scene.id === this.currentScene.id) {
                        card.classList.add('current-position');
                    } else if (scene.is_safe_zone === 1 || scene.is_safe_zone === undefined) {
                        card.classList.add('safe-zone');
                    } else {
                        card.classList.add('danger-zone');
                    }

                    // 检查连接并添加连接线样式
                    const exits = scene.exits || {};
                    const eastScene = sceneCoords.get(`${x + 1},${y}`);
                    const southScene = sceneCoords.get(`${x},${y - 1}`);

                    if (exits.east && eastScene && eastScene.id === exits.east.id) {
                        card.classList.add('has-east');
                    }
                    if (exits.south && southScene && southScene.id === exits.south.id) {
                        card.classList.add('has-south');
                    }

                    // 创建场景名称
                    const nameDiv = document.createElement('div');
                    nameDiv.className = 'map-scene-name';
                    nameDiv.textContent = scene.name;
                    nameDiv.title = scene.name; // 悬停显示完整名称

                    card.appendChild(nameDiv);

                    // 添加点击事件（如果不是当前位置且是相邻场景）
                    if (scene.id !== this.currentScene.id) {
                        // 检查是否是相邻场景
                        const currentX = Number(this.currentScene.x);
                        const currentY = Number(this.currentScene.y);
                        const deltaX = x - currentX;
                        const deltaY = y - currentY;
                        const isAdjacent = Math.abs(deltaX) + Math.abs(deltaY) === 1;

                        if (isAdjacent) {
                            // 检查是否有对应的出口
                            const exits = this.currentScene.exits || {};
                            let direction = '';
                            if (deltaX === 1) direction = 'east';
                            else if (deltaX === -1) direction = 'west';
                            else if (deltaY === 1) direction = 'north';
                            else if (deltaY === -1) direction = 'south';

                            const exitInfo = exits[direction];
                            if (exitInfo && exitInfo.id === scene.id) {
                                card.classList.add('clickable');
                                card.style.cursor = 'pointer';
                                card.onclick = () => {
                                    this.moveToScene(scene, x, y);
                                };
                            }
                        }
                    }
                } else {
                    // 空白区域
                    card.className = 'map-scene-card empty';
                }

                mapGrid.appendChild(card);
            }
        }

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('battleView').style.display = 'none';
        document.getElementById('mapView').style.display = 'block';
    }

    hideMap() {
        document.getElementById('mapView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    moveToScene(targetScene, targetX, targetY) {
        if (!this.currentScene || !targetScene) {
            this.addLog("无法移动到目标场景。");
            return;
        }

        const currentX = Number(this.currentScene.x);
        const currentY = Number(this.currentScene.y);

        if (isNaN(currentX) || isNaN(currentY) || isNaN(targetX) || isNaN(targetY)) {
            this.addLog("坐标信息错误，无法移动。");
            return;
        }

        // 计算移动方向
        const deltaX = targetX - currentX;
        const deltaY = targetY - currentY;

        // 检查是否是相邻场景（只能移动到相邻的场景）
        if (Math.abs(deltaX) + Math.abs(deltaY) !== 1) {
            this.addLog(`${targetScene.name} 不是相邻场景，无法直接移动。`);
            return;
        }

        // 确定移动方向
        let direction = '';
        if (deltaX === 1) direction = 'east';
        else if (deltaX === -1) direction = 'west';
        else if (deltaY === 1) direction = 'north';
        else if (deltaY === -1) direction = 'south';

        if (!direction) {
            this.addLog("无法确定移动方向。");
            return;
        }

        // 检查当前场景是否有对应方向的出口
        const exits = this.currentScene.exits || {};
        const exitInfo = exits[direction];

        if (!exitInfo || exitInfo.id !== targetScene.id) {
            this.addLog(`无法向${this.getDirectionName(direction)}移动到 ${targetScene.name}。`);
            return;
        }

        // 执行移动
        // this.addLog(`正在移动到 ${targetScene.name}...`);
        this.sendMessage(MessageProtocol.C2S_MOVE, { direction: direction });

        // 关闭地图界面
        this.hideMap();
    }

    getDirectionName(direction) {
        const directionNames = {
            'north': '北',
            'south': '南',
            'east': '东',
            'west': '西'
        };
        return directionNames[direction] || direction;
    }

    assignSceneCoordinates() {
        console.log("开始分配场景坐标");
        if (!this.scenes || this.scenes.size === 0) {
            console.error("没有场景数据");
            return;
        }

        const sceneList = Array.from(this.scenes.values());

        // 转换所有场景的坐标为数字
        sceneList.forEach(scene => {
            scene.x = Number(scene.x);
            scene.y = Number(scene.y);
        });

        // 如果已经有坐标，直接返回
        if (sceneList.some(s => !isNaN(s.x) && !isNaN(s.y))) {
            console.log("场景已有坐标，无需重新生成。");
            return;
        }
        
        console.log("开始生成场景坐标");

        const scenesById = new Map(sceneList.map(s => [s.id, { ...s, processed: false }]));
        
        // 选择第一个场景作为起点
        const startScene = scenesById.get(sceneList[0].id);
        if (!startScene) {
             console.error("无法确定起始场景来生成坐标。");
             return;
        }
        startScene.x = 0;
        startScene.y = 0;
        startScene.processed = true;

        const queue = [startScene.id];
        const directions = [
            { name: 'north', dx: 0, dy: 1 },
            { name: 'south', dx: 0, dy: -1 },
            { name: 'east', dx: 10, dy: 0 },
            { name: 'west', dx: -1, dy: 0 }
        ];

        while (queue.length > 0) {
            const currentSceneId = queue.shift();
            const currentScene = scenesById.get(currentSceneId);

            if (!currentScene) continue;

            const exits = currentScene.exits || {};

            directions.forEach(dir => {
                const neighborInfo = exits[dir.name];
                if (neighborInfo && neighborInfo.id) {
                    const neighborScene = scenesById.get(neighborInfo.id);
                    if (neighborScene && !neighborScene.processed) {
                        neighborScene.x = (currentScene.x || 0) + dir.dx;
                        neighborScene.y = (currentScene.y || 0) + dir.dy;
                        neighborScene.processed = true;
                        queue.push(neighborInfo.id);
                    }
                }
            });
        }

        // 更新 this.scenes (Map) 中的坐标
        this.scenes.forEach(scene => {
            const processedScene = scenesById.get(scene.id);
            if (processedScene) {
                scene.x = processedScene.x;
                scene.y = processedScene.y;
            }
        });

        console.log("场景坐标分配完成", this.scenes);
    }

    showSelfView() {
        this.updateSelfView();
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('selfView').style.display = 'block';
    }

    hideSelfView() {
        document.getElementById('selfView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    updateSelfView() {
        if (!this.currentPlayer || !this.currentPlayer.attributes) {
            document.getElementById('selfInfoContent').innerHTML = '<p>暂无属性信息。</p>';
            return;
        }

        const attr = this.currentPlayer.attributes;
        const content = `
            <table width="100%" style="border-collapse: collapse;">
                <tr>
                    <td width="50%" style="padding: 4px 0;"><b>等级:</b> ${attr.level || 1}</td>
                    <td width="50%" style="padding: 4px 0;"><b>职业:</b> ${attr.job_name || '无业'}</td>
                </tr>
                 <tr>
                    <td width="50%" style="padding: 4px 0;"><b>潜力:</b> <span style="color: #28a745; font-weight: bold;">${attr.potential_points || 0}</span></td>
                    <td width="50%" style="padding: 4px 0;"><b>知识:</b> <span style="color: #17a2b8;">${attr.knowledge_points || 0}</span></td>
                </tr>
                <tr>
                    <td colspan="2" style="color:#555; font-size: 14px; padding-bottom: 5px;"><i>${attr.job_description || '暂无职业描述'}</i></td>
                </tr>
                <tr>
                    <td colspan="2" style="padding: 4px 0; border-top: 1px dotted var(--border-color); border-bottom: 1px dotted var(--border-color);">
                        <b>经验:</b> ${attr.experience || 0} / ${attr.experience_to_next_level || 100}
                    </td>
                </tr>
                <tr>
                    <td style="padding-top: 8px;"><b>生命:</b> ${attr.hp || 0} / ${attr.max_hp || 0}</td>
                    <td style="padding-top: 8px;"><b>魔力:</b> ${attr.mp || 0} / ${attr.max_mp || 0}</td>
                </tr>
                <tr>
                    <td colspan="2"><hr style="border-color: var(--border-color); border-style: dotted;"></td>
                </tr>
                <tr>
                    <td id="self-view-strength"><b>力量:</b> ${attr.strength || 0}</td>
                    <td id="self-view-agility"><b>敏捷:</b> ${attr.agility || 0}</td>
                </tr>
                <tr>
                    <td id="self-view-constitution"><b>体质:</b> ${attr.constitution || 0}</td>
                    <td id="self-view-intelligence"><b>智慧:</b> ${attr.intelligence || 0}</td>
                </tr>
                <tr>
                    <td colspan="2"><hr style="border-color: var(--border-color); border-style: dotted;"></td>
                </tr>
                <tr>
                    <td><b>攻击:</b> ${attr.attack || 0}</td>
                    <td><b>防御:</b> ${attr.defense || 0}</td>
                </tr>
                <tr>
                    <td><b>攻速:</b> ${Math.floor(attr.attack_speed) || 0}</td>
                    <td><b>善恶:</b> ${attr.karma || 0}</td>
                </tr>
                 <tr>
                    <td colspan="2"><hr style="border-color: var(--border-color); border-style: dotted;"></td>
                </tr>
                <tr>
                    <td><b>火抗:</b> ${attr.fire_resistance || 0}</td>
                    <td><b>冰抗:</b> ${attr.ice_resistance || 0}</td>
                </tr>
                <tr>
                    <td><b>风抗:</b> ${attr.wind_resistance || 0}</td>
                    <td><b>电抗:</b> ${attr.electric_resistance || 0}</td>
                </tr>
                <tr>
                    <td colspan="2"><hr style="border-color: var(--border-color); border-style: dotted;"></td>
                </tr>
                <tr>
                    <td><b>火焰:</b> ${attr.fire_damage || 0}</td>
                    <td><b>冰冻:</b> ${attr.ice_damage || 0}</td>
                </tr>
                <tr>
                    <td><b>风裂:</b> ${attr.wind_damage || 0}</td>
                    <td><b>闪电:</b> ${attr.electric_damage || 0}</td>
                </tr>
            </table>
        `;
        document.getElementById('selfInfoContent').innerHTML = content;

        const allocator = document.getElementById('potentialPointsAllocator');
        if (attr.potential_points > 0) {
            allocator.style.display = 'block';
            document.getElementById('allocatorError').textContent = '';
            document.getElementById('strength_points').value = 0;
            document.getElementById('agility_points').value = 0;
            document.getElementById('constitution_points').value = 0;
            document.getElementById('intelligence_points').value = 0;
            this.updatePointAllocationSummary();
        } else {
            allocator.style.display = 'none';
        }

        // 每次更新角色页面时都初始化控件
        this.setupPointAllocatorControls();
    }

    updatePointAllocationSummary() {
        const summarySpan = document.getElementById('points-distribution-summary');
        if (!summarySpan) return;

        const potentialPoints = this.currentPlayer.attributes.potential_points || 0;
        const allocatedPoints = ['strength', 'agility', 'constitution', 'intelligence'].reduce((total, attr) => {
            return total + (parseInt(document.getElementById(`${attr}_points`).value) || 0);
        }, 0);

        const remaining = potentialPoints - allocatedPoints;
        summarySpan.innerHTML = `(已分配: <span style="color: var(--main-color);">${allocatedPoints}</span> / 剩余: <span style="color: #28a745;">${remaining}</span>)`;
    }

    handleInventoryData(payload) {
        // Cache the inventory data
        this.inventory.equipped = payload.equipped || [];
        this.inventory.backpack = payload.backpack || [];
        this.inventory.currencies = payload.currencies || { gold: 0, diamonds: 0 };
        this.inventoryCacheTime = Date.now();
        
        // 合并背包和装备数据，方便在商店中使用
        this.inventory.items = [
            ...(this.inventory.backpack || []),
            ...(this.inventory.equipped || [])
        ];
        
        // 背包数据已更新
        
        // If the inventory view is currently visible, update it.
        if (document.getElementById('inventoryView').style.display !== 'none') {
            this.updateInventoryView();
        }
        // Also update equipment view if it's visible, as it's part of the same data
        if (document.getElementById('equipmentView').style.display !== 'none') {
            this.updateEquipmentView();
        }

        // If item detail view is open, refresh it or close it.
        const itemDetailView = document.getElementById('itemDetailView');
        if (itemDetailView && itemDetailView.style.display === 'block' && this.currentDetailItem) {
            const allItems = [...(payload.backpack || []), ...(payload.equipped || [])];
            const updatedItem = allItems.find(i => i.inventory_id === this.currentDetailItem.inventory_id);

            if (updatedItem) {
                // Item still exists (e.g., quantity decreased or it was unequipped), refresh the view.
                this.showItemDetails(updatedItem, this.itemDetailOrigin);
            } else {
                // Item is gone (e.g., consumed), close view and show log message.
                this.addLog(`[${this.currentDetailItem.name}] 已用完。`);
                this.hideItemDetailView();
            }
        }
        
        // 通知建筑管理器背包数据已更新
        if (this.buildingManager) {
            this.buildingManager.handleInventoryUpdate(this.inventory);
        }

        // 如果交易物品选择界面正在显示，重新生成分类选项
        const tradeItemSelectionView = document.getElementById('tradeItemSelectionView');
        if (tradeItemSelectionView && tradeItemSelectionView.style.display === 'block') {
            this.generateDynamicCategoryOptions();
            this.displayTradeItemList(); // 重新显示物品列表
        }
    }



    showInventoryView() {
        this.clearMessages();
        this.sendMessage(MessageProtocol.C2S_GET_INVENTORY);
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('equipmentView').style.display = 'none';
        document.getElementById('selfView').style.display = 'none';
        document.getElementById('mapView').style.display = 'none';
        document.getElementById('configView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'none';
        document.getElementById('monsterDetailView').style.display = 'none';
        document.getElementById('playerDetailView').style.display = 'none';
        document.getElementById('inventoryView').style.display = 'block';
    }

    hideInventoryView() {
        this.clearMessages();
        document.getElementById('inventoryView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }
    
    filterInventory(category) {
        this.clearMessages();
        this.inventoryView.currentFilter = category;
        this.inventoryView.currentPage = 1;
        this.updateInventoryView();
    }

    changeInventoryPage(direction) {
        this.clearMessages();
        this.inventoryView.currentPage += direction;
        this.updateInventoryView();
    }

    updateInventoryView() {
        const inventoryList = document.getElementById('inventory-list');
        const pagination = document.getElementById('inventory-pagination');
        const filterContainer = document.getElementById('inventory-filters');
        
        inventoryList.innerHTML = '正在加载...';
        
        // 更新货币显示
        const goldEl = document.getElementById('inventory-gold');
        const diamondsEl = document.getElementById('inventory-diamonds');
        if (goldEl) {
            goldEl.textContent = `💰 金币: ${this.inventory.currencies.gold.toLocaleString()}`;
        }
        if (diamondsEl) {
            diamondsEl.textContent = `💎 钻石: ${this.inventory.currencies.diamonds.toLocaleString()}`;
        }

        // 添加分类过滤器
        const categories = ['All', ...new Set(this.inventory.backpack.map(item => item.category))];
        
        // 检查分类数量，超过6个时分两行显示
        if (categories.length > 6) {
            const firstRow = categories.slice(0, 3);
            const secondRow = categories.slice(3);
            
            filterContainer.innerHTML = `
                <div style="text-align: center; margin-bottom: 5px;">
                    ${firstRow.map(cat => {
                        const displayName = cat === 'All' ? '全部' : (this.categoryMap[cat] || cat);
                        const isActive = this.inventoryView.currentFilter === cat;
                        const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                        return `<button class="btn-small" style="${style}" onclick="game.filterInventory('${cat}')">${displayName}</button>`;
                    }).join(' ')}
                </div>
                <div style="text-align: center;">
                    ${secondRow.map(cat => {
                        const displayName = cat === 'All' ? '全部' : (this.categoryMap[cat] || cat);
                        const isActive = this.inventoryView.currentFilter === cat;
                        const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                        return `<button class="btn-small" style="${style}" onclick="game.filterInventory('${cat}')">${displayName}</button>`;
                    }).join(' ')}
                </div>
            `;
        } else {
            // 分类少于等于6个时，使用单行居中显示
            filterContainer.innerHTML = `
                <div style="text-align: center;">
                    ${categories.map(cat => {
                        const displayName = cat === 'All' ? '全部' : (this.categoryMap[cat] || cat);
                        const isActive = this.inventoryView.currentFilter === cat;
                        const style = `background-color: ${isActive ? 'var(--main-color)' : '#fff'}; color: ${isActive ? 'var(--bg-color)' : 'var(--link-color)'}`;
                        return `<button class="btn-small" style="${style}" onclick="game.filterInventory('${cat}')">${displayName}</button>`;
                    }).join(' ')}
                </div>
            `;
        }

        const category = this.inventoryView.currentFilter;
        let itemsToShow = this.inventory.backpack;

        const filteredItems = this.inventoryView.currentFilter === 'All'
            ? this.inventory.backpack
            : this.inventory.backpack.filter(item => item.category === this.inventoryView.currentFilter);

        const totalPages = Math.ceil(filteredItems.length / this.inventoryView.itemsPerPage);
        this.inventoryView.currentPage = Math.min(this.inventoryView.currentPage, totalPages) || 1;
        const startIndex = (this.inventoryView.currentPage - 1) * this.inventoryView.itemsPerPage;
        const pagedItems = filteredItems.slice(startIndex, startIndex + this.inventoryView.itemsPerPage);

        if (pagedItems.length === 0) {
            inventoryList.innerHTML = '<p style="text-align: center; color: #666; padding: 20px 0;">背包里没有这类物品。</p>';
        } else {
            let html = '<table width="100%" style="border-spacing: 0 2px; table-layout: fixed; border-collapse: separate;">';
            pagedItems.forEach(item => {
                const itemJson = JSON.stringify(item).replace(/"/g, '&quot;');
                let actionButtons = '';

                // 检查是否为技能书
                let isSkillBook = false;
                if (item.effects) {
                    try {
                        const effects = typeof item.effects === 'string' ? JSON.parse(item.effects) : item.effects;
                        isSkillBook = effects && effects.learn_skill_id;
                    } catch (e) {
                        console.error("解析物品效果失败:", e);
                    }
                }

                // 创建物品名称变量，用于显示提示
                const itemName = (item.instance_data && JSON.parse(item.instance_data).display_name) ?
                    JSON.parse(item.instance_data).display_name : item.name;

                // 如果是技能书，添加学习按钮，无论是否为消耗品
                if (isSkillBook) {
                    actionButtons += `<button class="btn-small skill-book-button"
                        onclick="event.stopPropagation();
                        game.useItem(${item.inventory_id});">[学习]</button> `;
                }
                // 如果是普通消耗品，添加使用按钮
                else if (item.is_consumable == 1) {
                    actionButtons += `<button class="btn-small"
                        onclick="event.stopPropagation();
                        game.useItem(${item.inventory_id});">[使用]</button> `;
                }

                if (item.slot) {
                    actionButtons += `<button class="btn-small"
                        onclick="event.stopPropagation();
                        game.equipItem(${item.inventory_id});">[装备]</button>`;
                }

                // Use display_name from instance_data if available
                const displayName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name;

                // 检查物品是否绑定，如果绑定则添加[绑]标记
                const isBound = item.is_bound == 1;
                const boundMark = isBound ? ' <span style="color: #ff6b6b;">[绑]</span>' : '';

                // 为技能书添加特殊样式
                const itemStyle = isSkillBook ? 'color: #4b0082; font-weight: bold;' : '';

                html += `
                    <tr style="cursor: pointer; background-color: rgba(255,255,255,0.3); border: 1px solid #ddd; border-radius: 3px;" onclick='game.showItemDetails(${itemJson}, "inventory")'>
                        <td style="width: 70%; padding: 6px 8px; word-wrap: break-word; word-break: break-all; line-height: 1.3; vertical-align: middle;">
                            <a href="#" style="${itemStyle}" onclick="event.preventDefault()">${displayName}</a>${boundMark}
                            ${item.stackable == 1 ? `<span style="color: #666; font-size: 0.9em;">(x${item.quantity})</span>` : ''}
                        </td>
                        <td style="width: 30%; text-align: right; padding: 6px 8px; vertical-align: middle;">${actionButtons.trim()}</td>
                    </tr>`;
            });
            html += '</table>';
            inventoryList.innerHTML = html;
        }

        pagination.innerHTML = '';
        if (totalPages > 1) {
            let paginationHtml = `<span style="margin-right:10px;">第 ${this.inventoryView.currentPage} / ${totalPages} 页</span>`;
            if (this.inventoryView.currentPage > 1) {
                paginationHtml += `<button class="btn-small" onclick="game.changeInventoryPage(-1)">上一页</button>`;
            }
            if (this.inventoryView.currentPage < totalPages) {
                paginationHtml += `<button class="btn-small" onclick="game.changeInventoryPage(1)">下一页</button>`;
            }
            pagination.innerHTML = paginationHtml;
        }
    }
    
    showEquipmentView() {
        this.clearMessages();
        this.itemDetailOrigin = 'equipment'; // 重置来源
        this.sendMessage(MessageProtocol.C2S_GET_INVENTORY);
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('inventoryView').style.display = 'none';
        document.getElementById('selfView').style.display = 'none';
        document.getElementById('mapView').style.display = 'none';
        document.getElementById('configView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'none';
        document.getElementById('monsterDetailView').style.display = 'none';
        document.getElementById('playerDetailView').style.display = 'none';
        document.getElementById('equipmentView').style.display = 'block';
    }

    hideEquipmentView() {
        this.clearMessages();
        document.getElementById('equipmentView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }
    
    updateEquipmentView() {
        const listContainer = document.getElementById('equipment-list');
        
        const slotOrder = {
            'Head': '头部', 'Neck': '颈部', 'Body': '身体', 'Back': '背部',
            'LeftHand': '左手', 'RightHand': '右手', 'TwoHanded': '双手',
            'Finger': '手指'
        };

        const equippedMap = new Map();
        this.inventory.equipped.forEach(item => equippedMap.set(item.slot, item));

        let displaySlots = Object.keys(slotOrder);
        
        if (equippedMap.has('TwoHanded')) {
            displaySlots = displaySlots.filter(slot => slot !== 'LeftHand' && slot !== 'RightHand');
        } else {
            displaySlots = displaySlots.filter(slot => slot !== 'TwoHanded');
        }

        if (this.inventory.equipped.length === 0) {
            listContainer.innerHTML = '<p style="text-align: center; color: #666; padding: 20px 0;">未装备任何物品。</p>';
            return;
        }

        let html = '<table width="100%" style="border-collapse: collapse; table-layout: fixed;">';
        displaySlots.forEach(slotKey => {
            const item = equippedMap.get(slotKey);
            // Use display_name from instance_data if available
            const displayName = item ? ((item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name) : null;
            
            // 检查物品是否绑定，如果绑定则添加[绑]标记
            const isBound = item && item.is_bound == 1;
            const boundMark = isBound ? ' <span style="color: #ff6b6b;">[绑]</span>' : '';
            
            const itemName = item ? `<span style="color: #005a00;">${displayName}${boundMark}</span>` : '<span style="color: #6c8095;">无</span>';
            const slotName = slotOrder[slotKey];
            const clickHandler = item ? `onclick='game.showItemDetails(${JSON.stringify(item).replace(/"/g, '&quot;')}, "equipment")'` : '';
            const style = item ? 'cursor: pointer; border-bottom: 1px dotted var(--border-color);' : 'border-bottom: 1px dotted var(--border-color);';

            html += `
                <tr style="${style}" ${clickHandler}>
                    <td style="width: 20%; padding: 8px 5px; vertical-align: top;"><b>${slotName}:</b></td>
                    <td style="padding: 8px 5px; word-wrap: break-word; word-break: break-all; line-height: 1.4;">${itemName}</td>
                </tr>`;
        });
        html += '</table>';
        listContainer.innerHTML = html;
    }

    handlePlayerAttributeUpdate(playerData) {
        console.log('handlePlayerAttributeUpdate 收到数据:', playerData);

        // 检查是否包含分配的点数信息
        const player = playerData.player || playerData;
        const allocatedPoints = playerData.allocated_points;

        console.log('分配的点数:', allocatedPoints);

        const updatedPlayer = this.normalizePlayer(player);
        if (!updatedPlayer) return;

        this.currentPlayer = updatedPlayer;
        this.updatePlayerInfo(); // This should always run

        if (document.getElementById('selfView').style.display === 'block') {
            this.updateSelfView(); // Redraws the view with new numbers

            // 如果有分配点数信息，使用实际分配的点数显示效果
            if (allocatedPoints) {
                console.log('开始显示属性点分配效果');
                const allocatableAttributes = ['strength', 'agility', 'constitution', 'intelligence'];

                allocatableAttributes.forEach(attr => {
                    const pointsAllocated = allocatedPoints[attr] || 0;
                    console.log(`属性 ${attr}: 分配了 ${pointsAllocated} 点`);
                    if (pointsAllocated > 0) {
                        console.log(`调用 addAttributePointEffect(${attr}, ${pointsAllocated})`);
                        this.addAttributePointEffect(attr, pointsAllocated);
                    }
                });
            } else {
                console.log('没有分配点数信息，不显示效果');
            }
        } else {
            console.log('selfView 不可见，不显示效果');
        }
    }

    useItem(inventoryId) {
        // Optimistically find the item to show a message
        const item = this.inventory.backpack.find(i => i.inventory_id === inventoryId);
        if (item) {
            const itemName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name;
            let isSkillBook = false;
            if (item.effects) {
                try {
                    const effects = JSON.parse(item.effects);
                    isSkillBook = !!effects.learn_skill_id;
                } catch (e) { /* ignore */ }
            }
            const message = isSkillBook ? `正在学习 [${itemName}]...` : `正在使用 [${itemName}]...`;
            this.showActionMessage(message, 'info'); // Show an optimistic "info" message
        }
        
        // Send the action to the server
        this.sendMessage(MessageProtocol.C2S_USE_ITEM, { inventory_id: inventoryId });
    }

    equipItem(inventoryId, fromDetailView = false) {
        const item = this.inventory.backpack.find(i => i.inventory_id === inventoryId);
        if (item) {
            const itemName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name;
            this.showActionMessage(`已装备 [${itemName}]`, 'success');
        }

        this.sendMessage(MessageProtocol.C2S_EQUIP_ITEM, { inventory_id: inventoryId });
        
        if (fromDetailView) {
            this.hideItemDetailView();
        }
    }
    
    unequipItem(inventoryId) {
        const item = this.inventory.equipped.find(i => i.inventory_id === inventoryId);
        if (item) {
            const itemName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name;
            this.showActionMessage(`已卸下 [${itemName}]`, 'info');
        }

        this.sendMessage(MessageProtocol.C2S_UNEQUIP_ITEM, { inventory_id: inventoryId });

        // After unequipping, the detail view is no longer valid, so hide it.
        this.hideItemDetailView();
    }

    showItemDetails(item, origin = 'inventory') {
        this.clearMessages();

        // 如果当前已经在装备详情界面，并且来源是从玩家详情，需要保存导航栈
        if (origin === 'playerDetail' && this.itemDetailOrigin === 'refineLeaderboard') {
            // 保存当前的导航状态到栈中
            if (!this.navigationStack) {
                this.navigationStack = [];
            }
            this.navigationStack.push({
                view: 'itemDetailView',
                origin: this.itemDetailOrigin,
                item: this.currentDetailItem
            });
            console.log('保存导航状态到栈:', this.navigationStack);
        }

        this.itemDetailOrigin = origin;
        document.getElementById('inventoryView').style.display = 'none';
        document.getElementById('equipmentView').style.display = 'none';
        document.getElementById('playerDetailView').style.display = 'none';
        document.getElementById('monsterDetailView').style.display = 'none';
        document.getElementById('refineLeaderboardView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'block';

        const itemDetailError = document.getElementById('itemDetailError');
        itemDetailError.textContent = '';
        itemDetailError.style.borderColor = 'transparent';

        // Use display_name from instance_data if available, and parse custom suffix
        let displayName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : item.name;
        displayName = displayName.replace(/§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">');
        displayName = displayName.replace(/§/g, '</span>'); // Should be fine as it will close the last opened span
        
        // 显示物品名称，如果可堆叠则显示数量
        const itemNameElement = document.getElementById('itemDetailName');
        itemNameElement.innerHTML = displayName;
        if (item.stackable == 1 && item.quantity > 1) {
            const quantitySpan = document.createElement('span');
            quantitySpan.className = 'item-quantity';
            quantitySpan.style.fontSize = '14px';
            quantitySpan.style.color = '#666';
            quantitySpan.textContent = ` (x${item.quantity})`;
            itemNameElement.appendChild(quantitySpan);
        }
        
        let contentHtml = '';
        // 如果是来自凝练榜的装备，不显示分类信息
        if (origin !== 'refineLeaderboard') {
            contentHtml += `<p><b>分类:</b> ${this.categoryMap[item.category] || item.category}</p>`;
        }
        if (item.description && origin !== 'refineLeaderboard') {
            contentHtml += `<p style="color: #555;">${item.description}</p>`;
        }
        if (item.granted_job_name) {
            contentHtml += `<p><b>授予职业:</b> <span style="color: #28a745; font-weight: bold;">${item.granted_job_name}</span></p>`;
        }
        if (item.job_restriction_name) {
            contentHtml += `<p><b>职业要求:</b> <span style="color: #a02c2c; font-weight: bold;">[${item.job_restriction_name}]</span></p>`;
        }

        // 显示装备位置（如果是装备）
        if (item.slot) {
            const slotMap = {
                'Head': '头部', 'Neck': '颈部', 'LeftHand': '左手', 'RightHand': '右手',
                'TwoHanded': '双手', 'Body': '身体', 'Finger': '手指', 'Back': '背部'
            };
            const slotName = slotMap[item.slot] || item.slot;
            contentHtml += `<p><b>装备部位:</b> <span style="color: #6f42c1; font-weight: bold;">${slotName}</span></p>`;
        }

        // 如果是来自凝练榜的装备，只显示装备所有者信息
        if (item._refineLeaderboardData) {
            // 需要获取玩家ID来实现点击查看功能
            const ownerId = item._refineLeaderboardData.owner_id || item.player_id;
            if (ownerId) {
                contentHtml += `<p><b>装备所有者:</b> <a href="#" onclick="event.preventDefault(); game.showPlayerDetailsFromEquipment(${ownerId})" style="color: #e67e22; font-weight: bold; text-decoration: underline; cursor: pointer;" title="点击查看玩家详情">${item._refineLeaderboardData.owner}</a></p>`;
            } else {
                contentHtml += `<p><b>装备所有者:</b> <span style="color: #e67e22; font-weight: bold;">${item._refineLeaderboardData.owner}</span></p>`;
            }
        }

        contentHtml += `<hr class="section-divider">`;

        // 先解析物品效果
        let parsedEffects = null;
        if (item.effects) {
            try {
                parsedEffects = typeof item.effects === 'string' ? JSON.parse(item.effects) : item.effects;
            } catch (e) {
                console.error("解析物品效果失败:", e, item.effects);
            }
        }

        const stats = item.stats ? JSON.parse(item.stats) : null;

        if (stats) {
            contentHtml += '<b>装备属性:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            for (const [key, value] of Object.entries(stats)) {
                contentHtml += `<li>${this.playerAttributeMap[key] || key}: ${value > 0 ? '+' : ''}${value}</li>`;
            }
            contentHtml += '</ul>';
        }
        
        // 解析物品实例数据以查找凝练信息
        let instanceData = null;
        if (item.instance_data) {
            try {
                instanceData = JSON.parse(item.instance_data);
            } catch (e) {
                console.error("解析物品实例数据失败:", e, item.instance_data);
            }
        }

        // 添加凝练信息（如果有的话）
        if (instanceData) {
            contentHtml += this.getRefineInfoHtml(instanceData);
        }
        
        // Add socket display logic
        if (item.sockets > 0) {
            contentHtml += '<hr class="section-divider"><b>插槽:</b><div style="padding-left: 10px; margin-top: 5px;">';
            
            let instanceSockets = [];
            if (item.instance_data) {
                try {
                    const instanceData = JSON.parse(item.instance_data);
                    // Check for the new structure
                    if (instanceData.sockets && Array.isArray(instanceData.sockets) && (instanceData.sockets.length === 0 || typeof instanceData.sockets[0] === 'object')) {
                        instanceSockets = instanceData.sockets;
                    }
                } catch(e) { console.error("Error parsing instance_data sockets", e); }
            }

            // Fallback to create a placeholder structure if needed
            if (instanceSockets.length !== item.sockets) {
                instanceSockets = Array(Number(item.sockets)).fill(null).map(() => ({ gem_id: null, attempts: 0, gem_name: null }));
            }

            for (let i = 0; i < item.sockets; i++) {
                const socketInfo = instanceSockets[i] || { gem_id: null, attempts: 0, gem_name: null };
                
                if (socketInfo.gem_id) {
                    const gemName = socketInfo.gem_name || '未知宝石';
                    contentHtml += `<div style="margin: 3px 0;">插槽 ${i + 1}: <span style="color: #005a00;">[已镶嵌: ${gemName}]</span></div>`;
                } else {
                    const attemptsText = socketInfo.attempts > 0 ? ` <span style="font-size:12px; color: #6c8095;">(尝试: ${socketInfo.attempts}次)</span>` : '';
                    let socketLine = `<div style="margin: 3px 0;">插槽 ${i + 1}: [空]${attemptsText}`;
                    if (origin !== 'playerDetail' && origin !== 'monsterDetail') {
                        socketLine += ` <button class="btn-small" onclick="game.showGemSocketingView(${item.inventory_id}, ${i})">[镶嵌]</button>`;
                    }
                    socketLine += `</div>`;
                    contentHtml += socketLine;
                }
            }
            contentHtml += '</div>';
        }

        if (parsedEffects) {
            contentHtml += '<b>效果:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            for (const [key, value] of Object.entries(parsedEffects)) {
                if (key !== 'learn_skill_id') { // 不显示技能ID
                contentHtml += `<li>${this.playerAttributeMap[key] || key}: ${value > 0 ? '+' : ''}${value}</li>`;
                }
            }
            contentHtml += '</ul>';
        }
        
        // 检查是否是技能书
        const isSkillBook = parsedEffects && parsedEffects.learn_skill_id;
        
        // 如果是技能书，添加技能书描述
        if (isSkillBook) {
            contentHtml += `<p style="color: #4b0082; margin-top: 10px;">可学习新技能</p>`;
        }

        document.getElementById('itemDetailContent').innerHTML = contentHtml;
        
        const actionsContainer = document.getElementById('itemDetailActions');
        let actionsHtml = '';
        // 来自凝练榜的装备不显示操作按钮，因为这些装备属于其他玩家
        if (origin !== 'playerDetail' && origin !== 'monsterDetail' && origin !== 'refineLeaderboard') {
            if (item.is_equipped == 1) {
                actionsHtml = `<button class="btn-primary" onclick="game.unequipItem(${item.inventory_id})">[卸下]</button>`;
            } else if (item.slot) { // equippable if it has a slot
                actionsHtml = `<button class="btn-primary" onclick="game.equipItem(${item.inventory_id}, true)">[装备]</button>`;
            }
            
            // 如果是技能书，添加学习按钮，无论是否为消耗品
            if (isSkillBook) {
                if (actionsHtml) {
                    actionsHtml += ' ';
                }
                actionsHtml += `<button class="btn-primary skill-book-button" onclick="game.useItem(${item.inventory_id})">[学习]</button>`;
            }
            // 如果是普通消耗品，添加使用按钮
            else if (item.is_consumable == 1) {
                if (actionsHtml) {
                    actionsHtml += ' ';
                }
                actionsHtml += `<button class="btn-primary" onclick="game.useItem(${item.inventory_id})">[使用]</button>`;
            }
            
            // 如果不是装备中，添加丢弃按钮
            if (!item.is_equipped == 1) {
                if (actionsHtml) {
                    actionsHtml += ' ';
                }
                actionsHtml += `<button class="btn-primary" onclick="game.dropItem(${item.inventory_id})">[丢弃]</button>`;
            }

            if (item.is_bound == 0) {
                if (actionsHtml) {
                    actionsHtml += ' ';
                }
                actionsHtml += `<button class="btn-primary" onclick="game.bindItem(${item.inventory_id})">[绑定]</button>`;
            }
        }

        actionsContainer.innerHTML = actionsHtml;
        
        // 保存当前物品信息，以便在使用后更新
        this.currentDetailItem = item;
    }

    hideItemDetailView() {
        document.getElementById('itemDetailView').style.display = 'none';

        // 检查是否有导航栈，如果有则从栈中恢复
        if (this.navigationStack && this.navigationStack.length > 0) {
            const previousState = this.navigationStack.pop();
            console.log('从导航栈恢复状态:', previousState);

            if (previousState.view === 'itemDetailView') {
                // 恢复到之前的装备详情界面
                this.itemDetailOrigin = previousState.origin;
                this.currentDetailItem = previousState.item;
                this.showItemDetails(previousState.item, previousState.origin);
                return;
            }
        }

        // 正常的导航逻辑
        if (this.itemDetailOrigin === 'equipment') {
            this.showEquipmentView();
        } else if (this.itemDetailOrigin === 'playerDetail') {
            // This is a simple solution. For a full update, we might need to re-request the player data.
            document.getElementById('playerDetailView').style.display = 'block';
        } else if (this.itemDetailOrigin === 'monsterDetail') {
            document.getElementById('monsterDetailView').style.display = 'block';
        } else if (this.itemDetailOrigin === 'shop') {
            // 从商店物品详情返回到商店界面
            document.getElementById('buildingView').style.display = 'block';
        } else if (this.itemDetailOrigin === 'trade') {
            // 从交易物品详情返回到交易界面
            document.getElementById('tradeView').style.display = 'block';
        } else if (this.itemDetailOrigin === 'warehouse') {
            // 从仓库物品详情返回到仓库界面
            document.getElementById('buildingView').style.display = 'block';
        } else if (this.itemDetailOrigin === 'refineLeaderboard') {
            // 从装备详情返回到凝练榜界面
            this.showRefineLeaderboardView();
        } else {
            this.showInventoryView();
        }
        // this.itemDetailOrigin = null; // 暂时注释掉，让来源状态保持
    }

    showGemSocketingView(weaponInventoryId, socketIndex) {
        this.socketingContext = { weaponInventoryId, socketIndex };

        const gems = this.inventory.backpack.filter(item => item.category === 'Gem');
        const listEl = document.getElementById('gem-selection-list');
        listEl.innerHTML = '';

        if (gems.length === 0) {
            listEl.innerHTML = '<p style="text-align: center; color: #666;">背包里没有可镶嵌的宝石。</p>';
        } else {
            let html = '<table width="100%" style="border-spacing: 0 10px;">';
            gems.forEach(gem => {
                let effectsText = '';
                if (gem.effects) {
                    try {
                        const effects = JSON.parse(gem.effects);
                        effectsText = Object.entries(effects).map(([key, value]) => {
                            return `${this.playerAttributeMap[key] || key} ${value > 0 ? '+' : ''}${value}`;
                        }).join(', ');
                    } catch (e) {}
                }
                
                html += `
                    <tr>
                        <td>${gem.name} <span style="font-size: 12px; color: #555;">(${effectsText})</span></td>
                        <td style="text-align: right;">
                            <button class="btn-small" onclick="game.socketGem(${gem.inventory_id})">[镶嵌]</button>
                        </td>
                    </tr>`;
            });
            html += '</table>';
            listEl.innerHTML = html;
        }
        
        // 隐藏所有其他视图
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('inventoryView').style.display = 'none';
        document.getElementById('equipmentView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'none';
        document.getElementById('playerDetailView').style.display = 'none';
        document.getElementById('monsterDetailView').style.display = 'none';
        document.getElementById('buildingView').style.display = 'none';
        
        // 显示宝石镶嵌视图
        document.getElementById('gemSocketingView').style.display = 'block';
    }

    hideGemSocketingView() {
        document.getElementById('gemSocketingView').style.display = 'none';
        // Re-show the item detail view
        const weapon = this.inventory.equipped.find(i => i.inventory_id === this.socketingContext.weaponInventoryId) || this.inventory.backpack.find(i => i.inventory_id === this.socketingContext.weaponInventoryId);
        if (weapon) {
            this.showItemDetails(weapon, this.itemDetailOrigin);
        }
        this.socketingContext = null;
    }

    socketGem(gemInventoryId) {
        if (!this.socketingContext) return;
        const { weaponInventoryId, socketIndex } = this.socketingContext;
        
        // 查找武器和宝石信息
        const weapon = this.inventory.equipped.find(i => i.inventory_id === weaponInventoryId) || 
                      this.inventory.backpack.find(i => i.inventory_id === weaponInventoryId);
        const gem = this.inventory.backpack.find(i => i.inventory_id === gemInventoryId);
        
        // 保存宝石镶嵌信息，用于后续处理结果
        this.lastGemSocketingInfo = {
            weaponId: weaponInventoryId,
            weaponName: weapon ? weapon.name : '装备',
            gemId: gemInventoryId,
            gemName: gem ? gem.name : '宝石',
            socketIndex: socketIndex,
            effects: gem && gem.effects ? (typeof gem.effects === 'string' ? JSON.parse(gem.effects) : gem.effects) : {}
        };
        
        // 设置等待宝石镶嵌结果标志
        this.waitingForGemSocketingResult = true;
        
        // 发送宝石镶嵌请求
        this.sendMessage(MessageProtocol.C2S_SOCKET_GEM, {
            weapon_inventory_id: weaponInventoryId,
            gem_inventory_id: gemInventoryId,
            socket_index: socketIndex
        });
        
        // 隐藏宝石镶嵌选择界面，但不立即返回到物品详情
        // 等待服务器返回结果后再显示成功界面
        document.getElementById('gemSocketingView').style.display = 'none';
        
        // 显示镶嵌中的提示
        this.showGemSocketingProgress();
    }
    
    /**
     * 显示宝石镶嵌进行中的提示
     */
    showGemSocketingProgress() {
        // 创建一个镶嵌进度提示界面
        let progressView = document.getElementById('gemSocketingProgressView');
        if (!progressView) {
            progressView = document.createElement('div');
            progressView.id = 'gemSocketingProgressView';
            progressView.style.cssText = 'display:none; border: 1px solid var(--border-color); padding: 20px; text-align: center;';
            document.querySelector('.game-content').appendChild(progressView);
        }
        
        progressView.innerHTML = `
            <h4 style="margin-top:0; text-align: center;">宝石镶嵌中</h4>
            <div style="margin: 20px 0;">
                <div class="gem-progress-animation">
                    <div class="gem-icon">💎</div>
                    <div class="weapon-icon">⚔️</div>
                </div>
                <p>正在镶嵌宝石，请稍候...</p>
            </div>
        `;
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            .gem-progress-animation {
                display: flex;
                justify-content: center;
                align-items: center;
                margin: 20px 0;
                position: relative;
                height: 60px;
            }
            .gem-icon {
                font-size: 24px;
                position: absolute;
                animation: moveGem 2s infinite;
            }
            .weapon-icon {
                font-size: 30px;
                margin-left: 40px;
            }
            @keyframes moveGem {
                0% { transform: translateX(-40px); opacity: 1; }
                50% { transform: translateX(0); opacity: 0.7; }
                100% { transform: translateX(-40px); opacity: 1; }
            }
        `;
        document.head.appendChild(style);
        
        // 显示进度界面
        progressView.style.display = 'block';
    }
    
    /**
     * 显示宝石镶嵌成功界面
     * @param {Object} result - 镶嵌结果数据
     */
    showGemSocketingResult(result) {
        // 隐藏进度提示界面
        const progressView = document.getElementById('gemSocketingProgressView');
        if (progressView) {
            progressView.style.display = 'none';
        }
        
        // 创建结果界面
        let resultView = document.getElementById('gemSocketingResultView');
        if (!resultView) {
            resultView = document.createElement('div');
            resultView.id = 'gemSocketingResultView';
            resultView.style.cssText = 'display:none; border: 1px solid var(--border-color); padding: 20px;';
            document.querySelector('.game-content').appendChild(resultView);
        }
        
        // 获取武器和宝石信息
        const weaponName = result.weapon_name || '装备';
        const gemName = result.gem_name || '宝石';
        const success = result.success === true;
        const message = result.message || (success ? '镶嵌成功！' : '镶嵌失败！');
        const effects = result.effects || {};
        
        // 构建效果文本
        let effectsHtml = '';
        if (Object.keys(effects).length > 0 && success) {
            effectsHtml = '<div class="gem-effects"><h5>宝石效果:</h5><ul>';
            for (const [key, value] of Object.entries(effects)) {
                effectsHtml += `<li>${this.playerAttributeMap[key] || key}: ${value > 0 ? '+' : ''}${value}</li>`;
            }
            effectsHtml += '</ul></div>';
        }
        
        // 构建结果界面HTML
        resultView.innerHTML = `
            <h4 style="margin-top:0; text-align: center;">镶嵌${success ? '成功' : '失败'}</h4>
            <div class="gem-result ${success ? 'success' : 'failure'}">
                <div class="result-icon">${success ? '✅' : '❌'}</div>
                <p class="result-message">${message}</p>
                <div class="result-details">
                    <p><b>装备:</b> ${weaponName}</p>
                    <p><b>宝石:</b> ${gemName}</p>
                </div>
                ${effectsHtml}
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button class="btn-primary" onclick="game.closeGemSocketingResult()">[确定]</button>
            </div>
        `;
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .gem-result {
                text-align: center;
                padding: 15px;
                border-radius: 5px;
                margin: 10px 0;
            }
            .gem-result.success {
                background-color: rgba(76, 175, 80, 0.1);
                border: 1px solid #4caf50;
            }
            .gem-result.failure {
                background-color: rgba(244, 67, 54, 0.1);
                border: 1px solid #f44336;
            }
            .result-icon {
                font-size: 36px;
                margin-bottom: 10px;
            }
            .result-message {
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 15px;
            }
            .result-details {
                text-align: left;
                margin: 10px auto;
                max-width: 80%;
            }
            .gem-effects {
                text-align: left;
                margin: 15px auto;
                max-width: 80%;
                padding: 10px;
                background-color: rgba(255, 255, 255, 0.05);
                border-radius: 4px;
            }
            .gem-effects h5 {
                margin-top: 0;
                margin-bottom: 8px;
                color: #4caf50;
            }
            .gem-effects ul {
                margin: 0;
                padding-left: 20px;
            }
        `;
        document.head.appendChild(style);
        
        // 显示结果界面
        resultView.style.display = 'block';
    }
    
    /**
     * 关闭宝石镶嵌结果界面
     */
    closeGemSocketingResult() {
        // 隐藏结果界面
        const resultView = document.getElementById('gemSocketingResultView');
        if (resultView) {
            resultView.style.display = 'none';
        }
        
        // 如果有武器信息，重新显示物品详情
        if (this.socketingContext && this.socketingContext.weaponInventoryId) {
            const weapon = this.inventory.equipped.find(i => i.inventory_id === this.socketingContext.weaponInventoryId) || 
                          this.inventory.backpack.find(i => i.inventory_id === this.socketingContext.weaponInventoryId);
            if (weapon) {
                this.showItemDetails(weapon, this.itemDetailOrigin);
            }
        }
        
        // 清除上下文
        this.socketingContext = null;
    }

    handleMonsterDetails(payload) {
        const { monster } = payload;
        if (!monster) {
            this.addLog("无法获取敌方详情。");
            return;
        }

        document.getElementById('main-view').style.display = 'none';
        document.getElementById('monsterDetailView').style.display = 'block';
        
        document.getElementById('monsterDetailName').textContent = monster.name;

        let contentHtml = '';
        
        // Health status description
        const hpPerc = monster.hp / monster.max_hp;
        if (hpPerc >= 1) {
            contentHtml += `<p><em>它看起来非常健康，充满了活力。</em></p>`;
        } else if (hpPerc > 0.7) {
            contentHtml += `<p><em>它精力充沛，但身上有些许擦伤。</em></p>`;
        } else if (hpPerc > 0.3) {
            contentHtml += `<p><em>它看起来有些疲惫，身上有多处伤口。</em></p>`;
        } else {
            contentHtml += `<p><em>它已是强弩之末，摇摇欲坠。</em></p>`;
        }

        // Monster description
        if (monster.description) {
            contentHtml += `<p style="color: #555;">${monster.description}</p>`;
        }

        contentHtml += `<hr class="section-divider">`;

        // Equipment
        if (monster.equipment && monster.equipment.length > 0) {
            // contentHtml += '<b>装备:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            const slotMap = { 'Head': '头部', 'Neck': '颈部', 'Body': '身体', 'Back': '背部', 'LeftHand': '左手', 'RightHand': '右手', 'TwoHanded': '双手', 'Finger': '手指' };
            monster.equipment.forEach(item => {
                const itemJson = JSON.stringify(item).replace(/"/g, '&quot;');
                contentHtml += `<li>${slotMap[item.slot] || item.slot}: <a href="#" onclick='event.preventDefault(); game.showItemDetails(${itemJson}, "monsterDetail")'>${item.name}</a></li>`;
            });
            contentHtml += '</ul>';
        } else {
            contentHtml += '<p style="text-align: center; color: #6c8095; padding: 10px 0;">它似乎没有装备任何东西。</p>';
        }
        
        document.getElementById('monsterDetailContent').innerHTML = contentHtml;
    }

    showMonsterDetails(monsterId) {
        this.sendMessage(MessageProtocol.C2S_GET_MONSTER_DETAILS, { monster_id: monsterId });
    }

    hideMonsterDetailView() {
        document.getElementById('monsterDetailView').style.display = 'none';
        document.getElementById('itemDetailView').style.display = 'none'; // 强制关闭物品详情
        document.getElementById('main-view').style.display = 'block';
        this.itemDetailOrigin = null; // 强制重置来源
    }

    handlePlayerDetails(payload) {
        // 隐藏所有界面，然后显示玩家详情
        this.hideAllViews();
        document.getElementById('playerDetailView').style.display = 'block';

        const { player, equipment } = payload;
        const { username, attributes } = player;

        document.getElementById('playerDetailName').textContent = `${username} (Lv.${attributes.level || 1})`;
        
        let contentHtml = '';
        // 只显示职业描述，并添加一个底部分割线
        if (attributes.job_description) {
            contentHtml += `<p style="color: #555; font-size: 14px; padding-bottom: 10px; border-bottom: 1px dotted var(--border-color);"><i>${attributes.job_description}</i></p>`;
        }

        // 显示健康状况描述
        const hpPerc = attributes.max_hp > 0 ? (attributes.hp / attributes.max_hp) : 0;
        let statusDescription = '';
        if (attributes.hp <= 0) {
            statusDescription = '这是一具失去了生命气息的躯体，灵魂似乎已经离开。';
        } else if (hpPerc >= 1) {
            statusDescription = '他身姿挺拔，步伐轻盈，气色极佳，处于完全健康的状态。';
        } else if (hpPerc > 0.7) {
            statusDescription = '他行动自如，精神尚佳，虽有些微伤痕但不影响战斗能力。';
        } else if (hpPerc > 0.3) {
            statusDescription = '他身体多处可见伤口，呼吸略显沉重，行动中略显迟缓。';
        } else {
            statusDescription = '他伤痕累累，面色苍白，气息微弱，随时可能倒下。';
        }
        contentHtml += `<p style="margin-top:10px;"><i>${statusDescription}</i></p>`;
        
        // 添加善恶值描述
        const karma = attributes.karma || 0;
        let karmaDescription = '';
        let karmaStyle = '';
        
        if (karma <= -50) {
            karmaDescription = '一股令人战栗的邪恶气息从他身上散发出来，你感到本能的恐惧。他的双眼中闪烁着嗜血的暗红色光芒，周围的空气似乎因他的存在而凝固。这是一个彻头彻尾的恶徒，无数亡魂在为他哭泣。';
            karmaStyle = 'color: #660000; font-weight: bold;';
        } else if (karma <= -30) {
            karmaDescription = '从他身上散发出一种不祥的气息，让人感到明显的不安。他的眼神冷酷无情，嘴角时常浮现出残忍的笑容。你能感觉到他曾经造成过难以挽回的伤害和痛苦。';
            karmaStyle = 'color: #990000;';
        } else if (karma <= -10) {
            karmaDescription = '他的眼神中透露着一丝狡黠和危险，行为举止中带着些许不羁和轻蔑。从他身上能感受到一种不屑于遵守规则的叛逆气息。需要小心与他打交道。';
            karmaStyle = 'color: #cc3300;';
        } else if (karma < 0) {
            karmaDescription = '他看起来有些粗鲁无礼，偶尔会流露出一丝不友善的神情。也许只是性格古怪，但最好还是保持一定距离为妙。';
            karmaStyle = 'color: #cc6600;';
        } else if (karma == 0) {
            karmaDescription = '他看起来十分普通，既不显得特别友善也不显得特别冷漠。他的表情平淡，难以判断他的真实性格。';
            karmaStyle = 'color: #555555;';
        } else if (karma <= 10) {
            karmaDescription = '他看起来温和有礼，面带微笑，给人以和善的印象。虽然不算特别突出，但绝对是个值得信赖的人。';
            karmaStyle = 'color: #336633;';
        } else if (karma <= 30) {
            karmaDescription = '他的眼神中闪烁着善良的光芒，举手投足间都透露着对他人的关怀。他给人一种值得信任和依靠的感觉，仿佛随时准备伸出援手。';
            karmaStyle = 'color: #006600;';
        } else if (karma <= 50) {
            karmaDescription = '他的面容上有种令人心安的宁静，仿佛面对再大的风暴也能保持平和。他身上散发的温暖气息让人不自觉地想要亲近，是一位备受敬重的善良之人。';
            karmaStyle = 'color: #005500; font-weight: bold;';
        } else {
            karmaDescription = '一种神圣而纯净的光环似乎环绕着他，让人感到由衷的敬畏。他的目光深邃而慈悲，仿佛能洞察人心却不带评判。在他面前，你感到自己的灵魂被温柔地触动，他无疑是一位真正的圣者。';
            karmaStyle = 'color: #004400; font-weight: bold;';
        }
        
        contentHtml += `<p style="margin-top:5px; ${karmaStyle}"><i>${karmaDescription}</i></p>`;
        
        contentHtml += '<hr class="section-divider">';
        if (equipment.length === 0) {
            contentHtml += '<p style="text-align: center; color: #6c8095; padding: 20px 0;">TA似乎没有装备任何东西。</p>';
        } else {
            // contentHtml += '<b style="display:block; margin-bottom: 5px;">装备:</b>';
            const slotOrder = { 'Head': '头部', 'Neck': '颈部', 'Body': '身体', 'Back': '背部', 'LeftHand': '左手', 'RightHand': '右手', 'TwoHanded': '双手', 'Finger': '手指' };
            const equippedMap = new Map();
            equipment.forEach(item => equippedMap.set(item.slot, item));
            
            let displaySlots = Object.keys(slotOrder);
            if (equippedMap.has('TwoHanded')) {
                displaySlots = displaySlots.filter(slot => slot !== 'LeftHand' && slot !== 'RightHand');
            } else {
                displaySlots = displaySlots.filter(slot => slot !== 'TwoHanded');
            }

            contentHtml += '<table width="100%" style="border-collapse: collapse;">';
            displaySlots.forEach(slotKey => {
                const item = equippedMap.get(slotKey);
                if (!item) return; // 只显示有装备的栏位
                
                const itemJson = JSON.stringify(item).replace(/"/g, '&quot;');
                const displayNameHTML = item.instance_data && JSON.parse(item.instance_data).display_name 
                    ? JSON.parse(item.instance_data).display_name.replace(/§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">').replace(/§/g, '</span>')
                    : item.name;
                
                const itemName = `<span style="color: #005a00;">${displayNameHTML}</span>`;
                const slotName = slotOrder[slotKey];
                const clickHandler = `onclick='game.showItemDetails(${itemJson}, "playerDetail")'`;
                const style = 'cursor: pointer; border-bottom: 1px dotted var(--border-color);';

                contentHtml += `<tr style="${style}" ${clickHandler}><td style="width: 20%; padding: 3px 5px;"><b>${slotName}:</b></td><td style="padding: 3px 5px;">${itemName}</td></tr>`;
            });
            contentHtml += '</table>';
        }

        document.getElementById('playerDetailContent').innerHTML = contentHtml;

        const detailContainer = document.getElementById('playerDetailContent');

                    // 添加PVP挑战按钮
            // 确保当前玩家信息存在，并且查看的不是玩家自己，并且双方都是存活状态
            if (this.currentPlayer && 
                payload.player.id !== this.currentPlayer.id && 
                this.currentPlayer.attributes.hp > 0 && 
                payload.player.attributes.hp > 0) {
                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'player-detail-actions';
                
                // 添加挑战按钮
                const challengeBtn = document.createElement('button');
                challengeBtn.className = 'btn-primary pvp-challenge-btn';
                challengeBtn.textContent = '挑战';
                challengeBtn.onclick = () => this.challengePlayerToPvp(payload.player.id);
                actionsContainer.appendChild(challengeBtn);

                // 添加交易按钮 - 只需要检查是否在同一场景
                if (this.isPlayerInSameScene(payload.player.id)) {
                    const tradeBtn = document.createElement('button');
                    tradeBtn.className = 'btn-primary trade-btn';
                    tradeBtn.textContent = '交易';
                    tradeBtn.style.marginLeft = '10px';
                    tradeBtn.onclick = () => this.requestTrade(payload.player.id);
                    actionsContainer.appendChild(tradeBtn);
                }

                
                // 添加私聊按钮 等级不够的将屏蔽按钮
                console.log('Creating private chat button for player:', payload.player.id, payload.player.username);

                // 检查当前玩家等级是否满足私聊要求
                const currentLevel = this.currentPlayer?.attributes?.level || 1;
                const requiredLevel = 10; // 私聊所需等级

                if (currentLevel >= requiredLevel) {
                    // 等级足够，显示可用的私聊按钮
                    const privateChatBtn = document.createElement('button');
                    privateChatBtn.className = 'btn-primary private-chat-btn';
                    privateChatBtn.textContent = '私聊';
                    privateChatBtn.style.marginLeft = '10px';
                    privateChatBtn.onclick = () => {
                        console.log('Private chat button clicked');
                        this.openPrivateChat(payload.player.id, payload.player.username);
                    };
                    actionsContainer.appendChild(privateChatBtn);
                    console.log('Private chat button added to DOM');
                } else {
                    // 等级不够，显示禁用的私聊按钮
                    const privateChatBtn = document.createElement('button');
                    privateChatBtn.className = 'btn-primary private-chat-btn-disabled';
                    privateChatBtn.textContent = '私聊';
                    privateChatBtn.style.marginLeft = '10px';
                    privateChatBtn.style.opacity = '0.5';
                    privateChatBtn.style.cursor = 'not-allowed';
                    privateChatBtn.disabled = true;
                    privateChatBtn.title = `需要达到${requiredLevel}级才能使用私聊功能`;
                    privateChatBtn.onclick = () => {
                        this.showActionMessage(`需要达到${requiredLevel}级才能使用私聊功能`, 'error');
                    };
                    actionsContainer.appendChild(privateChatBtn);
                    console.log('Private chat button added to DOM (disabled due to level requirement)');
                }
                
                // 添加攻击按钮，传递目标玩家的善恶值用于前端判断
                const attackBtn = document.createElement('button');
                attackBtn.className = 'btn-primary btn-danger pvp-attack-btn';
                attackBtn.textContent = '攻击';
                attackBtn.style.marginLeft = '10px';

                // 获取目标玩家的善恶值
                const targetKarma = payload.player.attributes.karma || 0;

                // 在安全区对红名玩家显示特殊提示
                if (this.currentScene && this.currentScene.is_safe_zone === 1 && targetKarma < 0) {
                    attackBtn.textContent = '制裁';
                    attackBtn.title = '这是一个红名玩家，即使在安全区也可以攻击';
                } else if (this.currentScene && this.currentScene.is_safe_zone === 1 && targetKarma >= 0) {
                    attackBtn.textContent = '攻击';
                    attackBtn.title = '安全区内不能攻击善良玩家';
                    attackBtn.style.opacity = '0.5';
                    attackBtn.style.cursor = 'not-allowed';
                }

                attackBtn.onclick = () => this.attackPlayer(payload.player.id, targetKarma);
                actionsContainer.appendChild(attackBtn);
                
                detailContainer.appendChild(actionsContainer);
        } else if (this.currentPlayer && 
                  payload.player.id !== this.currentPlayer.id && 
                  (this.currentPlayer.attributes.hp <= 0 || payload.player.attributes.hp <= 0)) {
                // 如果任一方已死亡，显示无法挑战的提示
                let dietips = '';
                //判断死亡状态
                if (this.currentPlayer.attributes.hp <= 0) {
                    dietips = '你已死亡';
                } else {
                    dietips = '对方已死亡';
                }
                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'player-detail-actions';
                
                const disabledBtn = document.createElement('button');
                disabledBtn.className = 'btn-disabled';
                disabledBtn.textContent = dietips;
                disabledBtn.title = dietips;
                disabledBtn.disabled = true;
                
                actionsContainer.appendChild(disabledBtn);
                detailContainer.appendChild(actionsContainer);
        } else if (this.currentPlayer && payload.player.id === this.currentPlayer.id){
                const actionsContainer = document.createElement('div');
                actionsContainer.className = 'player-detail-actions';

                const disabledBtn = document.createElement('button');
                disabledBtn.className = 'btn-disabled';
                disabledBtn.textContent = '你正在查看自己';
                disabledBtn.title = '你正在查看自己';
                disabledBtn.disabled = true;
                
                actionsContainer.appendChild(disabledBtn);
                detailContainer.appendChild(actionsContainer);
        }
    }

    showPlayerDetails(playerId) {
        // 记录当前显示的界面，用于返回时导航
        this.playerDetailOrigin = this.getCurrentActiveView();
        console.log('打开玩家详情，来源界面:', this.playerDetailOrigin);

        this.sendMessage(MessageProtocol.C2S_GET_PLAYER_DETAILS, { player_id_to_view: playerId });
    }

    // 从装备详情界面查看玩家详情
    showPlayerDetailsFromEquipment(playerId) {
        // 记录来源为装备详情界面
        this.playerDetailOrigin = 'itemDetailView';
        console.log('从装备详情查看玩家详情，玩家ID:', playerId);

        this.sendMessage(MessageProtocol.C2S_GET_PLAYER_DETAILS, { player_id_to_view: playerId });
    }

    // 获取当前活跃的界面
    getCurrentActiveView() {
        const views = [
            { id: 'chat-view', name: 'chat' },
            { id: 'private-chat-view', name: 'private-chat' },
            { id: 'selfView', name: 'self' },
            { id: 'inventoryView', name: 'inventory' },
            { id: 'equipmentView', name: 'equipment' },
            { id: 'skillsView', name: 'skills' },
            { id: 'main-view', name: 'main' }
        ];

        for (const view of views) {
            const element = document.getElementById(view.id);
            if (element && element.style.display === 'block') {
                return view.name;
            }
        }

        return 'main'; // 默认返回主界面
    }

    // 打开私聊界面
    openPrivateChat(playerId, playerName) {
        console.log('openPrivateChat called with:', { playerId, playerName });

        // 检查等级限制
        console.log('Player data structure:', this.currentPlayer);
        console.log('Player attributes:', this.currentPlayer?.attributes);

        const currentLevel = this.currentPlayer?.attributes?.level || 1;
        const requiredLevel = 10; // 假设需要5级才能私聊

        console.log('Level check:', { currentLevel, requiredLevel, playerExists: !!this.currentPlayer });

        if (currentLevel < requiredLevel) {
            console.log('Level too low, showing error message');
            this.showActionMessage(`需要达到${requiredLevel}级才能使用私聊功能`, 'error');
            return;
        }

        console.log('Level check passed, initializing private chat');

        // 初始化私聊相关变量
        this.currentPrivateChatContact = String(playerId); // 确保是字符串
        this.currentPrivateChatContactName = playerName;
        this.privateChatMessageCount = 0;
        this.privateChatLastMinute = Math.floor(Date.now() / 60000);

        console.log('Creating private chat view');

        // 创建或显示私聊界面
        this.createPrivateChatView();
        this.showPrivateChatView();

        // 加载聊天历史
        this.loadPrivateChatHistory(playerId);

        console.log('Private chat initialization complete');
    }

    // 创建私聊界面
    createPrivateChatView() {
        console.log('=== 创建私聊界面 ===');
        // 检查是否已存在
        let privateChatView = document.getElementById('private-chat-view');
        if (privateChatView) {
            console.log('私聊界面已存在，跳过创建');
            return;
        }

        console.log('创建新的私聊界面');

        privateChatView = document.createElement('div');
        privateChatView.id = 'private-chat-view';
        privateChatView.className = 'view';
        privateChatView.style.display = 'none';

        privateChatView.innerHTML = `
            <div class="private-chat-container">
                <!-- 联系人列表视图 -->
                <div id="private-chat-contacts-view" class="private-chat-contacts-view">
                    <div class="private-chat-header">
                        <h3>私聊</h3>
                        <button class="btn-primary" id="private-chat-back-to-main">[返回]</button>
                    </div>
                    <div class="private-chat-contacts-list" id="private-chat-contacts-list">
                        <div class="no-contacts-message">暂无聊天记录</div>
                    </div>
                </div>

                <!-- 聊天对话视图 -->
                <div id="private-chat-conversation-view" class="private-chat-conversation-view" style="display: none;">
                    <div class="private-chat-header">
                        <button class="btn-primary" id="private-chat-back-to-contacts">[返回]</button>
                        <h3 id="private-chat-contact-name">聊天</h3>
                        <button class="btn-danger" id="private-chat-delete-contact" title="删除联系人">[删除]</button>
                    </div>
                    <div class="private-chat-messages" id="private-chat-messages">
                        <div class="chat-message system">暂无聊天记录</div>
                    </div>
                    <div class="private-chat-input-area" id="private-chat-input-area">
                        <input type="text" id="private-chat-input" placeholder="输入消息..." maxlength="100">
                        <span id="private-chat-char-count">0/100</span>
                        <button id="private-chat-send-btn" class="btn-primary">发送</button>
                    </div>
                    <div class="private-chat-rate-limit" id="private-chat-rate-limit" style="display: none;">
                        <small>每分钟最多发送` + privateChatRateLimit + `条消息</small>
                    </div>
                </div>
            </div>
        `;

        document.getElementById('gameContent').appendChild(privateChatView);

        // 延迟绑定事件监听器，确保DOM元素已完全渲染
        setTimeout(() => {
            this.setupPrivateChatEventListeners();
        }, 0);

        console.log('私聊界面创建完成，检查元素:');
        console.log('private-chat-rate-limit元素:', document.getElementById('private-chat-rate-limit'));
    }

    // 设置私聊界面事件监听器
    setupPrivateChatEventListeners() {
        console.log('=== 设置私聊事件监听器 ===');

        // 返回主界面按钮
        const backToMainBtn = document.getElementById('private-chat-back-to-main');
        if (backToMainBtn) {
            backToMainBtn.addEventListener('click', () => {
                this.hidePrivateChatView();
            });
            console.log('返回主界面按钮事件已绑定');
        } else {
            console.error('找不到返回主界面按钮');
        }

        // 返回联系人列表按钮
        const backToContactsBtn = document.getElementById('private-chat-back-to-contacts');
        if (backToContactsBtn) {
            backToContactsBtn.addEventListener('click', () => {
                this.showPrivateChatContactsList();
            });
            console.log('返回联系人列表按钮事件已绑定');
        } else {
            console.error('找不到返回联系人列表按钮');
        }

        // 删除联系人按钮
        const deleteContactBtn = document.getElementById('private-chat-delete-contact');
        if (deleteContactBtn) {
            deleteContactBtn.addEventListener('click', () => {
                this.confirmDeleteChatContact();
            });
            console.log('删除联系人按钮事件已绑定');
        } else {
            console.error('找不到删除联系人按钮');
        }

        // 发送消息按钮
        const sendBtn = document.getElementById('private-chat-send-btn');
        if (sendBtn) {
            // 移除可能存在的旧事件监听器
            sendBtn.replaceWith(sendBtn.cloneNode(true));
            const newSendBtn = document.getElementById('private-chat-send-btn');

            newSendBtn.addEventListener('click', () => {
                console.log('私聊发送按钮被点击');
                this.sendPrivateChatMessage();
            });
            console.log('发送消息按钮事件已绑定');
        } else {
            console.error('找不到发送消息按钮');
        }

        // 输入框回车发送
        const privateChatInput = document.getElementById('private-chat-input');
        if (privateChatInput) {
            // 移除可能存在的旧事件监听器
            privateChatInput.replaceWith(privateChatInput.cloneNode(true));
            const newPrivateChatInput = document.getElementById('private-chat-input');

            newPrivateChatInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    console.log('私聊输入框回车键被按下');
                    this.sendPrivateChatMessage();
                }
            });

            // 字符计数
            newPrivateChatInput.addEventListener('input', () => {
                const length = newPrivateChatInput.value.length;
                const charCount = document.getElementById('private-chat-char-count');
                if (charCount) {
                    charCount.textContent = `${length}/100`;

                    if (length > 90) {
                        charCount.style.color = '#ff6b6b';
                    } else {
                        charCount.style.color = '';
                    }
                }
            });
            console.log('输入框事件已绑定');
        } else {
            console.error('找不到私聊输入框');
        }

        console.log('私聊事件监听器设置完成');
    }

    // 显示私聊界面
    showPrivateChatView() {
        console.log('=== 显示私聊界面 ===');

        // 检查等级要求（从主界面按钮调用时）
        if (!this.currentPrivateChatContact) {
            const currentLevel = this.currentPlayer?.attributes?.level || 1;
            const requiredLevel = chatLevel; // 需要5级才能使用私聊功能

            if (currentLevel < requiredLevel) {
                this.showActionMessage(`需要达到${requiredLevel}级才能使用私聊功能`, 'error');
                return;
            }
        }

        // 创建私聊界面（如果不存在）
        this.createPrivateChatView();

        this.hideAllViews();
        document.getElementById('private-chat-view').style.display = 'block';

        // 如果有当前联系人，直接显示对话视图；否则显示联系人列表
        if (this.currentPrivateChatContact) {
            this.showPrivateChatConversation();
        } else {
            this.showPrivateChatContactsList();
        }
    }

    // 隐藏私聊界面
    hidePrivateChatView() {
        document.getElementById('private-chat-view').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        this.currentPrivateChatContact = null;
        this.currentPrivateChatContactName = null;
    }

    // 显示联系人列表
    showPrivateChatContactsList() {
        document.getElementById('private-chat-contacts-view').style.display = 'block';
        document.getElementById('private-chat-conversation-view').style.display = 'none';

        // 隐藏删除按钮（在联系人列表中不显示）
        const deleteBtn = document.getElementById('private-chat-delete-contact');
        if (deleteBtn) {
            deleteBtn.style.display = 'none';
        }

        this.loadPrivateChatContacts();
    }

    // 显示对话界面
    showPrivateChatConversation() {
        document.getElementById('private-chat-contacts-view').style.display = 'none';
        document.getElementById('private-chat-conversation-view').style.display = 'block';

        // 设置标题
        const contactNameElement = document.getElementById('private-chat-contact-name');
        contactNameElement.textContent = this.currentPrivateChatContactName || '聊天';

        // 显示或隐藏删除按钮
        const deleteBtn = document.getElementById('private-chat-delete-contact');
        if (deleteBtn) {
            deleteBtn.style.display = this.currentPrivateChatContact ? 'inline-block' : 'none';
        }
    }

    // 发送私聊消息
    sendPrivateChatMessage() {
        console.log('=== sendPrivateChatMessage 被调用 ===');
        const input = document.getElementById('private-chat-input');

        // 如果输入框不存在（可能被错误提示替换了），直接返回
        if (!input) {
            console.log('输入框不存在，可能正在显示错误提示');
            return;
        }

        const message = input.value.trim();

        if (!message || !this.currentPrivateChatContact) return;

        // 检查发送频率限制
        const currentMinute = Math.floor(Date.now() / 60000);
        if (currentMinute === this.privateChatLastMinute) {
            if (this.privateChatMessageCount >= privateChatRateLimit) {
                this.showPrivateChatRateLimit();
                return;
            }
            this.privateChatMessageCount++;
        } else {
            this.privateChatLastMinute = currentMinute;
            this.privateChatMessageCount = 1;
        }

        // 发送消息到服务器
        this.sendMessage(MessageProtocol.C2S_SEND_PRIVATE_CHAT, {
            receiver_id: this.currentPrivateChatContact,
            message: message
        });

        // 清空输入框
        input.value = '';
        document.getElementById('private-chat-char-count').textContent = '0/100';
        document.getElementById('private-chat-char-count').style.color = '';

        // 隐藏频率限制提示
        this.hidePrivateChatRateLimit();

        // 发送消息后立即刷新聊天记录
        setTimeout(() => {
            this.loadPrivateChatHistory(this.currentPrivateChatContact);
        }, 100); // 稍微延迟一下确保服务器处理完成
    }

    // 显示发送频率限制提示
    showPrivateChatRateLimit() {
        console.log('=== 显示频率限制提示 ===');
        let inputArea = document.getElementById('private-chat-input-area');

        // 如果通过ID找不到，尝试通过class查找
        if (!inputArea) {
            inputArea = document.querySelector('.private-chat-input-area');
            console.log('通过class查找到输入区域:', inputArea);
        } else {
            console.log('通过ID查找到输入区域:', inputArea);
        }

        if (inputArea) {
            console.log('替换输入区域为频率限制提示');

            // 保存原始内容
            if (!this.originalInputAreaContent) {
                this.originalInputAreaContent = inputArea.innerHTML;
            }

            // 替换为错误提示，保持与输入框相同的高度和布局
            inputArea.innerHTML = `
                <div style="
                    background: rgba(255, 107, 107, 0.2);
                    color: #ff6b6b;
                    padding: 8px 12px;
                    text-align: center;
                    font-weight: bold;
                    border: 1px solid #ff6b6b;
                    border-radius: 4px;
                    animation: shake 0.5s ease-in-out;
                    height: 40px;
                    line-height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    font-size: 14px;
                ">
                    ⚠️ 发送太频繁！每分钟最多发送` + privateChatRateLimit + `条消息
                </div>
            `;

            // 3秒后恢复输入框
            setTimeout(() => {
                console.log('恢复输入区域');
                this.restoreInputArea();
            }, 3000);
        } else {
            console.log('❌ 找不到输入区域元素');
        }
    }

    // 隐藏发送频率限制提示
    hidePrivateChatRateLimit() {
        const rateLimitElement = document.getElementById('private-chat-rate-limit');
        if (rateLimitElement) {
            rateLimitElement.style.display = 'none';
        }
    }

    // 恢复输入区域
    restoreInputArea() {
        let inputArea = document.getElementById('private-chat-input-area');

        // 如果通过ID找不到，尝试通过class查找
        if (!inputArea) {
            inputArea = document.querySelector('.private-chat-input-area');
        }

        if (inputArea && this.originalInputAreaContent) {
            console.log('恢复输入区域内容');
            inputArea.innerHTML = this.originalInputAreaContent;

            // 延迟重新绑定事件监听器，确保DOM元素已完全渲染
            setTimeout(() => {
                this.setupPrivateChatEventListeners();
            }, 0);
        }
    }

    // 确保元素在可视区域内
    ensureElementVisible(element) {
        if (!element) return;

        // 检查元素是否在视窗内
        const rect = element.getBoundingClientRect();
        const viewportHeight = window.innerHeight;

        // 如果元素不在视窗内，滚动到元素位置
        if (rect.top < 0 || rect.bottom > viewportHeight) {
            console.log('元素不在可视区域，滚动到元素位置');
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }

        // 同时确保私聊对话容器滚动到底部
        const conversationView = document.getElementById('private-chat-conversation-view');
        if (conversationView) {
            setTimeout(() => {
                conversationView.scrollTop = conversationView.scrollHeight;
            }, 100);
        }
    }

    // 显示私聊错误消息
    showPrivateChatError(message) {
        console.log('=== 显示私聊错误消息 ===');
        console.log('错误消息:', message);

        let inputArea = document.getElementById('private-chat-input-area');

        // 如果通过ID找不到，尝试通过class查找
        if (!inputArea) {
            inputArea = document.querySelector('.private-chat-input-area');
            console.log('通过class查找到输入区域:', inputArea);
        } else {
            console.log('通过ID查找到输入区域:', inputArea);
        }

        if (inputArea) {
            console.log('替换输入区域为错误提示');

            // 保存原始内容
            if (!this.originalInputAreaContent) {
                this.originalInputAreaContent = inputArea.innerHTML;
            }

            // 替换为错误提示，保持与输入框相同的高度和布局
            inputArea.innerHTML = `
                <div style="
                    background: rgba(255, 107, 107, 0.2);
                    color: #ff6b6b;
                    padding: 8px 12px;
                    text-align: center;
                    font-weight: bold;
                    border: 1px solid #ff6b6b;
                    border-radius: 4px;
                    animation: shake 0.5s ease-in-out;
                    height: 40px;
                    line-height: 24px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    box-sizing: border-box;
                    font-size: 14px;
                    word-wrap: break-word;
                    overflow: hidden;
                ">
                    ❌ ${message}
                </div>
            `;

            // 5秒后恢复输入框
            setTimeout(() => {
                console.log('恢复输入区域');
                this.restoreInputArea();
            }, 5000);
        } else {
            console.log('找不到输入区域元素，使用通用错误提示');
            // 如果没有输入区域元素，使用通用的错误提示
            this.showActionMessage(message, 'error');
        }
    }

    // 处理私聊消息
    handlePrivateChatMessage(payload) {
        const { senderId, senderName, receiverId, message, sentAt } = payload;

        console.log('=== 处理私聊消息 ===');
        console.log('消息数据:', { senderId, senderName, receiverId, message, sentAt });

        // 判断是否是自己发送的消息
        const currentPlayerId = this.currentPlayer?.id;
        const isOwn = senderId == currentPlayerId;

        // 确定对话的另一方（联系人）
        const contactId = String(isOwn ? receiverId : senderId);
        const contactName = isOwn ? payload.receiverName || '未知用户' : senderName;

        // 检查私聊界面是否打开且是当前联系人
        const privateChatView = document.getElementById('private-chat-view');
        const isPrivateChatOpen = privateChatView && privateChatView.style.display === 'block';
        const isCurrentContact = this.currentPrivateChatContact == contactId;

        console.log('界面状态:', { isPrivateChatOpen, isCurrentContact, contactId, currentContact: this.currentPrivateChatContact });

        // 如果私聊界面未打开或不是当前联系人，显示通知
        if (!isPrivateChatOpen || !isCurrentContact) {
            console.log('显示通知');
            this.showChatNotification(senderName, message, 'private');
        }

        // 如果是当前联系人且私聊界面打开，刷新聊天记录
        if (isCurrentContact && isPrivateChatOpen) {
            console.log('刷新当前聊天记录');
            // 稍微延迟一下确保消息已保存到数据库
            setTimeout(() => {
                this.loadPrivateChatHistory(contactId);
            }, 100);
        }

        // 如果当前在私聊界面的联系人列表中，且不是自己发送的消息，刷新联系人列表以更新未读徽章
        const privateChatContactsList = document.getElementById('private-chat-contacts-list');
        const privateChatConversation = document.getElementById('private-chat-conversation');
        const isInContactsList = privateChatContactsList && privateChatContactsList.style.display !== 'none';
        const isInConversation = privateChatConversation && privateChatConversation.style.display !== 'none';

        // 只有接收到别人的消息时才刷新联系人列表（不是自己发送的消息）
        if (isPrivateChatOpen && isInContactsList && !isInConversation && !isOwn) {
            console.log('刷新联系人列表以更新未读徽章（接收到新消息）');
            setTimeout(() => {
                this.loadPrivateChatContacts();
            }, 200);
        }

        console.log('=== 私聊消息处理完成 ===');
    }

    // 添加私聊消息到界面
    addPrivateChatMessageToView(senderId, senderName, message, sentAt, isOwn) {
        const messagesContainer = document.getElementById('private-chat-messages');
        if (!messagesContainer) return;

        // 移除"暂无聊天记录"提示
        const systemMessages = messagesContainer.querySelectorAll('.chat-message.system');
        systemMessages.forEach(msg => {
            if (msg.textContent === '暂无聊天记录') {
                messagesContainer.removeChild(msg);
            }
        });

        const messageElement = document.createElement('div');
        messageElement.className = `chat-message ${isOwn ? 'own' : ''}`;

        const timestamp = new Date(sentAt).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});
        messageElement.innerHTML = `
            <div class="chat-time">${timestamp}</div>
            <div class="chat-name-line">
                <span class="chat-name ${isOwn ? 'own' : 'clickable'}" ${!isOwn ? `data-player-id="${senderId}"` : ''}>${senderName}:</span>
                <span class="chat-content">${message}</span>
            </div>
        `;

        // 如果不是自己的消息，添加点击查看详情功能
        if (!isOwn) {
            const playerNameElement = messageElement.querySelector('.chat-name');
            playerNameElement.addEventListener('click', () => {
                this.showPlayerDetails(senderId);
            });
        }

        messagesContainer.appendChild(messageElement);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;
    }

    // 加载私聊历史
    loadPrivateChatHistory(contactId) {
        this.sendMessage(MessageProtocol.C2S_GET_PRIVATE_CHAT, {
            contact_id: contactId
        });
    }

    // 加载私聊联系人列表
    loadPrivateChatContacts() {
        console.log('=== 加载联系人列表 ===');
        console.log('注意：这里只是加载列表，不应该标记任何消息为已读');
        this.sendMessage(MessageProtocol.C2S_GET_CHAT_CONTACTS);
    }

    // 处理私聊历史响应
    handlePrivateChatHistory(payload) {
        const messagesContainer = document.getElementById('private-chat-messages');
        if (!messagesContainer) return;

        // 清空现有消息
        messagesContainer.innerHTML = '';

        if (!payload.messages || payload.messages.length === 0) {
            messagesContainer.innerHTML = '<div class="chat-message system">暂无聊天记录</div>';
            return;
        }

        // 显示历史消息
        payload.messages.forEach(msg => {
            const currentPlayerId = this.currentPlayer?.id;
            const isOwn = msg.senderId == currentPlayerId;
            this.addPrivateChatMessageToView(msg.senderId, msg.senderName, msg.message, msg.sentAt, isOwn);
        });

        // 滚动到底部显示最新消息
        if (messagesContainer) {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }
    }

    // 处理联系人列表响应
    handleChatContactsList(payload) {
        console.log('=== 处理联系人列表 ===');
        console.log('联系人数据:', payload);

        const contactsList = document.getElementById('private-chat-contacts-list');
        if (!contactsList) return;

        // 清空现有联系人
        contactsList.innerHTML = '';

        if (!payload.contacts || payload.contacts.length === 0) {
            contactsList.innerHTML = '<div class="no-contacts-message">暂无聊天记录</div>';
            return;
        }

        // 显示联系人列表
        payload.contacts.forEach(contact => {
            console.log('添加联系人:', contact);
            this.addContactToPrivateChatList(contact);
        });
    }

    // 添加联系人到私聊列表
    addContactToPrivateChatList(contact) {
        console.log('=== 添加联系人到列表 ===');
        console.log('联系人信息:', contact);
        console.log('未读计数:', contact.unreadCount, '类型:', typeof contact.unreadCount);

        const contactsList = document.getElementById('private-chat-contacts-list');
        if (!contactsList) return;

        // 移除"暂无聊天记录"提示
        const noContactsMsg = contactsList.querySelector('.no-contacts-message');
        if (noContactsMsg) {
            contactsList.removeChild(noContactsMsg);
        }

        const contactElement = document.createElement('div');
        contactElement.className = 'private-chat-contact-item';
        contactElement.dataset.contactId = contact.contactId;

        const unreadBadge = contact.unreadCount > 0 ?
            `<span class="private-chat-unread-badge">${contact.unreadCount}</span>` : '';

        console.log('未读徽章HTML:', unreadBadge);

        const lastMessageTime = new Date(contact.lastMessageTime).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});

        contactElement.innerHTML = `
            <div class="private-chat-contact-info">
                <div class="private-chat-contact-name">${contact.contactName}</div>
                <div class="private-chat-contact-preview">${contact.lastMessage || '暂无消息'}</div>
            </div>
            <div>
                <div class="private-chat-contact-time">${lastMessageTime}</div>
                ${unreadBadge}
            </div>
        `;

        // 添加点击事件
        contactElement.addEventListener('click', () => {
            this.selectPrivateChatContact(contact.contactId, contact.contactName);
        });

        contactsList.appendChild(contactElement);
    }

    // 选择私聊联系人
    selectPrivateChatContact(contactId, contactName) {
        console.log('=== 选择联系人 ===');
        console.log('联系人ID:', contactId, '联系人名称:', contactName);
        console.log('现在将标记该联系人的消息为已读');

        this.currentPrivateChatContact = String(contactId); // 确保是字符串
        this.currentPrivateChatContactName = contactName;

        // 切换到对话视图
        this.showPrivateChatConversation();

        // 加载聊天历史
        this.loadPrivateChatHistory(contactId);

        // 标记消息为已读
        console.log('发送标记已读消息:', contactId);
        this.sendMessage(MessageProtocol.C2S_MARK_MESSAGES_READ, {
            contact_id: contactId
        });
    }

    // 更新联系人列表中的联系人信息
    updatePrivateChatContactInList(contactId, contactName, lastMessage, sentAt, hasUnread) {
        // 这个方法用于实时更新联系人列表，当收到新消息时调用
        // 如果当前在联系人列表界面，则更新显示
        const contactsList = document.getElementById('private-chat-contacts-list');
        if (!contactsList) return;

        const contactElement = contactsList.querySelector(`[data-contact-id="${contactId}"]`);
        if (contactElement) {
            // 更新现有联系人
            const previewElement = contactElement.querySelector('.private-chat-contact-preview');
            const timeElement = contactElement.querySelector('.private-chat-contact-time');
            const badgeElement = contactElement.querySelector('.private-chat-unread-badge');

            if (previewElement) previewElement.textContent = lastMessage;
            if (timeElement) timeElement.textContent = new Date(sentAt).toLocaleTimeString('zh-CN', {hour: '2-digit', minute: '2-digit'});

            if (hasUnread && !badgeElement) {
                // 添加未读徽章
                const badgeContainer = contactElement.querySelector('div:last-child');
                const newBadge = document.createElement('span');
                newBadge.className = 'private-chat-unread-badge';
                newBadge.textContent = '1';
                badgeContainer.appendChild(newBadge);
            }
        } else {
            // 添加新联系人
            this.addContactToPrivateChatList({
                contactId: contactId,
                contactName: contactName,
                lastMessage: lastMessage,
                lastMessageTime: sentAt,
                unreadCount: hasUnread ? 1 : 0
            });
        }
    }

    hidePlayerDetailView() {
        console.log('隐藏玩家详情，返回到:', this.playerDetailOrigin);

        document.getElementById('playerDetailView').style.display = 'none';

        // 根据来源界面决定返回到哪里
        switch (this.playerDetailOrigin) {
            case 'chat':
                // 返回公聊界面
                this.hideAllViews();
                document.getElementById('chat-view').style.display = 'block';
                break;
            case 'private-chat':
                // 返回私聊界面
                this.hideAllViews();
                document.getElementById('private-chat-view').style.display = 'block';
                break;
            case 'self':
                // 返回角色界面
                this.hideAllViews();
                document.getElementById('selfView').style.display = 'block';
                break;
            case 'inventory':
                // 返回背包界面
                this.hideAllViews();
                document.getElementById('inventoryView').style.display = 'block';
                break;
            case 'equipment':
                // 返回装备界面
                this.hideAllViews();
                document.getElementById('equipmentView').style.display = 'block';
                break;
            case 'skills':
                // 返回技能界面
                this.hideAllViews();
                document.getElementById('skillsView').style.display = 'block';
                break;
            case 'itemDetailView':
                // 返回装备详情界面
                this.hideAllViews();
                document.getElementById('itemDetailView').style.display = 'block';
                break;
            default:
                // 默认返回主界面
                document.getElementById('main-view').style.display = 'block';
                break;
        }

        // 重置玩家详情来源记录
        this.playerDetailOrigin = null;
        // 注意：不要重置 itemDetailOrigin，因为可能需要用于后续的装备详情返回导航
    }

    pickupItem(itemId) {
        this.sendMessage(MessageProtocol.C2S_PICKUP_ITEM, { item_id: Number(itemId) });
    }

    allocatePotentialPoints() {
        const points = {
            strength: parseInt(document.getElementById('strength_points').value) || 0,
            agility: parseInt(document.getElementById('agility_points').value) || 0,
            constitution: parseInt(document.getElementById('constitution_points').value) || 0,
            intelligence: parseInt(document.getElementById('intelligence_points').value) || 0,
        };

        const totalPoints = Object.values(points).reduce((sum, val) => sum + val, 0);
        if (totalPoints <= 0) {
            document.getElementById('allocatorError').textContent = '请输入要分配的点数。';
            return;
        }

        if (totalPoints > this.currentPlayer.attributes.potential_points) {
             document.getElementById('allocatorError').textContent = '潜力点不足。';
            return;
        }
        
        document.getElementById('allocatorError').textContent = '';
        this.sendMessage(MessageProtocol.C2S_ALLOCATE_POTENTIAL_POINTS, { points: points });
    }

    handleAtbStatusUpdate(payload) {
        if (!this.battleState) return;

        // 更新战斗状态
        if (payload.monster) {
            this.battleState.monster = payload.monster;
        }

        if (payload.all_players) {
            this.battleState.all_players = payload.all_players.map(p => this.normalizePlayer(p));
            const me = payload.all_players.find(p => p.id === this.currentPlayer.id);
            if (me) {
                this.battleState.player = this.normalizePlayer(me);
            }
        }

        if (payload.atb_status) {
            this.battleState.playerATBs = payload.atb_status.playerATBs;
            this.battleState.monsterATB = payload.atb_status.monsterATB;
            this.battleState.roundTimeRemaining = payload.atb_status.roundTimeRemaining;
            this.battleState.atbMax = payload.atb_status.atbMax;
        }

        // 更新状态效果
        if (payload.active_effects) {
            this.battleState.active_effects = payload.active_effects;
            // 更新状态效果显示
            if (this.skillManager) {
                this.skillManager.updateBattleEffectsDisplay(payload.active_effects, this.currentPlayer.id);
            }
        }

        // 更新战斗视图
        this.updateBattleView(this.battleState.monster, this.battleState.all_players);
    }

    returnToScene() {
        // 调用returnToSceneFromBattle方法，确保逻辑一致
        this.returnToSceneFromBattle();
    }

    showConfigView() {
        this.clearMessages();
        // Request both inventory (for potion list) and current config
        this.sendMessage(MessageProtocol.C2S_GET_INVENTORY);
        this.sendMessage(MessageProtocol.C2S_GET_COMBAT_POTION_CONFIG);
        
        document.getElementById('main-view').style.display = 'none';
        document.getElementById('configView').style.display = 'block';
        
        // 清除任何可能的未定义消息
        const configSuccess = document.getElementById('configSuccess');
        const configError = document.getElementById('configError');
        configSuccess.textContent = '';
        configSuccess.style.padding = '0';
        configSuccess.style.borderColor = 'transparent';
        configError.textContent = '';
        configError.style.padding = '0';
        configError.style.borderColor = 'transparent';
    }

    hideConfigView() {
        this.clearMessages();
        document.getElementById('configView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    updateConfigView() {
        const listContainer = document.getElementById('potion-selection-list');
        
        // 使用服务器返回的可用药水列表
        const hpPotions = this.availableHpPotions || [];
        const mpPotions = this.availableMpPotions || [];
        
        if (hpPotions.length === 0 && mpPotions.length === 0) {
            listContainer.innerHTML = '<p style="text-align: center; color: #666;">背包里没有可配置的药水。</p>';
        } else {
            let html = '<table width="100%" style="border-spacing: 0 10px;">';
            
            // 添加回血药水
            if (hpPotions.length > 0) {
                html += '<tr><td colspan="2"><div style="font-weight: bold; color: #8b0000; margin-bottom: 5px;">可用回血药水</div></td></tr>';
                hpPotions.forEach(potion => {
                    html += `
                        <tr>
                            <td>
                                <div style="font-weight: bold; margin-bottom: 3px;">${potion.name}</div>
                                <div style="font-size: 12px; color: #555;">生命 +${potion.effect_value}</div>
                            </td>
                            <td style="text-align: right; vertical-align: middle;">
                                <button class="btn-small" onclick="game.setCombatPotion(${potion.inventory_id}, 'hp')">[设为回血]</button>
                            </td>
                        </tr>`;
                });
            }
            
            // 添加回蓝药水
            if (mpPotions.length > 0) {
                html += '<tr><td colspan="2"><div style="font-weight: bold; color: #00008b; margin-top: 10px; margin-bottom: 5px;">可用回蓝药水</div></td></tr>';
                mpPotions.forEach(potion => {
                    html += `
                        <tr>
                            <td>
                                <div style="font-weight: bold; margin-bottom: 3px;">${potion.name}</div>
                                <div style="font-size: 12px; color: #555;">法力 +${potion.effect_value}</div>
                            </td>
                            <td style="text-align: right; vertical-align: middle;">
                                <button class="btn-small" onclick="game.setCombatPotion(${potion.inventory_id}, 'mp')">[设为回蓝]</button>
                            </td>
                        </tr>`;
                });
            }
            
            html += '</table>';
            listContainer.innerHTML = html;
        }
    }

    setCombatPotion(itemTemplateId, type) {
        this.clearMessages();
        
        // 显示正在配置的提示
        const configSuccess = document.getElementById('configSuccess');
        configSuccess.textContent = '正在配置...';
        configSuccess.style.borderColor = '#6c8095';
        configSuccess.style.padding = '5px';
        
        this.sendMessage(MessageProtocol.C2S_SET_COMBAT_POTION_CONFIG, {
            item_template_id: itemTemplateId,
            type: type
        });
        
        // 设置一个较短的超时，因为服务器响应通常很快
        // 如果服务器返回错误，handleError会处理
        // 这里只是作为网络故障的备用处理
        setTimeout(() => {
            if (configSuccess.textContent === '正在配置...') {
                const configError = document.getElementById('configError');
                configSuccess.textContent = '';
                configSuccess.style.padding = '0';
                configSuccess.style.borderColor = 'transparent';
                
                configError.textContent = '网络请求超时，请检查网络连接后重试';
                configError.style.borderColor = 'var(--error-color)';
                configError.style.padding = '5px';
                
                // 3秒后清除错误提示
                setTimeout(() => this.clearMessageElement('configError'), 3000);
            }
        }, 5000); // 延长到5秒，给服务器更多响应时间
    }

    handleCombatPotionConfigData(payload) {
        document.getElementById('configured-hp-potion').innerHTML = payload.hp_potion_name ? 
            `<div style="font-weight: bold;">${payload.hp_potion_name}</div>` : '未配置';
        document.getElementById('configured-mp-potion').innerHTML = payload.mp_potion_name ? 
            `<div style="font-weight: bold;">${payload.mp_potion_name}</div>` : '未配置';
        
        // 保存服务器返回的可用药水列表
        this.availableHpPotions = payload.available_hp_potions || [];
        this.availableMpPotions = payload.available_mp_potions || [];
        
        // 更新配置界面内容
        this.updateConfigView();
    }

    handlePlayerSkillsList(payload) {
        // 调试信息
        console.log("收到玩家技能列表消息:", payload);
        
        if (payload && payload.skills) {
            this.skillManager.updateSkillsList(payload.skills);
        } else {
            console.error("收到的技能列表数据无效:", payload);
        }
    }
    
    handleBattleSkillsList(payload) {
        const skillsContainer = document.getElementById('battleActionsContainer');
        if (!skillsContainer) {
            console.error('找不到battleActionsContainer元素');
            return;
        }
    
        // 移除任何现有的技能按钮和它们的换行符，以防止重复
        skillsContainer.querySelectorAll('.skill-btn, .skills-line-break').forEach(el => el.remove());

        // 如果有新技能要显示，在它们前面添加一个换行元素以确保它们从新行开始
        if (payload.skills && payload.skills.length > 0) {
            const lineBreak = document.createElement('div');
            lineBreak.className = 'skills-line-break';
            lineBreak.style.flexBasis = '100%';
            lineBreak.style.height = '0px'; // 减少间隔高度
            skillsContainer.appendChild(lineBreak);

            // 创建技能容器
            const skillsHeader = document.createElement('div');
            skillsHeader.className = 'skills-container';
            skillsHeader.style.marginTop = '5px'; // 减少顶部间距
            // skillsHeader.innerHTML = '<div class="skills-header">可用技能:</div>';
            skillsContainer.appendChild(skillsHeader);

            // Add the new skill buttons
            payload.skills.forEach((skill, index) => {
                console.log(`GameClient - 创建技能按钮 ${index}:`, skill);
                console.log(`GameClient - skill.skill_template_id: ${skill.skill_template_id}`);

                // 尝试多个可能的技能ID字段名
                const skillId = skill.skill_template_id || skill.id || skill.skill_id;
                console.log(`GameClient - 最终使用的skillId: ${skillId}`);

                const button = document.createElement('button');
                button.className = 'btn btn-skill';
                button.dataset.skillId = skillId; // 添加技能ID用于状态管理
                // 技能名称只显示前两个字符
                const shortSkillName = skill.name.length > 2 ? skill.name.substring(0, 2) : skill.name;
                button.textContent = `[${shortSkillName}]`;

                console.log(`GameClient - 创建按钮，dataset.skillId: ${button.dataset.skillId}`);

                button.onclick = () => {
                    // 检查技能是否可用（包括冷却、吟唱等状态）
                    if (this.skillManager && !this.skillManager.isSkillAvailable(skill)) {
                        return; // 如果技能不可用，直接返回
                    }

                    const mpCost = skill.mp_cost || 0;
                    if (this.battleState.player.attributes.mp < mpCost) {
                        this.addBattleLog(document.getElementById('battleLogContainer'), `法力不足，无法施放【${skill.name}】！`);
                        return;
                    }
                    this.sendBattleAction('use_skill', { skill_id: skillId, skill_name: skill.name });
                };
                skillsHeader.appendChild(button);
            });
        }
    }

    startAutoSceneResetTimer() {
        if (this.autoResetTimer) {
            clearInterval(this.autoResetTimer);
        }
        
        this.autoResetTimer = setInterval(() => {
            if (this.currentScene) {
                console.log(`正在尝试自动重置场景: ${this.currentScene.id}`);
                this.sendMessage(MessageProtocol.C2S_RESET_SCENE, { scene_id: this.currentScene.id });
            }
        }, 120000); // 120000 毫秒 = 2 分钟

        console.log("自动场景重置计时器已启动，每2分钟执行一次。");
    }

    /**
     * 处理玩家复活事件
     * @param {CustomEvent} event - 复活事件
     */
    handlePlayerRevivedEvent(event) {
        // 更新玩家状态
        if (event.detail && event.detail.player) {
            this.currentPlayer = event.detail.player;
            this.updatePlayerInfo();
        }
        
        // 如果在战斗界面，则返回场景
        if (document.getElementById('battleView').style.display === 'block') {
            this.returnToSceneFromBattle();
        }
    }

    /**
     * 生成物品凝练信息的HTML
     * @param {Object} instanceData - 物品实例数据
     * @returns {string} - 凝练信息HTML
     */
    getRefineInfoHtml(instanceData) {
        if (!instanceData || !instanceData.refined || !instanceData.refine_bonuses) {
            return '';
        }
        
        let html = `<hr class="section-divider">`;
        html += `<div class="refined-info" style="background-color: rgba(76, 175, 80, 0.05); padding: 8px; border-left: 3px solid #4caf50; margin-top: 10px;">`;
        html += `<b style="color: #4caf50;">凝练信息:</b>`;
        html += `<div style="margin-left: 10px; margin-top: 5px;">`;
        html += `<p><span style="color: #555;">凝练等级:</span> <b>${instanceData.refine_prefix}</b></p>`;
        html += `<p><span style="color: #555;">凝练值:</span> <b>${instanceData.refine_value.toFixed(2)}</b></p>`;
        
        // 显示凝练属性加成
        html += '<b>凝练属性加成:</b><ul style="margin-top: 5px; padding-left: 20px;">';
        const bonusTypes = instanceData.refine_bonus_types || {};
        for (const [key, value] of Object.entries(instanceData.refine_bonuses)) {
            const bonusType = bonusTypes[key] || 'flat';
            const displayValue = bonusType === 'percentage' ?
                `${value > 0 ? '+' : ''}${value}%` :
                `${value > 0 ? '+' : ''}${value}`;
            html += `<li>${this.playerAttributeMap[key] || key}: <span style="color: #4caf50; font-weight: bold;">${displayValue}</span></li>`;
        }
        html += '</ul>';
        html += `</div></div>`;
        
        return html;
    }
    // 在物品详情页面显示消息
    showActionMessage(message, type = 'success') {
        let viewPrefix = null;
        if (document.getElementById('inventoryView').style.display === 'block') {
            viewPrefix = 'inventory';
        } else if (document.getElementById('itemDetailView').style.display === 'block') {
            viewPrefix = 'itemDetail';
        }

        if (viewPrefix) {
            const successEl = document.getElementById(`${viewPrefix}Success`);
            const errorEl = document.getElementById(`${viewPrefix}Error`);

            // 在显示新消息前，清除之前的消息
            if (successEl) {
                successEl.textContent = '';
                successEl.style.padding = '0';
                successEl.style.borderColor = 'transparent';
            }
            if (errorEl) {
                errorEl.textContent = '';
                errorEl.style.padding = '0';
                errorEl.style.borderColor = 'transparent';
            }

            const messageElement = type === 'error' ? errorEl : successEl;

            if (messageElement) {
                messageElement.textContent = message;
                let color = '#00662a'; // 默认是成功的绿色
                if (type === 'error') {
                    color = 'var(--error-color)';
                } else if (type === 'info') {
                    color = '#6c757d'; // 信息用灰色
                }
                
                messageElement.style.borderColor = color;
                messageElement.style.padding = '5px';

                // 3秒后自动清除消息
                setTimeout(() => {
                    if (messageElement.textContent === message) {
                        messageElement.textContent = '';
                        messageElement.style.padding = '0';
                        messageElement.style.borderColor = 'transparent';
                    }
                }, 3000);
                return; // 消息已在视图中显示，完成
            }
        }

        // 如果没有找到合适的视图来显示消息，则退回到游戏日志
        const icon = type === 'error' ? '❌' : (type === 'success' ? '✅' : 'ℹ️');
        this.addLog(`${icon} ${message}`);
    }

    /**
     * 发起PVP挑战
     * @param {string} targetPlayerId 目标玩家ID
     */
    challengePlayerToPvp(targetPlayerId) {
        if (!targetPlayerId) return;
        
        this.pvpManager.sendChallengeRequest(targetPlayerId);
    }
    
    /**
     * 直接攻击玩家（无需等待对方接受）
     * @param {string} targetPlayerId 目标玩家ID
     * @param {number} targetKarma 目标玩家的善恶值（可选，从玩家详情获取）
     */
    attackPlayer(targetPlayerId, targetKarma = null) {
        if (!targetPlayerId) return;

        // 检查当前场景是否是安全区
        if (this.currentScene && this.currentScene.is_safe_zone === 1) {
            // 在安全区，需要检查目标玩家的善恶值
            if (targetKarma !== null) {
                // 如果有善恶值信息，进行前端检查
                if (targetKarma >= 0) {
                    this.handleError('安全区内不能对善良玩家发起攻击！');
                    return;
                }
                // 如果是红名玩家（karma < 0），继续执行攻击
            }
            // 如果没有善恶值信息，让服务端来判断
        }

        // 保存目标玩家ID，以便在取消时可以重新显示玩家详情
        this.currentTargetPlayerId = targetPlayerId;
        
        // 显示确认对话框
        this.showConfirmDialog(
            '确认攻击',
            `你确定要直接攻击该玩家吗？一旦确认，将立即进入战斗！`,
            () => {
                // 用户确认后发送直接攻击请求
                this.sendMessage(MessageProtocol.C2S_PVP_DIRECT_ATTACK, {
                    target_player_id: targetPlayerId
                });
                this.hidePlayerDetailView();
                this.addLog(`你发起了直接攻击，战斗即将开始...`);
                // 清除目标玩家ID
                this.currentTargetPlayerId = null;
            }
        );
    }
    
    /**
     * 显示确认对话框
     * @param {string} title 对话框标题
     * @param {string} message 对话框消息
     * @param {Function} onConfirm 确认回调函数
     */
    showConfirmDialog(title, message, onConfirm) {
        // 检查是否已存在确认对话框，如果存在则先移除
        const existingDialog = document.getElementById('confirmDialog');
        if (existingDialog) {
            existingDialog.parentNode.removeChild(existingDialog);
        }
        
        // 创建对话框容器
        const dialogContainer = document.createElement('div');
        dialogContainer.id = 'confirmDialog';
        dialogContainer.className = 'confirm-dialog';
        
        // 设置对话框内容
        dialogContainer.innerHTML = `
            <div class="confirm-dialog-content">
                <h3>${title}</h3>
                <p>${message}</p>
                <div class="confirm-dialog-actions">
                    <button id="confirmDialogCancel" class="btn-primary">取消</button>
                    <button id="confirmDialogConfirm" class="btn-primary btn-danger">确认</button>
                </div>
            </div>
        `;
        
        // 添加到游戏内容区域而不是body
        document.getElementById('playerDetailView').style.display = 'none';
        document.getElementById('gameContent').appendChild(dialogContainer);
        
        // 绑定事件
        document.getElementById('confirmDialogCancel').onclick = () => {
            document.getElementById('gameContent').removeChild(dialogContainer);
            
            // 如果是从玩家详情页面来的，并且有当前目标玩家ID，则返回玩家详情页面
            if (this.currentTargetPlayerId) {
                this.showPlayerDetails(this.currentTargetPlayerId);
            }
        };
        
        document.getElementById('confirmDialogConfirm').onclick = () => {
            document.getElementById('gameContent').removeChild(dialogContainer);
            if (typeof onConfirm === 'function') {
                onConfirm();
            }
        };
    }
    
    /**
     * 显示PVP排行榜
     */
    showPvpLeaderboard() {
        this.pvpManager.requestPvpLeaderboard();
    }
    
    /**
     * 显示PVP个人战绩
     */
    showPvpStats() {
        this.pvpManager.requestPvpStats();
    }
    
    /**
     * 显示等级排行榜
     */
    showLevelRanking() {
        this.pvpManager.requestLevelRanking();
    }
    
    /**
     * 显示英雄排行榜（按善恶值从高到低）
     */
    showHeroRanking() {
        this.pvpManager.requestHeroRanking();
    }
    
    /**
     * 显示黑手排行榜（按善恶值从低到高）
     */
    showVillainRanking() {
        this.pvpManager.requestVillainRanking();
    }
    
    /**
     * 丢弃物品
     */
    dropItem(inventoryId) {
        if (!inventoryId) return;
        
        // 保存当前物品以及所在界面信息
        const item = this.currentDetailItem;
        this.currentDropItem = item;
        this.droppingInventoryId = inventoryId;
        this.previousView = 'itemDetail'; // 记录来源界面
        
        // 隐藏当前界面
        this.hideItemDetailView();
        
        // 显示丢弃物品界面
        const dropItemView = document.getElementById('dropItemView');
        const dropItemName = document.getElementById('dropItemName');
        // 获取绑定状态
        const isBound = item.is_bound == 1;
        const boundMark = isBound ? ' [绑定物品]' : '';
        const dropItemDescription = document.getElementById('dropItemDescription');
        const dropQuantitySelector = document.getElementById('dropQuantitySelector');
        const dropQuantity = document.getElementById('dropQuantity');
        const dropItemMessage = document.getElementById('dropItemMessage');
        
        // 设置物品名称和描述
        dropItemName.textContent = item.name + boundMark;
        dropItemDescription.textContent = item.quantity > 1 ? `当前拥有: ${item.quantity}个` : '';
        dropItemMessage.textContent = '';
        
        // 如果是可堆叠物品且数量大于1，显示数量选择器
        if (item.stackable == 1 && item.quantity > 1) {
            dropQuantitySelector.style.display = 'block';
            dropQuantity.max = item.quantity;
            dropQuantity.value = 1;
            
            // 绑定增减按钮事件
            document.getElementById('decreaseDropQty').onclick = () => {
                let value = parseInt(dropQuantity.value) || 1;
                value = Math.max(1, value - 1);
                dropQuantity.value = value;
            };
            
            document.getElementById('increaseDropQty').onclick = () => {
                let value = parseInt(dropQuantity.value) || 1;
                value = Math.min(item.quantity, value + 1);
                dropQuantity.value = value;
            };
            
            // 由于输入框是只读的，不再需要监听change事件
            // 但保留此处以防未来需要其他处理
        } else {
            // 不可堆叠物品或单个物品，隐藏数量选择器
            dropQuantitySelector.style.display = 'none';
        }
        
        // 显示丢弃界面
        this.hideAllViews();
        dropItemView.style.display = 'block';
    }
    
    // 隐藏丢弃物品界面
    hideDropItemView() {
        document.getElementById('dropItemView').style.display = 'none';
        
        // 根据之前的界面返回
        if (this.previousView === 'itemDetail') {
            // 重新显示物品详情
            this.showItemDetails(this.currentDropItem);
        } else {
            // 显示主界面
            document.getElementById('main-view').style.display = 'block';
        }
        
        // 清除缓存的物品
        this.currentDropItem = null;
        this.droppingInventoryId = null;
    }
    
    // 确认丢弃物品
    confirmDropItem() {
        const inventoryId = this.droppingInventoryId;
        const item = this.currentDropItem;
        let quantity = 1;
        
        if (!inventoryId || !item) {
            return;
        }
        
        // 如果是可堆叠物品，获取选择的数量
        if (item.stackable == 1 && item.quantity > 1) {
            const dropQuantity = document.getElementById('dropQuantity');
            quantity = parseInt(dropQuantity.value) || 1;
            if (quantity <= 0 || quantity > item.quantity) {
                document.getElementById('dropItemMessage').textContent = '无效的丢弃数量';
                return;
            }
        }
        
        // 发送丢弃物品请求
        this.sendMessage(MessageProtocol.C2S_DROP_ITEM, {
            inventory_id: inventoryId,
            quantity: quantity
        });
        
        // 隐藏丢弃界面，显示主界面
        document.getElementById('dropItemView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
        
        // 记录丢弃操作
        this.addLog(`正在丢弃 ${item.name}${quantity > 1 ? ' (x'+quantity+')' : ''}...`);
        
        // 清除缓存的物品
        this.currentDropItem = null;
        this.droppingInventoryId = null;
    }

    /**
     * 处理物品丢弃响应
     * @param {Object} payload - 服务器响应数据
     */
    handleItemDropped(payload) {
        if (payload.success) {
            // 确保所有视图都关闭，只显示主界面
            this.hideAllViews();
            document.getElementById('main-view').style.display = 'block';
            
            // 在游戏日志中显示成功消息
            this.addLog(`✅ ${payload.message || '物品丢弃成功'}`);
            
            // 丢弃成功后不再需要调用场景刷新，服务器会发送重置场景消息
            // 仅保留当前场景数据确保正确处理
            if (this.currentScene && !this.currentScene.name && this.currentScene.id) {
                // 如果当前场景没有名称信息，尝试获取
                this.refreshScene();
            }
        } else {
            // 显示错误消息
            this.addLog(`❌ ${payload.message || '丢弃物品失败'}`);
            
            // 如果丢弃界面还在显示，显示错误消息
            const dropItemMessage = document.getElementById('dropItemMessage');
            if (document.getElementById('dropItemView').style.display === 'block' && dropItemMessage) {
                dropItemMessage.textContent = payload.message || '丢弃物品失败';
            }
        }
    }

    /**
     * 绑定物品
     * @param {number} inventoryId 物品ID
     */
    bindItem(inventoryId) {
        if (!inventoryId) return;
        
        // 保存当前物品以及所在界面信息
        const item = this.currentDetailItem;
        this.currentBindItem = item;
        this.bindingInventoryId = inventoryId;
        this.previousView = 'itemDetail'; // 记录来源界面
        
        // 隐藏当前界面
        this.hideItemDetailView();
        
        // 显示绑定物品界面
        const bindItemView = document.getElementById('bindItemView');
        const bindItemName = document.getElementById('bindItemName');
        const bindItemMessage = document.getElementById('bindItemMessage');
        
        // 设置物品名称和描述
        bindItemName.textContent = item.name;
        bindItemMessage.textContent = '';
        
        // 显示绑定界面
        this.hideAllViews();
        bindItemView.style.display = 'block';
    }
    
    // 隐藏绑定物品界面
    hideBindItemView() {
        document.getElementById('bindItemView').style.display = 'none';
        
        // 根据之前的界面返回
        if (this.previousView === 'itemDetail') {
            // 重新显示物品详情
            this.showItemDetails(this.currentBindItem);
        } else {
            // 显示主界面
            document.getElementById('main-view').style.display = 'block';
        }
        
        // 清除缓存的物品
        this.currentBindItem = null;
        this.bindingInventoryId = null;
    }
    
    // 确认绑定物品
    confirmBindItem() {
        const inventoryId = this.bindingInventoryId;
        const item = this.currentBindItem;
        
        if (!inventoryId || !item) {
            return;
        }
        
        // 发送绑定物品请求
        this.sendMessage(MessageProtocol.C2S_BIND_ITEM, {
            inventory_id: inventoryId
        });
        
        // 隐藏绑定界面，但不显示任何其他界面，等待绑定结果
        document.getElementById('bindItemView').style.display = 'none';
        
        // 记录绑定操作
        this.addLog(`正在绑定 ${item.name}...`);
        
        // 清除缓存的物品
        this.currentBindItem = null;
        this.bindingInventoryId = null;
    }
    
    /**
     * 处理物品绑定响应
     * @param {Object} payload - 服务器响应数据
     */
    handleItemBound(payload) {
        if (payload.success) {
            // 确保所有视图都关闭
            this.hideAllViews();
            
            // 在游戏日志中显示成功消息
            this.addLog(`✅ ${payload.message || '物品绑定成功！绑定后的物品无法交易或掉落。'}`);
            
            // 显示背包界面
            this.showInventoryView();
        } else {
            // 显示错误消息
            this.addLog(`❌ ${payload.message || '绑定物品失败'}`);
            
            // 如果绑定界面还在显示，显示错误消息
            const bindItemMessage = document.getElementById('bindItemMessage');
            if (document.getElementById('bindItemView').style.display === 'block' && bindItemMessage) {
                bindItemMessage.textContent = payload.message || '绑定物品失败';
            }
        }
    }

    // 处理联系人更新消息
    handleChatContactUpdated(payload) {
        const { action, contactId, unreadCount } = payload;

        console.log('联系人更新:', { action, contactId, unreadCount });

        if (action === 'marked_read') {
            // 更新联系人列表中的未读计数
            this.updateContactUnreadCountInList(contactId, 0);
        } else if (action === 'deleted') {
            // 从联系人列表中移除
            this.removeContactFromList(contactId);

            // 隐藏删除确认界面（如果存在）
            this.hideDeleteContactConfirmation();

            // 如果删除的是当前正在聊天的联系人，返回联系人列表
            if (this.currentPrivateChatContact === contactId) {
                this.showActionMessage('联系人已删除', 'success');
                this.currentPrivateChatContact = null;
                this.currentPrivateChatContactName = null;
                this.showPrivateChatContactsList();
            }
        }
    }

    // 更新联系人列表中的未读计数
    updateContactUnreadCountInList(contactId, unreadCount) {
        const contactsList = document.getElementById('private-chat-contacts-list');
        if (!contactsList) return;

        const contactElement = contactsList.querySelector(`[data-contact-id="${contactId}"]`);
        if (contactElement) {
            const badgeElement = contactElement.querySelector('.private-chat-unread-badge');

            if (unreadCount > 0) {
                if (badgeElement) {
                    badgeElement.textContent = unreadCount;
                } else {
                    // 添加未读徽章
                    const badgeContainer = contactElement.querySelector('div:last-child');
                    const newBadge = document.createElement('span');
                    newBadge.className = 'private-chat-unread-badge';
                    newBadge.textContent = unreadCount;
                    badgeContainer.appendChild(newBadge);
                }
            } else {
                // 移除未读徽章
                if (badgeElement) {
                    badgeElement.remove();
                }
            }
        }
    }

    // 从联系人列表中移除联系人
    removeContactFromList(contactId) {
        const contactsList = document.getElementById('private-chat-contacts-list');
        if (!contactsList) return;

        const contactElement = contactsList.querySelector(`[data-contact-id="${contactId}"]`);
        if (contactElement) {
            contactElement.remove();

            // 如果列表为空，显示提示信息
            if (contactsList.children.length === 0) {
                contactsList.innerHTML = '<div class="no-contacts-message">暂无聊天记录</div>';
            }
        }
    }

    // 确认删除联系人
    confirmDeleteChatContact() {
        if (!this.currentPrivateChatContact || !this.currentPrivateChatContactName) {
            this.showActionMessage('没有选择要删除的联系人', 'error');
            return;
        }

        // 显示内联确认界面
        this.showDeleteContactConfirmation();
    }

    // 显示删除联系人确认界面
    showDeleteContactConfirmation() {
        const deleteBtn = document.getElementById('private-chat-delete-contact');
        if (!deleteBtn) return;

        // 创建确认界面
        const confirmationDiv = document.createElement('div');
        confirmationDiv.id = 'delete-contact-confirmation';
        confirmationDiv.style.cssText = `
            position: absolute;
            top: 100%;
            left: 50%;
            transform: translateX(-50%);
            background: white;
            border: 2px solid #dc3545;
            border-radius: 8px;
            padding: 15px;
            box-shadow: 0 6px 16px rgba(0,0,0,0.15);
            z-index: 1000;
            width: 280px;
            font-size: 14px;
            margin-top: 5px;
            animation: slideDown 0.2s ease-out;
        `;

        confirmationDiv.innerHTML = `
            <div style="margin-bottom: 10px; color: #dc3545; font-weight: bold;">
                删除联系人确认
            </div>
            <div style="margin-bottom: 15px; line-height: 1.4;">
                确定要删除联系人"${this.currentPrivateChatContactName}"吗？<br>
                <small style="color: #666;">删除后将清除聊天记录，此操作不可撤销。</small>
            </div>
            <div style="text-align: right; margin-top: 5px;">
                <button id="confirm-delete-contact" style="background: #dc3545; color: white; border: none; padding: 6px 12px; border-radius: 4px; margin-right: 8px; cursor: pointer; font-size: 13px; transition: background 0.2s;">确认删除</button>
                <button id="cancel-delete-contact" style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; cursor: pointer; font-size: 13px; transition: background 0.2s;">取消</button>
            </div>
        `;

        // 设置父容器为相对定位
        const header = deleteBtn.parentElement;
        header.style.position = 'relative';

        // 添加到头部
        header.appendChild(confirmationDiv);

        // 绑定事件
        const confirmBtn = document.getElementById('confirm-delete-contact');
        const cancelBtn = document.getElementById('cancel-delete-contact');

        confirmBtn.addEventListener('click', () => {
            this.deleteChatContact(this.currentPrivateChatContact);
            this.hideDeleteContactConfirmation();
        });

        cancelBtn.addEventListener('click', () => {
            this.hideDeleteContactConfirmation();
        });

        // 添加悬停效果
        confirmBtn.addEventListener('mouseenter', () => {
            confirmBtn.style.background = '#c82333';
        });
        confirmBtn.addEventListener('mouseleave', () => {
            confirmBtn.style.background = '#dc3545';
        });

        cancelBtn.addEventListener('mouseenter', () => {
            cancelBtn.style.background = '#5a6268';
        });
        cancelBtn.addEventListener('mouseleave', () => {
            cancelBtn.style.background = '#6c757d';
        });

        // 点击外部关闭
        setTimeout(() => {
            document.addEventListener('click', this.handleDeleteConfirmationOutsideClick.bind(this), { once: true });
        }, 100);
    }

    // 隐藏删除联系人确认界面
    hideDeleteContactConfirmation() {
        const confirmationDiv = document.getElementById('delete-contact-confirmation');
        if (confirmationDiv) {
            confirmationDiv.remove();
        }
    }

    // 处理点击外部关闭确认界面
    handleDeleteConfirmationOutsideClick(event) {
        const confirmationDiv = document.getElementById('delete-contact-confirmation');
        if (confirmationDiv && !confirmationDiv.contains(event.target)) {
            this.hideDeleteContactConfirmation();
        }
    }

    // 删除聊天联系人
    deleteChatContact(contactId) {
        if (!contactId) {
            this.showActionMessage('联系人ID无效', 'error');
            return;
        }

        console.log('=== 删除联系人 ===');
        console.log('联系人ID:', contactId);
        console.log('联系人名称:', this.currentPrivateChatContactName);

        // 发送删除联系人请求
        this.sendMessage(MessageProtocol.C2S_DELETE_CHAT_CONTACT, {
            contact_id: contactId
        });

        console.log('删除联系人请求已发送');
    }

    // ==================== 交易系统相关方法 ====================

    /**
     * 发起交易请求
     * @param {number} targetPlayerId 目标玩家ID
     */
    requestTrade(targetPlayerId) {
        if (!this.currentPlayer) {
            this.addLog('请先登录');
            return;
        }

        this.sendMessage(MessageProtocol.C2S_TRADE_REQUEST, {
            player_id: this.currentPlayer.id,
            target_player_id: targetPlayerId
        });
       // 发送交易请求后返回主界面等待回应
        this.hidePlayerDetailView();
    }

    /**
     * 处理收到交易请求
     * @param {Object} payload 消息载荷
     */
    handleTradeRequestReceived(payload) {
        const { trade_id, initiator_name } = payload;

        // 创建交易请求通知UI（参考PVP挑战）
        this.createTradeRequestNotification(payload);
    }

    /**
     * 创建交易请求通知UI
     * @param {Object} tradeData 交易数据
     */
    createTradeRequestNotification(tradeData) {
        const { trade_id, initiator_name } = tradeData;

        // 创建通知容器
        const notification = document.createElement('div');
        notification.id = `trade-request-${trade_id}`;
        notification.className = 'trade-request-notification';

        // 设置通知样式
        notification.style.cssText = `
            position: absolute;
            top: 10px;
            right: 10px;
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.3);
            z-index: 100;
            min-width: 280px;
            max-width: 320px;
            animation: slideInRight 0.3s ease-out;
        `;

        // 设置通知内容
        notification.innerHTML = `
            <div style="font-size: 16px; font-weight: bold; margin-bottom: 10px;">收到交易请求!</div>
            <div style="margin-bottom: 15px;">
                <p style="margin: 0;">玩家 <strong>${initiator_name}</strong> 向你发起了交易请求!</p>
            </div>
            <div style="text-align: center;">
                <button class="btn-primary trade-accept-btn" data-trade-id="${trade_id}" style="margin-right: 10px; background: white; color: #4CAF50; border: none; padding: 8px 16px; border-radius: 5px; cursor: pointer;">接受</button>
                <button class="btn-secondary trade-decline-btn" data-trade-id="${trade_id}" style="background: rgba(255,255,255,0.2); color: white; border: 1px solid white; padding: 8px 16px; border-radius: 5px; cursor: pointer;">拒绝</button>
            </div>
        `;

        // 添加CSS动画
        if (!document.querySelector('#trade-notification-styles')) {
            const style = document.createElement('style');
            style.id = 'trade-notification-styles';
            style.textContent = `
                @keyframes slideInRight {
                    from { transform: translateX(100%); opacity: 0; }
                    to { transform: translateX(0); opacity: 1; }
                }
                .trade-accept-btn:hover { background: #f0f0f0 !important; }
                .trade-decline-btn:hover { background: rgba(255,255,255,0.3) !important; }
            `;
            document.head.appendChild(style);
        }

        // 添加到游戏主界面容器
        const mainView = document.getElementById('main-view');
        if (mainView) {
            // 确保main-view有相对定位，以便绝对定位的通知正确显示
            if (getComputedStyle(mainView).position === 'static') {
                mainView.style.position = 'relative';
            }
            mainView.appendChild(notification);
        } else {
            // 如果找不到main-view，回退到body
            document.body.appendChild(notification);
        }

        // 绑定按钮事件
        const acceptBtn = notification.querySelector('.trade-accept-btn');
        const declineBtn = notification.querySelector('.trade-decline-btn');

        acceptBtn.onclick = () => this.respondToTradeRequest(trade_id, true);
        declineBtn.onclick = () => this.respondToTradeRequest(trade_id, false);

        // 15秒后自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 15000);
    }

    /**
     * 响应交易请求
     * @param {string} tradeId 交易ID
     * @param {boolean} accepted 是否接受
     */
    respondToTradeRequest(tradeId, accepted) {
        // 移除通知
        const notification = document.getElementById(`trade-request-${tradeId}`);
        if (notification) {
            notification.remove();
        }

        // 发送响应
        this.sendMessage(MessageProtocol.C2S_TRADE_RESPOND, {
            player_id: this.currentPlayer.id,
            trade_id: tradeId,
            accepted: accepted
        });
    }

    /**
     * 处理交易开始
     * @param {Object} payload 消息载荷
     */
    handleTradeStarted(payload) {
        const { trade_id, partner_id, partner_name, is_initiator } = payload;

        console.log('🔄 交易开始:', {
            trade_id,
            partner_id,
            partner_name,
            currentPlayerId: this.currentPlayer.id,
            isInitiator: is_initiator  // 使用服务器明确告知的角色
        });

        // 保存当前交易信息
        this.currentTrade = {
            id: trade_id,
            partnerId: partner_id,
            partnerName: partner_name,
            isInitiator: is_initiator,  // 保存角色信息
            myItems: [],
            partnerItems: [],
            myCurrency: { gold: 0, diamonds: 0 },
            partnerCurrency: { gold: 0, diamonds: 0 },
            myConfirmed: false,
            partnerConfirmed: false,
            myFinalConfirmed: false,    // 二次确认状态
            partnerFinalConfirmed: false,
            stage: 'preparation'  // 交易阶段: preparation, final_confirm
        };

        console.log('💾 交易状态初始化:', this.currentTrade);

        // 显示交易界面
        this.showTradeView();

        // 确保界面状态正确重置
        this.resetInterfaceElements();

        this.addLog(`与 ${partner_name} 的交易开始`);
    }

    /**
     * 显示交易界面
     */
    showTradeView() {
        if (!this.currentTrade) return;

        this.hideAllViews();
        document.getElementById('tradeView').style.display = 'block';

        // 设置交易伙伴名称
        document.getElementById('tradePartnerName').textContent = this.currentTrade.partnerName;

        // 重置界面状态
        this.resetTradeInterface();

        // 确保有最新的库存数据
        if (this.currentPlayer) {
            this.sendMessage(MessageProtocol.C2S_GET_INVENTORY, {
                player_id: this.currentPlayer.id
            });
        }

        // 加载可交易物品列表
        this.loadTradableItems();
    }

    /**
     * 显示交易物品详情（复用背包详情界面）
     * @param {Object} item 物品信息
     */
    showTradeItemDetail(item) {
        console.log('📋 显示交易物品详情:', item);

        // 隐藏交易界面
        document.getElementById('tradeView').style.display = 'none';

        // 设置来源为交易界面
        this.itemDetailOrigin = 'trade';

        // 显示物品详情界面
        document.getElementById('itemDetailView').style.display = 'block';

        // 清除错误信息
        const itemDetailError = document.getElementById('itemDetailError');
        itemDetailError.textContent = '';
        itemDetailError.style.borderColor = 'transparent';

        // 使用与背包详情相同的名称处理逻辑
        let displayName = (item.instance_data && JSON.parse(item.instance_data).display_name) ? JSON.parse(item.instance_data).display_name : (item.item_name || item.name);
        displayName = displayName.replace(/§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">');
        displayName = displayName.replace(/§/g, '</span>');

        // 显示物品名称，如果可堆叠则显示数量
        const itemNameElement = document.getElementById('itemDetailName');
        itemNameElement.innerHTML = displayName;
        if (item.stackable == 1 && item.quantity > 1) {
            const quantitySpan = document.createElement('span');
            quantitySpan.className = 'item-quantity';
            quantitySpan.style.fontSize = '14px';
            quantitySpan.style.color = '#666';
            quantitySpan.textContent = ` (x${item.quantity})`;
            itemNameElement.appendChild(quantitySpan);
        }

        // 构建详情HTML - 完全参照背包详情的结构
        let contentHtml = '';

        contentHtml += `<p><b>分类:</b> ${this.categoryMap[item.category] || item.category}</p>`;
        if (item.description) {
            contentHtml += `<p style="color: #555;">${item.description}</p>`;
        }
        if (item.granted_job_name) {
            contentHtml += `<p><b>授予职业:</b> <span style="color: #28a745; font-weight: bold;">${item.granted_job_name}</span></p>`;
        }
        if (item.job_restriction_name) {
            contentHtml += `<p><b>职业要求:</b> <span style="color: #a02c2c; font-weight: bold;">[${item.job_restriction_name}]</span></p>`;
        }

        // 显示装备位置（如果是装备）
        if (item.slot) {
            const slotMap = {
                'Head': '头部', 'Neck': '颈部', 'LeftHand': '左手', 'RightHand': '右手',
                'TwoHanded': '双手', 'Body': '身体', 'Finger': '手指', 'Back': '背部'
            };
            const slotName = slotMap[item.slot] || item.slot;
            contentHtml += `<p><b>装备部位:</b> <span style="color: #6f42c1; font-weight: bold;">${slotName}</span></p>`;
        }

        contentHtml += `<hr class="section-divider">`;

        // 先解析物品效果
        let parsedEffects = null;
        if (item.effects) {
            try {
                parsedEffects = typeof item.effects === 'string' ? JSON.parse(item.effects) : item.effects;
            } catch (e) {
                console.error("解析物品效果失败:", e, item.effects);
            }
        }

        // 显示装备基础属性
        const stats = item.stats ? JSON.parse(item.stats) : null;
        if (stats) {
            contentHtml += '<b>装备属性:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            for (const [key, value] of Object.entries(stats)) {
                contentHtml += `<li>${this.playerAttributeMap[key] || key}: ${value > 0 ? '+' : ''}${value}</li>`;
            }
            contentHtml += '</ul>';
        }

        // 解析物品实例数据以查找凝练信息
        let instanceData = null;
        if (item.instance_data) {
            try {
                instanceData = JSON.parse(item.instance_data);
            } catch (e) {
                console.error("解析物品实例数据失败:", e, item.instance_data);
            }
        }

        // 添加凝练信息（使用与背包相同的方法）
        if (instanceData) {
            contentHtml += this.getRefineInfoHtml(instanceData);
        }

        // 显示插槽信息（完全参照背包详情的逻辑）
        if (item.sockets > 0) {
            contentHtml += '<hr class="section-divider" style="border: none; border-top: 1px solid #ddd; margin: 15px 0;"><b>插槽:</b><div style="padding-left: 10px; margin-top: 5px;">';

            let instanceSockets = [];
            if (item.instance_data) {
                try {
                    const instanceData = JSON.parse(item.instance_data);
                    // Check for the new structure
                    if (instanceData.sockets && Array.isArray(instanceData.sockets) && (instanceData.sockets.length === 0 || typeof instanceData.sockets[0] === 'object')) {
                        instanceSockets = instanceData.sockets;
                    }
                } catch(e) { console.error("Error parsing instance_data sockets", e); }
            }

            // Fallback to create a placeholder structure if needed
            if (instanceSockets.length !== item.sockets) {
                instanceSockets = Array(Number(item.sockets)).fill(null).map(() => ({ gem_id: null, attempts: 0, gem_name: null }));
            }

            for (let i = 0; i < item.sockets; i++) {
                const socketInfo = instanceSockets[i] || { gem_id: null, attempts: 0, gem_name: null };

                if (socketInfo.gem_id) {
                    const gemName = socketInfo.gem_name || '未知宝石';
                    contentHtml += `<div style="margin: 3px 0;">插槽 ${i + 1}: <span style="color: #005a00;">[已镶嵌: ${gemName}]</span></div>`;
                } else {
                    const attemptsText = socketInfo.attempts > 0 ? ` <span style="font-size:12px; color: #6c8095;">(尝试: ${socketInfo.attempts}次)</span>` : '';
                    contentHtml += `<div style="margin: 3px 0;">插槽 ${i + 1}: [空]${attemptsText}</div>`;
                }
            }
            contentHtml += '</div>';
        }

        // 显示物品效果
        if (parsedEffects) {
            contentHtml += '<b>效果:</b><ul style="margin-top: 5px; padding-left: 20px;">';
            for (const [key, value] of Object.entries(parsedEffects)) {
                if (key !== 'learn_skill_id') { // 不显示技能ID
                    contentHtml += `<li>${this.playerAttributeMap[key] || key}: ${value > 0 ? '+' : ''}${value}</li>`;
                }
            }
            contentHtml += '</ul>';
        }

        // 检查是否是技能书
        const isSkillBook = parsedEffects && parsedEffects.learn_skill_id;
        if (isSkillBook) {
            contentHtml += `<p style="color: #4b0082; margin-top: 10px;">可学习新技能</p>`;
        }

        // 设置内容
        document.getElementById('itemDetailContent').innerHTML = contentHtml;

        // 设置操作按钮（交易物品详情不显示操作按钮）
        const actionsContainer = document.getElementById('itemDetailActions');
        actionsContainer.innerHTML = '';

        // 保存当前物品信息
        this.currentDetailItem = item;
    }



    /**
     * 重置交易界面
     */
    resetTradeInterface() {
        console.log('🔄 重置交易界面状态');

        // 清空物品列表
        document.getElementById('myTradeItems').innerHTML = '<div style="text-align: center; color: #888; font-size: 12px; padding: 20px;">暂无物品</div>';
        document.getElementById('partnerTradeItems').innerHTML = '<div style="text-align: center; color: #888; font-size: 12px; padding: 20px;">暂无物品</div>';

        // 重置货币输入
        document.getElementById('myGoldInput').value = '0';
        document.getElementById('myDiamondInput').value = '0';
        document.getElementById('partnerCurrency').innerHTML = '暂无货币';

        // 重置确认状态
        document.getElementById('myConfirmBtn').disabled = true;
        document.getElementById('myConfirmStatus').textContent = '等待添加物品或货币';
        document.getElementById('partnerConfirmStatus').textContent = '等待对方操作...';

        // 隐藏状态消息
        document.getElementById('tradeStatusMessage').style.display = 'none';

        // 重置界面元素的启用状态
        this.resetInterfaceElements();
    }

    /**
     * 重置界面元素的启用状态
     */
    resetInterfaceElements() {
        console.log('🔓 重置界面元素启用状态');

        // 启用添加物品按钮
        const addItemBtn = document.getElementById('addMyItemBtn');
        if (addItemBtn) {
            addItemBtn.disabled = false;
            addItemBtn.style.opacity = '1';
            addItemBtn.style.cursor = 'pointer';
        }

        // 启用金币输入
        const goldInput = document.getElementById('myGoldInput');
        if (goldInput) {
            goldInput.disabled = false;
            goldInput.style.opacity = '1';
        }

        // 启用钻石输入
        const diamondInput = document.getElementById('myDiamondInput');
        if (diamondInput) {
            diamondInput.disabled = false;
            diamondInput.style.opacity = '1';
        }

        // 启用所有移除物品按钮
        const removeButtons = document.querySelectorAll('.remove-trade-item-btn');
        removeButtons.forEach(btn => {
            btn.disabled = false;
            btn.style.opacity = '1';
            btn.style.cursor = 'pointer';
        });

        // 重置确认按钮状态
        const confirmBtn = document.getElementById('myConfirmBtn');
        if (confirmBtn) {
            confirmBtn.textContent = '确认交易';
            confirmBtn.disabled = true; // 初始状态应该是禁用的，等待添加物品或货币
            confirmBtn.style.backgroundColor = '#2196F3';
            console.log('🔄 重置确认按钮状态');
        }

        console.log('✅ 界面元素状态重置完成');
    }

    /**
     * 加载可交易物品列表
     */
    loadTradableItems() {
        // 从当前库存中筛选可交易的物品（只取背包中的物品，不包括已装备的）
        if (this.inventory && this.inventory.backpack && Array.isArray(this.inventory.backpack)) {
            this.tradableItems = this.inventory.backpack.filter(item => {
                const isEquipped = item.is_equipped == 1 || item.is_equipped === true;
                const isBound = item.is_bound == 1 || item.is_bound === true;
                return !isEquipped && !isBound;
            });
        } else {
            this.tradableItems = [];

            // 如果没有库存数据，尝试请求库存数据
            if (this.currentPlayer) {
                this.sendMessage(MessageProtocol.C2S_GET_INVENTORY, {
                    player_id: this.currentPlayer.id
                });
            }
        }
    }

    /**
     * 显示添加交易物品对话框
     */
    showAddTradeItemDialog() {
        if (!this.currentTrade) return;

        // 显示模态对话框形式的物品选择界面
        document.getElementById('tradeItemSelectionView').style.display = 'block';

        // 清空搜索和筛选
        document.getElementById('tradeItemSearchInput').value = '';
        document.getElementById('tradeItemCategoryFilter').value = '';

        // 重新加载可交易物品
        this.loadTradableItems();

        // 动态生成分类选项
        this.generateDynamicCategoryOptions();

        // 显示物品列表
        this.displayTradeItemList();
    }

    /**
     * 动态生成分类选项
     */
    generateDynamicCategoryOptions() {
        const categoryFilter = document.getElementById('tradeItemCategoryFilter');
        if (!categoryFilter || !this.inventory || !this.inventory.backpack) return;

        console.log('🏷️ 动态生成交易物品分类选项');

        // 获取玩家当前拥有的所有分类
        const availableCategories = new Set();

        this.inventory.backpack.forEach(item => {
            // 只统计可交易的物品分类（未装备且未绑定）
            const isEquipped = parseInt(item.is_equipped) === 1;
            const isBound = parseInt(item.is_bound) === 1;

            if (!isEquipped && !isBound && item.category) {
                availableCategories.add(item.category);
            }
        });

        // 分类名称映射
        const categoryNames = {
            'Equipment': '装备',
            'Potion': '药品',
            'Material': '材料',
            'Gem': '宝石',
            'Misc': '杂物',
            'Scroll': '书卷',
            'Food': '食物',
            'Tool': '工具',
            'Quest': '任务物品',
            'Currency': '货币'
        };

        // 清空现有选项（保留"所有类别"）
        categoryFilter.innerHTML = '<option value="">所有类别</option>';

        // 按字母顺序排序分类
        const sortedCategories = Array.from(availableCategories).sort();

        // 动态添加分类选项
        sortedCategories.forEach(category => {
            const option = document.createElement('option');
            option.value = category;
            option.textContent = categoryNames[category] || category;
            categoryFilter.appendChild(option);
        });

        console.log('📋 可用分类:', {
            total: sortedCategories.length,
            categories: sortedCategories
        });
    }

    /**
     * 显示交易物品列表
     */
    displayTradeItemList() {
        const container = document.getElementById('tradeItemSelectionList');

        // 确保有可交易物品数据
        if (!this.tradableItems || this.tradableItems.length === 0) {
            // 重新加载可交易物品
            this.loadTradableItems();

            if (!this.tradableItems || this.tradableItems.length === 0) {
                container.innerHTML = '<div style="text-align: center; color: #888; padding: 20px;">没有可交易的物品<br><small>（绑定和已装备的物品不能交易）</small></div>';
                return;
            }
        }

        let html = '<table style="width: 100%; font-size: 14px; border-collapse: collapse;">';
        html += '<thead><tr style="background-color: rgba(255,255,255,0.1);"><th style="padding: 8px; text-align: left;">物品名称</th><th style="padding: 8px; text-align: center;">数量</th><th style="padding: 8px; text-align: center;">操作</th></tr></thead><tbody>';

        this.tradableItems.forEach(item => {
            // 检查物品是否已经添加到交易中
            const itemId = item.inventory_id || item.id;
            const isAlreadyInTrade = this.currentTrade && this.currentTrade.myItems.some(tradeItem =>
                tradeItem.inventory_id === itemId
            );

            // 如果物品已经在交易中，跳过显示
            if (isAlreadyInTrade) {
                console.log('🚫 跳过已在交易中的物品:', {
                    itemId,
                    name: item.name
                });
                return;
            }

            let displayName = item.name;
            try {
                if (item.instance_data) {
                    const instanceData = JSON.parse(item.instance_data);
                    if (instanceData.display_name) {
                        displayName = instanceData.display_name;
                    }
                }
            } catch (e) {
                // 忽略JSON解析错误，使用默认名称
            }

            // 处理前缀后缀显示（在表格中不使用HTML标签，保持纯文本）
            const plainDisplayName = displayName.replace(/§([^§]+)§/g, '$1');

            // 转义单引号以防止JavaScript错误
            const safeName = item.name.replace(/'/g, "\\'");
            const maxQuantity = item.quantity || 1;
            const isStackable = item.stackable && maxQuantity > 1;

            // 数量显示和控制
            let quantityControl;
            if (isStackable) {
                quantityControl = `
                    <div style="display: flex; align-items: center; justify-content: center; gap: 5px;">
                        <button class="quantity-btn" onclick="game.adjustTradeQuantity(${itemId}, -1)" style="width: 20px; height: 20px; border: 1px solid var(--border-color); background: var(--bg-color); color: var(--text-color); cursor: pointer; border-radius: 3px;">-</button>
                        <span id="quantity-${itemId}" style="min-width: 30px; text-align: center;">1</span>
                        <button class="quantity-btn" onclick="game.adjustTradeQuantity(${itemId}, 1)" style="width: 20px; height: 20px; border: 1px solid var(--border-color); background: var(--bg-color); color: var(--text-color); cursor: pointer; border-radius: 3px;">+</button>
                        <span style="color: #888; font-size: 12px;">/${maxQuantity}</span>
                    </div>
                `;
            } else {
                quantityControl = `<span style="color: #888;">1</span>`;
            }

            html += `
                <tr style="border-bottom: 1px solid var(--border-color);" data-item-id="${itemId}" data-item-name="${safeName}" data-max-quantity="${maxQuantity}">
                    <td style="padding: 8px;">${plainDisplayName}</td>
                    <td style="padding: 8px; text-align: center;">${quantityControl}</td>
                    <td style="padding: 8px; text-align: center;">
                        <button class="btn-primary" onclick="game.addSelectedItemToTrade(${itemId})" style="padding: 4px 12px; font-size: 12px;">添加</button>
                    </td>
                </tr>
            `;
        });
        html += '</tbody></table>';

        container.innerHTML = html;
    }

    /**
     * 筛选交易物品
     */
    filterTradeItems() {
        const searchText = document.getElementById('tradeItemSearchInput').value.toLowerCase();
        const categoryFilter = document.getElementById('tradeItemCategoryFilter').value;

        if (!this.inventory || !this.inventory.backpack) return;

        this.tradableItems = this.inventory.backpack.filter(item => {
            // 检查是否已装备或绑定（注意数据类型转换）
            const isEquipped = item.is_equipped == 1 || item.is_equipped === true;
            const isBound = item.is_bound == 1 || item.is_bound === true;

            if (isEquipped || isBound) return false;

            const matchesSearch = !searchText || item.name.toLowerCase().includes(searchText);
            const matchesCategory = !categoryFilter || item.category === categoryFilter;

            return matchesSearch && matchesCategory;
        });

        this.displayTradeItemList();
    }

    /**
     * 调节交易物品数量
     * @param {number} itemId 物品ID
     * @param {number} delta 变化量（+1 或 -1）
     */
    adjustTradeQuantity(itemId, delta) {
        const quantitySpan = document.getElementById(`quantity-${itemId}`);
        if (!quantitySpan) return;

        const row = document.querySelector(`tr[data-item-id="${itemId}"]`);
        if (!row) return;

        const maxQuantity = parseInt(row.dataset.maxQuantity);
        let currentQuantity = parseInt(quantitySpan.textContent);

        // 计算新数量
        const newQuantity = Math.max(1, Math.min(maxQuantity, currentQuantity + delta));

        // 更新显示
        quantitySpan.textContent = newQuantity;

        // 更新按钮状态
        const minusBtn = row.querySelector('button:first-of-type');
        const plusBtn = row.querySelector('button:nth-of-type(2)');

        if (minusBtn) minusBtn.disabled = newQuantity <= 1;
        if (plusBtn) plusBtn.disabled = newQuantity >= maxQuantity;
    }

    /**
     * 添加选中的物品到交易
     * @param {number} itemId 物品ID
     */
    addSelectedItemToTrade(itemId) {
        const quantitySpan = document.getElementById(`quantity-${itemId}`);
        const quantity = quantitySpan ? parseInt(quantitySpan.textContent) : 1;

        // 添加物品到交易
        this.addItemToTrade(itemId, quantity);
    }



    /**
     * 添加物品到交易
     * @param {number} inventoryId 库存物品ID
     * @param {number} quantity 数量
     */
    addItemToTrade(inventoryId, quantity) {
        console.log('📦 尝试添加物品到交易:', {
            inventoryId,
            quantity,
            tradeId: this.currentTrade?.id,
            myConfirmed: this.currentTrade?.myConfirmed,
            partnerConfirmed: this.currentTrade?.partnerConfirmed
        });

        // 检查交易阶段和确认状态
        if (this.currentTrade && (this.currentTrade.stage === 'final_confirm' || this.currentTrade.myConfirmed)) {
            console.log('🚫 添加物品被阻止 - 交易已进入确认阶段');
            if (this.currentTrade.stage === 'final_confirm') {
                this.showActionMessage('交易已进入最终确认阶段，无法修改物品', 'error', 3000);
            } else {
                this.showActionMessage('您已确认交易，无法添加物品', 'error', 3000);
            }
            return;
        }

        console.log('📤 发送添加物品消息');
        // 发送添加物品到交易的请求
        this.sendMessage(MessageProtocol.C2S_TRADE_ADD_ITEM, {
            player_id: this.currentPlayer.id,
            trade_id: this.currentTrade.id,
            inventory_id: inventoryId,
            quantity: quantity
        });

        // 关闭物品选择对话框
        this.hideTradeItemSelectionView();
    }

    /**
     * 隐藏交易物品选择界面
     */
    hideTradeItemSelectionView() {
        document.getElementById('tradeItemSelectionView').style.display = 'none';
    }

    /**
     * 更新交易货币
     */
    updateTradeCurrency() {
        if (!this.currentTrade) return;

        const goldAmount = parseInt(document.getElementById('myGoldInput').value) || 0;
        const diamondAmount = parseInt(document.getElementById('myDiamondInput').value) || 0;

        console.log('💰 尝试更新交易货币:', {
            goldAmount,
            diamondAmount,
            tradeId: this.currentTrade.id,
            myConfirmed: this.currentTrade.myConfirmed,
            partnerConfirmed: this.currentTrade.partnerConfirmed,
            playerGold: this.currentPlayer.attributes.gold,
            playerDiamonds: this.currentPlayer.attributes.diamonds
        });

        // 检查交易阶段和确认状态
        if (this.currentTrade.stage === 'final_confirm' || this.currentTrade.myConfirmed) {
            console.log('🚫 货币更新被阻止 - 交易已进入确认阶段');
            if (this.currentTrade.stage === 'final_confirm') {
                this.showActionMessage('交易已进入最终确认阶段，无法修改货币', 'error', 3000);
            } else {
                this.showActionMessage('您已确认交易，无法修改货币', 'error', 3000);
            }
            return;
        }

        // 验证货币余额
        if (this.currentPlayer.attributes.gold < goldAmount) {
            console.log('⚠️ 金币余额不足，重置输入');
            document.getElementById('myGoldInput').value = this.currentPlayer.attributes.gold;
            return;
        }

        if (this.currentPlayer.attributes.diamonds < diamondAmount) {
            console.log('⚠️ 钻石余额不足，重置输入');
            document.getElementById('myDiamondInput').value = this.currentPlayer.attributes.diamonds;
            return;
        }

        console.log('📤 发送货币更新消息');
        // 发送货币更新请求
        this.sendMessage(MessageProtocol.C2S_TRADE_ADD_CURRENCY, {
            player_id: this.currentPlayer.id,
            trade_id: this.currentTrade.id,
            gold_amount: goldAmount,
            diamond_amount: diamondAmount
        });
    }

    /**
     * 确认交易
     */
    confirmTrade() {
        if (!this.currentTrade) return;

        console.log('🔒 尝试确认交易:', {
            tradeId: this.currentTrade.id,
            stage: this.currentTrade.stage,
            currentPlayerId: this.currentPlayer.id,
            partnerId: this.currentTrade.partnerId,
            myConfirmed: this.currentTrade.myConfirmed,
            partnerConfirmed: this.currentTrade.partnerConfirmed,
            myFinalConfirmed: this.currentTrade.myFinalConfirmed,
            partnerFinalConfirmed: this.currentTrade.partnerFinalConfirmed
        });

        if (this.currentTrade.stage === 'preparation') {
            // 初次确认阶段
            if (this.currentTrade.myConfirmed) {
                console.log('⚠️ 重复初次确认被阻止');
                this.showActionMessage('您已经确认过交易了', 'warning', 3000);
                return;
            }

            console.log('📤 发送初次确认交易消息');
            this.sendMessage(MessageProtocol.C2S_TRADE_CONFIRM, {
                player_id: this.currentPlayer.id,
                trade_id: this.currentTrade.id
            });

            // 立即更新本地状态
            console.log('🔄 立即更新本地初次确认状态');
            this.currentTrade.myConfirmed = true;
            this.updateTradeInterface();

        } else if (this.currentTrade.stage === 'final_confirm') {
            // 二次确认阶段
            if (this.currentTrade.myFinalConfirmed) {
                console.log('⚠️ 重复最终确认被阻止');
                this.showActionMessage('您已经最终确认过交易了', 'warning', 3000);
                return;
            }

            console.log('📤 发送最终确认交易消息');
            this.sendMessage(MessageProtocol.C2S_TRADE_FINAL_CONFIRM, {
                player_id: this.currentPlayer.id,
                trade_id: this.currentTrade.id
            });

            // 立即更新本地状态
            console.log('🔄 立即更新本地最终确认状态');
            this.currentTrade.myFinalConfirmed = true;
            this.updateTradeInterface();
        }
    }

    /**
     * 取消交易
     */
    cancelTrade() {
        if (!this.currentTrade) return;

        this.sendMessage(MessageProtocol.C2S_TRADE_CANCEL, {
            player_id: this.currentPlayer.id,
            trade_id: this.currentTrade.id
        });
    }

    /**
     * 处理交易更新
     * @param {Object} payload 消息载荷
     */
    handleTradeUpdate(payload) {
        if (!this.currentTrade) return;

        const { trade, items, currencies } = payload;

        // 更新交易状态
        this.currentTrade.myConfirmed = trade.initiator_confirmed && trade.initiator_id === this.currentPlayer.id ||
                                       trade.target_confirmed && trade.target_id === this.currentPlayer.id;
        this.currentTrade.partnerConfirmed = trade.initiator_confirmed && trade.initiator_id !== this.currentPlayer.id ||
                                           trade.target_confirmed && trade.target_id !== this.currentPlayer.id;

        // 更新物品显示
        this.updateTradeItemsDisplay(items);

        // 更新货币显示
        this.updateTradeCurrencyDisplay(currencies);

        // 更新确认按钮状态
        this.updateTradeConfirmButton();

        // 如果物品选择界面正在显示，刷新可选物品列表
        const tradeItemSelectionView = document.getElementById('tradeItemSelectionView');
        if (tradeItemSelectionView && tradeItemSelectionView.style.display === 'block') {
            console.log('🔄 交易更新，刷新物品选择列表');
            this.displayTradeItemList();
        }
    }

    /**
     * 更新交易物品显示
     * @param {Array} items 交易物品列表
     */
    updateTradeItemsDisplay(items) {
        const myItems = items.filter(item => item.player_id === this.currentPlayer.id);
        const partnerItems = items.filter(item => item.player_id !== this.currentPlayer.id);

        // 更新我的物品
        const myItemsContainer = document.getElementById('myTradeItems');
        if (myItems.length === 0) {
            myItemsContainer.innerHTML = '<div style="text-align: center; color: #888; font-size: 12px; padding: 20px;">暂无物品</div>';
        } else {
            let html = '';
            myItems.forEach(item => {
                // 处理物品名称（包含前缀后缀）
                let displayName = item.item_name;
                if (item.instance_data) {
                    try {
                        const instanceData = JSON.parse(item.instance_data);
                        if (instanceData.display_name) {
                            displayName = instanceData.display_name;
                        }
                    } catch (e) {
                        console.log('解析instance_data失败:', e);
                    }
                }

                // 处理前缀后缀显示
                displayName = displayName.replace(/§([^§]+)§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">$1</span>');

                html += `
                    <div style="padding: 5px; border-bottom: 1px dashed #ccc; display: flex; justify-content: space-between; align-items: center;">
                        <span style="cursor: pointer; color: #3366cc; text-decoration: underline;" onclick="game.showTradeItemDetail(${JSON.stringify(item).replace(/"/g, '&quot;')})">${displayName} x${item.quantity}</span>
                        <button class="btn-danger" style="font-size: 10px; padding: 2px 6px;" onclick="game.removeTradeItem(${item.id})">删</button>
                    </div>
                `;
            });
            myItemsContainer.innerHTML = html;
        }

        // 更新对方物品
        const partnerItemsContainer = document.getElementById('partnerTradeItems');
        if (partnerItems.length === 0) {
            partnerItemsContainer.innerHTML = '<div style="text-align: center; color: #888; font-size: 12px; padding: 20px;">暂无物品</div>';
        } else {
            let html = '';
            partnerItems.forEach(item => {
                // 处理物品名称（包含前缀后缀）
                let displayName = item.item_name;
                if (item.instance_data) {
                    try {
                        const instanceData = JSON.parse(item.instance_data);
                        if (instanceData.display_name) {
                            displayName = instanceData.display_name;
                        }
                    } catch (e) {
                        console.log('解析instance_data失败:', e);
                    }
                }

                // 处理前缀后缀显示
                displayName = displayName.replace(/§([^§]+)§/g, '<span style="color: #a02c2c; font-weight: bold; margin: 0 2px;">$1</span>');

                html += `
                    <div style="padding: 5px; border-bottom: 1px dashed #ccc;">
                        <span style="cursor: pointer; color: #3366cc; text-decoration: underline;" onclick="game.showTradeItemDetail(${JSON.stringify(item).replace(/"/g, '&quot;')})">${displayName} x${item.quantity}</span>
                    </div>
                `;
            });
            partnerItemsContainer.innerHTML = html;
        }
    }

    /**
     * 更新交易货币显示
     * @param {Array} currencies 交易货币列表
     */
    updateTradeCurrencyDisplay(currencies) {
        const myCurrency = currencies.find(c => c.player_id === this.currentPlayer.id);
        const partnerCurrency = currencies.find(c => c.player_id !== this.currentPlayer.id);

        // 更新我的货币输入
        if (myCurrency) {
            document.getElementById('myGoldInput').value = myCurrency.gold_amount;
            document.getElementById('myDiamondInput').value = myCurrency.diamond_amount;
        }

        // 更新对方货币显示
        const partnerCurrencyContainer = document.getElementById('partnerCurrency');
        if (partnerCurrency && (partnerCurrency.gold_amount > 0 || partnerCurrency.diamond_amount > 0)) {
            let html = '';
            if (partnerCurrency.gold_amount > 0) {
                html += `<div>金币: ${partnerCurrency.gold_amount}</div>`;
            }
            if (partnerCurrency.diamond_amount > 0) {
                html += `<div>钻石: ${partnerCurrency.diamond_amount}</div>`;
            }
            partnerCurrencyContainer.innerHTML = html;
        } else {
            partnerCurrencyContainer.innerHTML = '暂无货币';
        }
    }

    /**
     * 更新交易确认按钮状态
     */
    updateTradeConfirmButton() {
        const confirmBtn = document.getElementById('myConfirmBtn');
        const statusDiv = document.getElementById('myConfirmStatus');

        // 检查是否有物品或货币
        const hasItems = document.getElementById('myTradeItems').innerHTML.indexOf('暂无物品') === -1;
        const goldAmount = parseInt(document.getElementById('myGoldInput').value) || 0;
        const diamondAmount = parseInt(document.getElementById('myDiamondInput').value) || 0;
        const hasCurrency = goldAmount > 0 || diamondAmount > 0;

        if (hasItems || hasCurrency) {
            confirmBtn.disabled = false;
            statusDiv.textContent = this.currentTrade.myConfirmed ? '已确认' : '点击确认交易';
            statusDiv.style.color = this.currentTrade.myConfirmed ? '#4CAF50' : '#888';
        } else {
            confirmBtn.disabled = true;
            statusDiv.textContent = '等待添加物品或货币';
            statusDiv.style.color = '#888';
        }
    }

    /**
     * 移除交易物品
     * @param {number} tradeItemId 交易物品ID
     */
    removeTradeItem(tradeItemId) {
        if (!this.currentTrade) return;

        // 检查交易阶段和确认状态
        if (this.currentTrade.stage === 'final_confirm' || this.currentTrade.myConfirmed) {
            console.log('🚫 移除物品被阻止 - 交易已进入确认阶段');
            if (this.currentTrade.stage === 'final_confirm') {
                this.showActionMessage('交易已进入最终确认阶段，无法移除物品', 'error', 3000);
            } else {
                this.showActionMessage('您已确认交易，无法移除物品', 'error', 3000);
            }
            return;
        }

        console.log('🗑️ 尝试移除交易物品:', {
            tradeItemId,
            tradeId: this.currentTrade.id,
            stage: this.currentTrade.stage,
            myConfirmed: this.currentTrade.myConfirmed
        });

        this.sendMessage(MessageProtocol.C2S_TRADE_REMOVE_ITEM, {
            player_id: this.currentPlayer.id,
            trade_id: this.currentTrade.id,
            trade_item_id: tradeItemId
        });
    }

    /**
     * 处理交易确认状态更新
     * @param {Object} payload 消息载荷
     */
    handleTradeConfirmed(payload) {
        if (!this.currentTrade) return;

        const { initiator_confirmed, target_confirmed, stage } = payload;

        console.log('✅ 收到交易确认状态更新:', {
            payload,
            currentPlayerId: this.currentPlayer.id,
            partnerId: this.currentTrade.partnerId,
            tradeId: this.currentTrade.id
        });

        // 使用保存的角色信息
        const isInitiator = this.currentTrade.isInitiator;

        console.log('🔍 角色判断:', {
            isInitiator,
            logic: `使用服务器明确告知的角色: ${isInitiator}`
        });

        // 更新确认状态
        if (isInitiator) {
            // 当前玩家是发起者
            this.currentTrade.myConfirmed = initiator_confirmed;
            this.currentTrade.partnerConfirmed = target_confirmed;
            console.log('👤 发起者状态更新:', {
                myConfirmed: initiator_confirmed,
                partnerConfirmed: target_confirmed
            });
        } else {
            // 当前玩家是目标者
            this.currentTrade.myConfirmed = target_confirmed;
            this.currentTrade.partnerConfirmed = initiator_confirmed;
            console.log('🎯 目标者状态更新:', {
                myConfirmed: target_confirmed,
                partnerConfirmed: initiator_confirmed
            });
        }

        console.log('📊 最终交易状态:', {
            myConfirmed: this.currentTrade.myConfirmed,
            partnerConfirmed: this.currentTrade.partnerConfirmed
        });

        // 更新交易阶段（如果服务器发送了阶段信息）
        if (stage && stage !== this.currentTrade.stage) {
            console.log(`🔄 交易阶段变更: ${this.currentTrade.stage} → ${stage}`);
            this.currentTrade.stage = stage;

            if (stage === 'final_confirm') {
                console.log('🎯 进入二次确认阶段');
                this.showFinalConfirmDialog();
            }
        }

        // 更新界面显示
        this.updateTradeInterface();

        const partnerStatus = document.getElementById('partnerConfirmStatus');
        if (this.currentTrade.partnerConfirmed) {
            partnerStatus.textContent = '对方已确认';
            partnerStatus.style.color = '#4CAF50';
        } else {
            partnerStatus.textContent = '等待对方确认...';
            partnerStatus.style.color = '#888';
        }
    }

    /**
     * 更新交易界面状态
     */
    updateTradeInterface() {
        if (!this.currentTrade) return;

        // 根据交易阶段和确认状态决定是否锁定
        // 在二次确认阶段或当前玩家已确认时锁定所有操作
        const shouldLock = this.currentTrade.stage === 'final_confirm' || this.currentTrade.myConfirmed;

        console.log('🎨 更新交易界面状态:', {
            stage: this.currentTrade.stage,
            myConfirmed: this.currentTrade.myConfirmed,
            partnerConfirmed: this.currentTrade.partnerConfirmed,
            shouldLock: shouldLock
        });

        // 禁用/启用添加物品按钮
        const addItemBtn = document.getElementById('addMyItemBtn');
        if (addItemBtn) {
            addItemBtn.disabled = shouldLock;
            addItemBtn.style.opacity = shouldLock ? '0.5' : '1';
            addItemBtn.style.cursor = shouldLock ? 'not-allowed' : 'pointer';
            console.log('🔘 添加物品按钮状态:', { disabled: shouldLock });
        }

        // 禁用/启用金币输入
        const goldInput = document.getElementById('myGoldInput');
        if (goldInput) {
            goldInput.disabled = shouldLock;
            goldInput.style.opacity = shouldLock ? '0.5' : '1';
            console.log('💰 金币输入状态:', { disabled: shouldLock });
        }

        // 禁用/启用钻石输入
        const diamondInput = document.getElementById('myDiamondInput');
        if (diamondInput) {
            diamondInput.disabled = shouldLock;
            diamondInput.style.opacity = shouldLock ? '0.5' : '1';
            console.log('💎 钻石输入状态:', { disabled: shouldLock });
        }

        // 禁用/启用移除物品按钮
        const removeButtons = document.querySelectorAll('.remove-trade-item-btn');
        removeButtons.forEach(btn => {
            btn.disabled = shouldLock;
            btn.style.opacity = shouldLock ? '0.5' : '1';
            btn.style.cursor = shouldLock ? 'not-allowed' : 'pointer';
        });
        console.log('🗑️ 移除按钮状态:', { count: removeButtons.length, disabled: shouldLock });

        // 更新确认按钮和状态提示
        this.updateConfirmButton();
        this.updateStatusMessage();
    }

    /**
     * 更新确认按钮状态
     */
    updateConfirmButton() {
        const confirmBtn = document.getElementById('myConfirmBtn');
        if (!confirmBtn || !this.currentTrade) return;

        switch (this.currentTrade.stage) {
            case 'preparation':
                // 准备阶段：显示"确认交易"按钮
                if (this.currentTrade.myConfirmed) {
                    confirmBtn.textContent = '已确认';
                    confirmBtn.disabled = true;
                    confirmBtn.style.backgroundColor = '#4CAF50';
                } else {
                    confirmBtn.textContent = '确认交易';
                    confirmBtn.disabled = false;
                    confirmBtn.style.backgroundColor = '#2196F3';
                }
                break;

            case 'final_confirm':
                // 二次确认阶段：显示"最终确认"按钮
                if (this.currentTrade.myFinalConfirmed) {
                    confirmBtn.textContent = '已最终确认';
                    confirmBtn.disabled = true;
                    confirmBtn.style.backgroundColor = '#4CAF50';
                } else {
                    confirmBtn.textContent = '最终确认';
                    confirmBtn.disabled = false;
                    confirmBtn.style.backgroundColor = '#FF5722';
                }
                break;

            default:
                confirmBtn.disabled = true;
                break;
        }
    }

    /**
     * 更新状态提示信息
     */
    updateStatusMessage() {
        const statusDiv = document.getElementById('tradeStatusMessage');
        if (!statusDiv || !this.currentTrade) return;

        switch (this.currentTrade.stage) {
            case 'preparation':
                if (this.currentTrade.myConfirmed) {
                    statusDiv.textContent = '您已确认交易，等待对方确认';
                    statusDiv.style.color = '#ff9800';
                    statusDiv.style.display = 'block';
                } else if (this.currentTrade.partnerConfirmed) {
                    statusDiv.textContent = '对方已确认，请您确认交易';
                    statusDiv.style.color = '#4CAF50';
                    statusDiv.style.display = 'block';
                } else {
                    statusDiv.style.display = 'none';
                }
                break;

            case 'final_confirm':
                if (this.currentTrade.myFinalConfirmed) {
                    statusDiv.textContent = '您已最终确认，等待对方最终确认';
                    statusDiv.style.color = '#ff9800';
                    statusDiv.style.display = 'block';
                } else if (this.currentTrade.partnerFinalConfirmed) {
                    statusDiv.textContent = '对方已最终确认，请您最终确认';
                    statusDiv.style.color = '#FF5722';
                    statusDiv.style.display = 'block';
                } else {
                    statusDiv.textContent = '请双方检查交易内容并最终确认';
                    statusDiv.style.color = '#FF5722';
                    statusDiv.style.display = 'block';
                }
                break;

            default:
                statusDiv.style.display = 'none';
                break;
        }
    }

    /**
     * 处理最终确认状态更新
     * @param {Object} payload 消息载荷
     */
    handleTradeFinalConfirmed(payload) {
        if (!this.currentTrade) return;

        const { initiator_final_confirmed, target_final_confirmed } = payload;

        console.log('✅ 收到最终确认状态更新:', {
            payload,
            currentPlayerId: this.currentPlayer.id,
            partnerId: this.currentTrade.partnerId,
            tradeId: this.currentTrade.id
        });

        // 使用保存的角色信息
        const isInitiator = this.currentTrade.isInitiator;

        console.log('🔍 最终确认角色判断:', {
            isInitiator,
            logic: `使用服务器明确告知的角色: ${isInitiator}`
        });

        // 更新最终确认状态
        if (isInitiator) {
            // 当前玩家是发起者
            this.currentTrade.myFinalConfirmed = initiator_final_confirmed;
            this.currentTrade.partnerFinalConfirmed = target_final_confirmed;
            console.log('👤 发起者最终确认状态更新:', {
                myFinalConfirmed: initiator_final_confirmed,
                partnerFinalConfirmed: target_final_confirmed
            });
        } else {
            // 当前玩家是目标者
            this.currentTrade.myFinalConfirmed = target_final_confirmed;
            this.currentTrade.partnerFinalConfirmed = initiator_final_confirmed;
            console.log('🎯 目标者最终确认状态更新:', {
                myFinalConfirmed: target_final_confirmed,
                partnerFinalConfirmed: initiator_final_confirmed
            });
        }

        console.log('📊 最终确认状态:', {
            myFinalConfirmed: this.currentTrade.myFinalConfirmed,
            partnerFinalConfirmed: this.currentTrade.partnerFinalConfirmed
        });

        // 更新界面显示
        this.updateTradeInterface();
    }

    /**
     * 显示二次确认对话框
     */
    showFinalConfirmDialog() {
        console.log('🎯 显示二次确认对话框');

        // 显示交易摘要
        this.showActionMessage('双方已确认交易内容，请进行最终确认', 'info', 5000);

        // 更新状态提示
        const statusDiv = document.getElementById('tradeStatusMessage');
        if (statusDiv) {
            statusDiv.textContent = '请双方检查交易内容并最终确认';
            statusDiv.style.color = '#FF5722';
            statusDiv.style.backgroundColor = 'transparent';
            statusDiv.style.display = 'block';
        }
    }

    /**
     * 处理交易完成
     * @param {Object} payload 消息载荷
     */
    handleTradeCompleted(payload) {
        this.addLog('交易完成！');

        // 显示交易完成消息
        const statusMessage = document.getElementById('tradeStatusMessage');
        statusMessage.textContent = '交易已完成！';
        statusMessage.style.backgroundColor = '#4CAF50';
        statusMessage.style.color = 'white';
        statusMessage.style.display = 'block';

        // 3秒后关闭交易界面
        setTimeout(() => {
            this.closeTradeView();
        }, 3000);
    }

    /**
     * 处理交易取消
     * @param {Object} payload 消息载荷
     */
    handleTradeCancelled(payload) {
        const reason = payload.reason || '交易已取消';
        this.addLog(reason);

        // 显示取消消息
        const statusMessage = document.getElementById('tradeStatusMessage');
        statusMessage.textContent = reason;
        statusMessage.style.backgroundColor = '#f44336';
        statusMessage.style.color = 'white';
        statusMessage.style.display = 'block';

        // 2秒后关闭交易界面
        setTimeout(() => {
            this.closeTradeView();
        }, 2000);
    }

    /**
     * 处理交易错误
     * @param {Object} payload 消息载荷
     */
    handleTradeError(payload) {
        const message = payload.message || '交易出现错误';
        this.addLog(message);

        // 显示错误消息
        const errorDiv = document.getElementById('tradeItemSelectionError');
        if (errorDiv && document.getElementById('tradeItemSelectionView').style.display !== 'none') {
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        } else {
            const statusMessage = document.getElementById('tradeStatusMessage');
            statusMessage.textContent = message;
            statusMessage.style.backgroundColor = '#f44336';
            statusMessage.style.color = 'white';
            statusMessage.style.display = 'block';

            setTimeout(() => {
                statusMessage.style.display = 'none';
            }, 3000);
        }
    }

    /**
     * 关闭交易界面
     */
    closeTradeView() {
        console.log('🔄 关闭交易界面，重置所有状态');

        // 清理交易状态
        this.currentTrade = null;

        // 重置界面元素状态
        this.resetTradeInterface();

        // 隐藏交易界面
        document.getElementById('tradeView').style.display = 'none';
        document.getElementById('tradeItemSelectionView').style.display = 'none';

        // 返回主界面
        this.hideAllViews();
        document.getElementById('main-view').style.display = 'block';
    }

    /**
     * 检查玩家是否在同一场景
     * @param {number} targetPlayerId 目标玩家ID
     * @returns {boolean}
     */
    isPlayerInSameScene(targetPlayerId) {
        // 检查玩家是否在当前场景的玩家列表中
        if (!this.players || this.players.length === 0) return false;
        return this.players.some(player => player.id == targetPlayerId);
    }

    /**
     * 处理成功消息
     * @param {Object} payload 消息载荷
     */
    handleSuccessMessage(payload) {
        console.log('GameClient.handleSuccessMessage - 收到成功消息:', payload);

        if (payload.message) {
            this.addLog(payload.message);
        }

        // 如果当前在仓库界面，刷新仓库数据和背包数据
        if (this.buildingManager && this.buildingManager.currentBuilding &&
            this.buildingManager.currentBuilding.type === 'WAREHOUSE') {
            console.log('GameClient.handleSuccessMessage - 刷新仓库数据');

            // 设置待显示的仓库操作消息
            this.buildingManager.pendingWarehouseMessage = {
                message: payload.message,
                type: 'success'
            };

            // 刷新仓库数据
            this.sendMessage(MessageProtocol.C2S_GET_BUILDING_DATA, {
                scene_building_id: this.buildingManager.currentBuilding.scene_building_id,
                player_id: this.currentPlayer.id
            });
            // 刷新背包数据
            this.sendMessage(MessageProtocol.C2S_GET_INVENTORY, {
                player_id: this.currentPlayer.id
            });
        }
    }

    // ==================== 公告系统相关方法 ====================

    // 获取最新公告
    requestLatestAnnouncement() {
        this.sendMessage(MessageProtocol.C2S_GET_LATEST_ANNOUNCEMENT);
    }

    // 处理最新公告数据
    handleLatestAnnouncement(payload) {
        console.log('收到最新公告数据:', payload);

        if (payload.announcement) {
            this.currentAnnouncement = payload.announcement;
            this.updateAnnouncementBar();
        } else {
            // 没有公告时隐藏公告栏
            const announcementBar = document.getElementById('announcementBar');
            if (announcementBar) {
                announcementBar.style.display = 'none';
            }
        }
    }

    // 更新公告栏显示
    updateAnnouncementBar() {
        const announcementBar = document.getElementById('announcementBar');
        if (!announcementBar || !this.currentAnnouncement) return;

        const announcement = this.currentAnnouncement;
        let summary = announcement.summary || announcement.title;

        // 确保摘要不超过18个字符，防止换行
        if (summary && summary.length > 18) {
            summary = summary.substring(0, 18);
        }

        // 设置公告栏内容
        announcementBar.innerHTML = `[公告] ${this.escapeHtml(summary)}`;

        // 根据重要性设置样式
        if (announcement.is_important) {
            announcementBar.style.borderLeftColor = '#e74c3c';
            announcementBar.style.backgroundColor = '#2c3e50';
        } else {
            announcementBar.style.borderLeftColor = '#3498db';
            announcementBar.style.backgroundColor = '#2c3e50';
        }

        announcementBar.style.display = 'block';
    }

    // 显示公告详情
    showAnnouncementDetail() {
        if (!this.currentAnnouncement) {
            this.addLog('暂无公告信息');
            return;
        }

        // 请求完整的公告详情
        this.sendMessage(MessageProtocol.C2S_GET_ANNOUNCEMENT_DETAIL, {
            announcement_id: this.currentAnnouncement.id
        });
    }

    // 处理公告详情数据
    handleAnnouncementDetail(payload) {
        console.log('收到公告详情数据:', payload);

        if (!payload.announcement) {
            this.addLog('获取公告详情失败');
            return;
        }

        const announcement = payload.announcement;

        // 更新公告详情界面
        const titleEl = document.getElementById('announcementTitle');
        const metaEl = document.getElementById('announcementMeta');
        const contentEl = document.getElementById('announcementContent');

        if (titleEl) {
            titleEl.textContent = announcement.title;
        }

        if (metaEl) {
            const createdDate = announcement.created_date;
            const viewCount = announcement.view_count || 0;
            metaEl.innerHTML = `发布时间: ${createdDate} | 查看次数: ${viewCount}`;
        }

        if (contentEl) {
            // 安全地显示HTML内容（服务器端已经过滤）
            contentEl.innerHTML = announcement.content;
        }

        // 显示公告详情界面
        this.hideAllViews();
        document.getElementById('announcementView').style.display = 'block';
    }

    // 隐藏公告详情界面
    hideAnnouncementView() {
        document.getElementById('announcementView').style.display = 'none';
        document.getElementById('main-view').style.display = 'block';
    }

    // 显示公告列表界面
    showAnnouncementListView() {
        this.hideAllViews();

        // 显示界面
        const view = document.getElementById('announcementListView');
        if (view) {
            view.style.display = 'block';

            // 加载公告列表
            this.loadAnnouncementList();
        }
    }

    // 隐藏公告列表界面
    hideAnnouncementListView() {
        const view = document.getElementById('announcementListView');
        if (view) {
            view.style.display = 'none';
        }
        document.getElementById('main-view').style.display = 'block';
    }

    // 从公告详情返回公告列表
    backToAnnouncementList() {
        document.getElementById('announcementView').style.display = 'none';
        this.showAnnouncementListView();
    }

    // 加载公告列表
    loadAnnouncementList() {
        console.log('=== 请求公告列表 ===');

        // 发送获取公告列表的请求
        this.sendMessage(MessageProtocol.C2S_GET_ANNOUNCEMENT_LIST, {});
    }

    // 显示装备凝练榜界面
    showRefineLeaderboardView() {
        this.hideAllViews();

        // 显示界面
        const view = document.getElementById('refineLeaderboardView');
        if (view) {
            view.style.display = 'block';

            // 加载凝练榜数据
            this.loadRefineLeaderboard();
        }
    }

    // 隐藏装备凝练榜界面
    hideRefineLeaderboardView() {
        const view = document.getElementById('refineLeaderboardView');
        if (view) {
            view.style.display = 'none';
        }
        document.getElementById('main-view').style.display = 'block';
    }

    // 刷新装备凝练榜
    refreshRefineLeaderboard() {
        this.loadRefineLeaderboard();
    }

    // 加载装备凝练榜数据
    loadRefineLeaderboard() {
        console.log('=== 请求装备凝练榜 ===');

        // 显示加载状态
        const content = document.getElementById('refine-leaderboard-content');
        if (content) {
            content.innerHTML = '<div class="loading" style="text-align: center; padding: 20px; color: #6c757d;">正在加载神兵榜...</div>';
        }

        // 发送获取装备凝练榜的请求
        this.sendMessage(MessageProtocol.C2S_GET_REFINE_LEADERBOARD, {});
    }

    // 处理公告列表数据
    handleAnnouncementList(payload) {
        console.log('=== 收到公告列表数据 ===');
        console.log('公告列表:', payload);

        const contentEl = document.getElementById('announcement-list-content');
        console.log('找到的内容元素:', contentEl);
        if (!contentEl) {
            console.error('未找到 announcement-list-content 元素');
            return;
        }

        // 检查数据结构，服务端返回的数据可能没有success字段
        const announcements = payload.announcements || [];
        console.log('解析的公告数组:', announcements);
        console.log('公告数量:', announcements.length);

        if (announcements && announcements.length > 0) {
            let html = '';

            announcements.forEach((announcement, index) => {
                console.log(`处理公告 ${index}:`, announcement);

                // 简化处理，先使用基本字段
                const title = announcement.title || '无标题';
                const summary = announcement.summary || announcement.content || '暂无内容';
                const id = announcement.id || index;

                // 处理日期 - 根据实际数据结构使用 created_date
                let publishDate = '未知日期';
                try {
                    const dateStr = announcement.created_date;
                    if (dateStr) {
                        publishDate = new Date(dateStr).toLocaleDateString('zh-CN');
                    }
                } catch (e) {
                    console.log('日期解析错误:', e);
                }

                // 判断是否为新公告
                const isNew = this.isAnnouncementNew(announcement.created_date);

                html += `
                    <div class="announcement-item" style="border-bottom: 1px solid var(--border-color); padding: 10px 0; cursor: pointer;"
                         onclick="game.openAnnouncementFromList(${id})">
                        <div class="announcement-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                            <h4 style="margin: 0; color: #2c3e50; font-size: 14px;">
                                ${this.escapeHtml(title)}
                                ${isNew ? '<span style="color: #e74c3c; font-size: 12px; margin-left: 5px;">[新]</span>' : ''}
                            </h4>
                            <span style="color: #6c757d; font-size: 12px;">${publishDate}</span>
                        </div>
                        <div class="announcement-summary" style="color: #555; font-size: 13px; line-height: 1.4;">
                            ${this.escapeHtml(this.truncateText(summary, 80))}
                        </div>
                    </div>
                `;
            });

            console.log('生成的HTML长度:', html.length);
            console.log('HTML内容预览:', html.substring(0, 200) + '...');
            contentEl.innerHTML = html;
            console.log('HTML已设置到元素中');
        } else {
            contentEl.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <p>暂无公告</p>
                </div>
            `;
        }
    }

    // 从列表打开公告详情
    openAnnouncementFromList(announcementId) {
        console.log('=== 从列表打开公告详情 ===');
        console.log('公告ID:', announcementId);

        // 隐藏列表界面，显示详情界面
        document.getElementById('announcementListView').style.display = 'none';
        this.showAnnouncementView(announcementId);
    }

    // 显示公告详情界面
    showAnnouncementView(announcementId) {
        // 请求公告详情
        this.sendMessage(MessageProtocol.C2S_GET_ANNOUNCEMENT_DETAIL, {
            announcement_id: announcementId
        });
    }

    // 判断公告是否为新公告（7天内发布）
    isAnnouncementNew(createdDate) {
        if (!createdDate) return false;
        const publishDate = new Date(createdDate);
        const now = new Date();
        const diffTime = now - publishDate;
        const diffDays = diffTime / (1000 * 60 * 60 * 24);
        return diffDays <= 7;
    }

    // 截断文本
    truncateText(text, maxLength) {
        if (!text) return '';
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }

    // 显示兑换码界面
    showRedemptionCodeView() {
        this.hideAllViews();

        // 显示界面
        const view = document.getElementById('redemptionCodeView');
        if (view) {
            view.style.display = 'block';

            // 清空输入框和结果
            this.clearRedemptionInput();
            this.hideRedemptionResult();
        }
    }

    // 隐藏兑换码界面
    hideRedemptionCodeView() {
        const view = document.getElementById('redemptionCodeView');
        if (view) {
            view.style.display = 'none';
        }
        document.getElementById('main-view').style.display = 'block';
    }

    // 清空兑换码输入
    clearRedemptionInput() {
        const input = document.getElementById('redemption-code-input');
        if (input) {
            input.value = '';
            input.focus();
        }
    }

    // 隐藏兑换结果
    hideRedemptionResult() {
        const resultSection = document.getElementById('redemption-result');
        if (resultSection) {
            resultSection.style.display = 'none';
        }
    }

    // 兑换码兑换
    redeemCode() {
        const input = document.getElementById('redemption-code-input');
        const button = document.getElementById('redeem-button');

        if (!input || !button) return;

        const code = input.value.trim().toUpperCase();

        // 验证兑换码格式
        if (!code) {
            this.showRedemptionError('请输入兑换码');
            return;
        }

        if (code.length !== 6) {
            this.showRedemptionError('兑换码必须是6位字符');
            return;
        }

        if (!/^[A-Z0-9]{6}$/.test(code)) {
            this.showRedemptionError('兑换码只能包含大写字母和数字');
            return;
        }

        // 禁用按钮，防止重复提交
        button.disabled = true;
        button.textContent = '[兑换中...]';

        console.log('=== 发送兑换码请求 ===');
        console.log('兑换码:', code);

        // 发送兑换请求
        this.sendMessage(MessageProtocol.C2S_REDEEM_CODE, {
            code: code
        });

        // 5秒后恢复按钮状态（防止卡死）
        setTimeout(() => {
            button.disabled = false;
            button.textContent = '[兑换]';
        }, 5000);
    }

    // 显示兑换错误
    showRedemptionError(message) {
        const resultSection = document.getElementById('redemption-result');
        const titleEl = document.getElementById('redemption-result-title');
        const contentEl = document.getElementById('redemption-result-content');

        if (resultSection && titleEl && contentEl) {
            titleEl.textContent = '兑换失败';
            titleEl.style.color = '#e74c3c';

            contentEl.innerHTML = `
                <div style="color: #e74c3c; font-weight: bold; text-align: center; padding: 10px;">
                    ${this.escapeHtml(message)}
                </div>
            `;

            resultSection.style.display = 'block';
            resultSection.style.borderColor = '#e74c3c';
            resultSection.style.backgroundColor = 'rgba(231, 76, 60, 0.05)';
        }
    }

    // 显示兑换成功结果
    showRedemptionSuccess(result) {
        const resultSection = document.getElementById('redemption-result');
        const titleEl = document.getElementById('redemption-result-title');
        const contentEl = document.getElementById('redemption-result-content');

        if (resultSection && titleEl && contentEl) {
            titleEl.textContent = '兑换成功';
            titleEl.style.color = '#27ae60';

            let itemsHtml = '';
            if (result.items && result.items.length > 0) {
                itemsHtml = '<div style="margin-top: 10px;"><strong>获得物品：</strong><ul style="margin: 5px 0; padding-left: 20px;">';
                result.items.forEach(item => {
                    const boundText = item.is_bound ? ' <span style="color: #f39c12;">[绑定]</span>' : '';
                    itemsHtml += `<li>${this.escapeHtml(item.item_name)} x${item.quantity}${boundText}</li>`;
                });
                itemsHtml += '</ul></div>';
            }

            contentEl.innerHTML = `
                <div style="color: #27ae60; font-weight: bold; text-align: center; padding: 10px;">
                    ${this.escapeHtml(result.message)}
                </div>
                ${result.code_name ? `<div style="text-align: center; margin: 10px 0; color: #7f8c8d; font-size: 14px;">兑换码：${this.escapeHtml(result.code_name)}</div>` : ''}
                ${itemsHtml}
            `;

            resultSection.style.display = 'block';
            resultSection.style.borderColor = '#27ae60';
            resultSection.style.backgroundColor = 'rgba(39, 174, 96, 0.05)';

            // 清空输入框
            const input = document.getElementById('redemption-code-input');
            if (input) {
                input.value = '';
            }
        }
    }

    // 处理兑换码兑换结果
    handleRedemptionCodeResult(payload) {
        console.log('=== 收到兑换码结果 ===');
        console.log('结果:', payload);

        // 恢复按钮状态
        const button = document.getElementById('redeem-button');
        if (button) {
            button.disabled = false;
            button.textContent = '[兑换]';
        }

        if (payload.success) {
            // 兑换成功
            this.showRedemptionSuccess(payload);

            // 显示系统消息
            this.addLog(payload.message || '兑换成功！');

            // 如果当前在背包界面，刷新背包数据
            const inventoryView = document.getElementById('inventory-view');
            if (inventoryView && inventoryView.style.display === 'block') {
                this.showInventoryView(); // 重新显示背包界面以刷新数据
            }
        } else {
            // 兑换失败
            this.showRedemptionError(payload.message || '兑换失败');
        }
    }



    // HTML转义函数，防止XSS
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 处理装备凝练榜数据
    handleRefineLeaderboard(payload) {
        console.log('=== 收到装备凝练榜数据 ===');
        console.log('凝练榜数据:', payload);

        const contentEl = document.getElementById('refine-leaderboard-content');
        if (!contentEl) {
            console.error('未找到 refine-leaderboard-content 元素');
            return;
        }

        const equipmentList = payload.equipment_list || [];
        console.log('解析的装备数组:', equipmentList);
        console.log('装备数量:', equipmentList.length);

        // 保存数据以供详情查看使用
        this.lastRefineLeaderboardData = equipmentList;

        if (equipmentList && equipmentList.length > 0) {
            let html = '<div class="refine-leaderboard-list">';

            equipmentList.forEach((equipment, index) => {
                const rank = index + 1;
                const refineValue = equipment.refine_value || 0;
                const displayName = equipment.display_name || equipment.item_name;
                const playerName = equipment.username;
                const refinePrefix = equipment.refine_prefix || '';
                const slotName = this.getSlotDisplayName(equipment.slot);

                html += `
                    <div class="refine-item" style="
                        border: 1px solid var(--border-color);
                        margin: 8px 0;
                        padding: 12px;
                        border-radius: 4px;
                        background-color: rgba(255,255,255,0.02);
                        cursor: pointer;
                        transition: background-color 0.2s;
                    " onclick="game.showRefineEquipmentDetail(${equipment.inventory_id})"
                       onmouseover="this.style.backgroundColor='rgba(255,255,255,0.05)'"
                       onmouseout="this.style.backgroundColor='rgba(255,255,255,0.02)'">
                        <div style="display: flex; justify-content: space-between; align-items: center;">
                            <div style="flex: 1;">
                                <div style="font-weight: bold; color: #2c3e50; margin-bottom: 4px;">
                                    <span style="color: #e74c3c; font-weight: bold; margin-right: 8px;">#${rank}</span>
                                    <span style="color: #3498db; text-decoration: underline;">${this.escapeHtml(displayName)}</span>
                                </div>
                                <div style="font-size: 13px; color: #666; margin-bottom: 2px;">
                                    <span style="margin-right: 15px;">玩家: ${this.escapeHtml(playerName)}</span>
                                    <span style="margin-right: 15px;">部位: ${slotName}</span>
                                </div>
                                <div style="font-size: 13px; color: #666;">
                                    <span style="margin-right: 15px;">凝练: ${refinePrefix}</span>
                                </div>
                            </div>
                            <div style="text-align: right;">
                                <div style="font-size: 16px; font-weight: bold; color: #e67e22;">
                                    ${refineValue.toFixed(2)}
                                </div>
                                <div style="font-size: 12px; color: #95a5a6;">
                                    凝练值
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            });

            html += '</div>';
            contentEl.innerHTML = html;
        } else {
            contentEl.innerHTML = `
                <div style="text-align: center; padding: 40px; color: #6c757d;">
                    <p>暂无凝练装备</p>
                </div>
            `;
        }
    }

    // 获取装备部位的中文显示名称
    getSlotDisplayName(slot) {
        const slotMap = {
            'Head': '头部',
            'Neck': '颈部',
            'Body': '身体',
            'LeftHand': '左手',
            'RightHand': '右手',
            'TwoHanded': '双手',
            'Finger': '手指',
            'Back': '背部'
        };
        return slotMap[slot] || slot;
    }

    // 显示装备凝练详情
    showRefineEquipmentDetail(inventoryId) {
        console.log('=== 查看装备凝练详情 ===');
        console.log('装备ID:', inventoryId);

        // 从凝练榜数据中找到对应的装备信息
        const refineLeaderboardContent = document.getElementById('refine-leaderboard-content');
        if (!refineLeaderboardContent || !this.lastRefineLeaderboardData) {
            this.addLog('无法获取装备详情');
            return;
        }

        // 在最后获取的凝练榜数据中查找装备
        const targetEquipment = this.lastRefineLeaderboardData.find(item => item.inventory_id == inventoryId);

        if (targetEquipment) {
            console.log('找到装备数据:', targetEquipment);
            // 将凝练榜装备数据转换为标准装备格式，然后使用现有的装备详情界面
            const standardItem = this.convertRefineDataToStandardItem(targetEquipment);
            this.showItemDetails(standardItem, 'refineLeaderboard');
        } else {
            console.log('未找到装备数据');
            this.addLog('无法获取装备详情');
        }
    }

    // 将凝练榜装备数据转换为标准装备格式
    convertRefineDataToStandardItem(equipment) {
        // 构造标准的装备对象，与背包/装备界面的格式保持一致
        const standardItem = {
            inventory_id: equipment.inventory_id,
            name: equipment.item_name,
            category: 'Equipment',
            slot: equipment.slot,
            instance_data: equipment.instance_data,
            description: `来自玩家: ${equipment.username}`,
            // 添加一些装备特有的属性
            is_bound: 1, // 其他玩家的装备视为绑定状态
            is_equipped: 0,
            quantity: 1,
            stackable: 0,
            // 添加特殊标记，表示这是来自凝练榜的装备
            _refineLeaderboardData: {
                owner: equipment.username,
                owner_id: equipment.player_id,
                refine_tier: equipment.refine_tier,
                refine_value: equipment.refine_value,
                refine_prefix: equipment.refine_prefix
            }
        };

        return standardItem;
    }



    // 获取属性的中文显示名称
    getAttributeDisplayName(attribute) {
        const attributeMap = {
            'hp': '生命',
            'max_hp': '最大生命',
            'mp': '魔力',
            'max_mp': '最大魔力',
            'strength': '力量',
            'agility': '敏捷',
            'constitution': '体质',
            'intelligence': '智力',
            'attack': '攻击力',
            'defense': '防御力',
            'attack_speed': '攻击速度',
            'fire_damage': '火属性伤害',
            'ice_damage': '冰属性伤害',
            'wind_damage': '风属性伤害',
            'electric_damage': '雷属性伤害',
            'fire_resistance': '火属性抗性',
            'ice_resistance': '冰属性抗性',
            'wind_resistance': '风属性抗性',
            'electric_resistance': '雷属性抗性',
            'dodge_bonus': '闪避加成',
            'karma': '善恶值',
            'potential_points': '潜力',
            'knowledge_points': '知识',
            'experience': '经验值',
            'level': '等级'
        };
        return attributeMap[attribute] || attribute;
    }
}

const game = new GameClient();