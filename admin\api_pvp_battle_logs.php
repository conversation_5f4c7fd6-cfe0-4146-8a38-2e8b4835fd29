<?php
require_once '../config/Database.php';
require_once 'auth.php';

// 确保用户已登录
// 这里不需要调用ensureAdminAuthenticated()，因为auth.php已经包含了会话检查的代码

$db = Database::getInstance();
$conn = $db->getConnection();

// 处理API请求
$action = $_GET['action'] ?? '';

switch ($action) {
    case 'get_logs':
        getLogs();
        break;
    case 'get_log_detail':
        getLogDetail();
        break;
    case 'export_log':
        exportLog();
        break;
    case 'get_scenes':
        getScenes();
        break;
    case 'clear_old_logs':
        clearOldLogs();
        break;
    default:
        outputError('未知操作');
}

// 获取PVP战斗日志列表
function getLogs() {
    global $conn;
    
    // 分页参数
    $page = isset($_GET['page']) ? (int)$_GET['page'] : 1;
    $pageSize = isset($_GET['page_size']) ? (int)$_GET['page_size'] : 20;
    $offset = ($page - 1) * $pageSize;
    
    // 过滤条件
    $battleResult = $_GET['battle_result'] ?? '';
    $startDate = $_GET['start_date'] ?? '';
    $endDate = $_GET['end_date'] ?? '';
    $sceneId = $_GET['scene_id'] ?? '';
    $playerName = $_GET['player_name'] ?? '';
    
    // 构建WHERE子句
    $where = ['1=1'];
    $params = [];
    
    if (!empty($battleResult)) {
        $where[] = 'battle_result = ?';
        $params[] = $battleResult;
    }
    
    if (!empty($startDate)) {
        $where[] = 'start_time >= ?';
        $params[] = $startDate . ' 00:00:00';
    }
    
    if (!empty($endDate)) {
        $where[] = 'start_time <= ?';
        $params[] = $endDate . ' 23:59:59';
    }
    
    if (!empty($sceneId)) {
        $where[] = 'scene_id = ?';
        $params[] = $sceneId;
    }
    
    if (!empty($playerName)) {
        $where[] = '(participants LIKE ? OR participants LIKE ?)';
        $params[] = '%"name":"' . $playerName . '%';
        $params[] = '%"name":"%' . $playerName . '%';
    }
    
    // 构建SQL查询
    $whereClause = implode(' AND ', $where);
    
    // 计算总记录数
    $countSql = "SELECT COUNT(*) FROM pvp_battle_logs WHERE $whereClause";
    $stmt = $conn->prepare($countSql);
    $stmt->execute($params);
    $totalRecords = $stmt->fetchColumn();
    
    // 获取分页数据
    $sql = "SELECT 
                id, 
                challenger_id,
                challenger_name, 
                defender_id,
                defender_name,
                scene_id, 
                scene_name, 
                start_time, 
                end_time, 
                battle_result, 
                total_damage,
                participants
            FROM pvp_battle_logs 
            WHERE $whereClause 
            ORDER BY start_time DESC 
            LIMIT $offset, $pageSize";
    
    $stmt = $conn->prepare($sql);
    $stmt->execute($params);
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    // 计算分页信息
    $totalPages = ceil($totalRecords / $pageSize);
    
    outputSuccess([
        'logs' => $logs,
        'pagination' => [
            'current_page' => $page,
            'total_pages' => $totalPages,
            'page_size' => $pageSize,
            'total_records' => $totalRecords,
            'showing_from' => $offset + 1,
            'showing_to' => min($offset + $pageSize, $totalRecords)
        ]
    ]);
}

// 获取单条PVP战斗日志详情
function getLogDetail() {
    global $conn;
    
    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if ($id <= 0) {
        outputError('无效的日志ID');
    }
    
    $sql = "SELECT * FROM pvp_battle_logs WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
    $log = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$log) {
        outputError('找不到指定的战斗日志');
    }
    
    outputSuccess($log);
}

// 导出战斗日志
function exportLog() {
    global $conn;
    
    $id = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if ($id <= 0) {
        outputError('无效的日志ID');
    }
    
    $sql = "SELECT * FROM pvp_battle_logs WHERE id = ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$id]);
    $log = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$log) {
        outputError('找不到指定的战斗日志');
    }
    
    // 准备导出数据
    $filename = "pvp_battle_log_{$id}_{$log['start_time']}.txt";
    $content = "PVP战斗日志 #{$id}\n";
    $content .= "===========================================\n\n";
    $content .= "战斗时间: {$log['start_time']} 至 {$log['end_time']}\n";
    $content .= "场景: {$log['scene_name']} (ID: {$log['scene_id']})\n";
    $content .= "挑战者: {$log['challenger_name']} (ID: {$log['challenger_id']})\n";
    $content .= "防守者: {$log['defender_name']} (ID: {$log['defender_id']})\n";
    $content .= "战斗结果: {$log['battle_result']}\n";
    $content .= "总伤害: {$log['total_damage']}\n\n";
    $content .= "战斗记录:\n";
    $content .= "===========================================\n\n";
    
    $logData = json_decode($log['log_data'], true);
    if (is_array($logData)) {
        foreach ($logData as $entry) {
            $content .= "$entry\n";
        }
    }
    
    // 输出文件
    header('Content-Type: text/plain');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    echo $content;
    exit;
}

// 获取场景列表
function getScenes() {
    global $conn;
    
    $sql = "SELECT id, name FROM scenes ORDER BY name";
    $stmt = $conn->query($sql);
    $scenes = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    outputSuccess($scenes);
}

// 清理旧日志
function clearOldLogs() {
    global $conn;
    
    if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
        outputError('必须使用POST方法');
    }
    
    $cleanupDate = $_POST['cleanup_date'] ?? '';
    if (empty($cleanupDate)) {
        outputError('清理日期不能为空');
    }
    
    $sql = "DELETE FROM pvp_battle_logs WHERE start_time < ?";
    $stmt = $conn->prepare($sql);
    $stmt->execute([$cleanupDate . ' 00:00:00']);
    $count = $stmt->rowCount();
    
    outputSuccess(['message' => "成功清理了 $count 条旧战斗日志"]);
}

// 输出成功响应
function outputSuccess($data) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => true,
        'data' => $data
    ]);
    exit;
}

// 输出错误响应
function outputError($message) {
    header('Content-Type: application/json');
    echo json_encode([
        'success' => false,
        'message' => $message
    ]);
    exit;
} 