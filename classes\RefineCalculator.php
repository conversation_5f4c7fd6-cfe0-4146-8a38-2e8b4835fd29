<?php
require_once __DIR__ . '/RarityManager.php';

class RefineCalculator {
    private $db;
    private $dynamicFactor = 1.0;
    private RarityManager $rarityManager;

    public function __construct(Database $db) {
        $this->db = $db;
        $this->rarityManager = new RarityManager();
    }

    // 设置服务器动态系数
    public function setDynamicFactor(float $factor): void {
        $this->dynamicFactor = max(0.8, min(1.5, $factor));
    }

    // 计算材料总分值（含衰减）
    private function calculateMaterialScore(array $materials): float {
        $totalScore = 0;
        $elementGroups = array_count_values(array_column($materials, 'element'));
        
        // 调试信息：打印材料信息
        error_log("RefineCalculator: 开始计算材料分值，使用的材料: " . json_encode($materials));
        error_log("RefineCalculator: 元素分组统计: " . json_encode($elementGroups));
        
        // 获取基础分值
        foreach ($materials as $index => $mat) {
            $stmt = $this->db->query("SELECT score FROM material_scores WHERE element = ? AND tier = ?", [$mat['element'], $mat['tier']]);
            $scoreResult = $stmt->fetch(PDO::FETCH_ASSOC);
            $baseScore = $scoreResult ? (float)$scoreResult['score'] : 0;
            
            // 如果没有找到分数，记录警告
            if (!$scoreResult) {
                error_log("RefineCalculator: 警告 - 未找到元素 {$mat['element']} 等级 {$mat['tier']} 的分数，使用默认值0");
            }
            
            // 应用衰减公式：1 / ln(e + position)
            $position = $index + 1;
            $decayFactor = 1 / log(M_E + $position);
            $materialScore = $baseScore * $decayFactor;
            $totalScore += $materialScore;
            
            // 调试信息：每个材料的分数计算
            error_log(sprintf(
                "RefineCalculator: 材料 #%d [%s] 等级 %d - 基础分值: %.2f, 衰减系数: %.4f, 实际得分: %.2f",
                $index + 1,
                $mat['element'],
                $mat['tier'],
                $baseScore,
                $decayFactor,
                $materialScore
            ));
        }
        
        error_log("RefineCalculator: 材料总分值计算结果: " . $totalScore);
        return $totalScore;
    }

    // 检测组合类型
    private function detectComboType(array $materials): string {
        $elements = array_column($materials, 'element');
        $uniqueElements = array_unique($elements);
        
        error_log("RefineCalculator: 检测组合类型 - 元素列表: " . implode(', ', $elements));
        error_log("RefineCalculator: 检测组合类型 - 唯一元素数量: " . count($uniqueElements));
        
        // 3. 检查相生/相克序列 (提升优先级，先检查这个)
        $sequence = implode('', $elements);
        $elementOrder = ['wood', 'fire', 'earth', 'gold', 'water'];
        
        error_log("RefineCalculator: 检测相生相克 - 当前序列: " . $sequence);
        error_log("RefineCalculator: 检测相生相克 - 相生顺序: " . implode(', ', $elementOrder));
        
        // 生成相生序列正则
        $generateRegex = function($sequence) use ($elementOrder) {
            $patterns = [];
            for ($i = 0; $i < 5; $i++) {
                $rotated = array_merge(array_slice($elementOrder, $i), array_slice($elementOrder, 0, $i));
                $patterns[] = implode('', $rotated);
            }
            return '/^(' . implode('|', $patterns) . '){1,}$/';
        };
        
        // 检查相生 - 修改为检查是否为连续子序列
        $generativeRegex = $generateRegex($elementOrder);
        error_log("RefineCalculator: 相生正则表达式: " . $generativeRegex);
        
        // 检查是否为完整的相生序列
        if (preg_match($generativeRegex, $sequence)) {
            error_log("RefineCalculator: 检测到组合类型 - 完整相生序列");
            return 'elemental_sequence';
        }
        
        // 检查是否为相生子序列
        $isSubSequence = false;
        $fullSequence = implode('', $elementOrder) . implode('', $elementOrder); // 重复一次以处理循环
        
        if (strpos($fullSequence, $sequence) !== false) {
            error_log("RefineCalculator: 检测到组合类型 - 相生子序列");
            $isSubSequence = true;
        }
        
        // 检查相克（反序）
        $reverseOrder = array_reverse($elementOrder);
        $conflictRegex = $generateRegex($reverseOrder);
        error_log("RefineCalculator: 相克正则表达式: " . $conflictRegex);
        
        // 检查是否为完整的相克序列
        if (preg_match($conflictRegex, $sequence)) {
            error_log("RefineCalculator: 检测到组合类型 - 完整相克序列");
            return 'conflict_sequence';
        }
        
        // 检查是否为相克子序列
        $fullReverseSequence = implode('', $reverseOrder) . implode('', $reverseOrder); // 重复一次以处理循环
        
        if (strpos($fullReverseSequence, $sequence) !== false) {
            error_log("RefineCalculator: 检测到组合类型 - 相克子序列");
            // 如果同时是相生和相克的子序列，优先选择相生
            if (!$isSubSequence) {
                return 'conflict_sequence';
            }
        }
        
        // 如果是相生子序列，返回相生类型
        if ($isSubSequence) {
            return 'elemental_sequence';
        }
        
        // 1. 检查五行俱全 (降低优先级)
        if (count($uniqueElements) === 5) {
            error_log("RefineCalculator: 检测到组合类型 - 五行俱全");
            return 'all_elements';
        }
        
        // 2. 检查同属性 (降低优先级)
        if (count($uniqueElements) === 1) {
            error_log("RefineCalculator: 检测到组合类型 - 同属性");
            return 'same_element';
        }
        
        error_log("RefineCalculator: 未检测到特殊组合，使用默认类型");
        return 'default';
    }

    // 执行凝练计算
    public function calculateRefine(array $materials, ?array $equipment = null): array {
        if (count($materials) !== 5) {
            throw new InvalidArgumentException('Exactly 5 materials required');
        }

        error_log("RefineCalculator: 开始凝练计算，材料数量: " . count($materials));

        // 计算总分值
        $totalScore = $this->calculateMaterialScore($materials);
        error_log("RefineCalculator: 材料总分值: " . $totalScore);

        // 获取装备稀有度加成
        $rarityMultiplier = 1.0;
        $equipmentName = '';
        if ($equipment && isset($equipment['name'])) {
            $equipmentName = $equipment['name'];
            $rarityMultiplier = $this->rarityManager->getRarityMultiplier($equipmentName);
            error_log("RefineCalculator: 装备名称: '{$equipmentName}', 稀有度加成系数: {$rarityMultiplier}");
        } else {
            error_log("RefineCalculator: 未提供装备信息或装备名称为空");
        }
        
        // 检查装备是否已有凝练值，如果有，增加额外加成
        $existingRefineBonus = 0;
        if ($equipment && isset($equipment['instance_data'])) {
            $instanceData = is_string($equipment['instance_data']) ? 
                json_decode($equipment['instance_data'], true) : $equipment['instance_data'];
            
            if (is_array($instanceData) && isset($instanceData['refine_value']) && $instanceData['refine_value'] > 0) {
                $existingRefineValue = (float)$instanceData['refine_value'];
                $existingRefineBonus = $existingRefineValue * mt_rand(5, 25) / 100; // 取原凝练值的10%到25%
                
                error_log("RefineCalculator: 检测到装备已有凝练值: {$existingRefineValue}, 增加额外加成: {$existingRefineBonus}");
                $totalScore += $existingRefineBonus;
                error_log("RefineCalculator: 加上已有凝练加成后的总分值: {$totalScore}");
            }
        }
        
        // 获取组合系数
        $comboType = $this->detectComboType($materials);
        $stmt = $this->db->query("SELECT factor FROM combo_factors WHERE combo_type = ?", [$comboType]);
        $factorResult = $stmt->fetch(PDO::FETCH_ASSOC);
        $comboFactor = $factorResult ? (float)$factorResult['factor'] : 1.0;
        
        // 如果没有找到组合系数，记录警告
        if (!$factorResult) {
            error_log("RefineCalculator: 警告 - 未找到组合类型 {$comboType} 的系数，使用默认值1.0");
        }
        
        error_log("RefineCalculator: 组合类型: {$comboType}, 组合系数: {$comboFactor}");

        // 基础RV计算（应用稀有度加成）
        $baseRV = $totalScore * $comboFactor * $rarityMultiplier;
        error_log("RefineCalculator: 基础RV值 = 总分值 * 组合系数 * 稀有度加成 = {$totalScore} * {$comboFactor} * {$rarityMultiplier} = {$baseRV}");
        
        // 随机波动 (±5%)
        $randomFactor = mt_rand(950, 1050) / 1000;
        $afterRandomRV = $baseRV * $randomFactor;
        error_log("RefineCalculator: 随机波动系数: {$randomFactor}, 应用随机波动后RV = {$afterRandomRV}");
        
        // 应用服务器动态系数
        $finalRV = $afterRandomRV * $this->dynamicFactor;
        error_log("RefineCalculator: 服务器动态系数: {$this->dynamicFactor}, 应用动态系数后RV = {$finalRV}");
        
        // 应用边界限制前的值
        $preClampRV = $finalRV;
        
        // 应用边界限制
        $finalRV = max(4.0, min(30.0, $finalRV));
        
        // 如果发生了边界限制，记录
        if ($finalRV != $preClampRV) {
            error_log("RefineCalculator: 应用边界限制，原始RV: {$preClampRV}, 限制后RV: {$finalRV}");
        }
        
        // 确定档位
        $stmt = $this->db->query("SELECT * FROM refine_tiers WHERE ? BETWEEN min_rv AND max_rv", [$finalRV]);
        $tierInfo = $stmt->fetch(PDO::FETCH_ASSOC);
        
        // 记录档位查询结果
        if ($tierInfo) {
            error_log("RefineCalculator: 档位查询成功 - RV值 {$finalRV} 对应档位 {$tierInfo['tier']}, 前缀: {$tierInfo['prefix_equipment']}");
            error_log("RefineCalculator: 档位详细信息: " . json_encode($tierInfo));
        } else {
            error_log("RefineCalculator: 警告 - 未找到RV值 {$finalRV} 对应的档位，使用默认档位1");
        }
        
        // 检查数据库中的档位配置
        $checkTiersStmt = $this->db->query("SELECT * FROM refine_tiers ORDER BY tier ASC");
        $allTiers = $checkTiersStmt->fetchAll(PDO::FETCH_ASSOC);
        error_log("RefineCalculator: 数据库中的所有档位配置: " . json_encode($allTiers));
        
        // 获取装备稀有度信息
        $rarityInfo = null;
        if (!empty($equipmentName)) {
            $rarityInfo = $this->rarityManager->detectRarity($equipmentName);
        }

        return [
            'base_score' => round($totalScore, 2),
            'existing_refine_bonus' => round($existingRefineBonus, 2),
            'combo_type' => $comboType,
            'combo_factor' => $comboFactor,
            'rarity_multiplier' => $rarityMultiplier,
            'rarity_info' => $rarityInfo,
            'base_rv' => round($baseRV, 2),
            'random_factor' => $randomFactor,
            'dynamic_factor' => $this->dynamicFactor,
            'final_rv' => round($finalRV, 2),
            'tier' => $tierInfo ? $tierInfo['tier'] : 1,
            'prefix' => $tierInfo ? [
                'equipment' => $tierInfo['prefix_equipment']
            ] : []
        ];
    }
}
?>