<?php
/**
 * 装备稀有度配置文件
 * 管理装备品级和对应的凝练加成系数
 */

return [
    // 稀有度等级配置
    'rarity_levels' => [
        // 品级名称 => [加成系数, 插槽数量, 描述]
        '普' => [
            'multiplier' => 1.0,
            'slots' => 0,
            'description' => '普通品质',
            'color' => '#FFFFFF'
        ],
        '凡' => [
            'multiplier' => 1.2,
            'slots' => 1,
            'description' => '凡级品质',
            'color' => '#CCCCCC'
        ],
        '良' => [
            'multiplier' => 1.4,
            'slots' => 2,
            'description' => '良级品质',
            'color' => '#00FF00'
        ],
        '优' => [
            'multiplier' => 1.6,
            'slots' => 3,
            'description' => '优级品质',
            'color' => '#0080FF'
        ],
        '珍' => [
            'multiplier' => 1.8,
            'slots' => 4,
            'description' => '珍级品质',
            'color' => '#8000FF'
        ],
        '极' => [
            'multiplier' => 2.0,
            'slots' => 5,
            'description' => '极级品质',
            'color' => '#FF8000'
        ],
        '玄' => [
            'multiplier' => 2.2,
            'slots' => 6,
            'description' => '玄级品质',
            'color' => '#FF0080'
        ],
        '灵' => [
            'multiplier' => 2.4,
            'slots' => 7,
            'description' => '灵级品质（未开放）',
            'color' => '#FF0000'
        ],
        '圣' => [
            'multiplier' => 2.6,
            'slots' => 8,
            'description' => '圣级品质（未开放）',
            'color' => '#FFD700'
        ]
    ],

    // 品级识别配置
    'rarity_detection' => [
        // 品级标识符（用于在装备名称中识别品级）
        'prefixes' => ['普', '凡', '良', '优', '珍', '极', '玄', '灵', '圣'],
        
        // 品级标识格式（正则表达式）
        'pattern' => '/^\[([普凡良优珍极玄灵圣])\](.+)$/u',
        
        // 默认品级（没有品级标识的装备）
        'default_rarity' => '普',
        
        // 是否启用严格模式（只识别配置中的品级）
        'strict_mode' => true
    ],

    // 凝练加成配置
    'refine_bonus' => [
        // 是否启用稀有度加成
        'enabled' => true,
        
        // 加成计算方式
        'calculation_method' => 'multiply', // multiply: 乘法, add: 加法
        
        // 最小加成系数
        'min_multiplier' => 1.0,
        
        // 最大加成系数
        'max_multiplier' => 3.0,
        
        // 加成系数递增值（每级增加）
        'increment_per_level' => 0.2
    ],

    // 显示配置
    'display' => [
        // 是否在装备名称中显示品级
        'show_rarity_in_name' => true,
        
        // 品级显示格式
        'name_format' => '[{rarity}]{name}',
        
        // 是否显示品级颜色
        'show_rarity_color' => true,
        
        // 工具提示中是否显示稀有度信息
        'show_in_tooltip' => true
    ],

    // 调试配置
    'debug' => [
        // 是否启用调试日志
        'enabled' => true,
        
        // 日志前缀
        'log_prefix' => '[稀有度系统]'
    ]
];
?>
