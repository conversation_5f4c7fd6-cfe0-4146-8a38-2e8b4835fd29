<?php
require_once 'auth.php';
require_once '../config/Database.php';

$pageTitle = '安全违规监控';
$currentPage = 'security_violations';
$extra_css = '
<link rel="stylesheet" href="security_violations.css">
<style>
/* 确保并列显示的关键样式 */
.stats-row {
    display: flex !important;
    flex-wrap: wrap;
    margin: 0 -15px 30px -15px;
    gap: 0;
}

.stats-col {
    flex: 0 0 50% !important;
    max-width: 50% !important;
    padding: 0 15px;
    box-sizing: border-box;
}

.stats-card {
    height: auto;
    margin-bottom: 0;
}

.mb-4 {
    margin-bottom: 2rem !important;
}

/* 响应式：小屏幕时堆叠 */
@media (max-width: 768px) {
    .stats-col {
        flex: 0 0 100% !important;
        max-width: 100% !important;
        margin-bottom: 20px;
    }
}
</style>';

$pdo = Database::getInstance()->getConnection();

// 处理封禁/解封操作
if ($_POST['action'] ?? '' === 'ban_ip') {
    $ip = $_POST['ip'] ?? '';
    if ($ip) {
        $sql = "INSERT INTO ip_bans (ip_address, reason, banned_at, expires_at, is_active) 
                VALUES (?, '手动封禁', NOW(), DATE_ADD(NOW(), INTERVAL 24 HOUR), 1)
                ON DUPLICATE KEY UPDATE 
                    expires_at = DATE_ADD(NOW(), INTERVAL 24 HOUR),
                    is_active = 1";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$ip]);
        $message = "IP $ip 已被封禁24小时";
    }
}

if ($_POST['action'] ?? '' === 'unban_ip') {
    $ip = $_POST['ip'] ?? '';
    if ($ip) {
        $sql = "UPDATE ip_bans SET is_active = 0 WHERE ip_address = ?";
        $stmt = $pdo->prepare($sql);
        $stmt->execute([$ip]);
        $message = "IP $ip 已解封";
    }
}

// 获取安全违规记录
$page = max(1, intval($_GET['page'] ?? 1));
$limit = 50;
$offset = ($page - 1) * $limit;

$sql = "SELECT * FROM security_violations 
        ORDER BY created_at DESC 
        LIMIT $limit OFFSET $offset";
$violations = $pdo->query($sql)->fetchAll(PDO::FETCH_ASSOC);

// 获取总记录数
$totalSql = "SELECT COUNT(*) as total FROM security_violations";
$total = $pdo->query($totalSql)->fetch(PDO::FETCH_ASSOC)['total'];
$totalPages = ceil($total / $limit);

// 获取IP封禁列表
$bansSql = "SELECT * FROM ip_bans WHERE is_active = 1 ORDER BY banned_at DESC";
$bans = $pdo->query($bansSql)->fetchAll(PDO::FETCH_ASSOC);

// 获取违规统计
$statsSql = "SELECT 
    reason,
    COUNT(*) as count,
    COUNT(DISTINCT client_ip) as unique_ips
    FROM security_violations 
    WHERE created_at > DATE_SUB(NOW(), INTERVAL 24 HOUR)
    GROUP BY reason 
    ORDER BY count DESC";
$stats = $pdo->query($statsSql)->fetchAll(PDO::FETCH_ASSOC);

include 'layout_header.php';
?>

<div class="container-fluid">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
        <div style="font-size: 0.875rem; color: #6c757d;">
            最后更新: <?= date('Y-m-d H:i:s') ?>
            <span style="margin-left: 10px; color: #28a745;">●</span> 实时监控
        </div>
    </div>

    <?php if (isset($message)): ?>
        <div class="alert alert-success"><?= htmlspecialchars($message) ?></div>
    <?php endif; ?>
    
    <!-- 统计信息 -->
    <div class="stats-row mb-4">
        <div class="stats-col">
            <div class="card stats-card">
                <div class="card-header">
                    <h5>📊 24小时内违规统计</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($stats)): ?>
                        <p>暂无违规记录</p>
                    <?php else: ?>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>违规类型</th>
                                    <th>次数</th>
                                    <th>涉及IP数</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($stats as $stat): ?>
                                <tr>
                                    <td><?= htmlspecialchars($stat['reason']) ?></td>
                                    <td><strong><?= $stat['count'] ?></strong></td>
                                    <td><?= $stat['unique_ips'] ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <div class="stats-col">
            <div class="card stats-card">
                <div class="card-header">
                    <h5>🚫 当前封禁IP列表</h5>
                </div>
                <div class="card-body">
                    <?php if (empty($bans)): ?>
                        <p>暂无封禁IP</p>
                    <?php else: ?>
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>IP地址</th>
                                    <th>封禁时间</th>
                                    <th>解封时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($bans as $ban): ?>
                                <tr>
                                    <td style="font-weight: 500;"><?= htmlspecialchars($ban['ip_address']) ?></td>
                                    <td style="font-size: 0.875rem;"><?= date('m-d H:i', strtotime($ban['banned_at'])) ?></td>
                                    <td style="font-size: 0.875rem;">
                                        <?= date('m-d H:i', strtotime($ban['expires_at'])) ?>
                                        <?php
                                        $remaining = strtotime($ban['expires_at']) - time();
                                        if ($remaining > 0) {
                                            echo '<br><small class="text-muted">剩余' . round($remaining/3600, 1) . '小时</small>';
                                        }
                                        ?>
                                    </td>
                                    <td>
                                        <form method="post" style="display:inline;">
                                            <input type="hidden" name="action" value="unban_ip">
                                            <input type="hidden" name="ip" value="<?= htmlspecialchars($ban['ip_address']) ?>">
                                            <button type="submit" class="btn btn-sm btn-warning" onclick="return confirm('确认解封此IP？')">解封</button>
                                        </form>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 违规记录列表 -->
    <div class="card">
        <div class="card-header">
            <h5>安全违规记录 (总计: <?= $total ?> 条)</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>IP地址</th>
                            <th>违规原因</th>
                            <th>用户代理</th>
                            <th>URL</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($violations as $violation): ?>
                        <tr>
                            <td style="white-space: nowrap;">
                                <div style="font-weight: 500;">
                                    <?= date('m-d H:i', strtotime($violation['created_at'])) ?>
                                </div>
                                <div style="font-size: 0.75rem; color: #6c757d;">
                                    <?= date('Y', strtotime($violation['created_at'])) ?>
                                </div>
                            </td>
                            <td>
                                <div style="font-weight: 500;"><?= htmlspecialchars($violation['client_ip']) ?></div>
                                <div style="margin-top: 4px;">
                                    <?php
                                    // 检查是否已封禁
                                    $checkBanSql = "SELECT * FROM ip_bans WHERE ip_address = ? AND is_active = 1";
                                    $checkBan = $pdo->prepare($checkBanSql);
                                    $checkBan->execute([$violation['client_ip']]);
                                    $isBanned = $checkBan->fetch();

                                    if ($isBanned) {
                                        echo '<span class="badge badge-danger">已封禁</span>';
                                    } else {
                                        echo '<span class="badge badge-success">正常</span>';
                                    }
                                    ?>
                                </div>
                            </td>
                            <td>
                                <span class="badge badge-warning">
                                    <?= htmlspecialchars($violation['reason']) ?>
                                </span>
                            </td>
                            <td style="max-width: 200px;">
                                <div style="font-size: 0.875rem; word-break: break-all; line-height: 1.3;">
                                    <?= htmlspecialchars(substr($violation['user_agent'], 0, 80)) ?>
                                    <?php if (strlen($violation['user_agent']) > 80): ?>...<?php endif; ?>
                                </div>
                            </td>
                            <td style="max-width: 150px;">
                                <div style="font-size: 0.875rem; word-break: break-all; line-height: 1.3;">
                                    <?= htmlspecialchars($violation['url']) ?>
                                </div>
                            </td>
                            <td>
                                <?php if (!$isBanned): ?>
                                <form method="post" style="display:inline;">
                                    <input type="hidden" name="action" value="ban_ip">
                                    <input type="hidden" name="ip" value="<?= htmlspecialchars($violation['client_ip']) ?>">
                                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('确认封禁此IP？')">封禁</button>
                                </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <?php if ($totalPages > 1): ?>
            <nav>
                <ul class="pagination">
                    <?php for ($i = 1; $i <= $totalPages; $i++): ?>
                    <li class="page-item <?= $i == $page ? 'active' : '' ?>">
                        <a class="page-link" href="?page=<?= $i ?>"><?= $i ?></a>
                    </li>
                    <?php endfor; ?>
                </ul>
            </nav>
            <?php endif; ?>
        </div>
    </div>
</div>

<script>
// 自动刷新页面数据
function autoRefresh() {
    // 每30秒刷新一次页面
    setTimeout(function() {
        window.location.reload();
    }, 30000);
}

// 确认操作
function confirmAction(message) {
    return confirm(message);
}

// 页面加载完成后启动自动刷新
document.addEventListener('DOMContentLoaded', function() {
    autoRefresh();

    // 为所有确认按钮添加事件
    const confirmButtons = document.querySelectorAll('button[onclick*="confirm"]');
    confirmButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const form = this.closest('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // 提交后显示加载状态
                    const table = document.querySelector('.table');
                    if (table) {
                        table.classList.add('table-loading');
                    }
                });
            }
        });
    });
});
</script>

<?php include 'layout_footer.php'; ?>
